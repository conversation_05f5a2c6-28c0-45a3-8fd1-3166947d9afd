# CultureConnect Payment System: Comprehensive Implementation Guide

## Table of Contents
1. [Overview](#overview)
2. [Current Issues](#current-issues)
3. [Architecture](#architecture)
4. [Implementation Strategy](#implementation-strategy)
5. [Detailed Implementation Steps](#detailed-implementation-steps)
6. [Integration Points](#integration-points)
7. [Testing Strategy](#testing-strategy)
8. [Troubleshooting](#troubleshooting)
9. [Timeline and Effort Estimation](#timeline-and-effort-estimation)

## Overview

This document serves as the comprehensive implementation guide for the CultureConnect payment system. It consolidates all information needed to implement a unified payment architecture that resolves existing conflicts between multiple payment models, adds support for geolocation-based routing, implements crypto payment integration, and provides PDF generation for receipts.

### Goals
- Create a unified payment model architecture
- Implement geolocation-based payment provider routing
- Add support for crypto payments
- Generate PDF receipts
- Ensure backward compatibility with existing code
- Provide comprehensive test coverage

### Key Features
- Multiple payment method support (cards, bank transfers, USSD, crypto)
- Geolocation-based payment provider selection (Stripe for international, Paystack for Africa)
- Busha.co integration for compliant cryptocurrency payments
- PDF receipt generation and sharing
- Comprehensive error handling
- Backward compatibility with legacy code

## Current Issues

1. **Conflicting Payment Models**:
   - Multiple payment method classes exist (`PaymentMethod`, `PaymentMethodModel`, specialized classes)
   - Conflicting `PaymentMethodType` enums with different values
   - Inconsistent transaction models (`PaymentTransaction`, `TransactionModel`)

2. **Import Conflicts**:
   - Legacy imports vs. new model imports
   - Inconsistent import patterns

3. **Missing Dependencies**:
   - PDF generation functionality references missing dependencies
   - Crypto payment integration requires additional dependencies

4. **Test Failures**:
   - Inconsistent method signatures between implementation and test files
   - Mock implementations need updating

5. **Architectural Inconsistencies**:
   - Lack of clear hierarchy between base and specialized payment models
   - Inconsistent naming conventions

## Architecture

### Hybrid Payment Architecture

```
┌─────────────────────────────────────────────────────────────────────┐
│                           Mobile App                                 │
├─────────────┬─────────────┬────────────────┬──────────────┬─────────┤
│ Payment     │ Provider    │ Payment Method │ Receipt      │ Payment │
│ Screens     │ SDK UIs     │ Components     │ Components   │ History │
└─────┬───────┴──────┬──────┴────────┬───────┴──────┬───────┴────┬────┘
      │              │               │              │            │
      │              │               │              │            │
      │              │               │              │            │
      ▼              │               │              │            │
┌─────────────────────────────────────────────────────────────────────┐
│                         FastAPI Backend                             │
├─────────────┬─────────────┬────────────────┬──────────────┬─────────┤
│ Payment     │ Provider    │ Geolocation    │ PDF          │ Storage │
│ Service     │ Proxies     │ Routing        │ Generation   │ Service │
└─────┬───────┴──────┬──────┴────────┬───────┴──────┬───────┴────┬────┘
      │              │               │              │            │
      │              │               │              │            │
      │              │               │              │            │
      ▼              ▼               ▼              ▼            ▼
┌─────────────────────────────────────────────────────────────────────┐
│                       Payment Providers                             │
├─────────────┬─────────────┬────────────────┬──────────────┬─────────┤
│ Stripe      │ Paystack    │ Busha.co       │ Webhooks     │ APIs    │
│ (Int'l)     │ (Africa)    │ (Crypto)       │              │         │
└─────────────┴─────────────┴────────────────┴──────────────┴─────────┘
```

### Payment Flow Architecture

```
┌─────────────────────────────────────────────────────────────────────┐
│                           Mobile App                                 │
└───────────────┬─────────────────────┬───────────────────────────────┘
                │                     │
                │                     │
                │                     │
                ▼                     ▼
┌───────────────────────┐   ┌─────────────────────────────────────────┐
│    1. Initialize      │   │            4. Present UI                │
│       Payment         │   │                                         │
│                       │   │                                         │
│  Backend generates    │   │  Provider SDK UI components handle      │
│  transaction reference│   │  payment data collection                │
│  and configuration    │   │                                         │
└─────────┬─────────────┘   └───────────────────┬─────────────────────┘
          │                                     │
          │                                     │
          │                                     │
          ▼                                     ▼
┌───────────────────────┐   ┌─────────────────────────────────────────┐
│  2. Process Payment   │   │           5. Verify Payment             │
│                       │   │                                         │
│  Provider processes   │   │  Backend verifies transaction status    │
│  payment with         │   │  with provider API                      │
│  tokenized data       │   │                                         │
└───────────────────────┘   └───────────────────┬─────────────────────┘
                                                │
                                                │
                                                │
                                                ▼
                            ┌─────────────────────────────────────────┐
                            │         6. Complete Transaction         │
                            │                                         │
                            │  - Update transaction records           │
                            │  - Generate receipt                     │
                            │  - Return result to mobile app          │
                            └─────────────────────────────────────────┘
```

### Component Responsibilities

```
┌─────────────────────────────────────────────────────────────────────┐
│                         Backend Components                          │
├─────────────────────────┬───────────────────────────────────────────┤
│ Payment Initialization  │ - Generate transaction references         │
│                         │ - Create transaction records              │
│                         │ - Determine appropriate provider          │
│                         │ - Return configuration to mobile app      │
├─────────────────────────┼───────────────────────────────────────────┤
│ Transaction Verification│ - Verify payment status with provider     │
│                         │ - Update transaction records              │
│                         │ - Handle webhooks from providers          │
│                         │ - Process refunds                         │
├─────────────────────────┼───────────────────────────────────────────┤
│ Receipt Generation      │ - Generate PDF receipts                   │
│                         │ - Store receipt records                   │
│                         │ - Provide receipt retrieval               │
└─────────────────────────┴───────────────────────────────────────────┘

┌─────────────────────────────────────────────────────────────────────┐
│                         Mobile Components                           │
├─────────────────────────┬───────────────────────────────────────────┤
│ Payment Method Selection│ - Display available payment methods       │
│                         │ - Handle user selection                   │
│                         │ - Request payment initialization          │
├─────────────────────────┼───────────────────────────────────────────┤
│ Provider SDK Integration│ - Initialize provider SDKs                │
│                         │ - Present provider UI components          │
│                         │ - Handle payment data collection          │
│                         │ - Submit payment to provider              │
├─────────────────────────┼───────────────────────────────────────────┤
│ Transaction Monitoring  │ - Display payment status                  │
│                         │ - Handle success/failure                  │
│                         │ - Show receipt                            │
└─────────────────────────┴───────────────────────────────────────────┘
```

## Implementation Strategy

We will follow a phased approach to implement the new payment architecture:

### Phase 1: Foundation (Days 1-5)
- [ ] Update dependencies in pubspec.yaml
  - [ ] Add Stripe SDK
  - [ ] Add Paystack SDK
  - [ ] Add HTTP client for Busha.co API
  - [ ] Add PDF generation libraries
  - [ ] Add geolocation libraries
- [ ] Implement payment models
  - [ ] Create base model classes
  - [ ] Implement transaction models
  - [ ] Implement receipt model
- [ ] Implement storage service
  - [ ] Create transaction storage
  - [ ] Create receipt storage
- [ ] Implement API service
  - [ ] Create HTTP client wrapper
  - [ ] Implement error handling
  - [ ] Add authentication headers
- [ ] Implement payment configuration
  - [ ] Create environment-specific configs
  - [ ] Implement secure API key storage
- [ ] Implement utility classes
  - [ ] Create currency formatter
  - [ ] Create date formatter
  - [ ] Implement validation utilities

### Phase 2: Core Services (Days 6-10)
- [ ] Implement payment service core
  - [ ] Create payment service interface
  - [ ] Implement base payment service
  - [ ] Create payment result model
  - [ ] Implement error handling
- [ ] Implement backend payment endpoints
  - [ ] Create payment initialization endpoint
  - [ ] Create payment verification endpoint
  - [ ] Create webhook handling endpoints
  - [ ] Implement transaction logging
- [ ] Implement payment provider framework
  - [ ] Create provider factory
  - [ ] Implement provider interface
  - [ ] Create provider configuration
- [ ] Implement Stripe payment provider
  - [ ] Integrate Stripe SDK
  - [ ] Implement payment processing
  - [ ] Handle Stripe webhooks
  - [ ] Implement error handling

### Phase 3: Core UI (Days 11-15)
- [ ] Implement payment method selection
  - [ ] Create payment method card component
  - [ ] Implement payment method selector
  - [ ] Create payment method icons
- [ ] Implement payment summary widget
  - [ ] Display booking details
  - [ ] Show price breakdown
  - [ ] Format currency amounts
- [ ] Implement payment initialization
  - [ ] Create payment initialization service
  - [ ] Implement loading states
  - [ ] Handle initialization errors
- [ ] Implement Stripe SDK UI integration
  - [ ] Initialize Stripe SDK with backend configuration
  - [ ] Implement Stripe payment sheet
  - [ ] Handle payment results
- [ ] Implement payment screen
  - [ ] Create main payment flow
  - [ ] Integrate payment method selection
  - [ ] Integrate payment summary
  - [ ] Implement provider selection logic
  - [ ] Add error handling and retry logic
- [ ] Implement payment success screen
  - [ ] Create success animation
  - [ ] Display transaction details
  - [ ] Show receipt information
  - [ ] Add options to view/share receipt

### Phase 4: Additional Providers (Days 16-20)
- [ ] Implement Paystack payment provider
  - [ ] Integrate Paystack SDK
  - [ ] Implement backend proxy endpoints
  - [ ] Create transaction initialization
  - [ ] Handle Paystack webhooks
  - [ ] Implement error handling
- [ ] Implement Paystack SDK UI integration
  - [ ] Initialize Paystack SDK with backend configuration
  - [ ] Integrate Paystack Drop-in UI components
  - [ ] Support card payments
  - [ ] Support bank transfers
  - [ ] Support USSD payments
  - [ ] Support mobile money (including M-Pesa)
- [ ] Implement location service
  - [ ] Create geolocation service
  - [ ] Implement country detection
  - [ ] Handle location permissions
  - [ ] Create fallback mechanisms
- [ ] Implement payment region service
  - [ ] Define payment regions
  - [ ] Map countries to regions
  - [ ] Determine recommended providers
- [ ] Implement payment service with geolocation
  - [ ] Extend payment service with location awareness
  - [ ] Implement provider selection logic
  - [ ] Create region-specific payment options

### Phase 5: PDF Generation (Days 21-23)
- [ ] Implement PDF service interface
  - [ ] Define PDF generation contract
  - [ ] Create receipt template model
  - [ ] Define sharing capabilities
- [ ] Implement PDF service
  - [ ] Create PDF document generation
  - [ ] Implement receipt content formatting
  - [ ] Add company branding and styling
  - [ ] Implement file saving functionality
  - [ ] Create sharing mechanisms
- [ ] Implement receipt viewer
  - [ ] Create receipt display component
  - [ ] Format currency and dates
  - [ ] Add PDF viewing capability
  - [ ] Implement sharing functionality
  - [ ] Add loading states

### Phase 6: Additional UI (Days 24-27)
- [ ] Implement payment provider selection
  - [ ] Create provider selection UI
  - [ ] Show available providers based on location
  - [ ] Highlight recommended provider
  - [ ] Handle provider selection
- [ ] Implement payment verification UI
  - [ ] Create verification status display
  - [ ] Implement polling for payment status
  - [ ] Show appropriate loading states
  - [ ] Handle verification errors
- [ ] Implement payment history screen
  - [ ] Create transaction list component
  - [ ] Implement filtering and sorting
  - [ ] Add search functionality
  - [ ] Create transaction detail view
  - [ ] Implement pull-to-refresh
- [ ] Implement receipt detail screen
  - [ ] Create detailed receipt view
  - [ ] Format all receipt information
  - [ ] Add PDF download option
  - [ ] Implement sharing functionality
  - [ ] Add printing capability

### Phase 7: Testing (Days 28-32)
- [ ] Implement mock classes
  - [ ] Create mock payment service
  - [ ] Implement mock payment providers
  - [ ] Create mock location service
  - [ ] Implement mock API responses
- [ ] Implement unit tests
  - [ ] Test payment models
  - [ ] Test payment service
  - [ ] Test provider integrations
  - [ ] Test PDF generation
  - [ ] Test utility classes
- [ ] Implement widget tests
  - [ ] Test payment method selection
  - [ ] Test payment forms
  - [ ] Test receipt viewer
  - [ ] Test payment history
  - [ ] Test error displays
- [ ] Implement integration tests
  - [ ] Test complete payment flow
  - [ ] Test provider switching
  - [ ] Test error recovery
  - [ ] Test receipt generation and sharing

### Phase 8: Advanced Features (Days 33-35)
- [ ] Implement Busha.co crypto payment provider
  - [ ] Create Busha.co API client
  - [ ] Implement backend proxy endpoints
  - [ ] Create crypto transaction initialization
  - [ ] Handle Busha.co webhooks
  - [ ] Implement automatic conversion to fiat
  - [ ] Add support for major cryptocurrencies (BTC, ETH, USDT, USDC)
  - [ ] Implement error handling
- [ ] Implement Busha.co payment integration UI
  - [ ] Create cryptocurrency selection UI
  - [ ] Implement QR code display
  - [ ] Show payment amount in crypto and fiat
  - [ ] Create payment timer and expiration display
  - [ ] Implement real-time status updates
  - [ ] Add success/failure handling

## Detailed Implementation Steps

### Phase 1: Foundation

#### 1. Update Dependencies (pubspec.yaml)

```yaml
dependencies:
  # Existing dependencies...

  # Payment Processing
  stripe_payment: ^1.1.4
  flutter_stripe: ^9.4.0
  flutter_paystack: ^1.0.7

  # PDF Generation
  pdf: ^3.10.4
  printing: ^5.11.0
  path_provider: ^2.1.1
  share_plus: ^7.1.0
  open_file: ^3.3.2

  # Geolocation
  geolocator: ^10.0.1
  geocoding: ^2.1.0

  # Crypto Integration (Busha.co)
  http: ^1.1.0
  qr_flutter: ^4.1.0

  # Utilities
  uuid: ^3.0.7
  intl: ^0.18.1
  encrypt: ^5.0.1
```

#### 2. Hybrid Architecture Implementation

The payment system will use a hybrid architecture where the backend handles sensitive operations while the mobile app integrates with provider SDKs for UI components.

##### Backend Payment Initialization Endpoint

```python
# FastAPI backend code
@router.post("/payments/initialize")
async def initialize_payment(
    request: PaymentInitRequest,
    current_user: User = Depends(get_current_user)
):
    # Generate a unique transaction reference
    transaction_ref = f"CC-{uuid.uuid4().hex[:8].upper()}"

    # Determine the appropriate provider based on user location
    country_code = request.country_code or await get_country_from_ip(request.ip_address)
    provider = determine_payment_provider(country_code)

    # Create a transaction record
    transaction = Transaction(
        id=transaction_ref,
        user_id=current_user.id,
        booking_id=request.booking_id,
        amount=request.amount,
        currency=request.currency,
        provider=provider,
        status="pending",
        metadata=request.metadata
    )
    await transaction_repository.save(transaction)

    # Get provider-specific configuration
    provider_config = get_provider_config(provider, environment=settings.ENVIRONMENT)

    # Return initialization data to the mobile app
    return {
        "reference": transaction_ref,
        "provider": provider,
        "providerConfig": {
            "publicKey": provider_config.public_key,
            # Other provider-specific configuration
        }
    }
```

##### Mobile App Payment Initialization

```dart
// Flutter mobile app code
Future<PaymentInitResult> initializePayment({
  required double amount,
  required String currency,
  required Booking booking,
}) async {
  try {
    // Get user's location for provider selection
    final position = await _locationService.getCurrentPosition();
    final countryCode = await _locationService.getCountryFromPosition(position);

    // Call backend to initialize payment
    final response = await _apiService.post(
      '/payments/initialize',
      data: {
        'amount': amount,
        'currency': currency,
        'booking_id': booking.id,
        'country_code': countryCode,
        'metadata': {
          'experience_id': booking.experienceId,
          'experience_name': booking.experienceName,
          'booking_date': booking.date.toIso8601String(),
          'user_email': booking.userEmail,
        }
      },
    );

    // Return initialization result
    return PaymentInitResult(
      reference: response.data['reference'],
      provider: response.data['provider'],
      providerConfig: response.data['providerConfig'],
    );
  } catch (e) {
    throw PaymentException('Failed to initialize payment: ${e.toString()}');
  }
}
```

#### 3. Payment Models

Each model should implement:
- Constructors (default and named)
- fromJson/toJson methods for serialization
- Equality operators
- toString method
- Helper methods specific to the model

Key models to implement:
- PaymentMethodBase (abstract)
- PaymentMethodType (enum)
- TransactionType (enum)
- TransactionStatus (enum)
- TransactionBase (abstract)
- CardPaymentMethod
- BankTransferPaymentMethod
- UssdPaymentMethod
- CryptoPaymentMethod
- PaymentTransaction
- RefundTransaction
- Receipt

```dart
// Example of base models
enum PaymentMethodType {
  card,
  bankTransfer,
  ussd,
  mobileMoney,
  crypto,
}

enum PaymentProvider {
  stripe,
  paystack,
  busha,
}

enum TransactionStatus {
  pending,
  processing,
  successful,
  failed,
  refunded,
  partiallyRefunded,
}

abstract class PaymentMethodBase {
  final String id;
  final PaymentMethodType type;
  final String title;
  final String description;
  final bool isDefault;

  // Constructor, methods, etc.
}
```

#### 4. Storage Service

Implement methods for:
- Initializing storage
- Saving payment methods
- Retrieving payment methods
- Removing payment methods
- Saving transactions
- Retrieving transactions
- Saving receipts
- Retrieving receipts

#### 5. API Service

Implement methods for:
- Making GET requests
- Making POST requests
- Handling authentication
- Error handling
- Retry logic

#### 6. Payment Configuration

Implement:
- API keys for payment providers
- Endpoint URLs
- Environment-specific configuration
- Feature flags

#### 7. Utility Classes

Implement:
- Currency formatter (formatting currency values with proper symbols)
- Date formatter (formatting dates in various formats)
- Payment validator (validating card numbers, expiry dates, CVV, etc.)

### Phase 2: Core Services

#### 8. Paystack Drop-in UI Integration

Instead of building custom forms, we'll use Paystack's Drop-in UI components:

```dart
// Paystack Card Payment
Future<PaymentResult> processCardPayment({
  required String reference,
  required double amount,
  required String email,
  required String currency,
}) async {
  try {
    // Initialize Paystack plugin
    final plugin = PaystackPlugin();
    await plugin.initialize(publicKey: _config.publicKey);

    // Create charge
    final charge = Charge()
      ..amount = (amount * 100).toInt() // Convert to kobo/cents
      ..email = email
      ..reference = reference
      ..currency = currency;

    // Present Paystack UI and process payment
    final response = await plugin.chargeCard(
      context: _navigationService.context,
      charge: charge,
    );

    // Verify payment with backend
    return await _verifyPayment(reference);
  } catch (e) {
    return PaymentResult(
      success: false,
      reference: reference,
      errorMessage: e.toString(),
    );
  }
}

// Paystack Bank Transfer
Future<PaymentResult> processBankTransfer({
  required String reference,
  required double amount,
  required String email,
  required String currency,
}) async {
  try {
    // Initialize Paystack plugin
    final plugin = PaystackPlugin();
    await plugin.initialize(publicKey: _config.publicKey);

    // Create charge
    final charge = Charge()
      ..amount = (amount * 100).toInt()
      ..email = email
      ..reference = reference
      ..currency = currency;

    // Present Paystack Bank Transfer UI
    final response = await plugin.chargeBank(
      context: _navigationService.context,
      charge: charge,
    );

    // Verify payment with backend
    return await _verifyPayment(reference);
  } catch (e) {
    return PaymentResult(
      success: false,
      reference: reference,
      errorMessage: e.toString(),
    );
  }
}

// Paystack USSD Payment
Future<PaymentResult> processUssdPayment({
  required String reference,
  required double amount,
  required String email,
  required String currency,
}) async {
  try {
    // Initialize Paystack plugin
    final plugin = PaystackPlugin();
    await plugin.initialize(publicKey: _config.publicKey);

    // Create charge
    final charge = Charge()
      ..amount = (amount * 100).toInt()
      ..email = email
      ..reference = reference
      ..currency = currency;

    // Present Paystack USSD UI
    final response = await plugin.chargeUSSD(
      context: _navigationService.context,
      charge: charge,
    );

    // Verify payment with backend
    return await _verifyPayment(reference);
  } catch (e) {
    return PaymentResult(
      success: false,
      reference: reference,
      errorMessage: e.toString(),
    );
  }
}
```

#### 1. Payment Service Interface

Define the contract for payment services with methods for:
- Initializing the payment service
- Processing payments
- Processing refunds
- Managing payment methods
- Generating receipts
- Sharing receipts

```dart
abstract class PaymentServiceInterface {
  Future<void> initialize();
  Future<PaymentResult> processPayment({
    required Booking booking,
    required String provider,
    required String userEmail,
    required String userName,
    String? userPhone,
    PaymentMethodBase? paymentMethod,
    Map<String, dynamic>? additionalData,
  });
  Future<PaymentResult> processRefund({
    required String transactionId,
    required double amount,
    required String reason,
    bool isPartial = false,
  });
  Future<List<PaymentMethodBase>> getSavedPaymentMethods();
  Future<PaymentMethodBase> addPaymentMethod({
    required PaymentMethodBase paymentMethod,
  });
  Future<void> removePaymentMethod(String paymentMethodId);
  Future<void> setDefaultPaymentMethod(String paymentMethodId);
  Future<List<PaymentTransaction>> getPaymentTransactionHistory();
  Future<List<RefundTransaction>> getRefundTransactionHistory();
  Future<Receipt?> getReceipt(String receiptId);
  Future<Receipt> generateReceipt({
    required String transactionId,
    required Booking booking,
    required String paymentMethodName,
  });
  Future<String> generatePdfReceipt(String receiptId);
  Future<void> shareReceipt(String receiptId);
}
```

#### 2. Base Payment Service

Implement common functionality:
- User authentication integration
- Storage service integration
- Payment method management
- Transaction history management
- Receipt generation

#### 3. Main Payment Service

Implement core payment functionality:
- Payment processing
- Refund processing
- Provider selection
- Error handling
- Receipt generation and sharing

#### 4. Legacy Payment Method Adapter

Implement conversion methods:
- Convert legacy payment methods to new models
- Convert new models to legacy format for backward compatibility

#### 5. Payment Provider Interface

Define the contract for payment providers:
```dart
abstract class PaymentProviderInterface {
  Future<void> initialize();
  Future<PaymentResult> processPayment({
    required PaymentTransaction transaction,
    required String userEmail,
    required String userName,
    String? userPhone,
    PaymentMethodBase? paymentMethod,
    Map<String, dynamic>? additionalData,
  });
  Future<PaymentResult> processRefund({
    required PaymentTransaction originalTransaction,
    required RefundTransaction refundTransaction,
  });
}
```

#### 6. Payment Provider Factory

Implement factory methods:
- Create provider instances based on provider type
- Cache provider instances
- Initialize providers

#### 7. Stripe Payment Provider

Implement Stripe-specific functionality:
- Initialize Stripe SDK
- Create payment intents
- Process payments
- Process refunds
- Handle Stripe-specific errors

### Phase 3: Core UI

#### 1. Card Payment Form

Implement a form for entering card details:
- Card number input with validation
- Expiry date input with validation
- CVV input with validation
- Card holder name input
- Set as default checkbox
- Submit button with loading state

```dart
class CardPaymentForm extends StatefulWidget {
  final PaymentMethodType type;
  final Function({
    required String cardNumber,
    required String cardHolderName,
    required int expirationMonth,
    required int expirationYear,
    required String cvv,
    required bool setAsDefault,
  }) onSubmit;
  final bool isLoading;

  // Constructor, build method, etc.
}
```

#### 2. Payment Summary Widget

Implement a widget to display payment summary:
- Experience details
- Booking details
- Price breakdown
- Total amount
- Currency formatting

#### 3. Payment Method Card

Implement a card widget to display payment method details:
- Payment method icon
- Payment method name
- Masked details (last 4 digits, etc.)
- Default indicator
- Selection state

#### 4. Payment Method Selector

Implement a widget to select payment methods:
- List of saved payment methods
- Add new payment method button
- Selection handling
- Default payment method handling

#### 5. Add Payment Method Sheet

Implement a bottom sheet for adding payment methods:
- Payment method type selector
- Dynamic form based on selected type
- Form validation
- Submit handling with loading state

#### 6. Payment Screen

Implement the main payment screen:
- Payment summary
- Payment method selection
- Payment provider selection (if applicable)
- Pay button with loading state
- Error handling and display

#### 7. Payment Success Screen

Implement a screen to show after successful payment:
- Success animation
- Payment details
- Receipt information
- Options to view/share receipt
- Return to home/booking button

### Phase 4: Additional Providers

#### 1. Paystack Payment Provider

Implement Paystack-specific functionality:
- Initialize Paystack SDK
- Process card payments
- Process bank transfers
- Support USSD payments
- Support mobile money (including M-Pesa)
- Process refunds
- Handle Paystack-specific errors

#### 2. USSD Payment Integration

Implement USSD payment functionality through Paystack:
- Generate USSD codes for payment
- Display USSD code to user
- Monitor payment status
- Handle USSD-specific errors and timeouts

#### 3. Location Service

Implement location-related functionality:
- Get current position
- Get last known position
- Get country from position
- Get address from position
- Handle location permissions

#### 4. Payment Region Service

Implement region-based provider selection:
- Define payment regions
- Map countries to regions
- Determine recommended providers by region
- Get available providers by region

#### 5. Payment Service with Geolocation

Extend the payment service with geolocation features:
- Determine user's location
- Select appropriate payment provider
- Fallback mechanisms if location unavailable
- Region-specific payment options

### Phase 5: PDF Generation

#### 1. PDF Service Interface

Define the contract for PDF generation:
```dart
abstract class PdfServiceInterface {
  Future<String> generatePdfReceipt(Receipt receipt);
  Future<void> sharePdfReceipt(String pdfPath, {String? subject, String? text});
  Future<void> viewPdfReceipt(String pdfPath);
  String getReceiptFilePath(String receiptId);
}
```

#### 2. PDF Service Implementation

Implement PDF generation functionality:
- Create PDF documents
- Add receipt content (header, details, footer)
- Save PDF to file
- Share PDF
- View PDF

#### 3. Receipt Viewer

Implement a widget to view receipt details:
- Display receipt information
- Format currency and dates
- Buttons to view PDF and share receipt
- Loading states for PDF operations

### Phase 6: Additional UI

#### 1. Bank Transfer Payment Form

Implement a form for entering bank details:
- Account number input with validation
- Account holder name input
- Bank name input
- Routing number input with validation
- Account type selection
- Set as default checkbox
- Submit button with loading state

#### 2. USSD Payment Form

Implement a form for USSD payments:
- Network provider selection
- Phone number input with validation
- Display generated USSD code
- Instructions for completing payment
- Status monitoring UI
- Submit button with loading state

#### 3. Payment Provider Selection

Implement a widget to select payment providers:
- List of available providers based on location
- Recommended provider indication
- Selection handling
- Provider descriptions and icons

#### 4. Payment History Screen

Implement a screen to view payment history:
- List of transactions with details
- Filter by transaction type
- Sort by date
- Search functionality
- Pull to refresh

#### 5. Receipt Detail Screen

Implement a screen to view detailed receipt information:
- Complete receipt information
- Formatted currency and dates
- Options to view PDF, share, and download receipt
- Print functionality

### Phase 7: Testing

#### 1. Payment Service Mock

Implement a mock payment service for testing:
- Simulate successful payments
- Simulate failed payments
- Simulate payment method operations
- Simulate receipt operations

#### 2. Payment Provider Mock

Implement mock payment providers for testing:
- Simulate provider-specific behaviors
- Simulate different response scenarios
- Simulate errors and edge cases

#### 3. Payment Service Tests

Implement unit tests for the payment service:
- Test payment processing
- Test refund processing
- Test payment method management
- Test receipt generation
- Test error handling

#### 4. Payment UI Tests

Implement widget tests for payment UI components:
- Test form validation
- Test UI state management
- Test user interactions
- Test error displays

#### 5. Payment Integration Tests

Implement integration tests for the complete payment flow:
- Test end-to-end payment process
- Test different payment methods
- Test different payment providers
- Test error scenarios and recovery

### Phase 8: Advanced Features

#### 1. Busha.co Crypto Payment Provider

Implement Busha.co cryptocurrency payment functionality:
- Initialize Busha.co Commerce API integration
- Generate payment addresses through Busha.co API
- Monitor payment status through webhooks
- Support major cryptocurrencies (BTC, ETH, USDT, USDC)
- Handle automatic conversion to fiat currency
- Implement compliant crypto payment flow
- Handle Busha.co-specific errors

#### 2. Busha.co Payment Integration UI

Implement UI for Busha.co crypto payment flow:
- Crypto currency selection (BTC, ETH, USDT, USDC)
- Display QR code for payment generated by Busha.co
- Show payment amount in both crypto and fiat currency
- Display payment timer and expiration
- Real-time payment status updates
- Success/failure handling with appropriate user feedback

## Hybrid Architecture Implementation

### Backend Payment Verification

The backend handles payment verification to ensure security:

```python
# FastAPI backend code
@router.post("/payments/verify")
async def verify_payment(
    request: PaymentVerifyRequest,
    current_user: User = Depends(get_current_user)
):
    # Get the transaction from the database
    transaction = await transaction_repository.get_by_id(request.reference)
    if not transaction:
        raise HTTPException(status_code=404, detail="Transaction not found")

    # Verify the transaction with the provider
    provider = get_provider_service(transaction.provider)
    verification_result = await provider.verify_transaction(transaction.id)

    # Update the transaction status
    transaction.status = verification_result.status
    transaction.provider_reference = verification_result.provider_reference
    transaction.last_updated = datetime.utcnow()
    await transaction_repository.update(transaction)

    # Generate receipt if payment was successful
    receipt = None
    if verification_result.status == "successful":
        receipt = await receipt_service.generate_receipt(transaction)

    # Return the verification result
    return {
        "success": verification_result.status == "successful",
        "status": verification_result.status,
        "message": verification_result.message,
        "receiptId": receipt.id if receipt else None,
    }
```

### Webhook Handling

Webhooks are crucial for receiving asynchronous payment updates:

```python
# FastAPI backend code
@router.post("/webhooks/paystack")
async def paystack_webhook(request: Request):
    # Verify webhook signature
    signature = request.headers.get("x-paystack-signature")
    if not verify_paystack_signature(signature, await request.body()):
        raise HTTPException(status_code=400, detail="Invalid signature")

    # Parse webhook data
    data = await request.json()
    event = data.get("event")

    # Handle different event types
    if event == "charge.success":
        # Get transaction reference from payload
        reference = data.get("data", {}).get("reference")

        # Update transaction status
        transaction = await transaction_repository.get_by_id(reference)
        if transaction:
            transaction.status = "successful"
            transaction.provider_reference = data.get("data", {}).get("id")
            transaction.last_updated = datetime.utcnow()
            await transaction_repository.update(transaction)

            # Generate receipt
            await receipt_service.generate_receipt(transaction)

    # Return success response
    return {"status": "success"}
```

## Integration Points

### 1. Payment Service and UI Integration

The payment service is integrated with the UI through the following key points:

```dart
// In payment_screen.dart
final paymentService = ref.read(paymentServiceProvider);

// Process payment
final result = await paymentService.processPayment(
  booking: widget.booking,
  provider: _selectedProvider.toString().split('.').last,
  userEmail: widget.userEmail,
  userName: widget.userName,
  userPhone: widget.userPhone,
  paymentMethod: _selectedPaymentMethod,
);

// Handle result
if (result.success) {
  // Get the receipt
  final receipt = await paymentService.getReceipt(
    result.additionalData?['receiptId'] as String,
  );

  // Navigate to success screen
  Navigator.pushReplacement(
    context,
    MaterialPageRoute(
      builder: (context) => PaymentSuccessScreen(
        experience: widget.experience,
        booking: widget.booking,
        receipt: receipt!,
      ),
    ),
  );
}
```

### 2. Payment Provider and Service Integration

Payment providers are integrated with the payment service through the provider factory:

```dart
// In payment_service.dart
final providerService = _providerFactory.getProvider(paymentProvider);

// Process the payment with the provider
final result = await providerService.processPayment(
  transaction: transaction,
  userEmail: userEmail,
  userName: userName,
  userPhone: userPhone,
  paymentMethod: paymentMethod,
  additionalData: additionalData,
);
```

### 3. Geolocation and Payment Provider Integration

Geolocation is integrated with payment provider selection:

```dart
// In payment_service_with_geolocation.dart
Future<PaymentProvider> _determinePaymentProvider() async {
  try {
    // Get the user's location
    final position = await _locationService.getCurrentPosition();

    // Get the country from the location
    final country = await _locationService.getCountryFromPosition(position);

    // Select the appropriate payment provider based on the country
    switch (country?.toLowerCase()) {
      case 'nigeria':
      case 'ghana':
      case 'kenya':
      case 'south africa':
      case 'tanzania':
      case 'uganda':
      case 'rwanda':
        return PaymentProvider.paystack;
      default:
        return PaymentProvider.stripe;
    }
  } catch (e) {
    // Default to Stripe if location cannot be determined
    return PaymentProvider.stripe;
  }
}
```

### 4. PDF Generation and Receipt Integration

PDF generation is integrated with the receipt model:

```dart
// In pdf_service.dart
Future<String> generatePdfReceipt(Receipt receipt) async {
  // Create a PDF document
  final pdf = pw.Document();

  // Add a page with receipt content
  pdf.addPage(
    pw.Page(
      build: (context) => buildReceiptContent(receipt),
    ),
  );

  // Save the PDF to a file
  final file = File(getReceiptFilePath(receipt.id));
  await file.writeAsBytes(await pdf.save());

  return file.path;
}
```

## Testing Strategy

### Unit Testing

1. **Model Tests**:
   - Test serialization/deserialization
   - Test validation methods
   - Test helper methods
   - Test equality operators

2. **Service Tests**:
   - Test payment processing with mock providers
   - Test refund processing
   - Test payment method management
   - Test receipt generation
   - Test error handling and recovery

3. **Provider Tests**:
   - Test provider initialization
   - Test payment processing
   - Test refund processing
   - Test error handling

### Widget Testing

1. **Form Tests**:
   - Test input validation
   - Test form submission
   - Test loading states
   - Test error displays

2. **UI Component Tests**:
   - Test payment method card display
   - Test payment method selection
   - Test payment provider selection
   - Test receipt display

### Integration Testing

1. **Payment Flow Tests**:
   - Test complete payment flow with mock providers
   - Test different payment methods
   - Test different payment providers
   - Test error scenarios and recovery

2. **Geolocation Tests**:
   - Test provider selection based on location
   - Test fallback mechanisms

3. **PDF Generation Tests**:
   - Test receipt PDF generation
   - Test PDF sharing
   - Test PDF viewing

### Test Scenarios

For each phase of implementation, the following test scenarios should pass:

#### Phase 1: Foundation
- Models can be serialized and deserialized
- Storage service can save and retrieve data
- API service can make requests and handle responses
- Utility classes correctly format and validate data

#### Phase 2: Core Services
- Payment service can process payments and refunds
- Payment providers can be initialized and used
- Legacy adapters correctly convert between models

#### Phase 3: Core UI
- Forms validate inputs correctly
- UI components display data correctly
- User interactions are handled properly

#### Phase 4: Additional Providers
- Paystack payment provider works correctly for African markets
- Geolocation-based routing selects the correct provider (Stripe or Paystack)
- Fallback mechanisms work when location is unavailable

#### Phase 5: PDF Generation
- PDFs are generated with correct content
- PDFs can be shared and viewed
- Receipt viewer displays receipt information correctly

## Security Considerations for Hybrid Architecture

### 1. API Key Management

The hybrid architecture improves security by keeping sensitive API keys on the server:

- **Public Keys**: Only public API keys are exposed in the mobile app
- **Secret Keys**: All secret keys remain securely on the backend server
- **Key Rotation**: Backend can rotate keys without requiring app updates
- **Environment Separation**: Different keys for development, staging, and production

### 2. PCI Compliance

The architecture maintains PCI compliance by:

- **Tokenization**: Card details are tokenized by the payment provider SDKs
- **No Storage**: Sensitive card data is never stored in the app or backend
- **Provider UI**: Using provider-supplied UI components for data collection
- **Secure Transmission**: All data is transmitted over HTTPS

### 3. Data Protection

User and payment data is protected through:

- **Minimal Data Collection**: Only necessary data is collected
- **Encryption**: All sensitive data is encrypted in transit and at rest
- **Access Control**: Backend implements proper authentication and authorization
- **Audit Logging**: All payment operations are logged for audit purposes

### 4. Fraud Prevention

The architecture includes fraud prevention measures:

- **Server-side Verification**: All payments are verified on the server
- **IP Tracking**: User IP addresses are logged for suspicious activity detection
- **Transaction Limits**: Configurable limits on transaction amounts
- **Webhook Validation**: All webhooks are validated with signatures

## Expanded Explanation of the Hybrid Approach

The hybrid approach combines the security benefits of a backend-centric approach with the user experience benefits of direct SDK integration. This section provides concrete examples of payment flows in the CultureConnect application context and guidance for backend implementation.

### Payment Flow Examples

#### Card Payment Flow

1. **User selects "Pay with Card" in the CultureConnect app**
   - The app calls the backend to initialize a payment
   - Backend generates a transaction reference and determines the appropriate provider (Paystack for African users, Stripe for international)
   - Backend creates a transaction record and returns the reference and provider configuration

2. **App initializes the Paystack SDK**
   - Using the public key from the backend configuration
   - Creates a charge object with the transaction reference
   - Presents the Paystack card payment UI

3. **User enters card details in the Paystack UI**
   - Card details are securely handled by Paystack
   - Paystack processes the payment and returns a result
   - App receives the result from Paystack

4. **App verifies the payment with the backend**
   - Sends the transaction reference to the backend
   - Backend verifies the transaction status with Paystack
   - Backend updates the transaction record and generates a receipt if successful

5. **App displays the result to the user**
   - Shows success screen with receipt details if payment succeeded
   - Shows error message with retry option if payment failed

#### USSD Payment Flow

1. **User selects "Pay with USSD" in the CultureConnect app**
   - Same initialization process as card payment
   - Backend determines provider and returns configuration

2. **App initializes Paystack SDK for USSD**
   - Creates a charge object with the transaction reference
   - Presents the Paystack USSD payment UI

3. **Paystack displays USSD code to the user**
   - User dials the USSD code on their phone
   - Completes the payment through their mobile network
   - Paystack receives confirmation from the mobile network

4. **Paystack notifies the backend via webhook**
   - Backend receives the webhook notification
   - Verifies the webhook signature
   - Updates the transaction status
   - Generates a receipt if payment is successful

5. **App polls the backend for payment status**
   - Periodically checks the transaction status
   - Updates the UI based on the current status
   - Shows success screen when payment completes

#### Crypto Payment Flow (Busha.co)

1. **User selects "Pay with Crypto" in the CultureConnect app**
   - App calls backend to initialize a crypto payment
   - Backend creates transaction record and contacts Busha.co API
   - Busha.co generates payment address and returns details

2. **App displays payment information**
   - Shows QR code with crypto payment address
   - Displays amount in both crypto and fiat currency
   - Shows payment timer and instructions

3. **User completes payment from their crypto wallet**
   - Scans QR code or copies address
   - Sends the exact crypto amount
   - Transaction is recorded on blockchain

4. **Busha.co notifies backend via webhook**
   - Backend receives notification when payment is detected
   - Backend receives second notification when payment is confirmed
   - Backend updates transaction status accordingly

5. **App displays result to user**
   - Shows real-time status updates during confirmation
   - Displays success screen when fully confirmed
   - Shows receipt with transaction details

### Backend Implementation Guide

To implement the backend portion of this hybrid architecture, you'll need to:

#### 1. Set Up Payment Provider Accounts

- Create accounts with Stripe, Paystack, and Busha.co
- Generate API keys for each provider
- Configure webhook endpoints
- Set up test environments

#### 2. Create Database Schema

```python
# Example SQLAlchemy models

class Transaction(Base):
    __tablename__ = "transactions"

    id = Column(String, primary_key=True)  # Transaction reference
    user_id = Column(String, ForeignKey("users.id"), nullable=False)
    booking_id = Column(String, ForeignKey("bookings.id"), nullable=False)
    amount = Column(Float, nullable=False)
    currency = Column(String, nullable=False)
    provider = Column(String, nullable=False)  # "stripe", "paystack", "busha"
    provider_reference = Column(String, nullable=True)  # Provider's transaction ID
    status = Column(String, nullable=False)  # "pending", "processing", "successful", "failed"
    metadata = Column(JSON, nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    user = relationship("User", back_populates="transactions")
    booking = relationship("Booking", back_populates="transactions")
    receipts = relationship("Receipt", back_populates="transaction")


class Receipt(Base):
    __tablename__ = "receipts"

    id = Column(String, primary_key=True)
    transaction_id = Column(String, ForeignKey("transactions.id"), nullable=False)
    pdf_path = Column(String, nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow)

    # Relationships
    transaction = relationship("Transaction", back_populates="receipts")
```

#### 3. Implement API Endpoints

Create the following FastAPI endpoints:

1. **Payment Initialization** (`POST /api/payments/initialize`)
   - Accepts booking details, amount, currency
   - Determines appropriate payment provider
   - Creates transaction record
   - Returns transaction reference and configuration

2. **Payment Verification** (`POST /api/payments/verify`)
   - Accepts transaction reference
   - Verifies payment status with provider
   - Updates transaction record
   - Generates receipt if successful
   - Returns verification result

3. **Webhook Handlers**
   - Stripe webhook (`POST /api/webhooks/stripe`)
   - Paystack webhook (`POST /api/webhooks/paystack`)
   - Busha.co webhook (`POST /api/webhooks/busha`)

4. **Receipt Endpoints**
   - Get receipt (`GET /api/receipts/{receipt_id}`)
   - Download receipt PDF (`GET /api/receipts/{receipt_id}/pdf`)

#### 4. Implement Provider Services

Create service classes for each payment provider:

```python
# Example provider service structure

class PaymentProviderService(ABC):
    @abstractmethod
    async def initialize_payment(self, transaction: Transaction) -> Dict:
        pass

    @abstractmethod
    async def verify_transaction(self, transaction_id: str) -> VerificationResult:
        pass

    @abstractmethod
    async def process_webhook(self, payload: Dict, headers: Dict) -> Dict:
        pass


class PaystackService(PaymentProviderService):
    def __init__(self, config: PaystackConfig):
        self.secret_key = config.secret_key
        self.public_key = config.public_key
        self.base_url = config.base_url

    async def initialize_payment(self, transaction: Transaction) -> Dict:
        # Implementation for Paystack

    async def verify_transaction(self, transaction_id: str) -> VerificationResult:
        # Implementation for Paystack

    async def process_webhook(self, payload: Dict, headers: Dict) -> Dict:
        # Implementation for Paystack
```

#### 5. Implement Receipt Generation

Create a service for generating and managing receipts:

```python
class ReceiptService:
    def __init__(self, config: ReceiptConfig):
        self.storage_path = config.storage_path
        self.company_logo = config.company_logo
        self.company_details = config.company_details

    async def generate_receipt(self, transaction: Transaction) -> Receipt:
        # Create receipt record
        receipt = Receipt(
            id=f"REC-{uuid.uuid4().hex[:8].upper()}",
            transaction_id=transaction.id
        )

        # Generate PDF
        pdf_path = await self._generate_pdf(transaction, receipt)
        receipt.pdf_path = pdf_path

        # Save receipt to database
        await receipt_repository.save(receipt)

        return receipt

    async def _generate_pdf(self, transaction: Transaction, receipt: Receipt) -> str:
        # PDF generation implementation
```

### Benefits of the Hybrid Approach

This hybrid approach provides several benefits for CultureConnect:

1. **Enhanced Security**: Sensitive operations and keys remain on the server
2. **Better User Experience**: Native payment UI components provide a familiar experience
3. **Simplified Maintenance**: Backend changes don't always require app updates
4. **Regulatory Compliance**: Easier to maintain compliance with financial regulations
5. **Flexibility**: Can easily add or change payment providers without major app changes
6. **Reduced Development Time**: Using provider SDKs reduces custom UI development
7. **Improved Reliability**: Provider-maintained components are well-tested and reliable
8. **Better Error Handling**: Providers handle common payment errors gracefully

## Additional Implementation Considerations

### Security Considerations

#### 1. Security Audit Requirements

Before deploying the payment system to production, implement these security measures:

- [ ] **Penetration Testing**: Conduct penetration testing specifically targeting the payment flows
- [ ] **Vulnerability Scanning**: Regular automated vulnerability scanning of both backend and mobile app
- [ ] **Code Security Review**: Third-party security review of payment-related code
- [ ] **Dependency Scanning**: Regular scanning for vulnerabilities in dependencies
- [ ] **OWASP Compliance**: Verify compliance with OWASP Top 10 security risks
- [ ] **Data Encryption Audit**: Verify all sensitive data is properly encrypted at rest and in transit
- [ ] **Access Control Review**: Audit all access controls to payment-related endpoints and data

#### 2. Multi-Environment Configuration

Implement proper separation between environments:

- [ ] **Development Environment**:
  - Use sandbox/test credentials for all payment providers
  - Implement clear visual indicators that distinguish the environment (e.g., "TEST MODE" banners)
  - Disable actual money movement
  - Allow test credit card numbers and payment methods

- [ ] **Staging Environment**:
  - Mirror production configuration but use sandbox/test credentials
  - Implement end-to-end testing of complete payment flows
  - Test webhooks with provider test events
  - Validate receipt generation and notifications

- [ ] **Production Environment**:
  - Use production credentials with proper access controls
  - Implement strict IP restrictions for admin access
  - Enable enhanced logging and monitoring
  - Configure real-time alerts for payment failures
  - Implement gradual rollout strategy for new payment features

#### 3. Compliance Documentation

Maintain the following documentation for compliance purposes:

- [ ] **PCI DSS Compliance Documentation**:
  - Self-assessment questionnaire (SAQ A if using only embedded forms)
  - Network diagrams showing cardholder data flow
  - List of all service providers with access to payment data
  - Evidence of security awareness training for developers

- [ ] **Data Protection Impact Assessment**:
  - Document all personal data processed during payments
  - Risk assessment for data processing activities
  - Technical and organizational measures implemented

- [ ] **Audit Logs**:
  - Maintain immutable audit logs of all payment-related activities
  - Document log retention policies and access controls
  - Implement regular log reviews for suspicious activities

### Critical Components

#### 1. Retry Mechanisms

Implement robust retry handling for API calls:

- [ ] **Exponential Backoff Strategy**:
  ```python
  # Backend implementation
  async def call_provider_with_retry(func, *args, max_retries=3, **kwargs):
      retries = 0
      while retries < max_retries:
          try:
              return await func(*args, **kwargs)
          except (ConnectionError, TimeoutError) as e:
              retries += 1
              if retries >= max_retries:
                  raise
              # Exponential backoff with jitter
              wait_time = (2 ** retries) + random.uniform(0, 1)
              logger.warning(f"Retrying after {wait_time}s due to: {str(e)}")
              await asyncio.sleep(wait_time)
  ```

- [ ] **Mobile App Retry Logic**:
  ```dart
  // Flutter implementation
  Future<T> callWithRetry<T>({
    required Future<T> Function() apiCall,
    int maxRetries = 3,
  }) async {
    int retries = 0;
    while (true) {
      try {
        return await apiCall();
      } catch (e) {
        retries++;
        if (retries >= maxRetries) rethrow;

        // Exponential backoff with jitter
        final waitTime = Duration(
          milliseconds: (pow(2, retries) * 1000).toInt() +
              Random().nextInt(1000),
        );

        debugPrint('Retrying after ${waitTime.inMilliseconds}ms due to: $e');
        await Future.delayed(waitTime);
      }
    }
  }
  ```

#### 2. Idempotency Handling

Implement idempotency to prevent duplicate transactions:

- [ ] **Generate Idempotency Keys**:
  ```dart
  // Flutter implementation
  String generateIdempotencyKey(String transactionRef) {
    final now = DateTime.now().millisecondsSinceEpoch;
    final random = Random().nextInt(1000000);
    return '$transactionRef-$now-$random';
  }
  ```

- [ ] **Store and Verify Idempotency Keys**:
  ```python
  # Backend implementation
  class IdempotencyKey(Base):
      __tablename__ = "idempotency_keys"

      key = Column(String, primary_key=True)
      endpoint = Column(String, nullable=False)
      request_params = Column(JSON, nullable=False)
      response = Column(JSON, nullable=True)
      created_at = Column(DateTime, default=datetime.utcnow)
      expires_at = Column(DateTime, nullable=False)

  async def process_with_idempotency(key, endpoint, params, processor_func):
      # Check if we have a stored response
      stored_key = await idempotency_repository.get_by_id(key)
      if stored_key and stored_key.response:
          return stored_key.response

      # If no stored response, create a new record
      if not stored_key:
          expires_at = datetime.utcnow() + timedelta(days=3)
          stored_key = IdempotencyKey(
              key=key,
              endpoint=endpoint,
              request_params=params,
              expires_at=expires_at
          )
          await idempotency_repository.save(stored_key)

      # Process the request
      try:
          response = await processor_func(params)
          # Store the response
          stored_key.response = response
          await idempotency_repository.update(stored_key)
          return response
      except Exception as e:
          # Don't store errors as successful responses
          raise
  ```

#### 3. Reconciliation Processes

Implement daily reconciliation to ensure transaction integrity:

- [ ] **Automated Daily Reconciliation**:
  ```python
  # Backend implementation
  async def reconcile_transactions(provider, date):
      # Get transactions from our database
      our_transactions = await transaction_repository.get_by_provider_and_date(
          provider=provider,
          date=date
      )

      # Get transactions from provider API
      provider_service = get_provider_service(provider)
      provider_transactions = await provider_service.get_transactions_for_date(date)

      # Find discrepancies
      our_transaction_map = {t.provider_reference: t for t in our_transactions if t.provider_reference}
      provider_transaction_map = {t['id']: t for t in provider_transactions}

      # Transactions in provider but not in our system
      missing_transactions = [
          t for t_id, t in provider_transaction_map.items()
          if t_id not in our_transaction_map
      ]

      # Transactions with status mismatch
      status_mismatches = [
          (our_transaction_map[t_id], t)
          for t_id, t in provider_transaction_map.items()
          if t_id in our_transaction_map and
             our_transaction_map[t_id].status != map_provider_status(t['status'])
      ]

      # Generate reconciliation report
      report = {
          'date': date.isoformat(),
          'provider': provider,
          'total_transactions': len(provider_transactions),
          'matched_transactions': len(provider_transactions) - len(missing_transactions) - len(status_mismatches),
          'missing_transactions': missing_transactions,
          'status_mismatches': status_mismatches,
      }

      # Save reconciliation report
      await reconciliation_repository.save_report(report)

      # Alert if discrepancies found
      if missing_transactions or status_mismatches:
          await alert_service.send_alert(
              level='warning',
              title=f'Reconciliation discrepancies found for {provider}',
              details=report
          )

      return report
  ```

- [ ] **Reconciliation Dashboard**:
  - Implement an admin dashboard to view reconciliation reports
  - Allow manual resolution of discrepancies
  - Track resolution status of each discrepancy

#### 4. Refund and Partial Refund Flows

Implement comprehensive refund capabilities:

- [ ] **Backend Refund Implementation**:
  ```python
  # Backend implementation
  @router.post("/payments/{transaction_id}/refund")
  async def refund_payment(
      transaction_id: str,
      request: RefundRequest,
      current_user: User = Depends(get_admin_user)
  ):
      # Get the transaction
      transaction = await transaction_repository.get_by_id(transaction_id)
      if not transaction:
          raise HTTPException(status_code=404, detail="Transaction not found")

      # Validate refund amount
      if request.amount > transaction.amount:
          raise HTTPException(
              status_code=400,
              detail="Refund amount cannot exceed original transaction amount"
          )

      # Create refund record
      refund = Refund(
          id=f"REF-{uuid.uuid4().hex[:8].upper()}",
          transaction_id=transaction.id,
          amount=request.amount,
          reason=request.reason,
          status="pending",
          created_by=current_user.id
      )
      await refund_repository.save(refund)

      # Process refund with provider
      provider = get_provider_service(transaction.provider)
      try:
          result = await provider.process_refund(
              transaction_id=transaction.provider_reference,
              amount=request.amount,
              reason=request.reason
          )

          # Update refund status
          refund.status = result.status
          refund.provider_reference = result.provider_reference
          await refund_repository.update(refund)

          # Update transaction status if full refund
          if request.amount == transaction.amount:
              transaction.status = "refunded"
          else:
              transaction.status = "partially_refunded"
          await transaction_repository.update(transaction)

          return {
              "success": result.status == "successful",
              "refundId": refund.id,
              "status": refund.status,
              "message": result.message
          }
      except Exception as e:
          # Update refund status to failed
          refund.status = "failed"
          refund.failure_reason = str(e)
          await refund_repository.update(refund)

          raise HTTPException(
              status_code=500,
              detail=f"Failed to process refund: {str(e)}"
          )
  ```

- [ ] **Mobile App Refund UI**:
  - Implement admin-only refund interface
  - Allow selection of full or partial refund
  - Require reason for refund
  - Display refund history and status

#### 5. Dispute and Chargeback Handling

Implement processes for handling payment disputes:

- [ ] **Dispute Webhook Handling**:
  ```python
  # Backend implementation
  @router.post("/webhooks/stripe/dispute")
  async def stripe_dispute_webhook(request: Request):
      # Verify webhook signature
      signature = request.headers.get("stripe-signature")
      if not verify_stripe_signature(signature, await request.body()):
          raise HTTPException(status_code=400, detail="Invalid signature")

      # Parse webhook data
      data = await request.json()
      dispute = data.get("data", {}).get("object", {})

      # Get associated transaction
      charge_id = dispute.get("charge")
      transaction = await transaction_repository.get_by_provider_reference(charge_id)
      if not transaction:
          logger.error(f"Dispute received for unknown transaction: {charge_id}")
          return {"status": "error", "message": "Unknown transaction"}

      # Create dispute record
      dispute_record = Dispute(
          id=f"DSP-{uuid.uuid4().hex[:8].upper()}",
          transaction_id=transaction.id,
          provider_dispute_id=dispute.get("id"),
          amount=dispute.get("amount") / 100,  # Convert from cents
          currency=dispute.get("currency"),
          reason=dispute.get("reason"),
          status=dispute.get("status"),
          evidence_due_by=datetime.fromtimestamp(dispute.get("evidence_details", {}).get("due_by", 0))
      )
      await dispute_repository.save(dispute_record)

      # Update transaction status
      transaction.status = "disputed"
      await transaction_repository.update(transaction)

      # Send alert to admin
      await alert_service.send_alert(
          level="urgent",
          title=f"New payment dispute received",
          details={
              "transactionId": transaction.id,
              "disputeId": dispute_record.id,
              "amount": dispute_record.amount,
              "reason": dispute_record.reason,
              "evidenceDueBy": dispute_record.evidence_due_by.isoformat()
          }
      )

      return {"status": "success"}
  ```

- [ ] **Dispute Management UI**:
  - Create admin interface for viewing and responding to disputes
  - Implement evidence upload functionality
  - Track dispute resolution status
  - Set up reminders for evidence submission deadlines

#### 6. Monitoring and Alerting

Implement comprehensive monitoring systems:

- [ ] **Payment Success Rate Monitoring**:
  ```python
  # Backend implementation
  async def calculate_payment_success_rate(
      provider=None,
      start_time=None,
      end_time=None,
      interval="hour"
  ):
      # Default to last 24 hours if not specified
      if not end_time:
          end_time = datetime.utcnow()
      if not start_time:
          start_time = end_time - timedelta(days=1)

      # Get transactions in the time period
      transactions = await transaction_repository.get_by_time_range(
          start_time=start_time,
          end_time=end_time,
          provider=provider
      )

      # Group by interval
      intervals = []
      current = start_time
      while current < end_time:
          if interval == "hour":
              next_interval = current + timedelta(hours=1)
          elif interval == "day":
              next_interval = current + timedelta(days=1)
          else:
              raise ValueError(f"Unsupported interval: {interval}")

          intervals.append((current, next_interval))
          current = next_interval

      # Calculate success rate for each interval
      results = []
      for start, end in intervals:
          interval_transactions = [
              t for t in transactions
              if start <= t.created_at < end
          ]

          total = len(interval_transactions)
          successful = len([t for t in interval_transactions if t.status == "successful"])

          success_rate = (successful / total) * 100 if total > 0 else 0

          results.append({
              "start_time": start.isoformat(),
              "end_time": end.isoformat(),
              "total_transactions": total,
              "successful_transactions": successful,
              "success_rate": success_rate
          })

      # Check for anomalies
      for result in results:
          if result["total_transactions"] > 0 and result["success_rate"] < 90:
              await alert_service.send_alert(
                  level="warning",
                  title=f"Low payment success rate detected",
                  details={
                      "time_period": f"{result['start_time']} to {result['end_time']}",
                      "success_rate": f"{result['success_rate']:.2f}%",
                      "provider": provider or "all providers"
                  }
              )

      return results
  ```

- [ ] **Real-time Payment Monitoring Dashboard**:
  - Implement dashboard showing payment success rates
  - Display transaction volume by provider
  - Show average transaction processing time
  - Highlight anomalies and errors

### Edge Cases

#### 1. Network Disconnection During Payment

Implement robust handling for network issues:

- [ ] **Client-side Network Monitoring**:
  ```dart
  // Flutter implementation
  class NetworkAwarePaymentProcessor {
    final Connectivity _connectivity = Connectivity();
    StreamSubscription<ConnectivityResult>? _subscription;

    Future<void> processPayment({
      required PaymentFunction paymentFunction,
      required Function(PaymentResult) onSuccess,
      required Function(String) onError,
      required Function() onNetworkError,
    }) async {
      // Check connectivity before starting
      final connectivityResult = await _connectivity.checkConnectivity();
      if (connectivityResult == ConnectivityResult.none) {
        onNetworkError();
        return;
      }

      // Monitor connectivity during payment
      _subscription = _connectivity.onConnectivityChanged.listen((result) {
        if (result == ConnectivityResult.none) {
          // Network lost during payment
          onNetworkError();
        }
      });

      try {
        // Process payment
        final result = await paymentFunction();
        onSuccess(result);
      } catch (e) {
        onError(e.toString());
      } finally {
        // Clean up subscription
        await _subscription?.cancel();
        _subscription = null;
      }
    }
  }
  ```

- [ ] **Payment Status Verification**:
  - Implement status check endpoint to verify payment status
  - Allow users to manually check payment status after reconnection
  - Provide clear instructions for users when disconnection occurs

#### 2. Provider Downtime Strategies

Implement fallback mechanisms for provider outages:

- [ ] **Provider Health Checking**:
  ```python
  # Backend implementation
  async def check_provider_health(provider_name):
      provider = get_provider_service(provider_name)
      try:
          # Attempt to call a lightweight endpoint
          result = await provider.ping()
          return {
              "provider": provider_name,
              "status": "healthy" if result else "degraded",
              "response_time_ms": result.get("response_time_ms") if result else None,
              "checked_at": datetime.utcnow().isoformat()
          }
      except Exception as e:
          return {
              "provider": provider_name,
              "status": "unhealthy",
              "error": str(e),
              "checked_at": datetime.utcnow().isoformat()
          }

  async def check_all_providers_health():
      results = {}
      for provider_name in ["stripe", "paystack", "busha"]:
          results[provider_name] = await check_provider_health(provider_name)
      return results
  ```

- [ ] **Provider Fallback Strategy**:
  ```python
  # Backend implementation
  async def get_available_providers(country_code):
      # Check health of all providers
      health_results = await check_all_providers_health()

      # Determine primary provider based on country
      primary_provider = determine_primary_provider(country_code)

      # Check if primary provider is healthy
      if health_results.get(primary_provider, {}).get("status") == "healthy":
          return [primary_provider] + [p for p in health_results if p != primary_provider and health_results[p]["status"] == "healthy"]

      # If primary provider is unhealthy, return healthy alternatives
      healthy_providers = [p for p in health_results if health_results[p]["status"] == "healthy"]

      # If no healthy providers, return all providers ordered by status
      if not healthy_providers:
          return sorted(
              health_results.keys(),
              key=lambda p: 0 if health_results[p]["status"] == "degraded" else 1
          )

      return healthy_providers
  ```

#### 3. Cross-border Payment Limitations

Handle international payment restrictions:

- [ ] **Country-specific Restrictions Database**:
  ```python
  # Backend implementation
  class CountryRestriction(Base):
      __tablename__ = "country_restrictions"

      id = Column(Integer, primary_key=True)
      country_code = Column(String, nullable=False)
      provider = Column(String, nullable=False)
      payment_method = Column(String, nullable=False)
      is_restricted = Column(Boolean, default=False)
      restriction_reason = Column(String, nullable=True)
      alternative_methods = Column(JSON, nullable=True)

  async def check_payment_method_availability(country_code, provider, payment_method):
      # Check for specific restriction
      restriction = await restriction_repository.get_by_criteria(
          country_code=country_code,
          provider=provider,
          payment_method=payment_method
      )

      if restriction and restriction.is_restricted:
          return {
              "available": False,
              "reason": restriction.restriction_reason,
              "alternatives": restriction.alternative_methods
          }

      return {
          "available": True
      }
  ```

- [ ] **Client-side Restriction Handling**:
  ```dart
  // Flutter implementation
  Future<List<PaymentMethod>> getAvailablePaymentMethods(String countryCode) async {
    try {
      final response = await _apiService.get(
        '/payment-methods/available',
        queryParameters: {'countryCode': countryCode},
      );

      final methods = (response.data['methods'] as List)
          .map((m) => PaymentMethod.fromJson(m))
          .toList();

      // Filter out any methods with restrictions
      return methods.where((m) => m.isAvailable).toList();
    } catch (e) {
      // Fallback to basic methods if API call fails
      return [
        PaymentMethod(
          type: PaymentMethodType.card,
          isAvailable: true,
          title: 'Credit/Debit Card',
        ),
      ];
    }
  }
  ```

#### 4. Currency Conversion Edge Cases

Handle currency conversion challenges:

- [ ] **Exchange Rate Caching**:
  ```python
  # Backend implementation
  class ExchangeRate(Base):
      __tablename__ = "exchange_rates"

      id = Column(Integer, primary_key=True)
      base_currency = Column(String, nullable=False)
      target_currency = Column(String, nullable=False)
      rate = Column(Float, nullable=False)
      source = Column(String, nullable=False)
      fetched_at = Column(DateTime, default=datetime.utcnow)
      valid_until = Column(DateTime, nullable=False)

  async def get_exchange_rate(base_currency, target_currency):
      # Check for cached rate that's still valid
      now = datetime.utcnow()
      rate = await exchange_rate_repository.get_latest_valid_rate(
          base_currency=base_currency,
          target_currency=target_currency,
          valid_at=now
      )

      if rate:
          return rate.rate

      # Fetch new rate from external service
      try:
          new_rate = await exchange_rate_service.fetch_rate(
              base_currency=base_currency,
              target_currency=target_currency
          )

          # Cache the new rate (valid for 1 hour)
          valid_until = now + timedelta(hours=1)
          await exchange_rate_repository.save(ExchangeRate(
              base_currency=base_currency,
              target_currency=target_currency,
              rate=new_rate.rate,
              source=new_rate.source,
              valid_until=valid_until
          ))

          return new_rate.rate
      except Exception as e:
          # If fetching fails, try to get the most recent rate regardless of validity
          fallback_rate = await exchange_rate_repository.get_most_recent_rate(
              base_currency=base_currency,
              target_currency=target_currency
          )

          if fallback_rate:
              # Log that we're using an outdated rate
              logger.warning(
                  f"Using outdated exchange rate for {base_currency} to {target_currency}. "
                  f"Rate from {fallback_rate.fetched_at} used."
              )
              return fallback_rate.rate

          # If no rate available at all, raise an error
          raise ValueError(f"No exchange rate available for {base_currency} to {target_currency}")
  ```

- [ ] **Rate Lock for Transactions**:
  ```python
  # Backend implementation
  async def initialize_payment_with_conversion(
      amount,
      base_currency,
      target_currency,
      booking_id,
      user_id
  ):
      # Get current exchange rate
      exchange_rate = await get_exchange_rate(
          base_currency=base_currency,
          target_currency=target_currency
      )

      # Calculate converted amount
      converted_amount = amount * exchange_rate

      # Create transaction with both original and converted amounts
      transaction = Transaction(
          id=f"TX-{uuid.uuid4().hex[:8].upper()}",
          booking_id=booking_id,
          user_id=user_id,
          amount=converted_amount,
          currency=target_currency,
          status="pending",
          metadata={
              "original_amount": amount,
              "original_currency": base_currency,
              "exchange_rate": exchange_rate,
              "rate_locked_at": datetime.utcnow().isoformat()
          }
      )

      await transaction_repository.save(transaction)

      return {
          "transaction_id": transaction.id,
          "original_amount": amount,
          "original_currency": base_currency,
          "converted_amount": converted_amount,
          "target_currency": target_currency,
          "exchange_rate": exchange_rate
      }
  ```

## Troubleshooting

### Common Issues and Solutions

1. **Payment Provider SDK Integration Issues**:
   - **Issue**: SDK initialization fails
   - **Solution**: Verify API keys and ensure proper initialization order
   - **Example**: Stripe SDK must be initialized before use with `Stripe.publishableKey = 'pk_test_...'`

## Enhanced Provider Integration

### Stripe Integration Details

#### 1. Stripe Backend Implementation

```python
# Backend implementation
class StripeService(PaymentProviderService):
    def __init__(self, config: StripeConfig):
        self.secret_key = config.secret_key
        self.public_key = config.public_key
        self.webhook_secret = config.webhook_secret
        self.api_version = config.api_version

        # Initialize Stripe client
        stripe.api_key = self.secret_key
        stripe.api_version = self.api_version

    async def initialize_payment(self, transaction: Transaction) -> Dict:
        try:
            # Create a payment intent
            intent = stripe.PaymentIntent.create(
                amount=int(transaction.amount * 100),  # Convert to cents
                currency=transaction.currency.lower(),
                metadata={
                    'transaction_id': transaction.id,
                    'booking_id': transaction.booking_id,
                    'user_id': transaction.user_id
                },
                description=f"Payment for booking {transaction.booking_id}"
            )

            # Update transaction with provider reference
            transaction.provider_reference = intent.id
            transaction.metadata = {
                **transaction.metadata,
                'client_secret': intent.client_secret
            }
            await transaction_repository.update(transaction)

            return {
                'provider': 'stripe',
                'client_secret': intent.client_secret,
                'public_key': self.public_key
            }
        except stripe.error.StripeError as e:
            # Handle Stripe-specific errors
            error_message = self._handle_stripe_error(e)
            raise PaymentProviderError(error_message)

    async def verify_transaction(self, transaction_id: str) -> VerificationResult:
        transaction = await transaction_repository.get_by_id(transaction_id)
        if not transaction:
            raise ValueError(f"Transaction not found: {transaction_id}")

        try:
            # Retrieve payment intent from Stripe
            intent = stripe.PaymentIntent.retrieve(transaction.provider_reference)

            # Map Stripe status to our status
            status = self._map_stripe_status(intent.status)

            return VerificationResult(
                status=status,
                provider_reference=intent.id,
                message=f"Payment {status}",
                details={
                    'payment_method': intent.payment_method_types[0] if intent.payment_method_types else None,
                    'amount': intent.amount / 100,  # Convert from cents
                    'currency': intent.currency,
                    'stripe_status': intent.status
                }
            )
        except stripe.error.StripeError as e:
            # Handle Stripe-specific errors
            error_message = self._handle_stripe_error(e)
            return VerificationResult(
                status="failed",
                provider_reference=transaction.provider_reference,
                message=error_message
            )

    async def process_webhook(self, payload: Dict, headers: Dict) -> Dict:
        # Verify webhook signature
        signature = headers.get('stripe-signature')
        if not signature:
            raise ValueError("Missing Stripe signature")

        try:
            # Verify the event
            event = stripe.Webhook.construct_event(
                payload=json.dumps(payload),
                sig_header=signature,
                secret=self.webhook_secret
            )

            # Handle different event types
            if event.type == 'payment_intent.succeeded':
                await self._handle_payment_success(event.data.object)
            elif event.type == 'payment_intent.payment_failed':
                await self._handle_payment_failure(event.data.object)
            elif event.type == 'charge.refunded':
                await self._handle_refund(event.data.object)

            return {'status': 'success', 'event_type': event.type}
        except stripe.error.SignatureVerificationError:
            raise ValueError("Invalid signature")
        except Exception as e:
            logger.error(f"Error processing Stripe webhook: {str(e)}")
            raise

    async def process_refund(self, transaction_id: str, amount: float, reason: str) -> Dict:
        transaction = await transaction_repository.get_by_provider_reference(transaction_id)
        if not transaction:
            raise ValueError(f"Transaction not found for provider reference: {transaction_id}")

        try:
            # Create refund in Stripe
            refund = stripe.Refund.create(
                payment_intent=transaction_id,
                amount=int(amount * 100),  # Convert to cents
                reason=self._map_refund_reason(reason)
            )

            return {
                'status': 'successful' if refund.status == 'succeeded' else 'processing',
                'provider_reference': refund.id,
                'message': f"Refund {refund.status}"
            }
        except stripe.error.StripeError as e:
            error_message = self._handle_stripe_error(e)
            raise PaymentProviderError(error_message)

    def _map_stripe_status(self, stripe_status: str) -> str:
        status_map = {
            'requires_payment_method': 'pending',
            'requires_confirmation': 'pending',
            'requires_action': 'pending',
            'processing': 'processing',
            'requires_capture': 'processing',
            'canceled': 'failed',
            'succeeded': 'successful'
        }
        return status_map.get(stripe_status, 'pending')

    def _map_refund_reason(self, reason: str) -> str:
        reason_map = {
            'customer_request': 'requested_by_customer',
            'duplicate': 'duplicate',
            'fraudulent': 'fraudulent'
        }
        return reason_map.get(reason, 'requested_by_customer')

    def _handle_stripe_error(self, error: stripe.error.StripeError) -> str:
        if isinstance(error, stripe.error.CardError):
            return f"Card error: {error.user_message}"
        elif isinstance(error, stripe.error.RateLimitError):
            return "Too many requests made to the Stripe API"
        elif isinstance(error, stripe.error.InvalidRequestError):
            return f"Invalid request: {error.user_message}"
        elif isinstance(error, stripe.error.AuthenticationError):
            return "Authentication with Stripe failed"
        elif isinstance(error, stripe.error.APIConnectionError):
            return "Network communication with Stripe failed"
        elif isinstance(error, stripe.error.StripeError):
            return f"Stripe error: {error.user_message}"
        else:
            return f"Unknown error: {str(error)}"

    async def _handle_payment_success(self, payment_intent):
        # Find transaction by provider reference
        transaction = await transaction_repository.get_by_provider_reference(payment_intent.id)
        if not transaction:
            logger.error(f"Transaction not found for payment intent: {payment_intent.id}")
            return

        # Update transaction status
        transaction.status = "successful"
        transaction.updated_at = datetime.utcnow()
        await transaction_repository.update(transaction)

        # Generate receipt
        await receipt_service.generate_receipt(transaction)

    async def _handle_payment_failure(self, payment_intent):
        # Find transaction by provider reference
        transaction = await transaction_repository.get_by_provider_reference(payment_intent.id)
        if not transaction:
            logger.error(f"Transaction not found for payment intent: {payment_intent.id}")
            return

        # Update transaction status
        transaction.status = "failed"
        transaction.updated_at = datetime.utcnow()
        transaction.metadata = {
            **transaction.metadata,
            'failure_message': payment_intent.last_payment_error.message if payment_intent.last_payment_error else "Payment failed"
        }
        await transaction_repository.update(transaction)

    async def _handle_refund(self, charge):
        # Find transaction by charge ID
        transaction = await transaction_repository.get_by_provider_reference(charge.payment_intent)
        if not transaction:
            logger.error(f"Transaction not found for charge: {charge.id}")
            return

        # Check if fully refunded
        if charge.refunded:
            transaction.status = "refunded"
        else:
            transaction.status = "partially_refunded"

        transaction.updated_at = datetime.utcnow()
        await transaction_repository.update(transaction)
```

#### 2. Stripe Mobile Integration

```dart
// Flutter implementation
class StripePaymentService {
  final Stripe _stripe = Stripe();

  Future<void> initialize(String publishableKey) async {
    await _stripe.initialize(
      publishableKey: publishableKey,
      merchantIdentifier: 'merchant.com.cultureconnect',
      androidPayMode: 'test',
    );
  }

  Future<PaymentResult> processPayment({
    required String clientSecret,
    required String amount,
    required String currency,
    required BuildContext context,
  }) async {
    try {
      // Present the Stripe payment sheet
      final paymentResult = await _stripe.presentPaymentSheet(
        paymentSheetParameters: PaymentSheetParameters(
          clientSecret: clientSecret,
          merchantDisplayName: 'CultureConnect',
          customerId: null,
          customerEphemeralKeySecret: null,
          setupIntentClientSecret: null,
          applePay: const PaymentSheetApplePay(
            merchantCountryCode: 'US',
          ),
          googlePay: const PaymentSheetGooglePay(
            merchantCountryCode: 'US',
            testEnv: true,
          ),
          style: ThemeMode.system,
          appearance: PaymentSheetAppearance(
            colors: PaymentSheetAppearanceColors(
              primary: Theme.of(context).primaryColor,
            ),
          ),
        ),
      );

      // Return result
      return PaymentResult(
        success: true,
        reference: clientSecret.split('_secret_')[0],
      );
    } on StripeException catch (e) {
      return PaymentResult(
        success: false,
        reference: clientSecret.split('_secret_')[0],
        errorMessage: e.error.localizedMessage,
      );
    } catch (e) {
      return PaymentResult(
        success: false,
        reference: clientSecret.split('_secret_')[0],
        errorMessage: e.toString(),
      );
    }
  }

  Future<PaymentResult> confirmPayment({
    required String clientSecret,
    required PaymentMethodParams params,
  }) async {
    try {
      // Confirm the payment with specific payment method
      final paymentIntent = await _stripe.confirmPayment(
        paymentIntentClientSecret: clientSecret,
        data: params,
      );

      return PaymentResult(
        success: paymentIntent.status == PaymentIntentsStatus.Succeeded,
        reference: paymentIntent.id,
        additionalData: {
          'status': paymentIntent.status,
          'paymentMethodId': paymentIntent.paymentMethodId,
        },
      );
    } on StripeException catch (e) {
      return PaymentResult(
        success: false,
        reference: clientSecret.split('_secret_')[0],
        errorMessage: e.error.localizedMessage,
      );
    } catch (e) {
      return PaymentResult(
        success: false,
        reference: clientSecret.split('_secret_')[0],
        errorMessage: e.toString(),
      );
    }
  }
}
```

#### 3. Stripe Testing Tokens

Use these test card numbers for Stripe integration testing:

| Card Number         | Brand      | CVC          | Date            | Result                 |
|---------------------|------------|--------------|-----------------|------------------------|
| 4242 4242 4242 4242 | Visa       | Any 3 digits | Any future date | Successful payment     |
| 4000 0025 0000 3155 | Visa       | Any 3 digits | Any future date | Requires authentication|
| 4000 0000 0000 9995 | Visa       | Any 3 digits | Any future date | Insufficient funds     |
| 4000 0000 0000 0010 | Visa       | Any 3 digits | Any future date | Card declined          |
| 5555 5555 5555 4444 | Mastercard | Any 3 digits | Any future date | Successful payment     |
| 3782 8224 6310 005  | Amex       | Any 4 digits | Any future date | Successful payment     |

#### 4. Stripe Webhook Verification

```python
# Backend implementation
def verify_stripe_signature(signature: str, payload: bytes) -> bool:
    try:
        # Get webhook secret from environment or configuration
        webhook_secret = settings.STRIPE_WEBHOOK_SECRET

        # Construct event to verify signature
        stripe.Webhook.construct_event(
            payload=payload.decode('utf-8'),
            sig_header=signature,
            secret=webhook_secret
        )
        return True
    except stripe.error.SignatureVerificationError:
        return False
    except Exception:
        return False
```

### Paystack Integration Details

#### 1. Paystack Backend Implementation

```python
# Backend implementation
class PaystackService(PaymentProviderService):
    def __init__(self, config: PaystackConfig):
        self.secret_key = config.secret_key
        self.public_key = config.public_key
        self.base_url = "https://api.paystack.co"

    async def initialize_payment(self, transaction: Transaction) -> Dict:
        try:
            # Prepare request data
            payload = {
                "amount": int(transaction.amount * 100),  # Convert to kobo
                "email": transaction.metadata.get("email"),
                "currency": transaction.currency,
                "reference": transaction.id,
                "callback_url": f"{settings.APP_URL}/payments/verify",
                "metadata": {
                    "transaction_id": transaction.id,
                    "booking_id": transaction.booking_id,
                    "user_id": transaction.user_id,
                    "custom_fields": [
                        {
                            "display_name": "Booking Reference",
                            "variable_name": "booking_reference",
                            "value": transaction.booking_id
                        }
                    ]
                }
            }

            # Make API request to Paystack
            headers = {
                "Authorization": f"Bearer {self.secret_key}",
                "Content-Type": "application/json"
            }

            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{self.base_url}/transaction/initialize",
                    headers=headers,
                    json=payload
                ) as response:
                    result = await response.json()

                    if not result.get("status"):
                        raise PaymentProviderError(result.get("message", "Failed to initialize payment"))

                    # Update transaction with provider data
                    transaction.provider_reference = result["data"]["reference"]
                    transaction.metadata = {
                        **transaction.metadata,
                        "authorization_url": result["data"]["authorization_url"],
                        "access_code": result["data"]["access_code"]
                    }
                    await transaction_repository.update(transaction)

                    return {
                        "provider": "paystack",
                        "reference": result["data"]["reference"],
                        "authorization_url": result["data"]["authorization_url"],
                        "access_code": result["data"]["access_code"],
                        "public_key": self.public_key
                    }
        except Exception as e:
            logger.error(f"Paystack initialization error: {str(e)}")
            raise PaymentProviderError(f"Failed to initialize payment: {str(e)}")

    async def verify_transaction(self, transaction_id: str) -> VerificationResult:
        transaction = await transaction_repository.get_by_id(transaction_id)
        if not transaction:
            raise ValueError(f"Transaction not found: {transaction_id}")

        try:
            # Make API request to verify transaction
            headers = {
                "Authorization": f"Bearer {self.secret_key}",
                "Content-Type": "application/json"
            }

            async with aiohttp.ClientSession() as session:
                async with session.get(
                    f"{self.base_url}/transaction/verify/{transaction.provider_reference}",
                    headers=headers
                ) as response:
                    result = await response.json()

                    if not result.get("status"):
                        return VerificationResult(
                            status="failed",
                            provider_reference=transaction.provider_reference,
                            message=result.get("message", "Verification failed")
                        )

                    # Map Paystack status to our status
                    paystack_status = result["data"]["status"]
                    status = self._map_paystack_status(paystack_status)

                    # Update transaction with provider data
                    if status == "successful":
                        transaction.provider_reference = result["data"]["reference"]
                        transaction.status = status
                        transaction.metadata = {
                            **transaction.metadata,
                            "payment_channel": result["data"]["channel"],
                            "authorization": result["data"]["authorization"]
                        }
                        await transaction_repository.update(transaction)

                    return VerificationResult(
                        status=status,
                        provider_reference=result["data"]["reference"],
                        message=f"Payment {status}",
                        details={
                            "amount": result["data"]["amount"] / 100,  # Convert from kobo
                            "currency": result["data"]["currency"],
                            "payment_channel": result["data"]["channel"],
                            "paystack_status": paystack_status
                        }
                    )
        except Exception as e:
            logger.error(f"Paystack verification error: {str(e)}")
            return VerificationResult(
                status="failed",
                provider_reference=transaction.provider_reference,
                message=f"Verification failed: {str(e)}"
            )

    async def process_webhook(self, payload: Dict, headers: Dict) -> Dict:
        # Verify webhook signature
        signature = headers.get("x-paystack-signature")
        if not signature or not self._verify_signature(signature, json.dumps(payload)):
            raise ValueError("Invalid signature")

        try:
            # Handle different event types
            event = payload.get("event")

            if event == "charge.success":
                await self._handle_payment_success(payload["data"])
            elif event == "transfer.success":
                await self._handle_transfer_success(payload["data"])
            elif event == "transfer.failed":
                await self._handle_transfer_failed(payload["data"])
            elif event == "refund.processed":
                await self._handle_refund_processed(payload["data"])

            return {"status": "success", "event_type": event}
        except Exception as e:
            logger.error(f"Error processing Paystack webhook: {str(e)}")
            raise

    async def process_refund(self, transaction_id: str, amount: float, reason: str) -> Dict:
        transaction = await transaction_repository.get_by_provider_reference(transaction_id)
        if not transaction:
            raise ValueError(f"Transaction not found for provider reference: {transaction_id}")

        try:
            # Prepare request data
            payload = {
                "transaction": transaction_id,
                "amount": int(amount * 100),  # Convert to kobo
                "currency": transaction.currency,
                "customer_note": reason,
                "merchant_note": f"Refund for transaction {transaction_id}: {reason}"
            }

            # Make API request to Paystack
            headers = {
                "Authorization": f"Bearer {self.secret_key}",
                "Content-Type": "application/json"
            }

            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{self.base_url}/refund",
                    headers=headers,
                    json=payload
                ) as response:
                    result = await response.json()

                    if not result.get("status"):
                        raise PaymentProviderError(result.get("message", "Failed to process refund"))

                    return {
                        "status": "processing",  # Paystack refunds are always processing initially
                        "provider_reference": result["data"]["reference"],
                        "message": "Refund initiated"
                    }
        except Exception as e:
            logger.error(f"Paystack refund error: {str(e)}")
            raise PaymentProviderError(f"Failed to process refund: {str(e)}")

    def _map_paystack_status(self, paystack_status: str) -> str:
        status_map = {
            "success": "successful",
            "abandoned": "failed",
            "failed": "failed",
            "pending": "pending"
        }
        return status_map.get(paystack_status.lower(), "pending")

    def _verify_signature(self, signature: str, payload: str) -> bool:
        """Verify that the webhook payload was sent by Paystack"""
        computed_hmac = hmac.new(
            key=self.secret_key.encode(),
            msg=payload.encode(),
            digestmod=hashlib.sha512
        ).hexdigest()
        return hmac.compare_digest(computed_hmac, signature)

    async def _handle_payment_success(self, data: Dict):
        # Find transaction by reference
        reference = data.get("reference")
        transaction = await transaction_repository.get_by_provider_reference(reference)
        if not transaction:
            logger.error(f"Transaction not found for reference: {reference}")
            return

        # Update transaction status
        transaction.status = "successful"
        transaction.updated_at = datetime.utcnow()
        transaction.metadata = {
            **transaction.metadata,
            "payment_channel": data.get("channel"),
            "authorization": data.get("authorization")
        }
        await transaction_repository.update(transaction)

        # Generate receipt
        await receipt_service.generate_receipt(transaction)

    async def _handle_transfer_success(self, data: Dict):
        # Handle successful transfers (for payouts)
        pass

    async def _handle_transfer_failed(self, data: Dict):
        # Handle failed transfers (for payouts)
        pass

    async def _handle_refund_processed(self, data: Dict):
        # Find transaction by reference
        reference = data.get("transaction_reference")
        transaction = await transaction_repository.get_by_provider_reference(reference)
        if not transaction:
            logger.error(f"Transaction not found for reference: {reference}")
            return

        # Check if fully refunded
        original_amount = transaction.amount * 100  # Convert to kobo
        refund_amount = data.get("amount", 0)

        if refund_amount >= original_amount:
            transaction.status = "refunded"
        else:
            transaction.status = "partially_refunded"

        transaction.updated_at = datetime.utcnow()
        await transaction_repository.update(transaction)
```

#### 2. Paystack Mobile Integration

```dart
// Flutter implementation
class PaystackPaymentService {
  final PaystackPlugin _paystackPlugin = PaystackPlugin();

  Future<void> initialize(String publicKey) async {
    await _paystackPlugin.initialize(publicKey: publicKey);
  }

  // Card Payment
  Future<PaymentResult> processCardPayment({
    required String reference,
    required double amount,
    required String email,
    required String currency,
    required BuildContext context,
  }) async {
    try {
      // Create charge
      final charge = Charge()
        ..amount = (amount * 100).toInt() // Convert to kobo
        ..email = email
        ..reference = reference
        ..currency = currency;

      // Present Paystack UI and process payment
      final response = await _paystackPlugin.chargeCard(
        context: context,
        charge: charge,
      );

      if (response.status) {
        // Payment successful, verify with backend
        return PaymentResult(
          success: true,
          reference: reference,
          additionalData: {
            'authorizationCode': response.card?.authorizationCode,
            'cardType': response.card?.type,
            'last4': response.card?.last4,
          },
        );
      } else {
        // Payment failed
        return PaymentResult(
          success: false,
          reference: reference,
          errorMessage: response.message,
        );
      }
    } catch (e) {
      return PaymentResult(
        success: false,
        reference: reference,
        errorMessage: e.toString(),
      );
    }
  }

  // Bank Transfer Payment
  Future<PaymentResult> processBankTransfer({
    required String reference,
    required double amount,
    required String email,
    required String currency,
    required BuildContext context,
  }) async {
    try {
      // Create charge
      final charge = Charge()
        ..amount = (amount * 100).toInt()
        ..email = email
        ..reference = reference
        ..currency = currency;

      // Present Paystack Bank Transfer UI
      final response = await _paystackPlugin.chargeBank(
        context: context,
        charge: charge,
      );

      return PaymentResult(
        success: response.status,
        reference: reference,
        errorMessage: response.status ? null : response.message,
      );
    } catch (e) {
      return PaymentResult(
        success: false,
        reference: reference,
        errorMessage: e.toString(),
      );
    }
  }

  // USSD Payment
  Future<PaymentResult> processUssdPayment({
    required String reference,
    required double amount,
    required String email,
    required String currency,
    required String ussdCode,
    required BuildContext context,
  }) async {
    try {
      // Create charge
      final charge = Charge()
        ..amount = (amount * 100).toInt()
        ..email = email
        ..reference = reference
        ..currency = currency
        ..ussdCode = ussdCode;

      // Present Paystack USSD UI
      final response = await _paystackPlugin.chargeUSSD(
        context: context,
        charge: charge,
      );

      return PaymentResult(
        success: response.status,
        reference: reference,
        errorMessage: response.status ? null : response.message,
      );
    } catch (e) {
      return PaymentResult(
        success: false,
        reference: reference,
        errorMessage: e.toString(),
      );
    }
  }

  // Mobile Money Payment
  Future<PaymentResult> processMobileMoneyPayment({
    required String reference,
    required double amount,
    required String email,
    required String currency,
    required String phoneNumber,
    required String provider,
    required BuildContext context,
  }) async {
    try {
      // Create charge
      final charge = Charge()
        ..amount = (amount * 100).toInt()
        ..email = email
        ..reference = reference
        ..currency = currency
        ..mobileMoneyProvider = provider
        ..mobileMoneyPhoneNumber = phoneNumber;

      // Present Paystack Mobile Money UI
      final response = await _paystackPlugin.chargeMobileMoney(
        context: context,
        charge: charge,
      );

      return PaymentResult(
        success: response.status,
        reference: reference,
        errorMessage: response.status ? null : response.message,
      );
    } catch (e) {
      return PaymentResult(
        success: false,
        reference: reference,
        errorMessage: e.toString(),
      );
    }
  }
}
```

#### 3. Paystack Testing Tokens

Use these test card numbers for Paystack integration testing:

| Card Number         | Brand      | CVV          | Expiry Date     | PIN    | OTP    | Result                 |
|---------------------|------------|--------------|-----------------|--------|--------|------------------------|
| 5060 6666 6666 6666 666 | Mastercard | 123          | Any future date | 123456 | 123456 | Successful payment     |
| 5078 6666 6666 6666 666 | Verve      | 123          | Any future date | 123456 | 123456 | Successful payment     |
| 5078 6666 6666 6666 667 | Verve      | 123          | Any future date | 123456 | 123456 | Insufficient funds     |
| 5078 6666 6666 6666 668 | Verve      | 123          | Any future date | 123456 | 123456 | Card declined          |

For USSD testing, use any of the following codes:
- *737#
- *894#
- *919#
- *822#

For Mobile Money testing, use these providers:
- MTN
- Vodafone
- Airtel
- Tigo

#### 4. Paystack Webhook Verification

```python
# Backend implementation
def verify_paystack_signature(signature: str, payload: str) -> bool:
    try:
        # Get secret key from environment or configuration
        secret_key = settings.PAYSTACK_SECRET_KEY

        # Compute HMAC SHA512 hash
        computed_hmac = hmac.new(
            key=secret_key.encode(),
            msg=payload.encode(),
            digestmod=hashlib.sha512
        ).hexdigest()

        # Compare signatures
        return hmac.compare_digest(computed_hmac, signature)
    except Exception:
        return False
```

### Busha.co Integration Details

#### 1. Busha.co Backend Implementation

```python
# Backend implementation
class BushaService(PaymentProviderService):
    def __init__(self, config: BushaConfig):
        self.api_key = config.api_key
        self.secret_key = config.secret_key
        self.base_url = "https://commerce.busha.co/api/v1"

    async def initialize_payment(self, transaction: Transaction) -> Dict:
        try:
            # Prepare request data
            payload = {
                "title": f"Payment for booking {transaction.booking_id}",
                "description": f"CultureConnect booking payment",
                "local_price": {
                    "amount": str(transaction.amount),
                    "currency": transaction.currency
                },
                "pricing_type": "fixed_price",
                "metadata": {
                    "transaction_id": transaction.id,
                    "booking_id": transaction.booking_id,
                    "user_id": transaction.user_id
                },
                "redirect_url": f"{settings.APP_URL}/payments/crypto/complete?reference={transaction.id}",
                "cancel_url": f"{settings.APP_URL}/payments/crypto/cancel?reference={transaction.id}"
            }

            # Make API request to Busha
            headers = {
                "X-CC-Api-Key": self.api_key,
                "X-CC-Version": "2018-03-22",
                "Content-Type": "application/json"
            }

            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{self.base_url}/charges",
                    headers=headers,
                    json=payload
                ) as response:
                    result = await response.json()

                    if response.status != 201:
                        raise PaymentProviderError(result.get("error", {}).get("message", "Failed to initialize payment"))

                    # Update transaction with provider data
                    transaction.provider_reference = result["data"]["code"]
                    transaction.metadata = {
                        **transaction.metadata,
                        "hosted_url": result["data"]["hosted_url"],
                        "expires_at": result["data"]["expires_at"],
                        "addresses": result["data"]["addresses"]
                    }
                    await transaction_repository.update(transaction)

                    return {
                        "provider": "busha",
                        "reference": result["data"]["code"],
                        "hosted_url": result["data"]["hosted_url"],
                        "expires_at": result["data"]["expires_at"],
                        "addresses": result["data"]["addresses"],
                        "qr_codes": result["data"]["qr_codes"]
                    }
        except Exception as e:
            logger.error(f"Busha initialization error: {str(e)}")
            raise PaymentProviderError(f"Failed to initialize payment: {str(e)}")

    async def verify_transaction(self, transaction_id: str) -> VerificationResult:
        transaction = await transaction_repository.get_by_id(transaction_id)
        if not transaction:
            raise ValueError(f"Transaction not found: {transaction_id}")

        try:
            # Make API request to verify transaction
            headers = {
                "X-CC-Api-Key": self.api_key,
                "X-CC-Version": "2018-03-22",
                "Content-Type": "application/json"
            }

            async with aiohttp.ClientSession() as session:
                async with session.get(
                    f"{self.base_url}/charges/{transaction.provider_reference}",
                    headers=headers
                ) as response:
                    result = await response.json()

                    if response.status != 200:
                        return VerificationResult(
                            status="failed",
                            provider_reference=transaction.provider_reference,
                            message=result.get("error", {}).get("message", "Verification failed")
                        )

                    # Map Busha status to our status
                    busha_status = result["data"]["timeline"][-1]["status"]
                    status = self._map_busha_status(busha_status)

                    # Update transaction with provider data
                    if status == "successful":
                        transaction.status = status
                        transaction.metadata = {
                            **transaction.metadata,
                            "payment_details": result["data"]["payments"],
                            "timeline": result["data"]["timeline"]
                        }
                        await transaction_repository.update(transaction)

                    return VerificationResult(
                        status=status,
                        provider_reference=transaction.provider_reference,
                        message=f"Payment {status}",
                        details={
                            "amount": float(result["data"]["pricing"]["local"]["amount"]),
                            "currency": result["data"]["pricing"]["local"]["currency"],
                            "crypto_amount": result["data"]["pricing"]["crypto"],
                            "busha_status": busha_status
                        }
                    )
        except Exception as e:
            logger.error(f"Busha verification error: {str(e)}")
            return VerificationResult(
                status="failed",
                provider_reference=transaction.provider_reference,
                message=f"Verification failed: {str(e)}"
            )

    async def process_webhook(self, payload: Dict, headers: Dict) -> Dict:
        # Verify webhook signature
        signature = headers.get("X-CC-Webhook-Signature")
        timestamp = headers.get("X-CC-Webhook-Timestamp")
        if not signature or not timestamp or not self._verify_signature(signature, timestamp, json.dumps(payload)):
            raise ValueError("Invalid signature")

        try:
            # Handle different event types
            event_type = payload.get("type")

            if event_type == "charge:confirmed":
                await self._handle_payment_confirmed(payload["data"])
            elif event_type == "charge:failed":
                await self._handle_payment_failed(payload["data"])
            elif event_type == "charge:delayed":
                await self._handle_payment_delayed(payload["data"])
            elif event_type == "charge:pending":
                await self._handle_payment_pending(payload["data"])

            return {"status": "success", "event_type": event_type}
        except Exception as e:
            logger.error(f"Error processing Busha webhook: {str(e)}")
            raise

    def _map_busha_status(self, busha_status: str) -> str:
        status_map = {
            "NEW": "pending",
            "PENDING": "pending",
            "COMPLETED": "successful",
            "EXPIRED": "failed",
            "UNRESOLVED": "failed",
            "RESOLVED": "successful",
            "CANCELED": "failed"
        }
        return status_map.get(busha_status.upper(), "pending")

    def _verify_signature(self, signature: str, timestamp: str, payload: str) -> bool:
        """Verify that the webhook payload was sent by Busha"""
        message = f"{timestamp}.{payload}"
        computed_signature = hmac.new(
            key=self.secret_key.encode(),
            msg=message.encode(),
            digestmod=hashlib.sha256
        ).hexdigest()
        return hmac.compare_digest(computed_signature, signature)

    async def _handle_payment_confirmed(self, data: Dict):
        # Find transaction by code
        code = data.get("code")
        transaction = await transaction_repository.get_by_provider_reference(code)
        if not transaction:
            logger.error(f"Transaction not found for code: {code}")
            return

        # Update transaction status
        transaction.status = "successful"
        transaction.updated_at = datetime.utcnow()
        transaction.metadata = {
            **transaction.metadata,
            "payment_details": data.get("payments"),
            "timeline": data.get("timeline")
        }
        await transaction_repository.update(transaction)

        # Generate receipt
        await receipt_service.generate_receipt(transaction)

    async def _handle_payment_failed(self, data: Dict):
        # Find transaction by code
        code = data.get("code")
        transaction = await transaction_repository.get_by_provider_reference(code)
        if not transaction:
            logger.error(f"Transaction not found for code: {code}")
            return

        # Update transaction status
        transaction.status = "failed"
        transaction.updated_at = datetime.utcnow()
        transaction.metadata = {
            **transaction.metadata,
            "timeline": data.get("timeline")
        }
        await transaction_repository.update(transaction)

    async def _handle_payment_delayed(self, data: Dict):
        # Find transaction by code
        code = data.get("code")
        transaction = await transaction_repository.get_by_provider_reference(code)
        if not transaction:
            logger.error(f"Transaction not found for code: {code}")
            return

        # Update transaction status
        transaction.status = "processing"
        transaction.updated_at = datetime.utcnow()
        transaction.metadata = {
            **transaction.metadata,
            "timeline": data.get("timeline")
        }
        await transaction_repository.update(transaction)

    async def _handle_payment_pending(self, data: Dict):
        # Find transaction by code
        code = data.get("code")
        transaction = await transaction_repository.get_by_provider_reference(code)
        if not transaction:
            logger.error(f"Transaction not found for code: {code}")
            return

        # Update transaction status
        transaction.status = "pending"
        transaction.updated_at = datetime.utcnow()
        transaction.metadata = {
            **transaction.metadata,
            "timeline": data.get("timeline")
        }
        await transaction_repository.update(transaction)
```

#### 2. Busha.co Mobile Integration

```dart
// Flutter implementation
class BushaPaymentService {
  final ApiService _apiService;

  BushaPaymentService(this._apiService);

  Future<PaymentResult> initiateCryptoPayment({
    required String bookingId,
    required double amount,
    required String currency,
    required String email,
  }) async {
    try {
      // Call backend to initialize payment
      final response = await _apiService.post(
        '/payments/crypto/initialize',
        data: {
          'booking_id': bookingId,
          'amount': amount,
          'currency': currency,
          'email': email,
        },
      );

      if (response.statusCode != 200) {
        return PaymentResult(
          success: false,
          reference: '',
          errorMessage: 'Failed to initialize crypto payment',
        );
      }

      return PaymentResult(
        success: true,
        reference: response.data['reference'],
        additionalData: {
          'hosted_url': response.data['hosted_url'],
          'expires_at': response.data['expires_at'],
          'addresses': response.data['addresses'],
          'qr_codes': response.data['qr_codes'],
        },
      );
    } catch (e) {
      return PaymentResult(
        success: false,
        reference: '',
        errorMessage: e.toString(),
      );
    }
  }

  Future<PaymentStatus> checkPaymentStatus(String reference) async {
    try {
      final response = await _apiService.get(
        '/payments/crypto/status/$reference',
      );

      if (response.statusCode != 200) {
        return PaymentStatus(
          status: 'failed',
          message: 'Failed to check payment status',
        );
      }

      return PaymentStatus(
        status: response.data['status'],
        message: response.data['message'],
        details: response.data['details'],
      );
    } catch (e) {
      return PaymentStatus(
        status: 'error',
        message: e.toString(),
      );
    }
  }
}
```

#### 3. Busha.co Testing

For testing Busha.co integration, use the following cryptocurrencies in test mode:

| Cryptocurrency | Network       | Test Amount | Expected Result    |
|----------------|---------------|-------------|-------------------|
| BTC            | Bitcoin       | Any         | Always successful  |
| ETH            | Ethereum      | Any         | Always successful  |
| USDT           | Ethereum ERC20| Any         | Always successful  |
| USDC           | Ethereum ERC20| Any         | Always successful  |

#### 4. Busha.co Webhook Verification

```python
# Backend implementation
def verify_busha_signature(signature: str, timestamp: str, payload: str) -> bool:
    try:
        # Get secret key from environment or configuration
        secret_key = settings.BUSHA_SECRET_KEY

        # Create message string
        message = f"{timestamp}.{payload}"

        # Compute HMAC SHA256 hash
        computed_signature = hmac.new(
            key=secret_key.encode(),
            msg=message.encode(),
            digestmod=hashlib.sha256
        ).hexdigest()

        # Compare signatures
        return hmac.compare_digest(computed_signature, signature)
    except Exception:
        return False
```

2. **Geolocation Permission Issues**:
   - **Issue**: Unable to get user location
   - **Solution**: Implement proper permission handling and fallback mechanisms
   - **Example**: Use `Geolocator.checkPermission()` and `Geolocator.requestPermission()`

3. **PDF Generation Issues**:
   - **Issue**: PDF generation fails
   - **Solution**: Ensure proper font loading and file path handling
   - **Example**: Use `rootBundle.load('assets/fonts/Roboto-Regular.ttf')` to load fonts

4. **Payment Processing Errors**:
   - **Issue**: Payment provider returns error
   - **Solution**: Implement proper error handling and user feedback
   - **Example**: Check for specific error codes and display appropriate messages

5. **UI State Management Issues**:
   - **Issue**: UI doesn't update after state changes
   - **Solution**: Ensure proper state management and widget rebuilding
   - **Example**: Use `setState()` or state management libraries like Riverpod

6. **Backward Compatibility Issues**:
   - **Issue**: Legacy code breaks with new models
   - **Solution**: Use adapter classes to convert between model formats
   - **Example**: Implement `fromLegacy()` and `toLegacy()` methods in adapter classes

7. **Test Failures**:
   - **Issue**: Tests fail after implementation changes
   - **Solution**: Update test expectations and mock implementations
   - **Example**: Update mock responses to match new model structures

## Error Handling and Logging

### Standardized Error Response Format

Implement a consistent error response format across all payment methods:

```dart
// Flutter implementation
class PaymentError {
  final String code;
  final String message;
  final String userMessage;
  final Map<String, dynamic>? details;
  final String? providerErrorCode;
  final String? providerErrorMessage;
  final bool isRecoverable;

  const PaymentError({
    required this.code,
    required this.message,
    required this.userMessage,
    this.details,
    this.providerErrorCode,
    this.providerErrorMessage,
    this.isRecoverable = false,
  });

  factory PaymentError.fromJson(Map<String, dynamic> json) {
    return PaymentError(
      code: json['code'] as String,
      message: json['message'] as String,
      userMessage: json['user_message'] as String,
      details: json['details'] as Map<String, dynamic>?,
      providerErrorCode: json['provider_error_code'] as String?,
      providerErrorMessage: json['provider_error_message'] as String?,
      isRecoverable: json['is_recoverable'] as bool? ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'code': code,
      'message': message,
      'user_message': userMessage,
      if (details != null) 'details': details,
      if (providerErrorCode != null) 'provider_error_code': providerErrorCode,
      if (providerErrorMessage != null) 'provider_error_message': providerErrorMessage,
      'is_recoverable': isRecoverable,
    };
  }
}

// Backend implementation
class PaymentErrorResponse(BaseModel):
    code: str
    message: str
    user_message: str
    details: Optional[Dict[str, Any]] = None
    provider_error_code: Optional[str] = None
    provider_error_message: Optional[str] = None
    is_recoverable: bool = False

    class Config:
        schema_extra = {
            "example": {
                "code": "payment_failed",
                "message": "Payment processing failed due to insufficient funds",
                "user_message": "Your payment could not be processed. Please check your account balance and try again.",
                "details": {
                    "transaction_id": "TX-********",
                    "amount": 100.00,
                    "currency": "USD"
                },
                "provider_error_code": "insufficient_funds",
                "provider_error_message": "The card has insufficient funds to complete the purchase",
                "is_recoverable": True
            }
        }
```

### Error Code Mapping

Implement provider-specific error code mapping:

```python
# Backend implementation
STRIPE_ERROR_MAPPING = {
    "card_declined": {
        "code": "payment_failed",
        "message": "Card was declined",
        "user_message": "Your card was declined. Please try another payment method.",
        "is_recoverable": True
    },
    "insufficient_funds": {
        "code": "payment_failed",
        "message": "Insufficient funds",
        "user_message": "Your card has insufficient funds. Please try another card or payment method.",
        "is_recoverable": True
    },
    "expired_card": {
        "code": "payment_failed",
        "message": "Card expired",
        "user_message": "Your card has expired. Please try another card.",
        "is_recoverable": True
    },
    "authentication_required": {
        "code": "authentication_required",
        "message": "Authentication required",
        "user_message": "This payment requires authentication. Please complete the authentication process.",
        "is_recoverable": True
    },
    "rate_limit": {
        "code": "service_unavailable",
        "message": "Rate limit exceeded",
        "user_message": "Our payment service is temporarily unavailable. Please try again in a few moments.",
        "is_recoverable": True
    },
    "invalid_request_error": {
        "code": "invalid_request",
        "message": "Invalid request parameters",
        "user_message": "There was a problem with your payment information. Please check your details and try again.",
        "is_recoverable": True
    },
    "api_connection_error": {
        "code": "service_unavailable",
        "message": "Connection to payment service failed",
        "user_message": "We're having trouble connecting to our payment service. Please try again later.",
        "is_recoverable": True
    },
    "api_error": {
        "code": "service_unavailable",
        "message": "Payment service error",
        "user_message": "Our payment service is experiencing issues. Please try again later.",
        "is_recoverable": True
    },
    "authentication_error": {
        "code": "service_unavailable",
        "message": "Authentication with payment service failed",
        "user_message": "We're having trouble with our payment service. Please try again later.",
        "is_recoverable": False
    },
    "idempotency_error": {
        "code": "duplicate_request",
        "message": "Duplicate payment request",
        "user_message": "This payment request has already been processed. Please check your payment history.",
        "is_recoverable": False
    }
}

PAYSTACK_ERROR_MAPPING = {
    "insufficient_funds": {
        "code": "payment_failed",
        "message": "Insufficient funds",
        "user_message": "Your card has insufficient funds. Please try another card or payment method.",
        "is_recoverable": True
    },
    "authorization_failed": {
        "code": "payment_failed",
        "message": "Authorization failed",
        "user_message": "Your payment authorization failed. Please try another payment method.",
        "is_recoverable": True
    },
    "transaction_error": {
        "code": "payment_failed",
        "message": "Transaction processing error",
        "user_message": "We couldn't process your payment. Please try again or use another payment method.",
        "is_recoverable": True
    },
    "gateway_timeout": {
        "code": "service_unavailable",
        "message": "Payment gateway timeout",
        "user_message": "The payment is taking longer than expected. Please check your payment status later.",
        "is_recoverable": True
    },
    "expired_access_code": {
        "code": "session_expired",
        "message": "Payment session expired",
        "user_message": "Your payment session has expired. Please try again.",
        "is_recoverable": True
    },
    "invalid_transaction": {
        "code": "invalid_request",
        "message": "Invalid transaction parameters",
        "user_message": "There was a problem with your payment information. Please check your details and try again.",
        "is_recoverable": True
    }
}

BUSHA_ERROR_MAPPING = {
    "charge_expired": {
        "code": "payment_failed",
        "message": "Charge expired",
        "user_message": "The payment time window has expired. Please try again.",
        "is_recoverable": True
    },
    "insufficient_amount": {
        "code": "payment_failed",
        "message": "Insufficient amount sent",
        "user_message": "The amount sent was less than required. Please check the payment details and try again.",
        "is_recoverable": True
    },
    "unresolved_charge": {
        "code": "payment_pending",
        "message": "Unresolved charge",
        "user_message": "Your payment is being processed. We'll notify you once it's complete.",
        "is_recoverable": False
    },
    "invalid_address": {
        "code": "invalid_request",
        "message": "Invalid crypto address",
        "user_message": "The cryptocurrency address is invalid. Please check the address and try again.",
        "is_recoverable": True
    },
    "network_congestion": {
        "code": "payment_delayed",
        "message": "Network congestion",
        "user_message": "The cryptocurrency network is congested. Your payment may take longer to process.",
        "is_recoverable": False
    }
}

def map_provider_error(provider: str, error_code: str, error_message: str) -> PaymentErrorResponse:
    """Map provider-specific error codes to standardized error responses"""
    if provider == "stripe":
        mapping = STRIPE_ERROR_MAPPING.get(error_code, None)
    elif provider == "paystack":
        mapping = PAYSTACK_ERROR_MAPPING.get(error_code, None)
    elif provider == "busha":
        mapping = BUSHA_ERROR_MAPPING.get(error_code, None)
    else:
        mapping = None

    if mapping:
        return PaymentErrorResponse(
            code=mapping["code"],
            message=mapping["message"],
            user_message=mapping["user_message"],
            provider_error_code=error_code,
            provider_error_message=error_message,
            is_recoverable=mapping["is_recoverable"]
        )

    # Default error response for unknown error codes
    return PaymentErrorResponse(
        code="payment_failed",
        message=f"Payment processing failed: {error_message}",
        user_message="We couldn't process your payment. Please try again or contact support.",
        provider_error_code=error_code,
        provider_error_message=error_message,
        is_recoverable=True
    )
```

### User-facing Error Messages

Implement consistent user-facing error messages:

```dart
// Flutter implementation
class PaymentErrorDisplay extends StatelessWidget {
  final PaymentError error;
  final VoidCallback? onRetry;

  const PaymentErrorDisplay({
    Key? key,
    required this.error,
    this.onRetry,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(
          Icons.error_outline,
          color: Theme.of(context).colorScheme.error,
          size: 48,
        ),
        const SizedBox(height: 16),
        Text(
          error.userMessage,
          textAlign: TextAlign.center,
          style: Theme.of(context).textTheme.bodyLarge,
        ),
        const SizedBox(height: 24),
        if (error.isRecoverable && onRetry != null)
          ElevatedButton(
            onPressed: onRetry,
            child: const Text('Try Again'),
          ),
        const SizedBox(height: 8),
        TextButton(
          onPressed: () {
            // Navigate to payment method selection
            Navigator.of(context).pop();
          },
          child: const Text('Choose Another Payment Method'),
        ),
      ],
    );
  }
}
```

### Detailed Logging Requirements

Implement comprehensive logging for payment operations:

```python
# Backend implementation
class PaymentLogger:
    def __init__(self, logger_name: str = "payment_service"):
        self.logger = logging.getLogger(logger_name)

    def log_payment_attempt(self, transaction_id: str, provider: str, amount: float, currency: str, user_id: str):
        """Log payment attempt (NEVER log full card details or security codes)"""
        self.logger.info(
            f"Payment attempt initiated",
            extra={
                "transaction_id": transaction_id,
                "provider": provider,
                "amount": amount,
                "currency": currency,
                "user_id": user_id,
                "event_type": "payment_attempt"
            }
        )

    def log_payment_success(self, transaction_id: str, provider: str, provider_reference: str):
        """Log successful payment"""
        self.logger.info(
            f"Payment successful",
            extra={
                "transaction_id": transaction_id,
                "provider": provider,
                "provider_reference": provider_reference,
                "event_type": "payment_success"
            }
        )

    def log_payment_failure(self, transaction_id: str, provider: str, error_code: str, error_message: str):
        """Log payment failure"""
        self.logger.warning(
            f"Payment failed",
            extra={
                "transaction_id": transaction_id,
                "provider": provider,
                "error_code": error_code,
                "error_message": error_message,
                "event_type": "payment_failure"
            }
        )

    def log_refund_attempt(self, transaction_id: str, refund_id: str, amount: float, reason: str):
        """Log refund attempt"""
        self.logger.info(
            f"Refund attempt initiated",
            extra={
                "transaction_id": transaction_id,
                "refund_id": refund_id,
                "amount": amount,
                "reason": reason,
                "event_type": "refund_attempt"
            }
        )

    def log_refund_success(self, transaction_id: str, refund_id: str, provider_reference: str):
        """Log successful refund"""
        self.logger.info(
            f"Refund successful",
            extra={
                "transaction_id": transaction_id,
                "refund_id": refund_id,
                "provider_reference": provider_reference,
                "event_type": "refund_success"
            }
        )

    def log_refund_failure(self, transaction_id: str, refund_id: str, error_code: str, error_message: str):
        """Log refund failure"""
        self.logger.warning(
            f"Refund failed",
            extra={
                "transaction_id": transaction_id,
                "refund_id": refund_id,
                "error_code": error_code,
                "error_message": error_message,
                "event_type": "refund_failure"
            }
        )

    def log_webhook_received(self, provider: str, event_type: str, payload_size: int):
        """Log webhook receipt (NEVER log full webhook payload as it may contain sensitive data)"""
        self.logger.info(
            f"Webhook received from {provider}",
            extra={
                "provider": provider,
                "event_type": event_type,
                "payload_size": payload_size,
                "event_type": "webhook_received"
            }
        )

    def log_webhook_processed(self, provider: str, event_type: str, transaction_id: str = None):
        """Log webhook processing result"""
        self.logger.info(
            f"Webhook processed successfully",
            extra={
                "provider": provider,
                "event_type": event_type,
                "transaction_id": transaction_id,
                "event_type": "webhook_processed"
            }
        )

    def log_webhook_error(self, provider: str, event_type: str, error_message: str):
        """Log webhook processing error"""
        self.logger.error(
            f"Webhook processing failed",
            extra={
                "provider": provider,
                "event_type": event_type,
                "error_message": error_message,
                "event_type": "webhook_error"
            }
        )
```

### Security Logging Considerations

Follow these guidelines for secure payment logging:

1. **Never Log Sensitive Data**:
   - Never log full card numbers (only last 4 digits if needed)
   - Never log CVV/CVC codes
   - Never log card expiry dates
   - Never log API keys or secrets
   - Never log personal identifiable information (PII) beyond user IDs

2. **Log Retention Policy**:
   - Transaction logs: 2 years
   - Error logs: 1 year
   - Debug logs: 30 days
   - Webhook logs: 1 year

3. **Log Access Control**:
   - Implement role-based access control for log access
   - Maintain audit trail of log access
   - Encrypt logs at rest
   - Use secure transport for log transmission

### Health Check Endpoints

Implement health check endpoints for monitoring:

```python
# Backend implementation
@router.get("/health/payment-service")
async def payment_service_health():
    """Health check endpoint for payment service"""
    try:
        # Check database connectivity
        await transaction_repository.ping()

        # Check provider connectivity
        provider_health = {}
        for provider_name in ["stripe", "paystack", "busha"]:
            try:
                provider = get_provider_service(provider_name)
                provider_status = await provider.ping()
                provider_health[provider_name] = {
                    "status": "healthy" if provider_status else "degraded",
                    "response_time_ms": provider_status.get("response_time_ms") if provider_status else None
                }
            except Exception as e:
                provider_health[provider_name] = {
                    "status": "unhealthy",
                    "error": str(e)
                }

        # Overall status is healthy only if database and at least one provider is healthy
        overall_status = "healthy"
        if not any(p["status"] == "healthy" for p in provider_health.values()):
            overall_status = "degraded"

        return {
            "status": overall_status,
            "timestamp": datetime.utcnow().isoformat(),
            "components": {
                "database": {
                    "status": "healthy"
                },
                "providers": provider_health
            }
        }
    except Exception as e:
        return {
            "status": "unhealthy",
            "timestamp": datetime.utcnow().isoformat(),
            "error": str(e)
        }
```

### Recovery Procedures

Implement step-by-step recovery procedures for common failure scenarios:

#### 1. Network Disconnection Recovery

```dart
// Flutter implementation
Future<void> recoverFromNetworkDisconnection(String transactionId) async {
  // Step 1: Check if payment was actually processed
  final result = await _paymentService.checkTransactionStatus(transactionId);

  if (result.status == "successful") {
    // Payment was successful despite network error
    await _navigationService.navigateTo(
      PaymentSuccessScreen.routeName,
      arguments: PaymentSuccessArgs(
        transactionId: transactionId,
        receipt: result.receipt,
      ),
    );
    return;
  }

  if (result.status == "processing") {
    // Payment is still processing
    await _dialogService.showDialog(
      title: 'Payment Processing',
      description: 'Your payment is being processed. We will notify you once it completes.',
      buttonTitle: 'OK',
    );
    return;
  }

  if (result.status == "failed") {
    // Payment failed
    await _dialogService.showDialog(
      title: 'Payment Failed',
      description: result.message,
      buttonTitle: 'Try Again',
    );
    return;
  }

  // Payment was not processed
  final shouldRetry = await _dialogService.showConfirmationDialog(
    title: 'Connection Lost',
    description: 'Your payment was interrupted due to a network issue. Would you like to try again?',
    confirmationTitle: 'Try Again',
    cancelTitle: 'Cancel',
  );

  if (shouldRetry.confirmed) {
    // Retry payment
    await _paymentService.retryPayment(transactionId);
  }
}
```

#### 2. Provider Downtime Recovery

```python
# Backend implementation
async def recover_from_provider_downtime(transaction_id: str):
    """Recover from provider downtime"""
    # Step 1: Get the transaction
    transaction = await transaction_repository.get_by_id(transaction_id)
    if not transaction:
        raise ValueError(f"Transaction not found: {transaction_id}")

    # Step 2: Check if the primary provider is available
    primary_provider_name = transaction.provider
    primary_provider_health = await check_provider_health(primary_provider_name)

    if primary_provider_health["status"] == "healthy":
        # Primary provider is back online, verify the transaction
        provider = get_provider_service(primary_provider_name)
        verification_result = await provider.verify_transaction(transaction_id)

        # Update transaction status
        transaction.status = verification_result.status
        await transaction_repository.update(transaction)

        return verification_result

    # Step 3: If primary provider is still down, check for alternative providers
    available_providers = await get_available_providers(transaction.user_country_code)

    if not available_providers:
        # No providers available, mark transaction as pending
        transaction.status = "pending"
        transaction.metadata = {
            **transaction.metadata,
            "recovery_attempts": transaction.metadata.get("recovery_attempts", 0) + 1,
            "last_recovery_attempt": datetime.utcnow().isoformat()
        }
        await transaction_repository.update(transaction)

        return {
            "status": "pending",
            "message": "All payment providers are currently unavailable. We will process your payment once services are restored."
        }

    # Step 4: If alternative provider is available, create a new transaction
    alternative_provider_name = available_providers[0]

    # Create new transaction with alternative provider
    new_transaction = Transaction(
        id=f"TX-{uuid.uuid4().hex[:8].upper()}",
        user_id=transaction.user_id,
        booking_id=transaction.booking_id,
        amount=transaction.amount,
        currency=transaction.currency,
        provider=alternative_provider_name,
        status="pending",
        metadata={
            **transaction.metadata,
            "original_transaction_id": transaction.id,
            "provider_switch_reason": "provider_downtime"
        }
    )
    await transaction_repository.save(new_transaction)

    # Mark original transaction as failed
    transaction.status = "failed"
    transaction.metadata = {
        **transaction.metadata,
        "recovery_transaction_id": new_transaction.id,
        "failure_reason": "provider_downtime"
    }
    await transaction_repository.update(transaction)

    # Initialize payment with alternative provider
    alternative_provider = get_provider_service(alternative_provider_name)
    initialization_result = await alternative_provider.initialize_payment(new_transaction)

    return {
        "status": "provider_switched",
        "message": f"Payment provider switched to {alternative_provider_name}",
        "new_transaction_id": new_transaction.id,
        "initialization_result": initialization_result
    }
```

## Timeline and Effort Estimation

### Overall Timeline

The complete payment system implementation is estimated to take approximately 35 working days (7 weeks) with one developer working full-time. This can be accelerated with multiple developers working in parallel on different components.

### Effort Estimation by Phase

| Phase | Description | Estimated Days | Effort Level | Dependencies |
|-------|-------------|----------------|--------------|--------------|
| 1 | Foundation | 5 | High | None |
| 2 | Core Services | 5 | High | Phase 1 |
| 3 | Core UI | 5 | Medium | Phase 2 |
| 4 | Additional Providers | 5 | Medium | Phase 2 |
| 5 | PDF Generation | 3 | Medium | Phase 2 |
| 6 | Additional UI | 4 | Medium | Phase 3, 4, 5 |
| 7 | Testing | 5 | High | All previous phases |
| 8 | Advanced Features | 3 | Medium | Phase 2, 3 |

### Detailed Timeline

#### Week 1 (Days 1-5)
- **Days 1-2**: Update dependencies, implement base models
- **Days 3-5**: Implement specialized models, storage service, API service

#### Week 2 (Days 6-10)
- **Days 6-8**: Implement payment service core, provider framework
- **Days 9-10**: Implement Stripe payment provider

#### Week 3 (Days 11-15)
- **Days 11-15**: Implement core UI components (forms, selectors, screens)

#### Week 4 (Days 16-20)
- **Days 16-20**: Implement additional payment providers and geolocation routing

#### Week 5 (Days 21-27)
- **Days 21-23**: Implement PDF generation
- **Days 24-27**: Implement additional UI components

#### Week 6 (Days 28-32)
- **Days 28-32**: Implement testing (mocks, unit tests, widget tests, integration tests)

#### Week 7 (Days 33-35)
- **Days 33-35**: Implement advanced features (Busha.co crypto payments)

### Critical Path

The critical path for implementation is:
1. Foundation (Phase 1)
2. Core Services (Phase 2)
3. Core UI (Phase 3)
4. Testing (Phase 7)

Additional providers, PDF generation, and advanced features can be implemented in parallel after the core services are in place.

### Resource Allocation

For optimal implementation speed, resources can be allocated as follows:

- **Developer 1**: Foundation, Core Services, Testing
- **Developer 2**: Core UI, Additional UI
- **Developer 3**: Additional Providers, PDF Generation, Advanced Features

With this allocation, the implementation timeline can be reduced to approximately 15-20 working days.

### Milestones and Deliverables

1. **Milestone 1 (End of Week 1)**: Foundation complete
   - Deliverable: Models, storage service, API service

2. **Milestone 2 (End of Week 2)**: Core services complete
   - Deliverable: Payment service, provider framework, Stripe integration

3. **Milestone 3 (End of Week 3)**: Core UI complete
   - Deliverable: Payment forms, screens, and components

4. **Milestone 4 (End of Week 5)**: Additional features complete
   - Deliverable: Additional providers, geolocation routing, PDF generation, additional UI

5. **Milestone 5 (End of Week 7)**: Testing and advanced features complete
   - Deliverable: Complete payment system with testing and Busha.co crypto payment integration

## Additional Documentation Requirements

Before beginning implementation, the following additional documentation should be prepared:

### 1. API Contract Documentation

- [ ] **OpenAPI/Swagger Documentation**
  - Complete API specification for all payment-related endpoints
  - Request/response schemas
  - Authentication requirements
  - Error response formats
  - Example requests and responses

- [ ] **API Integration Guide**
  - Step-by-step guide for integrating with the payment API
  - Authentication and authorization flow
  - Webhook integration instructions
  - Rate limiting and throttling information

### 2. Provider-specific Integration Guides

- [ ] **Stripe Integration Guide**
  - Account setup instructions
  - API key management
  - Webhook configuration
  - Testing procedures
  - Production migration checklist

- [ ] **Paystack Integration Guide**
  - Account setup instructions
  - API key management
  - Webhook configuration
  - Testing procedures
  - Production migration checklist
  - Regional considerations for African markets

- [ ] **Busha.co Integration Guide**
  - Account setup instructions
  - API key management
  - Webhook configuration
  - Testing procedures
  - Production migration checklist
  - Cryptocurrency compliance considerations

### 3. Database Schema Documentation

- [ ] **Entity Relationship Diagram**
  - Complete diagram showing all payment-related tables
  - Relationships between entities
  - Primary and foreign keys
  - Indexes and constraints

- [ ] **Schema Migration Plan**
  - Version control strategy for database schema
  - Migration scripts for schema changes
  - Rollback procedures
  - Data migration strategy

### 4. Security Compliance Documentation

- [ ] **PCI DSS Compliance Checklist**
  - Requirements for each PCI DSS control
  - Implementation status
  - Evidence collection procedures
  - Audit preparation guidelines

- [ ] **Data Protection Impact Assessment**
  - Identification of personal data processed
  - Risk assessment for data processing activities
  - Technical and organizational measures
  - Data retention and deletion procedures

### 5. Testing Documentation

- [ ] **Test Plan**
  - Test strategy overview
  - Test environments
  - Test data management
  - Test execution procedures

- [ ] **Test Cases**
  - Unit test cases for all components
  - Integration test cases for payment flows
  - Performance test scenarios
  - Security test cases

- [ ] **Test Data**
  - Test card numbers for each provider
  - Test account credentials
  - Mock response data
  - Test environment configuration

### 6. Operational Documentation

- [ ] **Deployment Guide**
  - Environment setup instructions
  - Deployment procedures
  - Configuration management
  - Rollback procedures

- [ ] **Monitoring Guide**
  - Key metrics to monitor
  - Alert thresholds
  - Incident response procedures
  - Log analysis guidelines

- [ ] **Disaster Recovery Plan**
  - Backup procedures
  - Recovery time objectives
  - Recovery point objectives
  - Failover procedures

### 7. User Documentation

- [ ] **Payment Flow Guide**
  - Step-by-step guide for users
  - Screenshots of payment screens
  - Troubleshooting tips
  - FAQ section

- [ ] **Admin Guide**
  - Transaction management procedures
  - Refund processing instructions
  - Dispute handling procedures
  - Reporting capabilities
