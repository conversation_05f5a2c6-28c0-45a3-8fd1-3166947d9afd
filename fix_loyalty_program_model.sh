#!/bin/bash

# Script to fix the loyalty_program_model.dart file
# This script will:
# 1. Add imports for LoyaltyPointsTransaction and LoyaltyReward
# 2. Fix the null safety issues in toJson method

if [ -f "lib/models/loyalty/loyalty_program_model.dart" ]; then
  # Add imports
  sed -i '' '1s/^/import '\''package:culture_connect\/models\/loyalty\/loyalty_points_transaction.dart'\'';\nimport '\''package:culture_connect\/models\/loyalty\/loyalty_reward.dart'\'';\n/' "lib/models/loyalty/loyalty_program_model.dart"
  
  # Fix null safety issues in toJson method
  sed -i '' 's/recentTransactions.toJson()/recentTransactions?.toJson() ?? \[\]/g' "lib/models/loyalty/loyalty_program_model.dart"
  sed -i '' 's/availableRewards.toJson()/availableRewards?.toJson() ?? \[\]/g' "lib/models/loyalty/loyalty_program_model.dart"
  sed -i '' 's/redeemedRewards.toJson()/redeemedRewards?.toJson() ?? \[\]/g' "lib/models/loyalty/loyalty_program_model.dart"
  
  echo "Fixed loyalty_program_model.dart"
else
  echo "loyalty_program_model.dart not found"
fi
