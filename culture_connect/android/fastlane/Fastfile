default_platform(:android)

platform :android do
  desc "Build APK file"
  lane :build_apk do
    gradle(
      task: "clean assembleRelease",
      project_dir: "./"
    )
  end

  desc "Build App Bundle"
  lane :build_bundle do
    gradle(
      task: "clean bundleRelease",
      project_dir: "./"
    )
  end

  desc "Deploy to Google Play Internal Testing"
  lane :internal do
    # Build the app bundle
    gradle(
      task: "clean bundleRelease",
      project_dir: "./"
    )

    # Upload to Google Play Internal Testing
    upload_to_play_store(
      track: "internal",
      aab: "../build/app/outputs/bundle/release/app-release.aab",
      skip_upload_metadata: true,
      skip_upload_images: true,
      skip_upload_screenshots: true,
      json_key: ENV["GOOGLE_PLAY_JSON_KEY_FILE"]
    )
  end

  desc "Deploy to Google Play Beta"
  lane :beta do
    # Build the app bundle
    gradle(
      task: "clean bundleRelease",
      project_dir: "./"
    )

    # Upload to Google Play Beta
    upload_to_play_store(
      track: "beta",
      aab: "../build/app/outputs/bundle/release/app-release.aab",
      skip_upload_metadata: true,
      skip_upload_images: true,
      skip_upload_screenshots: true,
      json_key: ENV["GOOGLE_PLAY_JSON_KEY_FILE"]
    )
  end

  desc "Deploy to Google Play Production"
  lane :release do
    # Build the app bundle
    gradle(
      task: "clean bundleRelease",
      project_dir: "./"
    )

    # Upload to Google Play Production
    upload_to_play_store(
      track: "production",
      aab: "../build/app/outputs/bundle/release/app-release.aab",
      skip_upload_metadata: false,
      skip_upload_images: false,
      skip_upload_screenshots: false,
      json_key: ENV["GOOGLE_PLAY_JSON_KEY_FILE"]
    )
  end
end
