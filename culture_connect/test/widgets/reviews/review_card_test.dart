import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:culture_connect/models/review.dart';
import 'package:culture_connect/widgets/reviews/review_card.dart';

class MockReviewService extends Mock {
  Future<ReviewModel> markReviewAsHelpful(String reviewId, String userId) async {
    return review.copyWith(
      helpfulCount: review.helpfulCount + 1,
      helpfulUserIds: [...review.helpfulUserIds, userId],
    );
  }
}

final review = ReviewModel(
  id: 'review1',
  experienceId: 'exp1',
  userId: 'user1',
  userName: '<PERSON>',
  userProfileImageUrl: 'https://example.com/profile.jpg',
  rating: 4.5,
  content: 'This was an amazing experience! The guide was very knowledgeable and friendly. '
      'I learned a lot about the local culture and history. Highly recommended!',
  datePosted: DateTime.now().subtract(const Duration(days: 3)),
  helpfulCount: 5,
  helpfulUserIds: ['user2', 'user3', 'user4', 'user5', 'user6'],
  photoUrls: ['https://example.com/photo1.jpg', 'https://example.com/photo2.jpg'],
  tags: ['Informative', 'Friendly Guide'],
);

final reviewWithGuideResponse = review.copyWith(
  guideResponse: GuideResponse(
    guideId: 'guide1',
    guideName: 'Tour Guide',
    content: 'Thank you for your kind review! We\'re glad you enjoyed the experience.',
    datePosted: DateTime.now().subtract(const Duration(days: 2)),
  ),
);

void main() {
  group('ReviewCard Widget Tests', () {
    testWidgets('renders basic review information', (WidgetTester tester) async {
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: Scaffold(
              body: ReviewCard(
                review: review,
              ),
            ),
          ),
        ),
      );
      
      // Verify that basic review information is displayed
      expect(find.text(review.userName), findsOneWidget);
      expect(find.text(review.content), findsOneWidget);
      
      // Verify that the rating is displayed
      expect(find.byIcon(Icons.star_rounded), findsAtLeastNWidgets(4));
      
      // Verify that helpful count is displayed
      expect(find.text('5 people found this helpful'), findsOneWidget);
      
      // Verify that tags are displayed
      expect(find.text('Informative'), findsOneWidget);
      expect(find.text('Friendly Guide'), findsOneWidget);
    });
    
    testWidgets('renders guide response when present', (WidgetTester tester) async {
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: Scaffold(
              body: ReviewCard(
                review: reviewWithGuideResponse,
              ),
            ),
          ),
        ),
      );
      
      // Verify that guide response is displayed
      expect(find.text('Response from Tour Guide'), findsOneWidget);
      expect(find.text('Thank you for your kind review! We\'re glad you enjoyed the experience.'), findsOneWidget);
    });
    
    testWidgets('expands and collapses content on tap', (WidgetTester tester) async {
      // Create a review with long content
      final longReview = review.copyWith(
        content: 'This is a very long review that should be truncated initially. ' * 10,
      );
      
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: Scaffold(
              body: ReviewCard(
                review: longReview,
              ),
            ),
          ),
        ),
      );
      
      // Verify that "Read more" button is displayed
      expect(find.text('Read more'), findsOneWidget);
      
      // Tap the "Read more" button
      await tester.tap(find.text('Read more'));
      await tester.pump();
      
      // Verify that "Show less" button is now displayed
      expect(find.text('Show less'), findsOneWidget);
      
      // Tap the "Show less" button
      await tester.tap(find.text('Show less'));
      await tester.pump();
      
      // Verify that "Read more" button is displayed again
      expect(find.text('Read more'), findsOneWidget);
    });
    
    testWidgets('shows photos when present', (WidgetTester tester) async {
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: Scaffold(
              body: ReviewCard(
                review: review,
              ),
            ),
          ),
        ),
      );
      
      // Verify that photos are displayed
      expect(find.byType(ListView), findsOneWidget);
      
      // There should be 2 photo containers
      expect(find.byType(GestureDetector), findsAtLeastNWidgets(2));
    });
    
    testWidgets('does not show photos when showPhotos is false', (WidgetTester tester) async {
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: Scaffold(
              body: ReviewCard(
                review: review,
                showPhotos: false,
              ),
            ),
          ),
        ),
      );
      
      // Verify that photos are not displayed
      expect(find.byType(ListView), findsNothing);
    });
    
    testWidgets('shows experience name when showExperienceName is true', (WidgetTester tester) async {
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: Scaffold(
              body: ReviewCard(
                review: review,
                showExperienceName: true,
                experienceName: 'Cultural Tour',
              ),
            ),
          ),
        ),
      );
      
      // Verify that experience name is displayed
      expect(find.text('Review for Cultural Tour'), findsOneWidget);
    });
  });
}
