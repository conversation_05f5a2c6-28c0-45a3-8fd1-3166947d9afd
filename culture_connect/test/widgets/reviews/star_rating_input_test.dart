import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:culture_connect/widgets/reviews/star_rating_input.dart';

void main() {
  group('StarRatingInput Widget Tests', () {
    testWidgets('renders with initial rating', (WidgetTester tester) async {
      double rating = 3.0;
      
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: StarRatingInput(
              initialRating: rating,
            ),
          ),
        ),
      );
      
      // Verify that 5 stars are rendered
      expect(find.byIcon(Icons.star_rounded), findsNWidgets(3));
      expect(find.byIcon(Icons.star_border_rounded), findsNWidgets(2));
    });
    
    testWidgets('updates rating on tap', (WidgetTester tester) async {
      double rating = 0.0;
      
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: StatefulBuilder(
              builder: (context, setState) {
                return StarRatingInput(
                  initialRating: rating,
                  onChanged: (newRating) {
                    setState(() {
                      rating = newRating;
                    });
                  },
                );
              },
            ),
          ),
        ),
      );
      
      // Initially no filled stars
      expect(find.byIcon(Icons.star_rounded), findsNothing);
      expect(find.byIcon(Icons.star_border_rounded), findsNWidgets(5));
      
      // Tap on the third star
      await tester.tap(find.byIcon(Icons.star_border_rounded).at(2));
      await tester.pump();
      
      // Verify that 3 stars are now filled
      expect(find.byIcon(Icons.star_rounded), findsNWidgets(3));
      expect(find.byIcon(Icons.star_border_rounded), findsNWidgets(2));
      expect(rating, 3.0);
    });
    
    testWidgets('does not update rating when readOnly is true', (WidgetTester tester) async {
      double rating = 2.0;
      
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: StatefulBuilder(
              builder: (context, setState) {
                return StarRatingInput(
                  initialRating: rating,
                  readOnly: true,
                  onChanged: (newRating) {
                    setState(() {
                      rating = newRating;
                    });
                  },
                );
              },
            ),
          ),
        ),
      );
      
      // Initially 2 filled stars
      expect(find.byIcon(Icons.star_rounded), findsNWidgets(2));
      expect(find.byIcon(Icons.star_border_rounded), findsNWidgets(3));
      
      // Tap on the fourth star
      await tester.tap(find.byIcon(Icons.star_border_rounded).at(1));
      await tester.pump();
      
      // Verify that still only 2 stars are filled (no change)
      expect(find.byIcon(Icons.star_rounded), findsNWidgets(2));
      expect(find.byIcon(Icons.star_border_rounded), findsNWidgets(3));
      expect(rating, 2.0);
    });
  });
  
  group('StarRatingDisplay Widget Tests', () {
    testWidgets('renders with correct rating', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: StarRatingDisplay(
              rating: 3.5,
            ),
          ),
        ),
      );
      
      // Verify that 3 full stars, 1 half star, and 1 empty star are rendered
      expect(find.byIcon(Icons.star_rounded), findsNWidgets(3));
      expect(find.byIcon(Icons.star_half_rounded), findsNWidgets(1));
      expect(find.byIcon(Icons.star_border_rounded), findsNWidgets(1));
    });
    
    testWidgets('shows rating text when showRating is true', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: StarRatingDisplay(
              rating: 4.2,
              showRating: true,
            ),
          ),
        ),
      );
      
      // Verify that the rating text is displayed
      expect(find.text('4.2'), findsOneWidget);
    });
    
    testWidgets('does not show rating text when showRating is false', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: StarRatingDisplay(
              rating: 4.2,
              showRating: false,
            ),
          ),
        ),
      );
      
      // Verify that the rating text is not displayed
      expect(find.text('4.2'), findsNothing);
    });
  });
}
