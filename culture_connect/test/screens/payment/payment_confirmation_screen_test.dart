import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:culture_connect/models/booking.dart';
import 'package:culture_connect/models/experience.dart';
import 'package:culture_connect/models/payment_method.dart';
import 'package:culture_connect/models/payment_result.dart';
import 'package:culture_connect/screens/payment/payment_confirmation_screen.dart';
import 'package:culture_connect/services/payment_service.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

class MockPaymentService extends Mock implements PaymentService {
  @override
  Future<List<PaymentMethod>> getSavedPaymentMethods() async {
    return [
      PaymentMethod(
        id: '1',
        type: PaymentMethodType.creditCard,
        name: 'Visa ending in 4242',
        last4: '4242',
        brand: 'visa',
        expiryMonth: 12,
        expiryYear: 2025,
        isDefault: true,
        createdAt: DateTime.now().subtract(const Duration(days: 30)),
      ),
    ];
  }

  @override
  Future<PaymentResult> processPayment({
    required Booking booking,
    required PaymentProvider provider,
    required String userEmail,
    required String userName,
    String? userPhone,
    PaymentMethod? paymentMethod,
  }) async {
    return PaymentResult.success(
      transactionId: 'test_transaction_id',
      additionalData: {
        'receiptId': 'test_receipt_id',
      },
    );
  }
}

void main() {
  late MockPaymentService mockPaymentService;
  late Experience testExperience;
  late Booking testBooking;

  setUp(() {
    mockPaymentService = MockPaymentService();
    // Replace the singleton instance with our mock
    PaymentService._instance = mockPaymentService as PaymentService;

    // Create a test experience
    testExperience = Experience(
      id: 'exp1',
      title: 'Test Experience',
      description: 'A test experience',
      category: 'Test',
      price: 99.99,
      location: 'Test Location',
      imageUrl: 'https://example.com/image.jpg',
      rating: 4.5,
      reviewCount: 10,
      guideId: 'guide1',
      guideName: 'Test Guide',
      guideImageUrl: 'https://example.com/guide.jpg',
      coordinates: const LatLng(0, 0),
      languages: const ['English'],
      includedItems: const ['Item 1', 'Item 2'],
      requirements: const ['Requirement 1'],
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
      durationHours: 2,
    );

    // Create a test booking
    final now = DateTime.now();
    final startTime = DateTime(now.year, now.month, now.day, 10, 0);
    final endTime = DateTime(now.year, now.month, now.day, 12, 0);

    testBooking = Booking(
      id: 'booking1',
      experienceId: 'exp1',
      date: now,
      timeSlot: TimeSlot(
        startTime: startTime,
        endTime: endTime,
      ),
      participantCount: 2,
      totalAmount: 199.98,
      status: BookingStatus.pending,
      createdAt: now,
      updatedAt: now,
    );
  });

  group('PaymentConfirmationScreen Tests', () {
    testWidgets('renders experience and booking details',
        (WidgetTester tester) async {
      // Build the widget
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: PaymentConfirmationScreen(
              experience: testExperience,
              booking: testBooking,
            ),
          ),
        ),
      );

      // Wait for the payment methods to load
      await tester.pump(const Duration(seconds: 1));

      // Verify that experience details are displayed
      expect(find.text('Test Experience'), findsOneWidget);
      expect(find.text('Test Location'), findsOneWidget);

      // Verify that booking summary is displayed
      expect(find.text('Booking Summary'), findsOneWidget);
      expect(find.text('Participants'), findsOneWidget);
      expect(find.text('2 people'), findsOneWidget);
      expect(find.text('Base Price'), findsOneWidget);
      expect(find.text('\$99.99'), findsOneWidget);
      expect(find.text('Subtotal'), findsOneWidget);
      expect(find.text('\$199.98'), findsOneWidget);

      // Verify that payment button is displayed
      expect(find.textContaining('Pay'), findsOneWidget);
    });

    testWidgets('processes payment when button is tapped',
        (WidgetTester tester) async {
      // Build the widget
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: PaymentConfirmationScreen(
              experience: testExperience,
              booking: testBooking,
            ),
          ),
        ),
      );

      // Wait for the payment methods to load
      await tester.pump(const Duration(seconds: 1));

      // Tap the payment button
      await tester.tap(find.textContaining('Pay'));
      await tester.pump();

      // Verify that the payment is processing (loading indicator is shown)
      expect(find.byType(CircularProgressIndicator), findsOneWidget);

      // Wait for the payment to complete
      await tester.pump(const Duration(seconds: 1));

      // Verify that the payment service was called
      verify(mockPaymentService.processPayment(
        booking: testBooking,
        provider: anyNamed('provider'),
        userEmail: anyNamed('userEmail'),
        userName: anyNamed('userName'),
        paymentMethod: anyNamed('paymentMethod'),
      )).called(1);
    });

    testWidgets('shows error message when payment fails',
        (WidgetTester tester) async {
      // Override the mock to return a failed payment result
      when(mockPaymentService.processPayment(
        booking: anyNamed('booking'),
        provider: anyNamed('provider'),
        userEmail: anyNamed('userEmail'),
        userName: anyNamed('userName'),
        paymentMethod: anyNamed('paymentMethod'),
      )).thenAnswer((_) async => const PaymentResult(
            success: false,
            errorMessage: 'Payment failed: Card declined',
          ));

      // Build the widget
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: PaymentConfirmationScreen(
              experience: testExperience,
              booking: testBooking,
            ),
          ),
        ),
      );

      // Wait for the payment methods to load
      await tester.pump(const Duration(seconds: 1));

      // Tap the payment button
      await tester.tap(find.textContaining('Pay'));
      await tester.pump();

      // Wait for the payment to complete
      await tester.pump(const Duration(seconds: 1));

      // Verify that the error message is displayed
      expect(find.text('Payment failed: Card declined'), findsOneWidget);
    });
  });
}
