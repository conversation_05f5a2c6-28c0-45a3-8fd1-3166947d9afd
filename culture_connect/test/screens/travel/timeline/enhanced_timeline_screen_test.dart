import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';

import 'package:culture_connect/models/travel/timeline.dart';
import 'package:culture_connect/models/travel/timeline_event.dart';
import 'package:culture_connect/models/ar/ar_content_marker.dart';
import 'package:culture_connect/providers/travel/timeline_providers.dart';
import 'package:culture_connect/providers/ar/ar_content_providers.dart';
import 'package:culture_connect/screens/travel/timeline/enhanced_timeline_screen.dart';

// We'll manually create mock classes instead of using @GenerateMocks
class MockCurrentTimelineNotifier extends Mock
    implements CurrentTimelineNotifier {}

class MockCurrentARContentMarkerNotifier extends Mock
    implements CurrentARContentMarkerNotifier {}

void main() {
  late MockCurrentTimelineNotifier mockTimelineNotifier;
  late MockCurrentARContentMarkerNotifier mockARContentMarkerNotifier;

  // Sample timeline data
  final sampleTimeline = Timeline(
    id: 'timeline-1',
    userId: 'user-1',
    title: 'My Paris Trip',
    description: 'A wonderful trip to Paris',
    startDate: DateTime(2023, 6, 1),
    endDate: DateTime(2023, 6, 7),
    events: [
      TimelineEvent(
        id: 'event-1',
        title: 'Eiffel Tower Visit',
        description: 'Visit the iconic Eiffel Tower',
        eventDate: DateTime(2023, 6, 2),
        eventTime: const TimeOfDay(hour: 10, minute: 0),
        location: 'Eiffel Tower, Paris',
        coordinates: {'lat': 48.8584, 'lng': 2.2945},
        eventType: 'sightseeing',
        hasARContent: true,
        arContentId: 'ar-content-1',
      ),
      TimelineEvent(
        id: 'event-2',
        title: 'Louvre Museum',
        description: 'Explore the famous Louvre Museum',
        eventDate: DateTime(2023, 6, 3),
        eventTime: const TimeOfDay(hour: 13, minute: 0),
        location: 'Louvre Museum, Paris',
        coordinates: {'lat': 48.8606, 'lng': 2.3376},
        eventType: 'museum',
        hasARContent: false,
      ),
    ],
    theme: TimelineTheme.standard,
  );

  // Sample AR content marker
  final sampleARContentMarker = ARContentMarker(
    id: 'ar-content-1',
    title: 'Eiffel Tower AR Experience',
    description: 'Explore the Eiffel Tower in augmented reality',
    contentType: ARContentType.model,
    contentUrl: 'https://example.com/ar/eiffel-tower.glb',
    thumbnailUrl: 'https://example.com/ar/eiffel-tower-thumb.jpg',
    location: 'Eiffel Tower, Paris',
    coordinates: {'lat': 48.8584, 'lng': 2.2945},
  );

  setUp(() {
    mockTimelineNotifier = MockCurrentTimelineNotifier();
    mockARContentMarkerNotifier = MockCurrentARContentMarkerNotifier();
  });

  testWidgets(
      'EnhancedTimelineScreen displays loading indicator when timeline is loading',
      (WidgetTester tester) async {
    // Arrange
    when(mockTimelineNotifier.state).thenReturn(const AsyncValue.loading());

    // Create a ProviderScope with overridden providers
    final providerContainer = ProviderScope(
      overrides: [
        currentTimelineProvider.overrideWithValue(mockTimelineNotifier),
        currentARContentMarkerProvider
            .overrideWithValue(mockARContentMarkerNotifier),
      ],
      child: const MaterialApp(
        home: EnhancedTimelineScreen(timelineId: 'timeline-1'),
      ),
    );

    // Act
    await tester.pumpWidget(providerContainer);

    // Assert
    expect(find.byType(CircularProgressIndicator), findsOneWidget);
  });

  testWidgets('EnhancedTimelineScreen displays timeline when data is available',
      (WidgetTester tester) async {
    // Arrange
    when(mockTimelineNotifier.state)
        .thenReturn(AsyncValue.data(sampleTimeline));

    // Create a ProviderScope with overridden providers
    final providerContainer = ProviderScope(
      overrides: [
        currentTimelineProvider.overrideWithValue(mockTimelineNotifier),
        currentARContentMarkerProvider
            .overrideWithValue(mockARContentMarkerNotifier),
        timelineARFilterProvider.overrideWithValue(StateController(false)),
      ],
      child: const MaterialApp(
        home: EnhancedTimelineScreen(timelineId: 'timeline-1'),
      ),
    );

    // Act
    await tester.pumpWidget(providerContainer);
    await tester.pumpAndSettle(); // Wait for animations to complete

    // Assert
    expect(find.text('My Paris Trip'), findsOneWidget);
    expect(find.text('A wonderful trip to Paris'), findsOneWidget);
  });

  testWidgets(
      'EnhancedTimelineScreen displays AR content preview when AR content is tapped',
      (WidgetTester tester) async {
    // Arrange
    when(mockTimelineNotifier.state)
        .thenReturn(AsyncValue.data(sampleTimeline));
    when(mockARContentMarkerNotifier.state)
        .thenReturn(AsyncValue.data(sampleARContentMarker));

    // Create a ProviderScope with overridden providers
    final providerContainer = ProviderScope(
      overrides: [
        currentTimelineProvider.overrideWithValue(mockTimelineNotifier),
        currentARContentMarkerProvider
            .overrideWithValue(mockARContentMarkerNotifier),
        timelineARFilterProvider.overrideWithValue(StateController(false)),
      ],
      child: const MaterialApp(
        home: EnhancedTimelineScreen(timelineId: 'timeline-1'),
      ),
    );

    // Act
    await tester.pumpWidget(providerContainer);
    await tester.pumpAndSettle(); // Wait for animations to complete

    // Find and tap the AR content badge
    final arBadgeFinder = find.byIcon(Icons.view_in_ar);
    expect(arBadgeFinder, findsWidgets);
    await tester.tap(arBadgeFinder.first);
    await tester.pumpAndSettle(); // Wait for animations to complete

    // Verify that the AR content marker was loaded
    verify(mockARContentMarkerNotifier.loadARContentMarker('ar-content-1'))
        .called(1);

    // Assert that the AR preview overlay is displayed
    expect(find.text('Eiffel Tower AR Experience'), findsOneWidget);
    expect(find.text('Explore the Eiffel Tower in augmented reality'),
        findsOneWidget);
    expect(find.text('View in AR'), findsOneWidget);
  });

  testWidgets(
      'EnhancedTimelineScreen displays error view when timeline loading fails',
      (WidgetTester tester) async {
    // Arrange
    when(mockTimelineNotifier.state).thenReturn(
        AsyncValue.error('Failed to load timeline', StackTrace.empty));

    // Create a ProviderScope with overridden providers
    final providerContainer = ProviderScope(
      overrides: [
        currentTimelineProvider.overrideWithValue(mockTimelineNotifier),
        currentARContentMarkerProvider
            .overrideWithValue(mockARContentMarkerNotifier),
      ],
      child: const MaterialApp(
        home: EnhancedTimelineScreen(timelineId: 'timeline-1'),
      ),
    );

    // Act
    await tester.pumpWidget(providerContainer);

    // Assert
    expect(find.text('Failed to load timeline'), findsOneWidget);
    expect(find.byIcon(Icons.refresh), findsOneWidget);
  });

  testWidgets(
      'EnhancedTimelineScreen filters events with AR content when AR filter is enabled',
      (WidgetTester tester) async {
    // Arrange
    when(mockTimelineNotifier.state)
        .thenReturn(AsyncValue.data(sampleTimeline));

    // Create a ProviderScope with overridden providers
    final providerContainer = ProviderScope(
      overrides: [
        currentTimelineProvider.overrideWithValue(mockTimelineNotifier),
        currentARContentMarkerProvider
            .overrideWithValue(mockARContentMarkerNotifier),
        timelineARFilterProvider
            .overrideWithValue(StateController(true)), // Enable AR filter
      ],
      child: const MaterialApp(
        home: EnhancedTimelineScreen(timelineId: 'timeline-1'),
      ),
    );

    // Act
    await tester.pumpWidget(providerContainer);
    await tester.pumpAndSettle(); // Wait for animations to complete

    // Assert
    expect(find.text('Eiffel Tower Visit'),
        findsOneWidget); // Event with AR content
    expect(find.text('Louvre Museum'),
        findsNothing); // Event without AR content should be filtered out
  });
}
