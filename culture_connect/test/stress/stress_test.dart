import 'package:flutter_test/flutter_test.dart';
import 'package:flutter/material.dart';
import 'package:culture_connect/main.dart';
import 'package:culture_connect/models/experience_model.dart';
import 'package:culture_connect/models/travel/document/travel_documents.dart';
import 'package:culture_connect/services/cache_service.dart';
import 'package:culture_connect/services/logging_service.dart';
import 'package:hive/hive.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'dart:io';
import 'dart:async';
import 'package:path_provider/path_provider.dart';
import 'package:integration_test/integration_test.dart';

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  late CacheService cacheService;
  late LoggingService loggingService;
  late Directory tempDir;

  setUp(() async {
    // Create a temporary directory for Hive
    tempDir = await Directory.systemTemp.createTemp('hive_test');
    Hive.init(tempDir.path);
    
    // Register adapters
    Hive.registerAdapter(ExperienceModelAdapter());
    Hive.registerAdapter(TravelDocumentAdapter());
    Hive.registerAdapter(PassportAdapter());
    Hive.registerAdapter(VisaAdapter());
    
    // Open boxes
    await Hive.openBox<String>('cache');
    await Hive.openBox<String>('settings');
    
    loggingService = LoggingService();
    cacheService = CacheService(loggingService: loggingService);
  });

  tearDown(() async {
    // Close all Hive boxes
    await Hive.close();
    
    // Delete the temporary directory
    await tempDir.delete(recursive: true);
  });

  group('Memory Stress Tests', () {
    test('Should handle large cache sizes', () async {
      // Arrange
      const cacheSize = 10 * 1024 * 1024; // 10 MB
      final largeData = List.generate(cacheSize ~/ 1024, (_) => 'A').join(); // ~10 MB string
      
      // Act
      await cacheService.set('large_data', largeData);
      final retrievedData = await cacheService.get('large_data');
      
      // Assert
      expect(retrievedData, equals(largeData));
    });

    test('Should handle many small cache entries', () async {
      // Arrange
      const entryCount = 10000;
      
      // Act
      // Add many small entries
      for (int i = 0; i < entryCount; i++) {
        await cacheService.set('key_$i', 'value_$i');
      }
      
      // Retrieve all entries
      final futures = List.generate(
        entryCount,
        (i) => cacheService.get('key_$i'),
      );
      final results = await Future.wait(futures);
      
      // Assert
      expect(results.length, equals(entryCount));
      for (int i = 0; i < entryCount; i++) {
        expect(results[i], equals('value_$i'));
      }
    });

    test('Should handle cache eviction under memory pressure', () async {
      // This test simulates memory pressure by adding a large number of entries
      // and then checking if the cache service properly evicts old entries
      
      // Arrange
      const entryCount = 1000;
      final cacheBox = Hive.box<String>('cache');
      
      // Act
      // Add many entries
      for (int i = 0; i < entryCount; i++) {
        await cacheService.set('key_$i', 'value_$i', maxAge: Duration(seconds: i % 10));
      }
      
      // Simulate time passing
      await Future.delayed(const Duration(seconds: 5));
      
      // Force cache cleanup
      await cacheService.cleanup();
      
      // Assert
      // Entries with maxAge < 5 seconds should be evicted
      for (int i = 0; i < entryCount; i++) {
        final value = await cacheService.get('key_$i');
        if (i % 10 < 5) {
          expect(value, isNull, reason: 'Entry key_$i should be evicted');
        } else {
          expect(value, equals('value_$i'), reason: 'Entry key_$i should still exist');
        }
      }
    });
  });

  group('UI Stress Tests', () {
    testWidgets('Should handle rapid navigation', (WidgetTester tester) async {
      // Arrange
      await tester.pumpWidget(const MyApp());
      await tester.pumpAndSettle();
      
      // Act
      // Rapidly navigate between screens 50 times
      for (int i = 0; i < 50; i++) {
        // Navigate to a screen
        await tester.tap(find.text('Travel Documents'));
        await tester.pumpAndSettle();
        
        // Navigate back
        await tester.tap(find.byIcon(Icons.arrow_back));
        await tester.pumpAndSettle();
      }
      
      // Assert
      // No assertions needed - if the test completes without crashing, it passes
    });

    testWidgets('Should handle rapid widget rebuilds', (WidgetTester tester) async {
      // Arrange
      await tester.pumpWidget(const MyApp());
      await tester.pumpAndSettle();
      
      // Navigate to a screen with a list
      await tester.tap(find.text('Travel Documents'));
      await tester.pumpAndSettle();
      
      // Act
      // Rapidly scroll the list up and down
      for (int i = 0; i < 50; i++) {
        await tester.drag(find.byType(ListView), const Offset(0, -300));
        await tester.pump(const Duration(milliseconds: 10));
        
        await tester.drag(find.byType(ListView), const Offset(0, 300));
        await tester.pump(const Duration(milliseconds: 10));
      }
      
      // Assert
      // No assertions needed - if the test completes without crashing, it passes
    });

    testWidgets('Should handle multiple overlapping gestures', (WidgetTester tester) async {
      // Arrange
      await tester.pumpWidget(const MyApp());
      await tester.pumpAndSettle();
      
      // Navigate to a screen with a list
      await tester.tap(find.text('Travel Documents'));
      await tester.pumpAndSettle();
      
      // Act
      // Simulate multiple overlapping gestures
      final gesture1 = await tester.startGesture(const Offset(100, 100));
      final gesture2 = await tester.startGesture(const Offset(200, 200));
      
      await gesture1.moveBy(const Offset(50, 50));
      await gesture2.moveBy(const Offset(-50, -50));
      
      await tester.pump(const Duration(milliseconds: 10));
      
      await gesture1.moveBy(const Offset(50, 50));
      await gesture2.moveBy(const Offset(-50, -50));
      
      await tester.pump(const Duration(milliseconds: 10));
      
      await gesture1.up();
      await gesture2.up();
      
      await tester.pumpAndSettle();
      
      // Assert
      // No assertions needed - if the test completes without crashing, it passes
    });
  });

  group('Network Stress Tests', () {
    testWidgets('Should handle network disconnection during operation', (WidgetTester tester) async {
      // Arrange
      await tester.pumpWidget(const MyApp());
      await tester.pumpAndSettle();
      
      // Navigate to a screen that uses network
      await tester.tap(find.text('Airport Transfer'));
      await tester.pumpAndSettle();
      
      // Act
      // Simulate network disconnection
      // This is a bit tricky in a test environment, but we can use the connectivity service
      // to simulate a network disconnection
      
      // First, let's find a refresh button or similar to trigger a network request
      final refreshButton = find.byIcon(Icons.refresh);
      if (refreshButton.evaluate().isNotEmpty) {
        // Tap the refresh button to trigger a network request
        await tester.tap(refreshButton);
        await tester.pump(); // Don't wait for it to settle
        
        // Now simulate network disconnection
        // In a real test, we would use a mock connectivity service
        // For this example, we'll just wait a bit and then continue
        await tester.pump(const Duration(seconds: 1));
      }
      
      // Assert
      // The app should not crash and should show an error message or offline indicator
      // This is hard to verify in a generic way, but we can check that the app is still running
      expect(find.byType(MaterialApp), findsOneWidget);
    });
  });
}
