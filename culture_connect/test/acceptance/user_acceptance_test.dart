import 'package:flutter_test/flutter_test.dart';
import 'package:flutter/material.dart';
import 'package:culture_connect/main.dart';
import 'package:culture_connect/screens/home_screen.dart';
import 'package:culture_connect/screens/travel/document/travel_documents_screen.dart';
import 'package:culture_connect/screens/travel/transfer/transfer_services_screen.dart';
import 'package:culture_connect/widgets/common/app_button.dart';
import 'package:culture_connect/widgets/common/app_text_field.dart';
import 'package:culture_connect/widgets/travel/document/document_card.dart';
import 'package:culture_connect/widgets/travel/transfer/transfer_card.dart';
import 'package:integration_test/integration_test.dart';

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  group('User Acceptance Tests', () {
    testWidgets('User can navigate to Travel Documents screen', (WidgetTester tester) async {
      // Arrange
      await tester.pumpWidget(const MyApp());
      await tester.pumpAndSettle();

      // Act
      // Find and tap the Travel Documents button on the home screen
      final travelDocumentsButton = find.text('Travel Documents');
      await tester.tap(travelDocumentsButton);
      await tester.pumpAndSettle();

      // Assert
      // Verify that we're on the Travel Documents screen
      expect(find.byType(TravelDocumentsScreen), findsOneWidget);
      expect(find.text('My Documents'), findsOneWidget);
    });

    testWidgets('User can navigate to Airport Transfer Services screen', (WidgetTester tester) async {
      // Arrange
      await tester.pumpWidget(const MyApp());
      await tester.pumpAndSettle();

      // Act
      // Find and tap the Airport Transfer button on the home screen
      final airportTransferButton = find.text('Airport Transfer');
      await tester.tap(airportTransferButton);
      await tester.pumpAndSettle();

      // Assert
      // Verify that we're on the Airport Transfer Services screen
      expect(find.byType(TransferServicesScreen), findsOneWidget);
      expect(find.text('Airport Transfer Services'), findsOneWidget);
    });

    testWidgets('User can view document details', (WidgetTester tester) async {
      // Arrange
      await tester.pumpWidget(const MyApp());
      await tester.pumpAndSettle();

      // Navigate to Travel Documents screen
      final travelDocumentsButton = find.text('Travel Documents');
      await tester.tap(travelDocumentsButton);
      await tester.pumpAndSettle();

      // Act
      // Find and tap a document card
      final documentCard = find.byType(DocumentCard).first;
      await tester.tap(documentCard);
      await tester.pumpAndSettle();

      // Assert
      // Verify that we're on the Document Details screen
      expect(find.text('Document Details'), findsOneWidget);
      expect(find.byType(AppButton), findsWidgets);
    });

    testWidgets('User can view transfer service details', (WidgetTester tester) async {
      // Arrange
      await tester.pumpWidget(const MyApp());
      await tester.pumpAndSettle();

      // Navigate to Airport Transfer Services screen
      final airportTransferButton = find.text('Airport Transfer');
      await tester.tap(airportTransferButton);
      await tester.pumpAndSettle();

      // Act
      // Find and tap a transfer card
      final transferCard = find.byType(TransferCard).first;
      await tester.tap(transferCard);
      await tester.pumpAndSettle();

      // Assert
      // Verify that we're on the Transfer Details screen
      expect(find.text('Transfer Details'), findsOneWidget);
      expect(find.byType(AppButton), findsWidgets);
    });

    testWidgets('User can search for transfer services', (WidgetTester tester) async {
      // Arrange
      await tester.pumpWidget(const MyApp());
      await tester.pumpAndSettle();

      // Navigate to Airport Transfer Services screen
      final airportTransferButton = find.text('Airport Transfer');
      await tester.tap(airportTransferButton);
      await tester.pumpAndSettle();

      // Act
      // Find and tap the search button
      final searchButton = find.byIcon(Icons.search);
      await tester.tap(searchButton);
      await tester.pumpAndSettle();

      // Enter search text
      final searchField = find.byType(AppTextField);
      await tester.enterText(searchField, 'Airport');
      await tester.pumpAndSettle();

      // Tap the search button
      final searchSubmitButton = find.byType(AppButton).first;
      await tester.tap(searchSubmitButton);
      await tester.pumpAndSettle();

      // Assert
      // Verify that search results are displayed
      expect(find.text('Search Results'), findsOneWidget);
      expect(find.byType(TransferCard), findsWidgets);
    });

    testWidgets('User can filter transfer services', (WidgetTester tester) async {
      // Arrange
      await tester.pumpWidget(const MyApp());
      await tester.pumpAndSettle();

      // Navigate to Airport Transfer Services screen
      final airportTransferButton = find.text('Airport Transfer');
      await tester.tap(airportTransferButton);
      await tester.pumpAndSettle();

      // Act
      // Find and tap the filter button
      final filterButton = find.byIcon(Icons.filter_list);
      await tester.tap(filterButton);
      await tester.pumpAndSettle();

      // Select a filter option
      final filterOption = find.text('Price: Low to High');
      await tester.tap(filterOption);
      await tester.pumpAndSettle();

      // Assert
      // Verify that filtered results are displayed
      expect(find.byType(TransferCard), findsWidgets);
    });
  });
}
