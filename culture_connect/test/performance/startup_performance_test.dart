import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:culture_connect/main.dart' as app;

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  testWidgets('Measure cold start time', (WidgetTester tester) async {
    // Start timing
    final stopwatch = Stopwatch()..start();

    // Launch the app
    app.main();

    // Wait for the first frame to be rendered
    await tester.pumpAndSettle();

    // Stop timing
    stopwatch.stop();

    // Log the cold start time
    print('Cold start time: ${stopwatch.elapsedMilliseconds}ms');

    // Verify that the splash screen is shown
    expect(find.text('CultureConnect'), findsOneWidget);

    // Measure time to complete initialization
    final initStopwatch = Stopwatch()..start();

    // Wait for initialization to complete (splash screen animation and loading)
    await tester.pump(const Duration(seconds: 3));
    await tester.pumpAndSettle();

    // Stop timing
    initStopwatch.stop();

    // Log the initialization time
    print('Initialization time: ${initStopwatch.elapsedMilliseconds}ms');

    // Verify that we've navigated past the splash screen
    expect(find.text('CultureConnect'), findsNothing);
  });

  testWidgets('Measure memory usage during startup',
      (WidgetTester tester) async {
    // Launch the app
    app.main();

    // Wait for the first frame to be rendered
    await tester.pumpAndSettle();

    // Measure memory usage at splash screen
    final splashMemoryInfo = await IntegrationTestWidgetsFlutterBinding.instance
        .watchPerformance(() async {
      // Just wait on the splash screen
      await tester.pump(const Duration(seconds: 1));
    });

    // Log memory usage at splash screen
    print(
        'Memory usage at splash screen: ${splashMemoryInfo.memoryInfo.toJson()}');

    // Wait for initialization to complete
    await tester.pump(const Duration(seconds: 3));
    await tester.pumpAndSettle();

    // Measure memory usage after initialization
    final initializedMemoryInfo = await IntegrationTestWidgetsFlutterBinding
        .instance
        .watchPerformance(() async {
      // Just wait on the initialized screen
      await tester.pump(const Duration(seconds: 1));
    });

    // Log memory usage after initialization
    print(
        'Memory usage after initialization: ${initializedMemoryInfo.memoryInfo.toJson()}');
  });

  testWidgets('Measure frame build times during startup',
      (WidgetTester tester) async {
    // Create a list to store frame build times
    final List<Duration> frameBuildTimes = [];

    // Setup a frame timing callback
    final subscription =
        WidgetsBinding.instance.onFrameTimingsReported.listen((timings) {
      frameBuildTimes.add(timings.totalSpan);
    });

    // Launch the app
    app.main();

    // Wait for the first frame to be rendered
    await tester.pumpAndSettle();

    // Wait for splash screen animation
    await tester.pump(const Duration(seconds: 3));

    // Wait for initialization to complete
    await tester.pumpAndSettle();

    // Cancel the subscription
    await subscription.cancel();

    // Calculate statistics
    final totalFrames = frameBuildTimes.length;
    final totalBuildTime = frameBuildTimes.fold<Duration>(
      Duration.zero,
      (prev, curr) => prev + curr,
    );
    final averageBuildTime = totalFrames > 0
        ? Duration(microseconds: totalBuildTime.inMicroseconds ~/ totalFrames)
        : Duration.zero;
    final maxBuildTime = frameBuildTimes.isNotEmpty
        ? frameBuildTimes.reduce((a, b) => a > b ? a : b)
        : Duration.zero;

    // Log frame build statistics
    print('Total frames: $totalFrames');
    print(
        'Average frame build time: ${averageBuildTime.inMicroseconds / 1000}ms');
    print('Maximum frame build time: ${maxBuildTime.inMicroseconds / 1000}ms');

    // Check if any frames exceeded the 16ms threshold (for 60fps)
    final jankFrames =
        frameBuildTimes.where((time) => time.inMilliseconds > 16).length;
    final jankPercentage =
        totalFrames > 0 ? (jankFrames / totalFrames) * 100 : 0;

    print('Jank frames: $jankFrames ($jankPercentage%)');

    // Verify that jank percentage is below a threshold (e.g., 20%)
    expect(jankPercentage < 20, true,
        reason: 'Jank percentage is too high: $jankPercentage%');
  });

  testWidgets('Measure warm start time', (WidgetTester tester) async {
    // First launch to warm up
    app.main();
    await tester.pumpAndSettle();
    await tester.pump(const Duration(seconds: 3));
    await tester.pumpAndSettle();

    // Restart the app
    await tester.pumpWidget(Container());
    await tester.pumpAndSettle();

    // Start timing for warm start
    final stopwatch = Stopwatch()..start();

    // Launch the app again
    app.main();

    // Wait for the first frame to be rendered
    await tester.pumpAndSettle();

    // Stop timing
    stopwatch.stop();

    // Log the warm start time
    print('Warm start time: ${stopwatch.elapsedMilliseconds}ms');

    // Verify that the splash screen is shown
    expect(find.text('CultureConnect'), findsOneWidget);

    // Measure time to complete initialization
    final initStopwatch = Stopwatch()..start();

    // Wait for initialization to complete (splash screen animation and loading)
    await tester.pump(const Duration(seconds: 3));
    await tester.pumpAndSettle();

    // Stop timing
    initStopwatch.stop();

    // Log the initialization time
    print('Warm initialization time: ${initStopwatch.elapsedMilliseconds}ms');

    // Verify that we've navigated past the splash screen
    expect(find.text('CultureConnect'), findsNothing);

    // Compare warm start to typical cold start times
    // Note: This is just a rough comparison, actual values would depend on the device
    expect(stopwatch.elapsedMilliseconds < 2000, true,
        reason:
            'Warm start time is too high: ${stopwatch.elapsedMilliseconds}ms');
  });
}
