import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:culture_connect/main.dart' as app;
import 'package:culture_connect/services/auth_service.dart';
import 'package:culture_connect/services/ar_backend_service.dart';
import 'package:culture_connect/models/user_model.dart';
import 'package:culture_connect/models/landmark.dart';
import 'dart:async';
import 'dart:io';

// Generate mocks for dependencies
@GenerateMocks([AuthService, ARBackendService])
import 'ar_performance_test.mocks.dart';

// Mock providers
final mockAuthServiceProvider = Provider<AuthService>((ref) {
  return MockAuthService();
});

final mockARBackendServiceProvider = Provider<ARBackendService>((ref) {
  return MockARBackendService();
});

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  late MockAuthService mockAuthService;
  late MockARBackendService mockARBackendService;

  setUp(() {
    mockAuthService = MockAuthService();
    mockARBackendService = MockARBackendService();

    // Setup mock auth service
    when(mockAuthService.currentUserModel).thenAnswer((_) async => UserModel(
          id: 'test-uid',
          email: '<EMAIL>',
          firstName: 'Test',
          lastName: 'User',
          phoneNumber: '+**********',
          userType: 'tourist',
          isVerified: true,
          verificationLevel: 1,
          status: 'active',
          createdAt: DateTime.now().toIso8601String(),
          updatedAt: DateTime.now().toIso8601String(),
          lastLogin: DateTime.now().toIso8601String(),
          emailVerified: true,
        ));

    // Setup mock AR backend service
    when(mockARBackendService.initialize()).thenAnswer((_) async {});
    when(mockARBackendService.getLandmarks(
      latitude: anyNamed('latitude'),
      longitude: anyNamed('longitude'),
      radius: anyNamed('radius'),
    )).thenAnswer((_) async => [
          Landmark(
            id: '1',
            name: 'Eiffel Tower',
            description: 'Famous landmark in Paris',
            imageUrl: 'https://example.com/eiffel.jpg',
            location: {'latitude': 48.8584, 'longitude': 2.2945},
            rating: 4.8,
            reviewCount: 1250,
            tags: ['monument', 'paris', 'france'],
            historicalSignificance: 'Built for the 1889 World Fair',
            arContent: {
              'id': 'ar-eiffel-tower',
              'available': true,
              'modelUrl': 'https://example.com/eiffel-model.glb'
            },
            translations: {'fr': 'Tour Eiffel', 'es': 'Torre Eiffel'},
          ),
          Landmark(
            id: '2',
            name: 'Statue of Liberty',
            description: 'Famous landmark in New York',
            imageUrl: 'https://example.com/liberty.jpg',
            location: {'latitude': 40.6892, 'longitude': -74.0445},
            rating: 4.7,
            reviewCount: 980,
            tags: ['monument', 'new york', 'usa'],
            historicalSignificance: 'Gift from France to the United States',
            arContent: {
              'id': 'ar-statue-liberty',
              'available': true,
              'modelUrl': 'https://example.com/liberty-model.glb'
            },
            translations: {
              'fr': 'Statue de la Liberté',
              'es': 'Estatua de la Libertad'
            },
          ),
        ]);
    when(mockARBackendService.downloadARModelFile(any))
        .thenAnswer((_) async => File('mock_file_path'));
  });

  testWidgets('Measure AR initialization time', (WidgetTester tester) async {
    // Launch the app
    app.main();
    await tester.pumpAndSettle();

    // Wait for splash screen animation and initialization
    await tester.pump(const Duration(seconds: 3));
    await tester.pumpAndSettle();

    // Navigate to AR screen
    await tester.tap(find.text('AR'));
    await tester.pumpAndSettle();

    // Start timing AR initialization
    final stopwatch = Stopwatch()..start();

    // Wait for AR initialization
    await tester.pump(const Duration(seconds: 3));
    await tester.pumpAndSettle();

    // Stop timing
    stopwatch.stop();

    // Log AR initialization time
    print('AR initialization time: ${stopwatch.elapsedMilliseconds}ms');

    // Verify that AR screen is loaded
    expect(find.text('AR Explore'), findsOneWidget);
    expect(find.text('Eiffel Tower'), findsOneWidget);
  });

  testWidgets('Measure AR rendering performance', (WidgetTester tester) async {
    // Create a list to store frame build times
    final List<Duration> frameBuildTimes = [];

    // Setup a frame timing callback
    final subscription =
        WidgetsBinding.instance.onFrameTimingsReported.listen((timings) {
      frameBuildTimes.add(timings.totalSpan);
    });

    // Launch the app
    app.main();
    await tester.pumpAndSettle();

    // Wait for splash screen animation and initialization
    await tester.pump(const Duration(seconds: 3));
    await tester.pumpAndSettle();

    // Navigate to AR screen
    await tester.tap(find.text('AR'));
    await tester.pumpAndSettle();

    // Wait for AR initialization
    await tester.pump(const Duration(seconds: 3));
    await tester.pumpAndSettle();

    // Clear previous frame timings
    frameBuildTimes.clear();

    // Measure performance during AR interaction
    final performanceInfo = await IntegrationTestWidgetsFlutterBinding.instance
        .watchPerformance(() async {
      // Simulate user interactions with AR

      // Tap on a landmark
      await tester.tap(find.text('Eiffel Tower'));
      await tester.pumpAndSettle();

      // Close landmark details
      await tester.tap(find.byIcon(Icons.close));
      await tester.pumpAndSettle();

      // Simulate pinch to zoom
      await tester.pinch(find.byType(Stack), scale: 2.0);
      await tester.pumpAndSettle();

      // Simulate rotation gesture
      await tester.fling(find.byType(Stack), const Offset(100, 0), 500);
      await tester.pumpAndSettle();

      // Wait to capture more frames
      await tester.pump(const Duration(seconds: 2));
    });

    // Cancel the subscription
    await subscription.cancel();

    // Log performance metrics
    print('AR rendering performance:');
    print(
        'Average frame build time: ${performanceInfo.averageFrameBuildTime.inMicroseconds / 1000}ms');
    print(
        '90th percentile frame build time: ${performanceInfo.computePercentileFrameBuildTime(90).inMicroseconds / 1000}ms');
    print(
        '99th percentile frame build time: ${performanceInfo.computePercentileFrameBuildTime(99).inMicroseconds / 1000}ms');
    print(
        'Worst frame build time: ${performanceInfo.worstFrameBuildTime.inMicroseconds / 1000}ms');
    print('Missed frame count: ${performanceInfo.missedFrameBuildBudgetCount}');

    // Calculate frame rate
    final totalFrames = frameBuildTimes.length;
    final totalDuration = frameBuildTimes.fold<Duration>(
      Duration.zero,
      (prev, curr) => prev + curr,
    );
    final averageFrameTime =
        totalFrames > 0 ? totalDuration.inMicroseconds / totalFrames : 0;
    final estimatedFPS = averageFrameTime > 0 ? 1000000 / averageFrameTime : 0;

    print('Estimated FPS: $estimatedFPS');

    // Verify performance meets requirements
    expect(estimatedFPS >= 30, true,
        reason: 'Frame rate is too low: $estimatedFPS FPS');
    expect(
        performanceInfo.missedFrameBuildBudgetCount / totalFrames < 0.1, true,
        reason:
            'Too many missed frames: ${performanceInfo.missedFrameBuildBudgetCount} out of $totalFrames');
  });

  testWidgets('Measure memory usage during AR experience',
      (WidgetTester tester) async {
    // Launch the app
    app.main();
    await tester.pumpAndSettle();

    // Wait for splash screen animation and initialization
    await tester.pump(const Duration(seconds: 3));
    await tester.pumpAndSettle();

    // Measure baseline memory usage
    final baselineMemoryInfo = await IntegrationTestWidgetsFlutterBinding
        .instance
        .watchPerformance(() async {
      await tester.pump(const Duration(seconds: 1));
    });

    // Log baseline memory usage
    print('Baseline memory usage: ${baselineMemoryInfo.memoryInfo.toJson()}');

    // Navigate to AR screen
    await tester.tap(find.text('AR'));
    await tester.pumpAndSettle();

    // Wait for AR initialization
    await tester.pump(const Duration(seconds: 3));
    await tester.pumpAndSettle();

    // Measure memory usage during AR
    final arMemoryInfo = await IntegrationTestWidgetsFlutterBinding.instance
        .watchPerformance(() async {
      // Interact with AR
      await tester.tap(find.text('Eiffel Tower'));
      await tester.pumpAndSettle();

      await tester.tap(find.byIcon(Icons.close));
      await tester.pumpAndSettle();

      await tester.pump(const Duration(seconds: 2));
    });

    // Log AR memory usage
    print('AR memory usage: ${arMemoryInfo.memoryInfo.toJson()}');

    // Calculate memory increase
    final baselineMemory = baselineMemoryInfo.memoryInfo.currentRss ?? 0;
    final arMemory = arMemoryInfo.memoryInfo.currentRss ?? 0;
    final memoryIncrease = arMemory - baselineMemory;
    final memoryIncreasePercentage =
        baselineMemory > 0 ? (memoryIncrease / baselineMemory) * 100 : 0;

    print(
        'Memory increase: $memoryIncrease bytes ($memoryIncreasePercentage%)');

    // Verify memory usage is within acceptable limits
    // Note: Actual thresholds would depend on the device and app requirements
    expect(memoryIncreasePercentage < 100, true,
        reason: 'Memory usage increased too much: $memoryIncreasePercentage%');
  });
}
