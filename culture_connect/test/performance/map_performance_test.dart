import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:culture_connect/main.dart' as app;
import 'package:culture_connect/services/location_service.dart';
import 'package:culture_connect/services/map_cache_manager.dart';
import 'package:culture_connect/providers/location_permission_provider.dart';
import 'package:culture_connect/models/experience.dart';
import 'package:culture_connect/providers/experiences_provider.dart';

// Generate mocks for dependencies
@GenerateMocks([LocationService, MapCacheManager])
import 'map_performance_test.mocks.dart';

// Create a large number of sample experiences for performance testing
List<Experience> generateSampleExperiences(int count) {
  return List.generate(count, (index) => Experience(
    id: 'exp-$index',
    title: 'Experience $index',
    description: 'Description for experience $index',
    imageUrl: 'https://example.com/image$index.jpg',
    category: index % 5 == 0 ? 'Cultural Tours' :
             index % 5 == 1 ? 'Food & Drink' :
             index % 5 == 2 ? 'Adventure' :
             index % 5 == 3 ? 'Workshops' : 'Sightseeing',
    location: 'San Francisco, CA',
    price: 50.0 + (index % 10) * 10,
    rating: 3.0 + (index % 20) / 10,
    reviewCount: 10 + (index % 50) * 10,
    latitude: 37.7749 + (index % 100) * 0.001,
    longitude: -122.4194 - (index % 100) * 0.001,
  ));
}

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  late MockLocationService mockLocationService;
  late MockMapCacheManager mockMapCacheManager;

  setUp(() {
    mockLocationService = MockLocationService();
    mockMapCacheManager = MockMapCacheManager();
    
    // Setup mock location service
    when(mockLocationService.getCurrentPosition()).thenAnswer((_) async => null);
    when(mockLocationService.calculateDistance(any, any, any, any)).thenReturn(1000.0);
    when(mockLocationService.formatDistance(any)).thenReturn('1.0 km');
    when(mockLocationService.loadMapStyle(any)).thenAnswer((_) async => '[]');
    
    // Setup mock map cache manager
    when(mockMapCacheManager.getCachedRegions()).thenAnswer((_) async => []);
    when(mockMapCacheManager.getCacheSize()).thenAnswer((_) async => 0);
  });

  testWidgets('Measure map rendering performance', (WidgetTester tester) async {
    // Launch the app
    app.main();
    await tester.pumpAndSettle();
    
    // Navigate to the Map tab
    final mapTab = find.text('Map');
    expect(mapTab, findsOneWidget);
    
    // Measure performance of navigating to the map tab
    final mapTabPerformance = await IntegrationTestWidgetsFlutterBinding.instance.watchPerformance(() async {
      await tester.tap(mapTab);
      await tester.pumpAndSettle();
    });
    
    // Log map tab navigation performance
    print('Map tab navigation performance: ${mapTabPerformance.totalTime}');
    print('Frame build time: ${mapTabPerformance.buildTime}');
    print('Frame rasterization time: ${mapTabPerformance.rasterTime}');
    
    // Verify that the Map screen is shown
    expect(find.text('Map'), findsOneWidget);
    
    // Measure performance of panning the map
    final panPerformance = await IntegrationTestWidgetsFlutterBinding.instance.watchPerformance(() async {
      // Simulate panning the map
      await tester.drag(find.byType(Stack).first, const Offset(-200, 0));
      await tester.pumpAndSettle();
      
      await tester.drag(find.byType(Stack).first, const Offset(0, -200));
      await tester.pumpAndSettle();
      
      await tester.drag(find.byType(Stack).first, const Offset(200, 0));
      await tester.pumpAndSettle();
      
      await tester.drag(find.byType(Stack).first, const Offset(0, 200));
      await tester.pumpAndSettle();
    });
    
    // Log map panning performance
    print('Map panning performance: ${panPerformance.totalTime}');
    print('Frame build time: ${panPerformance.buildTime}');
    print('Frame rasterization time: ${panPerformance.rasterTime}');
    
    // Measure performance of zooming the map
    final zoomPerformance = await IntegrationTestWidgetsFlutterBinding.instance.watchPerformance(() async {
      // Simulate pinch-to-zoom
      await tester.timedDrag(
        find.byType(Stack).first,
        const Offset(100, 100),
        const Duration(milliseconds: 500),
      );
      await tester.pumpAndSettle();
      
      await tester.timedDrag(
        find.byType(Stack).first,
        const Offset(-100, -100),
        const Duration(milliseconds: 500),
      );
      await tester.pumpAndSettle();
    });
    
    // Log map zooming performance
    print('Map zooming performance: ${zoomPerformance.totalTime}');
    print('Frame build time: ${zoomPerformance.buildTime}');
    print('Frame rasterization time: ${zoomPerformance.rasterTime}');
    
    // Measure performance of filtering experiences
    final filterPerformance = await IntegrationTestWidgetsFlutterBinding.instance.watchPerformance(() async {
      // Filter by category
      final categoryFilter = find.text('Food & Drink');
      expect(categoryFilter, findsOneWidget);
      await tester.tap(categoryFilter);
      await tester.pumpAndSettle();
      
      // Reset category filter
      final allCategory = find.text('All');
      expect(allCategory, findsOneWidget);
      await tester.tap(allCategory);
      await tester.pumpAndSettle();
    });
    
    // Log filtering performance
    print('Experience filtering performance: ${filterPerformance.totalTime}');
    print('Frame build time: ${filterPerformance.buildTime}');
    print('Frame rasterization time: ${filterPerformance.rasterTime}');
    
    // Measure performance of searching experiences
    final searchPerformance = await IntegrationTestWidgetsFlutterBinding.instance.watchPerformance(() async {
      // Search for experiences
      final searchField = find.byType(TextField);
      expect(searchField, findsOneWidget);
      await tester.enterText(searchField, 'food');
      await tester.pumpAndSettle();
      
      // Clear search
      await tester.enterText(searchField, '');
      await tester.pumpAndSettle();
    });
    
    // Log search performance
    print('Experience search performance: ${searchPerformance.totalTime}');
    print('Frame build time: ${searchPerformance.buildTime}');
    print('Frame rasterization time: ${searchPerformance.rasterTime}');
    
    // Measure memory usage
    final memoryInfo = await IntegrationTestWidgetsFlutterBinding.instance.watchPerformance(() async {
      // Just wait on the map screen
      await tester.pump(const Duration(seconds: 1));
    });
    
    // Log memory usage
    print('Memory usage: ${memoryInfo.memoryInfo.toJson()}');
    
    // Verify performance thresholds
    expect(mapTabPerformance.totalTime.inMilliseconds < 1000, isTrue,
      reason: 'Map tab navigation took too long: ${mapTabPerformance.totalTime.inMilliseconds}ms');
    
    expect(panPerformance.totalTime.inMilliseconds < 2000, isTrue,
      reason: 'Map panning took too long: ${panPerformance.totalTime.inMilliseconds}ms');
    
    expect(zoomPerformance.totalTime.inMilliseconds < 2000, isTrue,
      reason: 'Map zooming took too long: ${zoomPerformance.totalTime.inMilliseconds}ms');
    
    expect(filterPerformance.totalTime.inMilliseconds < 1000, isTrue,
      reason: 'Experience filtering took too long: ${filterPerformance.totalTime.inMilliseconds}ms');
    
    expect(searchPerformance.totalTime.inMilliseconds < 1000, isTrue,
      reason: 'Experience search took too long: ${searchPerformance.totalTime.inMilliseconds}ms');
  });
  
  testWidgets('Measure marker clustering performance with many markers', (WidgetTester tester) async {
    // Generate a large number of experiences
    final largeExperienceSet = generateSampleExperiences(500);
    
    // Launch the app
    app.main();
    await tester.pumpAndSettle();
    
    // Navigate to the Map tab
    final mapTab = find.text('Map');
    expect(mapTab, findsOneWidget);
    await tester.tap(mapTab);
    await tester.pumpAndSettle();
    
    // Verify that the Map screen is shown
    expect(find.text('Map'), findsOneWidget);
    
    // Measure performance of rendering many markers
    final markerPerformance = await IntegrationTestWidgetsFlutterBinding.instance.watchPerformance(() async {
      // Simulate zooming out to trigger clustering
      await tester.timedDrag(
        find.byType(Stack).first,
        const Offset(-100, -100),
        const Duration(milliseconds: 500),
      );
      await tester.pumpAndSettle();
      
      // Simulate zooming in to expand clusters
      await tester.timedDrag(
        find.byType(Stack).first,
        const Offset(100, 100),
        const Duration(milliseconds: 500),
      );
      await tester.pumpAndSettle();
    });
    
    // Log marker clustering performance
    print('Marker clustering performance: ${markerPerformance.totalTime}');
    print('Frame build time: ${markerPerformance.buildTime}');
    print('Frame rasterization time: ${markerPerformance.rasterTime}');
    
    // Verify performance threshold
    expect(markerPerformance.totalTime.inMilliseconds < 3000, isTrue,
      reason: 'Marker clustering took too long: ${markerPerformance.totalTime.inMilliseconds}ms');
  });
}
