import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:culture_connect/main.dart' as app;
import 'package:culture_connect/models/message_model.dart';
import 'package:culture_connect/models/group_chat_model.dart';
import 'package:culture_connect/models/user_model.dart';
import 'package:culture_connect/models/translation/language_model.dart';
import 'package:culture_connect/models/translation/group_translation_model.dart';
import 'package:culture_connect/providers/auth_provider.dart';
import 'package:culture_connect/providers/group_chat_provider.dart';
import 'package:culture_connect/providers/group_translation_provider.dart';
import 'package:culture_connect/screens/messaging/group_chat_screen.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'dart:math';

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();
  
  // Sample data for testing
  final sampleGroupId = 'group-123';
  final sampleUserId = 'user-456';
  
  final sampleUser = UserModel(
    uid: sampleUserId,
    email: '<EMAIL>',
    displayName: 'Test User',
  );
  
  final sampleGroupChat = GroupChatModel(
    id: sampleGroupId,
    name: 'Test Group',
    description: 'A test group for performance testing',
    participants: [sampleUserId, 'user-789'],
    createdBy: 'user-789',
    createdAt: DateTime.now().subtract(const Duration(days: 7)),
    lastMessageAt: DateTime.now(),
    lastMessageText: 'Hello, world!',
    lastMessageSenderId: 'user-789',
    unreadCount: 0,
    isActive: true,
  );
  
  // Generate a large number of messages for performance testing
  List<MessageModel> generateLargeMessageList(int count) {
    final random = Random();
    final languages = ['en', 'fr', 'es', 'yo', 'ig', 'ha', 'sw'];
    final senders = [sampleUserId, 'user-789'];
    final messages = <MessageModel>[];
    
    for (int i = 0; i < count; i++) {
      final language = languages[random.nextInt(languages.length)];
      final sender = senders[random.nextInt(senders.length)];
      
      messages.add(MessageModel(
        id: 'message-$i',
        chatId: sampleGroupId,
        senderId: sender,
        recipientId: '',
        text: 'This is test message number $i in $language',
        timestamp: DateTime.now().subtract(Duration(minutes: count - i)),
        status: MessageStatus.sent,
        type: MessageType.text,
        originalLanguage: language,
      ));
    }
    
    return messages;
  }
  
  final sampleGroupMembers = {
    sampleUserId: sampleUser,
    'user-789': UserModel(
      uid: 'user-789',
      email: '<EMAIL>',
      displayName: 'Other User',
    ),
  };
  
  final sampleParticipantPreference = ParticipantLanguagePreference(
    userId: sampleUserId,
    displayName: 'Test User',
    preferredLanguage: LanguageModel(code: 'en', name: 'English'),
    autoTranslate: true,
    showOriginalText: false,
  );
  
  final sampleGroupSettings = GroupTranslationSettings(
    groupId: sampleGroupId,
    participantPreferences: {
      sampleUserId: sampleParticipantPreference,
    },
    enableRealTimeTranslation: true,
  );

  group('Group Translation Performance Tests', () {
    testWidgets('Measure rendering performance with 100 messages',
        (WidgetTester tester) async {
      // Generate 100 messages
      final messages = generateLargeMessageList(100);
      
      // Create a ProviderContainer with overrides for testing
      final container = ProviderContainer(
        overrides: [
          // Override auth provider
          currentUserProvider.overrideWith((ref) => AsyncValue.data(sampleUser)),
          
          // Override group chat providers
          groupChatProvider(sampleGroupId).overrideWith((ref) => 
            AsyncValue.data(sampleGroupChat)),
          
          groupChatMessagesProvider(sampleGroupId).overrideWith((ref) => 
            AsyncValue.data(messages)),
          
          groupMembersProvider(sampleGroupId).overrideWith((ref) => 
            AsyncValue.data(sampleGroupMembers)),
          
          // Override group translation providers
          groupTranslationSettingsProvider(sampleGroupId).overrideWith((ref) => 
            Future.value(sampleGroupSettings)),
          
          translationMetadataForUserProvider((
            message: any,
            userId: sampleUserId,
            groupId: sampleGroupId,
          )).overrideWith((ref) {
            final message = ref.watch(translationMetadataForUserProvider.argument).message;
            if (message.originalLanguage != 'en') {
              return Future.value(
                MessageTranslationMetadata(
                  messageId: message.id,
                  originalText: message.text,
                  translatedText: 'Translated: ${message.text}',
                  sourceLanguage: message.originalLanguage,
                  targetLanguage: 'en',
                  confidence: 0.95,
                  culturalContext: null,
                  slangIdiom: null,
                  pronunciation: null,
                )
              );
            }
            return Future.value(null);
          }),
          
          translatedTextForUserProvider((
            message: any,
            userId: sampleUserId,
            groupId: sampleGroupId,
          )).overrideWith((ref) {
            final message = ref.watch(translatedTextForUserProvider.argument).message;
            if (message.originalLanguage != 'en') {
              return Future.value('Translated: ${message.text}');
            }
            return Future.value(message.text);
          }),
          
          participantLanguagePreferenceProvider((
            groupId: sampleGroupId,
            userId: sampleUserId,
          )).overrideWith((ref) => Future.value(sampleParticipantPreference)),
        ],
      );
      
      // Measure the time it takes to render the screen
      final stopwatch = Stopwatch()..start();
      
      // Build the app with the overridden providers
      await tester.pumpWidget(
        UncontrolledProviderScope(
          container: container,
          child: MaterialApp(
            home: GroupChatScreen(groupId: sampleGroupId),
          ),
        ),
      );
      
      // Wait for the screen to load
      await tester.pumpAndSettle();
      
      stopwatch.stop();
      
      // Print the time it took to render
      print('Time to render 100 messages: ${stopwatch.elapsedMilliseconds}ms');
      
      // Verify some messages are displayed
      expect(find.textContaining('This is test message'), findsWidgets);
      expect(find.textContaining('Translated:'), findsWidgets);
    });
    
    testWidgets('Measure scrolling performance with 500 messages',
        (WidgetTester tester) async {
      // Generate 500 messages
      final messages = generateLargeMessageList(500);
      
      // Create a ProviderContainer with overrides for testing
      final container = ProviderContainer(
        overrides: [
          // Override auth provider
          currentUserProvider.overrideWith((ref) => AsyncValue.data(sampleUser)),
          
          // Override group chat providers
          groupChatProvider(sampleGroupId).overrideWith((ref) => 
            AsyncValue.data(sampleGroupChat)),
          
          groupChatMessagesProvider(sampleGroupId).overrideWith((ref) => 
            AsyncValue.data(messages)),
          
          groupMembersProvider(sampleGroupId).overrideWith((ref) => 
            AsyncValue.data(sampleGroupMembers)),
          
          // Override group translation providers
          groupTranslationSettingsProvider(sampleGroupId).overrideWith((ref) => 
            Future.value(sampleGroupSettings)),
          
          translationMetadataForUserProvider((
            message: any,
            userId: sampleUserId,
            groupId: sampleGroupId,
          )).overrideWith((ref) {
            final message = ref.watch(translationMetadataForUserProvider.argument).message;
            if (message.originalLanguage != 'en') {
              return Future.value(
                MessageTranslationMetadata(
                  messageId: message.id,
                  originalText: message.text,
                  translatedText: 'Translated: ${message.text}',
                  sourceLanguage: message.originalLanguage,
                  targetLanguage: 'en',
                  confidence: 0.95,
                  culturalContext: null,
                  slangIdiom: null,
                  pronunciation: null,
                )
              );
            }
            return Future.value(null);
          }),
          
          translatedTextForUserProvider((
            message: any,
            userId: sampleUserId,
            groupId: sampleGroupId,
          )).overrideWith((ref) {
            final message = ref.watch(translatedTextForUserProvider.argument).message;
            if (message.originalLanguage != 'en') {
              return Future.value('Translated: ${message.text}');
            }
            return Future.value(message.text);
          }),
          
          participantLanguagePreferenceProvider((
            groupId: sampleGroupId,
            userId: sampleUserId,
          )).overrideWith((ref) => Future.value(sampleParticipantPreference)),
        ],
      );
      
      // Build the app with the overridden providers
      await tester.pumpWidget(
        UncontrolledProviderScope(
          container: container,
          child: MaterialApp(
            home: GroupChatScreen(groupId: sampleGroupId),
          ),
        ),
      );
      
      // Wait for the screen to load
      await tester.pumpAndSettle();
      
      // Measure scrolling performance
      final stopwatch = Stopwatch()..start();
      
      // Scroll down
      await tester.fling(
        find.byType(ListView),
        const Offset(0, -500),
        3000,
      );
      await tester.pumpAndSettle();
      
      // Scroll up
      await tester.fling(
        find.byType(ListView),
        const Offset(0, 500),
        3000,
      );
      await tester.pumpAndSettle();
      
      stopwatch.stop();
      
      // Print the time it took to scroll
      print('Time to scroll through 500 messages: ${stopwatch.elapsedMilliseconds}ms');
      
      // Verify some messages are displayed
      expect(find.textContaining('This is test message'), findsWidgets);
    });
    
    testWidgets('Measure translation performance for 10 messages',
        (WidgetTester tester) async {
      // Generate 10 messages with non-English languages
      final messages = <MessageModel>[];
      final languages = ['fr', 'es', 'yo', 'ig', 'ha', 'sw'];
      
      for (int i = 0; i < 10; i++) {
        final language = languages[i % languages.length];
        
        messages.add(MessageModel(
          id: 'message-$i',
          chatId: sampleGroupId,
          senderId: 'user-789',
          recipientId: '',
          text: 'This is test message number $i in $language',
          timestamp: DateTime.now().subtract(Duration(minutes: 10 - i)),
          status: MessageStatus.sent,
          type: MessageType.text,
          originalLanguage: language,
        ));
      }
      
      // Create a ProviderContainer with overrides for testing
      final container = ProviderContainer(
        overrides: [
          // Override auth provider
          currentUserProvider.overrideWith((ref) => AsyncValue.data(sampleUser)),
          
          // Override group chat providers
          groupChatProvider(sampleGroupId).overrideWith((ref) => 
            AsyncValue.data(sampleGroupChat)),
          
          groupChatMessagesProvider(sampleGroupId).overrideWith((ref) => 
            AsyncValue.data(messages)),
          
          groupMembersProvider(sampleGroupId).overrideWith((ref) => 
            AsyncValue.data(sampleGroupMembers)),
          
          // Override group translation providers
          groupTranslationSettingsProvider(sampleGroupId).overrideWith((ref) => 
            Future.value(sampleGroupSettings)),
          
          // Use real translation logic for performance testing
          // Other providers are overridden in the previous tests
        ],
      );
      
      // Measure the time it takes to translate all messages
      final stopwatch = Stopwatch()..start();
      
      // Build the app with the overridden providers
      await tester.pumpWidget(
        UncontrolledProviderScope(
          container: container,
          child: MaterialApp(
            home: GroupChatScreen(groupId: sampleGroupId),
          ),
        ),
      );
      
      // Wait for the screen to load and translations to complete
      await tester.pumpAndSettle();
      
      stopwatch.stop();
      
      // Print the time it took to translate
      print('Time to translate 10 messages: ${stopwatch.elapsedMilliseconds}ms');
      print('Average time per message: ${stopwatch.elapsedMilliseconds / 10}ms');
      
      // Verify translations are displayed
      expect(find.textContaining('Translated from'), findsWidgets);
    });
  });
}
