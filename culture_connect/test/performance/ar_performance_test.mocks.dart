// Mocks generated by <PERSON><PERSON><PERSON> 5.4.5 from annotations
// in culture_connect/test/performance/ar_performance_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i4;
import 'dart:io' as _i9;

import 'package:culture_connect/models/ar_model.dart' as _i8;
import 'package:culture_connect/models/landmark.dart' as _i7;
import 'package:culture_connect/models/user_model.dart' as _i5;
import 'package:culture_connect/services/ar_backend_service.dart' as _i6;
import 'package:culture_connect/services/auth_service.dart' as _i3;
import 'package:firebase_auth/firebase_auth.dart' as _i2;
import 'package:mockito/mockito.dart' as _i1;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeUserCredential_0 extends _i1.SmartFake
    implements _i2.UserCredential {
  _FakeUserCredential_0(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

/// A class which mocks [AuthService].
///
/// See the documentation for Mockito's code generation for more information.
class MockAuthService extends _i1.Mock implements _i3.AuthService {
  MockAuthService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i4.Stream<_i3.AuthStatus> get authStateChanges => (super.noSuchMethod(
        Invocation.getter(#authStateChanges),
        returnValue: _i4.Stream<_i3.AuthStatus>.empty(),
      ) as _i4.Stream<_i3.AuthStatus>);

  @override
  _i4.Future<_i5.UserModel?> get currentUserModel => (super.noSuchMethod(
        Invocation.getter(#currentUserModel),
        returnValue: _i4.Future<_i5.UserModel?>.value(),
      ) as _i4.Future<_i5.UserModel?>);

  @override
  _i4.Future<_i2.UserCredential> registerWithEmailAndPassword({
    required String? email,
    required String? password,
    required String? firstName,
    required String? lastName,
    required String? phoneNumber,
    required String? dateOfBirth,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #registerWithEmailAndPassword,
          [],
          {
            #email: email,
            #password: password,
            #firstName: firstName,
            #lastName: lastName,
            #phoneNumber: phoneNumber,
            #dateOfBirth: dateOfBirth,
          },
        ),
        returnValue: _i4.Future<_i2.UserCredential>.value(_FakeUserCredential_0(
          this,
          Invocation.method(
            #registerWithEmailAndPassword,
            [],
            {
              #email: email,
              #password: password,
              #firstName: firstName,
              #lastName: lastName,
              #phoneNumber: phoneNumber,
              #dateOfBirth: dateOfBirth,
            },
          ),
        )),
      ) as _i4.Future<_i2.UserCredential>);

  @override
  _i4.Future<_i2.UserCredential> loginWithEmailAndPassword({
    required String? email,
    required String? password,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #loginWithEmailAndPassword,
          [],
          {
            #email: email,
            #password: password,
          },
        ),
        returnValue: _i4.Future<_i2.UserCredential>.value(_FakeUserCredential_0(
          this,
          Invocation.method(
            #loginWithEmailAndPassword,
            [],
            {
              #email: email,
              #password: password,
            },
          ),
        )),
      ) as _i4.Future<_i2.UserCredential>);

  @override
  _i4.Future<_i2.UserCredential?> signInWithGoogle() => (super.noSuchMethod(
        Invocation.method(
          #signInWithGoogle,
          [],
        ),
        returnValue: _i4.Future<_i2.UserCredential?>.value(),
      ) as _i4.Future<_i2.UserCredential?>);

  @override
  _i4.Future<void> signOut() => (super.noSuchMethod(
        Invocation.method(
          #signOut,
          [],
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);

  @override
  _i4.Future<void> sendPasswordResetEmail(String? email) => (super.noSuchMethod(
        Invocation.method(
          #sendPasswordResetEmail,
          [email],
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);

  @override
  _i4.Future<bool> verifyEmail() => (super.noSuchMethod(
        Invocation.method(
          #verifyEmail,
          [],
        ),
        returnValue: _i4.Future<bool>.value(false),
      ) as _i4.Future<bool>);

  @override
  _i4.Future<void> resendVerificationEmail() => (super.noSuchMethod(
        Invocation.method(
          #resendVerificationEmail,
          [],
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);

  @override
  _i4.Future<bool> isBiometricAvailable() => (super.noSuchMethod(
        Invocation.method(
          #isBiometricAvailable,
          [],
        ),
        returnValue: _i4.Future<bool>.value(false),
      ) as _i4.Future<bool>);

  @override
  _i4.Future<bool> authenticateWithBiometrics() => (super.noSuchMethod(
        Invocation.method(
          #authenticateWithBiometrics,
          [],
        ),
        returnValue: _i4.Future<bool>.value(false),
      ) as _i4.Future<bool>);
}

/// A class which mocks [ARBackendService].
///
/// See the documentation for Mockito's code generation for more information.
class MockARBackendService extends _i1.Mock implements _i6.ARBackendService {
  MockARBackendService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i4.Future<void> initialize() => (super.noSuchMethod(
        Invocation.method(
          #initialize,
          [],
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);

  @override
  _i4.Future<List<_i7.Landmark>> getLandmarks({
    double? latitude,
    double? longitude,
    double? radius,
    List<String>? tags,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #getLandmarks,
          [],
          {
            #latitude: latitude,
            #longitude: longitude,
            #radius: radius,
            #tags: tags,
          },
        ),
        returnValue: _i4.Future<List<_i7.Landmark>>.value(<_i7.Landmark>[]),
      ) as _i4.Future<List<_i7.Landmark>>);

  @override
  _i4.Future<_i8.ARModel?> getARModel(String? modelId) => (super.noSuchMethod(
        Invocation.method(
          #getARModel,
          [modelId],
        ),
        returnValue: _i4.Future<_i8.ARModel?>.value(),
      ) as _i4.Future<_i8.ARModel?>);

  @override
  _i4.Future<_i9.File?> downloadARModelFile(String? modelUrl) =>
      (super.noSuchMethod(
        Invocation.method(
          #downloadARModelFile,
          [modelUrl],
        ),
        returnValue: _i4.Future<_i9.File?>.value(),
      ) as _i4.Future<_i9.File?>);

  @override
  _i4.Future<bool> uploadUserARContent({
    required String? name,
    required String? description,
    required _i9.File? modelFile,
    required _i9.File? imageFile,
    List<_i9.File>? textureFiles,
    Map<String, dynamic>? metadata,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #uploadUserARContent,
          [],
          {
            #name: name,
            #description: description,
            #modelFile: modelFile,
            #imageFile: imageFile,
            #textureFiles: textureFiles,
            #metadata: metadata,
          },
        ),
        returnValue: _i4.Future<bool>.value(false),
      ) as _i4.Future<bool>);

  @override
  _i4.Future<bool> shareARContent({
    required String? contentId,
    required List<String>? recipientIds,
    String? message,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #shareARContent,
          [],
          {
            #contentId: contentId,
            #recipientIds: recipientIds,
            #message: message,
          },
        ),
        returnValue: _i4.Future<bool>.value(false),
      ) as _i4.Future<bool>);
}
