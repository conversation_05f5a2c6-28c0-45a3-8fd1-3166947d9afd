// Mocks generated by <PERSON><PERSON><PERSON> 5.4.5 from annotations
// in culture_connect/test/performance/map_performance_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i4;

import 'package:culture_connect/models/landmark.dart' as _i6;
import 'package:culture_connect/models/location/lat_lng.dart' as _i9;
import 'package:culture_connect/models/location/place_result.dart' as _i8;
import 'package:culture_connect/services/location_service.dart' as _i3;
import 'package:culture_connect/services/map_cache_manager.dart' as _i10;
import 'package:geolocator/geolocator.dart' as _i2;
import 'package:google_maps_flutter/google_maps_flutter.dart' as _i5;
import 'package:mockito/mockito.dart' as _i1;
import 'package:mockito/src/dummies.dart' as _i7;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakePosition_0 extends _i1.SmartFake implements _i2.Position {
  _FakePosition_0(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

/// A class which mocks [LocationService].
///
/// See the documentation for Mockito's code generation for more information.
class MockLocationService extends _i1.Mock implements _i3.LocationService {
  MockLocationService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i4.Stream<_i2.Position> get locationStream => (super.noSuchMethod(
        Invocation.getter(#locationStream),
        returnValue: _i4.Stream<_i2.Position>.empty(),
      ) as _i4.Stream<_i2.Position>);

  @override
  _i4.Stream<_i3.LocationPermissionStatus> get permissionStatusStream =>
      (super.noSuchMethod(
        Invocation.getter(#permissionStatusStream),
        returnValue: _i4.Stream<_i3.LocationPermissionStatus>.empty(),
      ) as _i4.Stream<_i3.LocationPermissionStatus>);

  @override
  _i4.Stream<_i5.LatLng> get locationUpdateStream => (super.noSuchMethod(
        Invocation.getter(#locationUpdateStream),
        returnValue: _i4.Stream<_i5.LatLng>.empty(),
      ) as _i4.Stream<_i5.LatLng>);

  @override
  _i4.Future<bool> startLocationTracking() => (super.noSuchMethod(
        Invocation.method(
          #startLocationTracking,
          [],
        ),
        returnValue: _i4.Future<bool>.value(false),
      ) as _i4.Future<bool>);

  @override
  _i4.Future<List<_i6.Landmark>> getNearbyLandmarks(
    double? latitude,
    double? longitude,
    double? radiusInMeters, {
    bool? useCache = true,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #getNearbyLandmarks,
          [
            latitude,
            longitude,
            radiusInMeters,
          ],
          {#useCache: useCache},
        ),
        returnValue: _i4.Future<List<_i6.Landmark>>.value(<_i6.Landmark>[]),
      ) as _i4.Future<List<_i6.Landmark>>);

  @override
  double calculateDistance(
    double? startLat,
    double? startLng,
    double? endLat,
    double? endLng,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #calculateDistance,
          [
            startLat,
            startLng,
            endLat,
            endLng,
          ],
        ),
        returnValue: 0.0,
      ) as double);

  @override
  String formatDistance(double? distanceInMeters) => (super.noSuchMethod(
        Invocation.method(
          #formatDistance,
          [distanceInMeters],
        ),
        returnValue: _i7.dummyValue<String>(
          this,
          Invocation.method(
            #formatDistance,
            [distanceInMeters],
          ),
        ),
      ) as String);

  @override
  _i4.Future<List<_i6.Landmark>> getRecommendedLandmarks(
    double? latitude,
    double? longitude, {
    int? limit = 5,
    List<String>? categories,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #getRecommendedLandmarks,
          [
            latitude,
            longitude,
          ],
          {
            #limit: limit,
            #categories: categories,
          },
        ),
        returnValue: _i4.Future<List<_i6.Landmark>>.value(<_i6.Landmark>[]),
      ) as _i4.Future<List<_i6.Landmark>>);

  @override
  _i4.Future<List<_i8.PlaceResult>> searchPlaces(String? query) =>
      (super.noSuchMethod(
        Invocation.method(
          #searchPlaces,
          [query],
        ),
        returnValue:
            _i4.Future<List<_i8.PlaceResult>>.value(<_i8.PlaceResult>[]),
      ) as _i4.Future<List<_i8.PlaceResult>>);

  @override
  _i4.Future<List<_i8.PlaceResult>> searchNearbyPlaces(
    double? latitude,
    double? longitude,
    double? radiusInMeters, {
    String? type,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #searchNearbyPlaces,
          [
            latitude,
            longitude,
            radiusInMeters,
          ],
          {#type: type},
        ),
        returnValue:
            _i4.Future<List<_i8.PlaceResult>>.value(<_i8.PlaceResult>[]),
      ) as _i4.Future<List<_i8.PlaceResult>>);

  @override
  _i4.Future<List<_i8.PlaceResult>> getNearbyAirports(
    _i9.LatLng? coordinates, {
    double? radiusKm = 50.0,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #getNearbyAirports,
          [coordinates],
          {#radiusKm: radiusKm},
        ),
        returnValue:
            _i4.Future<List<_i8.PlaceResult>>.value(<_i8.PlaceResult>[]),
      ) as _i4.Future<List<_i8.PlaceResult>>);

  @override
  _i4.Future<String?> loadMapStyle(String? styleName) => (super.noSuchMethod(
        Invocation.method(
          #loadMapStyle,
          [styleName],
        ),
        returnValue: _i4.Future<String?>.value(),
      ) as _i4.Future<String?>);

  @override
  _i4.Future<List<_i9.LatLng>> getRoute(
    _i9.LatLng? origin,
    _i9.LatLng? destination,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #getRoute,
          [
            origin,
            destination,
          ],
        ),
        returnValue: _i4.Future<List<_i9.LatLng>>.value(<_i9.LatLng>[]),
      ) as _i4.Future<List<_i9.LatLng>>);

  @override
  double calculateRouteDistance(List<_i9.LatLng>? route) => (super.noSuchMethod(
        Invocation.method(
          #calculateRouteDistance,
          [route],
        ),
        returnValue: 0.0,
      ) as double);

  @override
  int estimateTravelTime(
    double? distanceInMeters,
    String? mode,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #estimateTravelTime,
          [
            distanceInMeters,
            mode,
          ],
        ),
        returnValue: 0,
      ) as int);

  @override
  void dispose() => super.noSuchMethod(
        Invocation.method(
          #dispose,
          [],
        ),
        returnValueForMissingStub: null,
      );

  @override
  _i4.Future<_i2.Position> getCurrentPosition() => (super.noSuchMethod(
        Invocation.method(
          #getCurrentPosition,
          [],
        ),
        returnValue: _i4.Future<_i2.Position>.value(_FakePosition_0(
          this,
          Invocation.method(
            #getCurrentPosition,
            [],
          ),
        )),
      ) as _i4.Future<_i2.Position>);

  @override
  _i4.Future<_i2.Position?> getLastKnownPosition() => (super.noSuchMethod(
        Invocation.method(
          #getLastKnownPosition,
          [],
        ),
        returnValue: _i4.Future<_i2.Position?>.value(),
      ) as _i4.Future<_i2.Position?>);

  @override
  _i4.Stream<_i2.Position> getPositionStream({
    _i2.LocationAccuracy? accuracy = _i2.LocationAccuracy.high,
    int? distanceFilter = 10,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #getPositionStream,
          [],
          {
            #accuracy: accuracy,
            #distanceFilter: distanceFilter,
          },
        ),
        returnValue: _i4.Stream<_i2.Position>.empty(),
      ) as _i4.Stream<_i2.Position>);

  @override
  _i4.Future<bool> isLocationServiceEnabled() => (super.noSuchMethod(
        Invocation.method(
          #isLocationServiceEnabled,
          [],
        ),
        returnValue: _i4.Future<bool>.value(false),
      ) as _i4.Future<bool>);

  @override
  _i4.Future<_i2.LocationPermission> requestPermission() => (super.noSuchMethod(
        Invocation.method(
          #requestPermission,
          [],
        ),
        returnValue: _i4.Future<_i2.LocationPermission>.value(
            _i2.LocationPermission.denied),
      ) as _i4.Future<_i2.LocationPermission>);

  @override
  _i4.Future<_i2.LocationPermission> checkPermission() => (super.noSuchMethod(
        Invocation.method(
          #checkPermission,
          [],
        ),
        returnValue: _i4.Future<_i2.LocationPermission>.value(
            _i2.LocationPermission.denied),
      ) as _i4.Future<_i2.LocationPermission>);

  @override
  _i4.Future<bool> openLocationSettings() => (super.noSuchMethod(
        Invocation.method(
          #openLocationSettings,
          [],
        ),
        returnValue: _i4.Future<bool>.value(false),
      ) as _i4.Future<bool>);

  @override
  _i4.Future<bool> openAppSettings() => (super.noSuchMethod(
        Invocation.method(
          #openAppSettings,
          [],
        ),
        returnValue: _i4.Future<bool>.value(false),
      ) as _i4.Future<bool>);
}

/// A class which mocks [MapCacheManager].
///
/// See the documentation for Mockito's code generation for more information.
class MockMapCacheManager extends _i1.Mock implements _i10.MapCacheManager {
  MockMapCacheManager() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i4.Future<bool> cacheMapRegion(
    _i5.LatLngBounds? bounds,
    int? minZoom,
    int? maxZoom,
    dynamic Function(double)? onProgress,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #cacheMapRegion,
          [
            bounds,
            minZoom,
            maxZoom,
            onProgress,
          ],
        ),
        returnValue: _i4.Future<bool>.value(false),
      ) as _i4.Future<bool>);

  @override
  _i4.Future<List<_i10.CachedMapRegion>> getCachedRegions() =>
      (super.noSuchMethod(
        Invocation.method(
          #getCachedRegions,
          [],
        ),
        returnValue: _i4.Future<List<_i10.CachedMapRegion>>.value(
            <_i10.CachedMapRegion>[]),
      ) as _i4.Future<List<_i10.CachedMapRegion>>);

  @override
  _i4.Future<bool> deleteCachedRegion(String? regionId) => (super.noSuchMethod(
        Invocation.method(
          #deleteCachedRegion,
          [regionId],
        ),
        returnValue: _i4.Future<bool>.value(false),
      ) as _i4.Future<bool>);

  @override
  _i4.Future<bool> clearCache() => (super.noSuchMethod(
        Invocation.method(
          #clearCache,
          [],
        ),
        returnValue: _i4.Future<bool>.value(false),
      ) as _i4.Future<bool>);

  @override
  _i4.Future<int> getCacheSize() => (super.noSuchMethod(
        Invocation.method(
          #getCacheSize,
          [],
        ),
        returnValue: _i4.Future<int>.value(0),
      ) as _i4.Future<int>);
}
