import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:culture_connect/models/translation/dialect_model.dart';
import 'package:culture_connect/models/translation/accent_model.dart';
import 'package:culture_connect/models/translation/dialect_detection_result.dart';
import 'package:culture_connect/services/voice_translation/dialect_accent_detection_service.dart';
import 'package:culture_connect/services/voice_translation/language_detection_service.dart';
import 'package:culture_connect/models/translation/language_model.dart';

class MockSharedPreferences extends Mock implements SharedPreferences {
  final Map<String, dynamic> values = {};

  @override
  bool? getBool(String key) => values[key] as bool?;

  @override
  double? getDouble(String key) => values[key] as double?;

  @override
  String? getString(String key) => values[key] as String?;

  @override
  Future<bool> setBool(String key, bool value) async {
    values[key] = value;
    return true;
  }

  @override
  Future<bool> setDouble(String key, double value) async {
    values[key] = value;
    return true;
  }

  @override
  Future<bool> setString(String key, String value) async {
    values[key] = value;
    return true;
  }
}

class MockLanguageDetectionService extends Mock implements LanguageDetectionService {}

void main() {
  late MockSharedPreferences mockPrefs;
  late MockLanguageDetectionService mockLanguageDetectionService;
  late DialectAccentDetectionService service;

  setUp(() {
    mockPrefs = MockSharedPreferences();
    mockLanguageDetectionService = MockLanguageDetectionService();
    service = DialectAccentDetectionService(mockPrefs, mockLanguageDetectionService);
  });

  group('DialectAccentDetectionService', () {
    test('should initialize with default values', () {
      expect(service.useDialectDetection, isTrue);
      expect(service.useAccentDetection, isTrue);
    });

    test('should set and get dialect detection settings', () async {
      await service.setUseDialectDetection(false);
      expect(service.useDialectDetection, isFalse);
      expect(mockPrefs.values['use_dialect_detection'], isFalse);

      await service.setUseAccentDetection(false);
      expect(service.useAccentDetection, isFalse);
      expect(mockPrefs.values['use_accent_detection'], isFalse);
    });

    test('should set and get confidence thresholds', () async {
      await service.setDialectConfidenceThreshold(0.8);
      expect(mockPrefs.values['dialect_confidence_threshold'], 0.8);

      await service.setAccentConfidenceThreshold(0.7);
      expect(mockPrefs.values['accent_confidence_threshold'], 0.7);
    });

    test('should set and get preferred dialects', () async {
      await service.setPreferredDialect('en', 'en-us');
      expect(service.getPreferredDialect('en'), 'en-us');
    });

    test('should set and get preferred accents', () async {
      await service.setPreferredAccent('en-us', 'en-us-southern');
      expect(service.getPreferredAccent('en-us'), 'en-us-southern');
    });

    test('should detect dialect from text', () async {
      final result = await service.detectDialect(
        'Hello! I\'m taking the elevator to my apartment. I\'ll be on vacation next week.',
        'en',
      );

      expect(result, isNotNull);
      if (result != null) {
        expect(result.dialect.code, 'en-us');
        expect(result.confidence, greaterThan(0.5));
      }
    });

    test('should detect accent from audio', () async {
      final result = await service.detectAccentFromAudio(
        'test_audio.mp3',
        'en-us',
      );

      expect(result, isNotNull);
      if (result != null) {
        expect(result.accent.dialectCode, 'en-us');
        expect(result.confidence, greaterThan(0.5));
      }
    });

    test('should clear caches', () async {
      // First add some data to the caches
      await service.detectDialect(
        'Hello! I\'m taking the elevator to my apartment. I\'ll be on vacation next week.',
        'en',
      );

      await service.detectAccentFromAudio(
        'test_audio.mp3',
        'en-us',
      );

      // Then clear the caches
      await service.clearCaches();

      // Verify that the caches are cleared by checking if the service
      // makes new API calls when detecting the same text/audio again
      final dialectResult = await service.detectDialect(
        'Hello! I\'m taking the elevator to my apartment. I\'ll be on vacation next week.',
        'en',
      );

      expect(dialectResult, isNotNull);
    });
  });
}
