import 'package:flutter_test/flutter_test.dart';
import 'package:culture_connect/models/currency/currency_model.dart';
import 'package:culture_connect/services/currency/currency_data_service.dart';

void main() {
  group('CurrencyDataService', () {
    late CurrencyDataService currencyDataService;

    setUp(() {
      currencyDataService = CurrencyDataService();
    });

    test('should return all currencies', () {
      // Act
      final currencies = currencyDataService.getAllCurrencies();

      // Assert
      expect(currencies, isNotEmpty);
      expect(currencies.length, greaterThan(10));
    });

    test('should return major currencies', () {
      // Act
      final majorCurrencies = currencyDataService.getMajorCurrencies();

      // Assert
      expect(majorCurrencies, isNotEmpty);
      expect(majorCurrencies.every((currency) => currency.isMajor), isTrue);
    });

    test('should get currency by code', () {
      // Act
      final usd = currencyDataService.getCurrencyByCode('USD');
      final invalidCurrency = currencyDataService.getCurrencyByCode('XYZ');

      // Assert
      expect(usd, isNotNull);
      expect(usd?.code, equals('USD'));
      expect(usd?.name, equals('US Dollar'));
      expect(invalidCurrency, isNull);
    });

    test('should get currency symbol', () {
      // Act
      final usdSymbol = currencyDataService.getCurrencySymbol('USD');
      final invalidSymbol = currencyDataService.getCurrencySymbol('XYZ');

      // Assert
      expect(usdSymbol, equals('\$'));
      expect(invalidSymbol, equals('XYZ'));
    });

    test('should format amount according to currency rules', () {
      // Act
      final formattedUSD = currencyDataService.formatAmount('USD', 1234.56);
      final formattedJPY = currencyDataService.formatAmount('JPY', 1234.56);
      final formattedInvalid = currencyDataService.formatAmount('XYZ', 1234.56);

      // Assert
      expect(formattedUSD, equals('\$1234.56'));
      expect(formattedJPY, equals('¥1235')); // JPY has 0 decimal places
      expect(formattedInvalid, equals('XYZ1234.56'));
    });
  });
}
