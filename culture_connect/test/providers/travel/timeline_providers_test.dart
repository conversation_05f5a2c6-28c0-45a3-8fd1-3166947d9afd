import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';

import 'package:culture_connect/models/travel/timeline.dart';
import 'package:culture_connect/models/travel/timeline_event.dart';
import 'package:culture_connect/services/travel/timeline_service.dart';
import 'package:culture_connect/providers/travel/timeline_providers.dart';

// We'll manually create a mock class instead of using @GenerateMocks
class MockTimelineService extends Mock implements TimelineService {}

void main() {
  late MockTimelineService mockTimelineService;
  late ProviderContainer container;

  // Sample timeline data
  final sampleTimeline = Timeline(
    id: 'timeline-1',
    userId: 'user-1',
    title: 'My Paris Trip',
    description: 'A wonderful trip to Paris',
    startDate: DateTime(2023, 6, 1),
    endDate: DateTime(2023, 6, 7),
    events: [
      TimelineEvent(
        id: 'event-1',
        title: 'Eiffel Tower Visit',
        description: 'Visit the iconic Eiffel Tower',
        eventDate: DateTime(2023, 6, 2),
        eventTime: const TimeOfDay(hour: 10, minute: 0),
        location: 'Eiffel Tower, Paris',
        coordinates: {'lat': 48.8584, 'lng': 2.2945},
        eventType: 'sightseeing',
        hasARContent: false,
        arContentId: null,
      ),
      TimelineEvent(
        id: 'event-2',
        title: 'Louvre Museum',
        description: 'Explore the famous Louvre Museum',
        eventDate: DateTime(2023, 6, 3),
        eventTime: const TimeOfDay(hour: 13, minute: 0),
        location: 'Louvre Museum, Paris',
        coordinates: {'lat': 48.8606, 'lng': 2.3376},
        eventType: 'museum',
        hasARContent: false,
        arContentId: null,
      ),
    ],
    theme: TimelineTheme.standard,
  );

  setUp(() {
    mockTimelineService = MockTimelineService();

    container = ProviderContainer(
      overrides: [
        timelineServiceProvider.overrideWithValue(mockTimelineService),
      ],
    );
  });

  tearDown(() {
    container.dispose();
  });

  test('currentTimelineProvider loads timeline correctly', () async {
    // Arrange
    when(mockTimelineService.getTimeline('timeline-1'))
        .thenAnswer((_) async => sampleTimeline);

    // Act
    final notifier = container.read(currentTimelineProvider.notifier);
    await notifier.loadTimeline('timeline-1');
    final result = container.read(currentTimelineProvider);

    // Assert
    expect(result.value, equals(sampleTimeline));
    verify(mockTimelineService.getTimeline('timeline-1')).called(1);
  });

  test(
      'currentTimelineProvider.addARContentToEvent adds AR content to an event',
      () async {
    // Arrange
    when(mockTimelineService.getTimeline('timeline-1'))
        .thenAnswer((_) async => sampleTimeline);
    when(mockTimelineService.updateTimeline(any))
        .thenAnswer((invocation) async {
      final timeline = invocation.positionalArguments[0] as Timeline;
      return timeline;
    });

    // Act
    final notifier = container.read(currentTimelineProvider.notifier);
    await notifier.loadTimeline('timeline-1');
    await notifier.addARContentToEvent('event-1', 'ar-content-1');
    final result = container.read(currentTimelineProvider);

    // Assert
    expect(result.value, isNotNull);
    final updatedEvent =
        result.value!.events.firstWhere((e) => e.id == 'event-1');
    expect(updatedEvent.hasARContent, isTrue);
    expect(updatedEvent.arContentId, equals('ar-content-1'));
    verify(mockTimelineService.updateTimeline(any)).called(1);
  });

  test(
      'currentTimelineProvider.removeARContentFromEvent removes AR content from an event',
      () async {
    // Arrange
    // Create a timeline with an event that has AR content
    final timelineWithAR = sampleTimeline.copyWith(
      events: [
        sampleTimeline.events[0].copyWith(
          hasARContent: true,
          arContentId: 'ar-content-1',
        ),
        sampleTimeline.events[1],
      ],
    );

    when(mockTimelineService.getTimeline('timeline-1'))
        .thenAnswer((_) async => timelineWithAR);
    when(mockTimelineService.updateTimeline(any))
        .thenAnswer((invocation) async {
      final timeline = invocation.positionalArguments[0] as Timeline;
      return timeline;
    });

    // Act
    final notifier = container.read(currentTimelineProvider.notifier);
    await notifier.loadTimeline('timeline-1');
    await notifier.removeARContentFromEvent('event-1');
    final result = container.read(currentTimelineProvider);

    // Assert
    expect(result.value, isNotNull);
    final updatedEvent =
        result.value!.events.firstWhere((e) => e.id == 'event-1');
    expect(updatedEvent.hasARContent, isFalse);
    expect(updatedEvent.arContentId, isNull);
    verify(mockTimelineService.updateTimeline(any)).called(1);
  });

  test(
      'currentTimelineProvider.getEventsWithARContent returns only events with AR content',
      () async {
    // Arrange
    // Create a timeline with one event that has AR content and one that doesn't
    final timelineWithMixedAR = sampleTimeline.copyWith(
      events: [
        sampleTimeline.events[0].copyWith(
          hasARContent: true,
          arContentId: 'ar-content-1',
        ),
        sampleTimeline.events[1],
      ],
    );

    when(mockTimelineService.getTimeline('timeline-1'))
        .thenAnswer((_) async => timelineWithMixedAR);

    // Act
    final notifier = container.read(currentTimelineProvider.notifier);
    await notifier.loadTimeline('timeline-1');
    final arEvents = notifier.getEventsWithARContent();

    // Assert
    expect(arEvents.length, equals(1));
    expect(arEvents[0].id, equals('event-1'));
    expect(arEvents[0].hasARContent, isTrue);
  });

  test('timelineARFilterProvider toggles AR content filter', () {
    // Arrange
    final provider = StateProvider<bool>((ref) => false);
    container = ProviderContainer(
      overrides: [
        timelineARFilterProvider.overrideWithProvider(provider),
      ],
    );

    // Act & Assert
    // Initially false
    expect(container.read(timelineARFilterProvider), isFalse);

    // Toggle to true
    container.read(timelineARFilterProvider.notifier).state = true;
    expect(container.read(timelineARFilterProvider), isTrue);

    // Toggle back to false
    container.read(timelineARFilterProvider.notifier).state = false;
    expect(container.read(timelineARFilterProvider), isFalse);
  });
}
