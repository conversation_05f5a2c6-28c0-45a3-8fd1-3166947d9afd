import 'package:flutter_test/flutter_test.dart';
import 'package:culture_connect/models/experience.dart';

void main() {
  group('Experience Model Tests', () {
    test('Experience should be created from JSON correctly', () {
      // Arrange
      final json = {
        'id': '1',
        'title': 'Test Experience',
        'description': 'This is a test experience',
        'imageUrl': 'https://example.com/image.jpg',
        'category': 'Cultural Tours',
        'location': 'San Francisco, CA',
        'price': 99.99,
        'rating': 4.5,
        'reviewCount': 100,
        'duration': 120,
        'languages': ['English', 'Spanish'],
        'included': ['Guide', 'Transportation'],
        'excluded': ['Food', 'Drinks'],
        'highlights': ['Visit famous landmarks', 'Learn local history'],
        'itinerary': [
          {
            'title': 'Meeting Point',
            'description': 'Meet at the central square',
            'duration': 15,
          },
          {
            'title': 'Walking Tour',
            'description': 'Explore the historic district',
            'duration': 60,
          },
        ],
        'host': {
          'id': 'host-1',
          'name': '<PERSON>',
          'photoUrl': 'https://example.com/host.jpg',
          'bio': 'Experienced local guide',
          'languages': ['English', 'Spanish'],
          'rating': 4.8,
        },
        'availability': [
          {
            'date': '2023-06-01',
            'slots': [
              {
                'startTime': '09:00',
                'endTime': '11:00',
                'available': true,
              },
              {
                'startTime': '14:00',
                'endTime': '16:00',
                'available': false,
              },
            ],
          },
        ],
        'reviews': [
          {
            'id': 'review-1',
            'userId': 'user-1',
            'userName': 'Jane Smith',
            'rating': 5,
            'comment': 'Amazing experience!',
            'date': '2023-05-15',
          },
        ],
        'latitude': 37.7749,
        'longitude': -122.4194,
        'address': '123 Main St, San Francisco, CA',
        'maxGroupSize': 10,
        'minGroupSize': 2,
        'accessibility': ['Wheelchair accessible', 'Near public transportation'],
        'tags': ['History', 'Walking', 'Architecture'],
        'isFeatured': true,
        'createdAt': '2023-01-01T00:00:00.000Z',
        'updatedAt': '2023-05-01T00:00:00.000Z',
      };
      
      // Act
      final experience = Experience.fromJson(json);
      
      // Assert
      expect(experience.id, '1');
      expect(experience.title, 'Test Experience');
      expect(experience.description, 'This is a test experience');
      expect(experience.imageUrl, 'https://example.com/image.jpg');
      expect(experience.category, 'Cultural Tours');
      expect(experience.location, 'San Francisco, CA');
      expect(experience.price, 99.99);
      expect(experience.rating, 4.5);
      expect(experience.reviewCount, 100);
      expect(experience.duration, 120);
      expect(experience.languages, ['English', 'Spanish']);
      expect(experience.included, ['Guide', 'Transportation']);
      expect(experience.excluded, ['Food', 'Drinks']);
      expect(experience.highlights, ['Visit famous landmarks', 'Learn local history']);
      expect(experience.itinerary.length, 2);
      expect(experience.itinerary[0]['title'], 'Meeting Point');
      expect(experience.host['name'], 'John Doe');
      expect(experience.availability.length, 1);
      expect(experience.reviews.length, 1);
      expect(experience.latitude, 37.7749);
      expect(experience.longitude, -122.4194);
      expect(experience.address, '123 Main St, San Francisco, CA');
      expect(experience.maxGroupSize, 10);
      expect(experience.minGroupSize, 2);
      expect(experience.accessibility, ['Wheelchair accessible', 'Near public transportation']);
      expect(experience.tags, ['History', 'Walking', 'Architecture']);
      expect(experience.isFeatured, true);
      expect(experience.createdAt, '2023-01-01T00:00:00.000Z');
      expect(experience.updatedAt, '2023-05-01T00:00:00.000Z');
    });
    
    test('Experience should convert to JSON correctly', () {
      // Arrange
      final experience = Experience(
        id: '1',
        title: 'Test Experience',
        description: 'This is a test experience',
        imageUrl: 'https://example.com/image.jpg',
        category: 'Cultural Tours',
        location: 'San Francisco, CA',
        price: 99.99,
        rating: 4.5,
        reviewCount: 100,
        duration: 120,
        languages: ['English', 'Spanish'],
        included: ['Guide', 'Transportation'],
        excluded: ['Food', 'Drinks'],
        highlights: ['Visit famous landmarks', 'Learn local history'],
        itinerary: [
          {
            'title': 'Meeting Point',
            'description': 'Meet at the central square',
            'duration': 15,
          },
          {
            'title': 'Walking Tour',
            'description': 'Explore the historic district',
            'duration': 60,
          },
        ],
        host: {
          'id': 'host-1',
          'name': 'John Doe',
          'photoUrl': 'https://example.com/host.jpg',
          'bio': 'Experienced local guide',
          'languages': ['English', 'Spanish'],
          'rating': 4.8,
        },
        availability: [
          {
            'date': '2023-06-01',
            'slots': [
              {
                'startTime': '09:00',
                'endTime': '11:00',
                'available': true,
              },
              {
                'startTime': '14:00',
                'endTime': '16:00',
                'available': false,
              },
            ],
          },
        ],
        reviews: [
          {
            'id': 'review-1',
            'userId': 'user-1',
            'userName': 'Jane Smith',
            'rating': 5,
            'comment': 'Amazing experience!',
            'date': '2023-05-15',
          },
        ],
        latitude: 37.7749,
        longitude: -122.4194,
        address: '123 Main St, San Francisco, CA',
        maxGroupSize: 10,
        minGroupSize: 2,
        accessibility: ['Wheelchair accessible', 'Near public transportation'],
        tags: ['History', 'Walking', 'Architecture'],
        isFeatured: true,
        createdAt: '2023-01-01T00:00:00.000Z',
        updatedAt: '2023-05-01T00:00:00.000Z',
      );
      
      // Act
      final json = experience.toJson();
      
      // Assert
      expect(json['id'], '1');
      expect(json['title'], 'Test Experience');
      expect(json['description'], 'This is a test experience');
      expect(json['imageUrl'], 'https://example.com/image.jpg');
      expect(json['category'], 'Cultural Tours');
      expect(json['location'], 'San Francisco, CA');
      expect(json['price'], 99.99);
      expect(json['rating'], 4.5);
      expect(json['reviewCount'], 100);
      expect(json['duration'], 120);
      expect(json['languages'], ['English', 'Spanish']);
      expect(json['included'], ['Guide', 'Transportation']);
      expect(json['excluded'], ['Food', 'Drinks']);
      expect(json['highlights'], ['Visit famous landmarks', 'Learn local history']);
      expect(json['itinerary'].length, 2);
      expect(json['itinerary'][0]['title'], 'Meeting Point');
      expect(json['host']['name'], 'John Doe');
      expect(json['availability'].length, 1);
      expect(json['reviews'].length, 1);
      expect(json['latitude'], 37.7749);
      expect(json['longitude'], -122.4194);
      expect(json['address'], '123 Main St, San Francisco, CA');
      expect(json['maxGroupSize'], 10);
      expect(json['minGroupSize'], 2);
      expect(json['accessibility'], ['Wheelchair accessible', 'Near public transportation']);
      expect(json['tags'], ['History', 'Walking', 'Architecture']);
      expect(json['isFeatured'], true);
      expect(json['createdAt'], '2023-01-01T00:00:00.000Z');
      expect(json['updatedAt'], '2023-05-01T00:00:00.000Z');
    });
    
    test('Experience.copyWith should create a copy with modified fields', () {
      // Arrange
      final experience = Experience(
        id: '1',
        title: 'Test Experience',
        description: 'This is a test experience',
        imageUrl: 'https://example.com/image.jpg',
        category: 'Cultural Tours',
        location: 'San Francisco, CA',
        price: 99.99,
        rating: 4.5,
        reviewCount: 100,
      );
      
      // Act
      final copy = experience.copyWith(
        title: 'Updated Experience',
        price: 129.99,
        rating: 4.8,
      );
      
      // Assert
      expect(copy.id, '1'); // Unchanged
      expect(copy.title, 'Updated Experience'); // Changed
      expect(copy.description, 'This is a test experience'); // Unchanged
      expect(copy.price, 129.99); // Changed
      expect(copy.rating, 4.8); // Changed
      expect(copy.reviewCount, 100); // Unchanged
    });
  });
}
