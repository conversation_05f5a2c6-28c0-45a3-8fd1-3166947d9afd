import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:culture_connect/models/experience.dart';
import 'package:culture_connect/services/api_service.dart';
import 'package:culture_connect/services/location_service.dart';
import 'package:culture_connect/providers/experiences_provider.dart';
import 'package:culture_connect/providers/filter_options_provider.dart';

// Manual mock classes
class MockApiService extends Mock implements ApiService {}

class MockLocationService extends Mock implements LocationService {}

void main() {
  late MockApiService mockApiService;
  late MockLocationService mockLocationService;
  late ProviderContainer container;

  // Sample experiences for testing
  final sampleExperiences = [
    Experience(
      id: '1',
      title: 'Cultural Tour',
      description: 'Explore local culture',
      imageUrl: 'https://example.com/image1.jpg',
      category: 'Cultural Tours',
      location: 'San Francisco, CA',
      price: 99.99,
      rating: 4.5,
      reviewCount: 100,
      latitude: 37.7749,
      longitude: -122.4194,
      tags: ['History', 'Culture'],
    ),
    Experience(
      id: '2',
      title: 'Food Tour',
      description: 'Taste local cuisine',
      imageUrl: 'https://example.com/image2.jpg',
      category: 'Food & Drink',
      location: 'San Francisco, CA',
      price: 79.99,
      rating: 4.8,
      reviewCount: 150,
      latitude: 37.7750,
      longitude: -122.4195,
      tags: ['Food', 'Culinary'],
    ),
    Experience(
      id: '3',
      title: 'Adventure Tour',
      description: 'Outdoor activities',
      imageUrl: 'https://example.com/image3.jpg',
      category: 'Adventure',
      location: 'San Francisco, CA',
      price: 129.99,
      rating: 4.2,
      reviewCount: 80,
      latitude: 37.7751,
      longitude: -122.4196,
      tags: ['Outdoor', 'Adventure'],
    ),
  ];

  setUp(() {
    mockApiService = MockApiService();
    mockLocationService = MockLocationService();

    // Setup mock API service
    when(mockApiService.getExperiences())
        .thenAnswer((_) async => sampleExperiences);
    when(mockApiService.getExperienceById(any)).thenAnswer((invocation) async {
      final id = invocation.positionalArguments[0] as String;
      return sampleExperiences.firstWhere((exp) => exp.id == id);
    });

    // Setup mock location service
    when(mockLocationService.calculateDistance(any, any, any, any))
        .thenReturn(1000.0);
    when(mockLocationService.formatDistance(any)).thenReturn('1.0 km');

    // Create provider container with overrides
    container = ProviderContainer(
      overrides: [
        apiServiceProvider.overrideWithValue(mockApiService),
        locationServiceProvider.overrideWithValue(mockLocationService),
      ],
    );
  });

  tearDown(() {
    container.dispose();
  });

  group('ExperiencesProvider Tests', () {
    test('experiencesProvider should load experiences', () async {
      // Act
      final asyncValue = await container.read(experiencesProvider.future);

      // Assert
      expect(asyncValue, isA<List<Experience>>());
      expect(asyncValue.length, 3);
      expect(asyncValue[0].id, '1');
      expect(asyncValue[1].id, '2');
      expect(asyncValue[2].id, '3');
    });

    test('experienceProvider should return specific experience by ID',
        () async {
      // Act
      final asyncValue = await container.read(experienceProvider('2').future);

      // Assert
      expect(asyncValue, isA<Experience>());
      expect(asyncValue.id, '2');
      expect(asyncValue.title, 'Food Tour');
    });

    test('filteredExperiencesProvider should filter by category', () async {
      // Arrange
      container
          .read(filterOptionsProvider.notifier)
          .setCategory('Food & Drink');

      // Act
      final asyncValue =
          await container.read(filteredExperiencesProvider.future);

      // Assert
      expect(asyncValue.length, 1);
      expect(asyncValue[0].id, '2');
      expect(asyncValue[0].category, 'Food & Drink');
    });

    test('filteredExperiencesProvider should filter by price range', () async {
      // Arrange
      container
          .read(filterOptionsProvider.notifier)
          .setPriceRange(const RangeValues(70, 100));

      // Act
      final asyncValue =
          await container.read(filteredExperiencesProvider.future);

      // Assert
      expect(asyncValue.length, 2);
      expect(asyncValue[0].id, '1');
      expect(asyncValue[1].id, '2');
    });

    test('filteredExperiencesProvider should filter by rating', () async {
      // Arrange
      container.read(filterOptionsProvider.notifier).setMinRating(4.7);

      // Act
      final asyncValue =
          await container.read(filteredExperiencesProvider.future);

      // Assert
      expect(asyncValue.length, 1);
      expect(asyncValue[0].id, '2');
      expect(asyncValue[0].rating, 4.8);
    });

    test('filteredExperiencesProvider should filter by search query', () async {
      // Arrange
      container.read(filterOptionsProvider.notifier).setSearchQuery('food');

      // Act
      final asyncValue =
          await container.read(filteredExperiencesProvider.future);

      // Assert
      expect(asyncValue.length, 1);
      expect(asyncValue[0].id, '2');
      expect(asyncValue[0].title, 'Food Tour');
    });

    test('filteredExperiencesProvider should filter by tags', () async {
      // Arrange
      container.read(filterOptionsProvider.notifier).setTags(['Outdoor']);

      // Act
      final asyncValue =
          await container.read(filteredExperiencesProvider.future);

      // Assert
      expect(asyncValue.length, 1);
      expect(asyncValue[0].id, '3');
      expect(asyncValue[0].tags, contains('Outdoor'));
    });

    test('filteredExperiencesProvider should sort by price (low to high)',
        () async {
      // Arrange
      container
          .read(filterOptionsProvider.notifier)
          .setSortOption(SortOption.priceLowToHigh);

      // Act
      final asyncValue =
          await container.read(filteredExperiencesProvider.future);

      // Assert
      expect(asyncValue.length, 3);
      expect(asyncValue[0].id, '2'); // 79.99
      expect(asyncValue[1].id, '1'); // 99.99
      expect(asyncValue[2].id, '3'); // 129.99
    });

    test('filteredExperiencesProvider should sort by price (high to low)',
        () async {
      // Arrange
      container
          .read(filterOptionsProvider.notifier)
          .setSortOption(SortOption.priceHighToLow);

      // Act
      final asyncValue =
          await container.read(filteredExperiencesProvider.future);

      // Assert
      expect(asyncValue.length, 3);
      expect(asyncValue[0].id, '3'); // 129.99
      expect(asyncValue[1].id, '1'); // 99.99
      expect(asyncValue[2].id, '2'); // 79.99
    });

    test('filteredExperiencesProvider should sort by rating', () async {
      // Arrange
      container
          .read(filterOptionsProvider.notifier)
          .setSortOption(SortOption.rating);

      // Act
      final asyncValue =
          await container.read(filteredExperiencesProvider.future);

      // Assert
      expect(asyncValue.length, 3);
      expect(asyncValue[0].id, '2'); // 4.8
      expect(asyncValue[1].id, '1'); // 4.5
      expect(asyncValue[2].id, '3'); // 4.2
    });

    test('filteredExperiencesProvider should sort by popularity', () async {
      // Arrange
      container
          .read(filterOptionsProvider.notifier)
          .setSortOption(SortOption.popularity);

      // Act
      final asyncValue =
          await container.read(filteredExperiencesProvider.future);

      // Assert
      expect(asyncValue.length, 3);
      expect(asyncValue[0].id, '2'); // 150 reviews
      expect(asyncValue[1].id, '1'); // 100 reviews
      expect(asyncValue[2].id, '3'); // 80 reviews
    });

    test('featuredExperiencesProvider should return featured experiences',
        () async {
      // Arrange
      final featuredExperience = Experience(
        id: '4',
        title: 'Featured Tour',
        description: 'Special featured tour',
        imageUrl: 'https://example.com/image4.jpg',
        category: 'Featured',
        location: 'San Francisco, CA',
        price: 149.99,
        rating: 4.9,
        reviewCount: 200,
        isFeatured: true,
      );

      when(mockApiService.getExperiences())
          .thenAnswer((_) async => [...sampleExperiences, featuredExperience]);

      // Act
      final asyncValue =
          await container.read(featuredExperiencesProvider.future);

      // Assert
      expect(asyncValue.length, 1);
      expect(asyncValue[0].id, '4');
      expect(asyncValue[0].isFeatured, true);
    });

    test(
        'nearbyExperiencesProvider should return experiences sorted by distance',
        () async {
      // Arrange
      when(mockLocationService.getCurrentPosition())
          .thenAnswer((_) async => null);

      // Mock different distances for each experience
      when(mockLocationService.calculateDistance(any, any, 37.7749, -122.4194))
          .thenReturn(100.0); // Experience 1 is closest
      when(mockLocationService.calculateDistance(any, any, 37.7750, -122.4195))
          .thenReturn(500.0); // Experience 2 is second closest
      when(mockLocationService.calculateDistance(any, any, 37.7751, -122.4196))
          .thenReturn(1000.0); // Experience 3 is furthest

      // Act
      final asyncValue = await container.read(nearbyExperiencesProvider.future);

      // Assert
      expect(asyncValue.length, 3);
      expect(asyncValue[0].id, '1'); // Closest
      expect(asyncValue[1].id, '2'); // Second closest
      expect(asyncValue[2].id, '3'); // Furthest
    });
  });
}
