import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/annotations.dart';
import 'package:culture_connect/models/booking.dart';
import 'package:culture_connect/services/review_service.dart';

// Generate mocks
@GenerateMocks([])
void main() {
  late ReviewService reviewService;
  late Booking testBooking;
  late Booking pendingBooking;
  late Booking pastBooking;
  late Booking completedBooking;

  setUp(() {
    reviewService = ReviewService();

    final now = DateTime.now();
    final tomorrow = DateTime(now.year, now.month, now.day + 1);
    final yesterday = DateTime(now.year, now.month, now.day - 1);

    // Create a test booking for tomorrow (confirmed)
    testBooking = Booking(
      id: 'booking-123',
      experienceId: 'exp-456',
      date: tomorrow,
      timeSlot: TimeSlot(
        startTime: DateTime(tomorrow.year, tomorrow.month, tomorrow.day, 10, 0),
        endTime: DateTime(tomorrow.year, tomorrow.month, tomorrow.day, 12, 0),
      ),
      participantCount: 2,
      totalAmount: 100.0,
      status: BookingStatus.confirmed,
      specialRequirements: 'Vegetarian food',
      createdAt: now,
      updatedAt: now,
    );

    // Create a pending booking
    pendingBooking = Booking(
      id: 'booking-456',
      experienceId: 'exp-456',
      date: tomorrow,
      timeSlot: TimeSlot(
        startTime: DateTime(tomorrow.year, tomorrow.month, tomorrow.day, 14, 0),
        endTime: DateTime(tomorrow.year, tomorrow.month, tomorrow.day, 16, 0),
      ),
      participantCount: 1,
      totalAmount: 50.0,
      status: BookingStatus.pending,
      specialRequirements: '',
      createdAt: now,
      updatedAt: now,
    );

    // Create a past booking (not completed)
    pastBooking = Booking(
      id: 'booking-789',
      experienceId: 'exp-456',
      date: yesterday,
      timeSlot: TimeSlot(
        startTime:
            DateTime(yesterday.year, yesterday.month, yesterday.day, 10, 0),
        endTime:
            DateTime(yesterday.year, yesterday.month, yesterday.day, 12, 0),
      ),
      participantCount: 2,
      totalAmount: 100.0,
      status: BookingStatus.confirmed,
      specialRequirements: '',
      createdAt: now.subtract(const Duration(days: 2)),
      updatedAt: now.subtract(const Duration(days: 2)),
    );

    // Create a completed booking
    completedBooking = Booking(
      id: 'booking-012',
      experienceId: 'exp-456',
      date: yesterday,
      timeSlot: TimeSlot(
        startTime:
            DateTime(yesterday.year, yesterday.month, yesterday.day, 14, 0),
        endTime:
            DateTime(yesterday.year, yesterday.month, yesterday.day, 16, 0),
      ),
      participantCount: 3,
      totalAmount: 150.0,
      status: BookingStatus.completed,
      specialRequirements: '',
      createdAt: now.subtract(const Duration(days: 2)),
      updatedAt: now.subtract(const Duration(days: 1)),
    );
  });

  group('ReviewService', () {
    test('createReview should create a new review', () async {
      // Act
      final review = await reviewService.createReview(
        experienceId: 'exp-456',
        bookingId: 'booking-012',
        userId: 'user-123',
        rating: 4.5,
        comment: 'Great experience!',
      );

      // Assert
      expect(review.id, isNotEmpty);
      expect(review.experienceId, equals('exp-456'));
      expect(review.bookingId, equals('booking-012'));
      expect(review.userId, equals('user-123'));
      expect(review.rating, equals(4.5));
      expect(review.comment, equals('Great experience!'));
      expect(review.photoUrls, isEmpty);
      expect(review.isPublished, isTrue);
    });

    test('getReviewForBooking should return null if no review exists',
        () async {
      // Act
      final review =
          await reviewService.getReviewForBooking('nonexistent-booking');

      // Assert
      expect(review, isNull);
    });

    test('getReviewForBooking should return review if it exists', () async {
      // Arrange
      final createdReview = await reviewService.createReview(
        experienceId: 'exp-456',
        bookingId: 'booking-012',
        userId: 'user-123',
        rating: 4.5,
        comment: 'Great experience!',
      );

      // Act
      final retrievedReview =
          await reviewService.getReviewForBooking('booking-012');

      // Assert
      expect(retrievedReview, isNotNull);
      expect(retrievedReview!.id, equals(createdReview.id));
    });

    test('updateReview should update an existing review', () async {
      // Arrange
      final createdReview = await reviewService.createReview(
        experienceId: 'exp-456',
        bookingId: 'booking-012',
        userId: 'user-123',
        rating: 4.0,
        comment: 'Good experience',
      );

      // Act
      final updatedReview = await reviewService.updateReview(
        reviewId: createdReview.id,
        rating: 4.5,
        comment: 'Great experience!',
      );

      // Assert
      expect(updatedReview.id, equals(createdReview.id));
      expect(updatedReview.rating, equals(4.5));
      expect(updatedReview.comment, equals('Great experience!'));
    });

    test('deleteReview should delete an existing review', () async {
      // Arrange
      final createdReview = await reviewService.createReview(
        experienceId: 'exp-456',
        bookingId: 'booking-012',
        userId: 'user-123',
        rating: 4.0,
        comment: 'Good experience',
      );

      // Act
      final result = await reviewService.deleteReview(createdReview.id);

      // Assert
      expect(result, isTrue);

      // Verify review was deleted
      final retrievedReview =
          await reviewService.getReviewForBooking('booking-012');
      expect(retrievedReview, isNull);
    });

    test('isBookingEligibleForReview should return false for pending bookings',
        () async {
      // Act
      final isEligible =
          await reviewService.isBookingEligibleForReview(pendingBooking);

      // Assert
      expect(isEligible, isFalse);
    });

    test('isBookingEligibleForReview should return false for future bookings',
        () async {
      // Act
      final isEligible =
          await reviewService.isBookingEligibleForReview(testBooking);

      // Assert
      expect(isEligible, isFalse);
    });

    test(
        'isBookingEligibleForReview should return false for past non-completed bookings',
        () async {
      // Act
      final isEligible =
          await reviewService.isBookingEligibleForReview(pastBooking);

      // Assert
      expect(isEligible, isFalse);
    });

    test(
        'isBookingEligibleForReview should return true for completed past bookings',
        () async {
      // Act
      final isEligible =
          await reviewService.isBookingEligibleForReview(completedBooking);

      // Assert
      expect(isEligible, isTrue);
    });

    test(
        'isBookingEligibleForReview should return false if booking already has a review',
        () async {
      // Arrange
      await reviewService.createReview(
        experienceId: 'exp-456',
        bookingId: 'booking-012',
        userId: 'user-123',
        rating: 4.0,
        comment: 'Good experience',
      );

      // Act
      final isEligible =
          await reviewService.isBookingEligibleForReview(completedBooking);

      // Assert
      expect(isEligible, isFalse);
    });

    test('getAverageRatingForExperience should return 0 if no reviews exist',
        () async {
      // Act
      final avgRating =
          await reviewService.getAverageRatingForExperience('nonexistent-exp');

      // Assert
      expect(avgRating, equals(0.0));
    });

    test('getAverageRatingForExperience should calculate correct average',
        () async {
      // Arrange
      await reviewService.createReview(
        experienceId: 'exp-456',
        bookingId: 'booking-1',
        userId: 'user-1',
        rating: 4.0,
        comment: 'Good',
      );

      await reviewService.createReview(
        experienceId: 'exp-456',
        bookingId: 'booking-2',
        userId: 'user-2',
        rating: 5.0,
        comment: 'Excellent',
      );

      await reviewService.createReview(
        experienceId: 'exp-456',
        bookingId: 'booking-3',
        userId: 'user-3',
        rating: 3.0,
        comment: 'Average',
      );

      // Act
      final avgRating =
          await reviewService.getAverageRatingForExperience('exp-456');

      // Assert
      expect(avgRating, equals(4.0));
    });
  });
}
