import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter/services.dart';
import 'package:culture_connect/services/ar_voice_command_service.dart';
import 'package:culture_connect/services/voice_service.dart';

// Generate mocks for dependencies
@GenerateMocks([SharedPreferences, VoiceService])
import 'ar_voice_command_service_test.mocks.dart';

void main() {
  late ARVoiceCommandService service;
  late MockSharedPreferences mockPreferences;
  late MockVoiceService mockVoiceService;

  setUp(() {
    mockPreferences = MockSharedPreferences();
    mockVoiceService = MockVoiceService();
    
    // Setup mock preferences
    when(mockPreferences.getBool('voice_commands_enabled')).thenReturn(true);
    when(mockPreferences.getStringList('custom_voice_commands')).thenReturn([]);
    when(mockPreferences.setBool(any, any)).thenAnswer((_) async => true);
    when(mockPreferences.setStringList(any, any)).thenAnswer((_) async => true);
    
    // Setup mock voice service
    when(mockVoiceService.initialize()).thenAnswer((_) async => true);
    when(mockVoiceService.startListening()).thenAnswer((_) async => true);
    when(mockVoiceService.stopListening()).thenAnswer((_) async => true);
    
    // Create service instance
    service = ARVoiceCommandService(
      preferences: mockPreferences,
      voiceService: mockVoiceService,
    );
  });

  group('ARVoiceCommandService Tests', () {
    test('initialize should setup voice commands', () async {
      // Act
      final result = await service.initialize();
      
      // Assert
      expect(result, true);
      verify(mockVoiceService.initialize()).called(1);
      verify(mockPreferences.getBool('voice_commands_enabled')).called(1);
    });

    test('startListening should start voice recognition', () async {
      // Arrange
      await service.initialize();
      
      // Act
      final result = await service.startListening();
      
      // Assert
      expect(result, true);
      verify(mockVoiceService.startListening()).called(1);
    });

    test('stopListening should stop voice recognition', () async {
      // Arrange
      await service.initialize();
      await service.startListening();
      
      // Act
      final result = await service.stopListening();
      
      // Assert
      expect(result, true);
      verify(mockVoiceService.stopListening()).called(1);
    });

    test('processCommand should recognize zoom command', () async {
      // Arrange
      await service.initialize();
      
      // Act
      final result = await service.processCommand('zoom in');
      
      // Assert
      expect(result.recognized, true);
      expect(result.command, 'zoom');
      expect(result.parameters['direction'], 'in');
    });

    test('processCommand should recognize rotate command', () async {
      // Arrange
      await service.initialize();
      
      // Act
      final result = await service.processCommand('rotate right');
      
      // Assert
      expect(result.recognized, true);
      expect(result.command, 'rotate');
      expect(result.parameters['direction'], 'right');
    });

    test('processCommand should recognize info command', () async {
      // Arrange
      await service.initialize();
      
      // Act
      final result = await service.processCommand('show information');
      
      // Assert
      expect(result.recognized, true);
      expect(result.command, 'info');
    });

    test('processCommand should recognize navigation command', () async {
      // Arrange
      await service.initialize();
      
      // Act
      final result = await service.processCommand('navigate to Eiffel Tower');
      
      // Assert
      expect(result.recognized, true);
      expect(result.command, 'navigate');
      expect(result.parameters['destination'], 'Eiffel Tower');
    });

    test('processCommand should not recognize invalid commands', () async {
      // Arrange
      await service.initialize();
      
      // Act
      final result = await service.processCommand('invalid command');
      
      // Assert
      expect(result.recognized, false);
    });

    test('toggleVoiceCommands should toggle voice commands', () async {
      // Arrange
      await service.initialize();
      final initialValue = service.isVoiceCommandsEnabled;
      
      // Act
      await service.toggleVoiceCommands();
      
      // Assert
      expect(service.isVoiceCommandsEnabled, !initialValue);
      verify(mockPreferences.setBool('voice_commands_enabled', !initialValue)).called(1);
    });

    test('addCustomCommand should add custom command', () async {
      // Arrange
      await service.initialize();
      
      // Act
      await service.addCustomCommand(
        phrase: 'custom action',
        command: 'custom',
        parameters: {'param': 'value'},
      );
      
      // Process the custom command
      final result = await service.processCommand('custom action');
      
      // Assert
      expect(result.recognized, true);
      expect(result.command, 'custom');
      expect(result.parameters['param'], 'value');
    });

    test('removeCustomCommand should remove custom command', () async {
      // Arrange
      await service.initialize();
      await service.addCustomCommand(
        phrase: 'custom action',
        command: 'custom',
        parameters: {'param': 'value'},
      );
      
      // Act
      await service.removeCustomCommand(phrase: 'custom action');
      
      // Process the removed custom command
      final result = await service.processCommand('custom action');
      
      // Assert
      expect(result.recognized, false);
    });

    test('getAvailableCommands should return all commands', () async {
      // Arrange
      await service.initialize();
      
      // Act
      final commands = service.getAvailableCommands();
      
      // Assert
      expect(commands, isA<List<String>>());
      expect(commands.isNotEmpty, true);
      expect(commands.contains('zoom in'), true);
      expect(commands.contains('rotate left'), true);
    });
  });
}
