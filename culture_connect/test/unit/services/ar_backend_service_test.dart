import 'dart:convert';
import 'dart:io';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:dio/dio.dart';
import 'package:path_provider/path_provider.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:culture_connect/services/ar_backend_service.dart';
import 'package:culture_connect/models/landmark.dart';
import 'package:culture_connect/models/ar_model.dart';

// Generate mocks for dependencies
@GenerateMocks([Dio, Directory, File, Connectivity])
import 'ar_backend_service_test.mocks.dart';

// Override for path_provider in tests
Directory? _tempDirectory;
Future<Directory> getTemporaryDirectoryOverride() async {
  return _tempDirectory!;
}

void main() {
  late ARBackendService service;
  late MockDio mockDio;
  late MockDirectory mockDirectory;
  late MockConnectivity mockConnectivity;

  setUp(() {
    mockDio = MockDio();
    mockDirectory = MockDirectory();
    mockConnectivity = MockConnectivity();
    
    // Setup directory mock
    when(mockDirectory.path).thenReturn('/mock/cache/path');
    when(mockDirectory.exists()).thenAnswer((_) async => true);
    
    // Setup connectivity mock
    when(mockConnectivity.checkConnectivity())
        .thenAnswer((_) async => ConnectivityResult.wifi);
    
    // Mock path_provider
    TestWidgetsFlutterBinding.ensureInitialized();
    _tempDirectory = mockDirectory;
    getTemporaryDirectory = getTemporaryDirectoryOverride;
    
    // Create service instance
    service = ARBackendService();
    
    // Initialize the service
    service.initialize();
    
    // Replace the Dio instance with our mock
    // Note: This assumes ARBackendService has a setter for testing
    // If not available, you would need to use reflection or modify the service
    service.dioForTesting = mockDio;
  });

  tearDown(() {
    // Reset path_provider override
    _tempDirectory = null;
    getTemporaryDirectory = getTemporaryDirectory;
  });

  group('ARBackendService Tests', () {
    final mockLandmarksJson = {
      'landmarks': [
        {
          'id': '1',
          'name': 'Eiffel Tower',
          'description': 'Famous landmark in Paris',
          'historicalSignificance': 'Built for the 1889 World Fair',
          'imageUrl': 'https://example.com/eiffel.jpg',
          'rating': 4.8,
          'reviewCount': 1250,
          'tags': ['monument', 'paris', 'france'],
          'translations': {'fr': 'Tour Eiffel', 'es': 'Torre Eiffel'},
          'location': {
            'latitude': 48.8584,
            'longitude': 2.2945,
            'address': 'Champ de Mars, 5 Avenue Anatole France, 75007 Paris'
          },
          'arContent': {
            'id': 'ar-eiffel-tower',
            'available': true,
            'modelUrl': 'https://example.com/eiffel-model.glb'
          }
        },
        {
          'id': '2',
          'name': 'Statue of Liberty',
          'description': 'Famous landmark in New York',
          'historicalSignificance': 'Gift from France to the United States',
          'imageUrl': 'https://example.com/liberty.jpg',
          'rating': 4.7,
          'reviewCount': 980,
          'tags': ['monument', 'new york', 'usa'],
          'translations': {'fr': 'Statue de la Liberté', 'es': 'Estatua de la Libertad'},
          'location': {
            'latitude': 40.6892,
            'longitude': -74.0445,
            'address': 'New York, NY 10004'
          },
          'arContent': {
            'id': 'ar-statue-liberty',
            'available': true,
            'modelUrl': 'https://example.com/liberty-model.glb'
          }
        }
      ]
    };

    test('getLandmarks should return landmarks on success', () async {
      // Arrange
      when(mockDio.get(any, queryParameters: anyNamed('queryParameters')))
          .thenAnswer((_) async => Response(
                requestOptions: RequestOptions(path: ''),
                data: mockLandmarksJson,
                statusCode: 200,
              ));
      
      // Act
      final landmarks = await service.getLandmarks(
        latitude: 48.8584,
        longitude: 2.2945,
        radius: 1000,
      );
      
      // Assert
      expect(landmarks, isA<List<Landmark>>());
      expect(landmarks.length, 2);
      expect(landmarks[0].name, 'Eiffel Tower');
      expect(landmarks[1].name, 'Statue of Liberty');
    });

    test('getLandmarks should return mock data on error', () async {
      // Arrange
      when(mockDio.get(any, queryParameters: anyNamed('queryParameters')))
          .thenThrow(DioException(
            requestOptions: RequestOptions(path: ''),
            error: 'Network error',
          ));
      
      // Act
      final landmarks = await service.getLandmarks(
        latitude: 48.8584,
        longitude: 2.2945,
        radius: 1000,
      );
      
      // Assert
      expect(landmarks, isA<List<Landmark>>());
      expect(landmarks.isNotEmpty, true);
    });

    test('getARModel should return AR model on success', () async {
      // Arrange
      final mockModelJson = {
        'id': 'model-1',
        'name': 'Eiffel Tower Model',
        'modelUrl': 'https://example.com/eiffel-model.glb',
        'textureUrls': ['https://example.com/eiffel-texture.jpg'],
        'metadata': {'scale': 0.5, 'rotationY': 180},
        'type': 'glb',
        'scale': 1.0,
        'complexity': 'medium'
      };
      
      when(mockDio.get(any))
          .thenAnswer((_) async => Response(
                requestOptions: RequestOptions(path: ''),
                data: mockModelJson,
                statusCode: 200,
              ));
      
      // Act
      final model = await service.getARModel('model-1');
      
      // Assert
      expect(model, isA<ARModel>());
      expect(model!.id, 'model-1');
      expect(model.name, 'Eiffel Tower Model');
      expect(model.modelUrl, 'https://example.com/eiffel-model.glb');
    });

    test('getARModel should return null on error', () async {
      // Arrange
      when(mockDio.get(any))
          .thenThrow(DioException(
            requestOptions: RequestOptions(path: ''),
            error: 'Network error',
          ));
      
      // Act
      final model = await service.getARModel('model-1');
      
      // Assert
      expect(model, isNull);
    });
  });
}
