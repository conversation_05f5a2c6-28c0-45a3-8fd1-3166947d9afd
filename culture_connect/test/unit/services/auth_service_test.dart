import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:culture_connect/services/auth_service.dart';
import 'package:culture_connect/models/user_model.dart';

// Manual mock classes
class MockFirebaseAuth extends Mock implements FirebaseAuth {}

class MockUserCredential extends Mock implements UserCredential {}

class MockUser extends Mock implements User {}

class MockGoogleSignIn extends Mock implements GoogleSignIn {}

class MockGoogleSignInAccount extends Mock implements GoogleSignInAccount {}

class MockGoogleSignInAuthentication extends Mo<PERSON>
    implements GoogleSignInAuthentication {}

void main() {
  late AuthService authService;
  late MockFirebaseAuth mockFirebaseAuth;
  late MockUserCredential mockUserCredential;
  late MockUser mockUser;
  late MockGoogleSignIn mockGoogleSignIn;
  late MockGoogleSignInAccount mockGoogleSignInAccount;
  late MockGoogleSignInAuthentication mockGoogleSignInAuthentication;

  setUp(() {
    mockFirebaseAuth = MockFirebaseAuth();
    mockUserCredential = MockUserCredential();
    mockUser = MockUser();
    mockGoogleSignIn = MockGoogleSignIn();
    mockGoogleSignInAccount = MockGoogleSignInAccount();
    mockGoogleSignInAuthentication = MockGoogleSignInAuthentication();

    // Setup mock user
    when(mockUser.uid).thenReturn('test-uid');
    when(mockUser.email).thenReturn('<EMAIL>');
    when(mockUser.displayName).thenReturn('Test User');
    when(mockUser.photoURL).thenReturn('https://example.com/photo.jpg');
    when(mockUser.emailVerified).thenReturn(true);

    // Setup mock user credential
    when(mockUserCredential.user).thenReturn(mockUser);

    // Setup mock Google Sign In
    when(mockGoogleSignInAccount.authentication)
        .thenAnswer((_) async => mockGoogleSignInAuthentication);
    when(mockGoogleSignInAuthentication.accessToken).thenReturn('access-token');
    when(mockGoogleSignInAuthentication.idToken).thenReturn('id-token');

    // Create auth service with mocks
    authService = AuthService(
      firebaseAuth: mockFirebaseAuth,
      googleSignIn: mockGoogleSignIn,
    );
  });

  group('AuthService Tests', () {
    test('signInWithEmailAndPassword should return UserModel on success',
        () async {
      // Arrange
      when(mockFirebaseAuth.signInWithEmailAndPassword(
        email: '<EMAIL>',
        password: 'password123',
      )).thenAnswer((_) async => mockUserCredential);

      // Act
      final result = await authService.signInWithEmailAndPassword(
        email: '<EMAIL>',
        password: 'password123',
      );

      // Assert
      expect(result, isA<UserModel>());
      expect(result.uid, 'test-uid');
      expect(result.email, '<EMAIL>');
      expect(result.displayName, 'Test User');
      expect(result.photoUrl, 'https://example.com/photo.jpg');
      expect(result.isVerified, true);
    });

    test('signInWithEmailAndPassword should throw exception on failure',
        () async {
      // Arrange
      when(mockFirebaseAuth.signInWithEmailAndPassword(
        email: '<EMAIL>',
        password: 'wrong-password',
      )).thenThrow(FirebaseAuthException(code: 'wrong-password'));

      // Act & Assert
      expect(
        () => authService.signInWithEmailAndPassword(
          email: '<EMAIL>',
          password: 'wrong-password',
        ),
        throwsA(isA<FirebaseAuthException>()),
      );
    });

    test('registerWithEmailAndPassword should return UserModel on success',
        () async {
      // Arrange
      when(mockFirebaseAuth.createUserWithEmailAndPassword(
        email: '<EMAIL>',
        password: 'password123',
      )).thenAnswer((_) async => mockUserCredential);

      // Act
      final result = await authService.registerWithEmailAndPassword(
        email: '<EMAIL>',
        password: 'password123',
        fullName: 'New User',
      );

      // Assert
      expect(result, isA<UserModel>());
      expect(result.uid, 'test-uid');
      expect(result.email, '<EMAIL>');
    });

    test('signInWithGoogle should return UserModel on success', () async {
      // Arrange
      when(mockGoogleSignIn.signIn())
          .thenAnswer((_) async => mockGoogleSignInAccount);
      when(mockFirebaseAuth.signInWithCredential(any))
          .thenAnswer((_) async => mockUserCredential);

      // Act
      final result = await authService.signInWithGoogle();

      // Assert
      expect(result, isA<UserModel>());
      expect(result.uid, 'test-uid');
      expect(result.email, '<EMAIL>');
    });

    test('signOut should complete successfully', () async {
      // Arrange
      when(mockFirebaseAuth.signOut()).thenAnswer((_) async => null);
      when(mockGoogleSignIn.signOut()).thenAnswer((_) async => null);

      // Act & Assert
      expect(authService.signOut(), completes);
    });

    test('getCurrentUser should return UserModel when user is signed in',
        () async {
      // Arrange
      when(mockFirebaseAuth.currentUser).thenReturn(mockUser);

      // Act
      final result = await authService.getCurrentUser();

      // Assert
      expect(result, isA<UserModel>());
      expect(result?.uid, 'test-uid');
      expect(result?.email, '<EMAIL>');
    });

    test('getCurrentUser should return null when no user is signed in',
        () async {
      // Arrange
      when(mockFirebaseAuth.currentUser).thenReturn(null);

      // Act
      final result = await authService.getCurrentUser();

      // Assert
      expect(result, isNull);
    });

    test('sendEmailVerification should complete successfully', () async {
      // Arrange
      when(mockFirebaseAuth.currentUser).thenReturn(mockUser);
      when(mockUser.sendEmailVerification()).thenAnswer((_) async => null);

      // Act & Assert
      expect(authService.sendEmailVerification(), completes);
    });

    test('resetPassword should complete successfully', () async {
      // Arrange
      when(mockFirebaseAuth.sendPasswordResetEmail(email: '<EMAIL>'))
          .thenAnswer((_) async => null);

      // Act & Assert
      expect(
        authService.resetPassword(email: '<EMAIL>'),
        completes,
      );
    });
  });
}
