import 'dart:convert';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import 'package:culture_connect/models/message_model.dart';
import 'package:culture_connect/models/translation/language_model.dart';
import 'package:culture_connect/models/translation/message_translation_metadata.dart';
import 'package:culture_connect/models/translation/group_translation_model.dart';
import 'package:culture_connect/models/translation/translation_cultural_context.dart';
import 'package:culture_connect/models/translation/translation_slang_idiom.dart';
import 'package:culture_connect/models/translation/translation_pronunciation.dart';
import 'package:culture_connect/services/group_translation_service.dart';
import 'package:culture_connect/services/voice_translation/message_translation_service.dart';
import 'package:culture_connect/services/voice_translation/cultural_context_service.dart';
import 'package:culture_connect/services/voice_translation/slang_idiom_service.dart';
import 'package:culture_connect/services/voice_translation/pronunciation_service.dart';
import 'package:culture_connect/services/language_detection_service.dart';
import 'package:culture_connect/services/translation_metrics_service.dart';

// Generate mocks for dependencies
@GenerateMocks([
  http.Client,
  SharedPreferences,
  MessageTranslationService,
  CulturalContextService,
  SlangIdiomService,
  PronunciationService,
  LanguageDetectionService,
  TranslationMetricsService,
])
import 'group_translation_service_test.mocks.dart';

void main() {
  late GroupTranslationService service;
  late MockClient mockClient;
  late MockSharedPreferences mockPrefs;
  late MockMessageTranslationService mockMessageTranslationService;
  late MockCulturalContextService mockCulturalContextService;
  late MockSlangIdiomService mockSlangIdiomService;
  late MockPronunciationService mockPronunciationService;
  late MockLanguageDetectionService mockLanguageDetectionService;
  late MockTranslationMetricsService mockMetricsService;

  // Sample data for testing
  final sampleGroupId = 'group-123';
  final sampleUserId = 'user-456';
  final sampleMessageId = 'message-789';

  final sampleLanguage = LanguageModel(
    code: 'en',
    name: 'English',
  );

  final sampleParticipantPreference = ParticipantLanguagePreference(
    userId: sampleUserId,
    displayName: 'Test User',
    preferredLanguage: sampleLanguage,
  );

  final sampleGroupSettings = GroupTranslationSettings(
    groupId: sampleGroupId,
    participantPreferences: {
      sampleUserId: sampleParticipantPreference,
    },
  );

  final sampleMessage = MessageModel(
    id: sampleMessageId,
    chatId: sampleGroupId,
    senderId: 'sender-123',
    recipientId: '',
    text: 'Hello, world!',
    timestamp: DateTime.now(),
    status: MessageStatus.sent,
    type: MessageType.text,
    originalLanguage: 'en',
  );

  final sampleTranslationMetadata = MessageTranslationMetadata(
    messageId: sampleMessageId,
    originalText: 'Hello, world!',
    translatedText: 'Bonjour, monde!',
    sourceLanguage: 'en',
    targetLanguage: 'fr',
    confidence: 0.95,
    culturalContext: null,
    slangIdiom: null,
    pronunciation: null,
  );

  final sampleGroupMessageTranslation = GroupMessageTranslation(
    messageId: sampleMessageId,
    originalText: 'Hello, world!',
    originalLanguageCode: 'en',
    translations: {
      'fr': 'Bonjour, monde!',
      'es': '¡Hola, mundo!',
    },
    translatedAt: DateTime.now(),
  );

  setUp(() {
    mockClient = MockClient();
    mockPrefs = MockSharedPreferences();
    mockMessageTranslationService = MockMessageTranslationService();
    mockCulturalContextService = MockCulturalContextService();
    mockSlangIdiomService = MockSlangIdiomService();
    mockPronunciationService = MockPronunciationService();
    mockLanguageDetectionService = MockLanguageDetectionService();
    mockMetricsService = MockTranslationMetricsService();

    // Setup mock SharedPreferences
    when(mockPrefs.getString('group_translation_settings')).thenReturn(null);
    when(mockPrefs.getString('group_message_translations')).thenReturn(null);
    when(mockPrefs.setString(any, any)).thenAnswer((_) async => true);

    // Setup mock LanguageDetectionService
    when(mockLanguageDetectionService.detectLanguage(any))
        .thenAnswer((_) async => 'en');

    // Setup mock MessageTranslationService
    when(mockMessageTranslationService.translateMessage(any, any))
        .thenAnswer((_) async => sampleTranslationMetadata);

    // Setup mock TranslationMetricsService
    when(mockMetricsService.recordTranslation(
      sourceLanguage: anyNamed('sourceLanguage'),
      targetLanguage: anyNamed('targetLanguage'),
      durationMs: anyNamed('durationMs'),
      isCached: anyNamed('isCached'),
      isBackground: anyNamed('isBackground'),
    )).thenAnswer((_) async {});
    when(mockMetricsService.recordFailedTranslation()).thenAnswer((_) async {});

    service = GroupTranslationService(
      mockClient,
      mockPrefs,
      mockMessageTranslationService,
      mockCulturalContextService,
      mockSlangIdiomService,
      mockPronunciationService,
      mockLanguageDetectionService,
      mockMetricsService,
    );
  });

  group('GroupTranslationService Tests', () {
    test(
        'getGroupTranslationSettings should return default settings if none exist',
        () async {
      // Act
      final settings = await service.getGroupTranslationSettings(sampleGroupId);

      // Assert
      expect(settings.groupId, sampleGroupId);
      expect(settings.participantPreferences, isEmpty);
      expect(settings.autoDetectLanguages, isTrue);
    });

    test('updateGroupTranslationSettings should update settings', () async {
      // Act
      final updatedSettings =
          await service.updateGroupTranslationSettings(sampleGroupSettings);

      // Assert
      expect(updatedSettings.groupId, sampleGroupId);
      expect(updatedSettings.participantPreferences[sampleUserId], isNotNull);
      expect(updatedSettings.participantPreferences[sampleUserId]!.userId,
          sampleUserId);

      // Verify settings were saved to SharedPreferences
      verify(mockPrefs.setString(any, any)).called(1);
    });

    test('updateParticipantPreference should update participant preference',
        () async {
      // Arrange
      final newPreference = ParticipantLanguagePreference(
        userId: sampleUserId,
        displayName: 'Updated User',
        preferredLanguage: LanguageModel(code: 'fr', name: 'French'),
      );

      // Act
      final updatedSettings = await service.updateParticipantPreference(
        sampleGroupId,
        newPreference,
      );

      // Assert
      expect(updatedSettings.participantPreferences[sampleUserId], isNotNull);
      expect(updatedSettings.participantPreferences[sampleUserId]!.displayName,
          'Updated User');
      expect(
          updatedSettings
              .participantPreferences[sampleUserId]!.preferredLanguage.code,
          'fr');
    });

    test('removeParticipantPreference should remove participant preference',
        () async {
      // Arrange
      await service.updateGroupTranslationSettings(sampleGroupSettings);

      // Act
      final updatedSettings = await service.removeParticipantPreference(
        sampleGroupId,
        sampleUserId,
      );

      // Assert
      expect(updatedSettings.participantPreferences[sampleUserId], isNull);
    });

    test(
        'translateGroupMessage should translate message to all target languages',
        () async {
      // Arrange
      final settings = GroupTranslationSettings(
        groupId: sampleGroupId,
        participantPreferences: {
          'user1': ParticipantLanguagePreference(
            userId: 'user1',
            displayName: 'User 1',
            preferredLanguage: LanguageModel(code: 'fr', name: 'French'),
          ),
          'user2': ParticipantLanguagePreference(
            userId: 'user2',
            displayName: 'User 2',
            preferredLanguage: LanguageModel(code: 'es', name: 'Spanish'),
          ),
        },
      );

      // Act
      final translation =
          await service.translateGroupMessage(sampleMessage, settings);

      // Assert
      expect(translation.messageId, sampleMessageId);
      expect(translation.originalText, 'Hello, world!');
      expect(translation.originalLanguageCode, 'en');

      // Verify MessageTranslationService was called for each target language
      verify(mockMessageTranslationService.translateMessage(any, 'fr'))
          .called(1);
      verify(mockMessageTranslationService.translateMessage(any, 'es'))
          .called(1);
    });

    test('getTranslationForUser should return translated text for user',
        () async {
      // Arrange
      final settings = GroupTranslationSettings(
        groupId: sampleGroupId,
        participantPreferences: {
          sampleUserId: ParticipantLanguagePreference(
            userId: sampleUserId,
            displayName: 'Test User',
            preferredLanguage: LanguageModel(code: 'fr', name: 'French'),
          ),
        },
      );

      // Mock the translateGroupMessage method
      // This is a bit of a hack since we can't easily mock a method on the class under test
      // In a real test, you might use a test double or refactor for better testability
      await service.updateGroupTranslationSettings(settings);

      // Act
      final translatedText = await service.getTranslationForUser(
        sampleMessage,
        sampleUserId,
        settings,
      );

      // Assert
      expect(translatedText, 'Bonjour, monde!');
    });

    test(
        'getTranslationMetadataForUser should return translation metadata for user',
        () async {
      // Arrange
      final settings = GroupTranslationSettings(
        groupId: sampleGroupId,
        participantPreferences: {
          sampleUserId: ParticipantLanguagePreference(
            userId: sampleUserId,
            displayName: 'Test User',
            preferredLanguage: LanguageModel(code: 'fr', name: 'French'),
          ),
        },
      );

      // Act
      final metadata = await service.getTranslationMetadataForUser(
        sampleMessage,
        sampleUserId,
        settings,
      );

      // Assert
      expect(metadata, isNotNull);
      expect(metadata!.messageId, sampleMessageId);
      expect(metadata.originalText, 'Hello, world!');
      expect(metadata.translatedText, 'Bonjour, monde!');
      expect(metadata.sourceLanguage, 'en');
      expect(metadata.targetLanguage, 'fr');
    });

    test('clearGroupTranslationCache should clear cache for a group', () async {
      // Arrange
      await service.updateGroupTranslationSettings(sampleGroupSettings);

      // Act
      await service.clearGroupTranslationCache(sampleGroupId);

      // Assert
      final settings = await service.getGroupTranslationSettings(sampleGroupId);
      expect(settings.participantPreferences, isEmpty);
    });
  });
}
