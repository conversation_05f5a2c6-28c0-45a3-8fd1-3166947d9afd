// Mocks generated by <PERSON><PERSON>to 5.4.5 from annotations
// in culture_connect/test/unit/services/map_cache_manager_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i5;
import 'dart:convert' as _i10;
import 'dart:io' as _i3;
import 'dart:typed_data' as _i11;
import 'dart:ui' as _i9;

import 'package:culture_connect/services/error_handling_service.dart' as _i7;
import 'package:culture_connect/services/logging_service.dart' as _i4;
import 'package:flutter/material.dart' as _i8;
import 'package:http/http.dart' as _i2;
import 'package:mockito/mockito.dart' as _i1;
import 'package:mockito/src/dummies.dart' as _i6;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeResponse_0 extends _i1.SmartFake implements _i2.Response {
  _FakeResponse_0(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeStreamedResponse_1 extends _i1.SmartFake
    implements _i2.StreamedResponse {
  _FakeStreamedResponse_1(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeUri_2 extends _i1.SmartFake implements Uri {
  _FakeUri_2(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeDirectory_3 extends _i1.SmartFake implements _i3.Directory {
  _FakeDirectory_3(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeFileSystemEntity_4 extends _i1.SmartFake
    implements _i3.FileSystemEntity {
  _FakeFileSystemEntity_4(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeFileStat_5 extends _i1.SmartFake implements _i3.FileStat {
  _FakeFileStat_5(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeFile_6 extends _i1.SmartFake implements _i3.File {
  _FakeFile_6(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeDateTime_7 extends _i1.SmartFake implements DateTime {
  _FakeDateTime_7(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeRandomAccessFile_8 extends _i1.SmartFake
    implements _i3.RandomAccessFile {
  _FakeRandomAccessFile_8(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeIOSink_9 extends _i1.SmartFake implements _i3.IOSink {
  _FakeIOSink_9(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

/// A class which mocks [LoggingService].
///
/// See the documentation for Mockito's code generation for more information.
class MockLoggingService extends _i1.Mock implements _i4.LoggingService {
  MockLoggingService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i5.Future<void> initialize() => (super.noSuchMethod(
        Invocation.method(
          #initialize,
          [],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  void setLogLevel(_i4.LogLevel? level) => super.noSuchMethod(
        Invocation.method(
          #setLogLevel,
          [level],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void configure({
    bool? logToConsole,
    bool? logToFile,
    bool? logToCrashlytics,
    int? maxLogFileSize,
    int? maxLogFiles,
  }) =>
      super.noSuchMethod(
        Invocation.method(
          #configure,
          [],
          {
            #logToConsole: logToConsole,
            #logToFile: logToFile,
            #logToCrashlytics: logToCrashlytics,
            #maxLogFileSize: maxLogFileSize,
            #maxLogFiles: maxLogFiles,
          },
        ),
        returnValueForMissingStub: null,
      );

  @override
  void verbose(
    String? tag,
    String? message, [
    dynamic data,
    StackTrace? stackTrace,
  ]) =>
      super.noSuchMethod(
        Invocation.method(
          #verbose,
          [
            tag,
            message,
            data,
            stackTrace,
          ],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void debug(
    String? tag,
    String? message, [
    dynamic data,
    StackTrace? stackTrace,
  ]) =>
      super.noSuchMethod(
        Invocation.method(
          #debug,
          [
            tag,
            message,
            data,
            stackTrace,
          ],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void info(
    String? tag,
    String? message, [
    dynamic data,
    StackTrace? stackTrace,
  ]) =>
      super.noSuchMethod(
        Invocation.method(
          #info,
          [
            tag,
            message,
            data,
            stackTrace,
          ],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void warning(
    String? tag,
    String? message, [
    dynamic data,
    StackTrace? stackTrace,
  ]) =>
      super.noSuchMethod(
        Invocation.method(
          #warning,
          [
            tag,
            message,
            data,
            stackTrace,
          ],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void error(
    String? tag,
    String? message, [
    dynamic error,
    StackTrace? stackTrace,
  ]) =>
      super.noSuchMethod(
        Invocation.method(
          #error,
          [
            tag,
            message,
            error,
            stackTrace,
          ],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void critical(
    String? tag,
    String? message, [
    dynamic error,
    StackTrace? stackTrace,
  ]) =>
      super.noSuchMethod(
        Invocation.method(
          #critical,
          [
            tag,
            message,
            error,
            stackTrace,
          ],
        ),
        returnValueForMissingStub: null,
      );

  @override
  _i5.Future<String> getConnectivityStatus() => (super.noSuchMethod(
        Invocation.method(
          #getConnectivityStatus,
          [],
        ),
        returnValue: _i5.Future<String>.value(_i6.dummyValue<String>(
          this,
          Invocation.method(
            #getConnectivityStatus,
            [],
          ),
        )),
      ) as _i5.Future<String>);

  @override
  void dispose() => super.noSuchMethod(
        Invocation.method(
          #dispose,
          [],
        ),
        returnValueForMissingStub: null,
      );
}

/// A class which mocks [ErrorHandlingService].
///
/// See the documentation for Mockito's code generation for more information.
class MockErrorHandlingService extends _i1.Mock
    implements _i7.ErrorHandlingService {
  MockErrorHandlingService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i5.Future<void> initialize() => (super.noSuchMethod(
        Invocation.method(
          #initialize,
          [],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<void> handleError({
    required Object? error,
    required String? context,
    StackTrace? stackTrace,
    _i7.ErrorType? type = _i7.ErrorType.unknown,
    _i7.ErrorSeverity? severity = _i7.ErrorSeverity.medium,
    Map<String, dynamic>? additionalData,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #handleError,
          [],
          {
            #error: error,
            #context: context,
            #stackTrace: stackTrace,
            #type: type,
            #severity: severity,
            #additionalData: additionalData,
          },
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  void showErrorDialog(
    _i8.BuildContext? context,
    String? title,
    String? message, {
    String? buttonText,
    _i9.VoidCallback? onPressed,
  }) =>
      super.noSuchMethod(
        Invocation.method(
          #showErrorDialog,
          [
            context,
            title,
            message,
          ],
          {
            #buttonText: buttonText,
            #onPressed: onPressed,
          },
        ),
        returnValueForMissingStub: null,
      );

  @override
  void showErrorSnackBar(
    _i8.BuildContext? context,
    String? message, {
    Duration? duration = const Duration(seconds: 4),
  }) =>
      super.noSuchMethod(
        Invocation.method(
          #showErrorSnackBar,
          [
            context,
            message,
          ],
          {#duration: duration},
        ),
        returnValueForMissingStub: null,
      );
}

/// A class which mocks [Client].
///
/// See the documentation for Mockito's code generation for more information.
class MockClient extends _i1.Mock implements _i2.Client {
  MockClient() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i5.Future<_i2.Response> head(
    Uri? url, {
    Map<String, String>? headers,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #head,
          [url],
          {#headers: headers},
        ),
        returnValue: _i5.Future<_i2.Response>.value(_FakeResponse_0(
          this,
          Invocation.method(
            #head,
            [url],
            {#headers: headers},
          ),
        )),
      ) as _i5.Future<_i2.Response>);

  @override
  _i5.Future<_i2.Response> get(
    Uri? url, {
    Map<String, String>? headers,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #get,
          [url],
          {#headers: headers},
        ),
        returnValue: _i5.Future<_i2.Response>.value(_FakeResponse_0(
          this,
          Invocation.method(
            #get,
            [url],
            {#headers: headers},
          ),
        )),
      ) as _i5.Future<_i2.Response>);

  @override
  _i5.Future<_i2.Response> post(
    Uri? url, {
    Map<String, String>? headers,
    Object? body,
    _i10.Encoding? encoding,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #post,
          [url],
          {
            #headers: headers,
            #body: body,
            #encoding: encoding,
          },
        ),
        returnValue: _i5.Future<_i2.Response>.value(_FakeResponse_0(
          this,
          Invocation.method(
            #post,
            [url],
            {
              #headers: headers,
              #body: body,
              #encoding: encoding,
            },
          ),
        )),
      ) as _i5.Future<_i2.Response>);

  @override
  _i5.Future<_i2.Response> put(
    Uri? url, {
    Map<String, String>? headers,
    Object? body,
    _i10.Encoding? encoding,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #put,
          [url],
          {
            #headers: headers,
            #body: body,
            #encoding: encoding,
          },
        ),
        returnValue: _i5.Future<_i2.Response>.value(_FakeResponse_0(
          this,
          Invocation.method(
            #put,
            [url],
            {
              #headers: headers,
              #body: body,
              #encoding: encoding,
            },
          ),
        )),
      ) as _i5.Future<_i2.Response>);

  @override
  _i5.Future<_i2.Response> patch(
    Uri? url, {
    Map<String, String>? headers,
    Object? body,
    _i10.Encoding? encoding,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #patch,
          [url],
          {
            #headers: headers,
            #body: body,
            #encoding: encoding,
          },
        ),
        returnValue: _i5.Future<_i2.Response>.value(_FakeResponse_0(
          this,
          Invocation.method(
            #patch,
            [url],
            {
              #headers: headers,
              #body: body,
              #encoding: encoding,
            },
          ),
        )),
      ) as _i5.Future<_i2.Response>);

  @override
  _i5.Future<_i2.Response> delete(
    Uri? url, {
    Map<String, String>? headers,
    Object? body,
    _i10.Encoding? encoding,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #delete,
          [url],
          {
            #headers: headers,
            #body: body,
            #encoding: encoding,
          },
        ),
        returnValue: _i5.Future<_i2.Response>.value(_FakeResponse_0(
          this,
          Invocation.method(
            #delete,
            [url],
            {
              #headers: headers,
              #body: body,
              #encoding: encoding,
            },
          ),
        )),
      ) as _i5.Future<_i2.Response>);

  @override
  _i5.Future<String> read(
    Uri? url, {
    Map<String, String>? headers,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #read,
          [url],
          {#headers: headers},
        ),
        returnValue: _i5.Future<String>.value(_i6.dummyValue<String>(
          this,
          Invocation.method(
            #read,
            [url],
            {#headers: headers},
          ),
        )),
      ) as _i5.Future<String>);

  @override
  _i5.Future<_i11.Uint8List> readBytes(
    Uri? url, {
    Map<String, String>? headers,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #readBytes,
          [url],
          {#headers: headers},
        ),
        returnValue: _i5.Future<_i11.Uint8List>.value(_i11.Uint8List(0)),
      ) as _i5.Future<_i11.Uint8List>);

  @override
  _i5.Future<_i2.StreamedResponse> send(_i2.BaseRequest? request) =>
      (super.noSuchMethod(
        Invocation.method(
          #send,
          [request],
        ),
        returnValue:
            _i5.Future<_i2.StreamedResponse>.value(_FakeStreamedResponse_1(
          this,
          Invocation.method(
            #send,
            [request],
          ),
        )),
      ) as _i5.Future<_i2.StreamedResponse>);

  @override
  void close() => super.noSuchMethod(
        Invocation.method(
          #close,
          [],
        ),
        returnValueForMissingStub: null,
      );
}

/// A class which mocks [Directory].
///
/// See the documentation for Mockito's code generation for more information.
class MockDirectory extends _i1.Mock implements _i3.Directory {
  MockDirectory() {
    _i1.throwOnMissingStub(this);
  }

  @override
  String get path => (super.noSuchMethod(
        Invocation.getter(#path),
        returnValue: _i6.dummyValue<String>(
          this,
          Invocation.getter(#path),
        ),
      ) as String);

  @override
  Uri get uri => (super.noSuchMethod(
        Invocation.getter(#uri),
        returnValue: _FakeUri_2(
          this,
          Invocation.getter(#uri),
        ),
      ) as Uri);

  @override
  _i3.Directory get absolute => (super.noSuchMethod(
        Invocation.getter(#absolute),
        returnValue: _FakeDirectory_3(
          this,
          Invocation.getter(#absolute),
        ),
      ) as _i3.Directory);

  @override
  bool get isAbsolute => (super.noSuchMethod(
        Invocation.getter(#isAbsolute),
        returnValue: false,
      ) as bool);

  @override
  _i3.Directory get parent => (super.noSuchMethod(
        Invocation.getter(#parent),
        returnValue: _FakeDirectory_3(
          this,
          Invocation.getter(#parent),
        ),
      ) as _i3.Directory);

  @override
  _i5.Future<_i3.Directory> create({bool? recursive = false}) =>
      (super.noSuchMethod(
        Invocation.method(
          #create,
          [],
          {#recursive: recursive},
        ),
        returnValue: _i5.Future<_i3.Directory>.value(_FakeDirectory_3(
          this,
          Invocation.method(
            #create,
            [],
            {#recursive: recursive},
          ),
        )),
      ) as _i5.Future<_i3.Directory>);

  @override
  void createSync({bool? recursive = false}) => super.noSuchMethod(
        Invocation.method(
          #createSync,
          [],
          {#recursive: recursive},
        ),
        returnValueForMissingStub: null,
      );

  @override
  _i5.Future<_i3.Directory> createTemp([String? prefix]) => (super.noSuchMethod(
        Invocation.method(
          #createTemp,
          [prefix],
        ),
        returnValue: _i5.Future<_i3.Directory>.value(_FakeDirectory_3(
          this,
          Invocation.method(
            #createTemp,
            [prefix],
          ),
        )),
      ) as _i5.Future<_i3.Directory>);

  @override
  _i3.Directory createTempSync([String? prefix]) => (super.noSuchMethod(
        Invocation.method(
          #createTempSync,
          [prefix],
        ),
        returnValue: _FakeDirectory_3(
          this,
          Invocation.method(
            #createTempSync,
            [prefix],
          ),
        ),
      ) as _i3.Directory);

  @override
  _i5.Future<String> resolveSymbolicLinks() => (super.noSuchMethod(
        Invocation.method(
          #resolveSymbolicLinks,
          [],
        ),
        returnValue: _i5.Future<String>.value(_i6.dummyValue<String>(
          this,
          Invocation.method(
            #resolveSymbolicLinks,
            [],
          ),
        )),
      ) as _i5.Future<String>);

  @override
  String resolveSymbolicLinksSync() => (super.noSuchMethod(
        Invocation.method(
          #resolveSymbolicLinksSync,
          [],
        ),
        returnValue: _i6.dummyValue<String>(
          this,
          Invocation.method(
            #resolveSymbolicLinksSync,
            [],
          ),
        ),
      ) as String);

  @override
  _i5.Future<_i3.Directory> rename(String? newPath) => (super.noSuchMethod(
        Invocation.method(
          #rename,
          [newPath],
        ),
        returnValue: _i5.Future<_i3.Directory>.value(_FakeDirectory_3(
          this,
          Invocation.method(
            #rename,
            [newPath],
          ),
        )),
      ) as _i5.Future<_i3.Directory>);

  @override
  _i3.Directory renameSync(String? newPath) => (super.noSuchMethod(
        Invocation.method(
          #renameSync,
          [newPath],
        ),
        returnValue: _FakeDirectory_3(
          this,
          Invocation.method(
            #renameSync,
            [newPath],
          ),
        ),
      ) as _i3.Directory);

  @override
  _i5.Future<_i3.FileSystemEntity> delete({bool? recursive = false}) =>
      (super.noSuchMethod(
        Invocation.method(
          #delete,
          [],
          {#recursive: recursive},
        ),
        returnValue:
            _i5.Future<_i3.FileSystemEntity>.value(_FakeFileSystemEntity_4(
          this,
          Invocation.method(
            #delete,
            [],
            {#recursive: recursive},
          ),
        )),
      ) as _i5.Future<_i3.FileSystemEntity>);

  @override
  void deleteSync({bool? recursive = false}) => super.noSuchMethod(
        Invocation.method(
          #deleteSync,
          [],
          {#recursive: recursive},
        ),
        returnValueForMissingStub: null,
      );

  @override
  _i5.Stream<_i3.FileSystemEntity> list({
    bool? recursive = false,
    bool? followLinks = true,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #list,
          [],
          {
            #recursive: recursive,
            #followLinks: followLinks,
          },
        ),
        returnValue: _i5.Stream<_i3.FileSystemEntity>.empty(),
      ) as _i5.Stream<_i3.FileSystemEntity>);

  @override
  List<_i3.FileSystemEntity> listSync({
    bool? recursive = false,
    bool? followLinks = true,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #listSync,
          [],
          {
            #recursive: recursive,
            #followLinks: followLinks,
          },
        ),
        returnValue: <_i3.FileSystemEntity>[],
      ) as List<_i3.FileSystemEntity>);

  @override
  _i5.Future<bool> exists() => (super.noSuchMethod(
        Invocation.method(
          #exists,
          [],
        ),
        returnValue: _i5.Future<bool>.value(false),
      ) as _i5.Future<bool>);

  @override
  bool existsSync() => (super.noSuchMethod(
        Invocation.method(
          #existsSync,
          [],
        ),
        returnValue: false,
      ) as bool);

  @override
  _i5.Future<_i3.FileStat> stat() => (super.noSuchMethod(
        Invocation.method(
          #stat,
          [],
        ),
        returnValue: _i5.Future<_i3.FileStat>.value(_FakeFileStat_5(
          this,
          Invocation.method(
            #stat,
            [],
          ),
        )),
      ) as _i5.Future<_i3.FileStat>);

  @override
  _i3.FileStat statSync() => (super.noSuchMethod(
        Invocation.method(
          #statSync,
          [],
        ),
        returnValue: _FakeFileStat_5(
          this,
          Invocation.method(
            #statSync,
            [],
          ),
        ),
      ) as _i3.FileStat);

  @override
  _i5.Stream<_i3.FileSystemEvent> watch({
    int? events = 15,
    bool? recursive = false,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #watch,
          [],
          {
            #events: events,
            #recursive: recursive,
          },
        ),
        returnValue: _i5.Stream<_i3.FileSystemEvent>.empty(),
      ) as _i5.Stream<_i3.FileSystemEvent>);
}

/// A class which mocks [File].
///
/// See the documentation for Mockito's code generation for more information.
class MockFile extends _i1.Mock implements _i3.File {
  MockFile() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i3.File get absolute => (super.noSuchMethod(
        Invocation.getter(#absolute),
        returnValue: _FakeFile_6(
          this,
          Invocation.getter(#absolute),
        ),
      ) as _i3.File);

  @override
  String get path => (super.noSuchMethod(
        Invocation.getter(#path),
        returnValue: _i6.dummyValue<String>(
          this,
          Invocation.getter(#path),
        ),
      ) as String);

  @override
  Uri get uri => (super.noSuchMethod(
        Invocation.getter(#uri),
        returnValue: _FakeUri_2(
          this,
          Invocation.getter(#uri),
        ),
      ) as Uri);

  @override
  bool get isAbsolute => (super.noSuchMethod(
        Invocation.getter(#isAbsolute),
        returnValue: false,
      ) as bool);

  @override
  _i3.Directory get parent => (super.noSuchMethod(
        Invocation.getter(#parent),
        returnValue: _FakeDirectory_3(
          this,
          Invocation.getter(#parent),
        ),
      ) as _i3.Directory);

  @override
  _i5.Future<_i3.File> create({
    bool? recursive = false,
    bool? exclusive = false,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #create,
          [],
          {
            #recursive: recursive,
            #exclusive: exclusive,
          },
        ),
        returnValue: _i5.Future<_i3.File>.value(_FakeFile_6(
          this,
          Invocation.method(
            #create,
            [],
            {
              #recursive: recursive,
              #exclusive: exclusive,
            },
          ),
        )),
      ) as _i5.Future<_i3.File>);

  @override
  void createSync({
    bool? recursive = false,
    bool? exclusive = false,
  }) =>
      super.noSuchMethod(
        Invocation.method(
          #createSync,
          [],
          {
            #recursive: recursive,
            #exclusive: exclusive,
          },
        ),
        returnValueForMissingStub: null,
      );

  @override
  _i5.Future<_i3.File> rename(String? newPath) => (super.noSuchMethod(
        Invocation.method(
          #rename,
          [newPath],
        ),
        returnValue: _i5.Future<_i3.File>.value(_FakeFile_6(
          this,
          Invocation.method(
            #rename,
            [newPath],
          ),
        )),
      ) as _i5.Future<_i3.File>);

  @override
  _i3.File renameSync(String? newPath) => (super.noSuchMethod(
        Invocation.method(
          #renameSync,
          [newPath],
        ),
        returnValue: _FakeFile_6(
          this,
          Invocation.method(
            #renameSync,
            [newPath],
          ),
        ),
      ) as _i3.File);

  @override
  _i5.Future<_i3.FileSystemEntity> delete({bool? recursive = false}) =>
      (super.noSuchMethod(
        Invocation.method(
          #delete,
          [],
          {#recursive: recursive},
        ),
        returnValue:
            _i5.Future<_i3.FileSystemEntity>.value(_FakeFileSystemEntity_4(
          this,
          Invocation.method(
            #delete,
            [],
            {#recursive: recursive},
          ),
        )),
      ) as _i5.Future<_i3.FileSystemEntity>);

  @override
  void deleteSync({bool? recursive = false}) => super.noSuchMethod(
        Invocation.method(
          #deleteSync,
          [],
          {#recursive: recursive},
        ),
        returnValueForMissingStub: null,
      );

  @override
  _i5.Future<_i3.File> copy(String? newPath) => (super.noSuchMethod(
        Invocation.method(
          #copy,
          [newPath],
        ),
        returnValue: _i5.Future<_i3.File>.value(_FakeFile_6(
          this,
          Invocation.method(
            #copy,
            [newPath],
          ),
        )),
      ) as _i5.Future<_i3.File>);

  @override
  _i3.File copySync(String? newPath) => (super.noSuchMethod(
        Invocation.method(
          #copySync,
          [newPath],
        ),
        returnValue: _FakeFile_6(
          this,
          Invocation.method(
            #copySync,
            [newPath],
          ),
        ),
      ) as _i3.File);

  @override
  _i5.Future<int> length() => (super.noSuchMethod(
        Invocation.method(
          #length,
          [],
        ),
        returnValue: _i5.Future<int>.value(0),
      ) as _i5.Future<int>);

  @override
  int lengthSync() => (super.noSuchMethod(
        Invocation.method(
          #lengthSync,
          [],
        ),
        returnValue: 0,
      ) as int);

  @override
  _i5.Future<DateTime> lastAccessed() => (super.noSuchMethod(
        Invocation.method(
          #lastAccessed,
          [],
        ),
        returnValue: _i5.Future<DateTime>.value(_FakeDateTime_7(
          this,
          Invocation.method(
            #lastAccessed,
            [],
          ),
        )),
      ) as _i5.Future<DateTime>);

  @override
  DateTime lastAccessedSync() => (super.noSuchMethod(
        Invocation.method(
          #lastAccessedSync,
          [],
        ),
        returnValue: _FakeDateTime_7(
          this,
          Invocation.method(
            #lastAccessedSync,
            [],
          ),
        ),
      ) as DateTime);

  @override
  _i5.Future<dynamic> setLastAccessed(DateTime? time) => (super.noSuchMethod(
        Invocation.method(
          #setLastAccessed,
          [time],
        ),
        returnValue: _i5.Future<dynamic>.value(),
      ) as _i5.Future<dynamic>);

  @override
  void setLastAccessedSync(DateTime? time) => super.noSuchMethod(
        Invocation.method(
          #setLastAccessedSync,
          [time],
        ),
        returnValueForMissingStub: null,
      );

  @override
  _i5.Future<DateTime> lastModified() => (super.noSuchMethod(
        Invocation.method(
          #lastModified,
          [],
        ),
        returnValue: _i5.Future<DateTime>.value(_FakeDateTime_7(
          this,
          Invocation.method(
            #lastModified,
            [],
          ),
        )),
      ) as _i5.Future<DateTime>);

  @override
  DateTime lastModifiedSync() => (super.noSuchMethod(
        Invocation.method(
          #lastModifiedSync,
          [],
        ),
        returnValue: _FakeDateTime_7(
          this,
          Invocation.method(
            #lastModifiedSync,
            [],
          ),
        ),
      ) as DateTime);

  @override
  _i5.Future<dynamic> setLastModified(DateTime? time) => (super.noSuchMethod(
        Invocation.method(
          #setLastModified,
          [time],
        ),
        returnValue: _i5.Future<dynamic>.value(),
      ) as _i5.Future<dynamic>);

  @override
  void setLastModifiedSync(DateTime? time) => super.noSuchMethod(
        Invocation.method(
          #setLastModifiedSync,
          [time],
        ),
        returnValueForMissingStub: null,
      );

  @override
  _i5.Future<_i3.RandomAccessFile> open(
          {_i3.FileMode? mode = _i3.FileMode.read}) =>
      (super.noSuchMethod(
        Invocation.method(
          #open,
          [],
          {#mode: mode},
        ),
        returnValue:
            _i5.Future<_i3.RandomAccessFile>.value(_FakeRandomAccessFile_8(
          this,
          Invocation.method(
            #open,
            [],
            {#mode: mode},
          ),
        )),
      ) as _i5.Future<_i3.RandomAccessFile>);

  @override
  _i3.RandomAccessFile openSync({_i3.FileMode? mode = _i3.FileMode.read}) =>
      (super.noSuchMethod(
        Invocation.method(
          #openSync,
          [],
          {#mode: mode},
        ),
        returnValue: _FakeRandomAccessFile_8(
          this,
          Invocation.method(
            #openSync,
            [],
            {#mode: mode},
          ),
        ),
      ) as _i3.RandomAccessFile);

  @override
  _i5.Stream<List<int>> openRead([
    int? start,
    int? end,
  ]) =>
      (super.noSuchMethod(
        Invocation.method(
          #openRead,
          [
            start,
            end,
          ],
        ),
        returnValue: _i5.Stream<List<int>>.empty(),
      ) as _i5.Stream<List<int>>);

  @override
  _i3.IOSink openWrite({
    _i3.FileMode? mode = _i3.FileMode.write,
    _i10.Encoding? encoding = const _i10.Utf8Codec(),
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #openWrite,
          [],
          {
            #mode: mode,
            #encoding: encoding,
          },
        ),
        returnValue: _FakeIOSink_9(
          this,
          Invocation.method(
            #openWrite,
            [],
            {
              #mode: mode,
              #encoding: encoding,
            },
          ),
        ),
      ) as _i3.IOSink);

  @override
  _i5.Future<_i11.Uint8List> readAsBytes() => (super.noSuchMethod(
        Invocation.method(
          #readAsBytes,
          [],
        ),
        returnValue: _i5.Future<_i11.Uint8List>.value(_i11.Uint8List(0)),
      ) as _i5.Future<_i11.Uint8List>);

  @override
  _i11.Uint8List readAsBytesSync() => (super.noSuchMethod(
        Invocation.method(
          #readAsBytesSync,
          [],
        ),
        returnValue: _i11.Uint8List(0),
      ) as _i11.Uint8List);

  @override
  _i5.Future<String> readAsString(
          {_i10.Encoding? encoding = const _i10.Utf8Codec()}) =>
      (super.noSuchMethod(
        Invocation.method(
          #readAsString,
          [],
          {#encoding: encoding},
        ),
        returnValue: _i5.Future<String>.value(_i6.dummyValue<String>(
          this,
          Invocation.method(
            #readAsString,
            [],
            {#encoding: encoding},
          ),
        )),
      ) as _i5.Future<String>);

  @override
  String readAsStringSync({_i10.Encoding? encoding = const _i10.Utf8Codec()}) =>
      (super.noSuchMethod(
        Invocation.method(
          #readAsStringSync,
          [],
          {#encoding: encoding},
        ),
        returnValue: _i6.dummyValue<String>(
          this,
          Invocation.method(
            #readAsStringSync,
            [],
            {#encoding: encoding},
          ),
        ),
      ) as String);

  @override
  _i5.Future<List<String>> readAsLines(
          {_i10.Encoding? encoding = const _i10.Utf8Codec()}) =>
      (super.noSuchMethod(
        Invocation.method(
          #readAsLines,
          [],
          {#encoding: encoding},
        ),
        returnValue: _i5.Future<List<String>>.value(<String>[]),
      ) as _i5.Future<List<String>>);

  @override
  List<String> readAsLinesSync(
          {_i10.Encoding? encoding = const _i10.Utf8Codec()}) =>
      (super.noSuchMethod(
        Invocation.method(
          #readAsLinesSync,
          [],
          {#encoding: encoding},
        ),
        returnValue: <String>[],
      ) as List<String>);

  @override
  _i5.Future<_i3.File> writeAsBytes(
    List<int>? bytes, {
    _i3.FileMode? mode = _i3.FileMode.write,
    bool? flush = false,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #writeAsBytes,
          [bytes],
          {
            #mode: mode,
            #flush: flush,
          },
        ),
        returnValue: _i5.Future<_i3.File>.value(_FakeFile_6(
          this,
          Invocation.method(
            #writeAsBytes,
            [bytes],
            {
              #mode: mode,
              #flush: flush,
            },
          ),
        )),
      ) as _i5.Future<_i3.File>);

  @override
  void writeAsBytesSync(
    List<int>? bytes, {
    _i3.FileMode? mode = _i3.FileMode.write,
    bool? flush = false,
  }) =>
      super.noSuchMethod(
        Invocation.method(
          #writeAsBytesSync,
          [bytes],
          {
            #mode: mode,
            #flush: flush,
          },
        ),
        returnValueForMissingStub: null,
      );

  @override
  _i5.Future<_i3.File> writeAsString(
    String? contents, {
    _i3.FileMode? mode = _i3.FileMode.write,
    _i10.Encoding? encoding = const _i10.Utf8Codec(),
    bool? flush = false,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #writeAsString,
          [contents],
          {
            #mode: mode,
            #encoding: encoding,
            #flush: flush,
          },
        ),
        returnValue: _i5.Future<_i3.File>.value(_FakeFile_6(
          this,
          Invocation.method(
            #writeAsString,
            [contents],
            {
              #mode: mode,
              #encoding: encoding,
              #flush: flush,
            },
          ),
        )),
      ) as _i5.Future<_i3.File>);

  @override
  void writeAsStringSync(
    String? contents, {
    _i3.FileMode? mode = _i3.FileMode.write,
    _i10.Encoding? encoding = const _i10.Utf8Codec(),
    bool? flush = false,
  }) =>
      super.noSuchMethod(
        Invocation.method(
          #writeAsStringSync,
          [contents],
          {
            #mode: mode,
            #encoding: encoding,
            #flush: flush,
          },
        ),
        returnValueForMissingStub: null,
      );

  @override
  _i5.Future<bool> exists() => (super.noSuchMethod(
        Invocation.method(
          #exists,
          [],
        ),
        returnValue: _i5.Future<bool>.value(false),
      ) as _i5.Future<bool>);

  @override
  bool existsSync() => (super.noSuchMethod(
        Invocation.method(
          #existsSync,
          [],
        ),
        returnValue: false,
      ) as bool);

  @override
  _i5.Future<String> resolveSymbolicLinks() => (super.noSuchMethod(
        Invocation.method(
          #resolveSymbolicLinks,
          [],
        ),
        returnValue: _i5.Future<String>.value(_i6.dummyValue<String>(
          this,
          Invocation.method(
            #resolveSymbolicLinks,
            [],
          ),
        )),
      ) as _i5.Future<String>);

  @override
  String resolveSymbolicLinksSync() => (super.noSuchMethod(
        Invocation.method(
          #resolveSymbolicLinksSync,
          [],
        ),
        returnValue: _i6.dummyValue<String>(
          this,
          Invocation.method(
            #resolveSymbolicLinksSync,
            [],
          ),
        ),
      ) as String);

  @override
  _i5.Future<_i3.FileStat> stat() => (super.noSuchMethod(
        Invocation.method(
          #stat,
          [],
        ),
        returnValue: _i5.Future<_i3.FileStat>.value(_FakeFileStat_5(
          this,
          Invocation.method(
            #stat,
            [],
          ),
        )),
      ) as _i5.Future<_i3.FileStat>);

  @override
  _i3.FileStat statSync() => (super.noSuchMethod(
        Invocation.method(
          #statSync,
          [],
        ),
        returnValue: _FakeFileStat_5(
          this,
          Invocation.method(
            #statSync,
            [],
          ),
        ),
      ) as _i3.FileStat);

  @override
  _i5.Stream<_i3.FileSystemEvent> watch({
    int? events = 15,
    bool? recursive = false,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #watch,
          [],
          {
            #events: events,
            #recursive: recursive,
          },
        ),
        returnValue: _i5.Stream<_i3.FileSystemEvent>.empty(),
      ) as _i5.Stream<_i3.FileSystemEvent>);
}

/// A class which mocks [FileStat].
///
/// See the documentation for Mockito's code generation for more information.
class MockFileStat extends _i1.Mock implements _i3.FileStat {
  MockFileStat() {
    _i1.throwOnMissingStub(this);
  }

  @override
  DateTime get changed => (super.noSuchMethod(
        Invocation.getter(#changed),
        returnValue: _FakeDateTime_7(
          this,
          Invocation.getter(#changed),
        ),
      ) as DateTime);

  @override
  DateTime get modified => (super.noSuchMethod(
        Invocation.getter(#modified),
        returnValue: _FakeDateTime_7(
          this,
          Invocation.getter(#modified),
        ),
      ) as DateTime);

  @override
  DateTime get accessed => (super.noSuchMethod(
        Invocation.getter(#accessed),
        returnValue: _FakeDateTime_7(
          this,
          Invocation.getter(#accessed),
        ),
      ) as DateTime);

  @override
  _i3.FileSystemEntityType get type => (super.noSuchMethod(
        Invocation.getter(#type),
        returnValue: _i6.dummyValue<_i3.FileSystemEntityType>(
          this,
          Invocation.getter(#type),
        ),
      ) as _i3.FileSystemEntityType);

  @override
  int get mode => (super.noSuchMethod(
        Invocation.getter(#mode),
        returnValue: 0,
      ) as int);

  @override
  int get size => (super.noSuchMethod(
        Invocation.getter(#size),
        returnValue: 0,
      ) as int);

  @override
  String modeString() => (super.noSuchMethod(
        Invocation.method(
          #modeString,
          [],
        ),
        returnValue: _i6.dummyValue<String>(
          this,
          Invocation.method(
            #modeString,
            [],
          ),
        ),
      ) as String);
}
