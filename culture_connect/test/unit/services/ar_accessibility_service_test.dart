import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter/services.dart';
import 'package:culture_connect/services/ar_accessibility_service.dart';

// Generate mocks for dependencies
@GenerateMocks([SharedPreferences])
import 'ar_accessibility_service_test.mocks.dart';

void main() {
  late ARAccessibilityService service;
  late MockSharedPreferences mockPreferences;

  setUp(() {
    mockPreferences = MockSharedPreferences();

    // Setup mock preferences
    when(mockPreferences.getBool('high_contrast_mode')).thenReturn(false);
    when(mockPreferences.getBool('screen_reader_enabled')).thenReturn(false);
    when(mockPreferences.getBool('reduced_motion')).thenReturn(false);
    when(mockPreferences.getBool('simplified_gestures')).thenReturn(false);
    when(mockPreferences.getBool('audio_guidance')).thenReturn(false);
    when(mockPreferences.setString(any, any)).thenAnswer((_) async => true);
    when(mockPreferences.setBool(any, any)).thenAnswer((_) async => true);

    // Create service instance
    service = ARAccessibilityService(preferences: mockPreferences);
  });

  group('ARAccessibilityService Tests', () {
    test('initialize should load settings from preferences', () async {
      // Arrange
      when(mockPreferences.getBool('high_contrast_mode')).thenReturn(true);
      when(mockPreferences.getBool('screen_reader_enabled')).thenReturn(true);

      // Act
      await service.initialize();

      // Assert
      expect(service.isHighContrastModeEnabled, true);
      expect(service.isScreenReaderEnabled, true);
      verify(mockPreferences.getBool('high_contrast_mode')).called(1);
      verify(mockPreferences.getBool('screen_reader_enabled')).called(1);
    });

    test('toggleHighContrastMode should toggle high contrast mode', () async {
      // Arrange
      await service.initialize();
      final initialValue = service.isHighContrastModeEnabled;

      // Act
      await service.toggleHighContrastMode();

      // Assert
      expect(service.isHighContrastModeEnabled, !initialValue);
      verify(mockPreferences.setBool('high_contrast_mode', !initialValue))
          .called(1);
    });

    test('toggleScreenReader should toggle screen reader', () async {
      // Arrange
      await service.initialize();
      final initialValue = service.isScreenReaderEnabled;

      // Act
      service.toggleScreenReader();

      // Assert
      expect(service.isScreenReaderEnabled, !initialValue);
      verify(mockPreferences.setBool('screen_reader_enabled', !initialValue))
          .called(1);
    });

    test('toggleReducedMotion should toggle reduced motion', () async {
      // Arrange
      await service.initialize();
      final initialValue = service.isReducedMotionEnabled;

      // Act
      service.toggleReducedMotion();

      // Assert
      expect(service.isReducedMotionEnabled, !initialValue);
      verify(mockPreferences.setBool('reduced_motion', !initialValue))
          .called(1);
    });

    test('toggleSimplifiedGestures should toggle simplified gestures',
        () async {
      // Arrange
      await service.initialize();
      final initialValue = service.isSimplifiedGesturesEnabled;

      // Act
      await service.toggleSimplifiedGestures();

      // Assert
      expect(service.isSimplifiedGesturesEnabled, !initialValue);
      verify(mockPreferences.setBool('simplified_gestures', !initialValue))
          .called(1);
    });

    test('toggleAudioGuidance should toggle audio guidance', () async {
      // Arrange
      await service.initialize();
      final initialValue = service.isAudioGuidanceEnabled;

      // Act
      service.toggleAudioGuidance();

      // Assert
      expect(service.isAudioGuidanceEnabled, !initialValue);
      verify(mockPreferences.setBool('audio_guidance', !initialValue))
          .called(1);
    });

    test('applyHighContrastMode should apply high contrast settings', () async {
      // Arrange
      await service.initialize();
      await service.toggleHighContrastMode(); // Enable high contrast mode

      // Act
      final result = service.applyHighContrastMode(
        originalColor: const Color(0xFF123456),
      );

      // Assert
      expect(result, isA<Color>());
      // High contrast mode should increase contrast
      expect(
          result.computeLuminance() !=
              const Color(0xFF123456).computeLuminance(),
          true);
    });

    test('getAccessibleFontSize should return larger font size when enabled',
        () async {
      // Arrange
      await service.initialize();
      final defaultSize = service.getAccessibleFontSize(16.0);

      // Enable screen reader (which should increase font size)
      service.toggleScreenReader();

      // Act
      final accessibleSize = service.getAccessibleFontSize(16.0);

      // Assert
      expect(accessibleSize > defaultSize, true);
    });

    test(
        'getAccessibleAnimationDuration should return longer duration when reduced motion is enabled',
        () async {
      // Arrange
      await service.initialize();
      final defaultDuration = service.getAccessibleAnimationDuration(
        const Duration(milliseconds: 300),
      );

      // Enable reduced motion
      service.toggleReducedMotion();

      // Act
      final accessibleDuration = service.getAccessibleAnimationDuration(
        const Duration(milliseconds: 300),
      );

      // Assert
      expect(accessibleDuration.inMilliseconds > defaultDuration.inMilliseconds,
          true);
    });

    test(
        'getAccessibleTapTarget should return larger tap target when simplified gestures is enabled',
        () async {
      // Arrange
      await service.initialize();
      final defaultSize = service.getAccessibleTapTarget(24.0);

      // Enable simplified gestures
      await service.toggleSimplifiedGestures();

      // Act
      final accessibleSize = service.getAccessibleTapTarget(24.0);

      // Assert
      expect(accessibleSize > defaultSize, true);
    });

    test('resetToDefaults should reset all settings to defaults', () async {
      // Arrange
      await service.initialize();
      await service.toggleHighContrastMode();
      service.toggleScreenReader();

      // Act
      await service.resetToDefaults();

      // Assert
      expect(service.isHighContrastModeEnabled, false);
      expect(service.isScreenReaderEnabled, false);
      expect(service.isReducedMotionEnabled, false);
      expect(service.isSimplifiedGesturesEnabled, false);
      expect(service.isAudioGuidanceEnabled, false);
    });
  });
}
