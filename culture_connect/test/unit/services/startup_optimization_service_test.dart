import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:flutter/services.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:path_provider/path_provider.dart';
import 'package:culture_connect/services/startup_optimization_service.dart';

// Generate mocks for dependencies
@GenerateMocks([SharedPreferences])
import 'startup_optimization_service_test.mocks.dart';

void main() {
  TestWidgetsFlutterBinding.ensureInitialized();

  late StartupOptimizationService service;
  late MockSharedPreferences mockPreferences;

  // Setup method to initialize mocks and service before each test
  setUp(() {
    mockPreferences = MockSharedPreferences();
    
    // Mock path_provider
    const MethodChannel('plugins.flutter.io/path_provider')
      .setMockMethodCallHandler((MethodCall methodCall) async {
        if (methodCall.method == 'getApplicationDocumentsDirectory') {
          return '/mock/docs/path';
        } else if (methodCall.method == 'getTemporaryDirectory') {
          return '/mock/temp/path';
        }
        return null;
      });
    
    // Mock asset loading
    TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
      .setMockMessageHandler('flutter/assets', (ByteData? message) async {
        return ByteData(0);
      });
    
    // Create service instance
    service = StartupOptimizationService();
  });

  // Teardown method to clean up after each test
  tearDown(() {
    // Reset mocks
    reset(mockPreferences);
    
    // Reset method channels
    const MethodChannel('plugins.flutter.io/path_provider')
      .setMockMethodCallHandler(null);
    TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
      .setMockMessageHandler('flutter/assets', null);
  });

  group('StartupOptimizationService Tests', () {
    test('initializeApp should complete successfully', () async {
      // Act
      final result = await service.initializeApp();
      
      // Assert
      expect(result, true);
      expect(service.isInitialized, true);
    });
    
    test('initialized future should complete when initialization is done', () async {
      // Arrange
      final future = service.initialized;
      
      // Act
      await service.initializeApp();
      final result = await future;
      
      // Assert
      expect(result, true);
    });
    
    test('initializeApp should handle errors gracefully', () async {
      // Arrange
      // Mock path_provider to throw an error
      const MethodChannel('plugins.flutter.io/path_provider')
        .setMockMethodCallHandler((MethodCall methodCall) async {
          throw Exception('Mock error');
        });
      
      // Act & Assert
      try {
        final result = await service.initializeApp();
        // Should still return true even with errors
        expect(result, true);
      } catch (e) {
        fail('initializeApp should handle errors gracefully: $e');
      }
    });
    
    test('checkConnectivity should return a boolean value', () async {
      // Act
      final result = await service.checkConnectivity();
      
      // Assert
      expect(result, isA<bool>());
    });
    
    test('unawaited should not block execution', () async {
      // Arrange
      bool completed = false;
      
      // Act
      unawaited(Future.delayed(const Duration(milliseconds: 100), () {
        completed = true;
      }));
      
      // Assert
      expect(completed, false); // Should not be completed immediately
      
      // Wait for the delayed future to complete
      await Future.delayed(const Duration(milliseconds: 200));
      expect(completed, true); // Should be completed after waiting
    });
  });
}
