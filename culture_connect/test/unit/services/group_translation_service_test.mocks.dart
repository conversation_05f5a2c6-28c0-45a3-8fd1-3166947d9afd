// Mocks generated by <PERSON><PERSON><PERSON> 5.4.5 from annotations
// in culture_connect/test/unit/services/group_translation_service_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i7;
import 'dart:convert' as _i8;
import 'dart:typed_data' as _i10;

import 'package:culture_connect/models/message_model.dart' as _i13;
import 'package:culture_connect/models/translation/language_model.dart' as _i18;
import 'package:culture_connect/models/translation/message_translation_metadata.dart'
    as _i3;
import 'package:culture_connect/models/translation/translation_cultural_context.dart'
    as _i4;
import 'package:culture_connect/models/translation/translation_pronunciation.dart'
    as _i6;
import 'package:culture_connect/models/translation/translation_slang_idiom.dart'
    as _i5;
import 'package:culture_connect/services/language_detection_service.dart'
    as _i17;
import 'package:culture_connect/services/translation_metrics_service.dart'
    as _i19;
import 'package:culture_connect/services/voice_translation/cultural_context_service.dart'
    as _i14;
import 'package:culture_connect/services/voice_translation/message_translation_service.dart'
    as _i12;
import 'package:culture_connect/services/voice_translation/pronunciation_service.dart'
    as _i16;
import 'package:culture_connect/services/voice_translation/slang_idiom_service.dart'
    as _i15;
import 'package:http/http.dart' as _i2;
import 'package:mockito/mockito.dart' as _i1;
import 'package:mockito/src/dummies.dart' as _i9;
import 'package:shared_preferences/shared_preferences.dart' as _i11;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeResponse_0 extends _i1.SmartFake implements _i2.Response {
  _FakeResponse_0(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeStreamedResponse_1 extends _i1.SmartFake
    implements _i2.StreamedResponse {
  _FakeStreamedResponse_1(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeMessageTranslationMetadata_2 extends _i1.SmartFake
    implements _i3.MessageTranslationMetadata {
  _FakeMessageTranslationMetadata_2(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeTranslationCulturalContext_3 extends _i1.SmartFake
    implements _i4.TranslationCulturalContext {
  _FakeTranslationCulturalContext_3(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeTranslationSlangIdiom_4 extends _i1.SmartFake
    implements _i5.TranslationSlangIdiom {
  _FakeTranslationSlangIdiom_4(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeTranslationPronunciation_5 extends _i1.SmartFake
    implements _i6.TranslationPronunciation {
  _FakeTranslationPronunciation_5(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

/// A class which mocks [Client].
///
/// See the documentation for Mockito's code generation for more information.
class MockClient extends _i1.Mock implements _i2.Client {
  MockClient() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i7.Future<_i2.Response> head(
    Uri? url, {
    Map<String, String>? headers,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #head,
          [url],
          {#headers: headers},
        ),
        returnValue: _i7.Future<_i2.Response>.value(_FakeResponse_0(
          this,
          Invocation.method(
            #head,
            [url],
            {#headers: headers},
          ),
        )),
      ) as _i7.Future<_i2.Response>);

  @override
  _i7.Future<_i2.Response> get(
    Uri? url, {
    Map<String, String>? headers,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #get,
          [url],
          {#headers: headers},
        ),
        returnValue: _i7.Future<_i2.Response>.value(_FakeResponse_0(
          this,
          Invocation.method(
            #get,
            [url],
            {#headers: headers},
          ),
        )),
      ) as _i7.Future<_i2.Response>);

  @override
  _i7.Future<_i2.Response> post(
    Uri? url, {
    Map<String, String>? headers,
    Object? body,
    _i8.Encoding? encoding,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #post,
          [url],
          {
            #headers: headers,
            #body: body,
            #encoding: encoding,
          },
        ),
        returnValue: _i7.Future<_i2.Response>.value(_FakeResponse_0(
          this,
          Invocation.method(
            #post,
            [url],
            {
              #headers: headers,
              #body: body,
              #encoding: encoding,
            },
          ),
        )),
      ) as _i7.Future<_i2.Response>);

  @override
  _i7.Future<_i2.Response> put(
    Uri? url, {
    Map<String, String>? headers,
    Object? body,
    _i8.Encoding? encoding,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #put,
          [url],
          {
            #headers: headers,
            #body: body,
            #encoding: encoding,
          },
        ),
        returnValue: _i7.Future<_i2.Response>.value(_FakeResponse_0(
          this,
          Invocation.method(
            #put,
            [url],
            {
              #headers: headers,
              #body: body,
              #encoding: encoding,
            },
          ),
        )),
      ) as _i7.Future<_i2.Response>);

  @override
  _i7.Future<_i2.Response> patch(
    Uri? url, {
    Map<String, String>? headers,
    Object? body,
    _i8.Encoding? encoding,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #patch,
          [url],
          {
            #headers: headers,
            #body: body,
            #encoding: encoding,
          },
        ),
        returnValue: _i7.Future<_i2.Response>.value(_FakeResponse_0(
          this,
          Invocation.method(
            #patch,
            [url],
            {
              #headers: headers,
              #body: body,
              #encoding: encoding,
            },
          ),
        )),
      ) as _i7.Future<_i2.Response>);

  @override
  _i7.Future<_i2.Response> delete(
    Uri? url, {
    Map<String, String>? headers,
    Object? body,
    _i8.Encoding? encoding,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #delete,
          [url],
          {
            #headers: headers,
            #body: body,
            #encoding: encoding,
          },
        ),
        returnValue: _i7.Future<_i2.Response>.value(_FakeResponse_0(
          this,
          Invocation.method(
            #delete,
            [url],
            {
              #headers: headers,
              #body: body,
              #encoding: encoding,
            },
          ),
        )),
      ) as _i7.Future<_i2.Response>);

  @override
  _i7.Future<String> read(
    Uri? url, {
    Map<String, String>? headers,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #read,
          [url],
          {#headers: headers},
        ),
        returnValue: _i7.Future<String>.value(_i9.dummyValue<String>(
          this,
          Invocation.method(
            #read,
            [url],
            {#headers: headers},
          ),
        )),
      ) as _i7.Future<String>);

  @override
  _i7.Future<_i10.Uint8List> readBytes(
    Uri? url, {
    Map<String, String>? headers,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #readBytes,
          [url],
          {#headers: headers},
        ),
        returnValue: _i7.Future<_i10.Uint8List>.value(_i10.Uint8List(0)),
      ) as _i7.Future<_i10.Uint8List>);

  @override
  _i7.Future<_i2.StreamedResponse> send(_i2.BaseRequest? request) =>
      (super.noSuchMethod(
        Invocation.method(
          #send,
          [request],
        ),
        returnValue:
            _i7.Future<_i2.StreamedResponse>.value(_FakeStreamedResponse_1(
          this,
          Invocation.method(
            #send,
            [request],
          ),
        )),
      ) as _i7.Future<_i2.StreamedResponse>);

  @override
  void close() => super.noSuchMethod(
        Invocation.method(
          #close,
          [],
        ),
        returnValueForMissingStub: null,
      );
}

/// A class which mocks [SharedPreferences].
///
/// See the documentation for Mockito's code generation for more information.
class MockSharedPreferences extends _i1.Mock implements _i11.SharedPreferences {
  MockSharedPreferences() {
    _i1.throwOnMissingStub(this);
  }

  @override
  Set<String> getKeys() => (super.noSuchMethod(
        Invocation.method(
          #getKeys,
          [],
        ),
        returnValue: <String>{},
      ) as Set<String>);

  @override
  Object? get(String? key) => (super.noSuchMethod(Invocation.method(
        #get,
        [key],
      )) as Object?);

  @override
  bool? getBool(String? key) => (super.noSuchMethod(Invocation.method(
        #getBool,
        [key],
      )) as bool?);

  @override
  int? getInt(String? key) => (super.noSuchMethod(Invocation.method(
        #getInt,
        [key],
      )) as int?);

  @override
  double? getDouble(String? key) => (super.noSuchMethod(Invocation.method(
        #getDouble,
        [key],
      )) as double?);

  @override
  String? getString(String? key) => (super.noSuchMethod(Invocation.method(
        #getString,
        [key],
      )) as String?);

  @override
  bool containsKey(String? key) => (super.noSuchMethod(
        Invocation.method(
          #containsKey,
          [key],
        ),
        returnValue: false,
      ) as bool);

  @override
  List<String>? getStringList(String? key) =>
      (super.noSuchMethod(Invocation.method(
        #getStringList,
        [key],
      )) as List<String>?);

  @override
  _i7.Future<bool> setBool(
    String? key,
    bool? value,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #setBool,
          [
            key,
            value,
          ],
        ),
        returnValue: _i7.Future<bool>.value(false),
      ) as _i7.Future<bool>);

  @override
  _i7.Future<bool> setInt(
    String? key,
    int? value,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #setInt,
          [
            key,
            value,
          ],
        ),
        returnValue: _i7.Future<bool>.value(false),
      ) as _i7.Future<bool>);

  @override
  _i7.Future<bool> setDouble(
    String? key,
    double? value,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #setDouble,
          [
            key,
            value,
          ],
        ),
        returnValue: _i7.Future<bool>.value(false),
      ) as _i7.Future<bool>);

  @override
  _i7.Future<bool> setString(
    String? key,
    String? value,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #setString,
          [
            key,
            value,
          ],
        ),
        returnValue: _i7.Future<bool>.value(false),
      ) as _i7.Future<bool>);

  @override
  _i7.Future<bool> setStringList(
    String? key,
    List<String>? value,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #setStringList,
          [
            key,
            value,
          ],
        ),
        returnValue: _i7.Future<bool>.value(false),
      ) as _i7.Future<bool>);

  @override
  _i7.Future<bool> remove(String? key) => (super.noSuchMethod(
        Invocation.method(
          #remove,
          [key],
        ),
        returnValue: _i7.Future<bool>.value(false),
      ) as _i7.Future<bool>);

  @override
  _i7.Future<bool> commit() => (super.noSuchMethod(
        Invocation.method(
          #commit,
          [],
        ),
        returnValue: _i7.Future<bool>.value(false),
      ) as _i7.Future<bool>);

  @override
  _i7.Future<bool> clear() => (super.noSuchMethod(
        Invocation.method(
          #clear,
          [],
        ),
        returnValue: _i7.Future<bool>.value(false),
      ) as _i7.Future<bool>);

  @override
  _i7.Future<void> reload() => (super.noSuchMethod(
        Invocation.method(
          #reload,
          [],
        ),
        returnValue: _i7.Future<void>.value(),
        returnValueForMissingStub: _i7.Future<void>.value(),
      ) as _i7.Future<void>);
}

/// A class which mocks [MessageTranslationService].
///
/// See the documentation for Mockito's code generation for more information.
class MockMessageTranslationService extends _i1.Mock
    implements _i12.MessageTranslationService {
  MockMessageTranslationService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i7.Stream<String> get translationEvents => (super.noSuchMethod(
        Invocation.getter(#translationEvents),
        returnValue: _i7.Stream<String>.empty(),
      ) as _i7.Stream<String>);

  @override
  void setUseOfflineMode(bool? value) => super.noSuchMethod(
        Invocation.method(
          #setUseOfflineMode,
          [value],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void setUseDialectRecognition(bool? value) => super.noSuchMethod(
        Invocation.method(
          #setUseDialectRecognition,
          [value],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void setUseCustomVocabulary(bool? value) => super.noSuchMethod(
        Invocation.method(
          #setUseCustomVocabulary,
          [value],
        ),
        returnValueForMissingStub: null,
      );

  @override
  _i7.Future<_i3.MessageTranslationMetadata> translateMessage(
    _i13.MessageModel? message,
    String? targetLanguage,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #translateMessage,
          [
            message,
            targetLanguage,
          ],
        ),
        returnValue: _i7.Future<_i3.MessageTranslationMetadata>.value(
            _FakeMessageTranslationMetadata_2(
          this,
          Invocation.method(
            #translateMessage,
            [
              message,
              targetLanguage,
            ],
          ),
        )),
      ) as _i7.Future<_i3.MessageTranslationMetadata>);

  @override
  _i7.Future<void> playTranslatedAudio(String? audioPath) =>
      (super.noSuchMethod(
        Invocation.method(
          #playTranslatedAudio,
          [audioPath],
        ),
        returnValue: _i7.Future<void>.value(),
        returnValueForMissingStub: _i7.Future<void>.value(),
      ) as _i7.Future<void>);

  @override
  _i7.Future<void> stopAudio() => (super.noSuchMethod(
        Invocation.method(
          #stopAudio,
          [],
        ),
        returnValue: _i7.Future<void>.value(),
        returnValueForMissingStub: _i7.Future<void>.value(),
      ) as _i7.Future<void>);

  @override
  _i7.Future<_i3.MessageTranslationMetadata> rateTranslation(
    String? messageId,
    String? targetLanguage,
    _i3.TranslationQuality? quality,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #rateTranslation,
          [
            messageId,
            targetLanguage,
            quality,
          ],
        ),
        returnValue: _i7.Future<_i3.MessageTranslationMetadata>.value(
            _FakeMessageTranslationMetadata_2(
          this,
          Invocation.method(
            #rateTranslation,
            [
              messageId,
              targetLanguage,
              quality,
            ],
          ),
        )),
      ) as _i7.Future<_i3.MessageTranslationMetadata>);

  @override
  void clearCache() => super.noSuchMethod(
        Invocation.method(
          #clearCache,
          [],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void dispose() => super.noSuchMethod(
        Invocation.method(
          #dispose,
          [],
        ),
        returnValueForMissingStub: null,
      );
}

/// A class which mocks [CulturalContextService].
///
/// See the documentation for Mockito's code generation for more information.
class MockCulturalContextService extends _i1.Mock
    implements _i14.CulturalContextService {
  MockCulturalContextService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i7.Stream<String> get contextEvents => (super.noSuchMethod(
        Invocation.getter(#contextEvents),
        returnValue: _i7.Stream<String>.empty(),
      ) as _i7.Stream<String>);

  @override
  _i7.Stream<String> get errorEvents => (super.noSuchMethod(
        Invocation.getter(#errorEvents),
        returnValue: _i7.Stream<String>.empty(),
      ) as _i7.Stream<String>);

  @override
  bool get useCulturalContext => (super.noSuchMethod(
        Invocation.getter(#useCulturalContext),
        returnValue: false,
      ) as bool);

  @override
  bool get showSensitiveContent => (super.noSuchMethod(
        Invocation.getter(#showSensitiveContent),
        returnValue: false,
      ) as bool);

  @override
  bool get useOfflineMode => (super.noSuchMethod(
        Invocation.getter(#useOfflineMode),
        returnValue: false,
      ) as bool);

  @override
  bool get isOnline => (super.noSuchMethod(
        Invocation.getter(#isOnline),
        returnValue: false,
      ) as bool);

  @override
  _i7.Future<void> setUseCulturalContext(bool? value) => (super.noSuchMethod(
        Invocation.method(
          #setUseCulturalContext,
          [value],
        ),
        returnValue: _i7.Future<void>.value(),
        returnValueForMissingStub: _i7.Future<void>.value(),
      ) as _i7.Future<void>);

  @override
  _i7.Future<void> setShowSensitiveContent(bool? value) => (super.noSuchMethod(
        Invocation.method(
          #setShowSensitiveContent,
          [value],
        ),
        returnValue: _i7.Future<void>.value(),
        returnValueForMissingStub: _i7.Future<void>.value(),
      ) as _i7.Future<void>);

  @override
  _i7.Future<void> setUseOfflineMode(bool? value) => (super.noSuchMethod(
        Invocation.method(
          #setUseOfflineMode,
          [value],
        ),
        returnValue: _i7.Future<void>.value(),
        returnValueForMissingStub: _i7.Future<void>.value(),
      ) as _i7.Future<void>);

  @override
  _i7.Future<_i4.TranslationCulturalContext> getCulturalContext({
    required String? text,
    required String? sourceLanguage,
    required String? targetLanguage,
    String? sourceRegion,
    String? targetRegion,
    bool? forceRefresh = false,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #getCulturalContext,
          [],
          {
            #text: text,
            #sourceLanguage: sourceLanguage,
            #targetLanguage: targetLanguage,
            #sourceRegion: sourceRegion,
            #targetRegion: targetRegion,
            #forceRefresh: forceRefresh,
          },
        ),
        returnValue: _i7.Future<_i4.TranslationCulturalContext>.value(
            _FakeTranslationCulturalContext_3(
          this,
          Invocation.method(
            #getCulturalContext,
            [],
            {
              #text: text,
              #sourceLanguage: sourceLanguage,
              #targetLanguage: targetLanguage,
              #sourceRegion: sourceRegion,
              #targetRegion: targetRegion,
              #forceRefresh: forceRefresh,
            },
          ),
        )),
      ) as _i7.Future<_i4.TranslationCulturalContext>);

  @override
  void clearCache() => super.noSuchMethod(
        Invocation.method(
          #clearCache,
          [],
        ),
        returnValueForMissingStub: null,
      );

  @override
  _i7.Future<void> clearAllCaches() => (super.noSuchMethod(
        Invocation.method(
          #clearAllCaches,
          [],
        ),
        returnValue: _i7.Future<void>.value(),
        returnValueForMissingStub: _i7.Future<void>.value(),
      ) as _i7.Future<void>);

  @override
  int getMemoryCacheSize() => (super.noSuchMethod(
        Invocation.method(
          #getMemoryCacheSize,
          [],
        ),
        returnValue: 0,
      ) as int);

  @override
  int getOfflineCacheSize() => (super.noSuchMethod(
        Invocation.method(
          #getOfflineCacheSize,
          [],
        ),
        returnValue: 0,
      ) as int);

  @override
  void dispose() => super.noSuchMethod(
        Invocation.method(
          #dispose,
          [],
        ),
        returnValueForMissingStub: null,
      );
}

/// A class which mocks [SlangIdiomService].
///
/// See the documentation for Mockito's code generation for more information.
class MockSlangIdiomService extends _i1.Mock implements _i15.SlangIdiomService {
  MockSlangIdiomService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i7.Stream<String> get slangIdiomEvents => (super.noSuchMethod(
        Invocation.getter(#slangIdiomEvents),
        returnValue: _i7.Stream<String>.empty(),
      ) as _i7.Stream<String>);

  @override
  _i7.Stream<String> get errorStream => (super.noSuchMethod(
        Invocation.getter(#errorStream),
        returnValue: _i7.Stream<String>.empty(),
      ) as _i7.Stream<String>);

  @override
  bool get useSlangIdiomDetection => (super.noSuchMethod(
        Invocation.getter(#useSlangIdiomDetection),
        returnValue: false,
      ) as bool);

  @override
  bool get showPotentiallyOffensiveContent => (super.noSuchMethod(
        Invocation.getter(#showPotentiallyOffensiveContent),
        returnValue: false,
      ) as bool);

  @override
  bool get useOfflineMode => (super.noSuchMethod(
        Invocation.getter(#useOfflineMode),
        returnValue: false,
      ) as bool);

  @override
  bool get isOnline => (super.noSuchMethod(
        Invocation.getter(#isOnline),
        returnValue: false,
      ) as bool);

  @override
  _i7.Future<bool> setUseSlangIdiomDetection(bool? value) =>
      (super.noSuchMethod(
        Invocation.method(
          #setUseSlangIdiomDetection,
          [value],
        ),
        returnValue: _i7.Future<bool>.value(false),
      ) as _i7.Future<bool>);

  @override
  _i7.Future<bool> setShowPotentiallyOffensiveContent(bool? value) =>
      (super.noSuchMethod(
        Invocation.method(
          #setShowPotentiallyOffensiveContent,
          [value],
        ),
        returnValue: _i7.Future<bool>.value(false),
      ) as _i7.Future<bool>);

  @override
  _i7.Future<bool> setUseOfflineMode(bool? value) => (super.noSuchMethod(
        Invocation.method(
          #setUseOfflineMode,
          [value],
        ),
        returnValue: _i7.Future<bool>.value(false),
      ) as _i7.Future<bool>);

  @override
  _i7.Future<_i5.TranslationSlangIdiom> getSlangIdiom({
    required String? text,
    required String? sourceLanguage,
    required String? targetLanguage,
    String? sourceRegion,
    String? targetRegion,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #getSlangIdiom,
          [],
          {
            #text: text,
            #sourceLanguage: sourceLanguage,
            #targetLanguage: targetLanguage,
            #sourceRegion: sourceRegion,
            #targetRegion: targetRegion,
          },
        ),
        returnValue: _i7.Future<_i5.TranslationSlangIdiom>.value(
            _FakeTranslationSlangIdiom_4(
          this,
          Invocation.method(
            #getSlangIdiom,
            [],
            {
              #text: text,
              #sourceLanguage: sourceLanguage,
              #targetLanguage: targetLanguage,
              #sourceRegion: sourceRegion,
              #targetRegion: targetRegion,
            },
          ),
        )),
      ) as _i7.Future<_i5.TranslationSlangIdiom>);

  @override
  void clearCache() => super.noSuchMethod(
        Invocation.method(
          #clearCache,
          [],
        ),
        returnValueForMissingStub: null,
      );

  @override
  int getCacheSize() => (super.noSuchMethod(
        Invocation.method(
          #getCacheSize,
          [],
        ),
        returnValue: 0,
      ) as int);

  @override
  void dispose() => super.noSuchMethod(
        Invocation.method(
          #dispose,
          [],
        ),
        returnValueForMissingStub: null,
      );
}

/// A class which mocks [PronunciationService].
///
/// See the documentation for Mockito's code generation for more information.
class MockPronunciationService extends _i1.Mock
    implements _i16.PronunciationService {
  MockPronunciationService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i7.Stream<String> get pronunciationEvents => (super.noSuchMethod(
        Invocation.getter(#pronunciationEvents),
        returnValue: _i7.Stream<String>.empty(),
      ) as _i7.Stream<String>);

  @override
  _i7.Stream<String> get errorStream => (super.noSuchMethod(
        Invocation.getter(#errorStream),
        returnValue: _i7.Stream<String>.empty(),
      ) as _i7.Stream<String>);

  @override
  bool get isOnline => (super.noSuchMethod(
        Invocation.getter(#isOnline),
        returnValue: false,
      ) as bool);

  @override
  bool get usePronunciationGuidance => (super.noSuchMethod(
        Invocation.getter(#usePronunciationGuidance),
        returnValue: false,
      ) as bool);

  @override
  bool get useIpaNotation => (super.noSuchMethod(
        Invocation.getter(#useIpaNotation),
        returnValue: false,
      ) as bool);

  @override
  bool get useSimplifiedPhonetics => (super.noSuchMethod(
        Invocation.getter(#useSimplifiedPhonetics),
        returnValue: false,
      ) as bool);

  @override
  bool get useSyllableBreakdown => (super.noSuchMethod(
        Invocation.getter(#useSyllableBreakdown),
        returnValue: false,
      ) as bool);

  @override
  bool get showDifficultOnly => (super.noSuchMethod(
        Invocation.getter(#showDifficultOnly),
        returnValue: false,
      ) as bool);

  @override
  bool get autoPlayPronunciation => (super.noSuchMethod(
        Invocation.getter(#autoPlayPronunciation),
        returnValue: false,
      ) as bool);

  @override
  _i7.Future<void> setUsePronunciationGuidance(bool? value) =>
      (super.noSuchMethod(
        Invocation.method(
          #setUsePronunciationGuidance,
          [value],
        ),
        returnValue: _i7.Future<void>.value(),
        returnValueForMissingStub: _i7.Future<void>.value(),
      ) as _i7.Future<void>);

  @override
  _i7.Future<void> setUseIpaNotation(bool? value) => (super.noSuchMethod(
        Invocation.method(
          #setUseIpaNotation,
          [value],
        ),
        returnValue: _i7.Future<void>.value(),
        returnValueForMissingStub: _i7.Future<void>.value(),
      ) as _i7.Future<void>);

  @override
  _i7.Future<void> setUseSimplifiedPhonetics(bool? value) =>
      (super.noSuchMethod(
        Invocation.method(
          #setUseSimplifiedPhonetics,
          [value],
        ),
        returnValue: _i7.Future<void>.value(),
        returnValueForMissingStub: _i7.Future<void>.value(),
      ) as _i7.Future<void>);

  @override
  _i7.Future<void> setUseSyllableBreakdown(bool? value) => (super.noSuchMethod(
        Invocation.method(
          #setUseSyllableBreakdown,
          [value],
        ),
        returnValue: _i7.Future<void>.value(),
        returnValueForMissingStub: _i7.Future<void>.value(),
      ) as _i7.Future<void>);

  @override
  _i7.Future<void> setShowDifficultOnly(bool? value) => (super.noSuchMethod(
        Invocation.method(
          #setShowDifficultOnly,
          [value],
        ),
        returnValue: _i7.Future<void>.value(),
        returnValueForMissingStub: _i7.Future<void>.value(),
      ) as _i7.Future<void>);

  @override
  _i7.Future<void> setAutoPlayPronunciation(bool? value) => (super.noSuchMethod(
        Invocation.method(
          #setAutoPlayPronunciation,
          [value],
        ),
        returnValue: _i7.Future<void>.value(),
        returnValueForMissingStub: _i7.Future<void>.value(),
      ) as _i7.Future<void>);

  @override
  _i7.Future<_i6.TranslationPronunciation> getPronunciation({
    required String? text,
    required String? sourceLanguage,
    required String? targetLanguage,
    String? sourceRegion,
    String? targetRegion,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #getPronunciation,
          [],
          {
            #text: text,
            #sourceLanguage: sourceLanguage,
            #targetLanguage: targetLanguage,
            #sourceRegion: sourceRegion,
            #targetRegion: targetRegion,
          },
        ),
        returnValue: _i7.Future<_i6.TranslationPronunciation>.value(
            _FakeTranslationPronunciation_5(
          this,
          Invocation.method(
            #getPronunciation,
            [],
            {
              #text: text,
              #sourceLanguage: sourceLanguage,
              #targetLanguage: targetLanguage,
              #sourceRegion: sourceRegion,
              #targetRegion: targetRegion,
            },
          ),
        )),
      ) as _i7.Future<_i6.TranslationPronunciation>);

  @override
  _i7.Future<void> setOfflineMode(bool? enabled) => (super.noSuchMethod(
        Invocation.method(
          #setOfflineMode,
          [enabled],
        ),
        returnValue: _i7.Future<void>.value(),
        returnValueForMissingStub: _i7.Future<void>.value(),
      ) as _i7.Future<void>);

  @override
  _i7.Future<void> playPronunciationAudio(String? audioPath) =>
      (super.noSuchMethod(
        Invocation.method(
          #playPronunciationAudio,
          [audioPath],
        ),
        returnValue: _i7.Future<void>.value(),
        returnValueForMissingStub: _i7.Future<void>.value(),
      ) as _i7.Future<void>);

  @override
  _i7.Future<void> stopAudio() => (super.noSuchMethod(
        Invocation.method(
          #stopAudio,
          [],
        ),
        returnValue: _i7.Future<void>.value(),
        returnValueForMissingStub: _i7.Future<void>.value(),
      ) as _i7.Future<void>);

  @override
  void clearCache() => super.noSuchMethod(
        Invocation.method(
          #clearCache,
          [],
        ),
        returnValueForMissingStub: null,
      );

  @override
  int getCacheSize() => (super.noSuchMethod(
        Invocation.method(
          #getCacheSize,
          [],
        ),
        returnValue: 0,
      ) as int);

  @override
  void dispose() => super.noSuchMethod(
        Invocation.method(
          #dispose,
          [],
        ),
        returnValueForMissingStub: null,
      );
}

/// A class which mocks [LanguageDetectionService].
///
/// See the documentation for Mockito's code generation for more information.
class MockLanguageDetectionService extends _i1.Mock
    implements _i17.LanguageDetectionService {
  MockLanguageDetectionService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i7.Stream<String> get detectionEvents => (super.noSuchMethod(
        Invocation.getter(#detectionEvents),
        returnValue: _i7.Stream<String>.empty(),
      ) as _i7.Stream<String>);

  @override
  List<_i18.LanguageModel> get supportedLanguages => (super.noSuchMethod(
        Invocation.getter(#supportedLanguages),
        returnValue: <_i18.LanguageModel>[],
      ) as List<_i18.LanguageModel>);

  @override
  _i7.Future<String> detectLanguage(String? text) => (super.noSuchMethod(
        Invocation.method(
          #detectLanguage,
          [text],
        ),
        returnValue: _i7.Future<String>.value(_i9.dummyValue<String>(
          this,
          Invocation.method(
            #detectLanguage,
            [text],
          ),
        )),
      ) as _i7.Future<String>);

  @override
  _i7.Future<void> clearCache() => (super.noSuchMethod(
        Invocation.method(
          #clearCache,
          [],
        ),
        returnValue: _i7.Future<void>.value(),
        returnValueForMissingStub: _i7.Future<void>.value(),
      ) as _i7.Future<void>);

  @override
  void dispose() => super.noSuchMethod(
        Invocation.method(
          #dispose,
          [],
        ),
        returnValueForMissingStub: null,
      );
}

/// A class which mocks [TranslationMetricsService].
///
/// See the documentation for Mockito's code generation for more information.
class MockTranslationMetricsService extends _i1.Mock
    implements _i19.TranslationMetricsService {
  MockTranslationMetricsService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i7.Stream<Map<String, dynamic>> get metricsStream => (super.noSuchMethod(
        Invocation.getter(#metricsStream),
        returnValue: _i7.Stream<Map<String, dynamic>>.empty(),
      ) as _i7.Stream<Map<String, dynamic>>);

  @override
  Map<String, dynamic> get metrics => (super.noSuchMethod(
        Invocation.getter(#metrics),
        returnValue: <String, dynamic>{},
      ) as Map<String, dynamic>);

  @override
  _i7.Future<void> recordTranslation({
    required String? sourceLanguage,
    required String? targetLanguage,
    required int? durationMs,
    bool? isBackground = false,
    bool? isCached = false,
    bool? isOffline = false,
    String? syncStatus = '',
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #recordTranslation,
          [],
          {
            #sourceLanguage: sourceLanguage,
            #targetLanguage: targetLanguage,
            #durationMs: durationMs,
            #isBackground: isBackground,
            #isCached: isCached,
            #isOffline: isOffline,
            #syncStatus: syncStatus,
          },
        ),
        returnValue: _i7.Future<void>.value(),
        returnValueForMissingStub: _i7.Future<void>.value(),
      ) as _i7.Future<void>);

  @override
  _i7.Future<void> recordCancelledTranslation() => (super.noSuchMethod(
        Invocation.method(
          #recordCancelledTranslation,
          [],
        ),
        returnValue: _i7.Future<void>.value(),
        returnValueForMissingStub: _i7.Future<void>.value(),
      ) as _i7.Future<void>);

  @override
  _i7.Future<void> recordFailedTranslation() => (super.noSuchMethod(
        Invocation.method(
          #recordFailedTranslation,
          [],
        ),
        returnValue: _i7.Future<void>.value(),
        returnValueForMissingStub: _i7.Future<void>.value(),
      ) as _i7.Future<void>);

  @override
  double getAverageTranslationTimeForLanguagePair(
    String? sourceLanguage,
    String? targetLanguage,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #getAverageTranslationTimeForLanguagePair,
          [
            sourceLanguage,
            targetLanguage,
          ],
        ),
        returnValue: 0.0,
      ) as double);

  @override
  _i7.Future<void> resetMetrics() => (super.noSuchMethod(
        Invocation.method(
          #resetMetrics,
          [],
        ),
        returnValue: _i7.Future<void>.value(),
        returnValueForMissingStub: _i7.Future<void>.value(),
      ) as _i7.Future<void>);

  @override
  void dispose() => super.noSuchMethod(
        Invocation.method(
          #dispose,
          [],
        ),
        returnValueForMissingStub: null,
      );
}
