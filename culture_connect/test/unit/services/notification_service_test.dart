import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:culture_connect/models/booking.dart';
import 'package:culture_connect/services/notification_service.dart';

// Create a mock for FlutterLocalNotificationsPlugin
class MockFlutterLocalNotificationsPlugin extends Mock implements FlutterLocalNotificationsPlugin {
  @override
  Future<bool?> initialize(
    InitializationSettings initializationSettings, {
    SelectNotificationCallback? onSelectNotification,
    OnDidReceiveNotificationResponseCallback? onDidReceiveNotificationResponse,
    OnDidReceiveBackgroundNotificationResponseCallback? onDidReceiveBackgroundNotificationResponse,
  }) async {
    return true;
  }
  
  @override
  Future<void> zonedSchedule(
    int id,
    String? title,
    String? body,
    TZDateTime scheduledDate,
    NotificationDetails notificationDetails, {
    required UILocalNotificationDateInterpretation uiLocalNotificationDateInterpretation,
    AndroidScheduleMode? androidScheduleMode,
    String? payload,
    DateTimeComponents? matchDateTimeComponents,
  }) async {}
  
  @override
  Future<void> cancel(int id) async {}
}

// Generate mocks
@GenerateMocks([])
void main() {
  late NotificationService notificationService;
  late MockFlutterLocalNotificationsPlugin mockNotificationsPlugin;
  late Booking testBooking;

  setUp(() {
    notificationService = NotificationService();
    mockNotificationsPlugin = MockFlutterLocalNotificationsPlugin();
    
    // Create a test booking for tomorrow
    final now = DateTime.now();
    final tomorrow = DateTime(now.year, now.month, now.day + 1);
    
    testBooking = Booking(
      id: 'booking-123',
      experienceId: 'exp-456',
      date: tomorrow,
      timeSlot: TimeSlot(
        startTime: DateTime(tomorrow.year, tomorrow.month, tomorrow.day, 10, 0),
        endTime: DateTime(tomorrow.year, tomorrow.month, tomorrow.day, 12, 0),
      ),
      participantCount: 2,
      totalAmount: 100.0,
      status: BookingStatus.confirmed,
      specialRequirements: 'Vegetarian food',
      createdAt: now,
      updatedAt: now,
    );
    
    // Replace the real notifications plugin with our mock
    // This requires making the _flutterLocalNotificationsPlugin field accessible for testing
    // In a real app, you might need to use reflection or a test-specific constructor
  });

  group('NotificationService', () {
    test('_formatTime should format time correctly', () {
      // Test morning time
      final morningTime = DateTime(2023, 1, 1, 9, 30);
      expect(notificationService._formatTime(morningTime), equals('9:30 AM'));
      
      // Test afternoon time
      final afternoonTime = DateTime(2023, 1, 1, 14, 15);
      expect(notificationService._formatTime(afternoonTime), equals('2:15 PM'));
      
      // Test midnight
      final midnight = DateTime(2023, 1, 1, 0, 0);
      expect(notificationService._formatTime(midnight), equals('12:00 AM'));
      
      // Test noon
      final noon = DateTime(2023, 1, 1, 12, 0);
      expect(notificationService._formatTime(noon), equals('12:00 PM'));
    });

    test('scheduleBookingReminder should not schedule if notification time is in the past', () async {
      // Create a booking with a past date
      final now = DateTime.now();
      final yesterday = DateTime(now.year, now.month, now.day - 1);
      
      final pastBooking = Booking(
        id: 'booking-past',
        experienceId: 'exp-456',
        date: yesterday,
        timeSlot: TimeSlot(
          startTime: DateTime(yesterday.year, yesterday.month, yesterday.day, 10, 0),
          endTime: DateTime(yesterday.year, yesterday.month, yesterday.day, 12, 0),
        ),
        participantCount: 2,
        totalAmount: 100.0,
        status: BookingStatus.confirmed,
        specialRequirements: '',
        createdAt: now.subtract(const Duration(days: 2)),
        updatedAt: now.subtract(const Duration(days: 2)),
      );
      
      // Try to schedule a reminder
      await notificationService.scheduleBookingReminder(
        booking: pastBooking,
        beforeBooking: const Duration(hours: 1),
        title: 'Test Reminder',
        body: 'This is a test reminder',
      );
      
      // Since the notification time is in the past, no notification should be scheduled
      // We can't verify this directly without modifying the service for testability
    });

    test('setupBookingReminders should set up 24h and 1h reminders', () async {
      // This test requires making the service more testable
      // In a real app, you would inject the dependencies and mock them
      
      // For now, we can only test that the method doesn't throw an exception
      await expectLater(
        notificationService.setupBookingReminders(testBooking),
        completes,
      );
    });
  });
}
