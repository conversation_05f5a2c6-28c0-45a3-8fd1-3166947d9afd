import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:share_plus/share_plus.dart';
import 'package:culture_connect/models/booking.dart';
import 'package:culture_connect/models/experience.dart';
import 'package:culture_connect/services/sharing_service.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

// Create a mock for Share.share
class MockShare extends Mock {
  static Future<void> share(String text, {String? subject}) async {}
  static Future<void> shareXFiles(List<XFile> files, {String? text}) async {}
}

// Generate mocks
@GenerateMocks([])
void main() {
  late SharingService sharingService;
  late Booking testBooking;
  late Experience testExperience;

  setUp(() {
    sharingService = SharingService();
    
    final now = DateTime.now();
    
    // Create a test booking
    testBooking = Booking(
      id: 'booking-123',
      experienceId: 'exp-456',
      date: DateTime(2023, 10, 15),
      timeSlot: TimeSlot(
        startTime: DateTime(2023, 10, 15, 10, 0),
        endTime: DateTime(2023, 10, 15, 12, 0),
      ),
      participantCount: 2,
      totalAmount: 100.0,
      status: BookingStatus.confirmed,
      specialRequirements: 'Vegetarian food',
      createdAt: now.subtract(const Duration(days: 1)),
      updatedAt: now.subtract(const Duration(days: 1)),
    );
    
    // Create a test experience
    testExperience = Experience(
      id: 'exp-456',
      title: 'Cultural Walking Tour',
      description: 'Explore the rich cultural heritage of the city with a local guide.',
      imageUrl: 'https://example.com/image.jpg',
      rating: 4.8,
      reviewCount: 25,
      price: 50.0,
      category: 'Cultural Tours',
      location: 'City Center',
      coordinates: const LatLng(0, 0),
      guideId: 'guide-123',
      guideName: 'Local Guide',
      guideImageUrl: 'https://example.com/guide.jpg',
      languages: ['English', 'Spanish'],
      includedItems: ['Tour', 'Refreshments'],
      requirements: ['None'],
      createdAt: now.subtract(const Duration(days: 30)),
      updatedAt: now.subtract(const Duration(days: 1)),
    );
  });

  group('SharingService - Text Generation', () {
    test('_createBookingShareText should generate correct text', () {
      // Use a private method accessor to test private methods
      final shareText = sharingService._createBookingShareText(
        booking: testBooking,
        experienceTitle: 'Cultural Walking Tour',
      );
      
      // Assert
      expect(shareText, contains('Cultural Walking Tour'));
      expect(shareText, contains('Sunday, October 15, 2023'));
      expect(shareText, contains('10:00 AM - 12:00 PM'));
      expect(shareText, contains('Participants: 2'));
      expect(shareText, contains('Download CultureConnect'));
    });

    test('_createBookingShareText should include custom message when provided', () {
      // Use a private method accessor to test private methods
      final shareText = sharingService._createBookingShareText(
        booking: testBooking,
        experienceTitle: 'Cultural Walking Tour',
        customMessage: 'Join me on this amazing tour!',
      );
      
      // Assert
      expect(shareText, contains('Join me on this amazing tour!'));
      expect(shareText, isNot(contains('🎉 I just booked an experience on CultureConnect!')));
    });

    test('_createExperienceShareText should generate correct text', () {
      // Use a private method accessor to test private methods
      final shareText = sharingService._createExperienceShareText(
        experience: testExperience,
      );
      
      // Assert
      expect(shareText, contains('Cultural Walking Tour'));
      expect(shareText, contains('City Center'));
      expect(shareText, contains('4.8/5 (25 reviews)'));
      expect(shareText, contains('\$50.00'));
      expect(shareText, contains('Explore the rich cultural heritage'));
      expect(shareText, contains('Download CultureConnect'));
    });

    test('_createExperienceShareText should include custom message when provided', () {
      // Use a private method accessor to test private methods
      final shareText = sharingService._createExperienceShareText(
        experience: testExperience,
        customMessage: 'This tour looks amazing!',
      );
      
      // Assert
      expect(shareText, contains('This tour looks amazing!'));
      expect(shareText, isNot(contains('🌟 Check out this amazing cultural experience on CultureConnect!')));
    });

    test('_formatDate should format date correctly', () {
      // Use a private method accessor to test private methods
      final formattedDate = sharingService._formatDate(DateTime(2023, 10, 15));
      
      // Assert
      expect(formattedDate, equals('Sunday, October 15, 2023'));
    });
  });
}
