import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:geolocator/geolocator.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:culture_connect/services/location_service.dart';
import 'package:culture_connect/services/logging_service.dart';
import 'package:culture_connect/services/error_handling_service.dart';
import 'package:culture_connect/models/landmark.dart';

// Generate mocks for dependencies
@GenerateMocks([
  LoggingService,
  ErrorHandlingService,
  Position,
])
import 'location_service_test.mocks.dart';

// Mock Geolocator static methods
class MockGeolocator extends Mock implements Geolocator {
  static Future<Position> getCurrentPosition({
    LocationAccuracy desiredAccuracy = LocationAccuracy.best,
    bool forceAndroidLocationManager = false,
    Duration? timeLimit,
  }) async {
    return MockPosition();
  }

  static Future<LocationPermission> checkPermission() async {
    return LocationPermission.whileInUse;
  }

  static Future<LocationPermission> requestPermission() async {
    return LocationPermission.whileInUse;
  }

  static Future<bool> isLocationServiceEnabled() async {
    return true;
  }

  static Stream<Position> getPositionStream({
    LocationSettings? locationSettings,
  }) {
    return Stream.fromIterable([MockPosition()]);
  }

  static double distanceBetween(
    double startLatitude,
    double startLongitude,
    double endLatitude,
    double endLongitude,
  ) {
    return 1000.0; // 1 kilometer
  }
}

void main() {
  late LocationService locationService;
  late MockLoggingService mockLoggingService;
  late MockErrorHandlingService mockErrorHandlingService;
  late MockPosition mockPosition;

  setUp(() {
    mockLoggingService = MockLoggingService();
    mockErrorHandlingService = MockErrorHandlingService();
    mockPosition = MockPosition();

    // Setup mock position
    when(mockPosition.latitude).thenReturn(37.7749);
    when(mockPosition.longitude).thenReturn(-122.4194);
    when(mockPosition.timestamp).thenReturn(DateTime.now());
    when(mockPosition.accuracy).thenReturn(10.0);
    when(mockPosition.altitude).thenReturn(0.0);
    when(mockPosition.heading).thenReturn(90.0);
    when(mockPosition.speed).thenReturn(0.0);
    when(mockPosition.speedAccuracy).thenReturn(0.0);

    // Create location service with mocks
    locationService = LocationService(
      loggingService: mockLoggingService,
      errorHandlingService: mockErrorHandlingService,
    );
  });

  group('LocationService Tests', () {
    test('getCurrentPosition should return a Position', () async {
      // Arrange
      // Use the mock implementation

      // Act
      final position = await locationService.getCurrentPosition();

      // Assert
      expect(position, isNotNull);
      expect(position.latitude, 37.7749);
      expect(position.longitude, -122.4194);
    });

    test('calculateDistance should return correct distance', () {
      // Arrange
      const startLat = 37.7749;
      const startLng = -122.4194;
      const endLat = 37.7750;
      const endLng = -122.4195;

      // Act
      final distance = locationService.calculateDistance(
        startLat,
        startLng,
        endLat,
        endLng,
      );

      // Assert
      expect(distance, 1000.0);
    });

    test('formatDistance should format meters correctly', () {
      // Arrange
      const distanceInMeters = 500.0;

      // Act
      final formattedDistance =
          locationService.formatDistance(distanceInMeters);

      // Assert
      expect(formattedDistance, '500 m');
    });

    test('formatDistance should format kilometers correctly', () {
      // Arrange
      const distanceInMeters = 1500.0;

      // Act
      final formattedDistance =
          locationService.formatDistance(distanceInMeters);

      // Assert
      expect(formattedDistance, '1.5 km');
    });

    test('getNearbyLandmarks should return landmarks within radius', () async {
      // Arrange
      const latitude = 37.7749;
      const longitude = -122.4194;
      const radiusInMeters = 2000.0;

      // Act
      final landmarks = await locationService.getNearbyLandmarks(
        latitude,
        longitude,
        radiusInMeters,
      );

      // Assert
      expect(landmarks, isNotEmpty);
      // All landmarks should be within the radius
      for (final landmark in landmarks) {
        final landmarkLat = landmark.location['latitude'] as double;
        final landmarkLng = landmark.location['longitude'] as double;
        final distance = locationService.calculateDistance(
          latitude,
          longitude,
          landmarkLat,
          landmarkLng,
        );
        expect(distance <= radiusInMeters, isTrue);
      }
    });

    test('getRecommendedLandmarks should return sorted landmarks', () async {
      // Arrange
      const latitude = 37.7749;
      const longitude = -122.4194;
      const limit = 3;

      // Act
      final landmarks = await locationService.getRecommendedLandmarks(
        latitude,
        longitude,
        limit: limit,
      );

      // Assert
      expect(landmarks.length, lessThanOrEqualTo(limit));

      // Landmarks should be sorted by distance
      if (landmarks.length > 1) {
        final distances = landmarks.map((landmark) {
          final landmarkLat = landmark.location['latitude'] as double;
          final landmarkLng = landmark.location['longitude'] as double;
          return locationService.calculateDistance(
            latitude,
            longitude,
            landmarkLat,
            landmarkLng,
          );
        }).toList();

        for (int i = 0; i < distances.length - 1; i++) {
          expect(distances[i] <= distances[i + 1], isTrue);
        }
      }
    });

    test('loadMapStyle should return style string for valid style name',
        () async {
      // Arrange
      const styleName = 'silver';

      // Act
      final style = await locationService.loadMapStyle(styleName);

      // Assert
      expect(style, isNotNull);
      expect(style, contains('elementType'));
    });

    test('loadMapStyle should return null for invalid style name', () async {
      // Arrange
      const styleName = 'invalid_style';

      // Act
      final style = await locationService.loadMapStyle(styleName);

      // Assert
      expect(style, isNull);
    });

    test('getRoute should return a list of LatLng points', () async {
      // Arrange
      const origin = LatLng(37.7749, -122.4194);
      const destination = LatLng(37.7750, -122.4195);

      // Act
      final route = await locationService.getRoute(origin, destination);

      // Assert
      expect(route, isNotEmpty);
      expect(route.first, equals(origin));
      expect(route.last, equals(destination));
    });

    test('calculateRouteDistance should return correct distance', () {
      // Arrange
      const route = [
        LatLng(37.7749, -122.4194),
        LatLng(37.7750, -122.4195),
        LatLng(37.7751, -122.4196),
      ];

      // Act
      final distance = locationService.calculateRouteDistance(route);

      // Assert
      expect(distance, 2000.0); // 2 segments of 1000m each
    });

    test('estimateTravelTime should return correct time for walking', () {
      // Arrange
      const distanceInMeters = 1000.0;
      const mode = 'walking';

      // Act
      final timeInMinutes =
          locationService.estimateTravelTime(distanceInMeters, mode);

      // Assert
      expect(
          timeInMinutes, 13); // 1000m at 80m/min = 12.5 minutes, rounded to 13
    });

    test('estimateTravelTime should return correct time for cycling', () {
      // Arrange
      const distanceInMeters = 1000.0;
      const mode = 'cycling';

      // Act
      final timeInMinutes =
          locationService.estimateTravelTime(distanceInMeters, mode);

      // Assert
      expect(timeInMinutes, 4); // 1000m at 250m/min = 4 minutes
    });

    test('estimateTravelTime should return correct time for driving', () {
      // Arrange
      const distanceInMeters = 1000.0;
      const mode = 'driving';

      // Act
      final timeInMinutes =
          locationService.estimateTravelTime(distanceInMeters, mode);

      // Assert
      expect(timeInMinutes, 2); // 1000m at 500m/min = 2 minutes
    });
  });
}
