import 'dart:io';
import 'dart:convert';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:path_provider/path_provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:http/http.dart' as http;
import 'package:culture_connect/services/map_cache_manager.dart';
import 'package:culture_connect/services/logging_service.dart';
import 'package:culture_connect/services/error_handling_service.dart';

// Generate mocks for dependencies
@GenerateMocks([
  LoggingService, 
  ErrorHandlingService, 
  http.Client,
  Directory,
  File,
  FileStat,
])
import 'map_cache_manager_test.mocks.dart';

// Mock for path_provider
class MockPathProvider {
  static Future<Directory> getApplicationDocumentsDirectory() async {
    final mockDir = MockDirectory();
    when(mockDir.path).thenReturn('/mock/path');
    return mockDir;
  }
}

void main() {
  late MapCacheManager mapCacheManager;
  late MockLoggingService mockLoggingService;
  late MockErrorHandlingService mockErrorHandlingService;
  late MockClient mockHttpClient;
  late MockDirectory mockCacheDir;
  late MockFile mockCacheFile;
  late MockFileStat mockFileStat;

  setUp(() {
    mockLoggingService = MockLoggingService();
    mockErrorHandlingService = MockErrorHandlingService();
    mockHttpClient = MockClient();
    mockCacheDir = MockDirectory();
    mockCacheFile = MockFile();
    mockFileStat = MockFileStat();
    
    // Setup mock directory
    when(mockCacheDir.path).thenReturn('/mock/path/map_cache');
    when(mockCacheDir.exists()).thenAnswer((_) async => true);
    when(mockCacheDir.create(recursive: true)).thenAnswer((_) async => mockCacheDir);
    when(mockCacheDir.list()).thenAnswer((_) => Stream.fromIterable([mockCacheFile]));
    
    // Setup mock file
    when(mockCacheFile.path).thenReturn('/mock/path/map_cache/12_34_56.png');
    when(mockCacheFile.exists()).thenAnswer((_) async => true);
    when(mockCacheFile.lengthSync()).thenReturn(1024); // 1KB
    when(mockCacheFile.statSync()).thenReturn(mockFileStat);
    when(mockCacheFile.delete()).thenAnswer((_) async => mockCacheFile);
    when(mockCacheFile.writeAsBytes(any)).thenAnswer((_) async => mockCacheFile);
    
    // Setup mock file stat
    when(mockFileStat.modified).thenReturn(DateTime.now());
    
    // Setup shared preferences
    SharedPreferences.setMockInitialValues({
      'cached_map_regions': [
        jsonEncode({
          'id': 'region-1',
          'name': 'Test Region',
          'bounds': {
            'northeast': {
              'latitude': 37.78,
              'longitude': -122.41,
            },
            'southwest': {
              'latitude': 37.77,
              'longitude': -122.42,
            },
          },
          'minZoom': 10,
          'maxZoom': 15,
          'timestamp': DateTime.now().millisecondsSinceEpoch,
        }),
      ],
    });
    
    // Create map cache manager with mocks
    mapCacheManager = MapCacheManager(
      loggingService: mockLoggingService,
      errorHandlingService: mockErrorHandlingService,
    );
  });

  group('MapCacheManager Tests', () {
    test('getCachedRegions should return list of cached regions', () async {
      // Act
      final regions = await mapCacheManager.getCachedRegions();
      
      // Assert
      expect(regions, isNotEmpty);
      expect(regions.length, 1);
      expect(regions[0].id, 'region-1');
      expect(regions[0].name, 'Test Region');
      expect(regions[0].minZoom, 10);
      expect(regions[0].maxZoom, 15);
    });
    
    test('deleteCachedRegion should remove region from list', () async {
      // Arrange
      const regionId = 'region-1';
      
      // Act
      final result = await mapCacheManager.deleteCachedRegion(regionId);
      
      // Assert
      expect(result, isTrue);
      
      // Verify region was removed
      final regions = await mapCacheManager.getCachedRegions();
      expect(regions, isEmpty);
    });
    
    test('clearCache should remove all cached regions', () async {
      // Act
      final result = await mapCacheManager.clearCache();
      
      // Assert
      expect(result, isTrue);
      
      // Verify all regions were removed
      final regions = await mapCacheManager.getCachedRegions();
      expect(regions, isEmpty);
    });
    
    test('getCacheSize should return total size of cached files', () async {
      // Act
      final size = await mapCacheManager.getCacheSize();
      
      // Assert
      expect(size, 1024); // 1KB from our mock file
    });
    
    test('cacheMapRegion should download and cache tiles', () async {
      // Arrange
      final bounds = LatLngBounds(
        northeast: const LatLng(37.78, -122.41),
        southwest: const LatLng(37.77, -122.42),
      );
      const minZoom = 10;
      const maxZoom = 11;
      
      // Mock HTTP response
      when(mockHttpClient.get(any)).thenAnswer((_) async => 
        http.Response('mock image data', 200, headers: {'content-type': 'image/png'}));
      
      // Act
      final result = await mapCacheManager.cacheMapRegion(
        bounds,
        minZoom,
        maxZoom,
        (progress) {
          // Verify progress callback is called with values between 0 and 1
          expect(progress >= 0 && progress <= 1, isTrue);
        },
      );
      
      // Assert
      expect(result, isTrue);
      
      // Verify region was added to cached regions
      final regions = await mapCacheManager.getCachedRegions();
      expect(regions.length, 2); // Original + new region
    });
    
    test('_formatSize should format bytes correctly', () {
      // Using the extension method to test private method
      
      // Test bytes
      expect(mapCacheManager.formatSize(500), '500 B');
      
      // Test kilobytes
      expect(mapCacheManager.formatSize(1500), '1.5 KB');
      
      // Test megabytes
      expect(mapCacheManager.formatSize(1500000), '1.4 MB');
      
      // Test gigabytes
      expect(mapCacheManager.formatSize(1500000000), '1.4 GB');
    });
  });
}

// Extension to expose private methods for testing
extension MapCacheManagerTestExtension on MapCacheManager {
  String formatSize(int bytes) {
    if (bytes < 1024) {
      return '$bytes B';
    } else if (bytes < 1024 * 1024) {
      return '${(bytes / 1024).toStringAsFixed(1)} KB';
    } else if (bytes < 1024 * 1024 * 1024) {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    } else {
      return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
    }
  }
}
