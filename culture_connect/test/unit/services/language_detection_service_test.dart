import 'dart:convert';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import 'package:culture_connect/models/translation/language_model.dart';
import 'package:culture_connect/services/language_detection_service.dart';

// Generate mocks for dependencies
@GenerateMocks([
  http.Client,
  SharedPreferences,
])
import 'language_detection_service_test.mocks.dart';

void main() {
  late LanguageDetectionService service;
  late MockClient mockClient;
  late MockSharedPreferences mockPrefs;
  
  // Sample data for testing
  final sampleApiKey = 'test-api-key';
  final sampleCacheData = {
    '123456': 'en',
    '789012': 'fr',
    '345678': 'es',
  };

  setUp(() {
    mockClient = MockClient();
    mockPrefs = MockSharedPreferences();
    
    // Setup mock SharedPreferences
    when(mockPrefs.getString('language_detection_cache'))
        .thenReturn(jsonEncode(sampleCacheData));
    when(mockPrefs.setString(any, any)).thenAnswer((_) async => true);
    
    // Setup mock HTTP client
    when(mockClient.post(any, body: anyNamed('body')))
        .thenAnswer((_) async => http.Response(
              '{"data":{"detections":[[{"language":"en","confidence":0.9}]]}}',
              200,
              headers: {'content-type': 'application/json'},
            ));
    
    service = LanguageDetectionService(mockClient, mockPrefs, sampleApiKey);
  });

  group('LanguageDetectionService Tests', () {
    test('supportedLanguages should return a list of supported languages', () {
      // Act
      final languages = service.supportedLanguages;
      
      // Assert
      expect(languages, isA<List<LanguageModel>>());
      expect(languages.length, greaterThan(0));
      expect(languages.first.code, isNotEmpty);
      expect(languages.first.name, isNotEmpty);
    });
    
    test('detectLanguage should return language from cache if available', () async {
      // Arrange
      final text = 'This is a test';
      final cacheKey = text.hashCode.toString();
      
      // Mock that the cache contains this key
      when(mockPrefs.getString('language_detection_cache'))
          .thenReturn(jsonEncode({cacheKey: 'en'}));
      
      // Act
      final detectedLanguage = await service.detectLanguage(text);
      
      // Assert
      expect(detectedLanguage, 'en');
      
      // Verify HTTP client was not called
      verifyNever(mockClient.post(any, body: anyNamed('body')));
    });
    
    test('detectLanguage should detect language and cache result if not in cache', () async {
      // Arrange
      final text = 'This is a new test';
      final cacheKey = text.hashCode.toString();
      
      // Mock that the cache does not contain this key
      when(mockPrefs.getString('language_detection_cache'))
          .thenReturn(jsonEncode({}));
      
      // Act
      final detectedLanguage = await service.detectLanguage(text);
      
      // Assert
      expect(detectedLanguage, 'en');
      
      // Verify cache was updated
      verify(mockPrefs.setString(any, any)).called(1);
    });
    
    test('detectLanguage should default to English for very short texts', () async {
      // Arrange
      final text = 'Hi';
      
      // Act
      final detectedLanguage = await service.detectLanguage(text);
      
      // Assert
      expect(detectedLanguage, 'en');
      
      // Verify HTTP client was not called
      verifyNever(mockClient.post(any, body: anyNamed('body')));
    });
    
    test('detectLanguage should detect French text', () async {
      // Arrange
      final text = 'Bonjour comment ça va';
      
      // Act
      final detectedLanguage = await service.detectLanguage(text);
      
      // Assert
      expect(detectedLanguage, 'fr');
    });
    
    test('detectLanguage should detect Spanish text', () async {
      // Arrange
      final text = 'Hola cómo estás señor';
      
      // Act
      final detectedLanguage = await service.detectLanguage(text);
      
      // Assert
      expect(detectedLanguage, 'es');
    });
    
    test('detectLanguage should detect Yoruba text', () async {
      // Arrange
      final text = 'Bawo ni jowo e kaaro';
      
      // Act
      final detectedLanguage = await service.detectLanguage(text);
      
      // Assert
      expect(detectedLanguage, 'yo');
    });
    
    test('detectLanguage should detect Igbo text', () async {
      // Arrange
      final text = 'Kedu biko daalu';
      
      // Act
      final detectedLanguage = await service.detectLanguage(text);
      
      // Assert
      expect(detectedLanguage, 'ig');
    });
    
    test('detectLanguage should detect Hausa text', () async {
      // Arrange
      final text = 'Sannu yaya na gode';
      
      // Act
      final detectedLanguage = await service.detectLanguage(text);
      
      // Assert
      expect(detectedLanguage, 'ha');
    });
    
    test('detectLanguage should detect Swahili text', () async {
      // Arrange
      final text = 'Jambo habari asante';
      
      // Act
      final detectedLanguage = await service.detectLanguage(text);
      
      // Assert
      expect(detectedLanguage, 'sw');
    });
    
    test('clearCache should clear the detection cache', () async {
      // Act
      await service.clearCache();
      
      // Assert
      verify(mockPrefs.setString('language_detection_cache', '{}')).called(1);
    });
  });
}
