// Mocks generated by <PERSON>ckito 5.4.5 from annotations
// in culture_connect/test/unit/services/location_service_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i3;
import 'dart:ui' as _i7;

import 'package:culture_connect/services/error_handling_service.dart' as _i5;
import 'package:culture_connect/services/logging_service.dart' as _i2;
import 'package:flutter/material.dart' as _i6;
import 'package:geolocator_platform_interface/src/models/position.dart' as _i8;
import 'package:mockito/mockito.dart' as _i1;
import 'package:mockito/src/dummies.dart' as _i4;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeDateTime_0 extends _i1.SmartFake implements DateTime {
  _FakeDateTime_0(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

/// A class which mocks [LoggingService].
///
/// See the documentation for Mockito's code generation for more information.
class MockLoggingService extends _i1.Mock implements _i2.LoggingService {
  MockLoggingService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i3.Future<void> initialize() => (super.noSuchMethod(
        Invocation.method(
          #initialize,
          [],
        ),
        returnValue: _i3.Future<void>.value(),
        returnValueForMissingStub: _i3.Future<void>.value(),
      ) as _i3.Future<void>);

  @override
  void setLogLevel(_i2.LogLevel? level) => super.noSuchMethod(
        Invocation.method(
          #setLogLevel,
          [level],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void configure({
    bool? logToConsole,
    bool? logToFile,
    bool? logToCrashlytics,
    int? maxLogFileSize,
    int? maxLogFiles,
  }) =>
      super.noSuchMethod(
        Invocation.method(
          #configure,
          [],
          {
            #logToConsole: logToConsole,
            #logToFile: logToFile,
            #logToCrashlytics: logToCrashlytics,
            #maxLogFileSize: maxLogFileSize,
            #maxLogFiles: maxLogFiles,
          },
        ),
        returnValueForMissingStub: null,
      );

  @override
  void verbose(
    String? tag,
    String? message, [
    dynamic data,
    StackTrace? stackTrace,
  ]) =>
      super.noSuchMethod(
        Invocation.method(
          #verbose,
          [
            tag,
            message,
            data,
            stackTrace,
          ],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void debug(
    String? tag,
    String? message, [
    dynamic data,
    StackTrace? stackTrace,
  ]) =>
      super.noSuchMethod(
        Invocation.method(
          #debug,
          [
            tag,
            message,
            data,
            stackTrace,
          ],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void info(
    String? tag,
    String? message, [
    dynamic data,
    StackTrace? stackTrace,
  ]) =>
      super.noSuchMethod(
        Invocation.method(
          #info,
          [
            tag,
            message,
            data,
            stackTrace,
          ],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void warning(
    String? tag,
    String? message, [
    dynamic data,
    StackTrace? stackTrace,
  ]) =>
      super.noSuchMethod(
        Invocation.method(
          #warning,
          [
            tag,
            message,
            data,
            stackTrace,
          ],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void error(
    String? tag,
    String? message, [
    dynamic error,
    StackTrace? stackTrace,
  ]) =>
      super.noSuchMethod(
        Invocation.method(
          #error,
          [
            tag,
            message,
            error,
            stackTrace,
          ],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void critical(
    String? tag,
    String? message, [
    dynamic error,
    StackTrace? stackTrace,
  ]) =>
      super.noSuchMethod(
        Invocation.method(
          #critical,
          [
            tag,
            message,
            error,
            stackTrace,
          ],
        ),
        returnValueForMissingStub: null,
      );

  @override
  _i3.Future<String> getConnectivityStatus() => (super.noSuchMethod(
        Invocation.method(
          #getConnectivityStatus,
          [],
        ),
        returnValue: _i3.Future<String>.value(_i4.dummyValue<String>(
          this,
          Invocation.method(
            #getConnectivityStatus,
            [],
          ),
        )),
      ) as _i3.Future<String>);

  @override
  void dispose() => super.noSuchMethod(
        Invocation.method(
          #dispose,
          [],
        ),
        returnValueForMissingStub: null,
      );
}

/// A class which mocks [ErrorHandlingService].
///
/// See the documentation for Mockito's code generation for more information.
class MockErrorHandlingService extends _i1.Mock
    implements _i5.ErrorHandlingService {
  MockErrorHandlingService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i3.Future<void> initialize() => (super.noSuchMethod(
        Invocation.method(
          #initialize,
          [],
        ),
        returnValue: _i3.Future<void>.value(),
        returnValueForMissingStub: _i3.Future<void>.value(),
      ) as _i3.Future<void>);

  @override
  _i3.Future<void> handleError({
    required Object? error,
    required String? context,
    StackTrace? stackTrace,
    _i5.ErrorType? type = _i5.ErrorType.unknown,
    _i5.ErrorSeverity? severity = _i5.ErrorSeverity.medium,
    Map<String, dynamic>? additionalData,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #handleError,
          [],
          {
            #error: error,
            #context: context,
            #stackTrace: stackTrace,
            #type: type,
            #severity: severity,
            #additionalData: additionalData,
          },
        ),
        returnValue: _i3.Future<void>.value(),
        returnValueForMissingStub: _i3.Future<void>.value(),
      ) as _i3.Future<void>);

  @override
  void showErrorDialog(
    _i6.BuildContext? context,
    String? title,
    String? message, {
    String? buttonText,
    _i7.VoidCallback? onPressed,
  }) =>
      super.noSuchMethod(
        Invocation.method(
          #showErrorDialog,
          [
            context,
            title,
            message,
          ],
          {
            #buttonText: buttonText,
            #onPressed: onPressed,
          },
        ),
        returnValueForMissingStub: null,
      );

  @override
  void showErrorSnackBar(
    _i6.BuildContext? context,
    String? message, {
    Duration? duration = const Duration(seconds: 4),
  }) =>
      super.noSuchMethod(
        Invocation.method(
          #showErrorSnackBar,
          [
            context,
            message,
          ],
          {#duration: duration},
        ),
        returnValueForMissingStub: null,
      );
}

/// A class which mocks [Position].
///
/// See the documentation for Mockito's code generation for more information.
// ignore: must_be_immutable
class MockPosition extends _i1.Mock implements _i8.Position {
  MockPosition() {
    _i1.throwOnMissingStub(this);
  }

  @override
  double get latitude => (super.noSuchMethod(
        Invocation.getter(#latitude),
        returnValue: 0.0,
      ) as double);

  @override
  double get longitude => (super.noSuchMethod(
        Invocation.getter(#longitude),
        returnValue: 0.0,
      ) as double);

  @override
  DateTime get timestamp => (super.noSuchMethod(
        Invocation.getter(#timestamp),
        returnValue: _FakeDateTime_0(
          this,
          Invocation.getter(#timestamp),
        ),
      ) as DateTime);

  @override
  double get altitude => (super.noSuchMethod(
        Invocation.getter(#altitude),
        returnValue: 0.0,
      ) as double);

  @override
  double get altitudeAccuracy => (super.noSuchMethod(
        Invocation.getter(#altitudeAccuracy),
        returnValue: 0.0,
      ) as double);

  @override
  double get accuracy => (super.noSuchMethod(
        Invocation.getter(#accuracy),
        returnValue: 0.0,
      ) as double);

  @override
  double get heading => (super.noSuchMethod(
        Invocation.getter(#heading),
        returnValue: 0.0,
      ) as double);

  @override
  double get headingAccuracy => (super.noSuchMethod(
        Invocation.getter(#headingAccuracy),
        returnValue: 0.0,
      ) as double);

  @override
  double get speed => (super.noSuchMethod(
        Invocation.getter(#speed),
        returnValue: 0.0,
      ) as double);

  @override
  double get speedAccuracy => (super.noSuchMethod(
        Invocation.getter(#speedAccuracy),
        returnValue: 0.0,
      ) as double);

  @override
  bool get isMocked => (super.noSuchMethod(
        Invocation.getter(#isMocked),
        returnValue: false,
      ) as bool);

  @override
  Map<String, dynamic> toJson() => (super.noSuchMethod(
        Invocation.method(
          #toJson,
          [],
        ),
        returnValue: <String, dynamic>{},
      ) as Map<String, dynamic>);
}
