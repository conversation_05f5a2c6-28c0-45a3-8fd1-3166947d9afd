#!/bin/bash

# Run all tests for CultureConnect

# Set colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[0;33m'
NC='\033[0m' # No Color

echo -e "${YELLOW}Running CultureConnect Tests${NC}"
echo "==============================="

# Create test results directory
mkdir -p test_results

# Function to run tests and log results
run_test() {
  TEST_TYPE=$1
  TEST_PATH=$2
  TEST_NAME=$3

  echo -e "\n${YELLOW}Running $TEST_NAME tests...${NC}"

  if [ "$TEST_TYPE" = "unit" ] || [ "$TEST_TYPE" = "widget" ]; then
    flutter test $TEST_PATH --coverage > test_results/${TEST_NAME}_results.txt
  elif [ "$TEST_TYPE" = "integration" ]; then
    flutter test $TEST_PATH -d $DEVICE_ID --coverage > test_results/${TEST_NAME}_results.txt
  fi

  if [ $? -eq 0 ]; then
    echo -e "${GREEN}✓ $TEST_NAME tests passed${NC}"
    return 0
  else
    echo -e "${RED}✗ $TEST_NAME tests failed${NC}"
    return 1
  fi
}

# Check if a device is connected for integration tests
DEVICE_ID=$(flutter devices | grep -v "Chrome" | grep -v "web" | grep -v "• No devices" | head -n 1 | awk '{print $1}')

if [ -z "$DEVICE_ID" ]; then
  echo -e "${RED}No physical device or emulator found for integration tests.${NC}"
  echo -e "${YELLOW}Integration and performance tests will be skipped.${NC}"
  RUN_INTEGRATION=false
else
  echo -e "${GREEN}Device found: $DEVICE_ID${NC}"
  RUN_INTEGRATION=true
fi

# Run unit tests
echo -e "\n${YELLOW}Running Unit Tests${NC}"
echo "------------------------"

# Services tests
run_test "unit" "test/unit/services/startup_optimization_service_test.dart" "StartupOptimizationService"
run_test "unit" "test/unit/services/ar_lazy_loading_service_test.dart" "ARLazyLoadingService"
run_test "unit" "test/unit/services/auth_service_test.dart" "AuthService"
run_test "unit" "test/unit/services/ar_backend_service_test.dart" "ARBackendService"
run_test "unit" "test/unit/services/ar_accessibility_service_test.dart" "ARAccessibilityService"
run_test "unit" "test/unit/services/ar_voice_command_service_test.dart" "ARVoiceCommandService"
run_test "unit" "test/unit/services/ar_recording_service_test.dart" "ARRecordingService"
run_test "unit" "test/unit/services/location_service_test.dart" "LocationService"
run_test "unit" "test/unit/services/map_cache_manager_test.dart" "MapCacheManager"

# Models tests
run_test "unit" "test/unit/models/experience_model_test.dart" "ExperienceModel"

# Providers tests
run_test "unit" "test/unit/providers/experiences_provider_test.dart" "ExperiencesProvider"

# Run widget tests
echo -e "\n${YELLOW}Running Widget Tests${NC}"
echo "------------------------"

# Screens tests
run_test "widget" "test/widget/screens/enhanced_ar_explore_screen_test.dart" "EnhancedARExploreScreen"
run_test "widget" "test/widget/screens/splash_screen_test.dart" "SplashScreen"
run_test "widget" "test/widget/screens/map_view_screen_test.dart" "MapViewScreen"

# Components tests
run_test "widget" "test/widget/components/experience_card_test.dart" "ExperienceCard"

# Run integration tests if a device is connected
if [ "$RUN_INTEGRATION" = true ]; then
  echo -e "\n${YELLOW}Running Integration Tests${NC}"
  echo "------------------------"

  # User flows
  run_test "integration" "test/integration/auth_flow_test.dart" "AuthFlow"
  run_test "integration" "test/integration/ar_experience_flow_test.dart" "ARExperienceFlow"
  run_test "integration" "test/integration/map_exploration_flow_test.dart" "MapExplorationFlow"

  echo -e "\n${YELLOW}Running Performance Tests${NC}"
  echo "------------------------"

  # Performance tests
  run_test "integration" "test/performance/startup_performance_test.dart" "StartupPerformance"
  run_test "integration" "test/performance/ar_performance_test.dart" "ARPerformance"
  run_test "integration" "test/performance/map_performance_test.dart" "MapPerformance"
fi

# Generate coverage report
echo -e "\n${YELLOW}Generating Coverage Report${NC}"
echo "------------------------"

if command -v lcov >/dev/null 2>&1; then
  # Convert coverage data to lcov format
  flutter pub run test_coverage

  # Generate HTML report
  genhtml coverage/lcov.info -o coverage/html

  echo -e "${GREEN}Coverage report generated at coverage/html/index.html${NC}"
else
  echo -e "${YELLOW}lcov not installed. Skipping HTML coverage report generation.${NC}"
  echo -e "${YELLOW}Install lcov to generate HTML reports.${NC}"
fi

echo -e "\n${GREEN}All tests completed!${NC}"
echo "==============================="
