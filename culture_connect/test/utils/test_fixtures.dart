import 'dart:convert';
import 'dart:io';
import 'package:flutter/services.dart';
import 'package:path/path.dart' as path;

/// A utility class for loading test fixtures.
class TestFixtures {
  /// The base directory for test fixtures.
  static const String fixturesDir = 'test/fixtures';

  /// Loads a JSON fixture file and returns the decoded JSON.
  ///
  /// [fileName] is the name of the JSON file without the .json extension.
  /// [dir] is an optional subdirectory within the fixtures directory.
  ///
  /// Example:
  /// ```dart
  /// final userData = TestFixtures.loadJsonFixture('user_data');
  /// final landmarkData = TestFixtures.loadJsonFixture('landmark', dir: 'ar');
  /// ```
  static Map<String, dynamic> loadJsonFixture(String fileName, {String? dir}) {
    final filePath = _getFixturePath(fileName, dir: dir, extension: 'json');
    final fileContent = File(filePath).readAsStringSync();
    return json.decode(fileContent) as Map<String, dynamic>;
  }

  /// Loads a JSON fixture file and returns the decoded JSON as a list.
  ///
  /// [fileName] is the name of the JSON file without the .json extension.
  /// [dir] is an optional subdirectory within the fixtures directory.
  ///
  /// Example:
  /// ```dart
  /// final usersList = TestFixtures.loadJsonListFixture('users_list');
  /// final landmarksList = TestFixtures.loadJsonListFixture('landmarks', dir: 'ar');
  /// ```
  static List<dynamic> loadJsonListFixture(String fileName, {String? dir}) {
    final filePath = _getFixturePath(fileName, dir: dir, extension: 'json');
    final fileContent = File(filePath).readAsStringSync();
    return json.decode(fileContent) as List<dynamic>;
  }

  /// Loads a text fixture file and returns the content as a string.
  ///
  /// [fileName] is the name of the text file without the .txt extension.
  /// [dir] is an optional subdirectory within the fixtures directory.
  ///
  /// Example:
  /// ```dart
  /// final responseBody = TestFixtures.loadTextFixture('response_body');
  /// ```
  static String loadTextFixture(String fileName, {String? dir}) {
    final filePath = _getFixturePath(fileName, dir: dir, extension: 'txt');
    return File(filePath).readAsStringSync();
  }

  /// Loads a binary fixture file and returns the content as bytes.
  ///
  /// [fileName] is the name of the binary file with its extension.
  /// [dir] is an optional subdirectory within the fixtures directory.
  ///
  /// Example:
  /// ```dart
  /// final imageBytes = TestFixtures.loadBinaryFixture('test_image.png');
  /// ```
  static Uint8List loadBinaryFixture(String fileName, {String? dir}) {
    final filePath = _getFixturePath(fileName, dir: dir);
    return File(filePath).readAsBytesSync();
  }

  /// Creates a mock asset bundle that returns fixture data for asset lookups.
  ///
  /// This is useful for testing code that loads assets.
  ///
  /// Example:
  /// ```dart
  /// final mockBundle = TestFixtures.createMockAssetBundle();
  /// ```
  static AssetBundle createMockAssetBundle() {
    return _MockAssetBundle();
  }

  /// Gets the full path to a fixture file.
  ///
  /// [fileName] is the name of the file.
  /// [dir] is an optional subdirectory within the fixtures directory.
  /// [extension] is an optional file extension to append if not included in fileName.
  static String _getFixturePath(String fileName, {String? dir, String? extension}) {
    // If fileName already has an extension, don't add another one
    final hasExtension = fileName.contains('.');
    final fileNameWithExt = hasExtension ? fileName : '$fileName.${extension ?? 'json'}';
    
    // Construct the path
    final dirPath = dir != null ? path.join(fixturesDir, dir) : fixturesDir;
    return path.join(dirPath, fileNameWithExt);
  }
}

/// A mock implementation of [AssetBundle] that returns fixture data.
class _MockAssetBundle extends AssetBundle {
  @override
  Future<ByteData> load(String key) async {
    // Extract the asset name from the key (e.g., 'assets/images/logo.png' -> 'logo.png')
    final fileName = path.basename(key);
    
    // Determine the fixture subdirectory based on the asset path
    final assetDir = path.dirname(key);
    final fixtureDir = assetDir.replaceAll('assets/', '');
    
    // Load the binary fixture
    final bytes = TestFixtures.loadBinaryFixture(fileName, dir: fixtureDir);
    
    return ByteData.view(bytes.buffer);
  }

  @override
  Future<String> loadString(String key, {bool cache = true}) async {
    // Extract the asset name from the key
    final fileName = path.basename(key);
    
    // Determine the fixture subdirectory based on the asset path
    final assetDir = path.dirname(key);
    final fixtureDir = assetDir.replaceAll('assets/', '');
    
    // Determine the file extension
    final extension = path.extension(fileName).replaceAll('.', '');
    
    // Load the appropriate fixture based on extension
    if (extension == 'json') {
      final jsonData = TestFixtures.loadJsonFixture(
        fileName.replaceAll('.json', ''), 
        dir: fixtureDir,
      );
      return json.encode(jsonData);
    } else {
      return TestFixtures.loadTextFixture(
        fileName.replaceAll('.txt', ''), 
        dir: fixtureDir,
      );
    }
  }

  @override
  Future<T> loadStructuredData<T>(String key, Future<T> Function(String value) parser) async {
    final string = await loadString(key);
    return parser(string);
  }
}

/// Extension methods for working with test fixtures in tests.
extension TestFixturesExtension on Object {
  /// Creates a sample user data fixture for testing.
  static Map<String, dynamic> createSampleUserData({
    String id = 'test-user-id',
    String name = 'Test User',
    String email = '<EMAIL>',
    bool isVerified = true,
  }) {
    return {
      'id': id,
      'name': name,
      'email': email,
      'isVerified': isVerified,
      'photoUrl': 'https://example.com/photo.jpg',
      'createdAt': '2023-01-01T00:00:00.000Z',
    };
  }

  /// Creates a sample landmark data fixture for testing.
  static Map<String, dynamic> createSampleLandmarkData({
    String id = 'landmark-1',
    String name = 'Eiffel Tower',
    String description = 'Famous landmark in Paris',
    double latitude = 48.8584,
    double longitude = 2.2945,
    bool hasArContent = true,
  }) {
    return {
      'id': id,
      'name': name,
      'description': description,
      'latitude': latitude,
      'longitude': longitude,
      'hasArContent': hasArContent,
      'arContentId': hasArContent ? 'ar-$id' : null,
      'imageUrl': 'https://example.com/$id.jpg',
      'historicalSignificance': 'Built for the 1889 World Fair',
      'culturalContext': 'Symbol of Paris and France',
    };
  }

  /// Creates a sample AR model data fixture for testing.
  static Map<String, dynamic> createSampleArModelData({
    String id = 'model-1',
    String name = 'Eiffel Tower Model',
    String modelUrl = 'https://example.com/models/eiffel.glb',
  }) {
    return {
      'id': id,
      'name': name,
      'modelUrl': modelUrl,
      'textureUrl': 'https://example.com/textures/eiffel.jpg',
      'scale': 1.0,
      'rotation': [0.0, 0.0, 0.0],
      'position': [0.0, 0.0, 0.0],
      'fileSize': 1024 * 1024, // 1MB
    };
  }
}
