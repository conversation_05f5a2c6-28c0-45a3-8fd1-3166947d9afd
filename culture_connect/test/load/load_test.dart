import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'dart:async';
import 'package:culture_connect/services/api_service.dart';
import 'package:culture_connect/services/logging_service.dart';
import 'package:culture_connect/services/connectivity_service.dart';
import 'package:culture_connect/models/experience_model.dart';

@GenerateMocks([http.Client, ConnectivityService])
void main() {
  late MockClient mockClient;
  late ApiService apiService;
  late LoggingService loggingService;
  late MockConnectivityService mockConnectivityService;

  setUp(() {
    mockClient = MockClient();
    loggingService = LoggingService();
    mockConnectivityService = MockConnectivityService();
    
    apiService = ApiService(
      client: mockClient,
      loggingService: loggingService,
      connectivityService: mockConnectivityService,
    );
    
    // Set up default mock behavior
    when(mockConnectivityService.isConnected()).thenAnswer((_) async => true);
  });

  group('API Load Tests', () {
    test('Should handle multiple concurrent requests', () async {
      // Arrange
      when(mockClient.get(
        Uri.parse('https://api.example.com/experiences'),
        headers: anyNamed('headers'),
      )).thenAnswer((_) async {
        // Simulate network delay
        await Future.delayed(const Duration(milliseconds: 100));
        return http.Response(
          jsonEncode({
            'data': [
              {
                'id': '1',
                'title': 'Experience 1',
                'description': 'Description 1',
                'imageUrl': 'https://example.com/image1.jpg',
                'price': 100.0,
                'location': 'Location 1',
                'rating': 4.5,
                'reviewCount': 10,
              }
            ]
          }),
          200,
        );
      });

      // Act
      // Make 10 concurrent requests
      final futures = List.generate(
        10,
        (_) => apiService.getExperiences(),
      );
      
      final results = await Future.wait(futures);
      
      // Assert
      expect(results.length, equals(10));
      for (final experiences in results) {
        expect(experiences.length, equals(1));
        expect(experiences[0].id, equals('1'));
      }
      
      // Verify that the API was called 10 times
      verify(mockClient.get(
        Uri.parse('https://api.example.com/experiences'),
        headers: anyNamed('headers'),
      )).called(10);
    });

    test('Should handle high volume of sequential requests', () async {
      // Arrange
      when(mockClient.get(
        Uri.parse('https://api.example.com/experiences'),
        headers: anyNamed('headers'),
      )).thenAnswer((_) async {
        // Simulate network delay
        await Future.delayed(const Duration(milliseconds: 50));
        return http.Response(
          jsonEncode({
            'data': [
              {
                'id': '1',
                'title': 'Experience 1',
                'description': 'Description 1',
                'imageUrl': 'https://example.com/image1.jpg',
                'price': 100.0,
                'location': 'Location 1',
                'rating': 4.5,
                'reviewCount': 10,
              }
            ]
          }),
          200,
        );
      });

      // Act
      // Make 50 sequential requests
      for (int i = 0; i < 50; i++) {
        final experiences = await apiService.getExperiences();
        expect(experiences.length, equals(1));
        expect(experiences[0].id, equals('1'));
      }
      
      // Assert
      // Verify that the API was called 50 times
      verify(mockClient.get(
        Uri.parse('https://api.example.com/experiences'),
        headers: anyNamed('headers'),
      )).called(50);
    });

    test('Should handle timeouts gracefully', () async {
      // Arrange
      when(mockClient.get(
        Uri.parse('https://api.example.com/experiences'),
        headers: anyNamed('headers'),
      )).thenAnswer((_) async {
        // Simulate a timeout
        await Future.delayed(const Duration(seconds: 5));
        throw TimeoutException('Connection timeout');
      });

      // Act & Assert
      expect(
        () => apiService.getExperiences(),
        throwsA(isA<ConnectionException>()),
      );
    });

    test('Should handle server errors under load', () async {
      // Arrange
      int requestCount = 0;
      
      // Simulate a server that starts returning errors after 5 requests
      when(mockClient.get(
        Uri.parse('https://api.example.com/experiences'),
        headers: anyNamed('headers'),
      )).thenAnswer((_) async {
        requestCount++;
        
        if (requestCount <= 5) {
          return http.Response(
            jsonEncode({
              'data': [
                {
                  'id': '1',
                  'title': 'Experience 1',
                  'description': 'Description 1',
                  'imageUrl': 'https://example.com/image1.jpg',
                  'price': 100.0,
                  'location': 'Location 1',
                  'rating': 4.5,
                  'reviewCount': 10,
                }
              ]
            }),
            200,
          );
        } else {
          return http.Response(
            jsonEncode({
              'error': 'Server overloaded',
            }),
            503,
          );
        }
      });

      // Act
      // First 5 requests should succeed
      for (int i = 0; i < 5; i++) {
        final experiences = await apiService.getExperiences();
        expect(experiences.length, equals(1));
      }
      
      // Next request should fail
      expect(
        () => apiService.getExperiences(),
        throwsA(isA<ServerException>()),
      );
    });

    test('Should handle large response payloads', () async {
      // Arrange
      // Generate a large response with 1000 experiences
      final largeResponseData = {
        'data': List.generate(1000, (index) => {
          'id': index.toString(),
          'title': 'Experience $index',
          'description': 'Description $index',
          'imageUrl': 'https://example.com/image$index.jpg',
          'price': 100.0 + index,
          'location': 'Location $index',
          'rating': 4.5,
          'reviewCount': 10 + index,
        })
      };
      
      when(mockClient.get(
        Uri.parse('https://api.example.com/experiences'),
        headers: anyNamed('headers'),
      )).thenAnswer((_) async {
        // Simulate network delay
        await Future.delayed(const Duration(milliseconds: 500));
        return http.Response(
          jsonEncode(largeResponseData),
          200,
        );
      });

      // Act
      final experiences = await apiService.getExperiences();
      
      // Assert
      expect(experiences.length, equals(1000));
      expect(experiences.first.id, equals('0'));
      expect(experiences.last.id, equals('999'));
    });
  });
}
