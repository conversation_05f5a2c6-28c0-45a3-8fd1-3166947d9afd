# CultureConnect Testing Documentation

This document provides an overview of the testing strategy and implementation for the CultureConnect application.

## Table of Contents

1. [Testing Strategy](#testing-strategy)
2. [Test Types](#test-types)
   - [Unit Tests](#unit-tests)
   - [Widget Tests](#widget-tests)
   - [Integration Tests](#integration-tests)
   - [Performance Tests](#performance-tests)
3. [Test Infrastructure](#test-infrastructure)
4. [Running Tests](#running-tests)
5. [Test Coverage](#test-coverage)
6. [Continuous Integration](#continuous-integration)
7. [Best Practices](#best-practices)

## Testing Strategy

The CultureConnect testing strategy follows a comprehensive approach that includes multiple levels of testing to ensure the application's quality, reliability, and performance. The testing pyramid is structured as follows:

- **Unit Tests**: Form the base of the pyramid with the highest number of tests, focusing on individual components.
- **Widget Tests**: Test UI components and their interactions.
- **Integration Tests**: Verify that different parts of the application work together correctly.
- **Performance Tests**: Ensure the application meets performance requirements.

## Test Types

### Unit Tests

Unit tests focus on testing individual components in isolation, such as services, providers, and models. They verify that each unit of code works as expected.

#### Services Tests

- **LocationService**: Tests location detection, distance calculations, route planning, and map style loading.
- **MapCacheManager**: Tests offline map caching, region management, and cache size calculations.
- **AuthService**: Tests authentication flows, user management, and token handling.
- **ARServices**: Tests AR-related functionality, including content management, accessibility features, and voice commands.

#### Models Tests

- **Experience**: Tests serialization/deserialization, validation, and model transformations.
- **Landmark**: Tests geolocation properties and data structure integrity.
- **User**: Tests user data validation and profile handling.

#### Providers Tests

- **ExperiencesProvider**: Tests experience loading, filtering, sorting, and recommendations.
- **FilterOptionsProvider**: Tests filter state management and option combinations.
- **LocationPermissionProvider**: Tests permission state handling and transitions.
- **MapStyleProvider**: Tests map style selection and persistence.

### Widget Tests

Widget tests verify that UI components render correctly and respond appropriately to user interactions.

#### Components Tests

- **ExperienceCard**: Tests rendering in different orientations, handling missing data, and user interactions.
- **CustomInfoWindow**: Tests marker info window display and interactions.
- **MapStyleSelector**: Tests style selection and visual feedback.

#### Screens Tests

- **MapViewScreen**: Tests map rendering, experience filtering, category selection, and map controls.
- **ExploreScreen**: Tests experience listing, filtering, and search functionality.
- **ARExploreScreen**: Tests AR view rendering and interactions.
- **SplashScreen**: Tests initialization flow and navigation.

### Integration Tests

Integration tests verify that different parts of the application work together correctly through end-to-end user flows.

#### User Flows

- **MapExplorationFlow**: Tests the complete map exploration experience, including searching, filtering, viewing details, and using map features.
- **ARExperienceFlow**: Tests the AR experience flow, including discovering and interacting with AR content.
- **AuthFlow**: Tests the authentication flow, including registration, login, and profile management.

### Performance Tests

Performance tests ensure the application meets performance requirements and provides a smooth user experience.

#### Performance Metrics

- **MapPerformance**: Tests map rendering, marker clustering, and interaction performance.
- **StartupPerformance**: Tests application startup time and initialization sequence.
- **ARPerformance**: Tests AR rendering performance, frame rate, and memory usage.

## Test Infrastructure

The CultureConnect testing infrastructure includes:

- **Test Fixtures**: Provides sample data for tests in the `test/fixtures` directory.
- **Test Helpers**: Provides utility functions for testing in the `test/utils` directory.
- **Mock Objects**: Uses Mockito to create mock implementations of dependencies.
- **Golden Tests**: Uses golden_toolkit for visual regression testing.

## Running Tests

Tests can be run using the provided `run_tests.sh` script, which organizes tests by category and provides detailed output.

```bash
# Run all tests
./test/run_tests.sh

# Run specific test categories
flutter test test/unit/
flutter test test/widget/
flutter test test/integration/
flutter test test/performance/
```

## Test Coverage

Test coverage is measured using the `test_coverage` package. The coverage report is generated in the `coverage/html` directory after running the tests.

```bash
# Generate coverage report
flutter pub run test_coverage
genhtml coverage/lcov.info -o coverage/html
```

## Continuous Integration

Tests are automatically run as part of the CI/CD pipeline to ensure code quality before deployment. The pipeline includes:

1. Running unit and widget tests
2. Running integration and performance tests on emulators/simulators
3. Generating and publishing test coverage reports
4. Notifying the team of test failures

## Best Practices

When writing tests for CultureConnect, follow these best practices:

1. **Isolation**: Each test should be independent and not rely on the state from other tests.
2. **Readability**: Use descriptive test names and follow the Arrange-Act-Assert pattern.
3. **Mocking**: Use mocks for external dependencies to ensure tests are fast and reliable.
4. **Coverage**: Aim for high test coverage, especially for critical functionality.
5. **Maintenance**: Keep tests up to date with code changes.
6. **Performance**: Keep tests fast to encourage frequent running.
7. **Edge Cases**: Test edge cases and error conditions, not just the happy path.
8. **Consistency**: Follow the established testing patterns and conventions.
