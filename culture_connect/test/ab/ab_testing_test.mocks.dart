// Mocks generated by <PERSON><PERSON><PERSON> 5.4.5 from annotations
// in culture_connect/test/ab/ab_testing_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i3;

import 'package:culture_connect/services/analytics_service.dart' as _i2;
import 'package:culture_connect/services/error_handling_service.dart' as _i4;
import 'package:mockito/mockito.dart' as _i1;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

/// A class which mocks [AnalyticsService].
///
/// See the documentation for <PERSON><PERSON><PERSON>'s code generation for more information.
class MockAnalyticsService extends _i1.Mock implements _i2.AnalyticsService {
  MockAnalyticsService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i3.Future<void> initialize() => (super.noSuchMethod(
        Invocation.method(
          #initialize,
          [],
        ),
        returnValue: _i3.Future<void>.value(),
        returnValueForMissingStub: _i3.Future<void>.value(),
      ) as _i3.Future<void>);

  @override
  _i3.Future<void> setAnalyticsEnabled(bool? enabled) => (super.noSuchMethod(
        Invocation.method(
          #setAnalyticsEnabled,
          [enabled],
        ),
        returnValue: _i3.Future<void>.value(),
        returnValueForMissingStub: _i3.Future<void>.value(),
      ) as _i3.Future<void>);

  @override
  _i3.Future<void> endSession() => (super.noSuchMethod(
        Invocation.method(
          #endSession,
          [],
        ),
        returnValue: _i3.Future<void>.value(),
        returnValueForMissingStub: _i3.Future<void>.value(),
      ) as _i3.Future<void>);

  @override
  _i3.Future<void> setUserProperty({
    required String? name,
    required String? value,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #setUserProperty,
          [],
          {
            #name: name,
            #value: value,
          },
        ),
        returnValue: _i3.Future<void>.value(),
        returnValueForMissingStub: _i3.Future<void>.value(),
      ) as _i3.Future<void>);

  @override
  _i3.Future<void> setUserId(String? userId) => (super.noSuchMethod(
        Invocation.method(
          #setUserId,
          [userId],
        ),
        returnValue: _i3.Future<void>.value(),
        returnValueForMissingStub: _i3.Future<void>.value(),
      ) as _i3.Future<void>);

  @override
  _i3.Future<void> logScreenView({
    required String? screenName,
    String? screenClass,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #logScreenView,
          [],
          {
            #screenName: screenName,
            #screenClass: screenClass,
          },
        ),
        returnValue: _i3.Future<void>.value(),
        returnValueForMissingStub: _i3.Future<void>.value(),
      ) as _i3.Future<void>);

  @override
  _i3.Future<void> logEvent({
    required String? name,
    required _i2.AnalyticsCategory? category,
    Map<String, dynamic>? parameters,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #logEvent,
          [],
          {
            #name: name,
            #category: category,
            #parameters: parameters,
          },
        ),
        returnValue: _i3.Future<void>.value(),
        returnValueForMissingStub: _i3.Future<void>.value(),
      ) as _i3.Future<void>);

  @override
  _i3.Future<void> logError({
    required String? errorMessage,
    required _i4.ErrorType? errorType,
    String? errorDetails,
    bool? fatal = false,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #logError,
          [],
          {
            #errorMessage: errorMessage,
            #errorType: errorType,
            #errorDetails: errorDetails,
            #fatal: fatal,
          },
        ),
        returnValue: _i3.Future<void>.value(),
        returnValueForMissingStub: _i3.Future<void>.value(),
      ) as _i3.Future<void>);

  @override
  _i3.Future<void> logPerformance({
    required String? name,
    required int? durationMillis,
    Map<String, dynamic>? parameters,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #logPerformance,
          [],
          {
            #name: name,
            #durationMillis: durationMillis,
            #parameters: parameters,
          },
        ),
        returnValue: _i3.Future<void>.value(),
        returnValueForMissingStub: _i3.Future<void>.value(),
      ) as _i3.Future<void>);

  @override
  _i3.Future<void> logSearch({
    required String? searchTerm,
    String? searchCategory,
    int? resultCount,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #logSearch,
          [],
          {
            #searchTerm: searchTerm,
            #searchCategory: searchCategory,
            #resultCount: resultCount,
          },
        ),
        returnValue: _i3.Future<void>.value(),
        returnValueForMissingStub: _i3.Future<void>.value(),
      ) as _i3.Future<void>);

  @override
  _i3.Future<void> logLogin({required String? method}) => (super.noSuchMethod(
        Invocation.method(
          #logLogin,
          [],
          {#method: method},
        ),
        returnValue: _i3.Future<void>.value(),
        returnValueForMissingStub: _i3.Future<void>.value(),
      ) as _i3.Future<void>);

  @override
  _i3.Future<void> logSignUp({required String? method}) => (super.noSuchMethod(
        Invocation.method(
          #logSignUp,
          [],
          {#method: method},
        ),
        returnValue: _i3.Future<void>.value(),
        returnValueForMissingStub: _i3.Future<void>.value(),
      ) as _i3.Future<void>);
}
