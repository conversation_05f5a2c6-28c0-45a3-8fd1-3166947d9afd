import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter/material.dart';
import 'ab_testing_framework.dart';
import 'package:culture_connect/services/analytics_service.dart';

@GenerateMocks([AnalyticsService])
void main() {
  late ABTestingService abTestingService;
  late MockAnalyticsService mockAnalyticsService;

  setUp(() async {
    // Set up SharedPreferences for testing
    SharedPreferences.setMockInitialValues({});
    
    // Create a new instance of ABTestingService for each test
    abTestingService = ABTestingService();
    
    // Reset all experiments before each test
    await abTestingService.resetAllExperiments();
    
    // Mock the analytics service
    mockAnalyticsService = MockAnalyticsService();
  });

  group('A/B Testing Service Tests', () {
    test('Should assign a variant consistently', () async {
      // Arrange
      const experimentId = 'button_color_test';
      final variants = ['red', 'blue', 'green'];
      
      // Act
      final variant1 = await abTestingService.getVariant(experimentId, variants);
      final variant2 = await abTestingService.getVariant(experimentId, variants);
      
      // Assert
      expect(variant1, isNotNull);
      expect(variant2, isNotNull);
      expect(variant1, equals(variant2), reason: 'Variant should be consistent across calls');
      expect(variants.contains(variant1), isTrue, reason: 'Assigned variant should be from the provided list');
    });

    test('Should respect weights when assigning variants', () async {
      // This test is probabilistic and may occasionally fail
      // We'll run multiple assignments and check the distribution
      
      // Arrange
      const experimentId = 'weighted_test';
      final variants = ['A', 'B'];
      final weights = [0.9, 0.1]; // 90% A, 10% B
      
      // Act
      final results = <String>[];
      for (int i = 0; i < 100; i++) {
        // Reset for each iteration to get a new assignment
        await abTestingService.resetAllExperiments();
        final variant = await abTestingService.getVariant(experimentId, variants, weights: weights);
        results.add(variant);
      }
      
      // Assert
      final countA = results.where((v) => v == 'A').length;
      
      // With 90% weight, we expect roughly 90 out of 100 to be A
      // Allow for some statistical variation (±20%)
      expect(countA, greaterThan(70), reason: 'Should have at least 70% A variants with 90% weight');
      expect(countA, lessThan(100), reason: 'Should have some B variants with 10% weight');
    });

    test('Should persist variant assignments across sessions', () async {
      // Arrange
      const experimentId = 'persistent_test';
      final variants = ['variant1', 'variant2'];
      
      // Act - First session
      final firstVariant = await abTestingService.getVariant(experimentId, variants);
      
      // Simulate app restart by creating a new service instance
      final newService = ABTestingService();
      
      // Act - Second session
      final secondVariant = await newService.getVariant(experimentId, variants);
      
      // Assert
      expect(secondVariant, equals(firstVariant), reason: 'Variant should persist across sessions');
    });

    test('Should log conversion events correctly', () async {
      // Arrange
      const experimentId = 'conversion_test';
      final variants = ['control', 'treatment'];
      
      // Replace the analytics service with our mock
      // This is a bit of a hack since we can't easily inject the analytics service
      // In a real app, we would use dependency injection
      final originalService = abTestingService._analyticsService;
      abTestingService._analyticsService = mockAnalyticsService;
      
      // Act
      final variant = await abTestingService.getVariant(experimentId, variants);
      await abTestingService.logConversion(experimentId, 'button_click');
      
      // Assert
      verify(mockAnalyticsService.logEvent(
        'ab_test_conversion',
        parameters: {
          'experiment_id': experimentId,
          'variant': variant,
          'conversion_event': 'button_click',
        },
      )).called(1);
      
      // Restore the original service
      abTestingService._analyticsService = originalService;
    });

    test('Should handle invalid experiment IDs gracefully', () async {
      // Arrange
      const experimentId = 'non_existent_test';
      
      // Replace the analytics service with our mock
      final originalService = abTestingService._analyticsService;
      abTestingService._analyticsService = mockAnalyticsService;
      
      // Act & Assert
      // Should not throw an exception
      await abTestingService.logConversion(experimentId, 'some_event');
      
      // Verify that no event was logged
      verifyNever(mockAnalyticsService.logEvent(any, parameters: anyNamed('parameters')));
      
      // Restore the original service
      abTestingService._analyticsService = originalService;
    });
  });

  group('A/B Test Widget Tests', () {
    testWidgets('Should render the assigned variant', (WidgetTester tester) async {
      // Arrange
      const experimentId = 'widget_test';
      final variants = {
        'A': (BuildContext context) => const Text('Variant A'),
        'B': (BuildContext context) => const Text('Variant B'),
      };
      
      // Force variant A for testing
      await abTestingService.resetAllExperiments();
      await abTestingService.getVariant(experimentId, ['A', 'B']);
      
      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ABTestWidget(
              experimentId: experimentId,
              variants: variants,
            ),
          ),
        ),
      );
      
      // Wait for the widget to determine the variant
      await tester.pumpAndSettle();
      
      // Assert
      // One of the variants should be rendered
      expect(find.text('Variant A'), findsOneWidget);
    });

    testWidgets('Should show loading widget while determining variant', (WidgetTester tester) async {
      // Arrange
      const experimentId = 'loading_test';
      final variants = {
        'A': (BuildContext context) => const Text('Variant A'),
        'B': (BuildContext context) => const Text('Variant B'),
      };
      
      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ABTestWidget(
              experimentId: experimentId,
              variants: variants,
              loadingWidget: const Text('Loading...'),
            ),
          ),
        ),
      );
      
      // Assert - Initially should show loading widget
      expect(find.text('Loading...'), findsOneWidget);
      
      // Wait for the widget to determine the variant
      await tester.pumpAndSettle();
      
      // Assert - After loading, should show one of the variants
      expect(find.text('Loading...'), findsNothing);
      expect(find.text('Variant A'), findsOneWidget);
    });
  });
}
