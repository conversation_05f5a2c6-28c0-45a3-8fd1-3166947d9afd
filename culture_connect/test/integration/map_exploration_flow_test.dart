import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:culture_connect/main.dart' as app;
import 'package:culture_connect/services/location_service.dart';
import 'package:culture_connect/services/map_cache_manager.dart';
import 'package:culture_connect/providers/location_permission_provider.dart';
import 'package:culture_connect/models/experience.dart';
import 'package:culture_connect/providers/experiences_provider.dart';

// Generate mocks for dependencies
@GenerateMocks([LocationService, MapCacheManager])
import 'map_exploration_flow_test.mocks.dart';

// Mock providers
final mockLocationServiceProvider = Provider<LocationService>((ref) {
  return MockLocationService();
});

final mockMapCacheManagerProvider = Provider<MapCacheManager>((ref) {
  return MockMapCacheManager();
});

final mockLocationPermissionProvider = StateProvider<LocationPermissionState>((ref) {
  return LocationPermissionState.granted;
});

// Sample experiences for testing
final sampleExperiences = [
  Experience(
    id: '1',
    title: 'Cultural Tour',
    description: 'Explore local culture',
    imageUrl: 'https://example.com/image1.jpg',
    category: 'Cultural Tours',
    location: 'San Francisco, CA',
    price: 99.99,
    rating: 4.5,
    reviewCount: 100,
    latitude: 37.7749,
    longitude: -122.4194,
  ),
  Experience(
    id: '2',
    title: 'Food Tour',
    description: 'Taste local cuisine',
    imageUrl: 'https://example.com/image2.jpg',
    category: 'Food & Drink',
    location: 'San Francisco, CA',
    price: 79.99,
    rating: 4.8,
    reviewCount: 150,
    latitude: 37.7750,
    longitude: -122.4195,
  ),
  Experience(
    id: '3',
    title: 'Adventure Tour',
    description: 'Outdoor activities',
    imageUrl: 'https://example.com/image3.jpg',
    category: 'Adventure',
    location: 'San Francisco, CA',
    price: 129.99,
    rating: 4.2,
    reviewCount: 80,
    latitude: 37.7751,
    longitude: -122.4196,
  ),
];

final mockExperiencesProvider = Provider<AsyncValue<List<Experience>>>((ref) {
  return AsyncValue.data(sampleExperiences);
});

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  late MockLocationService mockLocationService;
  late MockMapCacheManager mockMapCacheManager;

  setUp(() {
    mockLocationService = MockLocationService();
    mockMapCacheManager = MockMapCacheManager();
    
    // Setup mock location service
    when(mockLocationService.getCurrentPosition()).thenAnswer((_) async => null);
    when(mockLocationService.calculateDistance(any, any, any, any)).thenReturn(1000.0);
    when(mockLocationService.formatDistance(any)).thenReturn('1.0 km');
    when(mockLocationService.loadMapStyle(any)).thenAnswer((_) async => '[]');
    
    // Setup mock map cache manager
    when(mockMapCacheManager.getCachedRegions()).thenAnswer((_) async => []);
    when(mockMapCacheManager.getCacheSize()).thenAnswer((_) async => 0);
  });

  testWidgets('Complete map exploration flow test', (WidgetTester tester) async {
    // Override the providers
    app.main();
    await tester.pumpAndSettle();
    
    // Should show splash screen initially
    expect(find.text('CultureConnect'), findsOneWidget);
    
    // Wait for splash screen animation and initialization
    await tester.pump(const Duration(seconds: 3));
    await tester.pumpAndSettle();
    
    // Navigate to the Map tab
    final mapTab = find.text('Map');
    expect(mapTab, findsOneWidget);
    await tester.tap(mapTab);
    await tester.pumpAndSettle();
    
    // Should show the Map screen
    expect(find.text('Map'), findsOneWidget);
    
    // Search for experiences
    final searchField = find.byType(TextField);
    expect(searchField, findsOneWidget);
    await tester.enterText(searchField, 'food');
    await tester.pumpAndSettle();
    
    // Should filter experiences
    expect(find.text('Food Tour'), findsOneWidget);
    expect(find.text('Cultural Tour'), findsNothing);
    expect(find.text('Adventure Tour'), findsNothing);
    
    // Clear search
    await tester.enterText(searchField, '');
    await tester.pumpAndSettle();
    
    // Should show all experiences again
    expect(find.text('Food Tour'), findsOneWidget);
    expect(find.text('Cultural Tour'), findsOneWidget);
    expect(find.text('Adventure Tour'), findsOneWidget);
    
    // Filter by category
    final categoryFilter = find.text('Food & Drink');
    expect(categoryFilter, findsOneWidget);
    await tester.tap(categoryFilter);
    await tester.pumpAndSettle();
    
    // Should filter experiences by category
    expect(find.text('Food Tour'), findsOneWidget);
    expect(find.text('Cultural Tour'), findsNothing);
    expect(find.text('Adventure Tour'), findsNothing);
    
    // Reset category filter
    final allCategory = find.text('All');
    expect(allCategory, findsOneWidget);
    await tester.tap(allCategory);
    await tester.pumpAndSettle();
    
    // Should show all experiences again
    expect(find.text('Food Tour'), findsOneWidget);
    expect(find.text('Cultural Tour'), findsOneWidget);
    expect(find.text('Adventure Tour'), findsOneWidget);
    
    // Tap on an experience card
    final experienceCard = find.text('Food Tour');
    expect(experienceCard, findsOneWidget);
    await tester.tap(experienceCard);
    await tester.pumpAndSettle();
    
    // Should navigate to experience details
    expect(find.text('Food Tour'), findsOneWidget);
    expect(find.text('Taste local cuisine'), findsOneWidget);
    
    // Go back to map
    final backButton = find.byType(BackButton);
    expect(backButton, findsOneWidget);
    await tester.tap(backButton);
    await tester.pumpAndSettle();
    
    // Should be back on the map screen
    expect(find.text('Map'), findsOneWidget);
    
    // Test map controls
    final locationButton = find.byIcon(Icons.my_location);
    expect(locationButton, findsOneWidget);
    await tester.tap(locationButton);
    await tester.pumpAndSettle();
    
    // Test route planning
    final routeButton = find.byIcon(Icons.directions);
    expect(routeButton, findsOneWidget);
    await tester.tap(routeButton);
    await tester.pumpAndSettle();
    
    // Test map style
    final styleButton = find.byIcon(Icons.layers);
    expect(styleButton, findsOneWidget);
    await tester.tap(styleButton);
    await tester.pumpAndSettle();
    
    // Should show map style options
    expect(find.text('Map Style'), findsOneWidget);
    
    // Select a map style
    final silverStyle = find.text('Silver');
    expect(silverStyle, findsOneWidget);
    await tester.tap(silverStyle);
    await tester.pumpAndSettle();
    
    // Test offline maps
    final offlineButton = find.byIcon(Icons.download);
    expect(offlineButton, findsOneWidget);
    await tester.tap(offlineButton);
    await tester.pumpAndSettle();
    
    // Should show offline map options
    expect(find.text('Offline Maps'), findsOneWidget);
    
    // Close the offline maps dialog
    final closeButton = find.byType(BackButton);
    expect(closeButton, findsOneWidget);
    await tester.tap(closeButton);
    await tester.pumpAndSettle();
    
    // Navigate to the Explore tab
    final exploreTab = find.text('Explore');
    expect(exploreTab, findsOneWidget);
    await tester.tap(exploreTab);
    await tester.pumpAndSettle();
    
    // Should show the Explore screen
    expect(find.text('Explore'), findsOneWidget);
    
    // Navigate back to the Map tab
    await tester.tap(mapTab);
    await tester.pumpAndSettle();
    
    // Should show the Map screen again
    expect(find.text('Map'), findsOneWidget);
  });
}
