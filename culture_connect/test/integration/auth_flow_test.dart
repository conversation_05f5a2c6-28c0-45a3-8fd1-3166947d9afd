import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:culture_connect/main.dart' as app;
import 'package:culture_connect/services/auth_service.dart';
import 'package:culture_connect/providers/auth_provider.dart';
import 'package:culture_connect/models/user_model.dart';

// Generate mocks for dependencies
@GenerateMocks([AuthService])
import 'auth_flow_test.mocks.dart';

// Mock auth service provider
final mockAuthServiceProvider = Provider<AuthService>((ref) {
  return MockAuthService();
});

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  late MockAuthService mockAuthService;

  setUp(() {
    mockAuthService = MockAuthService();
    
    // Setup mock auth service
    when(mockAuthService.getCurrentUser()).thenAnswer((_) async => null);
    when(mockAuthService.signInWithEmailAndPassword(
      email: anyNamed('email'),
      password: anyNamed('password'),
    )).thenAnswer((_) async => UserModel(
      uid: 'test-uid',
      email: '<EMAIL>',
      displayName: 'Test User',
      photoUrl: 'https://example.com/photo.jpg',
      isVerified: true,
    ));
    when(mockAuthService.registerWithEmailAndPassword(
      email: anyNamed('email'),
      password: anyNamed('password'),
      fullName: anyNamed('fullName'),
    )).thenAnswer((_) async => UserModel(
      uid: 'new-uid',
      email: '<EMAIL>',
      displayName: 'New User',
      photoUrl: null,
      isVerified: false,
    ));
    when(mockAuthService.signOut()).thenAnswer((_) async => null);
  });

  testWidgets('Complete authentication flow test', (WidgetTester tester) async {
    // Override the auth service provider
    app.main();
    await tester.pumpAndSettle();
    
    // Should show splash screen initially
    expect(find.text('CultureConnect'), findsOneWidget);
    
    // Wait for splash screen animation and initialization
    await tester.pump(const Duration(seconds: 3));
    await tester.pumpAndSettle();
    
    // Should navigate to onboarding screen
    expect(find.text('Welcome to CultureConnect'), findsOneWidget);
    
    // Swipe through onboarding screens
    await tester.drag(find.byType(PageView), const Offset(-500, 0));
    await tester.pumpAndSettle();
    
    await tester.drag(find.byType(PageView), const Offset(-500, 0));
    await tester.pumpAndSettle();
    
    // Find and tap the "Get Started" button
    final getStartedButton = find.text('Get Started');
    expect(getStartedButton, findsOneWidget);
    await tester.tap(getStartedButton);
    await tester.pumpAndSettle();
    
    // Should navigate to login screen
    expect(find.text('Sign In'), findsOneWidget);
    
    // Find and tap the "Create Account" button
    final createAccountButton = find.text('Create Account');
    expect(createAccountButton, findsOneWidget);
    await tester.tap(createAccountButton);
    await tester.pumpAndSettle();
    
    // Should navigate to registration screen
    expect(find.text('Create Account'), findsOneWidget);
    
    // Fill in registration form
    await tester.enterText(find.byType(TextFormField).at(0), 'New User');
    await tester.enterText(find.byType(TextFormField).at(1), '<EMAIL>');
    await tester.enterText(find.byType(TextFormField).at(2), 'password123');
    await tester.enterText(find.byType(TextFormField).at(3), 'password123');
    
    // Tap the "Next" button
    final nextButton = find.text('Next');
    expect(nextButton, findsOneWidget);
    await tester.tap(nextButton);
    await tester.pumpAndSettle();
    
    // Should navigate to additional info screen
    expect(find.text('Tell us about yourself'), findsOneWidget);
    
    // Select interests
    await tester.tap(find.text('History').first);
    await tester.tap(find.text('Art').first);
    await tester.pumpAndSettle();
    
    // Tap the "Create Account" button
    final registerButton = find.text('Create Account');
    expect(registerButton, findsOneWidget);
    await tester.tap(registerButton);
    await tester.pumpAndSettle();
    
    // Should navigate to verification screen
    expect(find.text('Verify Your Email'), findsOneWidget);
    
    // Tap the "I'll verify later" button
    final verifyLaterButton = find.text('I\'ll verify later');
    expect(verifyLaterButton, findsOneWidget);
    await tester.tap(verifyLaterButton);
    await tester.pumpAndSettle();
    
    // Should navigate to main navigation
    expect(find.text('Explore'), findsOneWidget);
    
    // Tap the profile tab
    await tester.tap(find.text('Profile'));
    await tester.pumpAndSettle();
    
    // Should show profile screen
    expect(find.text('New User'), findsOneWidget);
    
    // Tap the logout button
    final logoutButton = find.text('Log Out');
    expect(logoutButton, findsOneWidget);
    await tester.tap(logoutButton);
    await tester.pumpAndSettle();
    
    // Confirm logout
    final confirmButton = find.text('Yes');
    expect(confirmButton, findsOneWidget);
    await tester.tap(confirmButton);
    await tester.pumpAndSettle();
    
    // Should navigate back to login screen
    expect(find.text('Sign In'), findsOneWidget);
    
    // Fill in login form
    await tester.enterText(find.byType(TextFormField).at(0), '<EMAIL>');
    await tester.enterText(find.byType(TextFormField).at(1), 'password123');
    
    // Tap the "Sign In" button
    final signInButton = find.text('Sign In');
    expect(signInButton, findsOneWidget);
    await tester.tap(signInButton);
    await tester.pumpAndSettle();
    
    // Should navigate to main navigation
    expect(find.text('Explore'), findsOneWidget);
  });
}
