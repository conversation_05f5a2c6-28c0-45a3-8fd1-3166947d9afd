import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:culture_connect/main.dart' as app;
import 'package:culture_connect/models/message_model.dart';
import 'package:culture_connect/models/group_chat_model.dart';
import 'package:culture_connect/models/user_model.dart';
import 'package:culture_connect/models/translation/language_model.dart';
import 'package:culture_connect/models/translation/group_translation_model.dart';
import 'package:culture_connect/providers/auth_provider.dart';
import 'package:culture_connect/providers/group_chat_provider.dart';
import 'package:culture_connect/providers/group_translation_provider.dart';
import 'package:culture_connect/screens/messaging/group_chat_screen.dart';
import 'package:culture_connect/screens/messaging/group_translation_settings_screen.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();
  
  // Sample data for testing
  final sampleGroupId = 'group-123';
  final sampleUserId = 'user-456';
  
  final sampleUser = UserModel(
    uid: sampleUserId,
    email: '<EMAIL>',
    displayName: 'Test User',
  );
  
  final sampleGroupChat = GroupChatModel(
    id: sampleGroupId,
    name: 'Test Group',
    description: 'A test group for integration testing',
    participants: [sampleUserId, 'user-789'],
    createdBy: 'user-789',
    createdAt: DateTime.now().subtract(const Duration(days: 7)),
    lastMessageAt: DateTime.now(),
    lastMessageText: 'Hello, world!',
    lastMessageSenderId: 'user-789',
    unreadCount: 0,
    isActive: true,
  );
  
  final sampleMessages = [
    MessageModel(
      id: 'message-1',
      chatId: sampleGroupId,
      senderId: 'user-789',
      recipientId: '',
      text: 'Hello, how are you?',
      timestamp: DateTime.now().subtract(const Duration(minutes: 5)),
      status: MessageStatus.sent,
      type: MessageType.text,
      originalLanguage: 'en',
    ),
    MessageModel(
      id: 'message-2',
      chatId: sampleGroupId,
      senderId: sampleUserId,
      recipientId: '',
      text: 'I am fine, thank you!',
      timestamp: DateTime.now().subtract(const Duration(minutes: 3)),
      status: MessageStatus.sent,
      type: MessageType.text,
      originalLanguage: 'en',
    ),
    MessageModel(
      id: 'message-3',
      chatId: sampleGroupId,
      senderId: 'user-789',
      recipientId: '',
      text: 'Bonjour, comment ça va?',
      timestamp: DateTime.now().subtract(const Duration(minutes: 1)),
      status: MessageStatus.sent,
      type: MessageType.text,
      originalLanguage: 'fr',
    ),
  ];
  
  final sampleGroupMembers = {
    sampleUserId: sampleUser,
    'user-789': UserModel(
      uid: 'user-789',
      email: '<EMAIL>',
      displayName: 'Other User',
    ),
  };
  
  final sampleParticipantPreference = ParticipantLanguagePreference(
    userId: sampleUserId,
    displayName: 'Test User',
    preferredLanguage: LanguageModel(code: 'en', name: 'English'),
    autoTranslate: true,
    showOriginalText: false,
  );
  
  final sampleGroupSettings = GroupTranslationSettings(
    groupId: sampleGroupId,
    participantPreferences: {
      sampleUserId: sampleParticipantPreference,
    },
    enableRealTimeTranslation: true,
  );

  testWidgets('Complete group translation flow test', (WidgetTester tester) async {
    // Create a ProviderContainer with overrides for testing
    final container = ProviderContainer(
      overrides: [
        // Override auth provider
        currentUserProvider.overrideWith((ref) => AsyncValue.data(sampleUser)),
        
        // Override group chat providers
        groupChatProvider(sampleGroupId).overrideWith((ref) => 
          AsyncValue.data(sampleGroupChat)),
        
        groupChatMessagesProvider(sampleGroupId).overrideWith((ref) => 
          AsyncValue.data(sampleMessages)),
        
        groupMembersProvider(sampleGroupId).overrideWith((ref) => 
          AsyncValue.data(sampleGroupMembers)),
        
        // Override group translation providers
        groupTranslationSettingsProvider(sampleGroupId).overrideWith((ref) => 
          Future.value(sampleGroupSettings)),
        
        translationMetadataForUserProvider((
          message: any,
          userId: sampleUserId,
          groupId: sampleGroupId,
        )).overrideWith((ref) => Future.value(
          MessageTranslationMetadata(
            messageId: 'message-3',
            originalText: 'Bonjour, comment ça va?',
            translatedText: 'Hello, how are you?',
            sourceLanguage: 'fr',
            targetLanguage: 'en',
            confidence: 0.95,
            culturalContext: null,
            slangIdiom: null,
            pronunciation: null,
          )
        )),
        
        translatedTextForUserProvider((
          message: any,
          userId: sampleUserId,
          groupId: sampleGroupId,
        )).overrideWith((ref) {
          final message = ref.watch(translatedTextForUserProvider.argument).message;
          if (message.id == 'message-3') {
            return Future.value('Hello, how are you?');
          }
          return Future.value(message.text);
        }),
        
        participantLanguagePreferenceProvider((
          groupId: sampleGroupId,
          userId: sampleUserId,
        )).overrideWith((ref) => Future.value(sampleParticipantPreference)),
      ],
    );
    
    // Build the app with the overridden providers
    await tester.pumpWidget(
      UncontrolledProviderScope(
        container: container,
        child: MaterialApp(
          home: GroupChatScreen(groupId: sampleGroupId),
          routes: {
            '/group_translation_settings': (context) => 
              GroupTranslationSettingsScreen(groupId: sampleGroupId),
          },
        ),
      ),
    );
    
    // Wait for the screen to load
    await tester.pumpAndSettle();
    
    // Verify the group chat screen is displayed
    expect(find.text('Test Group'), findsOneWidget);
    
    // Verify messages are displayed
    expect(find.text('Hello, how are you?'), findsAtLeastNWidgets(1));
    expect(find.text('I am fine, thank you!'), findsOneWidget);
    
    // Verify the French message is translated to English
    expect(find.text('Translated from French'), findsOneWidget);
    
    // Tap the translation settings button
    await tester.tap(find.byIcon(Icons.translate));
    await tester.pumpAndSettle();
    
    // Verify the translation settings screen is displayed
    expect(find.text('Translation Settings'), findsOneWidget);
    
    // Verify user language preferences are displayed
    expect(find.text('Your Language Preferences'), findsOneWidget);
    expect(find.text('Preferred Language'), findsOneWidget);
    
    // Verify group translation settings are displayed
    expect(find.text('Group Translation Settings'), findsOneWidget);
    expect(find.text('Auto-Detect Languages'), findsOneWidget);
    expect(find.text('Real-Time Translation'), findsOneWidget);
    
    // Toggle show original text
    await tester.tap(find.text('Show Original Text').last);
    await tester.pumpAndSettle();
    
    // Go back to the group chat screen
    Navigator.pop(tester.element(find.byType(GroupTranslationSettingsScreen)));
    await tester.pumpAndSettle();
    
    // Verify we're back on the group chat screen
    expect(find.text('Test Group'), findsOneWidget);
    
    // Send a new message
    await tester.enterText(find.byType(TextField), 'This is a test message');
    await tester.tap(find.byIcon(Icons.send));
    await tester.pumpAndSettle();
    
    // Verify the message was sent
    expect(find.text('This is a test message'), findsOneWidget);
  });
}
