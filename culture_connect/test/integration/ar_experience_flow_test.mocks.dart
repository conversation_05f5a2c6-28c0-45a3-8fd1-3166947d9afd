// Mocks generated by <PERSON><PERSON><PERSON> 5.4.5 from annotations
// in culture_connect/test/integration/ar_experience_flow_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i4;
import 'dart:io' as _i9;
import 'dart:ui' as _i13;

import 'package:culture_connect/models/ar_model.dart' as _i8;
import 'package:culture_connect/models/landmark.dart' as _i7;
import 'package:culture_connect/models/user_model.dart' as _i5;
import 'package:culture_connect/services/ar_backend_service.dart' as _i6;
import 'package:culture_connect/services/ar_recording_service.dart' as _i12;
import 'package:culture_connect/services/ar_voice_command_service.dart' as _i10;
import 'package:culture_connect/services/auth_service.dart' as _i3;
import 'package:firebase_auth/firebase_auth.dart' as _i2;
import 'package:flutter/foundation.dart' as _i14;
import 'package:mockito/mockito.dart' as _i1;
import 'package:mockito/src/dummies.dart' as _i11;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeUserCredential_0 extends _i1.SmartFake
    implements _i2.UserCredential {
  _FakeUserCredential_0(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

/// A class which mocks [AuthService].
///
/// See the documentation for Mockito's code generation for more information.
class MockAuthService extends _i1.Mock implements _i3.AuthService {
  MockAuthService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i4.Stream<_i3.AuthStatus> get authStateChanges => (super.noSuchMethod(
        Invocation.getter(#authStateChanges),
        returnValue: _i4.Stream<_i3.AuthStatus>.empty(),
      ) as _i4.Stream<_i3.AuthStatus>);

  @override
  _i4.Future<_i5.UserModel?> get currentUserModel => (super.noSuchMethod(
        Invocation.getter(#currentUserModel),
        returnValue: _i4.Future<_i5.UserModel?>.value(),
      ) as _i4.Future<_i5.UserModel?>);

  @override
  _i4.Future<_i2.UserCredential> registerWithEmailAndPassword({
    required String? email,
    required String? password,
    required String? firstName,
    required String? lastName,
    required String? phoneNumber,
    required String? dateOfBirth,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #registerWithEmailAndPassword,
          [],
          {
            #email: email,
            #password: password,
            #firstName: firstName,
            #lastName: lastName,
            #phoneNumber: phoneNumber,
            #dateOfBirth: dateOfBirth,
          },
        ),
        returnValue: _i4.Future<_i2.UserCredential>.value(_FakeUserCredential_0(
          this,
          Invocation.method(
            #registerWithEmailAndPassword,
            [],
            {
              #email: email,
              #password: password,
              #firstName: firstName,
              #lastName: lastName,
              #phoneNumber: phoneNumber,
              #dateOfBirth: dateOfBirth,
            },
          ),
        )),
      ) as _i4.Future<_i2.UserCredential>);

  @override
  _i4.Future<_i2.UserCredential> loginWithEmailAndPassword({
    required String? email,
    required String? password,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #loginWithEmailAndPassword,
          [],
          {
            #email: email,
            #password: password,
          },
        ),
        returnValue: _i4.Future<_i2.UserCredential>.value(_FakeUserCredential_0(
          this,
          Invocation.method(
            #loginWithEmailAndPassword,
            [],
            {
              #email: email,
              #password: password,
            },
          ),
        )),
      ) as _i4.Future<_i2.UserCredential>);

  @override
  _i4.Future<_i2.UserCredential?> signInWithGoogle() => (super.noSuchMethod(
        Invocation.method(
          #signInWithGoogle,
          [],
        ),
        returnValue: _i4.Future<_i2.UserCredential?>.value(),
      ) as _i4.Future<_i2.UserCredential?>);

  @override
  _i4.Future<void> signOut() => (super.noSuchMethod(
        Invocation.method(
          #signOut,
          [],
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);

  @override
  _i4.Future<void> sendPasswordResetEmail(String? email) => (super.noSuchMethod(
        Invocation.method(
          #sendPasswordResetEmail,
          [email],
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);

  @override
  _i4.Future<bool> verifyEmail() => (super.noSuchMethod(
        Invocation.method(
          #verifyEmail,
          [],
        ),
        returnValue: _i4.Future<bool>.value(false),
      ) as _i4.Future<bool>);

  @override
  _i4.Future<void> resendVerificationEmail() => (super.noSuchMethod(
        Invocation.method(
          #resendVerificationEmail,
          [],
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);

  @override
  _i4.Future<bool> isBiometricAvailable() => (super.noSuchMethod(
        Invocation.method(
          #isBiometricAvailable,
          [],
        ),
        returnValue: _i4.Future<bool>.value(false),
      ) as _i4.Future<bool>);

  @override
  _i4.Future<bool> authenticateWithBiometrics() => (super.noSuchMethod(
        Invocation.method(
          #authenticateWithBiometrics,
          [],
        ),
        returnValue: _i4.Future<bool>.value(false),
      ) as _i4.Future<bool>);
}

/// A class which mocks [ARBackendService].
///
/// See the documentation for Mockito's code generation for more information.
class MockARBackendService extends _i1.Mock implements _i6.ARBackendService {
  MockARBackendService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i4.Future<void> initialize() => (super.noSuchMethod(
        Invocation.method(
          #initialize,
          [],
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);

  @override
  _i4.Future<List<_i7.Landmark>> getLandmarks({
    double? latitude,
    double? longitude,
    double? radius,
    List<String>? tags,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #getLandmarks,
          [],
          {
            #latitude: latitude,
            #longitude: longitude,
            #radius: radius,
            #tags: tags,
          },
        ),
        returnValue: _i4.Future<List<_i7.Landmark>>.value(<_i7.Landmark>[]),
      ) as _i4.Future<List<_i7.Landmark>>);

  @override
  _i4.Future<_i8.ARModel?> getARModel(String? modelId) => (super.noSuchMethod(
        Invocation.method(
          #getARModel,
          [modelId],
        ),
        returnValue: _i4.Future<_i8.ARModel?>.value(),
      ) as _i4.Future<_i8.ARModel?>);

  @override
  _i4.Future<_i9.File?> downloadARModelFile(String? modelUrl) =>
      (super.noSuchMethod(
        Invocation.method(
          #downloadARModelFile,
          [modelUrl],
        ),
        returnValue: _i4.Future<_i9.File?>.value(),
      ) as _i4.Future<_i9.File?>);

  @override
  _i4.Future<bool> uploadUserARContent({
    required String? name,
    required String? description,
    required _i9.File? modelFile,
    required _i9.File? imageFile,
    List<_i9.File>? textureFiles,
    Map<String, dynamic>? metadata,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #uploadUserARContent,
          [],
          {
            #name: name,
            #description: description,
            #modelFile: modelFile,
            #imageFile: imageFile,
            #textureFiles: textureFiles,
            #metadata: metadata,
          },
        ),
        returnValue: _i4.Future<bool>.value(false),
      ) as _i4.Future<bool>);

  @override
  _i4.Future<bool> shareARContent({
    required String? contentId,
    required List<String>? recipientIds,
    String? message,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #shareARContent,
          [],
          {
            #contentId: contentId,
            #recipientIds: recipientIds,
            #message: message,
          },
        ),
        returnValue: _i4.Future<bool>.value(false),
      ) as _i4.Future<bool>);
}

/// A class which mocks [ARVoiceCommandService].
///
/// See the documentation for Mockito's code generation for more information.
class MockARVoiceCommandService extends _i1.Mock
    implements _i10.ARVoiceCommandService {
  MockARVoiceCommandService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  bool get isInitialized => (super.noSuchMethod(
        Invocation.getter(#isInitialized),
        returnValue: false,
      ) as bool);

  @override
  bool get isListening => (super.noSuchMethod(
        Invocation.getter(#isListening),
        returnValue: false,
      ) as bool);

  @override
  String get lastRecognizedWords => (super.noSuchMethod(
        Invocation.getter(#lastRecognizedWords),
        returnValue: _i11.dummyValue<String>(
          this,
          Invocation.getter(#lastRecognizedWords),
        ),
      ) as String);

  @override
  double get confidence => (super.noSuchMethod(
        Invocation.getter(#confidence),
        returnValue: 0.0,
      ) as double);

  @override
  List<String> get commandHistory => (super.noSuchMethod(
        Invocation.getter(#commandHistory),
        returnValue: <String>[],
      ) as List<String>);

  @override
  _i4.Future<bool> initialize() => (super.noSuchMethod(
        Invocation.method(
          #initialize,
          [],
        ),
        returnValue: _i4.Future<bool>.value(false),
      ) as _i4.Future<bool>);

  @override
  void registerCommand(
    String? command,
    Function? handler,
  ) =>
      super.noSuchMethod(
        Invocation.method(
          #registerCommand,
          [
            command,
            handler,
          ],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void registerCommands(Map<String, Function>? handlers) => super.noSuchMethod(
        Invocation.method(
          #registerCommands,
          [handlers],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void unregisterCommand(String? command) => super.noSuchMethod(
        Invocation.method(
          #unregisterCommand,
          [command],
        ),
        returnValueForMissingStub: null,
      );

  @override
  _i4.Future<bool> startListening() => (super.noSuchMethod(
        Invocation.method(
          #startListening,
          [],
        ),
        returnValue: _i4.Future<bool>.value(false),
      ) as _i4.Future<bool>);

  @override
  _i4.Future<bool> stopListening() => (super.noSuchMethod(
        Invocation.method(
          #stopListening,
          [],
        ),
        returnValue: _i4.Future<bool>.value(false),
      ) as _i4.Future<bool>);

  @override
  _i4.Future<bool> cancelListening() => (super.noSuchMethod(
        Invocation.method(
          #cancelListening,
          [],
        ),
        returnValue: _i4.Future<bool>.value(false),
      ) as _i4.Future<bool>);

  @override
  void addRecognitionListener(dynamic Function(String)? listener) =>
      super.noSuchMethod(
        Invocation.method(
          #addRecognitionListener,
          [listener],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void removeRecognitionListener(dynamic Function(String)? listener) =>
      super.noSuchMethod(
        Invocation.method(
          #removeRecognitionListener,
          [listener],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void addListeningStateListener(dynamic Function(bool)? listener) =>
      super.noSuchMethod(
        Invocation.method(
          #addListeningStateListener,
          [listener],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void removeListeningStateListener(dynamic Function(bool)? listener) =>
      super.noSuchMethod(
        Invocation.method(
          #removeListeningStateListener,
          [listener],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void addCommandExecutedListener(dynamic Function(String)? listener) =>
      super.noSuchMethod(
        Invocation.method(
          #addCommandExecutedListener,
          [listener],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void removeCommandExecutedListener(dynamic Function(String)? listener) =>
      super.noSuchMethod(
        Invocation.method(
          #removeCommandExecutedListener,
          [listener],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void dispose() => super.noSuchMethod(
        Invocation.method(
          #dispose,
          [],
        ),
        returnValueForMissingStub: null,
      );
}

/// A class which mocks [ARRecordingService].
///
/// See the documentation for Mockito's code generation for more information.
class MockARRecordingService extends _i1.Mock
    implements _i12.ARRecordingService {
  MockARRecordingService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  int get recordingDurationInSeconds => (super.noSuchMethod(
        Invocation.getter(#recordingDurationInSeconds),
        returnValue: 0,
      ) as int);

  @override
  String get recordingDurationFormatted => (super.noSuchMethod(
        Invocation.getter(#recordingDurationFormatted),
        returnValue: _i11.dummyValue<String>(
          this,
          Invocation.getter(#recordingDurationFormatted),
        ),
      ) as String);

  @override
  bool get isRecording => (super.noSuchMethod(
        Invocation.getter(#isRecording),
        returnValue: false,
      ) as bool);

  @override
  bool get isPaused => (super.noSuchMethod(
        Invocation.getter(#isPaused),
        returnValue: false,
      ) as bool);

  @override
  List<_i9.File> get screenshots => (super.noSuchMethod(
        Invocation.getter(#screenshots),
        returnValue: <_i9.File>[],
      ) as List<_i9.File>);

  @override
  _i4.Future<void> initialize({
    _i13.VoidCallback? onRecordingStateChanged,
    _i14.ValueChanged<int>? onRecordingTimerUpdated,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #initialize,
          [],
          {
            #onRecordingStateChanged: onRecordingStateChanged,
            #onRecordingTimerUpdated: onRecordingTimerUpdated,
          },
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);

  @override
  _i4.Future<bool> startRecording() => (super.noSuchMethod(
        Invocation.method(
          #startRecording,
          [],
        ),
        returnValue: _i4.Future<bool>.value(false),
      ) as _i4.Future<bool>);

  @override
  void pauseRecording() => super.noSuchMethod(
        Invocation.method(
          #pauseRecording,
          [],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void resumeRecording() => super.noSuchMethod(
        Invocation.method(
          #resumeRecording,
          [],
        ),
        returnValueForMissingStub: null,
      );

  @override
  _i4.Future<bool> stopRecording() => (super.noSuchMethod(
        Invocation.method(
          #stopRecording,
          [],
        ),
        returnValue: _i4.Future<bool>.value(false),
      ) as _i4.Future<bool>);

  @override
  _i4.Future<_i9.File?> takeScreenshot() => (super.noSuchMethod(
        Invocation.method(
          #takeScreenshot,
          [],
        ),
        returnValue: _i4.Future<_i9.File?>.value(),
      ) as _i4.Future<_i9.File?>);

  @override
  _i4.Future<_i9.File?> recordVideo() => (super.noSuchMethod(
        Invocation.method(
          #recordVideo,
          [],
        ),
        returnValue: _i4.Future<_i9.File?>.value(),
      ) as _i4.Future<_i9.File?>);

  @override
  _i4.Future<bool> shareRecording({
    required String? title,
    required String? description,
    List<String>? tags,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #shareRecording,
          [],
          {
            #title: title,
            #description: description,
            #tags: tags,
          },
        ),
        returnValue: _i4.Future<bool>.value(false),
      ) as _i4.Future<bool>);

  @override
  _i4.Future<bool> saveRecording({
    required String? title,
    required String? description,
    List<String>? tags,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #saveRecording,
          [],
          {
            #title: title,
            #description: description,
            #tags: tags,
          },
        ),
        returnValue: _i4.Future<bool>.value(false),
      ) as _i4.Future<bool>);

  @override
  void dispose() => super.noSuchMethod(
        Invocation.method(
          #dispose,
          [],
        ),
        returnValueForMissingStub: null,
      );
}
