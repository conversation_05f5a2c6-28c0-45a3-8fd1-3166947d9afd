import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:culture_connect/main.dart' as app;
import 'package:culture_connect/models/experience.dart';
import 'package:culture_connect/models/booking.dart';
import 'package:culture_connect/services/booking_service.dart';
import 'package:culture_connect/services/payment_service.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mockito/mockito.dart';

class MockBookingService extends Mock implements BookingService {
  @override
  Future<List<TimeSlot>> getAvailableTimeSlots(Experience experience, DateTime date) async {
    final timeSlots = [
      TimeSlot(
        startTime: DateTime(date.year, date.month, date.day, 10, 0),
        endTime: DateTime(date.year, date.month, date.day, 12, 0),
      ),
      TimeSlot(
        startTime: DateTime(date.year, date.month, date.day, 14, 0),
        endTime: DateTime(date.year, date.month, date.day, 16, 0),
      ),
    ];
    return timeSlots;
  }

  @override
  Future<Booking> createBooking({
    required Experience experience,
    required DateTime date,
    required TimeSlot timeSlot,
    required int participantCount,
    required String specialRequirements,
  }) async {
    return Booking(
      id: 'booking-${DateTime.now().millisecondsSinceEpoch}',
      experienceId: experience.id,
      date: date,
      timeSlot: timeSlot,
      participantCount: participantCount,
      totalAmount: experience.price * participantCount,
      status: BookingStatus.pending,
      specialRequirements: specialRequirements,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );
  }

  @override
  Future<void> updateBookingStatus(String bookingId, BookingStatus status, String? transactionId) async {
    // Do nothing in mock
  }

  @override
  Future<List<Booking>> getUserBookings() async {
    return [];
  }

  @override
  Future<List<Booking>> getUpcomingBookings() async {
    return [];
  }
}

class MockPaymentService extends Mock implements PaymentService {
  @override
  Future<Map<String, dynamic>> processPayment({
    required Booking booking,
    required String provider,
    required String userEmail,
    required String userName,
    required String userPhone,
  }) async {
    return {
      'success': true,
      'message': 'Payment successful',
      'paymentId': 'payment-${DateTime.now().millisecondsSinceEpoch}',
    };
  }
}

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  late MockBookingService mockBookingService;
  late MockPaymentService mockPaymentService;
  late Experience testExperience;

  setUp(() {
    mockBookingService = MockBookingService();
    mockPaymentService = MockPaymentService();
    
    testExperience = Experience(
      id: 'exp-123',
      title: 'Cultural Walking Tour',
      description: 'Explore the rich cultural heritage of the city with a local guide.',
      price: 50.0,
      durationMinutes: 120,
      location: 'City Center',
      coverImageUrl: 'https://example.com/image.jpg',
      imageUrls: ['https://example.com/image1.jpg'],
      rating: 4.8,
      reviewCount: 25,
      categories: ['Culture', 'History', 'Walking'],
      languages: ['English', 'Spanish'],
      maxParticipants: 10,
      isFeatured: true,
    );
  });

  testWidgets('Complete booking flow integration test', (WidgetTester tester) async {
    // Override providers with mocks
    final providerContainer = ProviderContainer(
      overrides: [
        bookingServiceProvider.overrideWithValue(mockBookingService),
        paymentServiceProvider.overrideWithValue(mockPaymentService),
      ],
    );

    // Start the app
    app.main();
    await tester.pumpAndSettle();

    // Navigate to experience details
    await tester.tap(find.text('Cultural Walking Tour').first);
    await tester.pumpAndSettle();

    // Tap on Book Now button
    await tester.tap(find.text('Book Now'));
    await tester.pumpAndSettle();

    // Select a date
    // This assumes a date picker is shown
    await tester.tap(find.text('15')); // Select the 15th day
    await tester.tap(find.text('OK'));
    await tester.pumpAndSettle();

    // Select a time slot
    await tester.tap(find.text('10:00 AM - 12:00 PM'));
    await tester.pumpAndSettle();

    // Set participant count
    await tester.tap(find.byIcon(Icons.add)); // Increase participant count
    await tester.pumpAndSettle();

    // Enter special requirements
    await tester.enterText(
      find.byType(TextField).last,
      'Vegetarian food options needed',
    );
    await tester.pumpAndSettle();

    // Continue to payment
    await tester.tap(find.text('Continue to Payment'));
    await tester.pumpAndSettle();

    // Verify booking summary is shown
    expect(find.text('Booking Summary'), findsOneWidget);
    expect(find.text('Cultural Walking Tour'), findsOneWidget);
    expect(find.text('2 Participants'), findsOneWidget);
    expect(find.text('\$100.00'), findsOneWidget);

    // Select payment method
    await tester.tap(find.text('Credit Card'));
    await tester.pumpAndSettle();

    // Enter payment details
    await tester.enterText(
      find.byKey(const Key('cardNumberField')),
      '****************',
    );
    await tester.enterText(
      find.byKey(const Key('expiryDateField')),
      '12/25',
    );
    await tester.enterText(
      find.byKey(const Key('cvvField')),
      '123',
    );
    await tester.enterText(
      find.byKey(const Key('nameOnCardField')),
      'Test User',
    );
    await tester.pumpAndSettle();

    // Complete payment
    await tester.tap(find.text('Pay Now'));
    await tester.pumpAndSettle();

    // Verify booking confirmation is shown
    expect(find.text('Booking Confirmed!'), findsOneWidget);
    expect(find.text('Your booking has been confirmed and payment processed successfully.'), findsOneWidget);

    // Verify booking details are shown
    expect(find.text('Booking Details'), findsOneWidget);
    expect(find.text('Status'), findsOneWidget);
    expect(find.text('Confirmed'), findsOneWidget);
    expect(find.text('Participants'), findsOneWidget);
    expect(find.text('2'), findsOneWidget);
    expect(find.text('Total Amount'), findsOneWidget);
    expect(find.text('\$100.00'), findsOneWidget);
    expect(find.text('Vegetarian food options needed'), findsOneWidget);

    // Add to calendar
    await tester.tap(find.text('Add to Calendar'));
    await tester.pumpAndSettle();

    // Verify success message
    expect(find.text('Booking added to calendar'), findsOneWidget);

    // Navigate to booking management
    await tester.tap(find.text('View All Bookings'));
    await tester.pumpAndSettle();

    // Verify booking management screen is shown
    expect(find.text('My Bookings'), findsOneWidget);
    expect(find.text('Upcoming'), findsOneWidget);
    expect(find.text('All Bookings'), findsOneWidget);
  });
}
