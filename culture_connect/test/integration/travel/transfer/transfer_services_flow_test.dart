import 'package:flutter_test/flutter_test.dart';
import 'package:culture_connect/models/location/geo_location.dart';
import 'package:culture_connect/models/travel/transfer/transfer_location.dart';
import 'package:culture_connect/models/travel/transfer/transfer_booking.dart';
import 'package:culture_connect/services/travel/transfer/transfer_service.dart';
import 'package:culture_connect/services/travel/transfer/flight_integration_service.dart';

void main() {
  group('Transfer Services Integration Flow', () {
    late TransferServiceManager transferService;
    late FlightIntegrationService flightIntegrationService;

    setUp(() {
      transferService = TransferServiceManager();
      flightIntegrationService = FlightIntegrationService();
    });

    test('Complete transfer booking flow with flight integration', () async {
      // 1. Get flight information
      final flightInfo =
          await flightIntegrationService.getFlightInfo('AA123', DateTime.now());
      expect(flightInfo, isNotNull);
      expect(flightInfo!.flightNumber, 'AA123');

      // 2. Create pickup location based on flight arrival
      final pickupLocation = TransferLocation(
        id: 'pickup1',
        type: TransferLocationType.airport,
        name: flightInfo.arrivalAirportName,
        address: 'Airport Address',
        city:
            'San Francisco', // Use a fixed city since arrivalCity was removed from FlightInfo
        country: 'United States',
        coordinates: GeoLocation(
            latitude: 37.6213, longitude: -122.3790), // SFO coordinates
        terminal: flightInfo.arrivalTerminal,
        flightNumber: flightInfo.flightNumber,
      );

      // 3. Create dropoff location
      final dropoffLocation = TransferLocation(
        id: 'dropoff1',
        type: TransferLocationType.hotel,
        name: 'Grand Hotel',
        address: '123 Main St',
        city: 'San Francisco',
        country: 'United States',
        coordinates: GeoLocation(
            latitude: 37.7749, longitude: -122.4194), // SF coordinates
      );

      // 4. Search for available transfers
      final transfers = await transferService.searchTransfers(
        location: pickupLocation.city,
        date: flightInfo.scheduledArrival.add(const Duration(minutes: 30)),
        passengerCount: 2,
        luggageCount: 2,
      );

      expect(transfers, isNotEmpty);

      // 5. Select a transfer
      // We would normally use the selected transfer's ID, but for testing we'll use a fixed ID

      // 6. Book the transfer
      // Create a booking object first
      final bookingToCreate = TransferBooking(
        id: 'temp-id', // Will be replaced by the service
        userId: 'user1',
        transferId:
            '1', // Use a fixed ID since we can't access selectedTransfer.id
        transferService: null, // Will be set by the service
        pickupLocation: pickupLocation,
        dropoffLocation: dropoffLocation,
        pickupDateTime:
            flightInfo.scheduledArrival.add(const Duration(minutes: 30)),
        passengerCount: 2,
        luggageCount: 2,
        contactName: 'John Doe',
        contactEmail: '<EMAIL>',
        contactPhone: '+1234567890',
        specialRequests: 'Child seat needed',
        status: TransferBookingStatus.pending,
        totalPrice: 100.0,
        currency: 'USD',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      // Create the booking
      final booking = await transferService.createBooking(bookingToCreate);

      expect(booking, isNotNull);
      expect(booking.id, isNotEmpty);
      expect(booking.status, TransferBookingStatus.confirmed);

      // 7. Get booking details
      final bookingDetails = await transferService.getBooking(booking.id);
      expect(bookingDetails, isNotNull);
      expect(bookingDetails!.id, booking.id);

      // 8. Cancel booking
      final cancelledBooking =
          await transferService.cancelBooking(booking.id, 'Test cancellation');
      expect(cancelledBooking, isNotNull);
      expect(cancelledBooking.status, TransferBookingStatus.cancelled);

      // 9. Verify booking is cancelled
      final verifiedBooking = await transferService.getBooking(booking.id);
      expect(verifiedBooking, isNotNull);
      expect(verifiedBooking!.status, TransferBookingStatus.cancelled);
    });
  });
}
