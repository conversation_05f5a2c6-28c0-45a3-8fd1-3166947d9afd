// Mocks generated by <PERSON><PERSON>to 5.4.5 from annotations
// in culture_connect/test/integration/auth_flow_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i4;

import 'package:culture_connect/models/user_model.dart' as _i5;
import 'package:culture_connect/services/auth_service.dart' as _i3;
import 'package:firebase_auth/firebase_auth.dart' as _i2;
import 'package:mockito/mockito.dart' as _i1;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeUserCredential_0 extends _i1.SmartFake
    implements _i2.UserCredential {
  _FakeUserCredential_0(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

/// A class which mocks [AuthService].
///
/// See the documentation for Mockito's code generation for more information.
class MockAuthService extends _i1.Mock implements _i3.AuthService {
  MockAuthService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i4.Stream<_i3.AuthStatus> get authStateChanges => (super.noSuchMethod(
        Invocation.getter(#authStateChanges),
        returnValue: _i4.Stream<_i3.AuthStatus>.empty(),
      ) as _i4.Stream<_i3.AuthStatus>);

  @override
  _i4.Future<_i5.UserModel?> get currentUserModel => (super.noSuchMethod(
        Invocation.getter(#currentUserModel),
        returnValue: _i4.Future<_i5.UserModel?>.value(),
      ) as _i4.Future<_i5.UserModel?>);

  @override
  _i4.Future<_i2.UserCredential> registerWithEmailAndPassword({
    required String? email,
    required String? password,
    required String? firstName,
    required String? lastName,
    required String? phoneNumber,
    required String? dateOfBirth,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #registerWithEmailAndPassword,
          [],
          {
            #email: email,
            #password: password,
            #firstName: firstName,
            #lastName: lastName,
            #phoneNumber: phoneNumber,
            #dateOfBirth: dateOfBirth,
          },
        ),
        returnValue: _i4.Future<_i2.UserCredential>.value(_FakeUserCredential_0(
          this,
          Invocation.method(
            #registerWithEmailAndPassword,
            [],
            {
              #email: email,
              #password: password,
              #firstName: firstName,
              #lastName: lastName,
              #phoneNumber: phoneNumber,
              #dateOfBirth: dateOfBirth,
            },
          ),
        )),
      ) as _i4.Future<_i2.UserCredential>);

  @override
  _i4.Future<_i2.UserCredential> loginWithEmailAndPassword({
    required String? email,
    required String? password,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #loginWithEmailAndPassword,
          [],
          {
            #email: email,
            #password: password,
          },
        ),
        returnValue: _i4.Future<_i2.UserCredential>.value(_FakeUserCredential_0(
          this,
          Invocation.method(
            #loginWithEmailAndPassword,
            [],
            {
              #email: email,
              #password: password,
            },
          ),
        )),
      ) as _i4.Future<_i2.UserCredential>);

  @override
  _i4.Future<_i2.UserCredential?> signInWithGoogle() => (super.noSuchMethod(
        Invocation.method(
          #signInWithGoogle,
          [],
        ),
        returnValue: _i4.Future<_i2.UserCredential?>.value(),
      ) as _i4.Future<_i2.UserCredential?>);

  @override
  _i4.Future<void> signOut() => (super.noSuchMethod(
        Invocation.method(
          #signOut,
          [],
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);

  @override
  _i4.Future<void> sendPasswordResetEmail(String? email) => (super.noSuchMethod(
        Invocation.method(
          #sendPasswordResetEmail,
          [email],
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);

  @override
  _i4.Future<bool> verifyEmail() => (super.noSuchMethod(
        Invocation.method(
          #verifyEmail,
          [],
        ),
        returnValue: _i4.Future<bool>.value(false),
      ) as _i4.Future<bool>);

  @override
  _i4.Future<void> resendVerificationEmail() => (super.noSuchMethod(
        Invocation.method(
          #resendVerificationEmail,
          [],
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);

  @override
  _i4.Future<bool> isBiometricAvailable() => (super.noSuchMethod(
        Invocation.method(
          #isBiometricAvailable,
          [],
        ),
        returnValue: _i4.Future<bool>.value(false),
      ) as _i4.Future<bool>);

  @override
  _i4.Future<bool> authenticateWithBiometrics() => (super.noSuchMethod(
        Invocation.method(
          #authenticateWithBiometrics,
          [],
        ),
        returnValue: _i4.Future<bool>.value(false),
      ) as _i4.Future<bool>);
}
