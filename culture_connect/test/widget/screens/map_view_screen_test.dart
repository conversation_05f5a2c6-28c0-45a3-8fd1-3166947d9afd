import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:culture_connect/screens/map_view_screen.dart';
import 'package:culture_connect/models/experience.dart';
import 'package:culture_connect/services/location_service.dart';
import 'package:culture_connect/services/map_cache_manager.dart';
import 'package:culture_connect/providers/experiences_provider.dart';
import 'package:culture_connect/providers/filter_options_provider.dart';
import 'package:culture_connect/providers/location_permission_provider.dart';
import 'package:culture_connect/providers/map_style_provider.dart';
import '../../utils/test_helpers.dart';

// Generate mocks for dependencies
@GenerateMocks([
  LocationService, 
  MapCacheManager,
])
import 'map_view_screen_test.mocks.dart';

// Mock GoogleMap widget for testing
class MockGoogleMap extends StatelessWidget {
  final CameraPosition initialCameraPosition;
  final Set<Marker> markers;
  final Set<Polyline> polylines;
  final void Function(GoogleMapController)? onMapCreated;
  final void Function(CameraPosition)? onCameraMove;
  final void Function()? onCameraIdle;
  
  const MockGoogleMap({
    super.key,
    required this.initialCameraPosition,
    required this.markers,
    required this.polylines,
    this.onMapCreated,
    this.onCameraMove,
    this.onCameraIdle,
  });
  
  @override
  Widget build(BuildContext context) {
    return Container(
      color: Colors.grey[300],
      child: Center(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('Mock Google Map'),
            Text('Markers: ${markers.length}'),
            Text('Polylines: ${polylines.length}'),
            Text('Camera: ${initialCameraPosition.target.latitude}, ${initialCameraPosition.target.longitude}'),
          ],
        ),
      ),
    );
  }
}

void main() {
  late MockLocationService mockLocationService;
  late MockMapCacheManager mockMapCacheManager;
  
  // Sample experiences for testing
  final sampleExperiences = [
    Experience(
      id: '1',
      title: 'Cultural Tour',
      description: 'Explore local culture',
      imageUrl: 'https://example.com/image1.jpg',
      category: 'Cultural Tours',
      location: 'San Francisco, CA',
      price: 99.99,
      rating: 4.5,
      reviewCount: 100,
      latitude: 37.7749,
      longitude: -122.4194,
    ),
    Experience(
      id: '2',
      title: 'Food Tour',
      description: 'Taste local cuisine',
      imageUrl: 'https://example.com/image2.jpg',
      category: 'Food & Drink',
      location: 'San Francisco, CA',
      price: 79.99,
      rating: 4.8,
      reviewCount: 150,
      latitude: 37.7750,
      longitude: -122.4195,
    ),
  ];

  setUp(() {
    mockLocationService = MockLocationService();
    mockMapCacheManager = MockMapCacheManager();
    
    // Setup mock location service
    when(mockLocationService.getCurrentPosition()).thenAnswer((_) async => null);
    when(mockLocationService.calculateDistance(any, any, any, any)).thenReturn(1000.0);
    when(mockLocationService.formatDistance(any)).thenReturn('1.0 km');
    when(mockLocationService.loadMapStyle(any)).thenAnswer((_) async => '[]');
    when(mockLocationService.getRoute(any, any)).thenAnswer((_) async => [
      const LatLng(37.7749, -122.4194),
      const LatLng(37.7750, -122.4195),
    ]);
    
    // Setup mock map cache manager
    when(mockMapCacheManager.getCachedRegions()).thenAnswer((_) async => []);
    when(mockMapCacheManager.getCacheSize()).thenAnswer((_) async => 0);
    when(mockMapCacheManager.cacheMapRegion(any, any, any, any))
        .thenAnswer((_) async => true);
  });

  testWidgets('MapViewScreen should render correctly', (WidgetTester tester) async {
    // Mock GoogleMap widget
    await tester.pumpWidget(
      ProviderScope(
        overrides: [
          // Override providers
          locationServiceProvider.overrideWithValue(mockLocationService),
          mapCacheManagerProvider.overrideWithValue(mockMapCacheManager),
          experiencesProvider.overrideWith((_) => AsyncValue.data(sampleExperiences)),
          locationPermissionProvider.overrideWith((_) => LocationPermissionState.granted),
          selectedMapStyleProvider.overrideWith((_) => 'standard'),
        ],
        child: MaterialApp(
          home: Builder(
            builder: (context) {
              return const MapViewScreen();
            },
          ),
        ),
      ),
    );
    
    // Wait for widget to build
    await tester.pumpAndSettle();
    
    // Verify that the screen renders
    expect(find.text('Map'), findsOneWidget);
    
    // Verify that the search bar is present
    expect(find.byType(TextField), findsOneWidget);
    
    // Verify that the category filter is present
    expect(find.text('All'), findsOneWidget);
  });
  
  testWidgets('MapViewScreen should show experiences', (WidgetTester tester) async {
    // Mock GoogleMap widget
    await tester.pumpWidget(
      ProviderScope(
        overrides: [
          // Override providers
          locationServiceProvider.overrideWithValue(mockLocationService),
          mapCacheManagerProvider.overrideWithValue(mockMapCacheManager),
          experiencesProvider.overrideWith((_) => AsyncValue.data(sampleExperiences)),
          locationPermissionProvider.overrideWith((_) => LocationPermissionState.granted),
          selectedMapStyleProvider.overrideWith((_) => 'standard'),
        ],
        child: MaterialApp(
          home: Builder(
            builder: (context) {
              return const MapViewScreen();
            },
          ),
        ),
      ),
    );
    
    // Wait for widget to build
    await tester.pumpAndSettle();
    
    // Verify that the experience cards are present
    expect(find.text('Cultural Tour'), findsOneWidget);
    expect(find.text('Food Tour'), findsOneWidget);
  });
  
  testWidgets('MapViewScreen should filter experiences by category', (WidgetTester tester) async {
    // Mock GoogleMap widget
    await tester.pumpWidget(
      ProviderScope(
        overrides: [
          // Override providers
          locationServiceProvider.overrideWithValue(mockLocationService),
          mapCacheManagerProvider.overrideWithValue(mockMapCacheManager),
          experiencesProvider.overrideWith((_) => AsyncValue.data(sampleExperiences)),
          locationPermissionProvider.overrideWith((_) => LocationPermissionState.granted),
          selectedMapStyleProvider.overrideWith((_) => 'standard'),
        ],
        child: MaterialApp(
          home: Builder(
            builder: (context) {
              return const MapViewScreen();
            },
          ),
        ),
      ),
    );
    
    // Wait for widget to build
    await tester.pumpAndSettle();
    
    // Tap on the Food & Drink category
    await tester.tap(find.text('Food & Drink'));
    await tester.pumpAndSettle();
    
    // Verify that only the Food Tour is visible
    expect(find.text('Cultural Tour'), findsNothing);
    expect(find.text('Food Tour'), findsOneWidget);
  });
  
  testWidgets('MapViewScreen should search experiences', (WidgetTester tester) async {
    // Mock GoogleMap widget
    await tester.pumpWidget(
      ProviderScope(
        overrides: [
          // Override providers
          locationServiceProvider.overrideWithValue(mockLocationService),
          mapCacheManagerProvider.overrideWithValue(mockMapCacheManager),
          experiencesProvider.overrideWith((_) => AsyncValue.data(sampleExperiences)),
          locationPermissionProvider.overrideWith((_) => LocationPermissionState.granted),
          selectedMapStyleProvider.overrideWith((_) => 'standard'),
        ],
        child: MaterialApp(
          home: Builder(
            builder: (context) {
              return const MapViewScreen();
            },
          ),
        ),
      ),
    );
    
    // Wait for widget to build
    await tester.pumpAndSettle();
    
    // Enter search query
    await tester.enterText(find.byType(TextField), 'food');
    await tester.pumpAndSettle();
    
    // Verify that only the Food Tour is visible
    expect(find.text('Cultural Tour'), findsNothing);
    expect(find.text('Food Tour'), findsOneWidget);
  });
  
  testWidgets('MapViewScreen should show location permission dialog when permission is denied', (WidgetTester tester) async {
    // Mock GoogleMap widget
    await tester.pumpWidget(
      ProviderScope(
        overrides: [
          // Override providers
          locationServiceProvider.overrideWithValue(mockLocationService),
          mapCacheManagerProvider.overrideWithValue(mockMapCacheManager),
          experiencesProvider.overrideWith((_) => AsyncValue.data(sampleExperiences)),
          locationPermissionProvider.overrideWith((_) => LocationPermissionState.denied),
          selectedMapStyleProvider.overrideWith((_) => 'standard'),
        ],
        child: MaterialApp(
          home: Builder(
            builder: (context) {
              return const MapViewScreen();
            },
          ),
        ),
      ),
    );
    
    // Wait for widget to build
    await tester.pumpAndSettle();
    
    // Verify that the location permission dialog is shown
    expect(find.text('Location Access Required'), findsOneWidget);
    expect(find.text('Allow location access to see nearby experiences and get directions.'), findsOneWidget);
    expect(find.text('Grant Permission'), findsOneWidget);
  });
  
  testWidgets('MapViewScreen should show map controls', (WidgetTester tester) async {
    // Mock GoogleMap widget
    await tester.pumpWidget(
      ProviderScope(
        overrides: [
          // Override providers
          locationServiceProvider.overrideWithValue(mockLocationService),
          mapCacheManagerProvider.overrideWithValue(mockMapCacheManager),
          experiencesProvider.overrideWith((_) => AsyncValue.data(sampleExperiences)),
          locationPermissionProvider.overrideWith((_) => LocationPermissionState.granted),
          selectedMapStyleProvider.overrideWith((_) => 'standard'),
        ],
        child: MaterialApp(
          home: Builder(
            builder: (context) {
              return const MapViewScreen();
            },
          ),
        ),
      ),
    );
    
    // Wait for widget to build
    await tester.pumpAndSettle();
    
    // Verify that the map controls are present
    expect(find.byIcon(Icons.my_location), findsOneWidget);
    expect(find.byIcon(Icons.directions), findsOneWidget);
    expect(find.byIcon(Icons.layers), findsOneWidget);
    expect(find.byIcon(Icons.download), findsOneWidget);
  });
}
