import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:culture_connect/screens/splash_screen.dart';
import 'package:culture_connect/providers/auth_provider.dart';
import 'package:culture_connect/providers/startup_optimization_provider.dart';
import 'package:culture_connect/services/auth_service.dart';
import 'package:culture_connect/services/startup_optimization_service.dart';

// Generate mocks for dependencies
@GenerateMocks([AuthService, StartupOptimizationService])
import 'splash_screen_test.mocks.dart';

// Mock providers
class MockAuthNotifier extends StateNotifier<AsyncValue<AuthStatus>> {
  MockAuthNotifier() : super(const AsyncValue.loading());

  void setStatus(AuthStatus status) {
    state = AsyncValue.data(status);
  }

  void setError(Object error) {
    state = AsyncValue.error(error, StackTrace.current);
  }
}

class MockAppInitializationNotifier
    extends StateNotifier<AppInitializationState> {
  MockAppInitializationNotifier() : super(AppInitializationState());

  void setInitialized() {
    state =
        state.copyWith(isInitialized: true, isLoading: false, progress: 1.0);
  }

  void setError(String error) {
    state = state.copyWith(isLoading: false, error: error, progress: 1.0);
  }

  void setProgress(double progress) {
    state = state.copyWith(progress: progress);
  }
}

final mockAuthStatusProvider =
    StateNotifierProvider<MockAuthNotifier, AsyncValue<AuthStatus>>((ref) {
  return MockAuthNotifier();
});

final mockAppInitializationStateProvider = StateNotifierProvider<
    MockAppInitializationNotifier, AppInitializationState>((ref) {
  return MockAppInitializationNotifier();
});

void main() {
  late MockAuthNotifier mockAuthNotifier;
  late MockAppInitializationNotifier mockAppInitializationNotifier;

  setUp(() {
    mockAuthNotifier = MockAuthNotifier();
    mockAppInitializationNotifier = MockAppInitializationNotifier();
  });

  testWidgets('SplashScreen should show loading indicator initially',
      (WidgetTester tester) async {
    // Build our app and trigger a frame
    await tester.pumpWidget(
      ProviderScope(
        overrides: [
          authStatusProvider.overrideWithProvider(mockAuthStatusProvider),
          appInitializationStateProvider
              .overrideWithProvider(mockAppInitializationStateProvider),
        ],
        child: const MaterialApp(
          home: SplashScreen(),
        ),
      ),
    );

    // Should show app logo
    expect(find.text('CultureConnect'), findsOneWidget);

    // Should show loading indicator
    expect(find.byType(LinearProgressIndicator), findsOneWidget);

    // Should show loading text
    expect(find.textContaining('Loading'), findsOneWidget);
  });

  testWidgets('SplashScreen should show progress updates',
      (WidgetTester tester) async {
    // Build our app and trigger a frame
    await tester.pumpWidget(
      ProviderScope(
        overrides: [
          authStatusProvider.overrideWithProvider(mockAuthStatusProvider),
          appInitializationStateProvider
              .overrideWithProvider(mockAppInitializationStateProvider),
        ],
        child: const MaterialApp(
          home: SplashScreen(),
        ),
      ),
    );

    // Update progress to 50%
    mockAppInitializationNotifier.setProgress(0.5);
    await tester.pump();

    // Should show updated progress
    expect(find.text('Loading... 50%'), findsOneWidget);
  });

  testWidgets(
      'SplashScreen should navigate to MainNavigation when authenticated',
      (WidgetTester tester) async {
    // Mock navigator observer to track navigation
    final mockObserver = MockNavigatorObserver();

    // Build our app and trigger a frame
    await tester.pumpWidget(
      ProviderScope(
        overrides: [
          authStatusProvider.overrideWithProvider(mockAuthStatusProvider),
          appInitializationStateProvider
              .overrideWithProvider(mockAppInitializationStateProvider),
        ],
        child: MaterialApp(
          home: const SplashScreen(),
          navigatorObservers: [mockObserver],
        ),
      ),
    );

    // Complete initialization
    mockAppInitializationNotifier.setInitialized();
    await tester.pump();

    // Set auth status to authenticated
    mockAuthNotifier.setStatus(AuthStatus.authenticated);
    await tester.pumpAndSettle();

    // Verify navigation to MainNavigation
    verify(mockObserver.didPush(any, any));
  });

  testWidgets(
      'SplashScreen should navigate to VerificationScreen when verification pending',
      (WidgetTester tester) async {
    // Mock navigator observer to track navigation
    final mockObserver = MockNavigatorObserver();

    // Build our app and trigger a frame
    await tester.pumpWidget(
      ProviderScope(
        overrides: [
          authStatusProvider.overrideWithProvider(mockAuthStatusProvider),
          appInitializationStateProvider
              .overrideWithProvider(mockAppInitializationStateProvider),
        ],
        child: MaterialApp(
          home: const SplashScreen(),
          navigatorObservers: [mockObserver],
        ),
      ),
    );

    // Complete initialization
    mockAppInitializationNotifier.setInitialized();
    await tester.pump();

    // Set auth status to verification pending
    mockAuthNotifier.setStatus(AuthStatus.verificationPending);
    await tester.pumpAndSettle();

    // Verify navigation to VerificationScreen
    verify(mockObserver.didPush(any, any));
  });

  testWidgets(
      'SplashScreen should navigate to OnboardingScreen when unauthenticated',
      (WidgetTester tester) async {
    // Mock navigator observer to track navigation
    final mockObserver = MockNavigatorObserver();

    // Build our app and trigger a frame
    await tester.pumpWidget(
      ProviderScope(
        overrides: [
          authStatusProvider.overrideWithProvider(mockAuthStatusProvider),
          appInitializationStateProvider
              .overrideWithProvider(mockAppInitializationStateProvider),
        ],
        child: MaterialApp(
          home: const SplashScreen(),
          navigatorObservers: [mockObserver],
        ),
      ),
    );

    // Complete initialization
    mockAppInitializationNotifier.setInitialized();
    await tester.pump();

    // Set auth status to unauthenticated
    mockAuthNotifier.setStatus(AuthStatus.unauthenticated);
    await tester.pumpAndSettle();

    // Verify navigation to OnboardingScreen
    verify(mockObserver.didPush(any, any));
  });

  testWidgets(
      'SplashScreen should show error message when initialization fails',
      (WidgetTester tester) async {
    // Build our app and trigger a frame
    await tester.pumpWidget(
      ProviderScope(
        overrides: [
          authStatusProvider.overrideWithProvider(mockAuthStatusProvider),
          appInitializationStateProvider
              .overrideWithProvider(mockAppInitializationStateProvider),
        ],
        child: const MaterialApp(
          home: SplashScreen(),
        ),
      ),
    );

    // Set initialization error
    mockAppInitializationNotifier.setError('Failed to initialize app');
    await tester.pump();

    // Should show error message
    expect(find.text('Error: Failed to initialize app'), findsOneWidget);
  });

  testWidgets(
      'SplashScreen should show error message when authentication fails',
      (WidgetTester tester) async {
    // Build our app and trigger a frame
    await tester.pumpWidget(
      ProviderScope(
        overrides: [
          authStatusProvider.overrideWithProvider(mockAuthStatusProvider),
          appInitializationStateProvider
              .overrideWithProvider(mockAppInitializationStateProvider),
        ],
        child: const MaterialApp(
          home: SplashScreen(),
        ),
      ),
    );

    // Complete initialization
    mockAppInitializationNotifier.setInitialized();
    await tester.pump();

    // Set auth error
    mockAuthNotifier.setError('Authentication failed');
    await tester.pumpAndSettle();

    // Should show error message
    expect(find.textContaining('Authentication error'), findsOneWidget);
  });
}

// Mock navigator observer
class MockNavigatorObserver extends Mock implements NavigatorObserver {
  @override
  void didPush(Route<dynamic> route, Route<dynamic>? previousRoute) {}
}
