import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:culture_connect/models/booking.dart';
import 'package:culture_connect/screens/booking_management_screen.dart';
import 'package:culture_connect/services/booking_service.dart';

// Generate mocks for dependencies
@GenerateMocks([BookingService])
import 'booking_management_screen_test.mocks.dart';

void main() {
  late MockBookingService mockBookingService;
  late List<Booking> testBookings;
  late List<Booking> testUpcomingBookings;

  setUp(() {
    mockBookingService = MockBookingService();

    final now = DateTime.now();
    final tomorrow = DateTime(now.year, now.month, now.day + 1);
    final yesterday = DateTime(now.year, now.month, now.day - 1);

    // Create test bookings
    testBookings = [
      // Upcoming confirmed booking
      Booking(
        id: 'booking-123',
        experienceId: 'exp-456',
        date: tomorrow,
        timeSlot: TimeSlot(
          startTime:
              DateTime(tomorrow.year, tomorrow.month, tomorrow.day, 10, 0),
          endTime: DateTime(tomorrow.year, tomorrow.month, tomorrow.day, 12, 0),
        ),
        participantCount: 2,
        totalAmount: 100.0,
        status: BookingStatus.confirmed,
        specialRequirements: 'Vegetarian food',
        createdAt: now.subtract(const Duration(days: 1)),
        updatedAt: now.subtract(const Duration(days: 1)),
      ),
      // Past completed booking
      Booking(
        id: 'booking-456',
        experienceId: 'exp-789',
        date: yesterday,
        timeSlot: TimeSlot(
          startTime:
              DateTime(yesterday.year, yesterday.month, yesterday.day, 14, 0),
          endTime:
              DateTime(yesterday.year, yesterday.month, yesterday.day, 16, 0),
        ),
        participantCount: 1,
        totalAmount: 50.0,
        status: BookingStatus.completed,
        specialRequirements: '',
        createdAt: now.subtract(const Duration(days: 3)),
        updatedAt: now.subtract(const Duration(days: 2)),
      ),
      // Cancelled booking
      Booking(
        id: 'booking-789',
        experienceId: 'exp-123',
        date: tomorrow,
        timeSlot: TimeSlot(
          startTime:
              DateTime(tomorrow.year, tomorrow.month, tomorrow.day, 16, 0),
          endTime: DateTime(tomorrow.year, tomorrow.month, tomorrow.day, 18, 0),
        ),
        participantCount: 3,
        totalAmount: 150.0,
        status: BookingStatus.cancelled,
        specialRequirements: 'Need wheelchair access',
        createdAt: now.subtract(const Duration(days: 2)),
        updatedAt: now.subtract(const Duration(hours: 12)),
      ),
    ];

    // Only the first booking is upcoming and confirmed
    testUpcomingBookings = [testBookings[0]];

    // Setup mock responses
    when(mockBookingService.getUserBookings())
        .thenAnswer((_) async => testBookings);

    when(mockBookingService.getUpcomingBookings())
        .thenAnswer((_) async => testUpcomingBookings);

    when(mockBookingService.cancelBooking(any)).thenAnswer((_) async {
      return null;
    });

    when(mockBookingService.requestRefund(any, any))
        .thenAnswer((_) async => true);

    when(mockBookingService.addBookingToCalendar(any))
        .thenAnswer((_) async => true);
  });

  Widget createWidgetUnderTest() {
    return ProviderScope(
      overrides: [
        // Override the booking service provider to use our mock
        bookingServiceProvider.overrideWithValue(mockBookingService),
      ],
      child: const MaterialApp(
        home: BookingManagementScreen(),
      ),
    );
  }

  group('BookingManagementScreen Widget Tests', () {
    testWidgets('should display tabs correctly', (WidgetTester tester) async {
      // Arrange
      await tester.pumpWidget(createWidgetUnderTest());
      await tester.pumpAndSettle(); // Wait for any animations to complete

      // Assert
      expect(find.text('Upcoming'), findsOneWidget);
      expect(find.text('All Bookings'), findsOneWidget);
    });

    testWidgets('should load and display bookings',
        (WidgetTester tester) async {
      // Arrange
      await tester.pumpWidget(createWidgetUnderTest());
      await tester.pumpAndSettle();

      // Assert - Upcoming tab should be active by default
      expect(find.text('Booking #booking-123'), findsOneWidget);
      expect(find.text('Confirmed'), findsOneWidget);
      expect(find.text('Vegetarian food'), findsOneWidget);

      // Other bookings should not be visible in the Upcoming tab
      expect(find.text('Booking #booking-456'), findsNothing);
      expect(find.text('Booking #booking-789'), findsNothing);

      // Switch to All Bookings tab
      await tester.tap(find.text('All Bookings'));
      await tester.pumpAndSettle();

      // All bookings should be visible
      expect(find.text('Booking #booking-123'), findsOneWidget);
      expect(find.text('Booking #booking-456'), findsOneWidget);
      expect(find.text('Booking #booking-789'), findsOneWidget);

      // Status indicators should be visible
      expect(find.text('Confirmed'), findsOneWidget);
      expect(find.text('Completed'), findsOneWidget);
      expect(find.text('Cancelled'), findsOneWidget);
    });

    testWidgets('should show empty state when no bookings',
        (WidgetTester tester) async {
      // Arrange
      when(mockBookingService.getUserBookings()).thenAnswer((_) async => []);

      when(mockBookingService.getUpcomingBookings())
          .thenAnswer((_) async => []);

      await tester.pumpWidget(createWidgetUnderTest());
      await tester.pumpAndSettle();

      // Assert - Upcoming tab should show empty state
      expect(find.text('No upcoming bookings'), findsOneWidget);
      expect(find.text('Your confirmed upcoming bookings will appear here'),
          findsOneWidget);

      // Switch to All Bookings tab
      await tester.tap(find.text('All Bookings'));
      await tester.pumpAndSettle();

      // All bookings tab should show empty state
      expect(find.text('No bookings found'), findsOneWidget);
      expect(
          find.text('Your booking history will appear here'), findsOneWidget);
    });

    testWidgets('should call cancelBooking when cancel button is pressed',
        (WidgetTester tester) async {
      // Arrange
      await tester.pumpWidget(createWidgetUnderTest());
      await tester.pumpAndSettle();

      // Act - Find and tap the cancel button in the upcoming tab
      await tester.tap(find.text('Cancel Booking'));
      await tester.pumpAndSettle();

      // Assert
      verify(mockBookingService.cancelBooking('booking-123')).called(1);
    });

    testWidgets(
        'should show refund dialog when request refund button is pressed',
        (WidgetTester tester) async {
      // Arrange - Switch to All Bookings tab to see the cancelled booking
      await tester.pumpWidget(createWidgetUnderTest());
      await tester.pumpAndSettle();

      await tester.tap(find.text('All Bookings'));
      await tester.pumpAndSettle();

      // Act - Find and tap the request refund button
      await tester.tap(find.text('Request Refund'));
      await tester.pumpAndSettle();

      // Assert - Dialog should be visible
      expect(find.text('Request Refund'), findsOneWidget);
      expect(find.text('Please provide a reason for your refund request:'),
          findsOneWidget);

      // Enter reason and submit
      await tester.enterText(find.byType(TextField), 'Changed plans');
      await tester.tap(find.text('Submit'));
      await tester.pumpAndSettle();

      // Verify refund was requested
      verify(mockBookingService.requestRefund('booking-789', 'Changed plans'))
          .called(1);
    });

    testWidgets(
        'should call addToCalendar when add to calendar button is pressed',
        (WidgetTester tester) async {
      // Arrange
      await tester.pumpWidget(createWidgetUnderTest());
      await tester.pumpAndSettle();

      // Act - Find and tap the add to calendar button
      await tester.tap(find.text('Add to Calendar'));
      await tester.pumpAndSettle();

      // Assert
      verify(mockBookingService.addBookingToCalendar(any)).called(1);

      // Should show success snackbar
      expect(find.text('Booking added to calendar'), findsOneWidget);
    });

    testWidgets('should handle error in loading bookings',
        (WidgetTester tester) async {
      // Arrange
      when(mockBookingService.getUserBookings())
          .thenThrow(Exception('Network error'));

      when(mockBookingService.getUpcomingBookings())
          .thenThrow(Exception('Network error'));

      // Act
      await tester.pumpWidget(createWidgetUnderTest());
      await tester.pumpAndSettle();

      // Assert
      expect(find.text('Exception: Network error'), findsOneWidget);
      expect(find.text('Retry'), findsOneWidget);
    });

    testWidgets('should retry loading bookings when retry button is pressed',
        (WidgetTester tester) async {
      // Arrange
      when(mockBookingService.getUserBookings())
          .thenThrow(Exception('Network error'));

      when(mockBookingService.getUpcomingBookings())
          .thenThrow(Exception('Network error'));

      await tester.pumpWidget(createWidgetUnderTest());
      await tester.pumpAndSettle();

      // Reset mocks to succeed on retry
      when(mockBookingService.getUserBookings())
          .thenAnswer((_) async => testBookings);

      when(mockBookingService.getUpcomingBookings())
          .thenAnswer((_) async => testUpcomingBookings);

      // Act
      await tester.tap(find.text('Retry'));
      await tester.pumpAndSettle();

      // Assert
      verify(mockBookingService.getUserBookings()).called(2);
      verify(mockBookingService.getUpcomingBookings()).called(2);
      expect(find.text('Booking #booking-123'), findsOneWidget);
    });
  });
}
