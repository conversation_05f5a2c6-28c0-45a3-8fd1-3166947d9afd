// Mocks generated by <PERSON><PERSON><PERSON> 5.4.5 from annotations
// in culture_connect/test/widget/screens/booking_confirmation_screen_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i5;

import 'package:culture_connect/models/booking.dart' as _i2;
import 'package:culture_connect/models/booking_model.dart' as _i3;
import 'package:culture_connect/models/experience.dart' as _i6;
import 'package:culture_connect/services/booking_service.dart' as _i4;
import 'package:mockito/mockito.dart' as _i1;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeBooking_0 extends _i1.SmartFake implements _i2.Booking {
  _FakeBooking_0(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeBookingModel_1 extends _i1.SmartFake implements _i3.BookingModel {
  _FakeBookingModel_1(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

/// A class which mocks [BookingService].
///
/// See the documentation for Mockito's code generation for more information.
class MockBookingService extends _i1.Mock implements _i4.BookingService {
  MockBookingService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i5.Future<bool> isDateAvailable(
    _i6.Experience? experience,
    DateTime? date,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #isDateAvailable,
          [
            experience,
            date,
          ],
        ),
        returnValue: _i5.Future<bool>.value(false),
      ) as _i5.Future<bool>);

  @override
  _i5.Future<List<_i2.TimeSlot>> getAvailableTimeSlots(
    _i6.Experience? experience,
    DateTime? date,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #getAvailableTimeSlots,
          [
            experience,
            date,
          ],
        ),
        returnValue: _i5.Future<List<_i2.TimeSlot>>.value(<_i2.TimeSlot>[]),
      ) as _i5.Future<List<_i2.TimeSlot>>);

  @override
  double calculateTotalPrice(
    _i6.Experience? experience,
    int? participantCount,
    DateTime? date,
    _i2.TimeSlot? timeSlot,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #calculateTotalPrice,
          [
            experience,
            participantCount,
            date,
            timeSlot,
          ],
        ),
        returnValue: 0.0,
      ) as double);

  @override
  _i5.Future<_i2.Booking> createBooking({
    required _i6.Experience? experience,
    required DateTime? date,
    required _i2.TimeSlot? timeSlot,
    required int? participantCount,
    required String? specialRequirements,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #createBooking,
          [],
          {
            #experience: experience,
            #date: date,
            #timeSlot: timeSlot,
            #participantCount: participantCount,
            #specialRequirements: specialRequirements,
          },
        ),
        returnValue: _i5.Future<_i2.Booking>.value(_FakeBooking_0(
          this,
          Invocation.method(
            #createBooking,
            [],
            {
              #experience: experience,
              #date: date,
              #timeSlot: timeSlot,
              #participantCount: participantCount,
              #specialRequirements: specialRequirements,
            },
          ),
        )),
      ) as _i5.Future<_i2.Booking>);

  @override
  _i5.Future<void> updateBookingStatus(
    String? bookingId,
    _i2.BookingStatus? status,
    String? transactionId,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #updateBookingStatus,
          [
            bookingId,
            status,
            transactionId,
          ],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<List<_i2.Booking>> getUserBookings() => (super.noSuchMethod(
        Invocation.method(
          #getUserBookings,
          [],
        ),
        returnValue: _i5.Future<List<_i2.Booking>>.value(<_i2.Booking>[]),
      ) as _i5.Future<List<_i2.Booking>>);

  @override
  _i5.Future<List<_i3.BookingModel>> getUpcomingBookings() =>
      (super.noSuchMethod(
        Invocation.method(
          #getUpcomingBookings,
          [],
        ),
        returnValue:
            _i5.Future<List<_i3.BookingModel>>.value(<_i3.BookingModel>[]),
      ) as _i5.Future<List<_i3.BookingModel>>);

  @override
  _i5.Future<void> cancelBooking(String? bookingId) => (super.noSuchMethod(
        Invocation.method(
          #cancelBooking,
          [bookingId],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<bool> requestRefund(
    String? bookingId,
    String? reason,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #requestRefund,
          [
            bookingId,
            reason,
          ],
        ),
        returnValue: _i5.Future<bool>.value(false),
      ) as _i5.Future<bool>);

  @override
  _i5.Future<bool> addBookingToCalendar(_i2.Booking? booking) =>
      (super.noSuchMethod(
        Invocation.method(
          #addBookingToCalendar,
          [booking],
        ),
        returnValue: _i5.Future<bool>.value(false),
      ) as _i5.Future<bool>);

  @override
  _i5.Future<void> setupBookingReminders(_i2.Booking? booking) =>
      (super.noSuchMethod(
        Invocation.method(
          #setupBookingReminders,
          [booking],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<List<_i3.BookingModel>> getGuideBookings() => (super.noSuchMethod(
        Invocation.method(
          #getGuideBookings,
          [],
        ),
        returnValue:
            _i5.Future<List<_i3.BookingModel>>.value(<_i3.BookingModel>[]),
      ) as _i5.Future<List<_i3.BookingModel>>);

  @override
  _i5.Future<_i3.BookingModel?> getBookingById(String? bookingId) =>
      (super.noSuchMethod(
        Invocation.method(
          #getBookingById,
          [bookingId],
        ),
        returnValue: _i5.Future<_i3.BookingModel?>.value(),
      ) as _i5.Future<_i3.BookingModel?>);

  @override
  _i5.Future<_i3.BookingModel> approveBooking(String? bookingId) =>
      (super.noSuchMethod(
        Invocation.method(
          #approveBooking,
          [bookingId],
        ),
        returnValue: _i5.Future<_i3.BookingModel>.value(_FakeBookingModel_1(
          this,
          Invocation.method(
            #approveBooking,
            [bookingId],
          ),
        )),
      ) as _i5.Future<_i3.BookingModel>);

  @override
  _i5.Future<_i3.BookingModel> rejectBooking(
    String? bookingId,
    String? rejectionReason,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #rejectBooking,
          [
            bookingId,
            rejectionReason,
          ],
        ),
        returnValue: _i5.Future<_i3.BookingModel>.value(_FakeBookingModel_1(
          this,
          Invocation.method(
            #rejectBooking,
            [
              bookingId,
              rejectionReason,
            ],
          ),
        )),
      ) as _i5.Future<_i3.BookingModel>);

  @override
  _i5.Future<_i3.BookingModel> completeBooking(String? bookingId) =>
      (super.noSuchMethod(
        Invocation.method(
          #completeBooking,
          [bookingId],
        ),
        returnValue: _i5.Future<_i3.BookingModel>.value(_FakeBookingModel_1(
          this,
          Invocation.method(
            #completeBooking,
            [bookingId],
          ),
        )),
      ) as _i5.Future<_i3.BookingModel>);

  @override
  _i5.Future<_i3.BookingModel> markAsNoShow(String? bookingId) =>
      (super.noSuchMethod(
        Invocation.method(
          #markAsNoShow,
          [bookingId],
        ),
        returnValue: _i5.Future<_i3.BookingModel>.value(_FakeBookingModel_1(
          this,
          Invocation.method(
            #markAsNoShow,
            [bookingId],
          ),
        )),
      ) as _i5.Future<_i3.BookingModel>);

  @override
  _i5.Future<_i3.BookingModel> processRefund(
    String? bookingId,
    double? refundAmount,
    String? refundReason,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #processRefund,
          [
            bookingId,
            refundAmount,
            refundReason,
          ],
        ),
        returnValue: _i5.Future<_i3.BookingModel>.value(_FakeBookingModel_1(
          this,
          Invocation.method(
            #processRefund,
            [
              bookingId,
              refundAmount,
              refundReason,
            ],
          ),
        )),
      ) as _i5.Future<_i3.BookingModel>);

  @override
  _i5.Future<void> sendCustomerNotification(
    String? bookingId,
    String? message,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #sendCustomerNotification,
          [
            bookingId,
            message,
          ],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<List<_i3.BookingModel>> getPastBookings() => (super.noSuchMethod(
        Invocation.method(
          #getPastBookings,
          [],
        ),
        returnValue:
            _i5.Future<List<_i3.BookingModel>>.value(<_i3.BookingModel>[]),
      ) as _i5.Future<List<_i3.BookingModel>>);

  @override
  _i5.Future<List<_i3.BookingModel>> getPendingBookings() =>
      (super.noSuchMethod(
        Invocation.method(
          #getPendingBookings,
          [],
        ),
        returnValue:
            _i5.Future<List<_i3.BookingModel>>.value(<_i3.BookingModel>[]),
      ) as _i5.Future<List<_i3.BookingModel>>);
}
