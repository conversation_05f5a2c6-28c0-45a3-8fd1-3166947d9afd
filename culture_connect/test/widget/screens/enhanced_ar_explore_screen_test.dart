import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:culture_connect/screens/enhanced_ar_explore_screen.dart';
import 'package:culture_connect/services/ar_voice_command_service.dart';
import 'package:culture_connect/services/ar_backend_service.dart';
import 'package:culture_connect/services/ar_recording_service.dart';
import 'package:culture_connect/services/ar_lazy_loading_service.dart';
import 'package:culture_connect/providers/ar_lazy_loading_provider.dart';
import 'package:culture_connect/models/landmark.dart';

// Generate mocks for dependencies
@GenerateMocks([
  ARVoiceCommandService, 
  ARBackendService, 
  ARRecordingService,
  ARLazyLoadingService
])
import 'enhanced_ar_explore_screen_test.mocks.dart';

// Mock providers
final mockARVoiceCommandServiceProvider = Provider<ARVoiceCommandService>((ref) {
  return MockARVoiceCommandService();
});

final mockARBackendServiceProvider = Provider<ARBackendService>((ref) {
  return MockARBackendService();
});

final mockARRecordingServiceProvider = Provider<ARRecordingService>((ref) {
  return MockARRecordingService();
});

final mockARLazyLoadingServiceProvider = Provider<ARLazyLoadingService>((ref) {
  return MockARLazyLoadingService();
});

void main() {
  late MockARVoiceCommandService mockVoiceCommandService;
  late MockARBackendService mockBackendService;
  late MockARRecordingService mockRecordingService;
  late MockARLazyLoadingService mockLazyLoadingService;

  setUp(() {
    mockVoiceCommandService = MockARVoiceCommandService();
    mockBackendService = MockARBackendService();
    mockRecordingService = MockARRecordingService();
    mockLazyLoadingService = MockARLazyLoadingService();
    
    // Setup mock voice command service
    when(mockVoiceCommandService.initialize()).thenAnswer((_) async => true);
    when(mockVoiceCommandService.isVoiceCommandsEnabled).thenReturn(true);
    
    // Setup mock backend service
    when(mockBackendService.initialize()).thenAnswer((_) async => true);
    when(mockBackendService.fetchNearbyLandmarks(
      latitude: anyNamed('latitude'),
      longitude: anyNamed('longitude'),
      radius: anyNamed('radius'),
    )).thenAnswer((_) async => [
      Landmark(
        id: '1',
        name: 'Eiffel Tower',
        description: 'Famous landmark in Paris',
        latitude: 48.8584,
        longitude: 2.2945,
        hasArContent: true,
        arContentId: 'ar-eiffel-tower',
        imageUrl: 'https://example.com/eiffel.jpg',
      ),
      Landmark(
        id: '2',
        name: 'Statue of Liberty',
        description: 'Famous landmark in New York',
        latitude: 40.6892,
        longitude: -74.0445,
        hasArContent: true,
        arContentId: 'ar-statue-liberty',
        imageUrl: 'https://example.com/liberty.jpg',
      ),
    ]);
    
    // Setup mock recording service
    when(mockRecordingService.initialize(
      onRecordingStateChanged: anyNamed('onRecordingStateChanged'),
      onRecordingTimerUpdated: anyNamed('onRecordingTimerUpdated'),
    )).thenAnswer((_) async => true);
    when(mockRecordingService.isRecording).thenReturn(false);
    
    // Setup mock lazy loading service
    when(mockLazyLoadingService.arFeaturesLoaded).thenReturn(true);
    when(mockLazyLoadingService.initializeARFeatures()).thenAnswer((_) async => true);
  });

  testWidgets('EnhancedARExploreScreen should render correctly', (WidgetTester tester) async {
    // Build our app and trigger a frame
    await tester.pumpWidget(
      ProviderScope(
        overrides: [
          arVoiceCommandServiceProvider.overrideWithValue(mockVoiceCommandService),
          arBackendServiceProvider.overrideWithValue(mockBackendService),
          arRecordingServiceProvider.overrideWithValue(mockRecordingService),
          arLazyLoadingServiceProvider.overrideWithValue(mockLazyLoadingService),
        ],
        child: const MaterialApp(
          home: EnhancedARExploreScreen(),
        ),
      ),
    );
    
    // Initial loading state
    expect(find.byType(CircularProgressIndicator), findsOneWidget);
    
    // Wait for initialization
    await tester.pump(const Duration(seconds: 3));
    
    // Should show AR view after initialization
    expect(find.text('AR View'), findsOneWidget);
    
    // Should show landmarks
    expect(find.text('Eiffel Tower'), findsOneWidget);
    expect(find.text('Statue of Liberty'), findsOneWidget);
  });

  testWidgets('EnhancedARExploreScreen should show voice command UI when activated', (WidgetTester tester) async {
    // Build our app and trigger a frame
    await tester.pumpWidget(
      ProviderScope(
        overrides: [
          arVoiceCommandServiceProvider.overrideWithValue(mockVoiceCommandService),
          arBackendServiceProvider.overrideWithValue(mockBackendService),
          arRecordingServiceProvider.overrideWithValue(mockRecordingService),
          arLazyLoadingServiceProvider.overrideWithValue(mockLazyLoadingService),
        ],
        child: const MaterialApp(
          home: EnhancedARExploreScreen(),
        ),
      ),
    );
    
    // Wait for initialization
    await tester.pump(const Duration(seconds: 3));
    
    // Find and tap the voice command button
    final voiceCommandButton = find.byIcon(Icons.mic);
    expect(voiceCommandButton, findsOneWidget);
    await tester.tap(voiceCommandButton);
    await tester.pump();
    
    // Should show voice command UI
    expect(find.text('Listening...'), findsOneWidget);
  });

  testWidgets('EnhancedARExploreScreen should show landmark info when tapped', (WidgetTester tester) async {
    // Build our app and trigger a frame
    await tester.pumpWidget(
      ProviderScope(
        overrides: [
          arVoiceCommandServiceProvider.overrideWithValue(mockVoiceCommandService),
          arBackendServiceProvider.overrideWithValue(mockBackendService),
          arRecordingServiceProvider.overrideWithValue(mockRecordingService),
          arLazyLoadingServiceProvider.overrideWithValue(mockLazyLoadingService),
        ],
        child: const MaterialApp(
          home: EnhancedARExploreScreen(),
        ),
      ),
    );
    
    // Wait for initialization
    await tester.pump(const Duration(seconds: 3));
    
    // Find and tap a landmark
    final landmarkWidget = find.text('Eiffel Tower');
    expect(landmarkWidget, findsOneWidget);
    await tester.tap(landmarkWidget);
    await tester.pump();
    
    // Should show landmark info
    expect(find.text('Famous landmark in Paris'), findsOneWidget);
  });

  testWidgets('EnhancedARExploreScreen should show recording controls when activated', (WidgetTester tester) async {
    // Build our app and trigger a frame
    await tester.pumpWidget(
      ProviderScope(
        overrides: [
          arVoiceCommandServiceProvider.overrideWithValue(mockVoiceCommandService),
          arBackendServiceProvider.overrideWithValue(mockBackendService),
          arRecordingServiceProvider.overrideWithValue(mockRecordingService),
          arLazyLoadingServiceProvider.overrideWithValue(mockLazyLoadingService),
        ],
        child: const MaterialApp(
          home: EnhancedARExploreScreen(),
        ),
      ),
    );
    
    // Wait for initialization
    await tester.pump(const Duration(seconds: 3));
    
    // Find and tap the recording button
    final recordingButton = find.byIcon(Icons.videocam);
    expect(recordingButton, findsOneWidget);
    await tester.tap(recordingButton);
    await tester.pump();
    
    // Should show recording controls
    expect(find.byIcon(Icons.stop), findsOneWidget);
  });

  testWidgets('EnhancedARExploreScreen should handle errors gracefully', (WidgetTester tester) async {
    // Setup mock backend service to throw an error
    when(mockBackendService.fetchNearbyLandmarks(
      latitude: anyNamed('latitude'),
      longitude: anyNamed('longitude'),
      radius: anyNamed('radius'),
    )).thenThrow(Exception('Network error'));
    
    // Build our app and trigger a frame
    await tester.pumpWidget(
      ProviderScope(
        overrides: [
          arVoiceCommandServiceProvider.overrideWithValue(mockVoiceCommandService),
          arBackendServiceProvider.overrideWithValue(mockBackendService),
          arRecordingServiceProvider.overrideWithValue(mockRecordingService),
          arLazyLoadingServiceProvider.overrideWithValue(mockLazyLoadingService),
        ],
        child: const MaterialApp(
          home: EnhancedARExploreScreen(),
        ),
      ),
    );
    
    // Wait for initialization
    await tester.pump(const Duration(seconds: 3));
    
    // Should show error message
    expect(find.textContaining('Error'), findsOneWidget);
    
    // Should show retry button
    expect(find.byIcon(Icons.refresh), findsOneWidget);
  });
}
