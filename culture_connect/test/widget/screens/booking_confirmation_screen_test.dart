import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:culture_connect/models/booking.dart';
import 'package:culture_connect/screens/booking_confirmation_screen.dart';
import 'package:culture_connect/services/booking_service.dart';

// Generate mocks for dependencies
@GenerateMocks([BookingService])
import 'booking_confirmation_screen_test.mocks.dart';

void main() {
  late MockBookingService mockBookingService;
  late Booking testBooking;

  setUp(() {
    mockBookingService = MockBookingService();

    // Create a test booking
    testBooking = Booking(
      id: 'booking-123',
      experienceId: 'exp-456',
      date: DateTime(2023, 10, 15),
      timeSlot: TimeSlot(
        startTime: DateTime(2023, 10, 15, 10, 0),
        endTime: DateTime(2023, 10, 15, 12, 0),
      ),
      participantCount: 2,
      totalAmount: 100.0,
      status: BookingStatus.pending,
      specialRequirements: 'Vegetarian food',
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );

    // Setup mock responses
    when(mockBookingService.updateBookingStatus(
      any,
      any,
      any,
    )).thenAnswer((_) async {
      return null;
    });

    when(mockBookingService.addBookingToCalendar(any))
        .thenAnswer((_) async => true);
  });

  Widget createWidgetUnderTest() {
    return ProviderScope(
      overrides: [
        // Override the booking service provider to use our mock
        bookingServiceProvider.overrideWithValue(mockBookingService),
      ],
      child: MaterialApp(
        home: BookingConfirmationScreen(
          booking: testBooking,
          transactionId: 'tx-789',
        ),
        routes: {
          '/home': (context) => const Scaffold(body: Text('Home Screen')),
          '/booking-management': (context) =>
              const Scaffold(body: Text('Booking Management')),
        },
      ),
    );
  }

  group('BookingConfirmationScreen Widget Tests', () {
    testWidgets('should display booking details correctly',
        (WidgetTester tester) async {
      // Arrange
      await tester.pumpWidget(createWidgetUnderTest());
      await tester.pumpAndSettle(); // Wait for any animations to complete

      // Assert
      expect(find.text('Booking Confirmed!'), findsOneWidget);
      expect(find.text('Booking #booking-123'), findsOneWidget);
      expect(find.text('Confirmed'), findsOneWidget);
      expect(find.text('Sunday, October 15, 2023'), findsOneWidget);
      expect(find.text('10:00 AM - 12:00 PM'), findsOneWidget);
      expect(find.text('Participants'), findsOneWidget);
      expect(find.text('2'), findsOneWidget);
      expect(find.text('Total Amount'), findsOneWidget);
      expect(find.text('\$100.00'), findsOneWidget);
      expect(find.text('Vegetarian food'), findsOneWidget);
    });

    testWidgets('should call updateBookingStatus on initialization',
        (WidgetTester tester) async {
      // Arrange
      await tester.pumpWidget(createWidgetUnderTest());
      await tester.pumpAndSettle();

      // Assert
      verify(mockBookingService.updateBookingStatus(
        testBooking.id,
        BookingStatus.confirmed,
        'tx-789',
      )).called(1);
    });

    testWidgets(
        'should navigate to home screen when back to home button is pressed',
        (WidgetTester tester) async {
      // Arrange
      await tester.pumpWidget(createWidgetUnderTest());
      await tester.pumpAndSettle();

      // Act
      await tester.tap(find.text('Back to Home'));
      await tester.pumpAndSettle();

      // Assert
      expect(find.text('Home Screen'), findsOneWidget);
    });

    testWidgets(
        'should navigate to booking management when view all bookings button is pressed',
        (WidgetTester tester) async {
      // Arrange
      await tester.pumpWidget(createWidgetUnderTest());
      await tester.pumpAndSettle();

      // Act
      await tester.tap(find.text('View All Bookings'));
      await tester.pumpAndSettle();

      // Assert
      expect(find.text('Booking Management'), findsOneWidget);
    });

    testWidgets(
        'should call addBookingToCalendar when add to calendar button is tapped',
        (WidgetTester tester) async {
      // Arrange
      await tester.pumpWidget(createWidgetUnderTest());
      await tester.pumpAndSettle();

      // Act
      await tester.tap(find.text('Add to Calendar'));
      await tester.pump(const Duration(milliseconds: 500));

      // Assert
      verify(mockBookingService.addBookingToCalendar(any)).called(1);

      // Should show success snackbar
      await tester.pump(const Duration(milliseconds: 500));
      expect(find.text('Booking added to calendar'), findsOneWidget);
    });

    testWidgets('should handle error in updateBookingStatus',
        (WidgetTester tester) async {
      // Arrange
      when(mockBookingService.updateBookingStatus(any, any, any))
          .thenThrow(Exception('Network error'));

      // Act
      await tester.pumpWidget(createWidgetUnderTest());
      await tester.pumpAndSettle();

      // Assert
      expect(find.text('Error'), findsOneWidget);
      expect(
          find.text(
              'Failed to update booking status: Exception: Network error'),
          findsOneWidget);
      expect(find.text('Retry'), findsOneWidget);
    });

    testWidgets('should retry updateBookingStatus when retry button is pressed',
        (WidgetTester tester) async {
      // Arrange
      when(mockBookingService.updateBookingStatus(any, any, any))
          .thenThrow(Exception('Network error'));

      await tester.pumpWidget(createWidgetUnderTest());
      await tester.pumpAndSettle();

      // Reset mock to succeed on retry
      when(mockBookingService.updateBookingStatus(any, any, any))
          .thenAnswer((_) async {
        return null;
      });

      // Act
      await tester.tap(find.text('Retry'));
      await tester.pumpAndSettle();

      // Assert
      verify(mockBookingService.updateBookingStatus(any, any, any)).called(2);
      expect(find.text('Booking Confirmed!'), findsOneWidget);
    });
  });
}
