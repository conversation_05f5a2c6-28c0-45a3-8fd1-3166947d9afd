// Mocks generated by Mockito 5.4.5 from annotations
// in culture_connect/test/widget/screens/enhanced_ar_explore_screen_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i4;
import 'dart:io' as _i8;
import 'dart:ui' as _i10;

import 'package:culture_connect/models/ar_model.dart' as _i7;
import 'package:culture_connect/models/landmark.dart' as _i6;
import 'package:culture_connect/services/ar_backend_service.dart' as _i5;
import 'package:culture_connect/services/ar_lazy_loading_service.dart' as _i12;
import 'package:culture_connect/services/ar_recording_service.dart' as _i9;
import 'package:culture_connect/services/ar_voice_command_service.dart' as _i2;
import 'package:flutter/material.dart' as _i11;
import 'package:mockito/mockito.dart' as _i1;
import 'package:mockito/src/dummies.dart' as _i3;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

/// A class which mocks [ARVoiceCommandService].
///
/// See the documentation for Mockito's code generation for more information.
class MockARVoiceCommandService extends _i1.Mock
    implements _i2.ARVoiceCommandService {
  MockARVoiceCommandService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  bool get isInitialized => (super.noSuchMethod(
        Invocation.getter(#isInitialized),
        returnValue: false,
      ) as bool);

  @override
  bool get isListening => (super.noSuchMethod(
        Invocation.getter(#isListening),
        returnValue: false,
      ) as bool);

  @override
  String get lastRecognizedWords => (super.noSuchMethod(
        Invocation.getter(#lastRecognizedWords),
        returnValue: _i3.dummyValue<String>(
          this,
          Invocation.getter(#lastRecognizedWords),
        ),
      ) as String);

  @override
  double get confidence => (super.noSuchMethod(
        Invocation.getter(#confidence),
        returnValue: 0.0,
      ) as double);

  @override
  List<String> get commandHistory => (super.noSuchMethod(
        Invocation.getter(#commandHistory),
        returnValue: <String>[],
      ) as List<String>);

  @override
  _i4.Future<bool> initialize() => (super.noSuchMethod(
        Invocation.method(
          #initialize,
          [],
        ),
        returnValue: _i4.Future<bool>.value(false),
      ) as _i4.Future<bool>);

  @override
  void registerCommand(
    String? command,
    Function? handler,
  ) =>
      super.noSuchMethod(
        Invocation.method(
          #registerCommand,
          [
            command,
            handler,
          ],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void registerCommands(Map<String, Function>? handlers) => super.noSuchMethod(
        Invocation.method(
          #registerCommands,
          [handlers],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void unregisterCommand(String? command) => super.noSuchMethod(
        Invocation.method(
          #unregisterCommand,
          [command],
        ),
        returnValueForMissingStub: null,
      );

  @override
  _i4.Future<bool> startListening() => (super.noSuchMethod(
        Invocation.method(
          #startListening,
          [],
        ),
        returnValue: _i4.Future<bool>.value(false),
      ) as _i4.Future<bool>);

  @override
  _i4.Future<bool> stopListening() => (super.noSuchMethod(
        Invocation.method(
          #stopListening,
          [],
        ),
        returnValue: _i4.Future<bool>.value(false),
      ) as _i4.Future<bool>);

  @override
  _i4.Future<bool> cancelListening() => (super.noSuchMethod(
        Invocation.method(
          #cancelListening,
          [],
        ),
        returnValue: _i4.Future<bool>.value(false),
      ) as _i4.Future<bool>);

  @override
  void addRecognitionListener(dynamic Function(String)? listener) =>
      super.noSuchMethod(
        Invocation.method(
          #addRecognitionListener,
          [listener],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void removeRecognitionListener(dynamic Function(String)? listener) =>
      super.noSuchMethod(
        Invocation.method(
          #removeRecognitionListener,
          [listener],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void addListeningStateListener(dynamic Function(bool)? listener) =>
      super.noSuchMethod(
        Invocation.method(
          #addListeningStateListener,
          [listener],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void removeListeningStateListener(dynamic Function(bool)? listener) =>
      super.noSuchMethod(
        Invocation.method(
          #removeListeningStateListener,
          [listener],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void addCommandExecutedListener(dynamic Function(String)? listener) =>
      super.noSuchMethod(
        Invocation.method(
          #addCommandExecutedListener,
          [listener],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void removeCommandExecutedListener(dynamic Function(String)? listener) =>
      super.noSuchMethod(
        Invocation.method(
          #removeCommandExecutedListener,
          [listener],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void dispose() => super.noSuchMethod(
        Invocation.method(
          #dispose,
          [],
        ),
        returnValueForMissingStub: null,
      );
}

/// A class which mocks [ARBackendService].
///
/// See the documentation for Mockito's code generation for more information.
class MockARBackendService extends _i1.Mock implements _i5.ARBackendService {
  MockARBackendService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i4.Future<void> initialize() => (super.noSuchMethod(
        Invocation.method(
          #initialize,
          [],
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);

  @override
  _i4.Future<List<_i6.Landmark>> getLandmarks({
    double? latitude,
    double? longitude,
    double? radius,
    List<String>? tags,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #getLandmarks,
          [],
          {
            #latitude: latitude,
            #longitude: longitude,
            #radius: radius,
            #tags: tags,
          },
        ),
        returnValue: _i4.Future<List<_i6.Landmark>>.value(<_i6.Landmark>[]),
      ) as _i4.Future<List<_i6.Landmark>>);

  @override
  _i4.Future<_i7.ARModel?> getARModel(String? modelId) => (super.noSuchMethod(
        Invocation.method(
          #getARModel,
          [modelId],
        ),
        returnValue: _i4.Future<_i7.ARModel?>.value(),
      ) as _i4.Future<_i7.ARModel?>);

  @override
  _i4.Future<_i8.File?> downloadARModelFile(String? modelUrl) =>
      (super.noSuchMethod(
        Invocation.method(
          #downloadARModelFile,
          [modelUrl],
        ),
        returnValue: _i4.Future<_i8.File?>.value(),
      ) as _i4.Future<_i8.File?>);

  @override
  _i4.Future<bool> uploadUserARContent({
    required String? name,
    required String? description,
    required _i8.File? modelFile,
    required _i8.File? imageFile,
    List<_i8.File>? textureFiles,
    Map<String, dynamic>? metadata,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #uploadUserARContent,
          [],
          {
            #name: name,
            #description: description,
            #modelFile: modelFile,
            #imageFile: imageFile,
            #textureFiles: textureFiles,
            #metadata: metadata,
          },
        ),
        returnValue: _i4.Future<bool>.value(false),
      ) as _i4.Future<bool>);

  @override
  _i4.Future<bool> shareARContent({
    required String? contentId,
    required List<String>? recipientIds,
    String? message,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #shareARContent,
          [],
          {
            #contentId: contentId,
            #recipientIds: recipientIds,
            #message: message,
          },
        ),
        returnValue: _i4.Future<bool>.value(false),
      ) as _i4.Future<bool>);
}

/// A class which mocks [ARRecordingService].
///
/// See the documentation for Mockito's code generation for more information.
class MockARRecordingService extends _i1.Mock
    implements _i9.ARRecordingService {
  MockARRecordingService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  int get recordingDurationInSeconds => (super.noSuchMethod(
        Invocation.getter(#recordingDurationInSeconds),
        returnValue: 0,
      ) as int);

  @override
  String get recordingDurationFormatted => (super.noSuchMethod(
        Invocation.getter(#recordingDurationFormatted),
        returnValue: _i3.dummyValue<String>(
          this,
          Invocation.getter(#recordingDurationFormatted),
        ),
      ) as String);

  @override
  bool get isRecording => (super.noSuchMethod(
        Invocation.getter(#isRecording),
        returnValue: false,
      ) as bool);

  @override
  bool get isPaused => (super.noSuchMethod(
        Invocation.getter(#isPaused),
        returnValue: false,
      ) as bool);

  @override
  List<_i8.File> get screenshots => (super.noSuchMethod(
        Invocation.getter(#screenshots),
        returnValue: <_i8.File>[],
      ) as List<_i8.File>);

  @override
  _i4.Future<void> initialize({
    _i10.VoidCallback? onRecordingStateChanged,
    _i11.ValueChanged<int>? onRecordingTimerUpdated,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #initialize,
          [],
          {
            #onRecordingStateChanged: onRecordingStateChanged,
            #onRecordingTimerUpdated: onRecordingTimerUpdated,
          },
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);

  @override
  _i4.Future<bool> startRecording() => (super.noSuchMethod(
        Invocation.method(
          #startRecording,
          [],
        ),
        returnValue: _i4.Future<bool>.value(false),
      ) as _i4.Future<bool>);

  @override
  void pauseRecording() => super.noSuchMethod(
        Invocation.method(
          #pauseRecording,
          [],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void resumeRecording() => super.noSuchMethod(
        Invocation.method(
          #resumeRecording,
          [],
        ),
        returnValueForMissingStub: null,
      );

  @override
  _i4.Future<bool> stopRecording() => (super.noSuchMethod(
        Invocation.method(
          #stopRecording,
          [],
        ),
        returnValue: _i4.Future<bool>.value(false),
      ) as _i4.Future<bool>);

  @override
  _i4.Future<_i8.File?> takeScreenshot() => (super.noSuchMethod(
        Invocation.method(
          #takeScreenshot,
          [],
        ),
        returnValue: _i4.Future<_i8.File?>.value(),
      ) as _i4.Future<_i8.File?>);

  @override
  _i4.Future<_i8.File?> recordVideo() => (super.noSuchMethod(
        Invocation.method(
          #recordVideo,
          [],
        ),
        returnValue: _i4.Future<_i8.File?>.value(),
      ) as _i4.Future<_i8.File?>);

  @override
  _i4.Future<bool> shareRecording({
    required String? title,
    required String? description,
    List<String>? tags,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #shareRecording,
          [],
          {
            #title: title,
            #description: description,
            #tags: tags,
          },
        ),
        returnValue: _i4.Future<bool>.value(false),
      ) as _i4.Future<bool>);

  @override
  _i4.Future<bool> saveRecording({
    required String? title,
    required String? description,
    List<String>? tags,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #saveRecording,
          [],
          {
            #title: title,
            #description: description,
            #tags: tags,
          },
        ),
        returnValue: _i4.Future<bool>.value(false),
      ) as _i4.Future<bool>);

  @override
  void dispose() => super.noSuchMethod(
        Invocation.method(
          #dispose,
          [],
        ),
        returnValueForMissingStub: null,
      );
}

/// A class which mocks [ARLazyLoadingService].
///
/// See the documentation for Mockito's code generation for more information.
class MockARLazyLoadingService extends _i1.Mock
    implements _i12.ARLazyLoadingService {
  MockARLazyLoadingService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  bool get arFeaturesLoaded => (super.noSuchMethod(
        Invocation.getter(#arFeaturesLoaded),
        returnValue: false,
      ) as bool);

  @override
  _i4.Future<bool> get arFeaturesLoadingFuture => (super.noSuchMethod(
        Invocation.getter(#arFeaturesLoadingFuture),
        returnValue: _i4.Future<bool>.value(false),
      ) as _i4.Future<bool>);

  @override
  _i4.Future<bool> initializeARFeatures() => (super.noSuchMethod(
        Invocation.method(
          #initializeARFeatures,
          [],
        ),
        returnValue: _i4.Future<bool>.value(false),
      ) as _i4.Future<bool>);

  @override
  _i4.Future<String?> getARModelFilePath(String? modelName) =>
      (super.noSuchMethod(
        Invocation.method(
          #getARModelFilePath,
          [modelName],
        ),
        returnValue: _i4.Future<String?>.value(),
      ) as _i4.Future<String?>);

  @override
  _i4.Future<String?> getARTextureFilePath(String? textureName) =>
      (super.noSuchMethod(
        Invocation.method(
          #getARTextureFilePath,
          [textureName],
        ),
        returnValue: _i4.Future<String?>.value(),
      ) as _i4.Future<String?>);
}
