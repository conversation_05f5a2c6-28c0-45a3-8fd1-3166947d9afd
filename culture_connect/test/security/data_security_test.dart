import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:culture_connect/services/secure_storage_service.dart';
import 'package:culture_connect/services/logging_service.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:culture_connect/models/travel/document/travel_documents.dart';
import 'dart:convert';

@GenerateMocks([FlutterSecureStorage])
void main() {
  late MockFlutterSecureStorage mockSecureStorage;
  late SecureStorageService secureStorageService;
  late LoggingService loggingService;

  setUp(() {
    mockSecureStorage = MockFlutterSecureStorage();
    loggingService = LoggingService();
    secureStorageService = SecureStorageService(
      secureStorage: mockSecureStorage,
      loggingService: loggingService,
    );
  });

  group('Data Security Tests', () {
    test('Should securely store sensitive data', () async {
      // Arrange
      const key = 'passport_data';
      final passport = Passport(
        id: '123',
        userId: 'user1',
        documentNumber: 'AB123456',
        issuingCountry: 'United States',
        expiryDate: DateTime(2030, 1, 1),
        issueDate: DateTime(2020, 1, 1),
        fullName: 'John Doe',
        birthDate: DateTime(1990, 1, 1),
        documentType: 'Passport',
      );
      final jsonData = jsonEncode(passport.toJson());

      // Act
      await secureStorageService.write(key, jsonData);

      // Assert
      verify(mockSecureStorage.write(key: key, value: jsonData)).called(1);
    });

    test('Should securely retrieve sensitive data', () async {
      // Arrange
      const key = 'passport_data';
      final passport = Passport(
        id: '123',
        userId: 'user1',
        documentNumber: 'AB123456',
        issuingCountry: 'United States',
        expiryDate: DateTime(2030, 1, 1),
        issueDate: DateTime(2020, 1, 1),
        fullName: 'John Doe',
        birthDate: DateTime(1990, 1, 1),
        documentType: 'Passport',
      );
      final jsonData = jsonEncode(passport.toJson());
      
      when(mockSecureStorage.read(key: key)).thenAnswer((_) async => jsonData);

      // Act
      final result = await secureStorageService.read(key);

      // Assert
      expect(result, jsonData);
      verify(mockSecureStorage.read(key: key)).called(1);
    });

    test('Should securely delete sensitive data', () async {
      // Arrange
      const key = 'passport_data';

      // Act
      await secureStorageService.delete(key);

      // Assert
      verify(mockSecureStorage.delete(key: key)).called(1);
    });

    test('Should handle non-existent data gracefully', () async {
      // Arrange
      const key = 'non_existent_key';
      when(mockSecureStorage.read(key: key)).thenAnswer((_) async => null);

      // Act
      final result = await secureStorageService.read(key);

      // Assert
      expect(result, isNull);
      verify(mockSecureStorage.read(key: key)).called(1);
    });

    test('Should handle storage exceptions gracefully', () async {
      // Arrange
      const key = 'error_key';
      when(mockSecureStorage.read(key: key)).thenThrow(Exception('Storage error'));

      // Act & Assert
      expect(
        () => secureStorageService.read(key),
        throwsA(isA<Exception>()),
      );
    });

    test('Should not store sensitive data in plain text', () async {
      // Arrange
      const key = 'credit_card';
      const plainTextData = '{"number":"****************","cvv":"123","expiry":"12/25"}';
      
      // Act
      await secureStorageService.write(key, plainTextData);
      
      // Assert
      // Verify that we're using secure storage and not storing in plain text
      verify(mockSecureStorage.write(key: key, value: plainTextData)).called(1);
      // In a real implementation, we would verify encryption, but here we're just
      // verifying that we're using the secure storage mechanism
    });
  });
}
