import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:culture_connect/services/auth_service.dart';
import 'package:culture_connect/services/logging_service.dart';
import 'package:culture_connect/models/user_model.dart';
import 'package:firebase_auth/firebase_auth.dart';

@GenerateMocks([FirebaseAuth, User, UserCredential])
void main() {
  late MockFirebaseAuth mockFirebaseAuth;
  late AuthService authService;
  late LoggingService loggingService;
  late MockUser mockUser;
  late MockUserCredential mockUserCredential;

  setUp(() {
    mockFirebaseAuth = MockFirebaseAuth();
    loggingService = LoggingService();
    authService = AuthService(
      firebaseAuth: mockFirebaseAuth,
      loggingService: loggingService,
    );
    mockUser = MockUser();
    mockUserCredential = MockUserCredential();
    
    // Set up default mock behavior
    when(mockUser.uid).thenReturn('test-user-id');
    when(mockUser.email).thenReturn('<EMAIL>');
    when(mockUser.displayName).thenReturn('Test User');
    when(mockUserCredential.user).thenReturn(mockUser);
  });

  group('Authentication Security Tests', () {
    test('Should prevent login with weak password', () async {
      // Arrange
      when(mockFirebaseAuth.createUserWithEmailAndPassword(
        email: '<EMAIL>',
        password: 'weak',
      )).thenThrow(FirebaseAuthException(
        code: 'weak-password',
        message: 'The password provided is too weak.',
      ));

      // Act & Assert
      expect(
        () => authService.signUp('<EMAIL>', 'weak', 'Test User'),
        throwsA(isA<FirebaseAuthException>()),
      );
    });

    test('Should prevent login with invalid email', () async {
      // Arrange
      when(mockFirebaseAuth.createUserWithEmailAndPassword(
        email: 'invalid-email',
        password: 'Password123!',
      )).thenThrow(FirebaseAuthException(
        code: 'invalid-email',
        message: 'The email address is badly formatted.',
      ));

      // Act & Assert
      expect(
        () => authService.signUp('invalid-email', 'Password123!', 'Test User'),
        throwsA(isA<FirebaseAuthException>()),
      );
    });

    test('Should prevent login with already used email', () async {
      // Arrange
      when(mockFirebaseAuth.createUserWithEmailAndPassword(
        email: '<EMAIL>',
        password: 'Password123!',
      )).thenThrow(FirebaseAuthException(
        code: 'email-already-in-use',
        message: 'The email address is already in use by another account.',
      ));

      // Act & Assert
      expect(
        () => authService.signUp('<EMAIL>', 'Password123!', 'Test User'),
        throwsA(isA<FirebaseAuthException>()),
      );
    });

    test('Should prevent login with wrong password', () async {
      // Arrange
      when(mockFirebaseAuth.signInWithEmailAndPassword(
        email: '<EMAIL>',
        password: 'wrong-password',
      )).thenThrow(FirebaseAuthException(
        code: 'wrong-password',
        message: 'The password is invalid or the user does not have a password.',
      ));

      // Act & Assert
      expect(
        () => authService.signIn('<EMAIL>', 'wrong-password'),
        throwsA(isA<FirebaseAuthException>()),
      );
    });

    test('Should prevent login with non-existent user', () async {
      // Arrange
      when(mockFirebaseAuth.signInWithEmailAndPassword(
        email: '<EMAIL>',
        password: 'Password123!',
      )).thenThrow(FirebaseAuthException(
        code: 'user-not-found',
        message: 'There is no user record corresponding to this identifier.',
      ));

      // Act & Assert
      expect(
        () => authService.signIn('<EMAIL>', 'Password123!'),
        throwsA(isA<FirebaseAuthException>()),
      );
    });

    test('Should prevent too many login attempts', () async {
      // Arrange
      when(mockFirebaseAuth.signInWithEmailAndPassword(
        email: '<EMAIL>',
        password: 'Password123!',
      )).thenThrow(FirebaseAuthException(
        code: 'too-many-requests',
        message: 'Access to this account has been temporarily disabled due to many failed login attempts.',
      ));

      // Act & Assert
      expect(
        () => authService.signIn('<EMAIL>', 'Password123!'),
        throwsA(isA<FirebaseAuthException>()),
      );
    });

    test('Should handle token expiration', () async {
      // Arrange
      when(mockFirebaseAuth.signInWithEmailAndPassword(
        email: '<EMAIL>',
        password: 'Password123!',
      )).thenThrow(FirebaseAuthException(
        code: 'user-token-expired',
        message: 'The user\'s credential is no longer valid. The user must sign in again.',
      ));

      // Act & Assert
      expect(
        () => authService.signIn('<EMAIL>', 'Password123!'),
        throwsA(isA<FirebaseAuthException>()),
      );
    });
  });
}
