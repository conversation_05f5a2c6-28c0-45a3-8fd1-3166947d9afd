# Contributing to CultureConnect

Thank you for your interest in contributing to CultureConnect! This document provides guidelines and instructions for contributing to the project.

## Table of Contents

1. [Code of Conduct](#code-of-conduct)
2. [Getting Started](#getting-started)
3. [Development Workflow](#development-workflow)
4. [Pull Request Process](#pull-request-process)
5. [Coding Standards](#coding-standards)
6. [Testing Guidelines](#testing-guidelines)
7. [Documentation](#documentation)
8. [Issue Reporting](#issue-reporting)

## Code of Conduct

We expect all contributors to follow our [Code of Conduct](CODE_OF_CONDUCT.md). Please read it before participating.

## Getting Started

### Prerequisites

- Flutter SDK 3.19.0 or higher
- Dart SDK 3.0.0 or higher
- Android Studio / Xcode
- Git

### Setup

1. Fork the repository on GitHub
2. Clone your fork locally:
   ```bash
   git clone https://github.com/YOUR-USERNAME/mobile-app.git
   cd culture_connect
   ```
3. Add the upstream repository as a remote:
   ```bash
   git remote add upstream https://github.com/cultureconnect/mobile-app.git
   ```
4. Install dependencies:
   ```bash
   flutter pub get
   ```
5. Set up pre-commit hooks:
   ```bash
   dart run scripts/setup_hooks.dart
   ```

## Development Workflow

1. Create a new branch from `develop` for your feature or bugfix:
   ```bash
   git checkout develop
   git pull upstream develop
   git checkout -b feature/your-feature-name
   ```

2. Make your changes, following our [coding standards](#coding-standards)

3. Write or update tests as needed

4. Run tests locally:
   ```bash
   ./test/run_tests.sh
   ```

5. Commit your changes with a descriptive commit message:
   ```bash
   git commit -m "Add feature: your feature description"
   ```

6. Push your branch to your fork:
   ```bash
   git push origin feature/your-feature-name
   ```

7. Create a pull request from your fork to the `develop` branch of the main repository

## Pull Request Process

1. Ensure your PR includes tests for new functionality
2. Update documentation as needed
3. Make sure all CI checks pass
4. Request a review from at least one maintainer
5. Address any feedback from reviewers
6. Once approved, a maintainer will merge your PR

### PR Title Format

Use one of these prefixes for your PR title:

- `feat:` for new features
- `fix:` for bug fixes
- `docs:` for documentation changes
- `style:` for formatting changes
- `refactor:` for code refactoring
- `test:` for adding or updating tests
- `chore:` for build process or tooling changes

Example: `feat: Add voice command support for AR navigation`

## Coding Standards

We follow the [Effective Dart](https://dart.dev/guides/language/effective-dart) style guide with some additional conventions:

### Formatting

- Use 2 spaces for indentation
- Maximum line length of 80 characters
- Run `flutter format .` before committing

### Naming Conventions

- `snake_case` for file names
- `PascalCase` for class names
- `camelCase` for variables, functions, and methods
- Prefix private members with `_`

### Architecture

- Follow the existing architecture pattern (see [Architecture Guide](docs/developer_guides/architecture_guide.md))
- Keep UI separate from business logic
- Use Riverpod for state management
- Follow the unidirectional data flow pattern

### Comments and Documentation

- Document all public APIs
- Use `///` for documentation comments
- Write meaningful comments for complex logic
- See our [Code Documentation Guide](docs/code_documentation_guide.md)

## Testing Guidelines

We aim for high test coverage and follow these testing principles:

### Test Types

- **Unit Tests**: Test individual units of code in isolation
- **Widget Tests**: Test UI components
- **Integration Tests**: Test multiple components working together
- **Performance Tests**: Test application performance

### Test Structure

Follow the Arrange-Act-Assert pattern:

```dart
test('description of the test', () {
  // Arrange: Set up test data and conditions
  final service = MockService();
  when(service.getData()).thenReturn(['item']);
  
  // Act: Perform the action being tested
  final result = service.getData();
  
  // Assert: Verify the result
  expect(result, ['item']);
});
```

### Mocking

- Use `mockito` for mocking dependencies
- Create mock classes with `@GenerateMocks` annotation
- Keep mocks in the same file or in a dedicated mocks directory

## Documentation

Good documentation is essential. Please update documentation when you:

- Add new features
- Change existing functionality
- Fix bugs that might affect user behavior

Documentation types:

1. **Code Documentation**: Comments and doc strings in code
2. **API Documentation**: Generated from code comments
3. **User Guides**: Markdown files in `docs/user_guides/`
4. **Developer Guides**: Markdown files in `docs/developer_guides/`

## Issue Reporting

### Bug Reports

When reporting a bug, please include:

1. A clear, descriptive title
2. Steps to reproduce the issue
3. Expected behavior
4. Actual behavior
5. Screenshots (if applicable)
6. Device information (OS, Flutter version, etc.)
7. Any relevant logs or error messages

### Feature Requests

When requesting a feature, please include:

1. A clear, descriptive title
2. A detailed description of the proposed feature
3. The problem it solves
4. Any alternatives you've considered
5. Mockups or examples (if applicable)

## License

By contributing to CultureConnect, you agree that your contributions will be licensed under the project's [MIT License](LICENSE).
