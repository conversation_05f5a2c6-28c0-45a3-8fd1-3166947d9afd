default_platform(:ios)

platform :ios do
  desc "Build IPA file"
  lane :build_ipa do
    gym(
      workspace: "Runner.xcworkspace",
      scheme: "Runner",
      export_method: "development",
      output_directory: "build",
      output_name: "Runner.ipa",
      skip_codesigning: true
    )
  end

  desc "Deploy to TestFlight"
  lane :beta do
    # Ensure we're on the right branch
    ensure_git_branch(
      branch: ['main', 'develop'],
      match_message: "You need to be on main or develop branch to deploy to TestFlight"
    )

    # Get the version number from the tag or environment
    version = ENV["VERSION"] || last_git_tag.gsub('v', '')

    # Increment build number
    build_number = ENV["BUILD_NUMBER"] || (latest_testflight_build_number(
      app_identifier: "com.cultureconnect.app",
      username: <PERSON><PERSON><PERSON>["APPLE_ID"],
      team_id: ENV["TEAM_ID"]
    ) + 1).to_s

    increment_build_number(
      build_number: build_number,
      xcodeproj: "Runner.xcodeproj"
    )

    # Update version number if provided
    if version
      increment_version_number(
        version_number: version,
        xcodeproj: "Runner.xcodeproj"
      )
    end

    # Ensure that your app is properly signed
    update_code_signing_settings(
      use_automatic_signing: false,
      path: "Runner.xcodeproj",
      team_id: ENV["TEAM_ID"],
      code_sign_identity: "iPhone Distribution",
      profile_name: ENV["PROVISIONING_PROFILE_NAME"]
    )

    # Build the app
    gym(
      workspace: "Runner.xcworkspace",
      scheme: "Runner",
      export_method: "app-store",
      output_directory: "build",
      output_name: "Runner.ipa",
      include_bitcode: true,
      include_symbols: true
    )

    # Upload to TestFlight
    pilot(
      skip_waiting_for_build_processing: true,
      apple_id: ENV["APPLE_ID"],
      app_identifier: "com.cultureconnect.app",
      ipa: "build/Runner.ipa",
      changelog: "New version with exciting features and bug fixes!"
    )

    # Create a backup of the IPA
    backup_ipa(version: version, build: build_number)

    # Notify the team
    if ENV["SLACK_URL"]
      slack(
        message: "Successfully uploaded a new build (#{version} - #{build_number}) to TestFlight!",
        success: true,
        slack_url: ENV["SLACK_URL"]
      )
    end
  end

  desc "Deploy to App Store"
  lane :release do
    # Ensure we're on the right branch
    ensure_git_branch(
      branch: 'main',
      match_message: "You need to be on main branch to deploy to App Store"
    )

    # Get the version number from the tag or environment
    version = ENV["VERSION"] || last_git_tag.gsub('v', '')

    # Ensure that your app is properly signed
    update_code_signing_settings(
      use_automatic_signing: false,
      path: "Runner.xcodeproj",
      team_id: ENV["TEAM_ID"],
      code_sign_identity: "iPhone Distribution",
      profile_name: ENV["PROVISIONING_PROFILE_NAME"]
    )

    # Build the app
    gym(
      workspace: "Runner.xcworkspace",
      scheme: "Runner",
      export_method: "app-store",
      output_directory: "build",
      output_name: "Runner.ipa",
      include_bitcode: true,
      include_symbols: true
    )

    # Upload to App Store
    deliver(
      submit_for_review: true,
      force: true,
      skip_screenshots: true,
      skip_metadata: false,
      app_identifier: "com.cultureconnect.app",
      ipa: "build/Runner.ipa",
      automatic_release: true,
      submission_information: {
        add_id_info_uses_idfa: false,
        export_compliance_uses_encryption: false,
        export_compliance_encryption_updated: false,
        content_rights_contains_third_party_content: false
      },
      precheck_include_in_app_purchases: false
    )

    # Create a backup of the IPA
    backup_ipa(version: version, build: get_build_number(xcodeproj: "Runner.xcodeproj"))

    # Notify the team
    if ENV["SLACK_URL"]
      slack(
        message: "Successfully submitted version #{version} to the App Store for review!",
        success: true,
        slack_url: ENV["SLACK_URL"]
      )
    end
  end

  desc "Create a new beta build and submit for review"
  lane :beta_and_review do
    beta
    release
  end

  desc "Rollback to the previous version"
  lane :rollback do
    # Get the previous version
    previous_version = sh("git describe --abbrev=0 --tags `git rev-list --tags --skip=1 --max-count=1`").strip.gsub('v', '')

    # Checkout the previous version
    sh("git checkout v#{previous_version}")

    # Build and upload the previous version
    beta

    # Notify the team
    if ENV["SLACK_URL"]
      slack(
        message: "Rolled back to version #{previous_version}!",
        success: true,
        slack_url: ENV["SLACK_URL"]
      )
    end
  end

  desc "Backup the IPA file"
  private_lane :backup_ipa do |options|
    version = options[:version] || "unknown"
    build = options[:build] || "unknown"

    # Create a directory for backups if it doesn't exist
    sh("mkdir -p ../build/backups")

    # Copy the IPA to the backups directory with version and build number
    sh("cp ../build/Runner.ipa ../build/backups/Runner_#{version}_#{build}.ipa")

    # If we have AWS credentials, upload to S3
    if ENV["AWS_ACCESS_KEY_ID"] && ENV["AWS_SECRET_ACCESS_KEY"] && ENV["AWS_S3_BUCKET"]
      aws_s3(
        access_key: ENV["AWS_ACCESS_KEY_ID"],
        secret_access_key: ENV["AWS_SECRET_ACCESS_KEY"],
        bucket: ENV["AWS_S3_BUCKET"],
        region: ENV["AWS_REGION"] || "us-east-1",
        ipa: "../build/Runner.ipa",
        path: "backups/ios/#{version}/Runner_#{version}_#{build}.ipa",
        upload_metadata: true
      )
    end
  end
end
