PODS:
  - abseil/algorithm (1.20240116.2):
    - abseil/algorithm/algorithm (= 1.20240116.2)
    - abseil/algorithm/container (= 1.20240116.2)
  - abseil/algorithm/algorithm (1.20240116.2):
    - abseil/base/config
    - abseil/xcprivacy
  - abseil/algorithm/container (1.20240116.2):
    - abseil/algorithm/algorithm
    - abseil/base/core_headers
    - abseil/base/nullability
    - abseil/meta/type_traits
    - abseil/xcprivacy
  - abseil/base (1.20240116.2):
    - abseil/base/atomic_hook (= 1.20240116.2)
    - abseil/base/base (= 1.20240116.2)
    - abseil/base/base_internal (= 1.20240116.2)
    - abseil/base/config (= 1.20240116.2)
    - abseil/base/core_headers (= 1.20240116.2)
    - abseil/base/cycleclock_internal (= 1.20240116.2)
    - abseil/base/dynamic_annotations (= 1.20240116.2)
    - abseil/base/endian (= 1.20240116.2)
    - abseil/base/errno_saver (= 1.20240116.2)
    - abseil/base/fast_type_id (= 1.20240116.2)
    - abseil/base/log_severity (= 1.20240116.2)
    - abseil/base/malloc_internal (= 1.20240116.2)
    - abseil/base/no_destructor (= 1.20240116.2)
    - abseil/base/nullability (= 1.20240116.2)
    - abseil/base/prefetch (= 1.20240116.2)
    - abseil/base/pretty_function (= 1.20240116.2)
    - abseil/base/raw_logging_internal (= 1.20240116.2)
    - abseil/base/spinlock_wait (= 1.20240116.2)
    - abseil/base/strerror (= 1.20240116.2)
    - abseil/base/throw_delegate (= 1.20240116.2)
  - abseil/base/atomic_hook (1.20240116.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/xcprivacy
  - abseil/base/base (1.20240116.2):
    - abseil/base/atomic_hook
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/cycleclock_internal
    - abseil/base/dynamic_annotations
    - abseil/base/log_severity
    - abseil/base/nullability
    - abseil/base/raw_logging_internal
    - abseil/base/spinlock_wait
    - abseil/meta/type_traits
    - abseil/xcprivacy
  - abseil/base/base_internal (1.20240116.2):
    - abseil/base/config
    - abseil/meta/type_traits
    - abseil/xcprivacy
  - abseil/base/config (1.20240116.2):
    - abseil/xcprivacy
  - abseil/base/core_headers (1.20240116.2):
    - abseil/base/config
    - abseil/xcprivacy
  - abseil/base/cycleclock_internal (1.20240116.2):
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/xcprivacy
  - abseil/base/dynamic_annotations (1.20240116.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/xcprivacy
  - abseil/base/endian (1.20240116.2):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/nullability
    - abseil/xcprivacy
  - abseil/base/errno_saver (1.20240116.2):
    - abseil/base/config
    - abseil/xcprivacy
  - abseil/base/fast_type_id (1.20240116.2):
    - abseil/base/config
    - abseil/xcprivacy
  - abseil/base/log_severity (1.20240116.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/xcprivacy
  - abseil/base/malloc_internal (1.20240116.2):
    - abseil/base/base
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/dynamic_annotations
    - abseil/base/raw_logging_internal
    - abseil/xcprivacy
  - abseil/base/no_destructor (1.20240116.2):
    - abseil/base/config
    - abseil/xcprivacy
  - abseil/base/nullability (1.20240116.2):
    - abseil/base/core_headers
    - abseil/meta/type_traits
    - abseil/xcprivacy
  - abseil/base/prefetch (1.20240116.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/xcprivacy
  - abseil/base/pretty_function (1.20240116.2):
    - abseil/xcprivacy
  - abseil/base/raw_logging_internal (1.20240116.2):
    - abseil/base/atomic_hook
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/errno_saver
    - abseil/base/log_severity
    - abseil/xcprivacy
  - abseil/base/spinlock_wait (1.20240116.2):
    - abseil/base/base_internal
    - abseil/base/core_headers
    - abseil/base/errno_saver
    - abseil/xcprivacy
  - abseil/base/strerror (1.20240116.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/errno_saver
    - abseil/xcprivacy
  - abseil/base/throw_delegate (1.20240116.2):
    - abseil/base/config
    - abseil/base/raw_logging_internal
    - abseil/xcprivacy
  - abseil/cleanup/cleanup (1.20240116.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/cleanup/cleanup_internal
    - abseil/xcprivacy
  - abseil/cleanup/cleanup_internal (1.20240116.2):
    - abseil/base/base_internal
    - abseil/base/core_headers
    - abseil/utility/utility
    - abseil/xcprivacy
  - abseil/container/common (1.20240116.2):
    - abseil/meta/type_traits
    - abseil/types/optional
    - abseil/xcprivacy
  - abseil/container/common_policy_traits (1.20240116.2):
    - abseil/meta/type_traits
    - abseil/xcprivacy
  - abseil/container/compressed_tuple (1.20240116.2):
    - abseil/utility/utility
    - abseil/xcprivacy
  - abseil/container/container_memory (1.20240116.2):
    - abseil/base/config
    - abseil/memory/memory
    - abseil/meta/type_traits
    - abseil/utility/utility
    - abseil/xcprivacy
  - abseil/container/fixed_array (1.20240116.2):
    - abseil/algorithm/algorithm
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/dynamic_annotations
    - abseil/base/throw_delegate
    - abseil/container/compressed_tuple
    - abseil/memory/memory
    - abseil/xcprivacy
  - abseil/container/flat_hash_map (1.20240116.2):
    - abseil/algorithm/container
    - abseil/base/core_headers
    - abseil/container/container_memory
    - abseil/container/hash_function_defaults
    - abseil/container/raw_hash_map
    - abseil/memory/memory
    - abseil/xcprivacy
  - abseil/container/flat_hash_set (1.20240116.2):
    - abseil/algorithm/container
    - abseil/base/core_headers
    - abseil/container/container_memory
    - abseil/container/hash_function_defaults
    - abseil/container/raw_hash_set
    - abseil/memory/memory
    - abseil/xcprivacy
  - abseil/container/hash_function_defaults (1.20240116.2):
    - abseil/base/config
    - abseil/hash/hash
    - abseil/strings/cord
    - abseil/strings/strings
    - abseil/xcprivacy
  - abseil/container/hash_policy_traits (1.20240116.2):
    - abseil/container/common_policy_traits
    - abseil/meta/type_traits
    - abseil/xcprivacy
  - abseil/container/hashtable_debug_hooks (1.20240116.2):
    - abseil/base/config
    - abseil/xcprivacy
  - abseil/container/hashtablez_sampler (1.20240116.2):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/raw_logging_internal
    - abseil/debugging/stacktrace
    - abseil/memory/memory
    - abseil/profiling/exponential_biased
    - abseil/profiling/sample_recorder
    - abseil/synchronization/synchronization
    - abseil/time/time
    - abseil/utility/utility
    - abseil/xcprivacy
  - abseil/container/inlined_vector (1.20240116.2):
    - abseil/algorithm/algorithm
    - abseil/base/core_headers
    - abseil/base/throw_delegate
    - abseil/container/inlined_vector_internal
    - abseil/memory/memory
    - abseil/meta/type_traits
    - abseil/xcprivacy
  - abseil/container/inlined_vector_internal (1.20240116.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/container/compressed_tuple
    - abseil/memory/memory
    - abseil/meta/type_traits
    - abseil/types/span
    - abseil/xcprivacy
  - abseil/container/layout (1.20240116.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/debugging/demangle_internal
    - abseil/meta/type_traits
    - abseil/strings/strings
    - abseil/types/span
    - abseil/utility/utility
    - abseil/xcprivacy
  - abseil/container/raw_hash_map (1.20240116.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/throw_delegate
    - abseil/container/container_memory
    - abseil/container/raw_hash_set
    - abseil/xcprivacy
  - abseil/container/raw_hash_set (1.20240116.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/dynamic_annotations
    - abseil/base/endian
    - abseil/base/prefetch
    - abseil/base/raw_logging_internal
    - abseil/container/common
    - abseil/container/compressed_tuple
    - abseil/container/container_memory
    - abseil/container/hash_policy_traits
    - abseil/container/hashtable_debug_hooks
    - abseil/container/hashtablez_sampler
    - abseil/hash/hash
    - abseil/memory/memory
    - abseil/meta/type_traits
    - abseil/numeric/bits
    - abseil/utility/utility
    - abseil/xcprivacy
  - abseil/crc/cpu_detect (1.20240116.2):
    - abseil/base/base
    - abseil/base/config
    - abseil/xcprivacy
  - abseil/crc/crc32c (1.20240116.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/endian
    - abseil/base/prefetch
    - abseil/crc/cpu_detect
    - abseil/crc/crc_internal
    - abseil/crc/non_temporal_memcpy
    - abseil/strings/str_format
    - abseil/strings/strings
    - abseil/xcprivacy
  - abseil/crc/crc_cord_state (1.20240116.2):
    - abseil/base/config
    - abseil/crc/crc32c
    - abseil/numeric/bits
    - abseil/strings/strings
    - abseil/xcprivacy
  - abseil/crc/crc_internal (1.20240116.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/endian
    - abseil/base/prefetch
    - abseil/base/raw_logging_internal
    - abseil/crc/cpu_detect
    - abseil/memory/memory
    - abseil/numeric/bits
    - abseil/xcprivacy
  - abseil/crc/non_temporal_arm_intrinsics (1.20240116.2):
    - abseil/base/config
    - abseil/xcprivacy
  - abseil/crc/non_temporal_memcpy (1.20240116.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/crc/non_temporal_arm_intrinsics
    - abseil/xcprivacy
  - abseil/debugging/debugging_internal (1.20240116.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/dynamic_annotations
    - abseil/base/errno_saver
    - abseil/base/raw_logging_internal
    - abseil/xcprivacy
  - abseil/debugging/demangle_internal (1.20240116.2):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/xcprivacy
  - abseil/debugging/stacktrace (1.20240116.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/dynamic_annotations
    - abseil/base/raw_logging_internal
    - abseil/debugging/debugging_internal
    - abseil/xcprivacy
  - abseil/debugging/symbolize (1.20240116.2):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/dynamic_annotations
    - abseil/base/malloc_internal
    - abseil/base/raw_logging_internal
    - abseil/debugging/debugging_internal
    - abseil/debugging/demangle_internal
    - abseil/strings/strings
    - abseil/xcprivacy
  - abseil/flags/commandlineflag (1.20240116.2):
    - abseil/base/config
    - abseil/base/fast_type_id
    - abseil/flags/commandlineflag_internal
    - abseil/strings/strings
    - abseil/types/optional
    - abseil/xcprivacy
  - abseil/flags/commandlineflag_internal (1.20240116.2):
    - abseil/base/config
    - abseil/base/fast_type_id
    - abseil/xcprivacy
  - abseil/flags/config (1.20240116.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/flags/path_util
    - abseil/flags/program_name
    - abseil/strings/strings
    - abseil/synchronization/synchronization
    - abseil/xcprivacy
  - abseil/flags/flag (1.20240116.2):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/flags/config
    - abseil/flags/flag_internal
    - abseil/flags/reflection
    - abseil/strings/strings
    - abseil/xcprivacy
  - abseil/flags/flag_internal (1.20240116.2):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/dynamic_annotations
    - abseil/flags/commandlineflag
    - abseil/flags/commandlineflag_internal
    - abseil/flags/config
    - abseil/flags/marshalling
    - abseil/flags/reflection
    - abseil/memory/memory
    - abseil/meta/type_traits
    - abseil/strings/strings
    - abseil/synchronization/synchronization
    - abseil/utility/utility
    - abseil/xcprivacy
  - abseil/flags/marshalling (1.20240116.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/log_severity
    - abseil/numeric/int128
    - abseil/strings/str_format
    - abseil/strings/strings
    - abseil/types/optional
    - abseil/xcprivacy
  - abseil/flags/path_util (1.20240116.2):
    - abseil/base/config
    - abseil/strings/strings
    - abseil/xcprivacy
  - abseil/flags/private_handle_accessor (1.20240116.2):
    - abseil/base/config
    - abseil/flags/commandlineflag
    - abseil/flags/commandlineflag_internal
    - abseil/strings/strings
    - abseil/xcprivacy
  - abseil/flags/program_name (1.20240116.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/flags/path_util
    - abseil/strings/strings
    - abseil/synchronization/synchronization
    - abseil/xcprivacy
  - abseil/flags/reflection (1.20240116.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/no_destructor
    - abseil/container/flat_hash_map
    - abseil/flags/commandlineflag
    - abseil/flags/commandlineflag_internal
    - abseil/flags/config
    - abseil/flags/private_handle_accessor
    - abseil/strings/strings
    - abseil/synchronization/synchronization
    - abseil/xcprivacy
  - abseil/functional/any_invocable (1.20240116.2):
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/meta/type_traits
    - abseil/utility/utility
    - abseil/xcprivacy
  - abseil/functional/bind_front (1.20240116.2):
    - abseil/base/base_internal
    - abseil/container/compressed_tuple
    - abseil/meta/type_traits
    - abseil/utility/utility
    - abseil/xcprivacy
  - abseil/functional/function_ref (1.20240116.2):
    - abseil/base/base_internal
    - abseil/base/core_headers
    - abseil/functional/any_invocable
    - abseil/meta/type_traits
    - abseil/xcprivacy
  - abseil/hash/city (1.20240116.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/endian
    - abseil/xcprivacy
  - abseil/hash/hash (1.20240116.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/endian
    - abseil/container/fixed_array
    - abseil/functional/function_ref
    - abseil/hash/city
    - abseil/hash/low_level_hash
    - abseil/meta/type_traits
    - abseil/numeric/bits
    - abseil/numeric/int128
    - abseil/strings/strings
    - abseil/types/optional
    - abseil/types/variant
    - abseil/utility/utility
    - abseil/xcprivacy
  - abseil/hash/low_level_hash (1.20240116.2):
    - abseil/base/config
    - abseil/base/endian
    - abseil/base/prefetch
    - abseil/numeric/int128
    - abseil/xcprivacy
  - abseil/memory (1.20240116.2):
    - abseil/memory/memory (= 1.20240116.2)
  - abseil/memory/memory (1.20240116.2):
    - abseil/base/core_headers
    - abseil/meta/type_traits
    - abseil/xcprivacy
  - abseil/meta (1.20240116.2):
    - abseil/meta/type_traits (= 1.20240116.2)
  - abseil/meta/type_traits (1.20240116.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/xcprivacy
  - abseil/numeric/bits (1.20240116.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/xcprivacy
  - abseil/numeric/int128 (1.20240116.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/numeric/bits
    - abseil/xcprivacy
  - abseil/numeric/representation (1.20240116.2):
    - abseil/base/config
    - abseil/xcprivacy
  - abseil/profiling/exponential_biased (1.20240116.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/xcprivacy
  - abseil/profiling/sample_recorder (1.20240116.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/synchronization/synchronization
    - abseil/time/time
    - abseil/xcprivacy
  - abseil/random/bit_gen_ref (1.20240116.2):
    - abseil/base/core_headers
    - abseil/base/fast_type_id
    - abseil/meta/type_traits
    - abseil/random/internal/distribution_caller
    - abseil/random/internal/fast_uniform_bits
    - abseil/random/random
    - abseil/xcprivacy
  - abseil/random/distributions (1.20240116.2):
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/meta/type_traits
    - abseil/numeric/bits
    - abseil/random/internal/distribution_caller
    - abseil/random/internal/fast_uniform_bits
    - abseil/random/internal/fastmath
    - abseil/random/internal/generate_real
    - abseil/random/internal/iostream_state_saver
    - abseil/random/internal/traits
    - abseil/random/internal/uniform_helper
    - abseil/random/internal/wide_multiply
    - abseil/strings/strings
    - abseil/xcprivacy
  - abseil/random/internal/distribution_caller (1.20240116.2):
    - abseil/base/config
    - abseil/base/fast_type_id
    - abseil/utility/utility
    - abseil/xcprivacy
  - abseil/random/internal/fast_uniform_bits (1.20240116.2):
    - abseil/base/config
    - abseil/meta/type_traits
    - abseil/random/internal/traits
    - abseil/xcprivacy
  - abseil/random/internal/fastmath (1.20240116.2):
    - abseil/numeric/bits
    - abseil/xcprivacy
  - abseil/random/internal/generate_real (1.20240116.2):
    - abseil/meta/type_traits
    - abseil/numeric/bits
    - abseil/random/internal/fastmath
    - abseil/random/internal/traits
    - abseil/xcprivacy
  - abseil/random/internal/iostream_state_saver (1.20240116.2):
    - abseil/meta/type_traits
    - abseil/numeric/int128
    - abseil/xcprivacy
  - abseil/random/internal/nonsecure_base (1.20240116.2):
    - abseil/base/core_headers
    - abseil/container/inlined_vector
    - abseil/meta/type_traits
    - abseil/random/internal/pool_urbg
    - abseil/random/internal/salted_seed_seq
    - abseil/random/internal/seed_material
    - abseil/types/span
    - abseil/xcprivacy
  - abseil/random/internal/pcg_engine (1.20240116.2):
    - abseil/base/config
    - abseil/meta/type_traits
    - abseil/numeric/bits
    - abseil/numeric/int128
    - abseil/random/internal/fastmath
    - abseil/random/internal/iostream_state_saver
    - abseil/xcprivacy
  - abseil/random/internal/platform (1.20240116.2):
    - abseil/base/config
    - abseil/xcprivacy
  - abseil/random/internal/pool_urbg (1.20240116.2):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/endian
    - abseil/base/raw_logging_internal
    - abseil/random/internal/randen
    - abseil/random/internal/seed_material
    - abseil/random/internal/traits
    - abseil/random/seed_gen_exception
    - abseil/types/span
    - abseil/xcprivacy
  - abseil/random/internal/randen (1.20240116.2):
    - abseil/base/raw_logging_internal
    - abseil/random/internal/platform
    - abseil/random/internal/randen_hwaes
    - abseil/random/internal/randen_slow
    - abseil/xcprivacy
  - abseil/random/internal/randen_engine (1.20240116.2):
    - abseil/base/endian
    - abseil/meta/type_traits
    - abseil/random/internal/iostream_state_saver
    - abseil/random/internal/randen
    - abseil/xcprivacy
  - abseil/random/internal/randen_hwaes (1.20240116.2):
    - abseil/base/config
    - abseil/random/internal/platform
    - abseil/random/internal/randen_hwaes_impl
    - abseil/xcprivacy
  - abseil/random/internal/randen_hwaes_impl (1.20240116.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/numeric/int128
    - abseil/random/internal/platform
    - abseil/xcprivacy
  - abseil/random/internal/randen_slow (1.20240116.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/endian
    - abseil/numeric/int128
    - abseil/random/internal/platform
    - abseil/xcprivacy
  - abseil/random/internal/salted_seed_seq (1.20240116.2):
    - abseil/container/inlined_vector
    - abseil/meta/type_traits
    - abseil/random/internal/seed_material
    - abseil/types/optional
    - abseil/types/span
    - abseil/xcprivacy
  - abseil/random/internal/seed_material (1.20240116.2):
    - abseil/base/core_headers
    - abseil/base/dynamic_annotations
    - abseil/base/raw_logging_internal
    - abseil/random/internal/fast_uniform_bits
    - abseil/strings/strings
    - abseil/types/optional
    - abseil/types/span
    - abseil/xcprivacy
  - abseil/random/internal/traits (1.20240116.2):
    - abseil/base/config
    - abseil/numeric/bits
    - abseil/numeric/int128
    - abseil/xcprivacy
  - abseil/random/internal/uniform_helper (1.20240116.2):
    - abseil/base/config
    - abseil/meta/type_traits
    - abseil/numeric/int128
    - abseil/random/internal/traits
    - abseil/xcprivacy
  - abseil/random/internal/wide_multiply (1.20240116.2):
    - abseil/base/config
    - abseil/numeric/bits
    - abseil/numeric/int128
    - abseil/random/internal/traits
    - abseil/xcprivacy
  - abseil/random/random (1.20240116.2):
    - abseil/random/distributions
    - abseil/random/internal/nonsecure_base
    - abseil/random/internal/pcg_engine
    - abseil/random/internal/pool_urbg
    - abseil/random/internal/randen_engine
    - abseil/random/seed_sequences
    - abseil/xcprivacy
  - abseil/random/seed_gen_exception (1.20240116.2):
    - abseil/base/config
    - abseil/xcprivacy
  - abseil/random/seed_sequences (1.20240116.2):
    - abseil/base/config
    - abseil/random/internal/pool_urbg
    - abseil/random/internal/salted_seed_seq
    - abseil/random/internal/seed_material
    - abseil/random/seed_gen_exception
    - abseil/types/span
    - abseil/xcprivacy
  - abseil/status/status (1.20240116.2):
    - abseil/base/atomic_hook
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/no_destructor
    - abseil/base/nullability
    - abseil/base/raw_logging_internal
    - abseil/base/strerror
    - abseil/container/inlined_vector
    - abseil/debugging/stacktrace
    - abseil/debugging/symbolize
    - abseil/functional/function_ref
    - abseil/memory/memory
    - abseil/strings/cord
    - abseil/strings/str_format
    - abseil/strings/strings
    - abseil/types/optional
    - abseil/types/span
    - abseil/xcprivacy
  - abseil/status/statusor (1.20240116.2):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/nullability
    - abseil/base/raw_logging_internal
    - abseil/meta/type_traits
    - abseil/status/status
    - abseil/strings/has_ostream_operator
    - abseil/strings/str_format
    - abseil/strings/strings
    - abseil/types/variant
    - abseil/utility/utility
    - abseil/xcprivacy
  - abseil/strings/charset (1.20240116.2):
    - abseil/base/core_headers
    - abseil/strings/string_view
    - abseil/xcprivacy
  - abseil/strings/cord (1.20240116.2):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/endian
    - abseil/base/nullability
    - abseil/base/raw_logging_internal
    - abseil/container/inlined_vector
    - abseil/crc/crc32c
    - abseil/crc/crc_cord_state
    - abseil/functional/function_ref
    - abseil/meta/type_traits
    - abseil/numeric/bits
    - abseil/strings/cord_internal
    - abseil/strings/cordz_functions
    - abseil/strings/cordz_info
    - abseil/strings/cordz_statistics
    - abseil/strings/cordz_update_scope
    - abseil/strings/cordz_update_tracker
    - abseil/strings/internal
    - abseil/strings/strings
    - abseil/types/optional
    - abseil/types/span
    - abseil/xcprivacy
  - abseil/strings/cord_internal (1.20240116.2):
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/endian
    - abseil/base/raw_logging_internal
    - abseil/base/throw_delegate
    - abseil/container/compressed_tuple
    - abseil/container/container_memory
    - abseil/container/inlined_vector
    - abseil/container/layout
    - abseil/crc/crc_cord_state
    - abseil/functional/function_ref
    - abseil/meta/type_traits
    - abseil/strings/strings
    - abseil/types/span
    - abseil/xcprivacy
  - abseil/strings/cordz_functions (1.20240116.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/raw_logging_internal
    - abseil/profiling/exponential_biased
    - abseil/xcprivacy
  - abseil/strings/cordz_handle (1.20240116.2):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/raw_logging_internal
    - abseil/synchronization/synchronization
    - abseil/xcprivacy
  - abseil/strings/cordz_info (1.20240116.2):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/raw_logging_internal
    - abseil/container/inlined_vector
    - abseil/debugging/stacktrace
    - abseil/strings/cord_internal
    - abseil/strings/cordz_functions
    - abseil/strings/cordz_handle
    - abseil/strings/cordz_statistics
    - abseil/strings/cordz_update_tracker
    - abseil/synchronization/synchronization
    - abseil/time/time
    - abseil/types/span
    - abseil/xcprivacy
  - abseil/strings/cordz_statistics (1.20240116.2):
    - abseil/base/config
    - abseil/strings/cordz_update_tracker
    - abseil/xcprivacy
  - abseil/strings/cordz_update_scope (1.20240116.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/strings/cord_internal
    - abseil/strings/cordz_info
    - abseil/strings/cordz_update_tracker
    - abseil/xcprivacy
  - abseil/strings/cordz_update_tracker (1.20240116.2):
    - abseil/base/config
    - abseil/xcprivacy
  - abseil/strings/has_ostream_operator (1.20240116.2):
    - abseil/base/config
    - abseil/xcprivacy
  - abseil/strings/internal (1.20240116.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/endian
    - abseil/base/raw_logging_internal
    - abseil/meta/type_traits
    - abseil/xcprivacy
  - abseil/strings/str_format (1.20240116.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/nullability
    - abseil/strings/str_format_internal
    - abseil/strings/string_view
    - abseil/types/span
    - abseil/xcprivacy
  - abseil/strings/str_format_internal (1.20240116.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/container/fixed_array
    - abseil/container/inlined_vector
    - abseil/functional/function_ref
    - abseil/meta/type_traits
    - abseil/numeric/bits
    - abseil/numeric/int128
    - abseil/numeric/representation
    - abseil/strings/strings
    - abseil/types/optional
    - abseil/types/span
    - abseil/utility/utility
    - abseil/xcprivacy
  - abseil/strings/string_view (1.20240116.2):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/nullability
    - abseil/base/throw_delegate
    - abseil/xcprivacy
  - abseil/strings/strings (1.20240116.2):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/endian
    - abseil/base/nullability
    - abseil/base/raw_logging_internal
    - abseil/base/throw_delegate
    - abseil/memory/memory
    - abseil/meta/type_traits
    - abseil/numeric/bits
    - abseil/numeric/int128
    - abseil/strings/charset
    - abseil/strings/internal
    - abseil/strings/string_view
    - abseil/xcprivacy
  - abseil/synchronization/graphcycles_internal (1.20240116.2):
    - abseil/base/base
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/malloc_internal
    - abseil/base/raw_logging_internal
    - abseil/xcprivacy
  - abseil/synchronization/kernel_timeout_internal (1.20240116.2):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/raw_logging_internal
    - abseil/time/time
    - abseil/xcprivacy
  - abseil/synchronization/synchronization (1.20240116.2):
    - abseil/base/atomic_hook
    - abseil/base/base
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/dynamic_annotations
    - abseil/base/malloc_internal
    - abseil/base/raw_logging_internal
    - abseil/debugging/stacktrace
    - abseil/debugging/symbolize
    - abseil/synchronization/graphcycles_internal
    - abseil/synchronization/kernel_timeout_internal
    - abseil/time/time
    - abseil/xcprivacy
  - abseil/time (1.20240116.2):
    - abseil/time/internal (= 1.20240116.2)
    - abseil/time/time (= 1.20240116.2)
  - abseil/time/internal (1.20240116.2):
    - abseil/time/internal/cctz (= 1.20240116.2)
  - abseil/time/internal/cctz (1.20240116.2):
    - abseil/time/internal/cctz/civil_time (= 1.20240116.2)
    - abseil/time/internal/cctz/time_zone (= 1.20240116.2)
  - abseil/time/internal/cctz/civil_time (1.20240116.2):
    - abseil/base/config
    - abseil/xcprivacy
  - abseil/time/internal/cctz/time_zone (1.20240116.2):
    - abseil/base/config
    - abseil/time/internal/cctz/civil_time
    - abseil/xcprivacy
  - abseil/time/time (1.20240116.2):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/raw_logging_internal
    - abseil/numeric/int128
    - abseil/strings/strings
    - abseil/time/internal/cctz/civil_time
    - abseil/time/internal/cctz/time_zone
    - abseil/types/optional
    - abseil/xcprivacy
  - abseil/types (1.20240116.2):
    - abseil/types/any (= 1.20240116.2)
    - abseil/types/bad_any_cast (= 1.20240116.2)
    - abseil/types/bad_any_cast_impl (= 1.20240116.2)
    - abseil/types/bad_optional_access (= 1.20240116.2)
    - abseil/types/bad_variant_access (= 1.20240116.2)
    - abseil/types/compare (= 1.20240116.2)
    - abseil/types/optional (= 1.20240116.2)
    - abseil/types/span (= 1.20240116.2)
    - abseil/types/variant (= 1.20240116.2)
  - abseil/types/any (1.20240116.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/fast_type_id
    - abseil/meta/type_traits
    - abseil/types/bad_any_cast
    - abseil/utility/utility
    - abseil/xcprivacy
  - abseil/types/bad_any_cast (1.20240116.2):
    - abseil/base/config
    - abseil/types/bad_any_cast_impl
    - abseil/xcprivacy
  - abseil/types/bad_any_cast_impl (1.20240116.2):
    - abseil/base/config
    - abseil/base/raw_logging_internal
    - abseil/xcprivacy
  - abseil/types/bad_optional_access (1.20240116.2):
    - abseil/base/config
    - abseil/base/raw_logging_internal
    - abseil/xcprivacy
  - abseil/types/bad_variant_access (1.20240116.2):
    - abseil/base/config
    - abseil/base/raw_logging_internal
    - abseil/xcprivacy
  - abseil/types/compare (1.20240116.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/meta/type_traits
    - abseil/xcprivacy
  - abseil/types/optional (1.20240116.2):
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/nullability
    - abseil/memory/memory
    - abseil/meta/type_traits
    - abseil/types/bad_optional_access
    - abseil/utility/utility
    - abseil/xcprivacy
  - abseil/types/span (1.20240116.2):
    - abseil/algorithm/algorithm
    - abseil/base/core_headers
    - abseil/base/nullability
    - abseil/base/throw_delegate
    - abseil/meta/type_traits
    - abseil/xcprivacy
  - abseil/types/variant (1.20240116.2):
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/meta/type_traits
    - abseil/types/bad_variant_access
    - abseil/utility/utility
    - abseil/xcprivacy
  - abseil/utility/utility (1.20240116.2):
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/meta/type_traits
    - abseil/xcprivacy
  - abseil/xcprivacy (1.20240116.2)
  - AppCheckCore (10.19.2):
    - GoogleUtilities/Environment (~> 7.13)
    - GoogleUtilities/UserDefaults (~> 7.13)
    - PromisesObjC (~> 2.3)
  - BoringSSL-GRPC (0.0.32):
    - BoringSSL-GRPC/Implementation (= 0.0.32)
    - BoringSSL-GRPC/Interface (= 0.0.32)
  - BoringSSL-GRPC/Implementation (0.0.32):
    - BoringSSL-GRPC/Interface (= 0.0.32)
  - BoringSSL-GRPC/Interface (0.0.32)
  - cloud_firestore (4.17.5):
    - Firebase/Firestore (= 10.25.0)
    - firebase_core
    - Flutter
  - connectivity_plus (0.0.1):
    - Flutter
    - ReachabilitySwift
  - DKImagePickerController/Core (4.3.9):
    - DKImagePickerController/ImageDataManager
    - DKImagePickerController/Resource
  - DKImagePickerController/ImageDataManager (4.3.9)
  - DKImagePickerController/PhotoGallery (4.3.9):
    - DKImagePickerController/Core
    - DKPhotoGallery
  - DKImagePickerController/Resource (4.3.9)
  - DKPhotoGallery (0.0.19):
    - DKPhotoGallery/Core (= 0.0.19)
    - DKPhotoGallery/Model (= 0.0.19)
    - DKPhotoGallery/Preview (= 0.0.19)
    - DKPhotoGallery/Resource (= 0.0.19)
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Core (0.0.19):
    - DKPhotoGallery/Model
    - DKPhotoGallery/Preview
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Model (0.0.19):
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Preview (0.0.19):
    - DKPhotoGallery/Model
    - DKPhotoGallery/Resource
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Resource (0.0.19):
    - SDWebImage
    - SwiftyGif
  - file_picker (0.0.1):
    - DKImagePickerController/PhotoGallery
    - Flutter
  - Firebase/Analytics (10.25.0):
    - Firebase/Core
  - Firebase/Auth (10.25.0):
    - Firebase/CoreOnly
    - FirebaseAuth (~> 10.25.0)
  - Firebase/Core (10.25.0):
    - Firebase/CoreOnly
    - FirebaseAnalytics (~> 10.25.0)
  - Firebase/CoreOnly (10.25.0):
    - FirebaseCore (= 10.25.0)
  - Firebase/Crashlytics (10.25.0):
    - Firebase/CoreOnly
    - FirebaseCrashlytics (~> 10.25.0)
  - Firebase/Firestore (10.25.0):
    - Firebase/CoreOnly
    - FirebaseFirestore (~> 10.25.0)
  - Firebase/Messaging (10.25.0):
    - Firebase/CoreOnly
    - FirebaseMessaging (~> 10.25.0)
  - firebase_analytics (10.10.7):
    - Firebase/Analytics (= 10.25.0)
    - firebase_core
    - Flutter
  - firebase_app_check (0.2.2):
    - Firebase/CoreOnly (~> 10.25.0)
    - firebase_core
    - FirebaseAppCheck (~> 10.25.0-beta)
    - Flutter
  - firebase_auth (4.16.0):
    - Firebase/Auth (= 10.25.0)
    - firebase_core
    - Flutter
  - firebase_core (2.32.0):
    - Firebase/CoreOnly (= 10.25.0)
    - Flutter
  - firebase_crashlytics (3.5.7):
    - Firebase/Crashlytics (= 10.25.0)
    - firebase_core
    - Flutter
  - firebase_messaging (14.7.10):
    - Firebase/Messaging (= 10.25.0)
    - firebase_core
    - Flutter
  - FirebaseAnalytics (10.25.0):
    - FirebaseAnalytics/AdIdSupport (= 10.25.0)
    - FirebaseCore (~> 10.0)
    - FirebaseInstallations (~> 10.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30911.0, >= 2.30908.0)
  - FirebaseAnalytics/AdIdSupport (10.25.0):
    - FirebaseCore (~> 10.0)
    - FirebaseInstallations (~> 10.0)
    - GoogleAppMeasurement (= 10.25.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30911.0, >= 2.30908.0)
  - FirebaseAppCheck (10.25.0):
    - AppCheckCore (~> 10.19)
    - FirebaseAppCheckInterop (~> 10.17)
    - FirebaseCore (~> 10.0)
    - GoogleUtilities/Environment (~> 7.13)
    - GoogleUtilities/UserDefaults (~> 7.13)
    - PromisesObjC (~> 2.1)
  - FirebaseAppCheckInterop (10.29.0)
  - FirebaseAuth (10.25.0):
    - FirebaseAppCheckInterop (~> 10.17)
    - FirebaseCore (~> 10.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.8)
    - GoogleUtilities/Environment (~> 7.8)
    - GTMSessionFetcher/Core (< 4.0, >= 2.1)
    - RecaptchaInterop (~> 100.0)
  - FirebaseCore (10.25.0):
    - FirebaseCoreInternal (~> 10.0)
    - GoogleUtilities/Environment (~> 7.12)
    - GoogleUtilities/Logger (~> 7.12)
  - FirebaseCoreExtension (10.29.0):
    - FirebaseCore (~> 10.0)
  - FirebaseCoreInternal (10.29.0):
    - "GoogleUtilities/NSData+zlib (~> 7.8)"
  - FirebaseCrashlytics (10.25.0):
    - FirebaseCore (~> 10.5)
    - FirebaseInstallations (~> 10.0)
    - FirebaseRemoteConfigInterop (~> 10.23)
    - FirebaseSessions (~> 10.5)
    - GoogleDataTransport (~> 9.2)
    - GoogleUtilities/Environment (~> 7.8)
    - nanopb (< 2.30911.0, >= 2.30908.0)
    - PromisesObjC (~> 2.1)
  - FirebaseFirestore (10.25.0):
    - FirebaseCore (~> 10.0)
    - FirebaseCoreExtension (~> 10.0)
    - FirebaseFirestoreInternal (= 10.25.0)
    - FirebaseSharedSwift (~> 10.0)
  - FirebaseFirestoreInternal (10.25.0):
    - abseil/algorithm (~> 1.20240116.1)
    - abseil/base (~> 1.20240116.1)
    - abseil/container/flat_hash_map (~> 1.20240116.1)
    - abseil/memory (~> 1.20240116.1)
    - abseil/meta (~> 1.20240116.1)
    - abseil/strings/strings (~> 1.20240116.1)
    - abseil/time (~> 1.20240116.1)
    - abseil/types (~> 1.20240116.1)
    - FirebaseAppCheckInterop (~> 10.17)
    - FirebaseCore (~> 10.0)
    - "gRPC-C++ (~> 1.62.0)"
    - gRPC-Core (~> 1.62.0)
    - leveldb-library (~> 1.22)
    - nanopb (< 2.30911.0, >= 2.30908.0)
  - FirebaseInstallations (10.29.0):
    - FirebaseCore (~> 10.0)
    - GoogleUtilities/Environment (~> 7.8)
    - GoogleUtilities/UserDefaults (~> 7.8)
    - PromisesObjC (~> 2.1)
  - FirebaseMessaging (10.25.0):
    - FirebaseCore (~> 10.0)
    - FirebaseInstallations (~> 10.0)
    - GoogleDataTransport (~> 9.3)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.8)
    - GoogleUtilities/Environment (~> 7.8)
    - GoogleUtilities/Reachability (~> 7.8)
    - GoogleUtilities/UserDefaults (~> 7.8)
    - nanopb (< 2.30911.0, >= 2.30908.0)
  - FirebaseRemoteConfigInterop (10.29.0)
  - FirebaseSessions (10.29.0):
    - FirebaseCore (~> 10.5)
    - FirebaseCoreExtension (~> 10.0)
    - FirebaseInstallations (~> 10.0)
    - GoogleDataTransport (~> 9.2)
    - GoogleUtilities/Environment (~> 7.13)
    - GoogleUtilities/UserDefaults (~> 7.13)
    - nanopb (< 2.30911.0, >= 2.30908.0)
    - PromisesSwift (~> 2.1)
  - FirebaseSharedSwift (10.29.0)
  - Flutter (1.0.0)
  - flutter_local_notifications (0.0.1):
    - Flutter
  - flutter_secure_storage (6.0.0):
    - Flutter
  - geolocator_apple (1.2.0):
    - Flutter
  - Google-Maps-iOS-Utils (5.0.0):
    - GoogleMaps (~> 8.0)
  - google_maps_flutter_ios (0.0.1):
    - Flutter
    - Google-Maps-iOS-Utils (< 7.0, >= 5.0)
    - GoogleMaps (< 10.0, >= 8.4)
  - GoogleAppMeasurement (10.25.0):
    - GoogleAppMeasurement/AdIdSupport (= 10.25.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30911.0, >= 2.30908.0)
  - GoogleAppMeasurement/AdIdSupport (10.25.0):
    - GoogleAppMeasurement/WithoutAdIdSupport (= 10.25.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30911.0, >= 2.30908.0)
  - GoogleAppMeasurement/WithoutAdIdSupport (10.25.0):
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30911.0, >= 2.30908.0)
  - GoogleDataTransport (9.4.1):
    - GoogleUtilities/Environment (~> 7.7)
    - nanopb (< 2.30911.0, >= 2.30908.0)
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleMaps (8.4.0):
    - GoogleMaps/Maps (= 8.4.0)
  - GoogleMaps/Base (8.4.0)
  - GoogleMaps/Maps (8.4.0):
    - GoogleMaps/Base
  - GoogleUtilities/AppDelegateSwizzler (7.13.3):
    - GoogleUtilities/Environment
    - GoogleUtilities/Logger
    - GoogleUtilities/Network
    - GoogleUtilities/Privacy
  - GoogleUtilities/Environment (7.13.3):
    - GoogleUtilities/Privacy
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleUtilities/Logger (7.13.3):
    - GoogleUtilities/Environment
    - GoogleUtilities/Privacy
  - GoogleUtilities/MethodSwizzler (7.13.3):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/Network (7.13.3):
    - GoogleUtilities/Logger
    - "GoogleUtilities/NSData+zlib"
    - GoogleUtilities/Privacy
    - GoogleUtilities/Reachability
  - "GoogleUtilities/NSData+zlib (7.13.3)":
    - GoogleUtilities/Privacy
  - GoogleUtilities/Privacy (7.13.3)
  - GoogleUtilities/Reachability (7.13.3):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/UserDefaults (7.13.3):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - "gRPC-C++ (1.62.5)":
    - "gRPC-C++/Implementation (= 1.62.5)"
    - "gRPC-C++/Interface (= 1.62.5)"
  - "gRPC-C++/Implementation (1.62.5)":
    - abseil/algorithm/container (~> 1.20240116.2)
    - abseil/base/base (~> 1.20240116.2)
    - abseil/base/config (~> 1.20240116.2)
    - abseil/base/core_headers (~> 1.20240116.2)
    - abseil/cleanup/cleanup (~> 1.20240116.2)
    - abseil/container/flat_hash_map (~> 1.20240116.2)
    - abseil/container/flat_hash_set (~> 1.20240116.2)
    - abseil/container/inlined_vector (~> 1.20240116.2)
    - abseil/flags/flag (~> 1.20240116.2)
    - abseil/flags/marshalling (~> 1.20240116.2)
    - abseil/functional/any_invocable (~> 1.20240116.2)
    - abseil/functional/bind_front (~> 1.20240116.2)
    - abseil/functional/function_ref (~> 1.20240116.2)
    - abseil/hash/hash (~> 1.20240116.2)
    - abseil/memory/memory (~> 1.20240116.2)
    - abseil/meta/type_traits (~> 1.20240116.2)
    - abseil/random/bit_gen_ref (~> 1.20240116.2)
    - abseil/random/distributions (~> 1.20240116.2)
    - abseil/random/random (~> 1.20240116.2)
    - abseil/status/status (~> 1.20240116.2)
    - abseil/status/statusor (~> 1.20240116.2)
    - abseil/strings/cord (~> 1.20240116.2)
    - abseil/strings/str_format (~> 1.20240116.2)
    - abseil/strings/strings (~> 1.20240116.2)
    - abseil/synchronization/synchronization (~> 1.20240116.2)
    - abseil/time/time (~> 1.20240116.2)
    - abseil/types/optional (~> 1.20240116.2)
    - abseil/types/span (~> 1.20240116.2)
    - abseil/types/variant (~> 1.20240116.2)
    - abseil/utility/utility (~> 1.20240116.2)
    - "gRPC-C++/Interface (= 1.62.5)"
    - "gRPC-C++/Privacy (= 1.62.5)"
    - gRPC-Core (= 1.62.5)
  - "gRPC-C++/Interface (1.62.5)"
  - "gRPC-C++/Privacy (1.62.5)"
  - gRPC-Core (1.62.5):
    - gRPC-Core/Implementation (= 1.62.5)
    - gRPC-Core/Interface (= 1.62.5)
  - gRPC-Core/Implementation (1.62.5):
    - abseil/algorithm/container (~> 1.20240116.2)
    - abseil/base/base (~> 1.20240116.2)
    - abseil/base/config (~> 1.20240116.2)
    - abseil/base/core_headers (~> 1.20240116.2)
    - abseil/cleanup/cleanup (~> 1.20240116.2)
    - abseil/container/flat_hash_map (~> 1.20240116.2)
    - abseil/container/flat_hash_set (~> 1.20240116.2)
    - abseil/container/inlined_vector (~> 1.20240116.2)
    - abseil/flags/flag (~> 1.20240116.2)
    - abseil/flags/marshalling (~> 1.20240116.2)
    - abseil/functional/any_invocable (~> 1.20240116.2)
    - abseil/functional/bind_front (~> 1.20240116.2)
    - abseil/functional/function_ref (~> 1.20240116.2)
    - abseil/hash/hash (~> 1.20240116.2)
    - abseil/memory/memory (~> 1.20240116.2)
    - abseil/meta/type_traits (~> 1.20240116.2)
    - abseil/random/bit_gen_ref (~> 1.20240116.2)
    - abseil/random/distributions (~> 1.20240116.2)
    - abseil/random/random (~> 1.20240116.2)
    - abseil/status/status (~> 1.20240116.2)
    - abseil/status/statusor (~> 1.20240116.2)
    - abseil/strings/cord (~> 1.20240116.2)
    - abseil/strings/str_format (~> 1.20240116.2)
    - abseil/strings/strings (~> 1.20240116.2)
    - abseil/synchronization/synchronization (~> 1.20240116.2)
    - abseil/time/time (~> 1.20240116.2)
    - abseil/types/optional (~> 1.20240116.2)
    - abseil/types/span (~> 1.20240116.2)
    - abseil/types/variant (~> 1.20240116.2)
    - abseil/utility/utility (~> 1.20240116.2)
    - BoringSSL-GRPC (= 0.0.32)
    - gRPC-Core/Interface (= 1.62.5)
    - gRPC-Core/Privacy (= 1.62.5)
  - gRPC-Core/Interface (1.62.5)
  - gRPC-Core/Privacy (1.62.5)
  - GTMSessionFetcher/Core (3.5.0)
  - image_picker_ios (0.0.1):
    - Flutter
  - leveldb-library (1.22.6)
  - nanopb (2.30910.0):
    - nanopb/decode (= 2.30910.0)
    - nanopb/encode (= 2.30910.0)
  - nanopb/decode (2.30910.0)
  - nanopb/encode (2.30910.0)
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - permission_handler_apple (9.3.0):
    - Flutter
  - PromisesObjC (2.4.0)
  - PromisesSwift (2.4.0):
    - PromisesObjC (= 2.4.0)
  - ReachabilitySwift (5.2.4)
  - RecaptchaInterop (100.0.0)
  - SDWebImage (5.20.0):
    - SDWebImage/Core (= 5.20.0)
  - SDWebImage/Core (5.20.0)
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - smart_auth (0.0.1):
    - Flutter
  - sqflite_darwin (0.0.4):
    - Flutter
    - FlutterMacOS
  - SwiftyGif (5.4.5)

DEPENDENCIES:
  - cloud_firestore (from `.symlinks/plugins/cloud_firestore/ios`)
  - connectivity_plus (from `.symlinks/plugins/connectivity_plus/ios`)
  - file_picker (from `.symlinks/plugins/file_picker/ios`)
  - firebase_analytics (from `.symlinks/plugins/firebase_analytics/ios`)
  - firebase_app_check (from `.symlinks/plugins/firebase_app_check/ios`)
  - firebase_auth (from `.symlinks/plugins/firebase_auth/ios`)
  - firebase_core (from `.symlinks/plugins/firebase_core/ios`)
  - firebase_crashlytics (from `.symlinks/plugins/firebase_crashlytics/ios`)
  - firebase_messaging (from `.symlinks/plugins/firebase_messaging/ios`)
  - Flutter (from `Flutter`)
  - flutter_local_notifications (from `.symlinks/plugins/flutter_local_notifications/ios`)
  - flutter_secure_storage (from `.symlinks/plugins/flutter_secure_storage/ios`)
  - geolocator_apple (from `.symlinks/plugins/geolocator_apple/ios`)
  - google_maps_flutter_ios (from `.symlinks/plugins/google_maps_flutter_ios/ios`)
  - image_picker_ios (from `.symlinks/plugins/image_picker_ios/ios`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/darwin`)
  - permission_handler_apple (from `.symlinks/plugins/permission_handler_apple/ios`)
  - shared_preferences_foundation (from `.symlinks/plugins/shared_preferences_foundation/darwin`)
  - smart_auth (from `.symlinks/plugins/smart_auth/ios`)
  - sqflite_darwin (from `.symlinks/plugins/sqflite_darwin/darwin`)

SPEC REPOS:
  trunk:
    - abseil
    - AppCheckCore
    - BoringSSL-GRPC
    - DKImagePickerController
    - DKPhotoGallery
    - Firebase
    - FirebaseAnalytics
    - FirebaseAppCheck
    - FirebaseAppCheckInterop
    - FirebaseAuth
    - FirebaseCore
    - FirebaseCoreExtension
    - FirebaseCoreInternal
    - FirebaseCrashlytics
    - FirebaseFirestore
    - FirebaseFirestoreInternal
    - FirebaseInstallations
    - FirebaseMessaging
    - FirebaseRemoteConfigInterop
    - FirebaseSessions
    - FirebaseSharedSwift
    - Google-Maps-iOS-Utils
    - GoogleAppMeasurement
    - GoogleDataTransport
    - GoogleMaps
    - GoogleUtilities
    - "gRPC-C++"
    - gRPC-Core
    - GTMSessionFetcher
    - leveldb-library
    - nanopb
    - PromisesObjC
    - PromisesSwift
    - ReachabilitySwift
    - RecaptchaInterop
    - SDWebImage
    - SwiftyGif

EXTERNAL SOURCES:
  cloud_firestore:
    :path: ".symlinks/plugins/cloud_firestore/ios"
  connectivity_plus:
    :path: ".symlinks/plugins/connectivity_plus/ios"
  file_picker:
    :path: ".symlinks/plugins/file_picker/ios"
  firebase_analytics:
    :path: ".symlinks/plugins/firebase_analytics/ios"
  firebase_app_check:
    :path: ".symlinks/plugins/firebase_app_check/ios"
  firebase_auth:
    :path: ".symlinks/plugins/firebase_auth/ios"
  firebase_core:
    :path: ".symlinks/plugins/firebase_core/ios"
  firebase_crashlytics:
    :path: ".symlinks/plugins/firebase_crashlytics/ios"
  firebase_messaging:
    :path: ".symlinks/plugins/firebase_messaging/ios"
  Flutter:
    :path: Flutter
  flutter_local_notifications:
    :path: ".symlinks/plugins/flutter_local_notifications/ios"
  flutter_secure_storage:
    :path: ".symlinks/plugins/flutter_secure_storage/ios"
  geolocator_apple:
    :path: ".symlinks/plugins/geolocator_apple/ios"
  google_maps_flutter_ios:
    :path: ".symlinks/plugins/google_maps_flutter_ios/ios"
  image_picker_ios:
    :path: ".symlinks/plugins/image_picker_ios/ios"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/darwin"
  permission_handler_apple:
    :path: ".symlinks/plugins/permission_handler_apple/ios"
  shared_preferences_foundation:
    :path: ".symlinks/plugins/shared_preferences_foundation/darwin"
  smart_auth:
    :path: ".symlinks/plugins/smart_auth/ios"
  sqflite_darwin:
    :path: ".symlinks/plugins/sqflite_darwin/darwin"

SPEC CHECKSUMS:
  abseil: d121da9ef7e2ff4cab7666e76c5a3e0915ae08c3
  AppCheckCore: 9feb4300caa596a36416cde10674dc5bec1e022e
  BoringSSL-GRPC: 1e2348957acdbcad360b80a264a90799984b2ba6
  cloud_firestore: 28753092b326c43b17251b50aa6c5b3247c939e5
  connectivity_plus: 481668c94744c30c53b8895afb39159d1e619bdf
  DKImagePickerController: 946cec48c7873164274ecc4624d19e3da4c1ef3c
  DKPhotoGallery: b3834fecb755ee09a593d7c9e389d8b5d6deed60
  file_picker: a0560bc09d61de87f12d246fc47d2119e6ef37be
  Firebase: 0312a2352584f782ea56f66d91606891d4607f06
  firebase_analytics: e7280be84e7ff1c1e5859bd8e48de410b07e0b73
  firebase_app_check: 7ad6ee5b582c58babcbed05da127e6b335e5a373
  firebase_auth: 9cfa74cc3773c44d2f762c3baa0806234c8516f5
  firebase_core: 3b49a055ff54114cae400581c13671fe53936c36
  firebase_crashlytics: 66f38fa4dd09ba785fb909ee14e97b02693162f4
  firebase_messaging: 394589dcda43e42ec9807558a409a975e285c45b
  FirebaseAnalytics: ec00fe8b93b41dc6fe4a28784b8e51da0647a248
  FirebaseAppCheck: 148fa858b8070710c8f83d3f545b5f1e85d104a4
  FirebaseAppCheckInterop: 6a1757cfd4067d8e00fccd14fcc1b8fd78cfac07
  FirebaseAuth: c0f93dcc570c9da2bffb576969d793e95c344fbb
  FirebaseCore: 7ec4d0484817f12c3373955bc87762d96842d483
  FirebaseCoreExtension: 705ca5b14bf71d2564a0ddc677df1fc86ffa600f
  FirebaseCoreInternal: df84dd300b561c27d5571684f389bf60b0a5c934
  FirebaseCrashlytics: 4b96efb0ce73b38b2a85e8b8bd1bd8f63f09d015
  FirebaseFirestore: 977ccc27a3caa5d68279f209c3b0450f85b9dc5f
  FirebaseFirestoreInternal: 04b8afa77b4e5b84e86ab5ad985193e9573239fa
  FirebaseInstallations: 913cf60d0400ebd5d6b63a28b290372ab44590dd
  FirebaseMessaging: 88950ba9485052891ebe26f6c43a52bb62248952
  FirebaseRemoteConfigInterop: 6efda51fb5e2f15b16585197e26eaa09574e8a4d
  FirebaseSessions: dbd14adac65ce996228652c1fc3a3f576bdf3ecc
  FirebaseSharedSwift: 20530f495084b8d840f78a100d8c5ee613375f6e
  Flutter: e0871f40cf51350855a761d2e70bf5af5b9b5de7
  flutter_local_notifications: ad39620c743ea4c15127860f4b5641649a988100
  flutter_secure_storage: 1ed9476fba7e7a782b22888f956cce43e2c62f13
  geolocator_apple: 1560c3c875af2a412242c7a923e15d0d401966ff
  Google-Maps-iOS-Utils: 66d6de12be1ce6d3742a54661e7a79cb317a9321
  google_maps_flutter_ios: 0291eb2aa252298a769b04d075e4a9d747ff7264
  GoogleAppMeasurement: 9abf64b682732fed36da827aa2a68f0221fd2356
  GoogleDataTransport: 6c09b596d841063d76d4288cc2d2f42cc36e1e2a
  GoogleMaps: 8939898920281c649150e0af74aa291c60f2e77d
  GoogleUtilities: ea963c370a38a8069cc5f7ba4ca849a60b6d7d15
  "gRPC-C++": e725ef63c4475d7cdb7e2cf16eb0fde84bd9ee51
  gRPC-Core: eee4be35df218649fe66d721a05a7f27a28f069b
  GTMSessionFetcher: 5aea5ba6bd522a239e236100971f10cb71b96ab6
  image_picker_ios: 7fe1ff8e34c1790d6fff70a32484959f563a928a
  leveldb-library: cc8b8f8e013647a295ad3f8cd2ddf49a6f19be19
  nanopb: 438bc412db1928dac798aa6fd75726007be04262
  path_provider_foundation: 080d55be775b7414fd5a5ef3ac137b97b097e564
  permission_handler_apple: 4ed2196e43d0651e8ff7ca3483a069d469701f2d
  PromisesObjC: f5707f49cb48b9636751c5b2e7d227e43fba9f47
  PromisesSwift: 9d77319bbe72ebf6d872900551f7eeba9bce2851
  ReachabilitySwift: 32793e867593cfc1177f5d16491e3a197d2fccda
  RecaptchaInterop: 7d1a4a01a6b2cb1610a47ef3f85f0c411434cb21
  SDWebImage: 73c6079366fea25fa4bb9640d5fb58f0893facd8
  shared_preferences_foundation: 9e1978ff2562383bd5676f64ec4e9aa8fa06a6f7
  smart_auth: 33668081c5f646af84f10492a067d25fdcd16951
  sqflite_darwin: 20b2a3a3b70e43edae938624ce550a3cbf66a3d0
  SwiftyGif: 706c60cf65fa2bc5ee0313beece843c8eb8194d4

PODFILE CHECKSUM: 58d1fa6b195cad6f27faf55cafdd222d8a2dc564

COCOAPODS: 1.16.2
