name: CultureConnect CI/CD

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  analyze:
    name: Analyze code
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Setup Flutter
        uses: subosito/flutter-action@v2
        with:
          flutter-version: '3.19.0'
          channel: 'stable'
          cache: true

      - name: Install dependencies
        run: |
          cd culture_connect
          flutter pub get

      - name: Analyze code
        run: |
          cd culture_connect
          flutter analyze

  unit_tests:
    name: Run unit tests
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Setup Flutter
        uses: subosito/flutter-action@v2
        with:
          flutter-version: '3.19.0'
          channel: 'stable'
          cache: true

      - name: Install dependencies
        run: |
          cd culture_connect
          flutter pub get

      - name: Run unit tests
        run: |
          cd culture_connect
          flutter test test/unit/

      - name: Generate coverage report
        run: |
          cd culture_connect
          flutter test --coverage
          
      - name: Upload coverage to Codecov
        uses: codecov/codecov-action@v3
        with:
          file: culture_connect/coverage/lcov.info
          fail_ci_if_error: false

  widget_tests:
    name: Run widget tests
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Setup Flutter
        uses: subosito/flutter-action@v2
        with:
          flutter-version: '3.19.0'
          channel: 'stable'
          cache: true

      - name: Install dependencies
        run: |
          cd culture_connect
          flutter pub get

      - name: Run widget tests
        run: |
          cd culture_connect
          flutter test test/widget/

  integration_tests:
    name: Run integration tests
    runs-on: macos-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Setup Flutter
        uses: subosito/flutter-action@v2
        with:
          flutter-version: '3.19.0'
          channel: 'stable'
          cache: true

      - name: Install dependencies
        run: |
          cd culture_connect
          flutter pub get

      - name: Setup iOS simulator
        run: |
          UDID=$(xcrun simctl list devices | grep "iPhone 14" | grep -v "unavailable" | head -1 | awk -F'[()]' '{print $2}')
          if [ -z "$UDID" ]; then
            xcrun simctl create "iPhone 14" "iPhone 14"
            UDID=$(xcrun simctl list devices | grep "iPhone 14" | grep -v "unavailable" | head -1 | awk -F'[()]' '{print $2}')
          fi
          xcrun simctl boot $UDID

      - name: Run integration tests
        run: |
          cd culture_connect
          flutter test integration_test/

  build_android:
    name: Build Android APK
    runs-on: ubuntu-latest
    needs: [analyze, unit_tests, widget_tests]
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'
    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Setup Flutter
        uses: subosito/flutter-action@v2
        with:
          flutter-version: '3.19.0'
          channel: 'stable'
          cache: true

      - name: Install dependencies
        run: |
          cd culture_connect
          flutter pub get

      - name: Build APK
        run: |
          cd culture_connect
          flutter build apk --release

      - name: Upload APK
        uses: actions/upload-artifact@v3
        with:
          name: app-release
          path: culture_connect/build/app/outputs/flutter-apk/app-release.apk

  build_ios:
    name: Build iOS IPA
    runs-on: macos-latest
    needs: [analyze, unit_tests, widget_tests]
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'
    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Setup Flutter
        uses: subosito/flutter-action@v2
        with:
          flutter-version: '3.19.0'
          channel: 'stable'
          cache: true

      - name: Install dependencies
        run: |
          cd culture_connect
          flutter pub get

      - name: Build iOS
        run: |
          cd culture_connect
          flutter build ios --release --no-codesign

      - name: Setup Ruby
        uses: ruby/setup-ruby@v1
        with:
          ruby-version: '3.0'
          bundler-cache: true

      - name: Install Fastlane
        run: |
          cd culture_connect/ios
          gem install fastlane

      - name: Build IPA with Fastlane
        run: |
          cd culture_connect/ios
          fastlane build_ipa

      - name: Upload IPA
        uses: actions/upload-artifact@v3
        with:
          name: app-release-ipa
          path: culture_connect/ios/build/Runner.ipa
