name: CultureConnect CI/CD

on:
  push:
    branches: [ main, develop ]
    tags:
      - 'v*'
  pull_request:
    branches: [ main, develop ]

jobs:
  # Flutter test and build job
  flutter_test_and_build:
    name: Flutter Test and Build
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Setup Java
        uses: actions/setup-java@v3
        with:
          distribution: 'zulu'
          java-version: '17'

      - name: Setup Flutter
        uses: subosito/flutter-action@v2
        with:
          flutter-version: '3.16.0'
          channel: 'stable'
          cache: true

      - name: Install dependencies
        run: flutter pub get

      - name: Analyze code
        run: flutter analyze

      - name: Run tests
        run: flutter test

      - name: Build APK
        if: github.event_name != 'pull_request'
        run: flutter build apk --release

      - name: Upload APK
        if: github.event_name != 'pull_request'
        uses: actions/upload-artifact@v3
        with:
          name: release-apk
          path: build/app/outputs/flutter-apk/app-release.apk

  # Android build job
  build_android:
    name: Build Android App
    needs: flutter_test_and_build
    if: startsWith(github.ref, 'refs/tags/v')
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Setup Java
        uses: actions/setup-java@v3
        with:
          distribution: 'zulu'
          java-version: '17'

      - name: Setup Flutter
        uses: subosito/flutter-action@v2
        with:
          flutter-version: '3.16.0'
          channel: 'stable'
          cache: true

      - name: Install dependencies
        run: flutter pub get

      - name: Decode Keystore
        env:
          ENCODED_KEYSTORE: ${{ secrets.KEYSTORE_BASE64 }}
        run: |
          echo $ENCODED_KEYSTORE | base64 --decode > android/app/keystore.jks

      - name: Build App Bundle
        env:
          KEY_STORE_PASSWORD: ${{ secrets.KEY_STORE_PASSWORD }}
          KEY_PASSWORD: ${{ secrets.KEY_PASSWORD }}
          KEY_ALIAS: ${{ secrets.KEY_ALIAS }}
        run: |
          flutter build appbundle --release \
            --build-number=$(echo ${{ github.ref_name }} | sed 's/v//g' | sed 's/\.//g') \
            --build-name=$(echo ${{ github.ref_name }} | sed 's/v//g')

      - name: Upload App Bundle
        uses: actions/upload-artifact@v3
        with:
          name: release-aab
          path: build/app/outputs/bundle/release/app-release.aab

      - name: Deploy to Play Store
        uses: r0adkll/upload-google-play@v1
        if: github.event_name == 'push' && startsWith(github.ref, 'refs/tags/v')
        with:
          serviceAccountJsonPlainText: ${{ secrets.PLAY_STORE_SERVICE_ACCOUNT_JSON }}
          packageName: com.cultureconnect.app
          releaseFiles: build/app/outputs/bundle/release/app-release.aab
          track: internal
          status: completed
          whatsNewDirectory: distribution/whatsnew

  # iOS build job
  build_ios:
    name: Build iOS App
    needs: flutter_test_and_build
    if: startsWith(github.ref, 'refs/tags/v')
    runs-on: macos-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Setup Flutter
        uses: subosito/flutter-action@v2
        with:
          flutter-version: '3.16.0'
          channel: 'stable'
          cache: true

      - name: Install dependencies
        run: flutter pub get

      - name: Setup Ruby
        uses: ruby/setup-ruby@v1
        with:
          ruby-version: '3.0'
          bundler-cache: true

      - name: Install Fastlane
        run: |
          cd ios
          gem install bundler
          bundle install

      - name: Setup Provisioning Profiles
        env:
          APPLE_CERTIFICATE_BASE64: ${{ secrets.APPLE_CERTIFICATE_BASE64 }}
          APPLE_CERTIFICATE_PASSWORD: ${{ secrets.APPLE_CERTIFICATE_PASSWORD }}
          APPLE_PROVISIONING_PROFILE_BASE64: ${{ secrets.APPLE_PROVISIONING_PROFILE_BASE64 }}
          APPLE_TEAM_ID: ${{ secrets.APPLE_TEAM_ID }}
          MATCH_PASSWORD: ${{ secrets.MATCH_PASSWORD }}
        run: |
          cd ios
          echo $APPLE_CERTIFICATE_BASE64 | base64 --decode > certificate.p12
          mkdir -p ~/Library/MobileDevice/Provisioning\ Profiles
          echo $APPLE_PROVISIONING_PROFILE_BASE64 | base64 --decode > ~/Library/MobileDevice/Provisioning\ Profiles/profile.mobileprovision
          security create-keychain -p "" build.keychain
          security default-keychain -s build.keychain
          security unlock-keychain -p "" build.keychain
          security import certificate.p12 -k build.keychain -P $APPLE_CERTIFICATE_PASSWORD -A
          security set-key-partition-list -S apple-tool:,apple: -s -k "" build.keychain

      - name: Build and Deploy to TestFlight
        env:
          APP_STORE_CONNECT_API_KEY_ID: ${{ secrets.APP_STORE_CONNECT_API_KEY_ID }}
          APP_STORE_CONNECT_API_KEY_ISSUER_ID: ${{ secrets.APP_STORE_CONNECT_API_KEY_ISSUER_ID }}
          APP_STORE_CONNECT_API_KEY_CONTENT: ${{ secrets.APP_STORE_CONNECT_API_KEY_CONTENT }}
          APPLE_TEAM_ID: ${{ secrets.APPLE_TEAM_ID }}
        run: |
          cd ios
          bundle exec fastlane beta

  # Firebase App Distribution job
  firebase_distribution:
    name: Firebase App Distribution
    needs: flutter_test_and_build
    if: github.ref == 'refs/heads/develop'
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Download APK
        uses: actions/download-artifact@v3
        with:
          name: release-apk
          path: build/app/outputs/flutter-apk/

      - name: Upload to Firebase App Distribution
        uses: wzieba/Firebase-Distribution-Github-Action@v1
        with:
          appId: ${{ secrets.FIREBASE_APP_ID }}
          serviceCredentialsFileContent: ${{ secrets.FIREBASE_SERVICE_ACCOUNT_JSON }}
          groups: testers
          file: build/app/outputs/flutter-apk/app-release.apk
          releaseNotes: |
            Changes in this build:
            - ${{ github.event.head_commit.message }}

  # Monitoring setup job
  setup_monitoring:
    name: Setup Monitoring
    needs: [build_android, build_ios]
    if: startsWith(github.ref, 'refs/tags/v')
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Setup Firebase Crashlytics
        run: |
          echo "Setting up Firebase Crashlytics for version $(echo ${{ github.ref_name }} | sed 's/v//g')"
          # In a real implementation, this would configure Firebase Crashlytics for the new version

      - name: Setup Firebase Performance Monitoring
        run: |
          echo "Setting up Firebase Performance Monitoring for version $(echo ${{ github.ref_name }} | sed 's/v//g')"
          # In a real implementation, this would configure Firebase Performance Monitoring for the new version

      - name: Setup New Relic Mobile Monitoring
        run: |
          echo "Setting up New Relic Mobile Monitoring for version $(echo ${{ github.ref_name }} | sed 's/v//g')"
          # In a real implementation, this would configure New Relic Mobile Monitoring for the new version

  # Backup job
  backup:
    name: Backup Release Artifacts
    needs: [build_android, build_ios]
    if: startsWith(github.ref, 'refs/tags/v')
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Download APK
        uses: actions/download-artifact@v3
        with:
          name: release-apk
          path: artifacts/android/

      - name: Download AAB
        uses: actions/download-artifact@v3
        with:
          name: release-aab
          path: artifacts/android/

      - name: Create Release Backup
        run: |
          VERSION=$(echo ${{ github.ref_name }} | sed 's/v//g')
          tar -czvf culture_connect_${VERSION}_backup.tar.gz artifacts/
          echo "Created backup archive for version $VERSION"

      - name: Upload Backup to S3
        uses: jakejarvis/s3-sync-action@master
        with:
          args: --acl private --follow-symlinks
        env:
          AWS_S3_BUCKET: ${{ secrets.AWS_S3_BUCKET }}
          AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
          AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          SOURCE_DIR: 'culture_connect_*.tar.gz'
          DEST_DIR: 'backups/releases/'
