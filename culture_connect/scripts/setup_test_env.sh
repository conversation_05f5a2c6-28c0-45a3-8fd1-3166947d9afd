#!/bin/bash

# Setup Test Environment for CultureConnect
# This script helps set up the testing environment for the CultureConnect project

# Set colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[0;33m'
NC='\033[0m' # No Color

echo -e "${YELLOW}Setting up CultureConnect Testing Environment${NC}"
echo "======================================================="

# Check Flutter installation
echo -e "\n${YELLOW}Checking Flutter installation...${NC}"
if ! command -v flutter &> /dev/null; then
  echo -e "${RED}Flutter not found. Please install Flutter first.${NC}"
  echo "Visit https://flutter.dev/docs/get-started/install for installation instructions."
  exit 1
else
  FLUTTER_VERSION=$(flutter --version | head -1 | awk '{print $2}')
  echo -e "${GREEN}Flutter $FLUTTER_VERSION found.${NC}"
fi

# Check Dart installation
echo -e "\n${YELLOW}Checking Dart installation...${NC}"
if ! command -v dart &> /dev/null; then
  echo -e "${RED}Dart not found. It should be included with Flutter.${NC}"
  exit 1
else
  DART_VERSION=$(dart --version | awk '{print $4}')
  echo -e "${GREEN}Dart $DART_VERSION found.${NC}"
fi

# Check for minimum versions
MIN_FLUTTER_VERSION="3.19.0"
MIN_DART_VERSION="3.0.0"

function version_lt() {
  test "$(echo "$1 $2" | tr " " "\n" | sort -V | head -n 1)" != "$2"
}

if version_lt $FLUTTER_VERSION $MIN_FLUTTER_VERSION; then
  echo -e "${RED}Flutter version $FLUTTER_VERSION is below the minimum required version $MIN_FLUTTER_VERSION.${NC}"
  echo -e "${YELLOW}Please update Flutter:${NC}"
  echo "flutter upgrade"
  exit 1
fi

if version_lt $DART_VERSION $MIN_DART_VERSION; then
  echo -e "${RED}Dart version $DART_VERSION is below the minimum required version $MIN_DART_VERSION.${NC}"
  echo -e "${YELLOW}Please update Flutter and Dart:${NC}"
  echo "flutter upgrade"
  exit 1
fi

# Install dependencies
echo -e "\n${YELLOW}Installing dependencies...${NC}"
flutter pub get
if [ $? -ne 0 ]; then
  echo -e "${RED}Failed to install dependencies.${NC}"
  exit 1
else
  echo -e "${GREEN}Dependencies installed successfully.${NC}"
fi

# Generate mocks
echo -e "\n${YELLOW}Generating mock files...${NC}"
flutter pub run build_runner build --delete-conflicting-outputs
if [ $? -ne 0 ]; then
  echo -e "${RED}Failed to generate mock files.${NC}"
  exit 1
else
  echo -e "${GREEN}Mock files generated successfully.${NC}"
fi

# Check for lcov (for coverage reports)
echo -e "\n${YELLOW}Checking for lcov...${NC}"
if ! command -v lcov &> /dev/null; then
  echo -e "${YELLOW}lcov not found. Coverage HTML reports will not be available.${NC}"
  echo -e "${YELLOW}To install lcov:${NC}"
  echo "  - On macOS: brew install lcov"
  echo "  - On Ubuntu/Debian: sudo apt-get install lcov"
  echo "  - On Windows: Install through Chocolatey or manually"
else
  echo -e "${GREEN}lcov found. Coverage HTML reports will be available.${NC}"
fi

# Create test directories if they don't exist
echo -e "\n${YELLOW}Creating test directories...${NC}"
mkdir -p test/unit/models
mkdir -p test/unit/providers
mkdir -p test/unit/repositories
mkdir -p test/unit/services
mkdir -p test/widget/screens
mkdir -p test/widget/widgets
mkdir -p test/integration/flows
mkdir -p test/performance
mkdir -p test/mocks
mkdir -p test/fixtures
mkdir -p test/utils
mkdir -p test_results

echo -e "${GREEN}Test directories created.${NC}"

# Make test runner executable
echo -e "\n${YELLOW}Making test runner executable...${NC}"
chmod +x test/run_tests.sh
echo -e "${GREEN}Test runner is now executable.${NC}"

# Create a .env.test file for testing
echo -e "\n${YELLOW}Creating test environment file...${NC}"
if [ ! -f .env.test ]; then
  cat > .env.test << EOL
# Test Environment Configuration
API_URL=https://api.test.cultureconnect.com
ENABLE_ANALYTICS=false
MOCK_LOCATION=true
MOCK_AR=true
EOL
  echo -e "${GREEN}Created .env.test file.${NC}"
else
  echo -e "${YELLOW}.env.test file already exists. Skipping.${NC}"
fi

# Setup pre-commit hook for tests
echo -e "\n${YELLOW}Setting up pre-commit hook...${NC}"
mkdir -p .git/hooks

cat > .git/hooks/pre-commit << EOL
#!/bin/bash
echo "Running pre-commit tests..."

# Stash unstaged changes
git stash -q --keep-index

# Run flutter analyze
echo "Running flutter analyze..."
flutter analyze
ANALYZE_RESULT=\$?

# Run unit tests
echo "Running unit tests..."
flutter test test/unit/
UNIT_TEST_RESULT=\$?

# Restore stashed changes
git stash pop -q

# Return status
if [ \$ANALYZE_RESULT -ne 0 ] || [ \$UNIT_TEST_RESULT -ne 0 ]; then
  echo "Pre-commit checks failed. Please fix the issues before committing."
  exit 1
fi

echo "Pre-commit checks passed."
exit 0
EOL

chmod +x .git/hooks/pre-commit
echo -e "${GREEN}Pre-commit hook installed.${NC}"

# Create a sample test if none exists
echo -e "\n${YELLOW}Checking for existing tests...${NC}"
if [ ! -f test/unit/sample_test.dart ] && [ $(find test -name "*_test.dart" | wc -l) -eq 0 ]; then
  echo -e "${YELLOW}No tests found. Creating a sample test...${NC}"
  
  cat > test/unit/sample_test.dart << EOL
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('Sample Tests', () {
    test('should pass', () {
      expect(1 + 1, equals(2));
    });
    
    test('should handle string operations', () {
      final string = 'Hello, CultureConnect!';
      expect(string.contains('Culture'), isTrue);
      expect(string.startsWith('Hello'), isTrue);
    });
  });
}
EOL
  
  echo -e "${GREEN}Created a sample test at test/unit/sample_test.dart${NC}"
else
  echo -e "${GREEN}Tests already exist. Skipping sample test creation.${NC}"
fi

# Final instructions
echo -e "\n${GREEN}Testing environment setup complete!${NC}"
echo -e "\n${YELLOW}Next steps:${NC}"
echo "1. Run the tests: ./test/run_tests.sh"
echo "2. Check test coverage: flutter test --coverage"
echo "3. Read the testing guide: docs/testing_guide.md"
echo -e "\n${YELLOW}Happy testing!${NC}"
echo "======================================================="
