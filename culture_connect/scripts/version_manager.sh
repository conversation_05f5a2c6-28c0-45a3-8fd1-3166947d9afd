#!/bin/bash

# Version Manager for CultureConnect
# This script helps manage semantic versioning for the app

# Set colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[0;33m'
NC='\033[0m' # No Color

# Path to pubspec.yaml
PUBSPEC_PATH="pubspec.yaml"

# Function to get current version from pubspec.yaml
get_current_version() {
  local version=$(grep -E "^version:" $PUBSPEC_PATH | awk '{print $2}' | tr -d '"' | tr -d "'")
  echo $version
}

# Function to update version in pubspec.yaml
update_version() {
  local new_version=$1
  local new_build_number=$2
  
  # Update version in pubspec.yaml
  sed -i.bak "s/^version: .*/version: $new_version+$new_build_number/" $PUBSPEC_PATH
  rm "${PUBSPEC_PATH}.bak"
  
  echo -e "${GREEN}Updated version to $new_version+$new_build_number${NC}"
}

# Function to bump version
bump_version() {
  local version_type=$1
  local current_version=$(get_current_version)
  
  # Split version into parts
  local version_number=$(echo $current_version | cut -d'+' -f1)
  local build_number=$(echo $current_version | cut -d'+' -f2)
  
  # Split version number into major, minor, patch
  local major=$(echo $version_number | cut -d'.' -f1)
  local minor=$(echo $version_number | cut -d'.' -f2)
  local patch=$(echo $version_number | cut -d'.' -f3)
  
  # Increment build number
  local new_build_number=$((build_number + 1))
  
  # Increment version based on type
  local new_version=""
  case $version_type in
    major)
      new_version="$((major + 1)).0.0"
      ;;
    minor)
      new_version="$major.$((minor + 1)).0"
      ;;
    patch)
      new_version="$major.$minor.$((patch + 1))"
      ;;
    build)
      new_version="$major.$minor.$patch"
      ;;
    *)
      echo -e "${RED}Invalid version type. Use 'major', 'minor', 'patch', or 'build'.${NC}"
      exit 1
      ;;
  esac
  
  # Update version in pubspec.yaml
  update_version $new_version $new_build_number
  
  # Create git tag
  if [ "$version_type" != "build" ]; then
    git tag -a "v$new_version" -m "Version $new_version"
    echo -e "${GREEN}Created git tag v$new_version${NC}"
  fi
}

# Function to set specific version
set_version() {
  local new_version=$1
  local new_build_number=$2
  
  # Validate version format
  if ! [[ $new_version =~ ^[0-9]+\.[0-9]+\.[0-9]+$ ]]; then
    echo -e "${RED}Invalid version format. Use semantic versioning (e.g., 1.2.3).${NC}"
    exit 1
  fi
  
  # Validate build number
  if ! [[ $new_build_number =~ ^[0-9]+$ ]]; then
    echo -e "${RED}Invalid build number. Use a positive integer.${NC}"
    exit 1
  fi
  
  # Update version in pubspec.yaml
  update_version $new_version $new_build_number
  
  # Create git tag
  git tag -a "v$new_version" -m "Version $new_version"
  echo -e "${GREEN}Created git tag v$new_version${NC}"
}

# Function to show help
show_help() {
  echo "Version Manager for CultureConnect"
  echo "Usage:"
  echo "  $0 [command] [options]"
  echo ""
  echo "Commands:"
  echo "  get                     Get current version"
  echo "  bump [type]             Bump version (type: major, minor, patch, build)"
  echo "  set [version] [build]   Set specific version and build number"
  echo "  help                    Show this help message"
  echo ""
  echo "Examples:"
  echo "  $0 get"
  echo "  $0 bump minor"
  echo "  $0 set 1.2.3 42"
}

# Main script
case $1 in
  get)
    current_version=$(get_current_version)
    echo -e "${GREEN}Current version: $current_version${NC}"
    ;;
  bump)
    if [ -z "$2" ]; then
      echo -e "${RED}Please specify version type (major, minor, patch, build).${NC}"
      exit 1
    fi
    bump_version $2
    ;;
  set)
    if [ -z "$2" ] || [ -z "$3" ]; then
      echo -e "${RED}Please specify version and build number.${NC}"
      exit 1
    fi
    set_version $2 $3
    ;;
  help|--help|-h)
    show_help
    ;;
  *)
    echo -e "${RED}Unknown command. Use 'help' to see available commands.${NC}"
    exit 1
    ;;
esac

exit 0
