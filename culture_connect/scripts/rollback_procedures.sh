#!/bin/bash

# Rollback Procedures Script for CultureConnect
# This script manages rollback procedures for the application

# Usage:
# ./rollback_procedures.sh rollback [version]
# ./rollback_procedures.sh list
# ./rollback_procedures.sh status

# Configuration
RELEASES_DIR="../releases"

# Get the current version from pubspec.yaml
get_current_version() {
  grep -E "version: [0-9]+\.[0-9]+\.[0-9]+" ../pubspec.yaml | sed -E "s/version: ([0-9]+\.[0-9]+\.[0-9]+).*/\1/"
}

# Rollback to a specific version
rollback_to_version() {
  local target_version=$1
  
  if [ -z "$target_version" ]; then
    echo "Error: Target version is required"
    exit 1
  fi
  
  local current_version=$(get_current_version)
  
  echo "Rolling back from version $current_version to $target_version..."
  
  # Check if the target version exists in git tags
  if ! git rev-parse "v$target_version" >/dev/null 2>&1; then
    echo "Error: Version v$target_version does not exist in git tags"
    exit 1
  fi
  
  # Create a rollback branch
  local rollback_branch="rollback/v$current_version-to-v$target_version"
  git checkout -b $rollback_branch "v$target_version"
  
  echo "Created rollback branch: $rollback_branch"
  
  # Update version in pubspec.yaml to indicate rollback
  local rollback_version="$target_version+rollback.$(date +%Y%m%d)"
  sed -i.bak -E "s/version: [0-9]+\.[0-9]+\.[0-9]+.*/version: $rollback_version/" ../pubspec.yaml
  rm ../pubspec.yaml.bak
  
  # Commit the changes
  git add ../pubspec.yaml
  git commit -m "Rollback from v$current_version to v$target_version"
  
  echo "Rollback preparation complete. To deploy the rollback:"
  echo "1. Push the rollback branch: git push origin $rollback_branch"
  echo "2. Create a pull request to merge the rollback branch into main"
  echo "3. After approval, merge the pull request"
  echo "4. Tag the rollback: git tag -a v$rollback_version -m 'Rollback to v$target_version'"
  echo "5. Push the tag: git push origin v$rollback_version"
  echo "6. The CI/CD pipeline will deploy the rollback version"
}

# List available versions for rollback
list_versions() {
  echo "Available versions for rollback:"
  
  # List git tags
  git tag -l "v*" | sort -V | while read -r tag; do
    local version=${tag#v}
    local commit=$(git rev-list -n 1 $tag)
    local date=$(git show -s --format=%ci $commit)
    echo "- $version (tagged on $date)"
  done
}

# Check rollback status
check_status() {
  local current_version=$(get_current_version)
  
  echo "Current version: $current_version"
  
  # Check if this is a rollback version
  if [[ $current_version == *+rollback* ]]; then
    echo "This is a rollback version"
    local original_version=${current_version%+rollback*}
    echo "Original version: $original_version"
  fi
  
  # List recent deployments
  echo "Recent deployments:"
  git log --pretty=format:"%h %ad %s" --date=short -n 5 | grep -E "v[0-9]+\.[0-9]+\.[0-9]+"
}

# Rollback iOS app in App Store
rollback_ios_app() {
  echo "Rolling back iOS app in App Store..."
  
  # In a real implementation, this would use the App Store Connect API to rollback the app
  
  echo "iOS app rollback initiated"
}

# Rollback Android app in Play Store
rollback_android_app() {
  echo "Rolling back Android app in Play Store..."
  
  # In a real implementation, this would use the Google Play Developer API to rollback the app
  
  echo "Android app rollback initiated"
}

# Main script logic
case $1 in
  rollback)
    rollback_to_version $2
    ;;
  list)
    list_versions
    ;;
  status)
    check_status
    ;;
  rollback-ios)
    rollback_ios_app
    ;;
  rollback-android)
    rollback_android_app
    ;;
  *)
    echo "Usage:"
    echo "  ./rollback_procedures.sh rollback [version]"
    echo "  ./rollback_procedures.sh list"
    echo "  ./rollback_procedures.sh status"
    echo "  ./rollback_procedures.sh rollback-ios"
    echo "  ./rollback_procedures.sh rollback-android"
    exit 1
    ;;
esac

exit 0
