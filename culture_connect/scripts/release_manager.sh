#!/bin/bash

# Release Manager for CultureConnect
# This script helps manage the release process

# Set colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[0;33m'
NC='\033[0m' # No Color

# Path to version manager script
VERSION_MANAGER="./scripts/version_manager.sh"

# Function to check if working directory is clean
check_clean_working_directory() {
  if [ -n "$(git status --porcelain)" ]; then
    echo -e "${RED}Error: Working directory is not clean. Commit or stash changes before releasing.${NC}"
    exit 1
  fi
}

# Function to run tests
run_tests() {
  echo -e "${YELLOW}Running tests...${NC}"
  
  # Run unit tests
  flutter test test/unit/
  if [ $? -ne 0 ]; then
    echo -e "${RED}Unit tests failed. Aborting release.${NC}"
    exit 1
  fi
  
  # Run widget tests
  flutter test test/widget/
  if [ $? -ne 0 ]; then
    echo -e "${RED}Widget tests failed. Aborting release.${NC}"
    exit 1
  fi
  
  echo -e "${GREEN}All tests passed!${NC}"
}

# Function to create a release branch
create_release_branch() {
  local version=$1
  local branch_name="release/v$version"
  
  echo -e "${YELLOW}Creating release branch $branch_name...${NC}"
  
  # Create and checkout release branch
  git checkout -b $branch_name
  
  echo -e "${GREEN}Created and checked out release branch $branch_name${NC}"
}

# Function to build release artifacts
build_release_artifacts() {
  echo -e "${YELLOW}Building release artifacts...${NC}"
  
  # Build Android APK
  echo -e "${YELLOW}Building Android APK...${NC}"
  flutter build apk --release
  if [ $? -ne 0 ]; then
    echo -e "${RED}Android APK build failed. Aborting release.${NC}"
    exit 1
  fi
  
  # Build Android App Bundle
  echo -e "${YELLOW}Building Android App Bundle...${NC}"
  flutter build appbundle --release
  if [ $? -ne 0 ]; then
    echo -e "${RED}Android App Bundle build failed. Aborting release.${NC}"
    exit 1
  fi
  
  # Build iOS
  echo -e "${YELLOW}Building iOS...${NC}"
  flutter build ios --release --no-codesign
  if [ $? -ne 0 ]; then
    echo -e "${RED}iOS build failed. Aborting release.${NC}"
    exit 1
  fi
  
  echo -e "${GREEN}All release artifacts built successfully!${NC}"
}

# Function to create release commit
create_release_commit() {
  local version=$1
  
  echo -e "${YELLOW}Creating release commit...${NC}"
  
  # Commit version bump
  git add pubspec.yaml
  git commit -m "Release v$version"
  
  echo -e "${GREEN}Created release commit${NC}"
}

# Function to create GitHub release
create_github_release() {
  local version=$1
  local tag_name="v$version"
  
  echo -e "${YELLOW}Creating GitHub release...${NC}"
  
  # Check if GitHub CLI is installed
  if ! command -v gh &> /dev/null; then
    echo -e "${RED}GitHub CLI not found. Please install it to create GitHub releases.${NC}"
    echo -e "${YELLOW}You can manually create a release on GitHub.${NC}"
    return
  fi
  
  # Create GitHub release
  gh release create $tag_name \
    --title "Release $tag_name" \
    --notes "Release $tag_name" \
    --draft \
    build/app/outputs/flutter-apk/app-release.apk \
    build/app/outputs/bundle/release/app-release.aab
  
  echo -e "${GREEN}Created draft GitHub release $tag_name${NC}"
  echo -e "${YELLOW}Please edit the release notes on GitHub before publishing.${NC}"
}

# Function to merge release branch
merge_release_branch() {
  local version=$1
  local branch_name="release/v$version"
  
  echo -e "${YELLOW}Merging release branch to main and develop...${NC}"
  
  # Checkout main branch
  git checkout main
  
  # Merge release branch into main
  git merge --no-ff $branch_name -m "Merge release v$version into main"
  
  # Checkout develop branch
  git checkout develop
  
  # Merge release branch into develop
  git merge --no-ff $branch_name -m "Merge release v$version into develop"
  
  # Delete release branch
  git branch -d $branch_name
  
  echo -e "${GREEN}Merged release branch to main and develop${NC}"
}

# Function to show help
show_help() {
  echo "Release Manager for CultureConnect"
  echo "Usage:"
  echo "  $0 [command] [options]"
  echo ""
  echo "Commands:"
  echo "  start [type]            Start a new release (type: major, minor, patch)"
  echo "  finish [version]        Finish a release"
  echo "  help                    Show this help message"
  echo ""
  echo "Examples:"
  echo "  $0 start minor"
  echo "  $0 finish 1.2.0"
}

# Main script
case $1 in
  start)
    if [ -z "$2" ]; then
      echo -e "${RED}Please specify version type (major, minor, patch).${NC}"
      exit 1
    fi
    
    # Check if working directory is clean
    check_clean_working_directory
    
    # Run tests
    run_tests
    
    # Get current version
    current_version=$($VERSION_MANAGER get | grep "Current version:" | awk '{print $3}' | cut -d'+' -f1)
    
    # Bump version
    $VERSION_MANAGER bump $2
    
    # Get new version
    new_version=$($VERSION_MANAGER get | grep "Current version:" | awk '{print $3}' | cut -d'+' -f1)
    
    # Create release branch
    create_release_branch $new_version
    
    # Create release commit
    create_release_commit $new_version
    
    echo -e "${GREEN}Started release v$new_version${NC}"
    echo -e "${YELLOW}Next steps:${NC}"
    echo -e "1. Make any final adjustments to the release"
    echo -e "2. Run '$0 finish $new_version' to complete the release"
    ;;
  finish)
    if [ -z "$2" ]; then
      echo -e "${RED}Please specify version number.${NC}"
      exit 1
    fi
    
    # Check if working directory is clean
    check_clean_working_directory
    
    # Build release artifacts
    build_release_artifacts
    
    # Create GitHub release
    create_github_release $2
    
    # Merge release branch
    merge_release_branch $2
    
    echo -e "${GREEN}Finished release v$2${NC}"
    echo -e "${YELLOW}Next steps:${NC}"
    echo -e "1. Edit and publish the GitHub release"
    echo -e "2. Deploy to app stores using Fastlane"
    ;;
  help|--help|-h)
    show_help
    ;;
  *)
    echo -e "${RED}Unknown command. Use 'help' to see available commands.${NC}"
    exit 1
    ;;
esac

exit 0
