#!/bin/bash

# Version Management Script for CultureConnect
# This script helps manage version numbers and create release tags

# Usage:
# ./version_management.sh bump [major|minor|patch]
# ./version_management.sh tag
# ./version_management.sh release

# Get the current version from pubspec.yaml
get_current_version() {
  grep -E "version: [0-9]+\.[0-9]+\.[0-9]+" ../pubspec.yaml | sed -E "s/version: ([0-9]+\.[0-9]+\.[0-9]+).*/\1/"
}

# Bump the version number
bump_version() {
  local current_version=$(get_current_version)
  local major=$(echo $current_version | cut -d. -f1)
  local minor=$(echo $current_version | cut -d. -f2)
  local patch=$(echo $current_version | cut -d. -f3)
  
  case $1 in
    major)
      major=$((major + 1))
      minor=0
      patch=0
      ;;
    minor)
      minor=$((minor + 1))
      patch=0
      ;;
    patch)
      patch=$((patch + 1))
      ;;
    *)
      echo "Invalid bump type. Use major, minor, or patch."
      exit 1
      ;;
  esac
  
  local new_version="$major.$minor.$patch"
  echo "Bumping version from $current_version to $new_version"
  
  # Update pubspec.yaml
  sed -i.bak -E "s/version: [0-9]+\.[0-9]+\.[0-9]+.*/version: $new_version+$(date +%Y%m%d)/" ../pubspec.yaml
  rm ../pubspec.yaml.bak
  
  # Update version.dart if it exists
  if [ -f "../lib/utils/version.dart" ]; then
    sed -i.bak -E "s/static const String version = '[0-9]+\.[0-9]+\.[0-9]+';/static const String version = '$new_version';/" ../lib/utils/version.dart
    rm ../lib/utils/version.dart.bak
  fi
  
  echo "Version updated to $new_version"
}

# Create a git tag for the current version
tag_version() {
  local current_version=$(get_current_version)
  echo "Creating tag v$current_version"
  
  git add ../pubspec.yaml
  if [ -f "../lib/utils/version.dart" ]; then
    git add ../lib/utils/version.dart
  fi
  
  git commit -m "Bump version to $current_version"
  git tag -a "v$current_version" -m "Version $current_version"
  
  echo "Tag v$current_version created"
}

# Create a release branch and push to remote
create_release() {
  local current_version=$(get_current_version)
  local release_branch="release/v$current_version"
  
  echo "Creating release branch $release_branch"
  
  git checkout -b $release_branch
  git push origin $release_branch
  git push origin "v$current_version"
  
  echo "Release branch $release_branch created and pushed to remote"
}

# Main script logic
case $1 in
  bump)
    bump_version $2
    ;;
  tag)
    tag_version
    ;;
  release)
    create_release
    ;;
  *)
    echo "Usage:"
    echo "  ./version_management.sh bump [major|minor|patch]"
    echo "  ./version_management.sh tag"
    echo "  ./version_management.sh release"
    exit 1
    ;;
esac

exit 0
