#!/bin/bash

# Generate Coverage Report for CultureConnect
# This script generates a detailed test coverage report

# Set colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[0;33m'
NC='\033[0m' # No Color

echo -e "${YELLOW}Generating Test Coverage Report for CultureConnect${NC}"
echo "======================================================="

# Check if lcov is installed
if ! command -v lcov &> /dev/null; then
  echo -e "${RED}lcov is not installed. Please install it to generate HTML reports.${NC}"
  echo -e "${YELLOW}To install lcov:${NC}"
  echo "  - On macOS: brew install lcov"
  echo "  - On Ubuntu/Debian: sudo apt-get install lcov"
  echo "  - On Windows: Install through Chocolatey or manually"
  exit 1
fi

# Create coverage directory if it doesn't exist
mkdir -p coverage

# Run tests with coverage
echo -e "\n${YELLOW}Running tests with coverage...${NC}"
flutter test --coverage
if [ $? -ne 0 ]; then
  echo -e "${RED}Tests failed. Coverage report may be incomplete.${NC}"
else
  echo -e "${GREEN}Tests completed successfully.${NC}"
fi

# Process coverage data
echo -e "\n${YELLOW}Processing coverage data...${NC}"

# Remove generated files from coverage
echo -e "${YELLOW}Removing generated files from coverage...${NC}"
lcov --remove coverage/lcov.info \
  '**/*.g.dart' \
  '**/*.freezed.dart' \
  '**/generated_plugin_registrant.dart' \
  '**/generated/*' \
  '**/mocks/*' \
  -o coverage/lcov_cleaned.info

# Generate HTML report
echo -e "\n${YELLOW}Generating HTML report...${NC}"
genhtml coverage/lcov_cleaned.info -o coverage/html

# Calculate coverage percentage
COVERAGE_PCT=$(lcov --summary coverage/lcov_cleaned.info | grep "lines" | awk '{print $4}')
echo -e "\n${GREEN}Coverage report generated successfully.${NC}"
echo -e "${YELLOW}Overall coverage: ${GREEN}$COVERAGE_PCT${NC}"

# Generate coverage badge
echo -e "\n${YELLOW}Generating coverage badge...${NC}"
COVERAGE_NUM=$(echo $COVERAGE_PCT | sed 's/%//')

# Determine badge color based on coverage percentage
if (( $(echo "$COVERAGE_NUM >= 80" | bc -l) )); then
  BADGE_COLOR="brightgreen"
elif (( $(echo "$COVERAGE_NUM >= 70" | bc -l) )); then
  BADGE_COLOR="green"
elif (( $(echo "$COVERAGE_NUM >= 60" | bc -l) )); then
  BADGE_COLOR="yellowgreen"
elif (( $(echo "$COVERAGE_NUM >= 50" | bc -l) )); then
  BADGE_COLOR="yellow"
elif (( $(echo "$COVERAGE_NUM >= 40" | bc -l) )); then
  BADGE_COLOR="orange"
else
  BADGE_COLOR="red"
fi

# Create badge JSON file
cat > coverage/coverage-badge.json << EOL
{
  "schemaVersion": 1,
  "label": "coverage",
  "message": "$COVERAGE_PCT",
  "color": "$BADGE_COLOR"
}
EOL

# Generate coverage by directory report
echo -e "\n${YELLOW}Coverage by directory:${NC}"
echo "======================================================="
echo -e "Directory\t\t\tCoverage"
echo "-------------------------------------------------------"

# Get list of directories
DIRS=$(find lib -type d -not -path "*/\.*" | sort)

for DIR in $DIRS; do
  if [ "$DIR" != "lib" ]; then
    # Calculate coverage for this directory
    DIR_COVERAGE=$(lcov --extract coverage/lcov_cleaned.info "*/$DIR/*" -o /tmp/dir_coverage.info 2>/dev/null)
    DIR_PCT=$(lcov --summary /tmp/dir_coverage.info 2>&1 | grep "lines" | awk '{print $4}')
    
    if [ -n "$DIR_PCT" ]; then
      # Format directory name for display
      DISPLAY_DIR=$(echo $DIR | sed 's/lib\///')
      
      # Determine color based on coverage percentage
      DIR_NUM=$(echo $DIR_PCT | sed 's/%//')
      if (( $(echo "$DIR_NUM >= 80" | bc -l) )); then
        DIR_COLOR=$GREEN
      elif (( $(echo "$DIR_NUM >= 50" | bc -l) )); then
        DIR_COLOR=$YELLOW
      else
        DIR_COLOR=$RED
      fi
      
      # Print directory coverage
      printf "%-30s\t${DIR_COLOR}%s${NC}\n" "$DISPLAY_DIR" "$DIR_PCT"
    fi
  fi
done

echo "======================================================="

# Final instructions
echo -e "\n${GREEN}Coverage report generated at:${NC} coverage/html/index.html"
echo -e "\n${YELLOW}To view the report, open the following URL in your browser:${NC}"
echo "file://$(pwd)/coverage/html/index.html"

# Check if we're running in CI
if [ -n "$CI" ]; then
  echo -e "\n${YELLOW}Running in CI environment. Uploading coverage to Codecov...${NC}"
  
  # Check if codecov is installed
  if ! command -v codecov &> /dev/null; then
    echo -e "${YELLOW}Installing codecov...${NC}"
    pip install codecov
  fi
  
  # Upload to Codecov
  codecov -f coverage/lcov.info
  
  echo -e "${GREEN}Coverage uploaded to Codecov.${NC}"
fi

echo -e "\n${GREEN}Done!${NC}"
