# CultureConnect Project Guardrails

This document serves as a comprehensive guide for AI assistants working on the CultureConnect project. It outlines the correct project structure, coding standards, and best practices to ensure consistency and prevent common mistakes.

## 1. Project Structure

### Single Source of Truth

The **ONLY** valid source code location is:
```
culture_connect/lib/
```

**IMPORTANT**: There should never be a separate `lib` folder at the root level or anywhere else in the project. All code must reside within `culture_connect/lib/`.

### Directory Structure

The project follows this directory structure:

```
culture_connect/
├── lib/
│   ├── config/           # Configuration files, router definitions
│   ├── models/           # Data models and DTOs
│   ├── providers/        # Riverpod providers
│   ├── screens/          # UI screens
│   │   ├── messaging/    # Messaging-related screens
│   │   └── safety/       # Safety-related screens
│   ├── services/         # Business logic and API services
│   ├── theme/            # Theme definitions
│   ├── widgets/          # Reusable UI components
│   │   └── common/       # Highly reusable basic components
│   └── main.dart         # Application entry point
├── assets/               # Static assets (images, fonts, etc.)
├── test/                 # Test files
└── pubspec.yaml          # Package dependencies
```

## 2. File Organization and Naming Conventions

### Naming Conventions

- **Files**: Use `snake_case` for all file names
  - ✅ `user_profile_screen.dart`
  - ❌ `UserProfileScreen.dart` or `userProfileScreen.dart`

- **Classes**: Use `PascalCase` for all class names
  - ✅ `class UserProfileScreen extends StatelessWidget {...}`
  - ❌ `class userProfileScreen extends StatelessWidget {...}`

- **Variables/Functions**: Use `camelCase` for variables and functions
  - ✅ `final userProfile = UserProfile(...)`
  - ❌ `final UserProfile = UserProfile(...)`

### File Content Guidelines

- **One Class Per File**: Each file should generally contain only one primary class
  - Exception: Small helper classes or extensions closely related to the primary class

- **File Naming**: Files should be named after the primary class they contain
  - `user_profile_screen.dart` should contain `UserProfileScreen` class

- **Folder Organization**: Group related files in appropriate directories
  - All screens go in `screens/` or its subdirectories
  - All reusable widgets go in `widgets/` or its subdirectories
  - All models go in `models/`
  - All services go in `services/`

## 3. Import Path Standards

### Package Imports vs Relative Imports

**ALWAYS use package imports** rather than relative imports:

✅ **Correct**:
```dart
import 'package:culture_connect/models/user.dart';
import 'package:culture_connect/services/auth_service.dart';
```

❌ **Incorrect**:
```dart
import '../models/user.dart';
import '../../services/auth_service.dart';
```

### Import Order

Follow this order for imports:
1. Dart SDK imports
2. Flutter imports
3. Third-party package imports
4. Project imports (package:culture_connect/...)

Example:
```dart
// Dart SDK imports
import 'dart:async';
import 'dart:io';

// Flutter imports
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

// Third-party package imports
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:http/http.dart' as http;

// Project imports
import 'package:culture_connect/models/user.dart';
import 'package:culture_connect/services/auth_service.dart';
```

## 4. Guidelines for Creating New Components

### Where to Place New Components

- **Screens**: Place in `lib/screens/` or appropriate subdirectory
  - Example: `lib/screens/profile/edit_profile_screen.dart`

- **Reusable Widgets**: Place in `lib/widgets/` or appropriate subdirectory
  - Example: `lib/widgets/cards/experience_card.dart`

- **Common Widgets**: Place highly reusable basic components in `lib/widgets/common/`
  - Example: `lib/widgets/common/app_button.dart`

- **Models**: Place in `lib/models/`
  - Example: `lib/models/booking.dart`

- **Services**: Place in `lib/services/`
  - Example: `lib/services/booking_service.dart`

- **Providers**: Place in `lib/providers/`
  - Example: `lib/providers/auth_provider.dart`

### Component Creation Checklist

When creating a new component:

1. Verify the component doesn't already exist elsewhere in the codebase
2. Place it in the appropriate directory based on its function
3. Follow naming conventions
4. Use package imports for all project files
5. Document the component with appropriate comments
6. Ensure the component is properly exported if needed

## 5. Standards for Code Consolidation

When finding duplicate or similar components:

1. **Identify the Superior Implementation**: Determine which implementation is more complete, robust, and follows best practices

2. **Create a Consolidated Version**: 
   - Combine the best features from all implementations
   - Ensure backward compatibility if possible
   - Place in the appropriate directory
   - Use proper naming conventions

3. **Update References**:
   - Update all import statements to reference the consolidated component
   - Ensure all calling code works with the consolidated component

4. **Remove Duplicates**:
   - Only after verifying the consolidated component works correctly
   - Remove all duplicate implementations

### Example Consolidation Process

For duplicate button components:

1. Identify the most complete implementation
2. Create a consolidated version with all features
3. Update all references to use the consolidated component
4. Remove the duplicate implementations

## 6. Verification Checklist

Before committing changes, verify:

- [ ] All code is in the correct `culture_connect/lib/` directory
- [ ] No duplicate files or components exist
- [ ] All imports use package imports (`package:culture_connect/...`)
- [ ] File and class naming follows conventions
- [ ] Components are in the appropriate directories
- [ ] The project builds without errors
- [ ] Existing functionality is preserved

### Testing Changes

Run these commands to verify changes:

```bash
# Navigate to the project directory
cd culture_connect

# Get dependencies
flutter pub get

# Analyze the code
flutter analyze

# Run tests
flutter test
```

## 7. Common Mistakes to Avoid

### ⚠️ CRITICAL WARNINGS ⚠️

1. **NEVER create a separate `lib` folder outside of `culture_connect/lib/`**
   - This causes confusion and duplicate code

2. **NEVER use relative imports**
   - Always use package imports (`package:culture_connect/...`)

3. **NEVER duplicate existing components**
   - Always check if a component already exists before creating a new one

4. **NEVER modify the project structure**
   - The directory structure is fixed and should not be changed

5. **NEVER create files with inconsistent naming**
   - Always follow the established naming conventions

6. **NEVER leave duplicate dependencies in pubspec.yaml**
   - Check for and remove duplicate entries in the dependencies section

### Specific Issues We've Encountered

1. **Duplicate `lib` Folders**: 
   - Problem: Creating code in both `./lib` and `./culture_connect/lib`
   - Solution: Only use `./culture_connect/lib`

2. **Inconsistent Import Paths**:
   - Problem: Mixing relative and package imports
   - Solution: Always use package imports

3. **Component Duplication**:
   - Problem: Creating multiple versions of the same component (e.g., multiple button implementations)
   - Solution: Consolidate into a single, comprehensive component

4. **Incorrect File Placement**:
   - Problem: Placing files in incorrect directories
   - Solution: Follow the directory structure guidelines

## Conclusion

Following these guardrails will ensure consistency in the CultureConnect project and prevent common mistakes. Always refer to this document when making changes to the project structure or adding new components.

Remember: The goal is to maintain a clean, organized codebase that is easy to understand and extend.
