# CultureConnect Architecture Guide

This guide provides an overview of the CultureConnect application architecture, helping developers understand the codebase structure, design patterns, and best practices.

## Table of Contents

1. [Architecture Overview](#architecture-overview)
2. [Project Structure](#project-structure)
3. [State Management](#state-management)
4. [Navigation](#navigation)
5. [Data Flow](#data-flow)
6. [Services](#services)
7. [UI Components](#ui-components)
8. [Testing Strategy](#testing-strategy)
9. [Performance Considerations](#performance-considerations)
10. [Coding Standards](#coding-standards)

## Architecture Overview

CultureConnect follows a layered architecture with clear separation of concerns:

```
┌─────────────────────────────────────────────────────────────┐
│                        Presentation Layer                    │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐  │
│  │   Screens   │  │   Widgets   │  │ Navigation/Routing  │  │
│  └─────────────┘  └─────────────┘  └─────────────────────┘  │
└───────────────────────────┬─────────────────────────────────┘
                            │
┌───────────────────────────▼─────────────────────────────────┐
│                        Business Logic Layer                  │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐  │
│  │  Providers  │  │  Services   │  │     Controllers     │  │
│  └─────────────┘  └─────────────┘  └─────────────────────┘  │
└───────────────────────────┬─────────────────────────────────┘
                            │
┌───────────────────────────▼─────────────────────────────────┐
│                        Data Layer                            │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐  │
│  │   Models    │  │ Repositories│  │       API Client    │  │
│  └─────────────┘  └─────────────┘  └─────────────────────┘  │
└─────────────────────────────────────────────────────────────┘
```

### Key Architectural Principles

1. **Separation of Concerns**: Each layer has a specific responsibility
2. **Dependency Injection**: Services and repositories are injected where needed
3. **Unidirectional Data Flow**: Data flows from repositories through services to UI
4. **Immutable State**: State objects are immutable to prevent unexpected changes
5. **Reactive Programming**: UI reacts to state changes through providers

## Project Structure

The project follows a feature-first organization with shared components:

```
lib/
├── main.dart                 # Application entry point
├── app.dart                  # Root application widget
├── config/                   # App configuration
│   ├── app_config.dart       # Environment-specific configuration
│   ├── routes.dart           # Route definitions
│   └── theme/                # Theming configuration
├── models/                   # Data models
├── screens/                  # UI screens
├── widgets/                  # Reusable UI components
├── providers/                # State management
├── services/                 # Business logic services
├── repositories/             # Data access layer
├── utils/                    # Utility functions and extensions
└── l10n/                     # Localization
```

### Feature Organization

For larger features like AR, we use a feature-first approach:

```
lib/
├── ar/                       # AR feature module
│   ├── models/               # AR-specific models
│   ├── screens/              # AR screens
│   ├── widgets/              # AR-specific widgets
│   ├── services/             # AR services
│   └── providers/            # AR state management
```

## State Management

CultureConnect uses Riverpod for state management, which provides:

1. **Dependency Injection**: Providers can depend on other providers
2. **Testability**: Easy to mock providers for testing
3. **Code Organization**: Clear separation between UI and business logic
4. **Performance**: Efficient rebuilds with fine-grained reactivity

### Provider Types

We use different provider types based on the use case:

1. **Provider**: For simple values that don't change
2. **StateProvider**: For simple state that can be modified
3. **StateNotifierProvider**: For complex state with multiple operations
4. **FutureProvider**: For asynchronous data
5. **StreamProvider**: For reactive data streams

### Example Provider

```dart
// State class
class ARState {
  final bool isLoading;
  final List<ARModel> models;
  final String? error;

  ARState({
    this.isLoading = false,
    this.models = const [],
    this.error,
  });

  ARState copyWith({
    bool? isLoading,
    List<ARModel>? models,
    String? error,
  }) {
    return ARState(
      isLoading: isLoading ?? this.isLoading,
      models: models ?? this.models,
      error: error,
    );
  }
}

// StateNotifier
class ARNotifier extends StateNotifier<ARState> {
  final ARService _arService;

  ARNotifier(this._arService) : super(ARState());

  Future<void> loadModels() async {
    state = state.copyWith(isLoading: true, error: null);
    try {
      final models = await _arService.getModels();
      state = state.copyWith(isLoading: false, models: models);
    } catch (e) {
      state = state.copyWith(isLoading: false, error: e.toString());
    }
  }
}

// Provider
final arProvider = StateNotifierProvider<ARNotifier, ARState>((ref) {
  final arService = ref.watch(arServiceProvider);
  return ARNotifier(arService);
});
```

## Navigation

CultureConnect uses GoRouter for navigation, which provides:

1. **Declarative Routes**: Routes are defined in a central location
2. **Deep Linking**: Support for deep links and web URLs
3. **Nested Navigation**: Support for nested navigation
4. **Path Parameters**: Type-safe path parameters

### Route Configuration

```dart
final router = GoRouter(
  routes: [
    GoRoute(
      path: '/',
      builder: (context, state) => const SplashScreen(),
    ),
    GoRoute(
      path: '/onboarding',
      builder: (context, state) => const OnboardingScreen(),
    ),
    GoRoute(
      path: '/home',
      builder: (context, state) => const MainNavigation(),
      routes: [
        GoRoute(
          path: 'ar',
          builder: (context, state) => const ARExploreScreen(),
          routes: [
            GoRoute(
              path: 'landmark/:id',
              builder: (context, state) {
                final landmarkId = state.params['id']!;
                return ARLandmarkScreen(landmarkId: landmarkId);
              },
            ),
          ],
        ),
      ],
    ),
  ],
);
```

## Data Flow

Data flows through the application in a unidirectional manner:

1. **User Interaction**: User interacts with the UI
2. **State Update**: UI calls methods on providers
3. **Business Logic**: Providers call services
4. **Data Access**: Services call repositories
5. **API/Database**: Repositories interact with APIs or local storage
6. **Response**: Data flows back up the chain
7. **UI Update**: UI rebuilds based on the new state

### Example Data Flow

```
User taps "Load AR Models" button
  ↓
ARExploreScreen calls arProvider.loadModels()
  ↓
ARNotifier updates state to loading
  ↓
ARNotifier calls arService.getModels()
  ↓
ARService calls arRepository.fetchModels()
  ↓
ARRepository makes API request
  ↓
API returns model data
  ↓
ARRepository parses response into ARModel objects
  ↓
ARService processes and optimizes models
  ↓
ARNotifier updates state with models
  ↓
ARExploreScreen rebuilds with new models
```

## Services

Services contain the business logic of the application. They:

1. Coordinate between repositories
2. Transform data
3. Handle business rules
4. Manage application state

### Service Organization

Services are organized by feature and responsibility:

```
services/
├── auth_service.dart         # Authentication logic
├── user_service.dart         # User profile management
├── ar/
│   ├── ar_service.dart       # General AR functionality
│   ├── ar_content_service.dart # AR content management
│   ├── ar_recording_service.dart # AR recording functionality
│   └── ar_voice_command_service.dart # Voice commands
```

### Service Implementation

Services are implemented as classes with dependency injection:

```dart
class ARService {
  final ARRepository _repository;
  final CacheService _cacheService;

  ARService(this._repository, this._cacheService);

  Future<List<ARModel>> getModels() async {
    // Check cache first
    final cachedModels = await _cacheService.getARModels();
    if (cachedModels.isNotEmpty) {
      return cachedModels;
    }

    // Fetch from repository
    final models = await _repository.fetchModels();

    // Cache for future use
    await _cacheService.saveARModels(models);

    return models;
  }
}
```

## UI Components

UI components are organized into screens and reusable widgets:

### Screens

Screens represent full pages in the application. They:

1. Consume providers to access state
2. Handle user interactions
3. Manage screen-level state
4. Compose widgets to build the UI

### Widgets

Widgets are reusable UI components. They:

1. Accept parameters for customization
2. Have a single responsibility
3. Are composable
4. Can be stateful or stateless

### UI Best Practices

1. **Composition over Inheritance**: Prefer composing widgets over extending them
2. **Separation of Concerns**: Keep UI logic separate from business logic
3. **Responsive Design**: Use ScreenUtil for responsive layouts
4. **Accessibility**: Ensure all UI elements are accessible
5. **Theming**: Use theme data for consistent styling

## Testing Strategy

CultureConnect has a comprehensive testing strategy:

### Unit Tests

Test individual units of code in isolation:

- Services
- Providers
- Repositories
- Utility functions

### Widget Tests

Test UI components in isolation:

- Individual widgets
- Screens
- Navigation flows

### Integration Tests

Test multiple components working together:

- End-to-end user flows
- API integration
- Device feature integration

### Performance Tests

Test application performance:

- Startup time
- Frame rate during AR
- Memory usage
- Battery consumption

## Performance Considerations

AR applications are performance-intensive. Key considerations:

1. **Lazy Loading**: Load resources only when needed
2. **Asset Optimization**: Optimize 3D models and textures
3. **Memory Management**: Dispose resources when not in use
4. **Background Processing**: Perform heavy tasks in isolates
5. **Caching**: Cache frequently used data
6. **Efficient Rebuilds**: Minimize UI rebuilds

## Coding Standards

CultureConnect follows the [Effective Dart](https://dart.dev/guides/language/effective-dart) style guide with some additional conventions:

1. **File Naming**: `snake_case.dart` for file names
2. **Class Naming**: `PascalCase` for class names
3. **Variable Naming**: `camelCase` for variables and methods
4. **Private Members**: Prefix private members with `_`
5. **Documentation**: Document all public APIs
6. **Immutability**: Prefer `final` variables and immutable objects
7. **Error Handling**: Use explicit error handling with meaningful messages
8. **Logging**: Use structured logging for debugging

For more details, see the [Code Documentation Guide](../code_documentation_guide.md).
