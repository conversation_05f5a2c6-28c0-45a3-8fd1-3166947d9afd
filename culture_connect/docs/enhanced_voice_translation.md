# Enhanced Voice Translation Documentation

## Overview

The Enhanced Voice Translation feature in CultureConnect provides advanced translation capabilities with offline support, dialect recognition, custom vocabulary, and conversation mode. This document outlines the technical implementation details of these features.

## Core Models

### LanguageModel

The `LanguageModel` represents a language supported by the translation system.

```dart
class LanguageModel {
  final String code;
  final String name;
  final String flag;
  final bool isOfflineAvailable;
  final double downloadSizeMB;
  final bool isDownloaded;
  final double downloadProgress;
  final bool isDownloading;
  final List<String> supportedDialects;
  
  // Constructor, copyWith, toJson, fromJson methods
}
```

### LanguagePackModel

The `LanguagePackModel` represents a downloaded language pack for offline translation.

```dart
class LanguagePackModel {
  final String languageCode;
  final String version;
  final double sizeMB;
  final String localPath;
  final DateTime downloadedAt;
  final DateTime lastUsedAt;
  final LanguagePackStatus status;
  final double downloadProgress;
  final String? selectedDialect;
  final bool isPrimary;
  
  // Constructor, copyWith, toJson, fromJson methods
}
```

### CustomVocabularyModel

The `CustomVocabularyModel` represents a custom vocabulary term for specialized translation.

```dart
class CustomVocabularyModel {
  final String id;
  final String originalTerm;
  final String originalLanguageCode;
  final Map<String, String> translations;
  final VocabularyCategory category;
  final String? customCategory;
  final String? description;
  final DateTime createdAt;
  final DateTime updatedAt;
  final int usageCount;
  final bool isFavorite;
  
  // Constructor, copyWith, toJson, fromJson methods
}
```

### ConversationModel

The `ConversationModel` represents a two-way conversation with translation.

```dart
class ConversationModel {
  final String id;
  final String title;
  final String userLanguageCode;
  final String otherLanguageCode;
  final List<ConversationTurn> turns;
  final ConversationStatus status;
  final DateTime createdAt;
  final DateTime updatedAt;
  final bool isFavorite;
  final bool autoDetectLanguage;
  final bool speakerIdentification;
  
  // Constructor, copyWith, toJson, fromJson methods
}
```

## Services

### LanguagePackService

The `LanguagePackService` manages the downloading, storage, and usage of language packs for offline translation.

Key responsibilities:
- Download language packs
- Track download progress
- Manage language pack storage
- Set primary language
- Set selected dialect
- Delete language packs

### CustomVocabularyService

The `CustomVocabularyService` manages custom vocabulary terms for specialized translation.

Key responsibilities:
- Add vocabulary terms
- Update vocabulary terms
- Delete vocabulary terms
- Toggle favorite status
- Track usage count
- Search vocabulary terms
- Filter vocabulary terms by category, language, etc.

### ConversationService

The `ConversationService` manages two-way conversations with translation.

Key responsibilities:
- Start conversations
- Add conversation turns
- End/pause/resume conversations
- Delete conversations
- Toggle favorite status
- Update conversation settings

### VoiceTranslationService

The enhanced `VoiceTranslationService` now supports offline translation, dialect recognition, and custom vocabulary.

Key responsibilities:
- Start translations
- Record audio
- Process translations with offline support
- Apply dialect recognition
- Apply custom vocabulary
- Play translated audio
- Manage translation history

## Providers

### VoiceTranslationEnhancedProviders

The enhanced providers include:

- `languagePacksProvider`: Stream of language packs
- `languagePackProvider`: Provider for a specific language pack
- `primaryLanguagePackProvider`: Provider for the primary language pack
- `downloadedLanguagePacksProvider`: Provider for downloaded language packs
- `downloadLanguagePackProvider`: Provider for downloading a language pack
- `languagePackDownloadProgressProvider`: Stream of download progress
- `customVocabularyTermsProvider`: Stream of vocabulary terms
- `customVocabularyTermProvider`: Provider for a specific vocabulary term
- `customVocabularyTermsByCategoryProvider`: Provider for vocabulary terms by category
- `customVocabularyTermsByLanguageProvider`: Provider for vocabulary terms by language
- `favoriteCustomVocabularyTermsProvider`: Provider for favorite vocabulary terms
- `mostUsedCustomVocabularyTermsProvider`: Provider for most used vocabulary terms
- `recentlyAddedCustomVocabularyTermsProvider`: Provider for recently added vocabulary terms
- `conversationsProvider`: Stream of conversations
- `currentConversationProvider`: Stream of the current conversation
- `conversationProvider`: Provider for a specific conversation
- `activeConversationsProvider`: Provider for active conversations
- `favoriteConversationsProvider`: Provider for favorite conversations
- `useOfflineModeProvider`: Provider for offline mode setting
- `useDialectRecognitionProvider`: Provider for dialect recognition setting
- `useCustomVocabularyProvider`: Provider for custom vocabulary setting
- `customVocabularyNotifierProvider`: Notifier for custom vocabulary actions
- `conversationNotifierProvider`: Notifier for conversation actions

## UI Components

### LanguagePackItem

The `LanguagePackItem` displays a language pack with download status, progress, and actions.

### CustomVocabularyItem

The `CustomVocabularyItem` displays a custom vocabulary term with translations, category, and actions.

### ConversationTurnItem

The `ConversationTurnItem` displays a conversation turn with original and translated text, speaker role, and actions.

## Screens

### EnhancedVoiceTranslationScreen

The `EnhancedVoiceTranslationScreen` provides an enhanced interface for voice translation with offline mode, dialect recognition, and custom vocabulary.

Features:
- Language selection
- Feature indicators (offline, dialect, vocabulary)
- Conversation mode button
- Translation result display
- Voice recording button
- Settings access

### LanguagePackManagerScreen

The `LanguagePackManagerScreen` allows users to manage language packs for offline translation.

Features:
- Downloaded language packs list
- Available language packs list
- Download progress tracking
- Primary language selection
- Dialect selection
- Language pack deletion

### CustomVocabularyScreen

The `CustomVocabularyScreen` allows users to manage custom vocabulary terms for specialized translation.

Features:
- All terms list
- Category-based grouping
- Favorites list
- Term addition/editing/deletion
- Term details display
- Usage tracking

### ConversationScreen

The `ConversationScreen` provides a two-way conversation interface with real-time translation.

Features:
- Language selection for both speakers
- Speaker role switching
- Conversation history display
- Voice recording for both speakers
- Conversation settings
- Playback of translated audio

## Integration Points

The enhanced voice translation features integrate with:

- **Messaging System**: Future integration for translating messages
- **Offline Mode**: Uses the offline mode infrastructure for language packs
- **User Preferences**: Stores and retrieves user preferences for translation settings
- **Main App Navigation**: Accessible from the main voice translation screen

## Usage Examples

### Offline Translation

```dart
// Check if offline mode is enabled
final useOfflineMode = ref.watch(useOfflineModeProvider);

// Check if a language pack is downloaded
final isDownloaded = ref.watch(languagePackProvider(languageCode))?.isDownloaded ?? false;

// Download a language pack
await ref.read(languagePackServiceProvider).downloadLanguagePack(languageCode);

// Use offline translation
if (useOfflineMode && isDownloaded) {
  // Translation will use the offline model
}
```

### Custom Vocabulary

```dart
// Add a custom vocabulary term
await ref.read(customVocabularyNotifierProvider.notifier).addVocabularyTerm(
  originalTerm: 'example',
  originalLanguageCode: 'en',
  translations: {'fr': 'exemple'},
  category: VocabularyCategory.general,
);

// Get custom vocabulary for a language
final terms = ref.watch(customVocabularyTermsByLanguageProvider('en'));

// Toggle favorite status
await ref.read(customVocabularyNotifierProvider.notifier).toggleFavorite(termId);
```

### Conversation Mode

```dart
// Start a new conversation
await ref.read(conversationNotifierProvider.notifier).startConversation(
  userLanguageCode: 'en',
  otherLanguageCode: 'fr',
  autoDetectLanguage: false,
  speakerIdentification: false,
);

// Add a conversation turn
await ref.read(conversationNotifierProvider.notifier).addConversationTurn(
  conversationId: conversationId,
  role: ConversationRole.user,
  audioPath: audioPath,
  sourceLanguageCode: 'en',
  targetLanguageCode: 'fr',
);

// End a conversation
await ref.read(conversationNotifierProvider.notifier).endConversation(conversationId);
```

## Future Enhancements

1. **Integration with Messaging System**: Translate messages in real-time
2. **Translation Accuracy Feedback**: Allow users to provide feedback on translation quality
3. **Cultural Context Awareness**: Provide cultural context for translations
4. **Slang and Idiom Handling**: Improve translation of slang and idioms
5. **Pronunciation Guidance**: Provide guidance on pronunciation
6. **Group Conversation Translation**: Support translation for group conversations
