# CultureConnect Deployment Guide

## Table of Contents
1. [Introduction](#introduction)
2. [Prerequisites](#prerequisites)
3. [CI/CD Pipeline](#ci-cd-pipeline)
4. [Version Management](#version-management)
5. [Release Management](#release-management)
6. [Beta Distribution](#beta-distribution)
7. [Production Deployment](#production-deployment)
8. [Monitoring Setup](#monitoring-setup)
9. [Backup Strategy](#backup-strategy)
10. [Rollback Procedures](#rollback-procedures)

## Introduction

This deployment guide provides comprehensive instructions for deploying the CultureConnect application. It covers the CI/CD pipeline, version management, release management, beta distribution, production deployment, monitoring setup, backup strategy, and rollback procedures.

## Prerequisites

Before deploying the CultureConnect application, ensure you have the following:

- GitHub account with access to the CultureConnect repository
- Apple Developer account for iOS deployment
- Google Play Developer account for Android deployment
- Firebase account for beta distribution and monitoring
- AWS account for backups (optional)
- Fastlane installed for iOS deployment
- Flutter SDK installed

## CI/CD Pipeline

The CultureConnect application uses GitHub Actions for continuous integration and continuous deployment. The CI/CD pipeline is defined in the `.github/workflows/ci_cd.yml` file.

### Pipeline Stages

1. **Test and Build**: Runs on every push to the `main` and `develop` branches, and on pull requests to these branches. It runs tests and builds the application.
2. **Android Build**: Runs when a tag is pushed. It builds the Android app bundle and uploads it to the Play Store.
3. **iOS Build**: Runs when a tag is pushed. It builds the iOS app and uploads it to TestFlight.
4. **Firebase Distribution**: Runs when a push is made to the `develop` branch. It distributes the app to testers via Firebase App Distribution.
5. **Monitoring Setup**: Runs when a tag is pushed. It sets up monitoring for the new version.
6. **Backup**: Runs when a tag is pushed. It creates a backup of the release artifacts.

### Running the Pipeline

The pipeline runs automatically when code is pushed to the repository. You can also manually trigger the workflow from the GitHub Actions tab.

## Version Management

Version management is handled by the `scripts/version_management.sh` script. This script helps manage version numbers and create release tags.

### Bumping the Version

To bump the version number, run:

```bash
./scripts/version_management.sh bump [major|minor|patch]
```

This will update the version number in the `pubspec.yaml` file and the `lib/utils/version.dart` file (if it exists).

### Creating a Tag

To create a git tag for the current version, run:

```bash
./scripts/version_management.sh tag
```

This will create a git tag with the current version number.

### Creating a Release Branch

To create a release branch and push it to the remote repository, run:

```bash
./scripts/version_management.sh release
```

This will create a release branch with the current version number and push it to the remote repository.

## Release Management

Release management involves planning, scheduling, and controlling the release of the CultureConnect application. The release process is as follows:

1. **Planning**: Determine the features and bug fixes to include in the release.
2. **Development**: Implement the features and bug fixes on feature branches.
3. **Testing**: Test the features and bug fixes on the feature branches.
4. **Integration**: Merge the feature branches into the `develop` branch.
5. **Release Preparation**: Create a release branch from the `develop` branch.
6. **Release Testing**: Test the release branch.
7. **Release**: Merge the release branch into the `main` branch and tag the release.
8. **Deployment**: Deploy the release to the App Store and Play Store.

## Beta Distribution

Beta distribution is handled by Firebase App Distribution. The CI/CD pipeline automatically distributes the app to testers when a push is made to the `develop` branch.

### Adding Testers

To add testers to Firebase App Distribution, follow these steps:

1. Go to the Firebase console.
2. Select the CultureConnect project.
3. Go to App Distribution.
4. Click on "Add testers".
5. Enter the email addresses of the testers.
6. Click "Add testers".

### Distributing a Beta Build

To distribute a beta build manually, run:

```bash
flutter build apk --release
firebase appdistribution:distribute build/app/outputs/flutter-apk/app-release.apk --app <app-id> --groups testers
```

Replace `<app-id>` with your Firebase app ID.

## Production Deployment

Production deployment is handled by the CI/CD pipeline. When a tag is pushed to the repository, the pipeline builds the app and uploads it to the App Store and Play Store.

### App Store Submission

The iOS app is submitted to the App Store using Fastlane. The `ios/fastlane/Fastfile` file defines the lanes for beta and production deployment.

To submit the app to the App Store manually, run:

```bash
cd ios
bundle exec fastlane release
```

### Play Store Submission

The Android app is submitted to the Play Store using the Google Play Developer API. The CI/CD pipeline uploads the app bundle to the Play Store.

To submit the app to the Play Store manually, run:

```bash
flutter build appbundle --release
```

Then upload the app bundle to the Play Store manually.

## Monitoring Setup

Monitoring is set up using Firebase Crashlytics, Firebase Performance Monitoring, and New Relic. The `scripts/monitoring_setup.sh` script helps set up monitoring for the application.

### Setting Up Monitoring

To set up monitoring, run:

```bash
./scripts/monitoring_setup.sh setup
```

This will set up Firebase Crashlytics, Firebase Performance Monitoring, and New Relic.

### Updating Monitoring Configuration

To update the monitoring configuration, run:

```bash
./scripts/monitoring_setup.sh update
```

This will update the monitoring configuration.

## Backup Strategy

The backup strategy is implemented using the `scripts/backup_strategy.sh` script. This script manages backups for the application.

### Creating a Backup

To create a backup, run:

```bash
./scripts/backup_strategy.sh backup
```

This will create a backup of the application and upload it to S3 if the AWS CLI is available.

### Restoring a Backup

To restore a backup, run:

```bash
./scripts/backup_strategy.sh restore [backup_id]
```

Replace `[backup_id]` with the ID of the backup to restore.

### Listing Backups

To list available backups, run:

```bash
./scripts/backup_strategy.sh list
```

This will list all available backups.

## Rollback Procedures

Rollback procedures are implemented using the `scripts/rollback_procedures.sh` script. This script manages rollback procedures for the application.

### Rolling Back to a Specific Version

To rollback to a specific version, run:

```bash
./scripts/rollback_procedures.sh rollback [version]
```

Replace `[version]` with the version to rollback to.

### Listing Available Versions

To list available versions for rollback, run:

```bash
./scripts/rollback_procedures.sh list
```

This will list all available versions for rollback.

### Checking Rollback Status

To check the rollback status, run:

```bash
./scripts/rollback_procedures.sh status
```

This will show the current version and whether it is a rollback version.
