# CultureConnect Code Documentation Guide

This guide outlines the standards and best practices for documenting code in the CultureConnect project. Consistent documentation helps maintain code quality, improves collaboration, and makes onboarding new developers easier.

## Table of Contents

1. [General Guidelines](#general-guidelines)
2. [Class Documentation](#class-documentation)
3. [Method Documentation](#method-documentation)
4. [Variable Documentation](#variable-documentation)
5. [File Headers](#file-headers)
6. [Code Examples](#code-examples)
7. [TODO Comments](#todo-comments)
8. [Markdown Documentation](#markdown-documentation)

## General Guidelines

- Write documentation as if you're explaining the code to someone who is familiar with Flutter but not with our codebase.
- Keep documentation concise but complete.
- Update documentation when you change code.
- Use proper grammar and spelling.
- Follow <PERSON><PERSON>'s documentation style using `///` for documentation comments.
- Use [Dartdoc](https://dart.dev/guides/language/effective-dart/documentation) conventions.

## Class Documentation

Every class should have a documentation comment that explains:

1. What the class represents or does
2. How it's intended to be used
3. Any important implementation details
4. Any dependencies or requirements

Example:

```dart
/// A service that handles AR content loading and optimization.
///
/// This service is responsible for:
/// - Loading AR models and textures
/// - Optimizing AR content for different device capabilities
/// - Caching AR assets for offline use
/// - Managing AR content lifecycle
///
/// Typical usage:
/// ```dart
/// final arService = ARContentService();
/// await arService.initialize();
/// final model = await arService.loadModel('landmark_1');
/// ```
class ARContentService {
  // Class implementation
}
```

## Method Documentation

Document all public methods with:

1. A brief description of what the method does
2. Parameter descriptions
3. Return value description
4. Any exceptions that might be thrown
5. Example usage for complex methods

Example:

```dart
/// Loads an AR model from the given [modelId].
///
/// The [quality] parameter determines the model resolution:
/// - 'low': Simplified model for low-end devices
/// - 'medium': Standard quality for most devices
/// - 'high': High-resolution model for high-end devices
///
/// Returns a [Future] that completes with the loaded [ARModel].
/// If the model cannot be found, returns `null`.
///
/// Throws [ARLoadException] if there's an error during loading.
///
/// Example:
/// ```dart
/// final model = await loadARModel('eiffel_tower', quality: 'medium');
/// ```
Future<ARModel?> loadARModel(String modelId, {String quality = 'medium'}) async {
  // Method implementation
}
```

## Variable Documentation

Document class-level variables and complex local variables:

```dart
/// The maximum number of AR models that can be loaded simultaneously.
/// This limit helps manage memory usage on low-end devices.
final int maxSimultaneousModels = 5;

/// A map of model IDs to their loaded [ARModel] instances.
/// Used for caching models that have already been loaded.
final Map<String, ARModel> _loadedModels = {};
```

## File Headers

Each file should start with a header comment that describes the purpose of the file:

```dart
/// CultureConnect AR Experience Screen
///
/// This file contains the implementation of the AR experience screen,
/// which allows users to explore cultural landmarks in augmented reality.
/// It includes the AR view, controls, and information display.
```

## Code Examples

Include code examples in documentation when:

- The usage is not immediately obvious
- There are multiple ways to use the code
- The code has complex parameters or return values

Format examples using triple backticks with the language specified:

```dart
/// Example:
/// ```dart
/// final landmark = await arService.findNearbyLandmark(
///   latitude: 48.8584,
///   longitude: 2.2945,
///   radius: 100,
/// );
/// ```
```

## TODO Comments

Use TODO comments to mark code that needs future attention:

```dart
// TODO: Implement caching for offline mode
// TODO(username): Optimize this algorithm for better performance
```

Always include:
1. The TODO keyword in all caps
2. Optionally, the name of the person responsible in parentheses
3. A clear description of what needs to be done

## Markdown Documentation

For standalone documentation files (like this one), use Markdown format:

1. Use headers (`#`, `##`, etc.) for section titles
2. Use bullet points (`-` or `*`) for lists
3. Use code blocks (triple backticks) for code examples
4. Use links to reference other documentation
5. Include a table of contents for longer documents

## Example Documentation

Here's a complete example of a well-documented class:

```dart
/// A service that manages AR voice commands for hands-free interaction.
///
/// This service provides functionality to:
/// - Initialize voice recognition
/// - Process voice commands
/// - Map commands to AR actions
/// - Provide feedback for recognized commands
///
/// The service supports both built-in commands and custom user-defined commands.
class ARVoiceCommandService {
  /// Whether voice commands are currently enabled.
  bool isVoiceCommandsEnabled = false;

  /// Whether the service is currently listening for commands.
  bool isListening = false;

  /// A map of command phrases to their corresponding actions.
  final Map<String, VoiceCommandAction> _commandMap = {};

  /// Initializes the voice command service.
  ///
  /// This method sets up the voice recognition engine and loads
  /// the default command set. If [customCommands] is provided,
  /// these commands will be added to the default set.
  ///
  /// Returns a [Future] that completes with `true` if initialization
  /// was successful, or `false` otherwise.
  ///
  /// Example:
  /// ```dart
  /// final success = await voiceCommandService.initialize(
  ///   customCommands: {'show info': VoiceCommandAction.showInfo},
  /// );
  /// ```
  Future<bool> initialize({
    Map<String, VoiceCommandAction>? customCommands,
  }) async {
    // Implementation
    return true;
  }

  /// Starts listening for voice commands.
  ///
  /// Returns a [Future] that completes with `true` if listening
  /// started successfully, or `false` otherwise.
  ///
  /// Throws [VoiceRecognitionException] if there's an error with
  /// the voice recognition engine.
  Future<bool> startListening() async {
    // Implementation
    return true;
  }

  /// Processes a voice command string and returns the corresponding action.
  ///
  /// The [command] parameter is the raw string from voice recognition.
  /// Returns a [VoiceCommandResult] with the recognized command and parameters.
  ///
  /// If the command is not recognized, the `recognized` field in the result
  /// will be `false`.
  VoiceCommandResult processCommand(String command) {
    // Implementation
    return VoiceCommandResult(
      recognized: true,
      command: 'zoom',
      parameters: {'direction': 'in'},
    );
  }
}
```

## Generating Documentation

To generate HTML documentation from your Dart code:

1. Install `dartdoc`:
   ```bash
   dart pub global activate dartdoc
   ```

2. Run `dartdoc` in the project root:
   ```bash
   dartdoc
   ```

3. The generated documentation will be in the `doc/api` directory.

## Conclusion

Following these documentation guidelines will help maintain a high-quality, understandable codebase for the CultureConnect project. Good documentation is an investment that pays off in reduced bugs, faster onboarding, and better collaboration.
