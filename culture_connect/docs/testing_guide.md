# CultureConnect Testing Guide

This guide provides detailed information about testing practices, patterns, and tools used in the CultureConnect project. It serves as a comprehensive reference for developers working on tests.

## Table of Contents

1. [Testing Philosophy](#testing-philosophy)
2. [Test Types](#test-types)
3. [Test Directory Structure](#test-directory-structure)
4. [Writing Effective Tests](#writing-effective-tests)
5. [Mocking and Test Doubles](#mocking-and-test-doubles)
6. [Testing Providers](#testing-providers)
7. [Testing UI Components](#testing-ui-components)
8. [Performance Testing](#performance-testing)
9. [Test Coverage](#test-coverage)
10. [Continuous Integration](#continuous-integration)
11. [Troubleshooting](#troubleshooting)

## Testing Philosophy

At CultureConnect, we believe that testing is an integral part of the development process, not an afterthought. Our testing philosophy is guided by these principles:

1. **Test Early, Test Often**: Write tests alongside or before the implementation code
2. **Test Behavior, Not Implementation**: Focus on what the code does, not how it does it
3. **Comprehensive Coverage**: Aim for high test coverage across all layers of the application
4. **Fast Feedback**: Tests should run quickly to provide immediate feedback
5. **Maintainable Tests**: Tests should be easy to understand and maintain

## Test Types

### Unit Tests

Unit tests verify that individual units of code work as expected in isolation. In CultureConnect, we primarily write unit tests for:

- Services
- Repositories
- Providers
- Utility functions
- Models

Unit tests should be:
- Fast
- Independent
- Focused on a single unit of functionality
- Comprehensive in covering edge cases

### Widget Tests

Widget tests verify that UI components render correctly and respond appropriately to user interactions. We write widget tests for:

- Individual widgets
- Screens
- Navigation flows
- UI state changes

Widget tests should:
- Test UI rendering
- Verify user interactions
- Check state-dependent UI changes
- Validate accessibility features

### Integration Tests

Integration tests verify that multiple components work together correctly. We write integration tests for:

- End-to-end user flows
- API integration
- Device feature integration
- Cross-component interactions

Integration tests should:
- Test realistic user scenarios
- Verify data flow between components
- Validate system behavior as a whole

### Performance Tests

Performance tests measure and verify the application's performance characteristics. We write performance tests for:

- Startup time
- Frame rate during AR experiences
- Memory usage
- Battery consumption
- Network efficiency

Performance tests should:
- Establish performance baselines
- Detect performance regressions
- Validate performance on different devices

## Test Directory Structure

```
test/
├── unit/                     # Unit tests
│   ├── models/               # Tests for data models
│   ├── providers/            # Tests for state providers
│   ├── repositories/         # Tests for data repositories
│   └── services/             # Tests for business logic services
├── widget/                   # Widget tests
│   ├── screens/              # Tests for screens
│   └── widgets/              # Tests for reusable widgets
├── integration/              # Integration tests
│   └── flows/                # Tests for user flows
├── performance/              # Performance tests
├── mocks/                    # Shared mock objects
├── fixtures/                 # Test data fixtures
├── utils/                    # Test utilities
└── run_tests.sh              # Test runner script
```

## Writing Effective Tests

### Test Naming

Use descriptive names that clearly indicate what is being tested:

```dart
// Good
test('should return user profile when authenticated', () { ... });

// Bad
test('test user profile', () { ... });
```

### Test Structure

Follow the Arrange-Act-Assert (AAA) pattern:

```dart
test('should update user name', () {
  // Arrange: Set up the test environment
  final userService = MockUserService();
  final user = User(id: '1', name: 'Old Name');
  when(userService.getUser('1')).thenReturn(user);
  
  // Act: Perform the action being tested
  userService.updateUserName('1', 'New Name');
  
  // Assert: Verify the expected outcome
  verify(userService.saveUser(User(id: '1', name: 'New Name'))).called(1);
});
```

### Test Groups

Organize related tests into groups:

```dart
group('UserService', () {
  group('getUser', () {
    test('should return user when found', () { ... });
    test('should return null when user not found', () { ... });
    test('should throw exception when network error occurs', () { ... });
  });
  
  group('updateUser', () {
    test('should update user successfully', () { ... });
    test('should throw exception when validation fails', () { ... });
  });
});
```

### Testing Asynchronous Code

Use `async`/`await` for testing asynchronous code:

```dart
test('should fetch user data asynchronously', () async {
  // Arrange
  final userRepository = MockUserRepository();
  when(userRepository.fetchUser('1')).thenAnswer(
    (_) async => User(id: '1', name: 'Test User')
  );
  
  // Act
  final user = await userRepository.fetchUser('1');
  
  // Assert
  expect(user.name, 'Test User');
});
```

## Mocking and Test Doubles

We use several types of test doubles:

- **Mocks**: Objects that record interactions for verification
- **Stubs**: Objects that provide canned responses
- **Fakes**: Simplified implementations of real components
- **Dummies**: Objects that are passed around but not used

### Using Mockito

We use Mockito for creating mock objects:

```dart
// Generate mocks
@GenerateMocks([UserRepository, AuthService])
import 'user_service_test.mocks.dart';

void main() {
  late MockUserRepository mockUserRepository;
  late MockAuthService mockAuthService;
  late UserService userService;
  
  setUp(() {
    mockUserRepository = MockUserRepository();
    mockAuthService = MockAuthService();
    userService = UserService(mockUserRepository, mockAuthService);
  });
  
  test('should get authenticated user', () async {
    // Arrange
    when(mockAuthService.getCurrentUserId()).thenReturn('1');
    when(mockUserRepository.getUser('1')).thenAnswer(
      (_) async => User(id: '1', name: 'Test User')
    );
    
    // Act
    final user = await userService.getCurrentUser();
    
    // Assert
    expect(user?.name, 'Test User');
    verify(mockAuthService.getCurrentUserId()).called(1);
    verify(mockUserRepository.getUser('1')).called(1);
  });
}
```

### Mocking Platform Channels

For testing code that uses platform channels:

```dart
setUp(() {
  // Mock method channel
  const MethodChannel('plugins.flutter.io/path_provider')
    .setMockMethodCallHandler((MethodCall methodCall) async {
      if (methodCall.method == 'getApplicationDocumentsDirectory') {
        return '/mock/docs/path';
      }
      return null;
    });
});

tearDown(() {
  // Reset mock
  const MethodChannel('plugins.flutter.io/path_provider')
    .setMockMethodCallHandler(null);
});
```

## Testing Providers

### Testing StateNotifier Providers

```dart
test('should update state when action is performed', () async {
  // Create a ProviderContainer for testing
  final container = ProviderContainer(
    overrides: [
      userRepositoryProvider.overrideWithValue(mockUserRepository),
    ],
  );
  
  // Add a listener to track state changes
  final states = <AsyncValue<User>>[];
  container.listen<AsyncValue<User>>(
    userProvider,
    (_, state) => states.add(state),
    fireImmediately: true,
  );
  
  // Initial state should be loading
  expect(states[0], const AsyncValue<User>.loading());
  
  // Trigger the action
  await container.read(userProvider.notifier).fetchUser('1');
  
  // Final state should be data
  expect(states.last, isA<AsyncData<User>>());
  expect(states.last.value?.name, 'Test User');
  
  // Dispose the container
  container.dispose();
});
```

### Testing Provider Dependencies

```dart
test('should depend on other providers', () {
  final container = ProviderContainer(
    overrides: [
      authServiceProvider.overrideWithValue(mockAuthService),
      userRepositoryProvider.overrideWithValue(mockUserRepository),
    ],
  );
  
  // Setup mocks
  when(mockAuthService.isAuthenticated).thenReturn(true);
  when(mockAuthService.currentUserId).thenReturn('1');
  when(mockUserRepository.getUser('1')).thenAnswer(
    (_) async => User(id: '1', name: 'Test User')
  );
  
  // Read the provider
  final userState = container.read(userProvider);
  
  // Verify dependencies were used
  verify(mockAuthService.isAuthenticated).called(1);
  verify(mockAuthService.currentUserId).called(1);
  
  container.dispose();
});
```

## Testing UI Components

### Widget Testing Basics

```dart
testWidgets('should display user information', (WidgetTester tester) async {
  // Build the widget
  await tester.pumpWidget(
    MaterialApp(
      home: UserProfileWidget(user: User(id: '1', name: 'Test User')),
    ),
  );
  
  // Verify UI elements
  expect(find.text('Test User'), findsOneWidget);
  expect(find.byIcon(Icons.person), findsOneWidget);
  
  // Interact with the widget
  await tester.tap(find.byType(ElevatedButton));
  await tester.pump();
  
  // Verify state changes
  expect(find.text('Profile Updated'), findsOneWidget);
});
```

### Testing Riverpod Widgets

```dart
testWidgets('should display data from provider', (WidgetTester tester) async {
  // Create a ProviderScope with overridden providers
  await tester.pumpWidget(
    ProviderScope(
      overrides: [
        userProvider.overrideWith((ref) => AsyncData(User(id: '1', name: 'Test User'))),
      ],
      child: const MaterialApp(
        home: UserProfileScreen(),
      ),
    ),
  );
  
  // Wait for async operations
  await tester.pumpAndSettle();
  
  // Verify UI reflects provider state
  expect(find.text('Test User'), findsOneWidget);
});
```

### Testing Navigation

```dart
testWidgets('should navigate to details screen', (WidgetTester tester) async {
  // Setup navigator observer
  final mockObserver = MockNavigatorObserver();
  
  // Build the widget
  await tester.pumpWidget(
    MaterialApp(
      navigatorObservers: [mockObserver],
      home: const UserListScreen(),
    ),
  );
  
  // Tap on a list item
  await tester.tap(find.text('User 1'));
  await tester.pumpAndSettle();
  
  // Verify navigation occurred
  verify(mockObserver.didPush(any, any)).called(1);
  
  // Verify new screen is displayed
  expect(find.text('User Details'), findsOneWidget);
});
```

## Performance Testing

### Measuring Startup Time

```dart
testWidgets('should start up within acceptable time', (WidgetTester tester) async {
  final stopwatch = Stopwatch()..start();
  
  // Launch the app
  await tester.pumpWidget(const MyApp());
  await tester.pumpAndSettle();
  
  stopwatch.stop();
  
  // Log the startup time
  print('Startup time: ${stopwatch.elapsedMilliseconds}ms');
  
  // Verify startup time is within acceptable range
  expect(stopwatch.elapsedMilliseconds < 2000, true);
});
```

### Measuring Frame Rate

```dart
testWidgets('should maintain acceptable frame rate during AR', (WidgetTester tester) async {
  // Setup frame timing tracking
  final frameTimings = <Duration>[];
  
  final subscription = WidgetsBinding.instance.onFrameTimingsReported.listen((timings) {
    frameTimings.add(timings.totalSpan);
  });
  
  // Launch the app and navigate to AR screen
  await tester.pumpWidget(const MyApp());
  await tester.pumpAndSettle();
  
  await tester.tap(find.text('AR'));
  await tester.pumpAndSettle();
  
  // Interact with AR for a few seconds
  await tester.pump(const Duration(seconds: 5));
  
  // Calculate average frame time
  final totalFrameTime = frameTimings.fold<Duration>(
    Duration.zero, 
    (prev, curr) => prev + curr,
  );
  
  final averageFrameTimeMs = totalFrameTime.inMicroseconds / 
    (frameTimings.length * 1000);
  
  // Log results
  print('Average frame time: ${averageFrameTimeMs}ms');
  print('Estimated FPS: ${1000 / averageFrameTimeMs}');
  
  // Verify frame rate is acceptable (at least 30 FPS)
  expect(1000 / averageFrameTimeMs >= 30, true);
  
  // Clean up
  subscription.cancel();
});
```

## Test Coverage

We aim for high test coverage, but focus on meaningful coverage rather than arbitrary percentage targets.

### Generating Coverage Reports

```bash
# Run tests with coverage
flutter test --coverage

# Generate HTML report
genhtml coverage/lcov.info -o coverage/html

# Open the report
open coverage/html/index.html
```

### Coverage Priorities

Focus test coverage on:

1. Critical business logic
2. Complex algorithms
3. Error-prone areas
4. Public APIs
5. User-facing features

## Continuous Integration

Our CI pipeline runs all tests on every pull request and merge to main branches:

1. **Static Analysis**: Flutter analyze
2. **Unit Tests**: All unit tests
3. **Widget Tests**: All widget tests
4. **Integration Tests**: On selected devices
5. **Coverage Report**: Generated and published

See our [GitHub Actions workflow](../.github/workflows/ci.yml) for details.

## Troubleshooting

### Common Test Issues

1. **Tests hanging**: Usually caused by uncompleted Futures or Streams. Make sure all async operations complete.

2. **Widget tests failing with "No MaterialLocalizations found"**: Wrap your widget with MaterialApp in tests:
   ```dart
   await tester.pumpWidget(MaterialApp(home: YourWidget()));
   ```

3. **Mockito errors**: Ensure you've generated the mock files with `flutter pub run build_runner build`.

4. **Platform channel errors**: Mock platform channels in your tests.

5. **Provider not found errors**: Make sure you're wrapping widgets with ProviderScope in tests.

### Debugging Tests

1. Use `print` statements for simple debugging
2. Set breakpoints in your IDE for step-by-step debugging
3. Use `tester.takeException()` to catch exceptions in widget tests
4. Isolate failing tests by running them individually

## Conclusion

Testing is a critical part of our development process at CultureConnect. By following these guidelines, we can ensure that our application remains robust, reliable, and maintainable as it grows.
