# AR Testing Guide for CultureConnect

This guide provides specific instructions and best practices for testing Augmented Reality (AR) features in the CultureConnect application.

## Table of Contents

1. [Introduction](#introduction)
2. [AR Testing Challenges](#ar-testing-challenges)
3. [Testing AR Services](#testing-ar-services)
4. [Testing AR UI Components](#testing-ar-ui-components)
5. [Testing AR Performance](#testing-ar-performance)
6. [Testing AR Accessibility](#testing-ar-accessibility)
7. [Device-Specific Testing](#device-specific-testing)
8. [Manual Testing Checklist](#manual-testing-checklist)
9. [Troubleshooting](#troubleshooting)

## Introduction

Testing AR features presents unique challenges due to the integration of camera, sensors, 3D rendering, and real-world interaction. This guide outlines approaches to effectively test AR functionality in CultureConnect.

## AR Testing Challenges

AR testing involves several challenges:

1. **Device Dependency**: AR behavior varies across devices with different capabilities
2. **Real-World Context**: AR features interact with the physical environment
3. **Sensor Simulation**: Difficult to simulate camera, gyroscope, and accelerometer in automated tests
4. **3D Rendering**: Testing 3D model loading, rendering, and interactions
5. **Performance Variability**: Performance depends on device capabilities and environmental conditions

## Testing AR Services

### Mocking AR Dependencies

For unit testing AR services, mock the following dependencies:

```dart
// Generate mocks
@GenerateMocks([
  ARCoreController,
  ARKitController,
  CameraController,
  File,
  Directory,
])
import 'ar_service_test.mocks.dart';

void main() {
  late MockARCoreController mockArCoreController;
  late MockCameraController mockCameraController;
  late ARService arService;

  setUp(() {
    mockArCoreController = MockARCoreController();
    mockCameraController = MockCameraController();
    
    // Setup mock responses
    when(mockArCoreController.addArCoreNode(any))
        .thenAnswer((_) async => true);
    
    arService = ARService(
      arCoreController: mockArCoreController,
      cameraController: mockCameraController,
    );
  });
  
  // Tests...
}
```

### Testing AR Model Loading

```dart
test('should load AR model successfully', () async {
  // Arrange
  final mockFile = MockFile();
  when(mockFile.exists()).thenAnswer((_) async => true);
  when(mockFile.path).thenReturn('/mock/path/model.glb');
  
  when(mockArRepository.getModelFile('landmark-1'))
      .thenAnswer((_) async => mockFile);
  
  // Act
  final result = await arService.loadModel('landmark-1');
  
  // Assert
  expect(result, isNotNull);
  expect(result.path, '/mock/path/model.glb');
  verify(mockArRepository.getModelFile('landmark-1')).called(1);
});
```

### Testing AR Content Management

```dart
test('should cache AR content after loading', () async {
  // Arrange
  when(mockArRepository.downloadArContent('ar-eiffel-tower'))
      .thenAnswer((_) async => true);
  
  // Act
  await arService.loadArContent('ar-eiffel-tower');
  final isCached = await arService.isArContentCached('ar-eiffel-tower');
  
  // Assert
  expect(isCached, true);
  verify(mockArRepository.downloadArContent('ar-eiffel-tower')).called(1);
});
```

## Testing AR UI Components

### Mocking AR View

For widget tests involving AR views, create a mock AR view widget:

```dart
class MockARView extends StatelessWidget {
  final String landmarkId;
  final Function(ARModel)? onModelLoaded;

  const MockARView({
    Key? key,
    required this.landmarkId,
    this.onModelLoaded,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // Simulate AR view with a placeholder
    return Container(
      color: Colors.black,
      child: Center(
        child: Text('AR View for $landmarkId', style: TextStyle(color: Colors.white)),
      ),
    );
  }
}

// In your test
testWidgets('AR screen should display landmark information', (WidgetTester tester) async {
  // Override the real AR view with mock
  await tester.pumpWidget(
    MaterialApp(
      home: ARViewScreen(
        landmarkId: 'landmark-1',
        arViewBuilder: (context, landmarkId, onModelLoaded) => MockARView(
          landmarkId: landmarkId,
          onModelLoaded: onModelLoaded,
        ),
      ),
    ),
  );
  
  // Verify UI elements
  expect(find.text('AR View for landmark-1'), findsOneWidget);
  expect(find.byType(ARControlPanel), findsOneWidget);
});
```

### Testing AR Interactions

```dart
testWidgets('should handle pinch to zoom gesture', (WidgetTester tester) async {
  // Arrange
  bool zoomCalled = false;
  
  await tester.pumpWidget(
    MaterialApp(
      home: ARViewScreen(
        landmarkId: 'landmark-1',
        arViewBuilder: (context, landmarkId, onModelLoaded) => MockARView(
          landmarkId: landmarkId,
          onModelLoaded: onModelLoaded,
        ),
        onZoom: (scale) {
          zoomCalled = true;
        },
      ),
    ),
  );
  
  // Act - simulate pinch gesture
  await tester.pinch(find.byType(MockARView), scale: 2.0);
  await tester.pump();
  
  // Assert
  expect(zoomCalled, true);
});
```

## Testing AR Performance

### Frame Rate Testing

```dart
testWidgets('should maintain acceptable frame rate during AR rendering', (WidgetTester tester) async {
  // Setup frame timing tracking
  final frameTimings = <Duration>[];
  
  final subscription = WidgetsBinding.instance.onFrameTimingsReported.listen((timings) {
    frameTimings.add(timings.totalSpan);
  });
  
  // Launch AR view
  await tester.pumpWidget(
    MaterialApp(
      home: ARViewScreen(landmarkId: 'landmark-1'),
    ),
  );
  
  // Simulate AR interactions for a few seconds
  for (int i = 0; i < 10; i++) {
    // Simulate rotation gesture
    await tester.drag(find.byType(ARViewScreen), const Offset(100, 0));
    await tester.pump(const Duration(milliseconds: 100));
  }
  
  // Calculate average frame time
  final totalFrameTime = frameTimings.fold<Duration>(
    Duration.zero, 
    (prev, curr) => prev + curr,
  );
  
  final averageFrameTimeMs = totalFrameTime.inMicroseconds / 
    (frameTimings.length * 1000);
  
  // Verify frame rate is acceptable (at least 30 FPS)
  expect(1000 / averageFrameTimeMs >= 30, true);
  
  // Clean up
  subscription.cancel();
});
```

### Memory Usage Testing

```dart
testWidgets('should not exceed memory threshold during AR session', (WidgetTester tester) async {
  // This test requires integration_test package
  final binding = IntegrationTestWidgetsFlutterBinding.ensureInitialized();
  
  // Measure baseline memory
  final baselineMemory = await binding.traceAction(
    () async {
      await tester.pumpWidget(const MaterialApp(home: Scaffold()));
      await tester.pumpAndSettle();
    },
    reportKey: 'baseline_memory',
  );
  
  // Measure AR memory usage
  final arMemory = await binding.traceAction(
    () async {
      await tester.pumpWidget(
        MaterialApp(home: ARViewScreen(landmarkId: 'landmark-1')),
      );
      await tester.pumpAndSettle();
      
      // Simulate AR usage
      for (int i = 0; i < 5; i++) {
        await tester.drag(find.byType(ARViewScreen), const Offset(100, 0));
        await tester.pump(const Duration(seconds: 1));
      }
    },
    reportKey: 'ar_memory',
  );
  
  // Calculate memory increase
  final baselineRss = baselineMemory['RssAfter'] as int;
  final arRss = arMemory['RssAfter'] as int;
  final memoryIncrease = arRss - baselineRss;
  
  // Verify memory usage is within acceptable limits
  // Actual threshold depends on your app's requirements
  expect(memoryIncrease < 100 * 1024 * 1024, true, // 100MB threshold
    reason: 'AR memory usage increased by ${memoryIncrease / (1024 * 1024)}MB');
});
```

## Testing AR Accessibility

### Testing High Contrast Mode

```dart
testWidgets('should apply high contrast mode to AR UI', (WidgetTester tester) async {
  // Arrange
  final mockARAccessibilityService = MockARAccessibilityService();
  when(mockARAccessibilityService.isHighContrastModeEnabled).thenReturn(true);
  
  // Act
  await tester.pumpWidget(
    ProviderScope(
      overrides: [
        arAccessibilityServiceProvider.overrideWithValue(mockARAccessibilityService),
      ],
      child: MaterialApp(
        home: ARViewScreen(landmarkId: 'landmark-1'),
      ),
    ),
  );
  
  // Assert - verify high contrast UI elements
  final finder = find.byType(ARHighContrastOverlay);
  expect(finder, findsOneWidget);
});
```

### Testing Voice Commands

```dart
testWidgets('should recognize and execute voice commands', (WidgetTester tester) async {
  // Arrange
  final mockARVoiceCommandService = MockARVoiceCommandService();
  when(mockARVoiceCommandService.processCommand('zoom in')).thenReturn(
    VoiceCommandResult(
      recognized: true,
      command: 'zoom',
      parameters: {'direction': 'in'},
    ),
  );
  
  bool zoomInCalled = false;
  
  // Act
  await tester.pumpWidget(
    ProviderScope(
      overrides: [
        arVoiceCommandServiceProvider.overrideWithValue(mockARVoiceCommandService),
      ],
      child: MaterialApp(
        home: ARViewScreen(
          landmarkId: 'landmark-1',
          onZoomIn: () {
            zoomInCalled = true;
          },
        ),
      ),
    ),
  );
  
  // Simulate voice command
  await tester.tap(find.byIcon(Icons.mic));
  await tester.pump();
  
  // Execute the command (in a real app, this would come from the voice recognition)
  await tester.tap(find.byType(ARVoiceCommandButton));
  await tester.pump();
  
  // Assert
  expect(zoomInCalled, true);
});
```

## Device-Specific Testing

For comprehensive AR testing, test on multiple device types:

1. **High-end devices**: Test full AR feature set and performance
2. **Mid-range devices**: Test core functionality with moderate performance
3. **Low-end devices**: Test fallback modes and performance optimizations
4. **Different OS versions**: Test on various Android and iOS versions

Create a device test matrix:

| Device Category | Android Devices | iOS Devices | Test Focus |
|----------------|----------------|------------|------------|
| High-end | Pixel 6+, Samsung S21+ | iPhone 13+ | Full feature set, performance |
| Mid-range | Samsung A52, Pixel 4a | iPhone SE, iPhone 11 | Core functionality |
| Low-end | Devices with 2GB RAM | iPhone 8, iPhone X | Fallback modes |
| Tablet | Samsung Tab S7 | iPad Pro, iPad Air | Large screen layout |

## Manual Testing Checklist

Some AR features require manual testing. Use this checklist:

### AR Initialization
- [ ] AR initializes correctly in different lighting conditions
- [ ] Plane detection works on various surfaces
- [ ] Calibration process is clear and effective

### AR Content Rendering
- [ ] 3D models load correctly and appear at proper scale
- [ ] Textures and materials render properly
- [ ] Animations play smoothly

### AR Interactions
- [ ] Tap to place objects works correctly
- [ ] Pinch to zoom functions properly
- [ ] Rotation gestures work as expected
- [ ] Long press shows additional options

### AR Performance
- [ ] App maintains acceptable frame rate during AR sessions
- [ ] No significant battery drain during extended use
- [ ] Memory usage remains stable during AR sessions

### AR Accessibility
- [ ] Voice commands work in noisy environments
- [ ] High contrast mode improves visibility in bright conditions
- [ ] Audio guidance provides clear instructions

## Troubleshooting

### Common AR Testing Issues

1. **AR initialization failures**:
   - Check that mock controllers properly simulate initialization
   - Verify that permissions are correctly handled in tests

2. **3D model loading issues**:
   - Ensure test fixtures include valid model data
   - Mock file system operations correctly

3. **Gesture recognition problems**:
   - Use proper gesture simulation in widget tests
   - Verify gesture detectors are correctly implemented

4. **Performance test inconsistencies**:
   - Run performance tests on consistent hardware
   - Average results over multiple runs
   - Establish baseline performance metrics

5. **Device-specific failures**:
   - Create device-specific test configurations
   - Use conditional tests based on platform/capabilities

### Debugging AR Issues

For debugging AR-specific issues:

1. Enable AR debug visualization:
   ```dart
   arService.setDebugMode(true);
   ```

2. Log AR tracking state changes:
   ```dart
   arController.onTrackingChanged = (TrackingState state) {
     print('AR tracking state: $state');
   };
   ```

3. Monitor frame timing:
   ```dart
   arView.onFrameUpdate = (double deltaTime) {
     print('Frame time: ${deltaTime * 1000}ms');
   };
   ```

## Conclusion

Testing AR features requires a combination of automated tests, mocking, and manual verification. By following this guide, you can ensure that CultureConnect's AR features work reliably across different devices and environments.
