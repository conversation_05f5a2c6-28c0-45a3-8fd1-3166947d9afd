# CultureConnect Core Infrastructure

This document provides an overview of the core infrastructure components implemented in the CultureConnect application.

## Table of Contents

1. [Error Handling and Logging System](#error-handling-and-logging-system)
2. [Analytics Integration](#analytics-integration)
3. [Crash Reporting Setup](#crash-reporting-setup)
4. [Performance Monitoring](#performance-monitoring)
5. [Service Initialization](#service-initialization)
6. [Usage Examples](#usage-examples)

## Error Handling and Logging System

The error handling and logging system provides a comprehensive solution for tracking, managing, and responding to errors throughout the application.

### Logging Service

The `LoggingService` provides centralized logging functionality with the following features:

- **Log Levels**: Supports multiple log levels (verbose, debug, info, warning, error, critical)
- **Multiple Outputs**: Logs can be sent to console, file, and Firebase Crashlytics
- **Structured Logging**: Includes timestamp, tag, message, and optional data
- **Log Rotation**: Automatically manages log file size and rotation
- **Device Information**: Captures device and app information for context

### Error Handling Service

The `ErrorHandlingService` provides centralized error handling with the following features:

- **Error Categorization**: Categorizes errors by type (network, authentication, etc.)
- **Error Severity**: Assigns severity levels to errors (low, medium, high, critical)
- **User-Friendly Messages**: Converts technical errors to user-friendly messages
- **Global Error Handling**: Captures uncaught Flutter and platform errors
- **UI Integration**: Provides methods for showing error dialogs and snackbars

## Analytics Integration

The analytics integration provides comprehensive tracking of user behavior and app performance.

### Analytics Service

The `AnalyticsService` provides the following features:

- **Event Tracking**: Tracks custom events with categories and parameters
- **Screen Tracking**: Tracks screen views and time spent on screens
- **User Properties**: Sets user properties for segmentation
- **Session Management**: Tracks session start, duration, and end
- **Performance Events**: Tracks performance metrics
- **Search Events**: Tracks search queries and results
- **Authentication Events**: Tracks login and sign-up events
- **Offline Support**: Buffers events when offline

## Crash Reporting Setup

The crash reporting setup provides automatic capture and reporting of app crashes and errors.

### Crash Reporting Service

The `CrashReportingService` provides the following features:

- **Automatic Crash Reporting**: Captures and reports app crashes
- **Non-Fatal Error Reporting**: Reports non-fatal errors with context
- **Custom Keys**: Adds custom keys to crash reports for context
- **User Identification**: Associates crash reports with users
- **Device Information**: Includes device and app information in reports
- **Opt-Out Support**: Allows users to opt out of crash reporting

## Performance Monitoring

The performance monitoring system tracks and analyzes app performance metrics.

### Performance Monitoring Service

The `PerformanceMonitoringService` provides the following features:

- **Trace Monitoring**: Tracks custom traces with start and stop times
- **HTTP Monitoring**: Tracks HTTP request performance
- **Frame Rate Monitoring**: Tracks UI frame rate and jank
- **Memory Usage Monitoring**: Tracks app memory usage
- **Counter Metrics**: Tracks custom counter metrics within traces
- **Performance Attributes**: Adds custom attributes to performance traces
- **Analytics Integration**: Sends performance data to analytics

## Service Initialization

All services are initialized in the `main.dart` file using the following process:

1. Initialize Flutter binding
2. Initialize startup optimization service
3. Create a ProviderContainer for service initialization
4. Initialize services in the correct order:
   - Logging Service
   - Error Handling Service
   - Crash Reporting Service
   - Analytics Service
   - Performance Monitoring Service
5. Run the app with the initialized services

## Usage Examples

### Logging

```dart
// Get the logging service
final loggingService = ref.read(loggingServiceProvider);

// Log messages at different levels
loggingService.debug('MyComponent', 'Debug message');
loggingService.info('MyComponent', 'Info message');
loggingService.warning('MyComponent', 'Warning message');
loggingService.error('MyComponent', 'Error message', error, stackTrace);
```

### Error Handling

```dart
// Get the error handling service
final errorHandlingService = ref.read(errorHandlingServiceProvider);

// Handle an error
try {
  // Some code that might throw an error
} catch (e, stackTrace) {
  await errorHandlingService.handleError(
    error: e,
    context: 'MyComponent.myMethod',
    stackTrace: stackTrace,
    type: ErrorType.network,
    severity: ErrorSeverity.medium,
  );
  
  // Show error to user
  errorHandlingService.showErrorSnackBar(
    context,
    'Failed to load data. Please try again.',
  );
}
```

### Analytics

```dart
// Get the analytics service
final analyticsService = ref.read(analyticsServiceProvider);

// Log screen view
await analyticsService.logScreenView(
  screenName: 'HomeScreen',
  screenClass: 'HomeScreen',
);

// Log custom event
await analyticsService.logEvent(
  name: 'button_click',
  category: AnalyticsCategory.userAction,
  parameters: {
    'button_name': 'submit',
    'screen': 'profile',
  },
);
```

### Performance Monitoring

```dart
// Get the performance monitoring service
final performanceService = ref.read(performanceMonitoringServiceProvider);

// Track a custom trace
await performanceService.startTrace('data_loading', PerformanceMetricType.network);

try {
  // Load data
  final data = await loadData();
  
  // Increment counter
  await performanceService.incrementTraceCounter('data_loading', 'items_loaded', data.length);
  
  // Stop trace
  await performanceService.stopTrace('data_loading', attributes: {
    'data_size': '${data.length}',
    'source': 'api',
  });
} catch (e) {
  // Stop trace with error
  await performanceService.stopTrace('data_loading', attributes: {
    'error': 'true',
    'error_type': e.runtimeType.toString(),
  });
  rethrow;
}
```

### Crash Reporting

```dart
// Get the crash reporting service
final crashReportingService = ref.read(crashReportingServiceProvider);

// Set user identifier
await crashReportingService.setUserIdentifier(user.id);

// Set custom key
await crashReportingService.setCustomKey('subscription_level', user.subscriptionLevel);

// Log non-fatal error
try {
  // Some code that might throw an error
} catch (e, stackTrace) {
  await crashReportingService.logError(
    error: e,
    stackTrace: stackTrace,
    reason: 'Error loading user profile',
    fatal: false,
    customKeys: {
      'user_id': user.id,
      'profile_complete': user.isProfileComplete.toString(),
    },
  );
}
```
