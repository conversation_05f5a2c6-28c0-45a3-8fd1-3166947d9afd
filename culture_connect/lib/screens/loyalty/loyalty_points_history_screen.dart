import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';
import 'package:culture_connect/models/loyalty/loyalty_points_transaction.dart';
import 'package:culture_connect/models/loyalty/loyalty_program_model.dart';
import 'package:culture_connect/providers/loyalty_provider.dart';
import 'package:culture_connect/theme/app_theme.dart';
import 'package:culture_connect/widgets/custom_app_bar.dart';

/// A screen that displays loyalty points history
class LoyaltyPointsHistoryScreen extends ConsumerStatefulWidget {
  /// The loyalty program
  final LoyaltyProgramModel loyaltyProgram;
  
  /// Creates a new loyalty points history screen
  const LoyaltyPointsHistoryScreen({
    super.key,
    required this.loyaltyProgram,
  });
  
  @override
  ConsumerState<LoyaltyPointsHistoryScreen> createState() => _LoyaltyPointsHistoryScreenState();
}

class _LoyaltyPointsHistoryScreenState extends ConsumerState<LoyaltyPointsHistoryScreen> with SingleTickerProviderStateMixin {
  late TabController _tabController;
  
  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }
  
  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final transactionsAsync = ref.watch(loyaltyPointsTransactionsProvider);
    
    // Sort transactions by date (newest first)
    final sortedTransactions = List<LoyaltyPointsTransaction>.from(transactionsAsync)
      ..sort((a, b) => b.date.compareTo(a.date));
    
    // Filter transactions
    final earningTransactions = sortedTransactions.where((t) => t.isEarning).toList();
    final spendingTransactions = sortedTransactions.where((t) => !t.isEarning).toList();
    
    return Scaffold(
      appBar: CustomAppBar(
        title: 'Points History',
        showBackButton: true,
      ),
      body: Column(
        children: [
          // Points summary
          Container(
            padding: EdgeInsets.all(16.r),
            color: AppTheme.primaryColor.withOpacity(0.1),
            child: Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Current Balance',
                        style: TextStyle(
                          fontSize: 14.sp,
                          color: AppTheme.textSecondaryColor,
                        ),
                      ),
                      SizedBox(height: 4.h),
                      Text(
                        '${widget.loyaltyProgram.pointsBalance}',
                        style: TextStyle(
                          fontSize: 24.sp,
                          fontWeight: FontWeight.bold,
                          color: AppTheme.textPrimaryColor,
                        ),
                      ),
                    ],
                  ),
                ),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Lifetime Points',
                        style: TextStyle(
                          fontSize: 14.sp,
                          color: AppTheme.textSecondaryColor,
                        ),
                      ),
                      SizedBox(height: 4.h),
                      Text(
                        '${widget.loyaltyProgram.lifetimePoints}',
                        style: TextStyle(
                          fontSize: 24.sp,
                          fontWeight: FontWeight.bold,
                          color: AppTheme.textPrimaryColor,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          
          // Tab bar
          TabBar(
            controller: _tabController,
            tabs: const [
              Tab(text: 'All'),
              Tab(text: 'Earned'),
              Tab(text: 'Spent'),
            ],
            labelColor: AppTheme.primaryColor,
            unselectedLabelColor: AppTheme.textSecondaryColor,
            indicatorColor: AppTheme.primaryColor,
          ),
          
          // Tab content
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                // All transactions
                _buildTransactionsList(sortedTransactions),
                
                // Earning transactions
                _buildTransactionsList(earningTransactions),
                
                // Spending transactions
                _buildTransactionsList(spendingTransactions),
              ],
            ),
          ),
        ],
      ),
    );
  }
  
  Widget _buildTransactionsList(List<LoyaltyPointsTransaction> transactions) {
    if (transactions.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.history,
              size: 48.r,
              color: Colors.grey[400],
            ),
            SizedBox(height: 16.h),
            Text(
              'No transactions found',
              style: TextStyle(
                fontSize: 16.sp,
                color: AppTheme.textSecondaryColor,
              ),
            ),
          ],
        ),
      );
    }
    
    // Group transactions by month
    final groupedTransactions = <String, List<LoyaltyPointsTransaction>>{};
    
    for (final transaction in transactions) {
      final monthYear = DateFormat('MMMM yyyy').format(transaction.date);
      
      if (!groupedTransactions.containsKey(monthYear)) {
        groupedTransactions[monthYear] = [];
      }
      
      groupedTransactions[monthYear]!.add(transaction);
    }
    
    // Sort months (newest first)
    final sortedMonths = groupedTransactions.keys.toList()
      ..sort((a, b) {
        final dateA = DateFormat('MMMM yyyy').parse(a);
        final dateB = DateFormat('MMMM yyyy').parse(b);
        return dateB.compareTo(dateA);
      });
    
    return ListView.builder(
      padding: EdgeInsets.all(16.r),
      itemCount: sortedMonths.length,
      itemBuilder: (context, index) {
        final month = sortedMonths[index];
        final monthTransactions = groupedTransactions[month]!;
        
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Month header
            Padding(
              padding: EdgeInsets.symmetric(vertical: 8.h),
              child: Text(
                month,
                style: TextStyle(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.bold,
                  color: AppTheme.textPrimaryColor,
                ),
              ),
            ),
            
            // Month transactions
            ...monthTransactions.map((transaction) => _buildTransactionItem(transaction)),
            
            // Divider
            if (index < sortedMonths.length - 1)
              Divider(height: 32.h),
          ],
        );
      },
    );
  }
  
  Widget _buildTransactionItem(LoyaltyPointsTransaction transaction) {
    final isEarning = transaction.isEarning;
    final pointsColor = isEarning ? Colors.green : Colors.red;
    
    return Card(
      margin: EdgeInsets.only(bottom: 8.h),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8.r),
      ),
      child: Padding(
        padding: EdgeInsets.all(12.r),
        child: Row(
          children: [
            // Transaction type icon
            Container(
              width: 40.r,
              height: 40.r,
              decoration: BoxDecoration(
                color: transaction.type.isEarning
                    ? Colors.green.withOpacity(0.1)
                    : Colors.red.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8.r),
              ),
              child: Center(
                child: Icon(
                  transaction.type.icon,
                  size: 20.r,
                  color: transaction.type.isEarning ? Colors.green : Colors.red,
                ),
              ),
            ),
            
            SizedBox(width: 12.w),
            
            // Transaction details
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    transaction.type.displayName,
                    style: TextStyle(
                      fontSize: 14.sp,
                      fontWeight: FontWeight.bold,
                      color: AppTheme.textPrimaryColor,
                    ),
                  ),
                  SizedBox(height: 4.h),
                  Text(
                    transaction.description,
                    style: TextStyle(
                      fontSize: 12.sp,
                      color: AppTheme.textSecondaryColor,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  SizedBox(height: 4.h),
                  Text(
                    _formatDate(transaction.date),
                    style: TextStyle(
                      fontSize: 10.sp,
                      color: AppTheme.textSecondaryColor,
                    ),
                  ),
                ],
              ),
            ),
            
            SizedBox(width: 12.w),
            
            // Points
            Text(
              transaction.formattedPoints,
              style: TextStyle(
                fontSize: 16.sp,
                fontWeight: FontWeight.bold,
                color: pointsColor,
              ),
            ),
          ],
        ),
      ),
    );
  }
  
  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);
    
    if (difference.inDays < 1) {
      return 'Today, ${DateFormat('h:mm a').format(date)}';
    } else if (difference.inDays < 2) {
      return 'Yesterday, ${DateFormat('h:mm a').format(date)}';
    } else if (difference.inDays < 7) {
      return DateFormat('EEEE, h:mm a').format(date);
    } else {
      return DateFormat('MMM d, yyyy, h:mm a').format(date);
    }
  }
}
