import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:culture_connect/models/loyalty/loyalty_program_model.dart';
import 'package:culture_connect/models/loyalty/loyalty_reward.dart';
import 'package:culture_connect/providers/loyalty_provider.dart';
import 'package:culture_connect/screens/loyalty/loyalty_reward_details_screen.dart';
import 'package:culture_connect/theme/app_theme.dart';
import 'package:culture_connect/widgets/common/error_view.dart';
import 'package:culture_connect/widgets/common/loading_indicator.dart';
import 'package:culture_connect/widgets/custom_app_bar.dart';

/// A screen that displays loyalty rewards
class LoyaltyRewardsScreen extends ConsumerStatefulWidget {
  /// The loyalty program
  final LoyaltyProgramModel loyaltyProgram;
  
  /// Creates a new loyalty rewards screen
  const LoyaltyRewardsScreen({
    super.key,
    required this.loyaltyProgram,
  });
  
  @override
  ConsumerState<LoyaltyRewardsScreen> createState() => _LoyaltyRewardsScreenState();
}

class _LoyaltyRewardsScreenState extends ConsumerState<LoyaltyRewardsScreen> with SingleTickerProviderStateMixin {
  late TabController _tabController;
  
  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }
  
  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final rewardsAsync = ref.watch(loyaltyRewardsProvider);
    
    return Scaffold(
      appBar: CustomAppBar(
        title: 'Rewards',
        showBackButton: true,
      ),
      body: Column(
        children: [
          // Points summary
          Container(
            padding: EdgeInsets.all(16.r),
            color: AppTheme.primaryColor.withOpacity(0.1),
            child: Row(
              children: [
                Icon(
                  Icons.star,
                  size: 24.r,
                  color: AppTheme.primaryColor,
                ),
                SizedBox(width: 12.w),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Your Points Balance',
                        style: TextStyle(
                          fontSize: 14.sp,
                          color: AppTheme.textSecondaryColor,
                        ),
                      ),
                      SizedBox(height: 4.h),
                      Text(
                        '${widget.loyaltyProgram.pointsBalance}',
                        style: TextStyle(
                          fontSize: 24.sp,
                          fontWeight: FontWeight.bold,
                          color: AppTheme.textPrimaryColor,
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
                  decoration: BoxDecoration(
                    color: widget.loyaltyProgram.tier.color.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(16.r),
                    border: Border.all(
                      color: widget.loyaltyProgram.tier.color,
                      width: 1.w,
                    ),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        widget.loyaltyProgram.tier.icon,
                        size: 16.r,
                        color: widget.loyaltyProgram.tier.color,
                      ),
                      SizedBox(width: 4.w),
                      Text(
                        widget.loyaltyProgram.tier.displayName,
                        style: TextStyle(
                          fontSize: 14.sp,
                          fontWeight: FontWeight.bold,
                          color: widget.loyaltyProgram.tier.color,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          
          // Tab bar
          TabBar(
            controller: _tabController,
            tabs: const [
              Tab(text: 'Available'),
              Tab(text: 'Redeemed'),
              Tab(text: 'All'),
            ],
            labelColor: AppTheme.primaryColor,
            unselectedLabelColor: AppTheme.textSecondaryColor,
            indicatorColor: AppTheme.primaryColor,
          ),
          
          // Tab content
          Expanded(
            child: rewardsAsync.when(
              data: (rewards) {
                // Filter rewards
                final availableRewards = rewards
                    .where((reward) => reward.status == LoyaltyRewardStatus.available)
                    .toList();
                final redeemedRewards = rewards
                    .where((reward) => reward.status == LoyaltyRewardStatus.redeemed)
                    .toList();
                
                return TabBarView(
                  controller: _tabController,
                  children: [
                    // Available rewards
                    _buildRewardsList(availableRewards),
                    
                    // Redeemed rewards
                    _buildRewardsList(redeemedRewards),
                    
                    // All rewards
                    _buildRewardsList(rewards),
                  ],
                );
              },
              loading: () => const Center(
                child: LoadingIndicator(),
              ),
              error: (error, stackTrace) => Center(
                child: ErrorView(
                  error: error.toString(),
                  onRetry: () => ref.refresh(loyaltyRewardsProvider),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
  
  Widget _buildRewardsList(List<LoyaltyReward> rewards) {
    if (rewards.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.card_giftcard,
              size: 48.r,
              color: Colors.grey[400],
            ),
            SizedBox(height: 16.h),
            Text(
              'No rewards found',
              style: TextStyle(
                fontSize: 16.sp,
                color: AppTheme.textSecondaryColor,
              ),
            ),
          ],
        ),
      );
    }
    
    // Sort rewards by points required (lowest first)
    final sortedRewards = List<LoyaltyReward>.from(rewards)
      ..sort((a, b) => a.pointsRequired.compareTo(b.pointsRequired));
    
    // Group rewards by type
    final groupedRewards = <LoyaltyRewardType, List<LoyaltyReward>>{};
    
    for (final reward in sortedRewards) {
      if (!groupedRewards.containsKey(reward.type)) {
        groupedRewards[reward.type] = [];
      }
      
      groupedRewards[reward.type]!.add(reward);
    }
    
    // Sort types by name
    final sortedTypes = groupedRewards.keys.toList()
      ..sort((a, b) => a.displayName.compareTo(b.displayName));
    
    return ListView.builder(
      padding: EdgeInsets.all(16.r),
      itemCount: sortedTypes.length,
      itemBuilder: (context, index) {
        final type = sortedTypes[index];
        final typeRewards = groupedRewards[type]!;
        
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Type header
            Padding(
              padding: EdgeInsets.symmetric(vertical: 8.h),
              child: Row(
                children: [
                  Icon(
                    type.icon,
                    size: 20.r,
                    color: AppTheme.primaryColor,
                  ),
                  SizedBox(width: 8.w),
                  Text(
                    type.displayName,
                    style: TextStyle(
                      fontSize: 18.sp,
                      fontWeight: FontWeight.bold,
                      color: AppTheme.textPrimaryColor,
                    ),
                  ),
                ],
              ),
            ),
            
            // Type rewards
            ...typeRewards.map((reward) => _buildRewardItem(reward)),
            
            // Divider
            if (index < sortedTypes.length - 1)
              Divider(height: 32.h),
          ],
        );
      },
    );
  }
  
  Widget _buildRewardItem(LoyaltyReward reward) {
    final isRedeemed = reward.status == LoyaltyRewardStatus.redeemed;
    final canRedeem = !isRedeemed &&
        widget.loyaltyProgram.pointsBalance >= reward.pointsRequired &&
        (reward.minimumTier == null || widget.loyaltyProgram.tier.index >= reward.minimumTier!.index);
    
    return Card(
      margin: EdgeInsets.only(bottom: 16.h),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: InkWell(
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => LoyaltyRewardDetailsScreen(
                reward: reward,
                loyaltyProgram: widget.loyaltyProgram,
              ),
            ),
          );
        },
        borderRadius: BorderRadius.circular(12.r),
        child: Padding(
          padding: EdgeInsets.all(16.r),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Reward name and status
              Row(
                children: [
                  Expanded(
                    child: Text(
                      reward.name,
                      style: TextStyle(
                        fontSize: 16.sp,
                        fontWeight: FontWeight.bold,
                        color: AppTheme.textPrimaryColor,
                      ),
                    ),
                  ),
                  if (isRedeemed)
                    Container(
                      padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
                      decoration: BoxDecoration(
                        color: Colors.green.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(16.r),
                        border: Border.all(
                          color: Colors.green,
                          width: 1.w,
                        ),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            Icons.check_circle,
                            size: 14.r,
                            color: Colors.green,
                          ),
                          SizedBox(width: 4.w),
                          Text(
                            'Redeemed',
                            style: TextStyle(
                              fontSize: 12.sp,
                              fontWeight: FontWeight.bold,
                              color: Colors.green,
                            ),
                          ),
                        ],
                      ),
                    ),
                ],
              ),
              
              SizedBox(height: 8.h),
              
              // Reward description
              Text(
                reward.description,
                style: TextStyle(
                  fontSize: 14.sp,
                  color: AppTheme.textSecondaryColor,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              
              SizedBox(height: 16.h),
              
              // Points required and value
              Row(
                children: [
                  // Points required
                  Row(
                    children: [
                      Icon(
                        Icons.star,
                        size: 16.r,
                        color: AppTheme.primaryColor,
                      ),
                      SizedBox(width: 4.w),
                      Text(
                        '${reward.pointsRequired} points',
                        style: TextStyle(
                          fontSize: 14.sp,
                          fontWeight: FontWeight.bold,
                          color: AppTheme.textPrimaryColor,
                        ),
                      ),
                    ],
                  ),
                  
                  SizedBox(width: 16.w),
                  
                  // Value
                  if (reward.formattedValue != null) ...[
                    Icon(
                      Icons.monetization_on,
                      size: 16.r,
                      color: Colors.amber,
                    ),
                    SizedBox(width: 4.w),
                    Text(
                      'Value: ${reward.formattedValue}',
                      style: TextStyle(
                        fontSize: 14.sp,
                        color: AppTheme.textSecondaryColor,
                      ),
                    ),
                  ],
                ],
              ),
              
              SizedBox(height: 16.h),
              
              // Redeem button or redemption details
              if (isRedeemed) ...[
                Container(
                  padding: EdgeInsets.all(8.r),
                  decoration: BoxDecoration(
                    color: Colors.grey[100],
                    borderRadius: BorderRadius.circular(8.r),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.info_outline,
                        size: 16.r,
                        color: AppTheme.textSecondaryColor,
                      ),
                      SizedBox(width: 8.w),
                      Expanded(
                        child: Text(
                          'Tap to view redemption details',
                          style: TextStyle(
                            fontSize: 12.sp,
                            color: AppTheme.textSecondaryColor,
                          ),
                        ),
                      ),
                      Icon(
                        Icons.arrow_forward_ios,
                        size: 12.r,
                        color: AppTheme.textSecondaryColor,
                      ),
                    ],
                  ),
                ),
              ] else ...[
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: canRedeem
                        ? () => _redeemReward(reward)
                        : null,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppTheme.primaryColor,
                      foregroundColor: Colors.white,
                      padding: EdgeInsets.symmetric(vertical: 12.h),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8.r),
                      ),
                    ),
                    child: Text(
                      canRedeem ? 'Redeem Reward' : 'Not Enough Points',
                      style: TextStyle(
                        fontSize: 14.sp,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }
  
  Future<void> _redeemReward(LoyaltyReward reward) async {
    // Show confirmation dialog
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Redeem Reward'),
        content: Text(
          'Are you sure you want to redeem ${reward.name} for ${reward.pointsRequired} points?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.primaryColor,
              foregroundColor: Colors.white,
            ),
            child: const Text('Redeem'),
          ),
        ],
      ),
    );
    
    if (confirmed == true) {
      try {
        // Show loading indicator
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Processing redemption...'),
            duration: Duration(seconds: 1),
          ),
        );
        
        // Redeem the reward
        await ref.read(redeemLoyaltyRewardNotifierProvider.notifier).redeemReward(reward.id);
        
        if (context.mounted) {
          // Show success message
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Successfully redeemed ${reward.name}'),
              backgroundColor: Colors.green,
            ),
          );
          
          // Navigate to reward details screen
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => LoyaltyRewardDetailsScreen(
                reward: reward.copyWith(
                  status: LoyaltyRewardStatus.redeemed,
                  redemptionDate: DateTime.now(),
                ),
                loyaltyProgram: widget.loyaltyProgram.copyWith(
                  pointsBalance: widget.loyaltyProgram.pointsBalance - reward.pointsRequired,
                ),
              ),
            ),
          );
        }
      } catch (e) {
        if (context.mounted) {
          // Show error message
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Error redeeming reward: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      } finally {
        // Reset the notifier
        ref.read(redeemLoyaltyRewardNotifierProvider.notifier).reset();
      }
    }
  }
}
