import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:culture_connect/models/loyalty/loyalty_program_model.dart';
import 'package:culture_connect/providers/loyalty_provider.dart';
import 'package:culture_connect/screens/loyalty/loyalty_points_history_screen.dart';
import 'package:culture_connect/screens/loyalty/loyalty_rewards_screen.dart';
import 'package:culture_connect/theme/app_theme.dart';
import 'package:culture_connect/widgets/common/error_view.dart';
import 'package:culture_connect/widgets/common/loading_indicator.dart';
import 'package:culture_connect/widgets/custom_app_bar.dart';
import 'package:culture_connect/widgets/loyalty/loyalty_points_history.dart';
import 'package:culture_connect/widgets/loyalty/loyalty_rewards_list.dart';
import 'package:culture_connect/widgets/loyalty/loyalty_tier_card.dart';

/// A screen that displays the loyalty dashboard
class LoyaltyDashboardScreen extends ConsumerWidget {
  /// Creates a new loyalty dashboard screen
  const LoyaltyDashboardScreen({super.key});
  
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final loyaltyProgramAsync = ref.watch(loyaltyProgramProvider);
    
    return Scaffold(
      appBar: CustomAppBar(
        title: 'Loyalty Program',
        showBackButton: true,
      ),
      body: loyaltyProgramAsync.when(
        data: (loyaltyProgram) {
          if (loyaltyProgram == null) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.card_membership,
                    size: 64.r,
                    color: Colors.grey[400],
                  ),
                  SizedBox(height: 16.h),
                  Text(
                    'No loyalty program found',
                    style: TextStyle(
                      fontSize: 18.sp,
                      fontWeight: FontWeight.bold,
                      color: AppTheme.textPrimaryColor,
                    ),
                  ),
                  SizedBox(height: 8.h),
                  Text(
                    'Please try again later',
                    style: TextStyle(
                      fontSize: 16.sp,
                      color: AppTheme.textSecondaryColor,
                    ),
                  ),
                ],
              ),
            );
          }
          
          return SingleChildScrollView(
            padding: EdgeInsets.all(16.r),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Loyalty tier card
                LoyaltyTierCard(
                  loyaltyProgram: loyaltyProgram,
                  showProgressBar: true,
                  showNextTier: true,
                  showBenefits: false,
                  onTap: () => _showTierDetailsBottomSheet(context, loyaltyProgram),
                ),
                
                SizedBox(height: 24.h),
                
                // Quick actions
                Text(
                  'Quick Actions',
                  style: TextStyle(
                    fontSize: 18.sp,
                    fontWeight: FontWeight.bold,
                    color: AppTheme.textPrimaryColor,
                  ),
                ),
                
                SizedBox(height: 16.h),
                
                // Quick action buttons
                Row(
                  children: [
                    Expanded(
                      child: _buildQuickActionButton(
                        context,
                        'View Rewards',
                        Icons.card_giftcard,
                        () => Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => LoyaltyRewardsScreen(
                              loyaltyProgram: loyaltyProgram,
                            ),
                          ),
                        ),
                      ),
                    ),
                    SizedBox(width: 16.w),
                    Expanded(
                      child: _buildQuickActionButton(
                        context,
                        'Points History',
                        Icons.history,
                        () => Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => LoyaltyPointsHistoryScreen(
                              loyaltyProgram: loyaltyProgram,
                            ),
                          ),
                        ),
                      ),
                    ),
                    SizedBox(width: 16.w),
                    Expanded(
                      child: _buildQuickActionButton(
                        context,
                        'Tier Benefits',
                        Icons.stars,
                        () => _showTierDetailsBottomSheet(context, loyaltyProgram),
                      ),
                    ),
                  ],
                ),
                
                SizedBox(height: 24.h),
                
                // Points history
                LoyaltyPointsHistory(
                  maxTransactions: 3,
                  showSeeAllButton: true,
                  onSeeAllTapped: () => Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => LoyaltyPointsHistoryScreen(
                        loyaltyProgram: loyaltyProgram,
                      ),
                    ),
                  ),
                ),
                
                SizedBox(height: 24.h),
                
                // Available rewards
                LoyaltyRewardsList(
                  loyaltyProgram: loyaltyProgram,
                  maxRewards: 3,
                  showSeeAllButton: true,
                  onSeeAllTapped: () => Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => LoyaltyRewardsScreen(
                        loyaltyProgram: loyaltyProgram,
                      ),
                    ),
                  ),
                ),
                
                SizedBox(height: 24.h),
              ],
            ),
          );
        },
        loading: () => const Center(
          child: LoadingIndicator(),
        ),
        error: (error, stackTrace) => Center(
          child: ErrorView(
            error: error.toString(),
            onRetry: () => ref.refresh(loyaltyProgramProvider),
          ),
        ),
      ),
    );
  }
  
  Widget _buildQuickActionButton(
    BuildContext context,
    String label,
    IconData icon,
    VoidCallback onTap,
  ) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8.r),
      child: Container(
        padding: EdgeInsets.all(12.r),
        decoration: BoxDecoration(
          color: Colors.grey[100],
          borderRadius: BorderRadius.circular(8.r),
        ),
        child: Column(
          children: [
            Icon(
              icon,
              size: 24.r,
              color: AppTheme.primaryColor,
            ),
            SizedBox(height: 8.h),
            Text(
              label,
              style: TextStyle(
                fontSize: 12.sp,
                fontWeight: FontWeight.bold,
                color: AppTheme.textPrimaryColor,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
  
  void _showTierDetailsBottomSheet(BuildContext context, LoyaltyProgramModel loyaltyProgram) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(16.r),
        ),
      ),
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.7,
        minChildSize: 0.5,
        maxChildSize: 0.9,
        expand: false,
        builder: (context, scrollController) => SingleChildScrollView(
          controller: scrollController,
          padding: EdgeInsets.all(16.r),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              Center(
                child: Container(
                  width: 40.w,
                  height: 4.h,
                  decoration: BoxDecoration(
                    color: Colors.grey[300],
                    borderRadius: BorderRadius.circular(2.r),
                  ),
                ),
              ),
              
              SizedBox(height: 16.h),
              
              // Title
              Text(
                'Loyalty Tiers & Benefits',
                style: TextStyle(
                  fontSize: 20.sp,
                  fontWeight: FontWeight.bold,
                  color: AppTheme.textPrimaryColor,
                ),
              ),
              
              SizedBox(height: 24.h),
              
              // Current tier
              Text(
                'Your Current Tier',
                style: TextStyle(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.bold,
                  color: AppTheme.textPrimaryColor,
                ),
              ),
              
              SizedBox(height: 8.h),
              
              // Current tier card
              Container(
                padding: EdgeInsets.all(16.r),
                decoration: BoxDecoration(
                  color: loyaltyProgram.tier.color.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8.r),
                  border: Border.all(
                    color: loyaltyProgram.tier.color,
                    width: 1.w,
                  ),
                ),
                child: Row(
                  children: [
                    Icon(
                      loyaltyProgram.tier.icon,
                      size: 32.r,
                      color: loyaltyProgram.tier.color,
                    ),
                    SizedBox(width: 16.w),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            loyaltyProgram.tier.displayName,
                            style: TextStyle(
                              fontSize: 18.sp,
                              fontWeight: FontWeight.bold,
                              color: loyaltyProgram.tier.color,
                            ),
                          ),
                          SizedBox(height: 4.h),
                          Text(
                            '${loyaltyProgram.lifetimePoints} lifetime points',
                            style: TextStyle(
                              fontSize: 14.sp,
                              color: AppTheme.textSecondaryColor,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
              
              SizedBox(height: 16.h),
              
              // Current tier benefits
              Text(
                'Your Benefits',
                style: TextStyle(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.bold,
                  color: AppTheme.textPrimaryColor,
                ),
              ),
              
              SizedBox(height: 8.h),
              
              // Benefits list
              ...loyaltyProgram.tier.benefits.map((benefit) => Padding(
                padding: EdgeInsets.only(bottom: 8.h),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Icon(
                      Icons.check_circle,
                      size: 16.r,
                      color: AppTheme.primaryColor,
                    ),
                    SizedBox(width: 8.w),
                    Expanded(
                      child: Text(
                        benefit,
                        style: TextStyle(
                          fontSize: 14.sp,
                          color: AppTheme.textSecondaryColor,
                        ),
                      ),
                    ),
                  ],
                ),
              )).toList(),
              
              SizedBox(height: 24.h),
              
              // All tiers
              Text(
                'All Loyalty Tiers',
                style: TextStyle(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.bold,
                  color: AppTheme.textPrimaryColor,
                ),
              ),
              
              SizedBox(height: 16.h),
              
              // Tiers list
              ...LoyaltyTier.values.map((tier) => _buildTierItem(
                context,
                tier,
                isCurrentTier: tier == loyaltyProgram.tier,
              )).toList(),
            ],
          ),
        ),
      ),
    );
  }
  
  Widget _buildTierItem(
    BuildContext context,
    LoyaltyTier tier, {
    bool isCurrentTier = false,
  }) {
    return Container(
      margin: EdgeInsets.only(bottom: 16.h),
      padding: EdgeInsets.all(16.r),
      decoration: BoxDecoration(
        color: isCurrentTier ? tier.color.withOpacity(0.1) : Colors.grey[100],
        borderRadius: BorderRadius.circular(8.r),
        border: Border.all(
          color: isCurrentTier ? tier.color : Colors.grey[300]!,
          width: isCurrentTier ? 1.w : 1.w,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Tier header
          Row(
            children: [
              Icon(
                tier.icon,
                size: 24.r,
                color: tier.color,
              ),
              SizedBox(width: 8.w),
              Text(
                tier.displayName,
                style: TextStyle(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.bold,
                  color: tier.color,
                ),
              ),
              const Spacer(),
              Text(
                '${tier.pointsRequired}+ points',
                style: TextStyle(
                  fontSize: 14.sp,
                  fontWeight: FontWeight.bold,
                  color: AppTheme.textPrimaryColor,
                ),
              ),
            ],
          ),
          
          SizedBox(height: 8.h),
          
          // Tier benefits
          Text(
            'Benefits:',
            style: TextStyle(
              fontSize: 14.sp,
              fontWeight: FontWeight.bold,
              color: AppTheme.textPrimaryColor,
            ),
          ),
          
          SizedBox(height: 4.h),
          
          ...tier.benefits.take(3).map((benefit) => Padding(
            padding: EdgeInsets.only(bottom: 4.h),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Icon(
                  Icons.check_circle,
                  size: 14.r,
                  color: isCurrentTier ? tier.color : AppTheme.textSecondaryColor,
                ),
                SizedBox(width: 4.w),
                Expanded(
                  child: Text(
                    benefit,
                    style: TextStyle(
                      fontSize: 12.sp,
                      color: AppTheme.textSecondaryColor,
                    ),
                  ),
                ),
              ],
            ),
          )).toList(),
          
          if (tier.benefits.length > 3) ...[
            SizedBox(height: 4.h),
            Text(
              '+ ${tier.benefits.length - 3} more benefits',
              style: TextStyle(
                fontSize: 12.sp,
                fontStyle: FontStyle.italic,
                color: AppTheme.textSecondaryColor,
              ),
            ),
          ],
        ],
      ),
    );
  }
}
