import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:culture_connect/models/loyalty/loyalty_program_model.dart';
import 'package:culture_connect/models/loyalty/loyalty_reward.dart';
import 'package:culture_connect/providers/loyalty_provider.dart';
import 'package:culture_connect/theme/app_theme.dart';
import 'package:culture_connect/widgets/custom_app_bar.dart';
import 'package:lottie/lottie.dart';

/// A screen that displays loyalty reward details
class LoyaltyRewardDetailsScreen extends ConsumerWidget {
  /// The loyalty reward to display
  final LoyaltyReward reward;
  
  /// The loyalty program
  final LoyaltyProgramModel loyaltyProgram;
  
  /// Creates a new loyalty reward details screen
  const LoyaltyRewardDetailsScreen({
    super.key,
    required this.reward,
    required this.loyaltyProgram,
  });
  
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final isRedeemed = reward.status == LoyaltyRewardStatus.redeemed;
    final canRedeem = !isRedeemed &&
        loyaltyProgram.pointsBalance >= reward.pointsRequired &&
        (reward.minimumTier == null || loyaltyProgram.tier.index >= reward.minimumTier!.index);
    
    return Scaffold(
      appBar: CustomAppBar(
        title: 'Reward Details',
        showBackButton: true,
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(16.r),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Reward image or icon
            Center(
              child: reward.imageUrl != null
                  ? ClipRRect(
                      borderRadius: BorderRadius.circular(16.r),
                      child: Image.network(
                        reward.imageUrl!,
                        width: double.infinity,
                        height: 200.h,
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) => _buildRewardIcon(),
                      ),
                    )
                  : _buildRewardIcon(),
            ),
            
            SizedBox(height: 24.h),
            
            // Reward name and status
            Row(
              children: [
                Expanded(
                  child: Text(
                    reward.name,
                    style: TextStyle(
                      fontSize: 24.sp,
                      fontWeight: FontWeight.bold,
                      color: AppTheme.textPrimaryColor,
                    ),
                  ),
                ),
                if (isRedeemed)
                  Container(
                    padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
                    decoration: BoxDecoration(
                      color: Colors.green.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(16.r),
                      border: Border.all(
                        color: Colors.green,
                        width: 1.w,
                      ),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          Icons.check_circle,
                          size: 16.r,
                          color: Colors.green,
                        ),
                        SizedBox(width: 4.w),
                        Text(
                          'Redeemed',
                          style: TextStyle(
                            fontSize: 14.sp,
                            fontWeight: FontWeight.bold,
                            color: Colors.green,
                          ),
                        ),
                      ],
                    ),
                  ),
              ],
            ),
            
            SizedBox(height: 8.h),
            
            // Reward type
            Row(
              children: [
                Icon(
                  reward.type.icon,
                  size: 16.r,
                  color: AppTheme.primaryColor,
                ),
                SizedBox(width: 8.w),
                Text(
                  reward.type.displayName,
                  style: TextStyle(
                    fontSize: 16.sp,
                    color: AppTheme.primaryColor,
                  ),
                ),
              ],
            ),
            
            SizedBox(height: 16.h),
            
            // Reward description
            Text(
              reward.description,
              style: TextStyle(
                fontSize: 16.sp,
                color: AppTheme.textSecondaryColor,
              ),
            ),
            
            SizedBox(height: 24.h),
            
            // Reward details
            Card(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12.r),
              ),
              child: Padding(
                padding: EdgeInsets.all(16.r),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Reward Details',
                      style: TextStyle(
                        fontSize: 18.sp,
                        fontWeight: FontWeight.bold,
                        color: AppTheme.textPrimaryColor,
                      ),
                    ),
                    SizedBox(height: 16.h),
                    _buildDetailRow(
                      'Points Required',
                      '${reward.pointsRequired} points',
                      Icons.star,
                    ),
                    if (reward.formattedValue != null)
                      _buildDetailRow(
                        'Value',
                        reward.formattedValue!,
                        Icons.monetization_on,
                      ),
                    if (reward.minimumTier != null)
                      _buildDetailRow(
                        'Minimum Tier',
                        reward.minimumTier!.displayName,
                        reward.minimumTier!.icon,
                        iconColor: reward.minimumTier!.color,
                      ),
                    if (reward.expirationDate != null)
                      _buildDetailRow(
                        'Expires On',
                        reward.formattedExpirationDate!,
                        Icons.event,
                      ),
                    if (isRedeemed && reward.redemptionDate != null)
                      _buildDetailRow(
                        'Redeemed On',
                        reward.formattedRedemptionDate!,
                        Icons.check_circle,
                        iconColor: Colors.green,
                      ),
                  ],
                ),
              ),
            ),
            
            SizedBox(height: 24.h),
            
            // Redemption code (if redeemed)
            if (isRedeemed && reward.redemptionCode != null) ...[
              Card(
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12.r),
                ),
                color: Colors.green.withOpacity(0.1),
                child: Padding(
                  padding: EdgeInsets.all(16.r),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Redemption Code',
                        style: TextStyle(
                          fontSize: 18.sp,
                          fontWeight: FontWeight.bold,
                          color: AppTheme.textPrimaryColor,
                        ),
                      ),
                      SizedBox(height: 16.h),
                      Container(
                        width: double.infinity,
                        padding: EdgeInsets.all(16.r),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(8.r),
                          border: Border.all(
                            color: Colors.grey[300]!,
                            width: 1.w,
                          ),
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(
                              reward.redemptionCode!,
                              style: TextStyle(
                                fontSize: 24.sp,
                                fontWeight: FontWeight.bold,
                                letterSpacing: 2.w,
                                color: AppTheme.textPrimaryColor,
                              ),
                            ),
                            SizedBox(width: 16.w),
                            IconButton(
                              icon: const Icon(Icons.copy),
                              onPressed: () {
                                Clipboard.setData(ClipboardData(text: reward.redemptionCode!));
                                ScaffoldMessenger.of(context).showSnackBar(
                                  const SnackBar(
                                    content: Text('Redemption code copied to clipboard'),
                                    backgroundColor: Colors.green,
                                  ),
                                );
                              },
                              tooltip: 'Copy to clipboard',
                            ),
                          ],
                        ),
                      ),
                      SizedBox(height: 16.h),
                      Text(
                        'Present this code when redeeming your reward.',
                        style: TextStyle(
                          fontSize: 14.sp,
                          color: AppTheme.textSecondaryColor,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
              ),
              
              SizedBox(height: 24.h),
            ],
            
            // Terms and conditions
            if (reward.termsAndConditions != null) ...[
              Text(
                'Terms and Conditions',
                style: TextStyle(
                  fontSize: 18.sp,
                  fontWeight: FontWeight.bold,
                  color: AppTheme.textPrimaryColor,
                ),
              ),
              SizedBox(height: 8.h),
              Text(
                reward.termsAndConditions!,
                style: TextStyle(
                  fontSize: 14.sp,
                  color: AppTheme.textSecondaryColor,
                ),
              ),
              SizedBox(height: 24.h),
            ],
            
            // Redeem button
            if (!isRedeemed)
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: canRedeem
                      ? () => _redeemReward(context, ref)
                      : null,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppTheme.primaryColor,
                    foregroundColor: Colors.white,
                    padding: EdgeInsets.symmetric(vertical: 16.h),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8.r),
                    ),
                  ),
                  child: Text(
                    canRedeem ? 'Redeem Reward' : 'Not Enough Points',
                    style: TextStyle(
                      fontSize: 16.sp,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
  
  Widget _buildRewardIcon() {
    return Container(
      width: 120.r,
      height: 120.r,
      decoration: BoxDecoration(
        color: AppTheme.primaryColor.withOpacity(0.1),
        shape: BoxShape.circle,
      ),
      child: Center(
        child: Icon(
          reward.type.icon,
          size: 60.r,
          color: AppTheme.primaryColor,
        ),
      ),
    );
  }
  
  Widget _buildDetailRow(String label, String value, IconData icon, {Color? iconColor}) {
    return Padding(
      padding: EdgeInsets.only(bottom: 12.h),
      child: Row(
        children: [
          Icon(
            icon,
            size: 20.r,
            color: iconColor ?? AppTheme.primaryColor,
          ),
          SizedBox(width: 12.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: AppTheme.textSecondaryColor,
                  ),
                ),
                SizedBox(height: 4.h),
                Text(
                  value,
                  style: TextStyle(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.bold,
                    color: AppTheme.textPrimaryColor,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
  
  Future<void> _redeemReward(BuildContext context, WidgetRef ref) async {
    // Show confirmation dialog
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Redeem Reward'),
        content: Text(
          'Are you sure you want to redeem ${reward.name} for ${reward.pointsRequired} points?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.primaryColor,
              foregroundColor: Colors.white,
            ),
            child: const Text('Redeem'),
          ),
        ],
      ),
    );
    
    if (confirmed == true) {
      try {
        // Show loading indicator
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Processing redemption...'),
            duration: Duration(seconds: 1),
          ),
        );
        
        // Redeem the reward
        await ref.read(redeemLoyaltyRewardNotifierProvider.notifier).redeemReward(reward.id);
        
        if (context.mounted) {
          // Show success message and animation
          showDialog(
            context: context,
            barrierDismissible: false,
            builder: (context) => AlertDialog(
              content: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Lottie.asset(
                    'assets/animations/success.json',
                    width: 200.r,
                    height: 200.r,
                    repeat: false,
                  ),
                  SizedBox(height: 16.h),
                  Text(
                    'Reward Redeemed!',
                    style: TextStyle(
                      fontSize: 20.sp,
                      fontWeight: FontWeight.bold,
                      color: AppTheme.textPrimaryColor,
                    ),
                  ),
                  SizedBox(height: 8.h),
                  Text(
                    'You have successfully redeemed ${reward.name}.',
                    style: TextStyle(
                      fontSize: 16.sp,
                      color: AppTheme.textSecondaryColor,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
              actions: [
                ElevatedButton(
                  onPressed: () {
                    Navigator.pop(context); // Close dialog
                    Navigator.pop(context); // Go back to previous screen
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppTheme.primaryColor,
                    foregroundColor: Colors.white,
                  ),
                  child: const Text('Done'),
                ),
              ],
            ),
          );
        }
      } catch (e) {
        if (context.mounted) {
          // Show error message
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Error redeeming reward: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      } finally {
        // Reset the notifier
        ref.read(redeemLoyaltyRewardNotifierProvider.notifier).reset();
      }
    }
  }
}
