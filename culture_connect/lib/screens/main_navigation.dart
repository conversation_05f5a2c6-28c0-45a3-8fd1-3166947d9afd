import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:culture_connect/providers/navigation_provider.dart';
import 'package:culture_connect/widgets/custom_bottom_navigation.dart';
import 'package:culture_connect/screens/home_screen.dart';
import 'package:culture_connect/screens/explore_screen.dart';
import 'package:culture_connect/screens/bookings_screen.dart';
import 'package:culture_connect/screens/messaging/chat_list_screen.dart';
import 'package:culture_connect/screens/profile_screen.dart';

class MainNavigation extends ConsumerWidget {
  const MainNavigation({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final currentIndex = ref.watch(navigationProvider);

    return Scaffold(
      body: IndexedStack(
        index: currentIndex.index,
        children: const [
          HomeScreen(),
          ExploreScreen(),
          BookingsScreen(),
          ChatListScreen(),
          ProfileScreen(),
        ],
      ),
      bottomNavigationBar: const CustomBottomNavigation(),
    );
  }
}
