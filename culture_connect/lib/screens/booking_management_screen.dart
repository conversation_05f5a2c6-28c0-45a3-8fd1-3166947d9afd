import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:culture_connect/models/booking.dart';
import 'package:culture_connect/models/experience.dart';
import 'package:culture_connect/services/booking_service.dart';
import 'package:culture_connect/widgets/common/app_button.dart';
import 'package:culture_connect/widgets/common/loading_indicator.dart';
import 'package:culture_connect/screens/write_review_screen.dart';

class BookingManagementScreen extends ConsumerStatefulWidget {
  const BookingManagementScreen({super.key});

  @override
  ConsumerState<BookingManagementScreen> createState() =>
      _BookingManagementScreenState();
}

class _BookingManagementScreenState
    extends ConsumerState<BookingManagementScreen>
    with TickerProviderStateMixin {
  final _bookingService = BookingService();
  bool _isLoading = true;
  String? _errorMessage;
  List<Booking> _allBookings = [];
  List<Booking> _upcomingBookings = [];
  late TabController _tabController;
  final DateFormat _dateFormat = DateFormat('EEE, MMM d, yyyy');

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _loadBookings();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadBookings() async {
    if (!mounted) return;

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      // Load all bookings
      final allBookings = await _bookingService.getUserBookings();
      if (!mounted) return;

      // Load upcoming bookings
      final upcomingBookings = await _bookingService.getUpcomingBookings();
      if (!mounted) return;

      setState(() {
        _allBookings = allBookings.cast<Booking>();
        _upcomingBookings = upcomingBookings.cast<Booking>();
      });
    } catch (e) {
      if (!mounted) return;

      setState(() {
        _errorMessage = e.toString();
      });
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _cancelBooking(String bookingId) async {
    try {
      await _bookingService.cancelBooking(bookingId);
      if (mounted) {
        await _loadBookings(); // Reload bookings after cancellation
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = e.toString();
        });
      }
    }
  }

  Color _getStatusColor(BookingStatus status) {
    switch (status) {
      case BookingStatus.pending:
        return Colors.orange;
      case BookingStatus.confirmed:
        return Colors.green;
      case BookingStatus.cancelled:
        return Colors.red;
      case BookingStatus.completed:
        return Colors.blue;
      case BookingStatus.refunded:
        return Colors.purple;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('My Bookings'),
        bottom: _isLoading || _errorMessage != null
            ? null
            : TabBar(
                controller: _tabController,
                tabs: const [
                  Tab(text: 'Upcoming'),
                  Tab(text: 'All Bookings'),
                ],
              ),
      ),
      body: _isLoading
          ? const Center(child: LoadingIndicator())
          : _errorMessage != null
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        _errorMessage!,
                        style: TextStyle(
                          color: Theme.of(context).colorScheme.error,
                        ),
                      ),
                      const SizedBox(height: 16),
                      AppButton(
                        onPressed: _loadBookings,
                        text: 'Retry',
                      ),
                    ],
                  ),
                )
              : TabBarView(
                  controller: _tabController,
                  children: [
                    // Upcoming Bookings Tab
                    _buildBookingsList(_upcomingBookings, isUpcoming: true),

                    // All Bookings Tab
                    _buildBookingsList(_allBookings),
                  ],
                ),
    );
  }

  Widget _buildBookingsList(List<Booking> bookings, {bool isUpcoming = false}) {
    if (bookings.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              isUpcoming ? Icons.event_available : Icons.event_note,
              size: 64,
              color: Colors.grey.shade300,
            ),
            const SizedBox(height: 16),
            Text(
              isUpcoming ? 'No upcoming bookings' : 'No bookings found',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    color: Colors.grey.shade600,
                  ),
            ),
            const SizedBox(height: 8),
            Text(
              isUpcoming
                  ? 'Your confirmed upcoming bookings will appear here'
                  : 'Your booking history will appear here',
              textAlign: TextAlign.center,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Colors.grey.shade500,
                  ),
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: _loadBookings,
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: bookings.length,
        itemBuilder: (context, index) {
          final booking = bookings[index];
          return Card(
            margin: const EdgeInsets.only(bottom: 16),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Expanded(
                        child: Text(
                          'Booking #${booking.id.substring(0, 8)}',
                          style: Theme.of(context).textTheme.titleMedium,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          color: _getStatusColor(booking.status).withAlpha(25),
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: Text(
                          _getStatusText(booking.status),
                          style: TextStyle(
                            color: _getStatusColor(booking.status),
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  _buildInfoRow(
                    'Date',
                    _dateFormat.format(booking.date),
                  ),
                  _buildInfoRow(
                    'Time',
                    booking.timeSlot.formattedTime,
                  ),
                  _buildInfoRow(
                    'Participants',
                    booking.participantCount.toString(),
                  ),
                  _buildInfoRow(
                    'Total Amount',
                    '\$${booking.totalAmount.toStringAsFixed(2)}',
                  ),
                  if (booking.specialRequirements.isNotEmpty)
                    _buildInfoRow(
                      'Special Requirements',
                      booking.specialRequirements,
                    ),
                  const SizedBox(height: 16),
                  _buildBookingActions(booking),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildBookingActions(Booking booking) {
    final actions = <Widget>[];

    // Add to Calendar button (only for confirmed upcoming bookings)
    if (booking.status == BookingStatus.confirmed) {
      final now = DateTime.now();
      final bookingDate = DateTime(
        booking.date.year,
        booking.date.month,
        booking.date.day,
      );

      if (bookingDate.isAfter(now) ||
          bookingDate
              .isAtSameMomentAs(DateTime(now.year, now.month, now.day))) {
        actions.add(
          OutlinedButton.icon(
            onPressed: () => _addToCalendar(booking),
            icon: const Icon(Icons.calendar_today),
            label: const Text('Add to Calendar'),
          ),
        );
      }
    }

    // Cancel button (only for pending or confirmed bookings)
    if (booking.status == BookingStatus.pending ||
        booking.status == BookingStatus.confirmed) {
      if (actions.isNotEmpty) {
        actions.add(const SizedBox(width: 8));
      }

      actions.add(
        Expanded(
          child: AppButton(
            onPressed: () => _cancelBooking(booking.id),
            text: 'Cancel Booking',
            variant: ButtonVariant.outlined,
            color: Theme.of(context).colorScheme.error,
          ),
        ),
      );
    }

    // Request Refund button (only for cancelled bookings that were previously confirmed)
    if (booking.status == BookingStatus.cancelled) {
      actions.add(
        Expanded(
          child: AppButton(
            onPressed: () => _requestRefund(booking),
            text: 'Request Refund',
            variant: ButtonVariant.outlined,
          ),
        ),
      );
    }

    // Write Review button (only for completed bookings)
    if (booking.status == BookingStatus.completed) {
      actions.add(
        Expanded(
          child: AppButton(
            onPressed: () => _writeReview(booking),
            text: 'Write Review',
            variant: ButtonVariant.outlined,
            icon: Icons.rate_review,
          ),
        ),
      );
    }

    return actions.isEmpty ? const SizedBox.shrink() : Row(children: actions);
  }

  Future<void> _writeReview(Booking booking) async {
    try {
      // In a real app, we would fetch the experience details
      // For now, we'll create a mock experience with all required fields
      final now = DateTime.now();
      final experience = Experience(
        id: booking.experienceId,
        title: 'Cultural Experience',
        description: 'A wonderful cultural experience',
        imageUrl: 'https://example.com/image.jpg',
        rating: 4.5,
        reviewCount: 10,
        price: booking.totalAmount / booking.participantCount,
        category: 'Cultural Tours',
        location: 'City Center',
        coordinates: const LatLng(0, 0),
        guideId: 'guide-123',
        guideName: 'Local Guide',
        guideImageUrl: 'https://example.com/guide.jpg',
        languages: ['English'],
        includedItems: ['Tour', 'Refreshments'],
        requirements: ['None'],
        createdAt: now.subtract(const Duration(days: 30)),
        updatedAt: now.subtract(const Duration(days: 1)),
        maxParticipants: 10,
      );

      if (!mounted) return;

      // Navigate to the review screen
      final result = await Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => WriteReviewScreen(
            booking: booking,
            experience: experience,
          ),
        ),
      );

      // If review was submitted successfully, reload bookings
      if (result == true && mounted) {
        await _loadBookings();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  String _getStatusText(BookingStatus status) {
    switch (status) {
      case BookingStatus.pending:
        return 'Pending';
      case BookingStatus.confirmed:
        return 'Confirmed';
      case BookingStatus.cancelled:
        return 'Cancelled';
      case BookingStatus.completed:
        return 'Completed';
      case BookingStatus.refunded:
        return 'Refunded';
    }
  }

  Future<void> _addToCalendar(Booking booking) async {
    try {
      if (!mounted) return;

      final result = await _bookingService.addBookingToCalendar(booking);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              result
                  ? 'Booking added to calendar'
                  : 'Failed to add booking to calendar',
            ),
            backgroundColor: result ? Colors.green : Colors.red,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _requestRefund(Booking booking) async {
    if (!mounted) return;

    // Show refund reason dialog
    final reason = await showDialog<String>(
      context: context,
      builder: (context) => _buildRefundDialog(context),
    );

    if (reason == null || reason.isEmpty || !mounted) {
      return; // User cancelled or widget unmounted
    }

    try {
      final result = await _bookingService.requestRefund(booking.id, reason);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              result
                  ? 'Refund request submitted'
                  : 'Failed to submit refund request',
            ),
            backgroundColor: result ? Colors.green : Colors.red,
          ),
        );

        if (result && mounted) {
          await _loadBookings(); // Reload bookings to update status
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Widget _buildRefundDialog(BuildContext context) {
    final reasonController = TextEditingController();

    return AlertDialog(
      title: const Text('Request Refund'),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text('Please provide a reason for your refund request:'),
          const SizedBox(height: 16),
          TextField(
            controller: reasonController,
            maxLines: 3,
            decoration: const InputDecoration(
              hintText: 'Enter reason here...',
              border: OutlineInputBorder(),
            ),
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text('Cancel'),
        ),
        FilledButton(
          onPressed: () {
            if (reasonController.text.trim().isEmpty) {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Please enter a reason'),
                  backgroundColor: Colors.red,
                ),
              );
              return;
            }
            Navigator.pop(context, reasonController.text);
          },
          child: const Text('Submit'),
        ),
      ],
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Theme.of(context).textTheme.bodySmall?.color,
                  ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
          ),
        ],
      ),
    );
  }
}
