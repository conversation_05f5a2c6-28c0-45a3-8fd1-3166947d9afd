import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:culture_connect/models/translation/custom_vocabulary_model.dart';
import 'package:culture_connect/models/translation/language_model.dart';
import 'package:culture_connect/providers/voice_translation_enhanced_provider.dart';
import 'package:culture_connect/theme/app_theme.dart';
import 'package:culture_connect/widgets/custom_app_bar.dart';
import 'package:culture_connect/widgets/loading_indicator.dart';

/// A screen for adding or editing a custom vocabulary term
class CustomVocabularyFormScreen extends ConsumerStatefulWidget {
  /// The term to edit (null for adding a new term)
  final CustomVocabularyModel? term;
  
  /// Creates a new custom vocabulary form screen
  const CustomVocabularyFormScreen({
    Key? key,
    this.term,
  }) : super(key: key);
  
  @override
  ConsumerState<CustomVocabularyFormScreen> createState() => _CustomVocabularyFormScreenState();
}

class _CustomVocabularyFormScreenState extends ConsumerState<CustomVocabularyFormScreen> {
  final _formKey = GlobalKey<FormState>();
  
  late TextEditingController _originalTermController;
  late TextEditingController _descriptionController;
  late TextEditingController _customCategoryController;
  
  String _originalLanguageCode = 'en';
  VocabularyCategory _category = VocabularyCategory.general;
  final Map<String, String> _translations = {};
  final Map<String, TextEditingController> _translationControllers = {};
  
  bool _isLoading = false;
  bool _isEditing = false;
  
  @override
  void initState() {
    super.initState();
    
    _isEditing = widget.term != null;
    
    // Initialize controllers
    _originalTermController = TextEditingController(text: widget.term?.originalTerm ?? '');
    _descriptionController = TextEditingController(text: widget.term?.description ?? '');
    _customCategoryController = TextEditingController(text: widget.term?.customCategory ?? '');
    
    // Initialize values from term
    if (_isEditing) {
      _originalLanguageCode = widget.term!.originalLanguageCode;
      _category = widget.term!.category;
      _translations.addAll(widget.term!.translations);
      
      // Initialize translation controllers
      for (final entry in _translations.entries) {
        _translationControllers[entry.key] = TextEditingController(text: entry.value);
      }
    }
  }
  
  @override
  void dispose() {
    _originalTermController.dispose();
    _descriptionController.dispose();
    _customCategoryController.dispose();
    
    // Dispose translation controllers
    for (final controller in _translationControllers.values) {
      controller.dispose();
    }
    
    super.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title: _isEditing ? 'Edit Term' : 'Add Term',
        showBackButton: true,
      ),
      body: _isLoading
          ? const Center(child: LoadingIndicator())
          : Form(
              key: _formKey,
              child: ListView(
                padding: EdgeInsets.all(16.r),
                children: [
                  // Original term
                  TextFormField(
                    controller: _originalTermController,
                    decoration: const InputDecoration(
                      labelText: 'Original Term',
                      hintText: 'Enter the term in the original language',
                      border: OutlineInputBorder(),
                    ),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Please enter the original term';
                      }
                      return null;
                    },
                  ),
                  
                  SizedBox(height: 16.h),
                  
                  // Original language
                  DropdownButtonFormField<String>(
                    decoration: const InputDecoration(
                      labelText: 'Original Language',
                      border: OutlineInputBorder(),
                    ),
                    value: _originalLanguageCode,
                    items: supportedLanguages.map((language) {
                      return DropdownMenuItem<String>(
                        value: language.code,
                        child: Row(
                          children: [
                            Text(language.flag),
                            SizedBox(width: 8.w),
                            Text(language.name),
                          ],
                        ),
                      );
                    }).toList(),
                    onChanged: (value) {
                      if (value != null) {
                        setState(() {
                          _originalLanguageCode = value;
                        });
                      }
                    },
                  ),
                  
                  SizedBox(height: 16.h),
                  
                  // Category
                  DropdownButtonFormField<VocabularyCategory>(
                    decoration: const InputDecoration(
                      labelText: 'Category',
                      border: OutlineInputBorder(),
                    ),
                    value: _category,
                    items: VocabularyCategory.values.map((category) {
                      return DropdownMenuItem<VocabularyCategory>(
                        value: category,
                        child: Row(
                          children: [
                            Icon(_getCategoryIcon(category), size: 16.r),
                            SizedBox(width: 8.w),
                            Text(_getCategoryName(category)),
                          ],
                        ),
                      );
                    }).toList(),
                    onChanged: (value) {
                      if (value != null) {
                        setState(() {
                          _category = value;
                        });
                      }
                    },
                  ),
                  
                  // Custom category (if category is custom)
                  if (_category == VocabularyCategory.custom) ...[
                    SizedBox(height: 16.h),
                    TextFormField(
                      controller: _customCategoryController,
                      decoration: const InputDecoration(
                        labelText: 'Custom Category Name',
                        hintText: 'Enter a name for your custom category',
                        border: OutlineInputBorder(),
                      ),
                      validator: (value) {
                        if (_category == VocabularyCategory.custom && (value == null || value.isEmpty)) {
                          return 'Please enter a custom category name';
                        }
                        return null;
                      },
                    ),
                  ],
                  
                  SizedBox(height: 16.h),
                  
                  // Description
                  TextFormField(
                    controller: _descriptionController,
                    decoration: const InputDecoration(
                      labelText: 'Description (Optional)',
                      hintText: 'Enter a description or context for this term',
                      border: OutlineInputBorder(),
                    ),
                    maxLines: 3,
                  ),
                  
                  SizedBox(height: 24.h),
                  
                  // Translations section
                  Text(
                    'Translations',
                    style: TextStyle(
                      fontSize: 18.sp,
                      fontWeight: FontWeight.bold,
                      color: Colors.black87,
                    ),
                  ),
                  
                  SizedBox(height: 8.h),
                  
                  // Existing translations
                  ..._buildTranslationFields(),
                  
                  SizedBox(height: 16.h),
                  
                  // Add translation button
                  ElevatedButton.icon(
                    onPressed: _showAddTranslationDialog,
                    icon: const Icon(Icons.add),
                    label: const Text('Add Translation'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppTheme.primaryColor,
                      foregroundColor: Colors.white,
                      padding: EdgeInsets.symmetric(vertical: 12.h),
                    ),
                  ),
                  
                  SizedBox(height: 32.h),
                  
                  // Save button
                  ElevatedButton(
                    onPressed: _saveVocabularyTerm,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppTheme.primaryColor,
                      foregroundColor: Colors.white,
                      padding: EdgeInsets.symmetric(vertical: 16.h),
                    ),
                    child: Text(
                      _isEditing ? 'Update Term' : 'Save Term',
                      style: TextStyle(
                        fontSize: 16.sp,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
            ),
    );
  }
  
  /// Build the translation fields
  List<Widget> _buildTranslationFields() {
    final widgets = <Widget>[];
    
    for (final entry in _translations.entries) {
      // Create controller if it doesn't exist
      if (!_translationControllers.containsKey(entry.key)) {
        _translationControllers[entry.key] = TextEditingController(text: entry.value);
      }
      
      widgets.add(
        Padding(
          padding: EdgeInsets.only(bottom: 12.h),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Language flag and name
              Container(
                width: 100.w,
                padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 8.h),
                decoration: BoxDecoration(
                  color: Colors.grey[200],
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(8.r),
                    bottomLeft: Radius.circular(8.r),
                  ),
                ),
                child: Row(
                  children: [
                    Text(
                      _getLanguageFlag(entry.key),
                      style: TextStyle(
                        fontSize: 16.sp,
                      ),
                    ),
                    SizedBox(width: 4.w),
                    Expanded(
                      child: Text(
                        _getLanguageName(entry.key),
                        style: TextStyle(
                          fontSize: 12.sp,
                          color: Colors.black87,
                        ),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ),
              ),
              
              // Translation text field
              Expanded(
                child: TextFormField(
                  controller: _translationControllers[entry.key],
                  decoration: InputDecoration(
                    hintText: 'Translation',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.only(
                        topRight: Radius.circular(8.r),
                        bottomRight: Radius.circular(8.r),
                      ),
                    ),
                    contentPadding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.h),
                  ),
                  onChanged: (value) {
                    _translations[entry.key] = value;
                  },
                ),
              ),
              
              // Remove button
              IconButton(
                icon: Icon(
                  Icons.delete,
                  size: 20.r,
                  color: Colors.red,
                ),
                onPressed: () {
                  setState(() {
                    _translations.remove(entry.key);
                    _translationControllers[entry.key]?.dispose();
                    _translationControllers.remove(entry.key);
                  });
                },
              ),
            ],
          ),
        ),
      );
    }
    
    return widgets;
  }
  
  /// Show dialog to add a translation
  Future<void> _showAddTranslationDialog() async {
    String selectedLanguageCode = supportedLanguages.first.code;
    final translationController = TextEditingController();
    
    // Filter out languages that already have translations
    final availableLanguages = supportedLanguages
        .where((language) => 
            language.code != _originalLanguageCode && 
            !_translations.containsKey(language.code))
        .toList();
    
    if (availableLanguages.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('You have added translations for all available languages'),
        ),
      );
      return;
    }
    
    selectedLanguageCode = availableLanguages.first.code;
    
    final result = await showDialog<Map<String, String>>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Add Translation'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Language dropdown
            DropdownButtonFormField<String>(
              decoration: const InputDecoration(
                labelText: 'Language',
                border: OutlineInputBorder(),
              ),
              value: selectedLanguageCode,
              items: availableLanguages.map((language) {
                return DropdownMenuItem<String>(
                  value: language.code,
                  child: Row(
                    children: [
                      Text(language.flag),
                      SizedBox(width: 8.w),
                      Text(language.name),
                    ],
                  ),
                );
              }).toList(),
              onChanged: (value) {
                if (value != null) {
                  selectedLanguageCode = value;
                }
              },
            ),
            
            SizedBox(height: 16.h),
            
            // Translation text field
            TextFormField(
              controller: translationController,
              decoration: const InputDecoration(
                labelText: 'Translation',
                hintText: 'Enter the translation',
                border: OutlineInputBorder(),
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please enter the translation';
                }
                return null;
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              if (translationController.text.isNotEmpty) {
                Navigator.pop(
                  context,
                  {selectedLanguageCode: translationController.text},
                );
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.primaryColor,
              foregroundColor: Colors.white,
            ),
            child: const Text('Add'),
          ),
        ],
      ),
    );
    
    if (result != null) {
      setState(() {
        _translations.addAll(result);
        
        // Create controller for the new translation
        for (final entry in result.entries) {
          _translationControllers[entry.key] = TextEditingController(text: entry.value);
        }
      });
    }
    
    translationController.dispose();
  }
  
  /// Save the vocabulary term
  Future<void> _saveVocabularyTerm() async {
    if (_formKey.currentState!.validate()) {
      setState(() {
        _isLoading = true;
      });
      
      try {
        // Update translations from controllers
        for (final entry in _translationControllers.entries) {
          _translations[entry.key] = entry.value.text;
        }
        
        if (_isEditing) {
          // Update existing term
          await ref.read(customVocabularyNotifierProvider.notifier).updateVocabularyTerm(
            id: widget.term!.id,
            originalTerm: _originalTermController.text,
            originalLanguageCode: _originalLanguageCode,
            translations: _translations,
            category: _category,
            customCategory: _category == VocabularyCategory.custom ? _customCategoryController.text : null,
            description: _descriptionController.text.isNotEmpty ? _descriptionController.text : null,
          );
          
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Term updated successfully'),
                backgroundColor: Colors.green,
              ),
            );
            Navigator.pop(context);
          }
        } else {
          // Add new term
          await ref.read(customVocabularyNotifierProvider.notifier).addVocabularyTerm(
            originalTerm: _originalTermController.text,
            originalLanguageCode: _originalLanguageCode,
            translations: _translations,
            category: _category,
            customCategory: _category == VocabularyCategory.custom ? _customCategoryController.text : null,
            description: _descriptionController.text.isNotEmpty ? _descriptionController.text : null,
          );
          
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Term added successfully'),
                backgroundColor: Colors.green,
              ),
            );
            Navigator.pop(context);
          }
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Error: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      } finally {
        if (mounted) {
          setState(() {
            _isLoading = false;
          });
        }
      }
    }
  }
  
  /// Get the icon for a category
  IconData _getCategoryIcon(VocabularyCategory category) {
    switch (category) {
      case VocabularyCategory.general:
        return Icons.language;
      case VocabularyCategory.medical:
        return Icons.medical_services;
      case VocabularyCategory.technical:
        return Icons.build;
      case VocabularyCategory.business:
        return Icons.business;
      case VocabularyCategory.legal:
        return Icons.gavel;
      case VocabularyCategory.academic:
        return Icons.school;
      case VocabularyCategory.cultural:
        return Icons.public;
      case VocabularyCategory.travel:
        return Icons.flight;
      case VocabularyCategory.food:
        return Icons.restaurant;
      case VocabularyCategory.custom:
        return Icons.category;
    }
  }
  
  /// Get the name for a category
  String _getCategoryName(VocabularyCategory category) {
    switch (category) {
      case VocabularyCategory.general:
        return 'General';
      case VocabularyCategory.medical:
        return 'Medical';
      case VocabularyCategory.technical:
        return 'Technical';
      case VocabularyCategory.business:
        return 'Business';
      case VocabularyCategory.legal:
        return 'Legal';
      case VocabularyCategory.academic:
        return 'Academic';
      case VocabularyCategory.cultural:
        return 'Cultural';
      case VocabularyCategory.travel:
        return 'Travel';
      case VocabularyCategory.food:
        return 'Food';
      case VocabularyCategory.custom:
        return 'Custom';
    }
  }
  
  /// Get the flag for a language code
  String _getLanguageFlag(String languageCode) {
    // This is a simplified version - in a real app, you would use a more comprehensive mapping
    switch (languageCode) {
      case 'en':
        return '🇺🇸';
      case 'fr':
        return '🇫🇷';
      case 'es':
        return '🇪🇸';
      case 'de':
        return '🇩🇪';
      case 'it':
        return '🇮🇹';
      case 'pt':
        return '🇵🇹';
      case 'ru':
        return '🇷🇺';
      case 'zh':
        return '🇨🇳';
      case 'ja':
        return '🇯🇵';
      case 'ko':
        return '🇰🇷';
      case 'ar':
        return '🇸🇦';
      case 'hi':
        return '🇮🇳';
      case 'bn':
        return '🇧🇩';
      case 'sw':
        return '🇰🇪';
      case 'yo':
        return '🇳🇬';
      case 'ha':
        return '🇳🇬';
      case 'ig':
        return '🇳🇬';
      default:
        return '🌐';
    }
  }
  
  /// Get the name for a language code
  String _getLanguageName(String languageCode) {
    // This is a simplified version - in a real app, you would use a more comprehensive mapping
    switch (languageCode) {
      case 'en':
        return 'English';
      case 'fr':
        return 'French';
      case 'es':
        return 'Spanish';
      case 'de':
        return 'German';
      case 'it':
        return 'Italian';
      case 'pt':
        return 'Portuguese';
      case 'ru':
        return 'Russian';
      case 'zh':
        return 'Chinese';
      case 'ja':
        return 'Japanese';
      case 'ko':
        return 'Korean';
      case 'ar':
        return 'Arabic';
      case 'hi':
        return 'Hindi';
      case 'bn':
        return 'Bengali';
      case 'sw':
        return 'Swahili';
      case 'yo':
        return 'Yoruba';
      case 'ha':
        return 'Hausa';
      case 'ig':
        return 'Igbo';
      default:
        return languageCode;
    }
  }
}
