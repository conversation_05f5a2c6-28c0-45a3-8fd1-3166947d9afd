import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';
import 'package:culture_connect/models/translation/custom_vocabulary_model.dart';
import 'package:culture_connect/providers/voice_translation_enhanced_provider.dart';
import 'package:culture_connect/theme/app_theme.dart';
import 'package:culture_connect/widgets/custom_app_bar.dart';
import 'package:culture_connect/widgets/loading_indicator.dart';

/// A screen for viewing the details of a custom vocabulary term
class CustomVocabularyDetailScreen extends ConsumerStatefulWidget {
  /// The term to view
  final CustomVocabularyModel term;
  
  /// Creates a new custom vocabulary detail screen
  const CustomVocabularyDetailScreen({
    Key? key,
    required this.term,
  }) : super(key: key);
  
  @override
  ConsumerState<CustomVocabularyDetailScreen> createState() => _CustomVocabularyDetailScreenState();
}

class _CustomVocabularyDetailScreenState extends ConsumerState<CustomVocabularyDetailScreen> {
  bool _isLoading = false;
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title: 'Term Details',
        showBackButton: true,
        actions: [
          // Edit button
          IconButton(
            icon: const Icon(Icons.edit),
            onPressed: _editTerm,
            tooltip: 'Edit',
          ),
          
          // Favorite button
          IconButton(
            icon: Icon(
              widget.term.isFavorite ? Icons.favorite : Icons.favorite_border,
              color: widget.term.isFavorite ? Colors.red : null,
            ),
            onPressed: _toggleFavorite,
            tooltip: widget.term.isFavorite ? 'Remove from favorites' : 'Add to favorites',
          ),
          
          // Delete button
          IconButton(
            icon: const Icon(Icons.delete),
            onPressed: _confirmDelete,
            tooltip: 'Delete',
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: LoadingIndicator())
          : SingleChildScrollView(
              padding: EdgeInsets.all(16.r),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Term header
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Category badge
                      Container(
                        padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
                        decoration: BoxDecoration(
                          color: AppTheme.primaryColor.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(8.r),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              _getCategoryIcon(widget.term.category),
                              size: 18.r,
                              color: AppTheme.primaryColor,
                            ),
                            SizedBox(width: 6.w),
                            Text(
                              widget.term.getCategoryName(),
                              style: TextStyle(
                                fontSize: 14.sp,
                                fontWeight: FontWeight.bold,
                                color: AppTheme.primaryColor,
                              ),
                            ),
                          ],
                        ),
                      ),
                      
                      SizedBox(width: 12.w),
                      
                      // Language badge
                      Container(
                        padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
                        decoration: BoxDecoration(
                          color: Colors.grey[200],
                          borderRadius: BorderRadius.circular(8.r),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Text(
                              _getLanguageFlag(widget.term.originalLanguageCode),
                              style: TextStyle(
                                fontSize: 16.sp,
                              ),
                            ),
                            SizedBox(width: 6.w),
                            Text(
                              _getLanguageName(widget.term.originalLanguageCode),
                              style: TextStyle(
                                fontSize: 14.sp,
                                color: Colors.black87,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                  
                  SizedBox(height: 24.h),
                  
                  // Original term
                  Text(
                    'Original Term',
                    style: TextStyle(
                      fontSize: 14.sp,
                      fontWeight: FontWeight.bold,
                      color: Colors.grey[600],
                    ),
                  ),
                  
                  SizedBox(height: 4.h),
                  
                  Text(
                    widget.term.originalTerm,
                    style: TextStyle(
                      fontSize: 24.sp,
                      fontWeight: FontWeight.bold,
                      color: Colors.black87,
                    ),
                  ),
                  
                  SizedBox(height: 16.h),
                  
                  // Description
                  if (widget.term.description != null) ...[
                    Text(
                      'Description',
                      style: TextStyle(
                        fontSize: 14.sp,
                        fontWeight: FontWeight.bold,
                        color: Colors.grey[600],
                      ),
                    ),
                    
                    SizedBox(height: 4.h),
                    
                    Container(
                      padding: EdgeInsets.all(12.r),
                      decoration: BoxDecoration(
                        color: Colors.grey[100],
                        borderRadius: BorderRadius.circular(8.r),
                        border: Border.all(
                          color: Colors.grey[300]!,
                          width: 1,
                        ),
                      ),
                      child: Text(
                        widget.term.description!,
                        style: TextStyle(
                          fontSize: 16.sp,
                          color: Colors.black87,
                        ),
                      ),
                    ),
                    
                    SizedBox(height: 24.h),
                  ],
                  
                  // Translations
                  Text(
                    'Translations',
                    style: TextStyle(
                      fontSize: 18.sp,
                      fontWeight: FontWeight.bold,
                      color: Colors.black87,
                    ),
                  ),
                  
                  SizedBox(height: 12.h),
                  
                  if (widget.term.translations.isEmpty)
                    Text(
                      'No translations added yet',
                      style: TextStyle(
                        fontSize: 14.sp,
                        fontStyle: FontStyle.italic,
                        color: Colors.grey[600],
                      ),
                    )
                  else
                    ListView.builder(
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      itemCount: widget.term.translations.length,
                      itemBuilder: (context, index) {
                        final entry = widget.term.translations.entries.elementAt(index);
                        return Card(
                          margin: EdgeInsets.only(bottom: 12.h),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8.r),
                          ),
                          child: Padding(
                            padding: EdgeInsets.all(12.r),
                            child: Row(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                // Language flag and name
                                Container(
                                  width: 100.w,
                                  padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 8.h),
                                  decoration: BoxDecoration(
                                    color: Colors.grey[200],
                                    borderRadius: BorderRadius.circular(8.r),
                                  ),
                                  child: Row(
                                    children: [
                                      Text(
                                        _getLanguageFlag(entry.key),
                                        style: TextStyle(
                                          fontSize: 16.sp,
                                        ),
                                      ),
                                      SizedBox(width: 4.w),
                                      Expanded(
                                        child: Text(
                                          _getLanguageName(entry.key),
                                          style: TextStyle(
                                            fontSize: 12.sp,
                                            color: Colors.black87,
                                          ),
                                          overflow: TextOverflow.ellipsis,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                
                                SizedBox(width: 12.w),
                                
                                // Translation text
                                Expanded(
                                  child: Text(
                                    entry.value,
                                    style: TextStyle(
                                      fontSize: 16.sp,
                                      color: Colors.black87,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        );
                      },
                    ),
                  
                  SizedBox(height: 24.h),
                  
                  // Usage statistics
                  Text(
                    'Usage Statistics',
                    style: TextStyle(
                      fontSize: 18.sp,
                      fontWeight: FontWeight.bold,
                      color: Colors.black87,
                    ),
                  ),
                  
                  SizedBox(height: 12.h),
                  
                  // Usage count
                  _buildStatisticItem(
                    icon: Icons.history,
                    title: 'Usage Count',
                    value: '${widget.term.usageCount} times',
                    color: Colors.amber[700]!,
                  ),
                  
                  SizedBox(height: 8.h),
                  
                  // Created at
                  _buildStatisticItem(
                    icon: Icons.calendar_today,
                    title: 'Created',
                    value: DateFormat('MMM d, yyyy').format(widget.term.createdAt),
                    color: Colors.blue[700]!,
                  ),
                  
                  SizedBox(height: 8.h),
                  
                  // Updated at
                  _buildStatisticItem(
                    icon: Icons.update,
                    title: 'Last Updated',
                    value: DateFormat('MMM d, yyyy').format(widget.term.updatedAt),
                    color: Colors.green[700]!,
                  ),
                  
                  SizedBox(height: 32.h),
                  
                  // Action buttons
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: [
                      // Edit button
                      ElevatedButton.icon(
                        onPressed: _editTerm,
                        icon: const Icon(Icons.edit),
                        label: const Text('Edit'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppTheme.primaryColor,
                          foregroundColor: Colors.white,
                          padding: EdgeInsets.symmetric(horizontal: 24.w, vertical: 12.h),
                        ),
                      ),
                      
                      // Delete button
                      ElevatedButton.icon(
                        onPressed: _confirmDelete,
                        icon: const Icon(Icons.delete),
                        label: const Text('Delete'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.red,
                          foregroundColor: Colors.white,
                          padding: EdgeInsets.symmetric(horizontal: 24.w, vertical: 12.h),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
    );
  }
  
  /// Build a statistic item
  Widget _buildStatisticItem({
    required IconData icon,
    required String title,
    required String value,
    required Color color,
  }) {
    return Container(
      padding: EdgeInsets.all(12.r),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8.r),
      ),
      child: Row(
        children: [
          Icon(
            icon,
            size: 20.r,
            color: color,
          ),
          SizedBox(width: 12.w),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: TextStyle(
                  fontSize: 14.sp,
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                ),
              ),
              SizedBox(height: 2.h),
              Text(
                value,
                style: TextStyle(
                  fontSize: 12.sp,
                  color: Colors.black54,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
  
  /// Edit the term
  void _editTerm() {
    Navigator.pushNamed(
      context,
      '/custom-vocabulary/edit',
      arguments: widget.term,
    );
  }
  
  /// Toggle favorite status
  Future<void> _toggleFavorite() async {
    setState(() {
      _isLoading = true;
    });
    
    try {
      await ref.read(customVocabularyNotifierProvider.notifier).toggleFavorite(widget.term.id);
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              widget.term.isFavorite
                  ? 'Removed from favorites'
                  : 'Added to favorites',
            ),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
  
  /// Confirm deletion
  Future<void> _confirmDelete() async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Term'),
        content: Text(
          'Are you sure you want to delete "${widget.term.originalTerm}"? '
          'This action cannot be undone.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            child: const Text('Delete', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
    
    if (confirmed == true) {
      setState(() {
        _isLoading = true;
      });
      
      try {
        await ref.read(customVocabularyNotifierProvider.notifier).deleteVocabularyTerm(widget.term.id);
        
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Term deleted successfully'),
              backgroundColor: Colors.green,
            ),
          );
          Navigator.pop(context);
        }
      } catch (e) {
        if (mounted) {
          setState(() {
            _isLoading = false;
          });
          
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Error: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }
  
  /// Get the icon for a category
  IconData _getCategoryIcon(VocabularyCategory category) {
    switch (category) {
      case VocabularyCategory.general:
        return Icons.language;
      case VocabularyCategory.medical:
        return Icons.medical_services;
      case VocabularyCategory.technical:
        return Icons.build;
      case VocabularyCategory.business:
        return Icons.business;
      case VocabularyCategory.legal:
        return Icons.gavel;
      case VocabularyCategory.academic:
        return Icons.school;
      case VocabularyCategory.cultural:
        return Icons.public;
      case VocabularyCategory.travel:
        return Icons.flight;
      case VocabularyCategory.food:
        return Icons.restaurant;
      case VocabularyCategory.custom:
        return Icons.category;
    }
  }
  
  /// Get the flag for a language code
  String _getLanguageFlag(String languageCode) {
    // This is a simplified version - in a real app, you would use a more comprehensive mapping
    switch (languageCode) {
      case 'en':
        return '🇺🇸';
      case 'fr':
        return '🇫🇷';
      case 'es':
        return '🇪🇸';
      case 'de':
        return '🇩🇪';
      case 'it':
        return '🇮🇹';
      case 'pt':
        return '🇵🇹';
      case 'ru':
        return '🇷🇺';
      case 'zh':
        return '🇨🇳';
      case 'ja':
        return '🇯🇵';
      case 'ko':
        return '🇰🇷';
      case 'ar':
        return '🇸🇦';
      case 'hi':
        return '🇮🇳';
      case 'bn':
        return '🇧🇩';
      case 'sw':
        return '🇰🇪';
      case 'yo':
        return '🇳🇬';
      case 'ha':
        return '🇳🇬';
      case 'ig':
        return '🇳🇬';
      default:
        return '🌐';
    }
  }
  
  /// Get the name for a language code
  String _getLanguageName(String languageCode) {
    // This is a simplified version - in a real app, you would use a more comprehensive mapping
    switch (languageCode) {
      case 'en':
        return 'English';
      case 'fr':
        return 'French';
      case 'es':
        return 'Spanish';
      case 'de':
        return 'German';
      case 'it':
        return 'Italian';
      case 'pt':
        return 'Portuguese';
      case 'ru':
        return 'Russian';
      case 'zh':
        return 'Chinese';
      case 'ja':
        return 'Japanese';
      case 'ko':
        return 'Korean';
      case 'ar':
        return 'Arabic';
      case 'hi':
        return 'Hindi';
      case 'bn':
        return 'Bengali';
      case 'sw':
        return 'Swahili';
      case 'yo':
        return 'Yoruba';
      case 'ha':
        return 'Hausa';
      case 'ig':
        return 'Igbo';
      default:
        return languageCode;
    }
  }
}
