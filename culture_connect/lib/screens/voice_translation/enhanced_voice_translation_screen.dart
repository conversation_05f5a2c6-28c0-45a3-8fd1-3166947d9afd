/// An enhanced screen for voice translation with additional features like offline mode,
/// dialect recognition, and custom vocabulary
library enhanced_voice_translation;

// Package imports
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

// Project imports - Models are imported via providers

// Project imports - Providers
import 'package:culture_connect/providers/voice_translation_enhanced_provider.dart';
import 'package:culture_connect/providers/voice_translation_provider.dart';

// Project imports - Screens
import 'package:culture_connect/screens/voice_translation/conversation_screen.dart';
import 'package:culture_connect/screens/voice_translation/custom_vocabulary_screen.dart';
import 'package:culture_connect/screens/voice_translation/language_pack_manager_screen.dart';

// Project imports - Theme
import 'package:culture_connect/theme/app_theme.dart';

// Project imports - Widgets
import 'package:culture_connect/widgets/custom_app_bar.dart';
import 'package:culture_connect/widgets/voice_translation/enhanced_language_selector.dart';
import 'package:culture_connect/widgets/voice_translation/voice_recording_button.dart';
import 'package:culture_connect/widgets/voice_translation/translated_content_display.dart';

/// An enhanced screen for voice translation
class EnhancedVoiceTranslationScreen extends ConsumerStatefulWidget {
  /// Creates a new enhanced voice translation screen
  const EnhancedVoiceTranslationScreen({super.key});

  @override
  ConsumerState<EnhancedVoiceTranslationScreen> createState() =>
      _EnhancedVoiceTranslationScreenState();
}

class _EnhancedVoiceTranslationScreenState
    extends ConsumerState<EnhancedVoiceTranslationScreen> {
  /// Build the UI for the enhanced voice translation screen
  @override
  Widget build(BuildContext context) {
    final sourceLanguage = ref.watch(sourceLanguageProvider);
    final targetLanguage = ref.watch(targetLanguageProvider);
    final currentTranslation = ref.watch(currentVoiceTranslationProvider);

    // Enhanced features
    final useOfflineMode = ref.watch(useOfflineModeProvider);
    final useDialectRecognition = ref.watch(useDialectRecognitionProvider);
    final useCustomVocabulary = ref.watch(useCustomVocabularyProvider);

    return Scaffold(
      appBar: CustomAppBar(
        title: 'Voice Translation',
        showBackButton: true,
        actions: [
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: () => _showSettingsBottomSheet(context),
            tooltip: 'Settings',
          ),
        ],
      ),
      body: Column(
        children: [
          // Language selectors
          Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                // Source language
                Expanded(
                  child: LanguageSelector(
                    selectedLanguage: sourceLanguage,
                    onSelected: (language) {
                      ref.read(sourceLanguageProvider.notifier).state =
                          language;
                    },
                    labelText: 'Source',
                  ),
                ),

                // Swap button
                IconButton(
                  icon: const Icon(
                    Icons.swap_horiz,
                    color: AppTheme.primaryColor,
                    size: 24,
                  ),
                  onPressed: () {
                    final source = ref.read(sourceLanguageProvider);
                    final target = ref.read(targetLanguageProvider);

                    ref.read(sourceLanguageProvider.notifier).state = target;
                    ref.read(targetLanguageProvider.notifier).state = source;
                  },
                  tooltip: 'Swap languages',
                ),

                // Target language
                Expanded(
                  child: LanguageSelector(
                    selectedLanguage: targetLanguage,
                    onSelected: (language) {
                      ref.read(targetLanguageProvider.notifier).state =
                          language;
                    },
                    labelText: 'Target',
                  ),
                ),
              ],
            ),
          ),

          // Feature indicators
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // Offline mode
                _buildFeatureIndicator(
                  icon: Icons.offline_bolt,
                  label: 'Offline',
                  isActive: useOfflineMode,
                  onTap: () => _navigateToLanguagePackManager(context),
                ),

                const SizedBox(width: 16),

                // Dialect recognition
                _buildFeatureIndicator(
                  icon: Icons.record_voice_over,
                  label: 'Dialect',
                  isActive: useDialectRecognition,
                  onTap: () => _toggleDialectRecognition(),
                ),

                const SizedBox(width: 16),

                // Custom vocabulary
                _buildFeatureIndicator(
                  icon: Icons.menu_book,
                  label: 'Vocabulary',
                  isActive: useCustomVocabulary,
                  onTap: () => _navigateToCustomVocabulary(context),
                ),
              ],
            ),
          ),

          const SizedBox(height: 16),

          // Conversation mode button
          ElevatedButton.icon(
            onPressed: () => _navigateToConversationMode(context),
            icon: const Icon(Icons.forum),
            label: const Text('Conversation Mode'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.primaryColor,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            ),
          ),

          const SizedBox(height: 16),

          // Translation result
          Expanded(
            child: currentTranslation != null
                ? const TranslatedContentDisplay()
                : Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.record_voice_over,
                          size: 64,
                          color: Colors.grey[400],
                        ),
                        const SizedBox(height: 16),
                        const Text(
                          'Tap the microphone button to start',
                          style: TextStyle(
                            fontSize: 16,
                            color: AppTheme.textSecondaryColor,
                          ),
                        ),
                      ],
                    ),
                  ),
          ),

          // Recording button
          const Padding(
            padding: EdgeInsets.all(16),
            child: Center(
              child: VoiceRecordingButton(
                size: 64,
                color: AppTheme.primaryColor,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Build a feature indicator button
  ///
  /// [icon] The icon to display
  /// [label] The label text
  /// [isActive] Whether the feature is active
  /// [onTap] Callback when the indicator is tapped
  /// Returns a widget displaying the feature indicator
  Widget _buildFeatureIndicator({
    required IconData icon,
    required String label,
    required bool isActive,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color: isActive
              ? AppTheme.primaryColor.withAlpha(25) // 0.1 opacity
              : Colors.grey[200],
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: isActive ? AppTheme.primaryColor : Colors.grey[300]!,
            width: 1,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              size: 16,
              color: isActive ? AppTheme.primaryColor : Colors.grey,
            ),
            const SizedBox(width: 4),
            Text(
              label,
              style: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.bold,
                color: isActive ? AppTheme.primaryColor : Colors.grey,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Show the settings bottom sheet
  ///
  /// [context] The build context
  /// Displays a bottom sheet with translation settings
  Future<void> _showSettingsBottomSheet(BuildContext context) async {
    final useOfflineMode = ref.read(useOfflineModeProvider);
    final useDialectRecognition = ref.read(useDialectRecognitionProvider);
    final useCustomVocabulary = ref.read(useCustomVocabularyProvider);

    await showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(16),
        ),
      ),
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              const Row(
                children: [
                  Icon(
                    Icons.settings,
                    size: 24,
                    color: AppTheme.primaryColor,
                  ),
                  SizedBox(width: 8),
                  Text(
                    'Translation Settings',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: AppTheme.textPrimaryColor,
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 16),

              // Offline mode
              SwitchListTile(
                title: const Row(
                  children: [
                    Icon(
                      Icons.offline_bolt,
                      size: 20,
                      color: AppTheme.primaryColor,
                    ),
                    SizedBox(width: 8),
                    Text('Offline Mode'),
                  ],
                ),
                subtitle:
                    const Text('Use downloaded language packs when available'),
                value: useOfflineMode,
                onChanged: (value) {
                  setState(() {
                    // Update the state directly without service call
                    ref.read(useOfflineModeProvider.notifier).state = value;
                  });
                },
                activeColor: AppTheme.primaryColor,
              ),

              // Dialect recognition
              SwitchListTile(
                title: const Row(
                  children: [
                    Icon(
                      Icons.record_voice_over,
                      size: 20,
                      color: AppTheme.primaryColor,
                    ),
                    SizedBox(width: 8),
                    Text('Dialect Recognition'),
                  ],
                ),
                subtitle: const Text('Recognize and use specific dialects'),
                value: useDialectRecognition,
                onChanged: (value) {
                  setState(() {
                    // Update the state directly without service call
                    ref.read(useDialectRecognitionProvider.notifier).state =
                        value;
                  });
                },
                activeColor: AppTheme.primaryColor,
              ),

              // Custom vocabulary
              SwitchListTile(
                title: const Row(
                  children: [
                    Icon(
                      Icons.menu_book,
                      size: 20,
                      color: AppTheme.primaryColor,
                    ),
                    SizedBox(width: 8),
                    Text('Custom Vocabulary'),
                  ],
                ),
                subtitle: const Text('Use your custom vocabulary terms'),
                value: useCustomVocabulary,
                onChanged: (value) {
                  setState(() {
                    // Update the state directly without service call
                    ref.read(useCustomVocabularyProvider.notifier).state =
                        value;
                  });
                },
                activeColor: AppTheme.primaryColor,
              ),

              const SizedBox(height: 16),

              // Buttons
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  // Language packs
                  ElevatedButton.icon(
                    onPressed: () {
                      Navigator.pop(context);
                      if (context.mounted) {
                        _navigateToLanguagePackManager(context);
                      }
                    },
                    icon: const Icon(Icons.language),
                    label: const Text('Language Packs'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppTheme.primaryColor,
                      foregroundColor: Colors.white,
                    ),
                  ),

                  // Custom vocabulary
                  ElevatedButton.icon(
                    onPressed: () {
                      Navigator.pop(context);
                      if (context.mounted) {
                        _navigateToCustomVocabulary(context);
                      }
                    },
                    icon: const Icon(Icons.menu_book),
                    label: const Text('Vocabulary'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppTheme.primaryColor,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Toggle dialect recognition on/off
  void _toggleDialectRecognition() {
    final current = ref.read(useDialectRecognitionProvider);
    // Update the state directly without service call
    ref.read(useDialectRecognitionProvider.notifier).state = !current;
  }

  /// Navigate to the language pack manager screen
  ///
  /// [context] The build context
  void _navigateToLanguagePackManager(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const LanguagePackManagerScreen(),
      ),
    );
  }

  /// Navigate to the custom vocabulary screen
  ///
  /// [context] The build context
  void _navigateToCustomVocabulary(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const CustomVocabularyScreen(),
      ),
    );
  }

  /// Navigate to the conversation mode screen
  ///
  /// [context] The build context
  void _navigateToConversationMode(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const ConversationScreen(),
      ),
    );
  }
}
