import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:culture_connect/models/translation/voice_translation_model.dart';
import 'package:culture_connect/providers/voice_translation_enhanced_provider.dart';
import 'package:culture_connect/providers/voice_translation_provider.dart';
import 'package:culture_connect/screens/voice_translation/conversation_screen.dart';
import 'package:culture_connect/screens/voice_translation/custom_vocabulary_screen.dart';
import 'package:culture_connect/screens/voice_translation/enhanced_voice_translation_screen.dart';
import 'package:culture_connect/screens/voice_translation/language_pack_manager_screen.dart';
import 'package:culture_connect/screens/voice_translation/translation_history_screen.dart';
import 'package:culture_connect/theme/app_theme.dart';
import 'package:culture_connect/widgets/custom_app_bar.dart';
import 'package:culture_connect/widgets/voice_translation/language_selector.dart';
import 'package:culture_connect/widgets/voice_translation/voice_recording_button.dart';
import 'package:culture_connect/widgets/voice_translation/translated_content_display.dart';

/// A screen for voice translation
class VoiceTranslationScreen extends ConsumerStatefulWidget {
  /// Creates a new voice translation screen
  const VoiceTranslationScreen({super.key});

  @override
  ConsumerState<VoiceTranslationScreen> createState() =>
      _VoiceTranslationScreenState();
}

class _VoiceTranslationScreenState extends ConsumerState<VoiceTranslationScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);

    // Start a new translation
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(voiceTranslationNotifierProvider.notifier).startTranslation();
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final translation = ref.watch(currentVoiceTranslationProvider);
    final isRecording = ref.watch(isRecordingProvider);

    return Scaffold(
      appBar: CustomAppBar(
        title: 'Voice Translation',
        showBackButton: true,
        actions: [
          IconButton(
            icon: const Icon(Icons.history),
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const TranslationHistoryScreen(),
                ),
              );
            },
            tooltip: 'Translation History',
          ),
          IconButton(
            icon: const Icon(Icons.upgrade),
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const EnhancedVoiceTranslationScreen(),
                ),
              );
            },
            tooltip: 'Enhanced Translation',
          ),
          IconButton(
            icon: const Icon(Icons.menu_book),
            onPressed: () {
              Navigator.pushNamed(context, '/custom-vocabulary');
            },
            tooltip: 'Custom Vocabulary',
          ),
          IconButton(
            icon: const Icon(Icons.image_search),
            onPressed: () {
              Navigator.pushNamed(context, '/image-text-translation');
            },
            tooltip: 'Image Text Translation',
          ),
          IconButton(
            icon: const Icon(Icons.psychology),
            onPressed: () {
              Navigator.pushNamed(context, '/cultural-context-settings');
            },
            tooltip: 'Cultural Context Settings',
          ),
          IconButton(
            icon: const Icon(Icons.text_rotation_none),
            onPressed: () {
              Navigator.pushNamed(context, '/rtl-settings');
            },
            tooltip: 'RTL Settings',
          ),
        ],
      ),
      body: Column(
        children: [
          // Language selector
          Padding(
            padding: EdgeInsets.all(16.r),
            child: const LanguageSelector(),
          ),

          // Tab bar
          TabBar(
            controller: _tabController,
            tabs: const [
              Tab(text: 'Translate'),
              Tab(text: 'Conversation'),
            ],
            labelColor: AppTheme.primaryColor,
            unselectedLabelColor: AppTheme.textSecondaryColor,
            indicatorColor: AppTheme.primaryColor,
          ),

          // Tab content
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                // Translate tab
                _buildTranslateTab(),

                // Conversation tab
                _buildConversationTab(),
              ],
            ),
          ),
        ],
      ),
      floatingActionButton:
          translation?.status == VoiceTranslationStatus.completed
              ? FloatingActionButton(
                  onPressed: () {
                    ref
                        .read(voiceTranslationNotifierProvider.notifier)
                        .startTranslation();
                  },
                  backgroundColor: AppTheme.primaryColor,
                  child: const Icon(Icons.add),
                )
              : null,
      floatingActionButtonLocation: FloatingActionButtonLocation.endFloat,
    );
  }

  Widget _buildTranslateTab() {
    return Stack(
      children: [
        // Content
        const Positioned.fill(
          child: TranslatedContentDisplay(),
        ),

        // Recording button
        Positioned(
          bottom: 24.h,
          left: 0,
          right: 0,
          child: Center(
            child: VoiceRecordingButton(
              size: 80.r,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildConversationTab() {
    return Column(
      children: [
        Expanded(
          child: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.forum,
                  size: 64.r,
                  color: Colors.grey[400],
                ),
                SizedBox(height: 16.h),
                Text(
                  'Conversation Mode',
                  style: TextStyle(
                    fontSize: 20.sp,
                    fontWeight: FontWeight.bold,
                    color: AppTheme.textPrimaryColor,
                  ),
                ),
                SizedBox(height: 8.h),
                Text(
                  'Now Available!',
                  style: TextStyle(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.bold,
                    color: Colors.green,
                  ),
                  textAlign: TextAlign.center,
                ),
                SizedBox(height: 16.h),
                Text(
                  'Have a real-time conversation with someone speaking a different language.',
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: AppTheme.textSecondaryColor,
                  ),
                  textAlign: TextAlign.center,
                ),
                SizedBox(height: 24.h),
                Container(
                  padding: EdgeInsets.all(16.r),
                  decoration: BoxDecoration(
                    color: AppTheme.primaryColor.withAlpha(25),
                    borderRadius: BorderRadius.circular(8.r),
                  ),
                  child: Column(
                    children: [
                      Text(
                        'Available Features:',
                        style: TextStyle(
                          fontSize: 16.sp,
                          fontWeight: FontWeight.bold,
                          color: AppTheme.primaryColor,
                        ),
                      ),
                      SizedBox(height: 8.h),
                      _buildFeatureItem('Real-time translation'),
                      _buildFeatureItem('Automatic language detection'),
                      _buildFeatureItem('Speaker identification'),
                      _buildFeatureItem('Offline conversation mode'),
                      _buildFeatureItem('Dialect recognition'),
                      _buildFeatureItem('Custom vocabulary support'),
                      _buildFeatureItem('Image text translation'),
                      _buildFeatureItem('Cultural context adaptation'),
                      _buildFeatureItem('RTL language support'),
                      SizedBox(height: 16.h),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          OutlinedButton.icon(
                            onPressed: () {
                              Navigator.pushNamed(
                                  context, '/custom-vocabulary');
                            },
                            icon: const Icon(Icons.menu_book),
                            label: const Text('Custom Vocabulary'),
                            style: OutlinedButton.styleFrom(
                              foregroundColor: AppTheme.primaryColor,
                              side: const BorderSide(
                                  color: AppTheme.primaryColor),
                            ),
                          ),
                          SizedBox(width: 8.w),
                          OutlinedButton.icon(
                            onPressed: () {
                              Navigator.pushNamed(
                                  context, '/image-text-translation');
                            },
                            icon: const Icon(Icons.image_search),
                            label: const Text('Image Translation'),
                            style: OutlinedButton.styleFrom(
                              foregroundColor: AppTheme.primaryColor,
                              side: const BorderSide(
                                  color: AppTheme.primaryColor),
                            ),
                          ),
                          SizedBox(width: 8.w),
                          OutlinedButton.icon(
                            onPressed: () {
                              Navigator.pushNamed(
                                  context, '/cultural-context-settings');
                            },
                            icon: const Icon(Icons.psychology),
                            label: const Text('Cultural Context'),
                            style: OutlinedButton.styleFrom(
                              foregroundColor: AppTheme.primaryColor,
                              side: const BorderSide(
                                  color: AppTheme.primaryColor),
                            ),
                          ),
                          SizedBox(width: 8.w),
                          OutlinedButton.icon(
                            onPressed: () {
                              Navigator.pushNamed(context, '/rtl-settings');
                            },
                            icon: const Icon(Icons.text_rotation_none),
                            label: const Text('RTL Settings'),
                            style: OutlinedButton.styleFrom(
                              foregroundColor: AppTheme.primaryColor,
                              side: const BorderSide(
                                  color: AppTheme.primaryColor),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                SizedBox(height: 24.h),
                ElevatedButton.icon(
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const ConversationScreen(),
                      ),
                    );
                  },
                  icon: const Icon(Icons.forum),
                  label: const Text('Start Conversation'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppTheme.primaryColor,
                    foregroundColor: Colors.white,
                    padding:
                        EdgeInsets.symmetric(horizontal: 24.w, vertical: 12.h),
                  ),
                ),
                SizedBox(height: 16.h),
                TextButton.icon(
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) =>
                            const EnhancedVoiceTranslationScreen(),
                      ),
                    );
                  },
                  icon: const Icon(Icons.upgrade),
                  label: const Text('Try Enhanced Translation'),
                  style: TextButton.styleFrom(
                    foregroundColor: AppTheme.primaryColor,
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildFeatureItem(String text) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 4.h),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.check_circle,
            size: 16.r,
            color: AppTheme.primaryColor,
          ),
          SizedBox(width: 8.w),
          Text(
            text,
            style: TextStyle(
              fontSize: 14.sp,
              color: AppTheme.textPrimaryColor,
            ),
          ),
        ],
      ),
    );
  }
}
