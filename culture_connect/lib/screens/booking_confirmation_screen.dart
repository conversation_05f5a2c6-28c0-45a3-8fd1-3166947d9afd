import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:share_plus/share_plus.dart';
import 'package:culture_connect/models/booking.dart';
import 'package:culture_connect/services/booking_service.dart';
import 'package:culture_connect/widgets/common/app_button.dart';
import 'package:culture_connect/widgets/common/loading_indicator.dart';

/// Screen shown after successful payment to confirm booking details
class BookingConfirmationScreen extends ConsumerStatefulWidget {
  final Booking booking;
  final String transactionId;

  const BookingConfirmationScreen({
    super.key,
    required this.booking,
    required this.transactionId,
  });

  @override
  ConsumerState<BookingConfirmationScreen> createState() =>
      _BookingConfirmationScreenState();
}

class _BookingConfirmationScreenState
    extends ConsumerState<BookingConfirmationScreen> {
  final BookingService _bookingService = BookingService();
  bool _isLoading = false;
  bool _isAddingToCalendar = false;
  String? _errorMessage;
  late Booking _confirmedBooking;

  @override
  void initState() {
    super.initState();
    _confirmedBooking = widget.booking.copyWith(
      status: BookingStatus.confirmed,
    );
    _updateBookingStatus();
  }

  Future<void> _updateBookingStatus() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      // Update booking status in backend
      await _bookingService.updateBookingStatus(
        widget.booking.id,
        BookingStatus.confirmed,
        widget.transactionId,
      );

      // Set up booking reminders
      await _setupBookingReminders();
    } catch (e) {
      setState(() {
        _errorMessage = 'Failed to update booking status: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _setupBookingReminders() async {
    try {
      // Set up booking reminders
      await _bookingService.setupBookingReminders(_confirmedBooking);
    } catch (e) {
      // Just log the error, don't show to user as it's not critical
      debugPrint('Failed to set up booking reminders: $e');
    }
  }

  Future<void> _addToCalendar() async {
    setState(() {
      _isAddingToCalendar = true;
    });

    try {
      await _bookingService.addBookingToCalendar(_confirmedBooking);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Booking added to calendar'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to add to calendar: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() {
        _isAddingToCalendar = false;
      });
    }
  }

  Future<void> _shareBookingDetails() async {
    final DateFormat dateFormat = DateFormat('EEEE, MMMM d, yyyy');
    final String formattedDate = dateFormat.format(_confirmedBooking.date);

    final String shareText = '''
🎉 I just booked an experience on CultureConnect!

📅 Date: $formattedDate
⏰ Time: ${_confirmedBooking.timeSlot.formattedTime}
👥 Participants: ${_confirmedBooking.participantCount}

Booking Reference: ${_confirmedBooking.id}

Download CultureConnect to discover authentic cultural experiences!
''';

    await Share.share(shareText);
  }

  void _viewAllBookings() {
    Navigator.pushReplacementNamed(context, '/booking-management');
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return PopScope(
      canPop: false,
      onPopInvoked: (didPop) {
        if (!didPop) {
          // Navigate to home instead of going back to payment screen
          Navigator.pushReplacementNamed(context, '/home');
        }
      },
      child: Scaffold(
        appBar: AppBar(
          title: const Text('Booking Confirmed'),
          automaticallyImplyLeading: false,
        ),
        body: _isLoading
            ? const Center(child: LoadingIndicator())
            : _errorMessage != null
                ? Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.error_outline,
                          size: 64,
                          color: theme.colorScheme.error,
                        ),
                        const SizedBox(height: 16),
                        Text(
                          'Error',
                          style: theme.textTheme.headlineMedium,
                        ),
                        const SizedBox(height: 8),
                        Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 32),
                          child: Text(
                            _errorMessage!,
                            textAlign: TextAlign.center,
                            style: TextStyle(
                              color: theme.colorScheme.error,
                            ),
                          ),
                        ),
                        const SizedBox(height: 24),
                        AppButton(
                          onPressed: _updateBookingStatus,
                          text: 'Retry',
                        ),
                      ],
                    ),
                  )
                : SingleChildScrollView(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        // Success Icon
                        Container(
                          width: 100,
                          height: 100,
                          decoration: BoxDecoration(
                            color: theme.colorScheme.primary.withAlpha(25),
                            shape: BoxShape.circle,
                          ),
                          child: Icon(
                            Icons.check_circle,
                            size: 64,
                            color: theme.colorScheme.primary,
                          ),
                        ),
                        const SizedBox(height: 24),

                        // Success Message
                        Text(
                          'Booking Confirmed!',
                          style: theme.textTheme.headlineMedium?.copyWith(
                            color: theme.colorScheme.primary,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          'Your booking has been confirmed and payment processed successfully.',
                          textAlign: TextAlign.center,
                          style: theme.textTheme.bodyLarge,
                        ),
                        const SizedBox(height: 32),

                        // Booking Details Card
                        Card(
                          child: Padding(
                            padding: const EdgeInsets.all(16),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'Booking Details',
                                  style: theme.textTheme.titleLarge,
                                ),
                                const SizedBox(height: 16),
                                _buildDetailRow(
                                  'Booking ID',
                                  _confirmedBooking.id,
                                ),
                                _buildDetailRow(
                                  'Status',
                                  'Confirmed',
                                  valueColor: Colors.green,
                                ),
                                _buildDetailRow(
                                  'Date',
                                  DateFormat('EEEE, MMMM d, yyyy')
                                      .format(_confirmedBooking.date),
                                ),
                                _buildDetailRow(
                                  'Time',
                                  _confirmedBooking.timeSlot.formattedTime,
                                ),
                                _buildDetailRow(
                                  'Participants',
                                  _confirmedBooking.participantCount.toString(),
                                ),
                                _buildDetailRow(
                                  'Total Amount',
                                  '\$${_confirmedBooking.totalAmount.toStringAsFixed(2)}',
                                  valueColor: theme.colorScheme.primary,
                                ),
                                _buildDetailRow(
                                  'Payment ID',
                                  widget.transactionId,
                                ),
                                if (_confirmedBooking
                                    .specialRequirements.isNotEmpty)
                                  _buildDetailRow(
                                    'Special Requirements',
                                    _confirmedBooking.specialRequirements,
                                    isMultiLine: true,
                                  ),
                              ],
                            ),
                          ),
                        ),
                        const SizedBox(height: 32),

                        // Action Buttons
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                          children: [
                            _buildActionButton(
                              icon: Icons.calendar_today,
                              label: 'Add to Calendar',
                              onTap: _addToCalendar,
                              isLoading: _isAddingToCalendar,
                            ),
                            _buildActionButton(
                              icon: Icons.share,
                              label: 'Share',
                              onTap: _shareBookingDetails,
                            ),
                          ],
                        ),
                        const SizedBox(height: 32),

                        // View All Bookings Button
                        AppButton(
                          onPressed: _viewAllBookings,
                          text: 'View All Bookings',
                          variant: ButtonVariant.outlined,
                        ),
                        const SizedBox(height: 16),

                        // Back to Home Button
                        AppButton(
                          onPressed: () {
                            Navigator.pushReplacementNamed(context, '/home');
                          },
                          text: 'Back to Home',
                        ),
                      ],
                    ),
                  ),
      ),
    );
  }

  Widget _buildDetailRow(
    String label,
    String value, {
    Color? valueColor,
    bool isMultiLine = false,
  }) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: isMultiLine
          ? Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: Theme.of(context).textTheme.bodyMedium,
                ),
                const SizedBox(height: 4),
                Text(
                  value,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: valueColor,
                      ),
                ),
              ],
            )
          : Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  label,
                  style: Theme.of(context).textTheme.bodyMedium,
                ),
                Text(
                  value,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: valueColor,
                      ),
                ),
              ],
            ),
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
    bool isLoading = false,
  }) {
    return InkWell(
      onTap: isLoading ? null : onTap,
      borderRadius: BorderRadius.circular(8),
      child: Container(
        padding: const EdgeInsets.symmetric(
          horizontal: 16,
          vertical: 12,
        ),
        child: Column(
          children: [
            isLoading
                ? const SizedBox(
                    width: 24,
                    height: 24,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                    ),
                  )
                : Icon(
                    icon,
                    size: 24,
                    color: Theme.of(context).colorScheme.primary,
                  ),
            const SizedBox(height: 8),
            Text(
              label,
              style: TextStyle(
                color: Theme.of(context).colorScheme.primary,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
