import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:image_picker/image_picker.dart';
import 'package:culture_connect/models/group_chat_model.dart';
import 'package:culture_connect/models/user_model.dart';
import 'package:culture_connect/providers/auth_provider.dart';
import 'package:culture_connect/providers/group_chat_provider.dart';
import 'package:culture_connect/theme/app_theme.dart';
import 'package:culture_connect/widgets/custom_app_bar.dart';

/// A screen for displaying and managing group information
class GroupInfoScreen extends ConsumerStatefulWidget {
  /// The ID of the group to display
  final String groupId;

  /// Creates a new group info screen
  const GroupInfoScreen({
    super.key,
    required this.groupId,
  });

  @override
  ConsumerState<GroupInfoScreen> createState() => _GroupInfoScreenState();
}

class _GroupInfoScreenState extends ConsumerState<GroupInfoScreen> {
  final TextEditingController _nameController = TextEditingController();
  final TextEditingController _descriptionController = TextEditingController();
  bool _isEditing = false;
  bool _isLoading = false;
  File? _newGroupImage;

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  void _toggleEditing() {
    setState(() {
      _isEditing = !_isEditing;
      if (!_isEditing) {
        _newGroupImage = null;
      }
    });
  }

  Future<void> _pickImage() async {
    final picker = ImagePicker();
    final pickedFile = await picker.pickImage(source: ImageSource.gallery);

    if (pickedFile != null && mounted) {
      setState(() {
        _newGroupImage = File(pickedFile.path);
      });
    }
  }

  Future<void> _saveChanges(GroupChatModel group) async {
    final name = _nameController.text.trim();
    final description = _descriptionController.text.trim();

    if (name.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Group name cannot be empty')),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      await ref.read(groupChatProvider.notifier).updateGroupChat(
            groupId: widget.groupId,
            name: name,
            description: description,
            image: _newGroupImage,
          );

      setState(() {
        _isLoading = false;
        _isEditing = false;
        _newGroupImage = null;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Group updated successfully')),
        );
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error updating group: $e')),
        );
      }
    }
  }

  Future<void> _leaveGroup() async {
    final confirm = await showDialog<bool>(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('Leave Group'),
          content: const Text('Are you sure you want to leave this group?'),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context, false),
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () => Navigator.pop(context, true),
              child: const Text('Leave'),
            ),
          ],
        );
      },
    );

    if (confirm != true) return;

    setState(() {
      _isLoading = true;
    });

    try {
      await ref.read(groupChatProvider.notifier).leaveGroup(widget.groupId);

      if (mounted) {
        Navigator.popUntil(context, (route) => route.isFirst);
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error leaving group: $e')),
        );
      }
    }
  }

  Future<void> _deleteGroup() async {
    final confirm = await showDialog<bool>(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('Delete Group'),
          content: const Text(
              'Are you sure you want to delete this group? This action cannot be undone.'),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context, false),
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () => Navigator.pop(context, true),
              style: TextButton.styleFrom(
                foregroundColor: Colors.red,
              ),
              child: const Text('Delete'),
            ),
          ],
        );
      },
    );

    if (confirm != true) return;

    setState(() {
      _isLoading = true;
    });

    try {
      await ref.read(groupChatProvider.notifier).deleteGroup(widget.groupId);

      if (mounted) {
        Navigator.popUntil(context, (route) => route.isFirst);
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error deleting group: $e')),
        );
      }
    }
  }

  Future<void> _showAddMemberDialog() async {
    // TODO: Implement add member dialog
  }

  Future<void> _showRemoveMemberDialog(
      GroupChatModel group, UserModel member) async {
    final confirm = await showDialog<bool>(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('Remove Member'),
          content: Text(
              'Are you sure you want to remove ${member.firstName} from the group?'),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context, false),
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () => Navigator.pop(context, true),
              style: TextButton.styleFrom(
                foregroundColor: Colors.red,
              ),
              child: const Text('Remove'),
            ),
          ],
        );
      },
    );

    if (confirm != true) return;

    setState(() {
      _isLoading = true;
    });

    try {
      await ref.read(groupChatProvider.notifier).removeMemberFromGroup(
            widget.groupId,
            member.id,
          );

      setState(() {
        _isLoading = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('${member.firstName} removed from the group')),
        );
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error removing member: $e')),
        );
      }
    }
  }

  Future<void> _showChangeRoleDialog(
      GroupChatModel group, UserModel member) async {
    if (!mounted) return;

    final currentRole = group.getUserRole(member.id) ?? GroupMemberRole.member;
    GroupMemberRole? selectedRole = currentRole;

    final newRole = await showDialog<GroupMemberRole>(
      context: context,
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              title: Text('Change ${member.firstName}\'s Role'),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  RadioListTile<GroupMemberRole>(
                    title: const Text('Admin'),
                    subtitle: const Text('Can manage group and members'),
                    value: GroupMemberRole.admin,
                    groupValue: selectedRole,
                    onChanged: (value) {
                      setState(() {
                        selectedRole = value;
                      });
                    },
                  ),
                  RadioListTile<GroupMemberRole>(
                    title: const Text('Moderator'),
                    subtitle:
                        const Text('Can manage messages and some settings'),
                    value: GroupMemberRole.moderator,
                    groupValue: selectedRole,
                    onChanged: (value) {
                      setState(() {
                        selectedRole = value;
                      });
                    },
                  ),
                  RadioListTile<GroupMemberRole>(
                    title: const Text('Member'),
                    subtitle: const Text('Regular group member'),
                    value: GroupMemberRole.member,
                    groupValue: selectedRole,
                    onChanged: (value) {
                      setState(() {
                        selectedRole = value;
                      });
                    },
                  ),
                ],
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.pop(context),
                  child: const Text('Cancel'),
                ),
                TextButton(
                  onPressed: () => Navigator.pop(context, selectedRole),
                  child: const Text('Save'),
                ),
              ],
            );
          },
        );
      },
    );

    if (newRole == null || newRole == currentRole || !mounted) return;

    setState(() {
      _isLoading = true;
    });

    try {
      await ref.read(groupChatProvider.notifier).updateMemberRole(
            widget.groupId,
            member.id,
            newRole,
          );

      if (!mounted) return;

      setState(() {
        _isLoading = false;
      });

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
            content: Text(
                '${member.firstName}\'s role updated to ${newRole.displayName}')),
      );
    } catch (e) {
      if (!mounted) return;

      setState(() {
        _isLoading = false;
      });

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error updating role: $e')),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final groupDetailsAsync =
        ref.watch(groupChatDetailsProvider(widget.groupId));
    final membersAsync = ref.watch(groupChatMembersProvider(widget.groupId));
    final currentUserAsync = ref.watch(currentUserModelProvider);

    return Scaffold(
      appBar: CustomAppBar(
        title: 'Group Info',
        showBackButton: true,
        actions: [
          if (!_isEditing)
            IconButton(
              icon: const Icon(Icons.edit),
              onPressed: _toggleEditing,
            )
          else
            IconButton(
              icon: const Icon(Icons.close),
              onPressed: _toggleEditing,
            ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : groupDetailsAsync.when(
              data: (group) {
                if (group == null) {
                  return const Center(child: Text('Group not found'));
                }

                // Initialize controllers if editing
                if (_isEditing && _nameController.text.isEmpty) {
                  _nameController.text = group.name;
                  _descriptionController.text = group.description;
                }

                return currentUserAsync.when(
                  data: (currentUser) {
                    if (currentUser == null) {
                      return const Center(child: Text('User not found'));
                    }

                    final isAdmin = group.isUserAdmin(currentUser.id);

                    return SingleChildScrollView(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          _buildGroupHeader(group, isAdmin),
                          const SizedBox(height: 24),
                          _buildMembersList(group, currentUser, membersAsync),
                          const SizedBox(height: 24),
                          _buildGroupActions(group, isAdmin),
                        ],
                      ),
                    );
                  },
                  loading: () =>
                      const Center(child: CircularProgressIndicator()),
                  error: (error, stack) => Center(
                    child: Text('Error loading user: $error'),
                  ),
                );
              },
              loading: () => const Center(child: CircularProgressIndicator()),
              error: (error, stack) => Center(
                child: Text('Error loading group: $error'),
              ),
            ),
    );
  }

  Widget _buildGroupHeader(GroupChatModel group, bool isAdmin) {
    if (_isEditing) {
      return Column(
        children: [
          GestureDetector(
            onTap: _pickImage,
            child: Stack(
              children: [
                CircleAvatar(
                  radius: 50,
                  backgroundColor: Colors.grey[300],
                  backgroundImage: _newGroupImage != null
                      ? FileImage(_newGroupImage!)
                      : group.imageUrl != null
                          ? NetworkImage(group.imageUrl!) as ImageProvider
                          : null,
                  child: _newGroupImage == null && group.imageUrl == null
                      ? const Icon(
                          Icons.group,
                          size: 50,
                          color: Colors.white,
                        )
                      : null,
                ),
                Positioned(
                  bottom: 0,
                  right: 0,
                  child: Container(
                    padding: const EdgeInsets.all(4),
                    decoration: const BoxDecoration(
                      color: AppTheme.primaryColor,
                      shape: BoxShape.circle,
                    ),
                    child: const Icon(
                      Icons.camera_alt,
                      color: Colors.white,
                      size: 20,
                    ),
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 16),
          TextField(
            controller: _nameController,
            decoration: InputDecoration(
              labelText: 'Group Name',
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          ),
          const SizedBox(height: 16),
          TextField(
            controller: _descriptionController,
            maxLines: 3,
            decoration: InputDecoration(
              labelText: 'Description',
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          ),
          const SizedBox(height: 16),
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              TextButton(
                onPressed: _toggleEditing,
                child: const Text('Cancel'),
              ),
              const SizedBox(width: 8),
              ElevatedButton(
                onPressed: () => _saveChanges(group),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppTheme.primaryColor,
                ),
                child: const Text('Save'),
              ),
            ],
          ),
        ],
      );
    } else {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          CircleAvatar(
            radius: 50,
            backgroundColor: Colors.grey[300],
            backgroundImage: group.imageUrl != null
                ? NetworkImage(group.imageUrl!) as ImageProvider
                : null,
            child: group.imageUrl == null
                ? const Icon(
                    Icons.group,
                    size: 50,
                    color: Colors.white,
                  )
                : null,
          ),
          const SizedBox(height: 16),
          Text(
            group.name,
            style: const TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: AppTheme.textPrimaryColor,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          Text(
            group.description,
            style: const TextStyle(
              fontSize: 16,
              color: AppTheme.textSecondaryColor,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          Text(
            'Created ${_formatDate(group.createdAt)}',
            style: const TextStyle(
              fontSize: 12,
              color: AppTheme.textSecondaryColor,
            ),
          ),
        ],
      );
    }
  }

  Widget _buildMembersList(
    GroupChatModel group,
    UserModel currentUser,
    AsyncValue<List<UserModel>> membersAsync,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Members (${group.members.length})',
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: AppTheme.textPrimaryColor,
              ),
            ),
            if (group.isUserAdmin(currentUser.id) && !_isEditing)
              TextButton.icon(
                onPressed: _showAddMemberDialog,
                icon: const Icon(Icons.add),
                label: const Text('Add'),
              ),
          ],
        ),
        const SizedBox(height: 8),
        membersAsync.when(
          data: (members) {
            if (members.isEmpty) {
              return const Center(
                child: Text('No members found'),
              );
            }

            // Sort members by role (admin first, then moderator, then member)
            members.sort((a, b) {
              final roleA = group.getUserRole(a.id) ?? GroupMemberRole.member;
              final roleB = group.getUserRole(b.id) ?? GroupMemberRole.member;
              return roleA.index.compareTo(roleB.index);
            });

            return ListView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: members.length,
              itemBuilder: (context, index) {
                final member = members[index];
                final role =
                    group.getUserRole(member.id) ?? GroupMemberRole.member;
                final isCurrentUser = member.id == currentUser.id;

                return ListTile(
                  leading: CircleAvatar(
                    radius: 20,
                    backgroundColor: Colors.grey[300],
                    backgroundImage: member.profilePicture != null
                        ? NetworkImage(member.profilePicture!) as ImageProvider
                        : null,
                    child: member.profilePicture == null
                        ? const Icon(
                            Icons.person,
                            size: 20,
                            color: Colors.white,
                          )
                        : null,
                  ),
                  title: Row(
                    children: [
                      Text(
                        '${member.firstName} ${member.lastName}',
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      if (isCurrentUser)
                        const Padding(
                          padding: EdgeInsets.only(left: 4),
                          child: Text(
                            '(You)',
                            style: TextStyle(
                              fontSize: 12,
                              color: AppTheme.textSecondaryColor,
                            ),
                          ),
                        ),
                    ],
                  ),
                  subtitle: Text(role.displayName),
                  trailing: group.isUserAdmin(currentUser.id) &&
                          !_isEditing &&
                          !isCurrentUser
                      ? PopupMenuButton<String>(
                          onSelected: (value) {
                            if (value == 'change_role') {
                              _showChangeRoleDialog(group, member);
                            } else if (value == 'remove') {
                              _showRemoveMemberDialog(group, member);
                            }
                          },
                          itemBuilder: (context) => [
                            const PopupMenuItem(
                              value: 'change_role',
                              child: Text('Change Role'),
                            ),
                            const PopupMenuItem(
                              value: 'remove',
                              child: Text('Remove'),
                            ),
                          ],
                        )
                      : null,
                );
              },
            );
          },
          loading: () => const Center(child: CircularProgressIndicator()),
          error: (error, stack) => Center(
            child: Text('Error loading members: $error'),
          ),
        ),
      ],
    );
  }

  Widget _buildGroupActions(GroupChatModel group, bool isAdmin) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Group Actions',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: AppTheme.textPrimaryColor,
          ),
        ),
        const SizedBox(height: 8),
        ListTile(
          leading: const Icon(Icons.exit_to_app, color: Colors.orange),
          title: const Text('Leave Group'),
          onTap: _leaveGroup,
        ),
        if (isAdmin)
          ListTile(
            leading: const Icon(Icons.delete, color: Colors.red),
            title: const Text('Delete Group'),
            onTap: _deleteGroup,
          ),
      ],
    );
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays < 1) {
      return 'today';
    } else if (difference.inDays < 2) {
      return 'yesterday';
    } else if (difference.inDays < 7) {
      return '${difference.inDays} days ago';
    } else if (difference.inDays < 30) {
      final weeks = (difference.inDays / 7).floor();
      return '$weeks ${weeks == 1 ? 'week' : 'weeks'} ago';
    } else if (difference.inDays < 365) {
      final months = (difference.inDays / 30).floor();
      return '$months ${months == 1 ? 'month' : 'months'} ago';
    } else {
      final years = (difference.inDays / 365).floor();
      return '$years ${years == 1 ? 'year' : 'years'} ago';
    }
  }
}
