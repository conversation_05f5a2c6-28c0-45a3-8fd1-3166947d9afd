import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:culture_connect/models/offline/sync_status.dart';
import 'package:culture_connect/models/offline/bandwidth_usage.dart';
import 'package:culture_connect/providers/bandwidth_provider.dart';
import 'package:culture_connect/services/translation_sync_service.dart';
import 'package:culture_connect/widgets/translation/translation_conflict_resolution.dart';

/// A screen for managing offline translations
class OfflineTranslationsScreen extends ConsumerStatefulWidget {
  /// Creates a new offline translations screen
  const OfflineTranslationsScreen({super.key});

  @override
  ConsumerState<OfflineTranslationsScreen> createState() =>
      _OfflineTranslationsScreenState();
}

class _OfflineTranslationsScreenState
    extends ConsumerState<OfflineTranslationsScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final syncStatus = ref.watch(translationSyncStatusProvider);
    final isSyncing = ref.watch(isSyncingProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Offline Translations'),
        actions: [
          // Sync button
          IconButton(
            icon: const Icon(Icons.sync),
            onPressed: isSyncing
                ? null
                : () {
                    ref.read(translationSyncServiceProvider).syncTranslations();
                  },
            tooltip: 'Sync translations',
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'Status'),
            Tab(text: 'Bandwidth'),
            Tab(text: 'Conflicts'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          // Status tab
          _buildStatusTab(syncStatus),

          // Bandwidth tab
          _buildBandwidthTab(),

          // Conflicts tab
          const TranslationConflictsList(),
        ],
      ),
    );
  }

  /// Builds the status tab
  Widget _buildStatusTab(AsyncValue<SyncStatus> syncStatus) {
    return syncStatus.when(
      data: (status) {
        return SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Sync status card
              _buildStatusCard(status),
              const SizedBox(height: 16),

              // Pending translations
              _buildPendingTranslationsCard(),
              const SizedBox(height: 16),

              // Settings
              _buildSettingsCard(),
            ],
          ),
        );
      },
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (error, stack) => Center(
        child: Text('Error loading sync status: $error'),
      ),
    );
  }

  /// Builds the status card
  Widget _buildStatusCard(SyncStatus status) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Sync Status',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: status.color.withAlpha(50),
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    status.icon,
                    color: status.color,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        status.displayName,
                        style: Theme.of(context).textTheme.titleSmall,
                      ),
                      const SizedBox(height: 4),
                      Text(
                        _getSyncStatusDescription(status),
                        style: Theme.of(context).textTheme.bodySmall,
                      ),
                    ],
                  ),
                ),
              ],
            ),
            if (status == SyncStatus.syncing) ...[
              const SizedBox(height: 16),
              const LinearProgressIndicator(),
            ],
          ],
        ),
      ),
    );
  }

  /// Gets the description for a sync status
  String _getSyncStatusDescription(SyncStatus status) {
    switch (status) {
      case SyncStatus.notSynced:
        return 'Your translations are not synced. Connect to the internet to sync.';
      case SyncStatus.pending:
        return 'Your translations are waiting to be synced. Connect to the internet to sync.';
      case SyncStatus.syncing:
        return 'Your translations are being synced. Please wait...';
      case SyncStatus.synced:
        return 'Your translations are up to date.';
      case SyncStatus.failed:
        return 'Sync failed. Please try again.';
    }
  }

  /// Builds the pending translations card
  Widget _buildPendingTranslationsCard() {
    return Consumer(
      builder: (context, ref, child) {
        final pendingTranslationsAsync = ref.watch(pendingTranslationsProvider);

        return pendingTranslationsAsync.when(
          data: (pendingCount) {
            return Card(
              elevation: 2,
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Pending Translations',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    const SizedBox(height: 16),
                    Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: pendingCount > 0
                                ? Colors.orange.withAlpha(50)
                                : Colors.green.withAlpha(50),
                            shape: BoxShape.circle,
                          ),
                          child: Icon(
                            pendingCount > 0
                                ? Icons.pending_actions
                                : Icons.check_circle,
                            color:
                                pendingCount > 0 ? Colors.orange : Colors.green,
                            size: 24,
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                pendingCount > 0
                                    ? '$pendingCount Pending'
                                    : 'All Synced',
                                style: Theme.of(context).textTheme.titleSmall,
                              ),
                              const SizedBox(height: 4),
                              Text(
                                pendingCount > 0
                                    ? 'You have $pendingCount translations waiting to be synced.'
                                    : 'All your translations are synced.',
                                style: Theme.of(context).textTheme.bodySmall,
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            );
          },
          loading: () => const Card(
            elevation: 2,
            child: Padding(
              padding: EdgeInsets.all(16.0),
              child: Center(child: CircularProgressIndicator()),
            ),
          ),
          error: (error, stack) => Card(
            elevation: 2,
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Center(
                child: Text('Error loading pending translations: $error'),
              ),
            ),
          ),
        );
      },
    );
  }

  /// Builds the settings card
  Widget _buildSettingsCard() {
    return Consumer(
      builder: (context, ref, child) {
        final bandwidthSettingsAsync = ref.watch(bandwidthSettingsProvider);

        return bandwidthSettingsAsync.when(
          data: (settings) {
            return Card(
              elevation: 2,
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Sync Settings',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    const SizedBox(height: 16),
                    SwitchListTile(
                      title: const Text('Sync on WiFi only'),
                      subtitle: const Text(
                          'Only sync translations when connected to WiFi'),
                      value: settings.syncOnWifiOnly,
                      onChanged: (value) {
                        if (!mounted) return;
                        ref.read(bandwidthSettingsProvider.notifier).update(
                              settings.copyWith(syncOnWifiOnly: value),
                            );
                      },
                    ),
                    SwitchListTile(
                      title: const Text('Sync automatically'),
                      subtitle: const Text(
                          'Automatically sync translations when online'),
                      value: settings.syncAutomatically,
                      onChanged: (value) {
                        if (!mounted) return;
                        ref.read(bandwidthSettingsProvider.notifier).update(
                              settings.copyWith(syncAutomatically: value),
                            );
                      },
                    ),
                  ],
                ),
              ),
            );
          },
          loading: () => const Card(
            elevation: 2,
            child: Padding(
              padding: EdgeInsets.all(16.0),
              child: Center(child: CircularProgressIndicator()),
            ),
          ),
          error: (error, stack) => Card(
            elevation: 2,
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Center(
                child: Text('Error loading settings: $error'),
              ),
            ),
          ),
        );
      },
    );
  }

  /// Builds the bandwidth tab
  Widget _buildBandwidthTab() {
    return Consumer(
      builder: (context, ref, child) {
        final bandwidthUsageAsync = ref.watch(bandwidthUsageProvider);

        return bandwidthUsageAsync.when(
          data: (usage) {
            return SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Usage card
                  Card(
                    elevation: 2,
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Bandwidth Usage',
                            style: Theme.of(context).textTheme.titleMedium,
                          ),
                          const SizedBox(height: 16),
                          _buildUsageRow(
                            'Total',
                            _formatBytes(
                                usage.bytesDownloaded + usage.bytesUploaded),
                            Icons.data_usage,
                            Colors.blue,
                          ),
                          const SizedBox(height: 8),
                          _buildUsageRow(
                            'WiFi',
                            _formatBytes(usage.networkType == NetworkType.wifi
                                ? usage.bytesDownloaded + usage.bytesUploaded
                                : 0),
                            Icons.wifi,
                            Colors.green,
                          ),
                          const SizedBox(height: 8),
                          _buildUsageRow(
                            'Mobile',
                            _formatBytes(usage.networkType == NetworkType.mobile
                                ? usage.bytesDownloaded + usage.bytesUploaded
                                : 0),
                            Icons.network_cell,
                            Colors.orange,
                          ),
                        ],
                      ),
                    ),
                  ),
                  const SizedBox(height: 16),

                  // Limits card
                  _buildLimitsCard(),
                ],
              ),
            );
          },
          loading: () => const Center(child: CircularProgressIndicator()),
          error: (error, stack) => Center(
            child: Text('Error loading bandwidth usage: $error'),
          ),
        );
      },
    );
  }

  /// Builds a usage row
  Widget _buildUsageRow(
      String label, String value, IconData icon, Color color) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: color.withAlpha(50),
            shape: BoxShape.circle,
          ),
          child: Icon(
            icon,
            color: color,
            size: 16,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: Text(
            label,
            style: Theme.of(context).textTheme.bodyMedium,
          ),
        ),
        Text(
          value,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
        ),
      ],
    );
  }

  /// Builds the limits card
  Widget _buildLimitsCard() {
    return Consumer(
      builder: (context, ref, child) {
        final bandwidthSettingsAsync = ref.watch(bandwidthSettingsProvider);

        return bandwidthSettingsAsync.when(
          data: (settings) {
            return Card(
              elevation: 2,
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Bandwidth Limits',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    const SizedBox(height: 16),
                    _buildLimitRow(
                      'Mobile Data',
                      _formatBytes(settings.maxMobileDataUsagePerDay),
                      Icons.network_cell,
                      Colors.orange,
                    ),
                    const SizedBox(height: 8),
                    _buildLimitRow(
                      'WiFi',
                      _formatBytes(settings.maxWifiDataUsagePerDay),
                      Icons.wifi,
                      Colors.green,
                    ),
                  ],
                ),
              ),
            );
          },
          loading: () => const Card(
            elevation: 2,
            child: Padding(
              padding: EdgeInsets.all(16),
              child: Center(child: CircularProgressIndicator()),
            ),
          ),
          error: (error, stack) => Card(
            elevation: 2,
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Center(
                child: Text('Error loading settings: $error'),
              ),
            ),
          ),
        );
      },
    );
  }

  /// Builds a limit row
  Widget _buildLimitRow(
      String label, String value, IconData icon, Color color) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: color.withAlpha(50),
            shape: BoxShape.circle,
          ),
          child: Icon(
            icon,
            color: color,
            size: 16,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: Theme.of(context).textTheme.bodyMedium,
              ),
              Text(
                'Daily limit',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Colors.grey,
                    ),
              ),
            ],
          ),
        ),
        Text(
          value,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
        ),
      ],
    );
  }

  /// Formats bytes to a human-readable string
  String _formatBytes(int bytes) {
    if (bytes < 1024) {
      return '$bytes B';
    } else if (bytes < 1024 * 1024) {
      final kb = bytes / 1024;
      return '${kb.toStringAsFixed(1)} KB';
    } else if (bytes < 1024 * 1024 * 1024) {
      final mb = bytes / (1024 * 1024);
      return '${mb.toStringAsFixed(1)} MB';
    } else {
      final gb = bytes / (1024 * 1024 * 1024);
      return '${gb.toStringAsFixed(1)} GB';
    }
  }
}
