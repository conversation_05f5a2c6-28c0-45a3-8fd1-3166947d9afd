import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:culture_connect/models/user_model.dart';
import 'package:culture_connect/providers/auth_provider.dart';
import 'package:culture_connect/providers/chat_provider.dart';
import 'package:culture_connect/screens/messaging/chat_screen.dart';
import 'package:culture_connect/theme/app_theme.dart';
import 'package:culture_connect/widgets/custom_app_bar.dart';

class NewChatScreen extends ConsumerStatefulWidget {
  const NewChatScreen({super.key});

  @override
  ConsumerState<NewChatScreen> createState() => _NewChatScreenState();
}

class _NewChatScreenState extends ConsumerState<NewChatScreen> {
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';
  bool _isLoading = false;
  List<UserModel> _searchResults = [];
  List<UserModel> _recentContacts = [];

  @override
  void initState() {
    super.initState();
    _loadRecentContacts();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _loadRecentContacts() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final currentUser = await ref.read(currentUserModelProvider.future);
      if (currentUser == null) return;

      // Get recent chats
      final chatsSnapshot = await FirebaseFirestore.instance
          .collection('chats')
          .where('participants', arrayContains: currentUser.id)
          .orderBy('lastMessageAt', descending: true)
          .limit(5)
          .get();

      final participantIds = <String>{};
      for (final doc in chatsSnapshot.docs) {
        final participants =
            List<String>.from(doc.data()['participants'] as List);
        participants.remove(currentUser.id);
        participantIds.addAll(participants);
      }

      // Get user data for participants
      if (participantIds.isNotEmpty) {
        final userDocs = await Future.wait(
          participantIds.map((userId) =>
              FirebaseFirestore.instance.collection('users').doc(userId).get()),
        );

        final users = userDocs
            .where((doc) => doc.exists)
            .map((doc) => UserModel.fromJson({...doc.data()!, 'id': doc.id}))
            .toList();

        setState(() {
          _recentContacts = users;
          _isLoading = false;
        });
      } else {
        setState(() {
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _searchUsers(String query) async {
    if (query.isEmpty) {
      setState(() {
        _searchResults = [];
      });
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final currentUser = await ref.read(currentUserModelProvider.future);
      if (currentUser == null) return;

      // Search for users by name
      final querySnapshot = await FirebaseFirestore.instance
          .collection('users')
          .where('firstName', isGreaterThanOrEqualTo: query)
          .where('firstName', isLessThanOrEqualTo: '$query\uf8ff')
          .limit(10)
          .get();

      final lastNameQuerySnapshot = await FirebaseFirestore.instance
          .collection('users')
          .where('lastName', isGreaterThanOrEqualTo: query)
          .where('lastName', isLessThanOrEqualTo: '$query\uf8ff')
          .limit(10)
          .get();

      final allDocs = [
        ...querySnapshot.docs,
        ...lastNameQuerySnapshot.docs,
      ];

      // Remove duplicates and current user
      final uniqueUsers = <String, UserModel>{};
      for (final doc in allDocs) {
        final user = UserModel.fromJson({...doc.data(), 'id': doc.id});
        if (user.id != currentUser.id) {
          uniqueUsers[user.id] = user;
        }
      }

      setState(() {
        _searchResults = uniqueUsers.values.toList();
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _updateSearchQuery(String query) {
    setState(() {
      _searchQuery = query;
    });
    _searchUsers(query);
  }

  Future<void> _startChat(UserModel recipient) async {
    try {
      setState(() {
        _isLoading = true;
      });

      final chatId =
          await ref.read(chatProvider.notifier).createChat(recipient.id);

      if (mounted) {
        setState(() {
          _isLoading = false;
        });

        Navigator.pushReplacement(
          context,
          MaterialPageRoute(
            builder: (context) => ChatScreen(
              chatId: chatId,
              recipient: recipient,
            ),
          ),
        );
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error starting chat: $e')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const CustomAppBar(
        title: 'New Chat',
        showBackButton: true,
      ),
      body: Column(
        children: [
          // Search bar
          Padding(
            padding: EdgeInsets.all(16.r),
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: 'Search for users...',
                prefixIcon: const Icon(Icons.search),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12.r),
                  borderSide: BorderSide.none,
                ),
                filled: true,
                fillColor: Colors.grey[200],
                contentPadding: EdgeInsets.symmetric(
                  horizontal: 16.w,
                  vertical: 12.h,
                ),
              ),
              onChanged: _updateSearchQuery,
            ),
          ),

          // Loading indicator
          if (_isLoading)
            const Padding(
              padding: EdgeInsets.all(16.0),
              child: Center(child: CircularProgressIndicator()),
            ),

          // Search results or recent contacts
          Expanded(
            child: _searchQuery.isNotEmpty
                ? _buildSearchResults()
                : _buildRecentContacts(),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchResults() {
    if (_searchResults.isEmpty && !_isLoading) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.search_off,
              size: 64.sp,
              color: Colors.grey[400],
            ),
            SizedBox(height: 16.h),
            Text(
              'No users found',
              style: TextStyle(
                fontSize: 16.sp,
                color: AppTheme.textSecondaryColor,
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      itemCount: _searchResults.length,
      itemBuilder: (context, index) {
        final user = _searchResults[index];
        return _buildUserTile(user);
      },
    );
  }

  Widget _buildRecentContacts() {
    if (_recentContacts.isEmpty && !_isLoading) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.people_outline,
              size: 64.sp,
              color: Colors.grey[400],
            ),
            SizedBox(height: 16.h),
            Text(
              'No recent contacts',
              style: TextStyle(
                fontSize: 16.sp,
                fontWeight: FontWeight.bold,
                color: AppTheme.textPrimaryColor,
              ),
            ),
            SizedBox(height: 8.h),
            Text(
              'Search for users to start chatting',
              style: TextStyle(
                fontSize: 14.sp,
                color: AppTheme.textSecondaryColor,
              ),
            ),
          ],
        ),
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
          child: Text(
            'Recent Contacts',
            style: TextStyle(
              fontSize: 16.sp,
              fontWeight: FontWeight.bold,
              color: AppTheme.textPrimaryColor,
            ),
          ),
        ),
        Expanded(
          child: ListView.builder(
            itemCount: _recentContacts.length,
            itemBuilder: (context, index) {
              final user = _recentContacts[index];
              return _buildUserTile(user);
            },
          ),
        ),
      ],
    );
  }

  Widget _buildUserTile(UserModel user) {
    return ListTile(
      onTap: () => _startChat(user),
      leading: CircleAvatar(
        radius: 24.r,
        backgroundColor: Colors.grey[300],
        backgroundImage: user.profilePicture != null
            ? NetworkImage(user.profilePicture!)
            : null,
        child: user.profilePicture == null
            ? Icon(
                Icons.person,
                color: Colors.white,
                size: 24.r,
              )
            : null,
      ),
      title: Text(
        '${user.firstName} ${user.lastName}',
        style: TextStyle(
          fontSize: 16.sp,
          fontWeight: FontWeight.bold,
          color: AppTheme.textPrimaryColor,
        ),
      ),
      subtitle: Text(
        user.userType == 'guide' ? 'Cultural Guide' : 'Tourist',
        style: TextStyle(
          fontSize: 14.sp,
          color: AppTheme.textSecondaryColor,
        ),
      ),
      trailing: Container(
        padding: EdgeInsets.symmetric(
          horizontal: 12.w,
          vertical: 6.h,
        ),
        decoration: BoxDecoration(
          color: user.userType == 'guide'
              ? AppTheme.primaryColor.withAlpha(30)
              : Colors.amber.withAlpha(30),
          borderRadius: BorderRadius.circular(16.r),
        ),
        child: Text(
          user.userType == 'guide' ? 'Guide' : 'Tourist',
          style: TextStyle(
            fontSize: 12.sp,
            fontWeight: FontWeight.bold,
            color: user.userType == 'guide'
                ? AppTheme.primaryColor
                : Colors.amber[800],
          ),
        ),
      ),
    );
  }
}
