// Flutter imports
import 'package:flutter/material.dart';

// Package imports
import 'package:flutter_riverpod/flutter_riverpod.dart';

// Project imports
import 'package:culture_connect/models/group_chat_model.dart';
import 'package:culture_connect/models/message_model.dart' as message_model;
import 'package:culture_connect/models/user_model.dart';
import 'package:culture_connect/providers/auth_provider.dart';
import 'package:culture_connect/providers/chat_provider.dart';
import 'package:culture_connect/providers/group_chat_provider.dart';
import 'package:culture_connect/providers/user_provider.dart';
import 'package:culture_connect/theme/app_theme.dart';
import 'package:culture_connect/widgets/custom_app_bar.dart';

/// Screen for selecting recipients to forward messages to
class ForwardMessageScreen extends ConsumerStatefulWidget {
  /// The messages to forward
  final List<message_model.MessageModel> messages;

  /// Creates a new forward message screen for a single message
  ForwardMessageScreen({
    super.key,
    required message_model.MessageModel message,
  }) : messages = [message];

  /// Creates a new forward message screen for multiple messages
  const ForwardMessageScreen.multiple({
    super.key,
    required this.messages,
  });

  @override
  ConsumerState<ForwardMessageScreen> createState() =>
      _ForwardMessageScreenState();
}

class _ForwardMessageScreenState extends ConsumerState<ForwardMessageScreen> {
  final List<String> _selectedChats = [];
  final List<String> _selectedGroups = [];
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';

  // We don't need these fields for this simplified version

  @override
  void initState() {
    super.initState();
    _searchController.addListener(() {
      setState(() {
        _searchQuery = _searchController.text.toLowerCase();
      });
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  void _toggleChatSelection(String chatId) {
    setState(() {
      if (_selectedChats.contains(chatId)) {
        _selectedChats.remove(chatId);
      } else {
        _selectedChats.add(chatId);
      }
    });
  }

  void _toggleGroupSelection(String groupId) {
    setState(() {
      if (_selectedGroups.contains(groupId)) {
        _selectedGroups.remove(groupId);
      } else {
        _selectedGroups.add(groupId);
      }
    });
  }

  Future<void> _forwardMessages() async {
    final currentUser = ref.read(currentUserModelProvider).value;
    if (currentUser == null) return;

    // Create a new message based on the original
    final now = DateTime.now();

    // Forward to selected chats
    for (final chatId in _selectedChats) {
      final chat = await ref.read(chatDetailsProvider(chatId).future);
      if (chat == null) continue;

      final recipientId = chat.participants.firstWhere(
        (id) => id != currentUser.id,
        orElse: () => '',
      );

      if (recipientId.isEmpty) continue;

      final forwardedMessage = message_model.MessageModel(
        id: now.millisecondsSinceEpoch.toString() + chatId,
        chatId: chatId,
        senderId: currentUser.id,
        recipientId: recipientId,
        text: widget.messages[0].text,
        timestamp: now,
        status: message_model.MessageStatus.sending,
        type: widget.messages[0].type,
        mediaUrl: widget.messages[0].mediaUrl,
        isForwarded: true,
        originalSenderId: widget.messages[0].senderId,
      );

      await ref.read(chatProvider.notifier).sendMessage(forwardedMessage);
    }

    // Forward to selected groups
    for (final groupId in _selectedGroups) {
      final forwardedMessage = message_model.MessageModel(
        id: now.millisecondsSinceEpoch.toString() + groupId,
        chatId: groupId,
        senderId: currentUser.id,
        recipientId: '',
        text: widget.messages[0].text,
        timestamp: now,
        status: message_model.MessageStatus.sending,
        type: widget.messages[0].type,
        mediaUrl: widget.messages[0].mediaUrl,
        isForwarded: true,
        originalSenderId: widget.messages[0].senderId,
        isGroupMessage: true,
      );

      await ref
          .read(groupChatProvider.notifier)
          .sendGroupMessage(forwardedMessage);
    }

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
              'Message forwarded to ${_selectedChats.length + _selectedGroups.length} chats'),
          backgroundColor: Colors.green,
        ),
      );
      Navigator.pop(context);
    }
  }

  @override
  Widget build(BuildContext context) {
    final chatsAsync = ref.watch(userChatsProvider);
    // Use the existing provider for group chats
    final groupsAsync = ref.watch(groupChatProvider);

    return Scaffold(
      appBar: const CustomAppBar(
        title: 'Forward Message',
        showBackButton: true,
      ),
      body: Column(
        children: [
          // Search bar
          Padding(
            padding: const EdgeInsets.all(16),
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: 'Search...',
                prefixIcon: const Icon(Icons.search),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(24),
                ),
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 12,
                ),
              ),
            ),
          ),

          // Message preview
          Container(
            margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.grey[100],
              borderRadius: BorderRadius.circular(16),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Message to forward:',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: AppTheme.textSecondaryColor,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  widget.messages[0].text,
                  style: const TextStyle(
                    fontSize: 16,
                    color: AppTheme.textPrimaryColor,
                  ),
                ),
              ],
            ),
          ),

          // Chats and groups list
          Expanded(
            child: ListView(
              children: [
                // Chats section
                const Padding(
                  padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  child: Text(
                    'Chats',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: AppTheme.textPrimaryColor,
                    ),
                  ),
                ),
                chatsAsync.when(
                  data: (chats) {
                    final filteredChats = chats.where((chat) {
                      if (_searchQuery.isEmpty) return true;
                      return chat.lastMessageText
                          .toLowerCase()
                          .contains(_searchQuery);
                    }).toList();

                    return ListView.builder(
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      itemCount: filteredChats.length,
                      itemBuilder: (context, index) {
                        final chat = filteredChats[index];
                        return _buildChatTile(chat);
                      },
                    );
                  },
                  loading: () =>
                      const Center(child: CircularProgressIndicator()),
                  error: (error, stack) => Center(
                    child: Text('Error loading chats: $error'),
                  ),
                ),

                // Groups section
                const Padding(
                  padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  child: Text(
                    'Groups',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: AppTheme.textPrimaryColor,
                    ),
                  ),
                ),
                groupsAsync.when(
                  data: (groups) {
                    final filteredGroups = groups.where((group) {
                      if (_searchQuery.isEmpty) return true;
                      return group.name.toLowerCase().contains(_searchQuery);
                    }).toList();

                    return ListView.builder(
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      itemCount: filteredGroups.length,
                      itemBuilder: (context, index) {
                        final group = filteredGroups[index];
                        return _buildGroupTile(group);
                      },
                    );
                  },
                  loading: () =>
                      const Center(child: CircularProgressIndicator()),
                  error: (error, stack) => Center(
                    child: Text('Error loading groups: $error'),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _selectedChats.isEmpty && _selectedGroups.isEmpty
            ? null
            : _forwardMessages,
        backgroundColor: _selectedChats.isEmpty && _selectedGroups.isEmpty
            ? Colors.grey
            : AppTheme.primaryColor,
        label: Text(
          'Forward (${_selectedChats.length + _selectedGroups.length})',
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
        icon: const Icon(Icons.send),
      ),
    );
  }

  Widget _buildChatTile(message_model.ChatModel chat) {
    final isSelected = _selectedChats.contains(chat.id);

    return FutureBuilder<UserModel?>(
      future: _getChatRecipient(chat),
      builder: (context, snapshot) {
        final recipient = snapshot.data;

        return ListTile(
          leading: CircleAvatar(
            backgroundColor: Colors.grey[300],
            backgroundImage: recipient?.profilePicture != null
                ? NetworkImage(recipient!.profilePicture!)
                : null,
            child: recipient?.profilePicture == null
                ? const Icon(
                    Icons.person,
                    color: Colors.white,
                    size: 24,
                  )
                : null,
          ),
          title: Text(
            recipient?.firstName ?? 'Unknown',
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          subtitle: Text(
            chat.lastMessageText.isNotEmpty
                ? chat.lastMessageText
                : 'No messages yet',
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
            style: const TextStyle(
              fontSize: 14,
              color: AppTheme.textSecondaryColor,
            ),
          ),
          trailing: Checkbox(
            value: isSelected,
            onChanged: (value) => _toggleChatSelection(chat.id),
            activeColor: AppTheme.primaryColor,
          ),
          onTap: () => _toggleChatSelection(chat.id),
        );
      },
    );
  }

  Widget _buildGroupTile(GroupChatModel group) {
    final isSelected = _selectedGroups.contains(group.id);

    return ListTile(
      leading: CircleAvatar(
        backgroundColor: Colors.grey[300],
        child: const Icon(
          Icons.group,
          color: Colors.white,
          size: 24,
        ),
      ),
      title: Text(
        group.name,
        style: const TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.bold,
        ),
      ),
      subtitle: Text(
        '${group.members.length} members',
        style: const TextStyle(
          fontSize: 14,
          color: AppTheme.textSecondaryColor,
        ),
      ),
      trailing: Checkbox(
        value: isSelected,
        onChanged: (value) => _toggleGroupSelection(group.id),
        activeColor: AppTheme.primaryColor,
      ),
      onTap: () => _toggleGroupSelection(group.id),
    );
  }

  Future<UserModel?> _getChatRecipient(message_model.ChatModel chat) async {
    final currentUser = ref.read(currentUserModelProvider).value;
    if (currentUser == null) return null;

    final recipientId = chat.participants.firstWhere(
      (id) => id != currentUser.id,
      orElse: () => '',
    );

    if (recipientId.isEmpty) return null;

    return ref.read(userProvider(recipientId).future);
  }
}
