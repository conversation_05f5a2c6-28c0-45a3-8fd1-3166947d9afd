import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:culture_connect/models/group_chat_model.dart';
import 'package:culture_connect/providers/group_chat_provider.dart';
import 'package:culture_connect/widgets/custom_app_bar.dart';
import 'package:culture_connect/widgets/messaging/group_language_preferences.dart';

/// A screen for managing group translation settings
class GroupTranslationSettingsScreen extends ConsumerWidget {
  /// The ID of the group chat
  final String groupId;

  /// Creates a new group translation settings screen
  const GroupTranslationSettingsScreen({
    super.key,
    required this.groupId,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final groupChatAsync = ref.watch(groupChatDetailsProvider(groupId));

    return Scaffold(
      appBar: _buildAppBar(groupChatAsync),
      body: GroupLanguagePreferences(
        groupId: groupId,
      ),
    );
  }

  /// Build the app bar
  PreferredSizeWidget _buildAppBar(AsyncValue<GroupChatModel?> groupChatAsync) {
    return groupChatAsync.when(
      data: (groupChat) {
        if (groupChat == null) {
          return const CustomAppBar(
            title: 'Translation Settings',
            showBackButton: true,
          );
        }

        return CustomAppBar(
          title: 'Translation Settings',
          subTitle: groupChat.name,
          showBackButton: true,
        );
      },
      loading: () => const CustomAppBar(
        title: 'Translation Settings',
        showBackButton: true,
      ),
      error: (_, __) => const CustomAppBar(
        title: 'Translation Settings',
        showBackButton: true,
      ),
    );
  }
}
