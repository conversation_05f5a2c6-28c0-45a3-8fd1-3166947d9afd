import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:culture_connect/models/booking.dart' as booking_model;
import 'package:culture_connect/models/experience.dart';
import 'package:culture_connect/models/guide.dart';
import 'package:culture_connect/models/report_model.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:culture_connect/screens/ar_experience_view_screen.dart';
import 'package:culture_connect/screens/payment/payment_confirmation_screen.dart';
import 'package:culture_connect/services/booking_service.dart';
import 'package:culture_connect/widgets/weather_info_card.dart';
import 'package:culture_connect/widgets/booking_calendar.dart';
import 'package:culture_connect/widgets/similar_experiences.dart';
import 'package:culture_connect/widgets/share_experience_button.dart';
import 'package:culture_connect/widgets/wishlist_button.dart';
import 'package:culture_connect/widgets/contact_guide_button.dart';
import 'package:culture_connect/widgets/report/report_dialog.dart';
import 'package:culture_connect/widgets/reviews/review_list.dart';

class ExperienceDetailsScreen extends ConsumerStatefulWidget {
  final Experience experience;

  const ExperienceDetailsScreen({
    super.key,
    required this.experience,
  });

  @override
  ConsumerState<ExperienceDetailsScreen> createState() =>
      _ExperienceDetailsScreenState();
}

class _ExperienceDetailsScreenState
    extends ConsumerState<ExperienceDetailsScreen> {
  final PageController _imagePageController = PageController();
  int _currentImageIndex = 0;
  DateTime? _selectedDate;
  int _selectedParticipants = 1;
  TimeSlot? _selectedTimeSlot;
  double _calculatedPrice = 0.0;

  @override
  void dispose() {
    _imagePageController.dispose();
    super.dispose();
  }

  /// Builds a verification chip with an icon and label
  Widget _buildVerificationChip(String label, IconData icon, Color color) {
    return Chip(
      avatar: Icon(
        icon,
        color: color,
        size: 16,
      ),
      label: Text(
        label,
        style: TextStyle(
          color: color,
          fontWeight: FontWeight.bold,
        ),
      ),
      backgroundColor: color.withOpacity(0.1),
      side: BorderSide(
        color: color.withOpacity(0.3),
      ),
    );
  }

  /// Builds a safety information item with title, description, and icon
  Widget _buildSafetyItem(String title, String description, IconData icon) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(
            icon,
            color: Colors.blue,
            size: 20,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  description,
                  style: const TextStyle(
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // Mock guide data
  Guide _getMockGuide(Experience experience) {
    return Guide(
      id: 'guide_${experience.id}',
      name: experience.guideName,
      bio: 'Professional guide with expertise in cultural experiences.',
      profileImageUrl: experience.guideImageUrl,
      rating: experience.rating,
      reviewCount: experience.reviewCount,
      languages: experience.languages,
      location: experience.location,
      coordinates: {
        'latitude': experience.coordinates.latitude,
        'longitude': experience.coordinates.longitude,
      },
      isVerified: true,
      specialties: [experience.category, 'Cultural Tours', 'Local Cuisine'],
      yearsOfExperience: 5,
      email: '<EMAIL>',
      phone: '+1234567890',
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final experience = widget.experience;

    // Get the images for the carousel
    final images = [
      experience.imageUrl,
      'https://picsum.photos/800/600?random=1',
      'https://picsum.photos/800/600?random=2',
    ];

    return Scaffold(
      body: CustomScrollView(
        slivers: [
          // App Bar with Image Carousel
          SliverAppBar(
            expandedHeight: 300,
            pinned: true,
            actions: [
              // Wishlist button
              WishlistIconButton(
                experienceId: experience.id,
                experienceName: experience.title,
                color: Colors.white,
              ),
              // Share button
              IconButton(
                icon: const Icon(Icons.share, color: Colors.white),
                onPressed: () {
                  showDialog(
                    context: context,
                    builder: (context) => ShareExperienceDialog(
                      experience: experience,
                    ),
                  );
                },
              ),
            ],
            flexibleSpace: FlexibleSpaceBar(
              background: Stack(
                fit: StackFit.expand,
                children: [
                  // Image Carousel
                  PageView.builder(
                    controller: _imagePageController,
                    itemCount: images.length,
                    onPageChanged: (index) {
                      setState(() {
                        _currentImageIndex = index;
                      });
                    },
                    itemBuilder: (context, index) {
                      return Image.network(
                        images[index],
                        fit: BoxFit.cover,
                        loadingBuilder: (context, child, loadingProgress) {
                          if (loadingProgress == null) return child;
                          return Center(
                            child: CircularProgressIndicator(
                              value: loadingProgress.expectedTotalBytes != null
                                  ? loadingProgress.cumulativeBytesLoaded /
                                      loadingProgress.expectedTotalBytes!
                                  : null,
                            ),
                          );
                        },
                        errorBuilder: (context, error, stackTrace) {
                          return const Center(
                            child: Icon(
                              Icons.error_outline,
                              color: Colors.red,
                              size: 48,
                            ),
                          );
                        },
                      );
                    },
                  ),

                  // AR View button (only if experience has AR content)
                  if (experience.hasARContent)
                    Positioned(
                      bottom: 70,
                      right: 16,
                      child: ElevatedButton.icon(
                        onPressed: () {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => ARExperienceViewScreen(
                                experience: experience,
                              ),
                            ),
                          );
                        },
                        icon: const Icon(Icons.view_in_ar),
                        label: const Text('View in AR'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.black.withAlpha(180),
                          foregroundColor: Colors.white,
                        ),
                      ),
                    ),
                  // Gradient Overlay
                  Container(
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: [
                          Colors.transparent,
                          Colors.black.withAlpha(179), // 0.7 opacity
                        ],
                      ),
                    ),
                  ),
                  // Image Indicators
                  Positioned(
                    bottom: 16,
                    left: 0,
                    right: 0,
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: List.generate(
                        images.length,
                        (index) => Container(
                          width: 8,
                          height: 8,
                          margin: const EdgeInsets.symmetric(horizontal: 4),
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            color: _currentImageIndex == index
                                ? theme.colorScheme.primary
                                : Colors.white.withAlpha(128), // 0.5 opacity
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),

          // Content
          SliverToBoxAdapter(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Title and Rating
                  Row(
                    children: [
                      Expanded(
                        child: Text(
                          experience.title,
                          style: theme.textTheme.headlineSmall,
                        ),
                      ),
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          color: theme.colorScheme.primary,
                          borderRadius: BorderRadius.circular(16),
                        ),
                        child: Row(
                          children: [
                            const Icon(
                              Icons.star,
                              color: Colors.white,
                              size: 16,
                            ),
                            const SizedBox(width: 4),
                            Text(
                              '${experience.rating}',
                              style: theme.textTheme.bodyMedium?.copyWith(
                                color: Colors.white,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),

                  // Location
                  Row(
                    children: [
                      const Icon(
                        Icons.location_on,
                        size: 16,
                        color: Colors.grey,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        experience.location,
                        style: theme.textTheme.bodyMedium?.copyWith(
                          color: Colors.grey,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),

                  // Action Buttons
                  Row(
                    children: [
                      Expanded(
                        child: WishlistButton(
                          experienceId: experience.id,
                          experienceName: experience.title,
                          showLabel: true,
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: ShareExperienceButton(
                          experience: experience,
                          showLabel: true,
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: OutlinedButton.icon(
                          onPressed: () {
                            // Show report dialog
                            ReportDialog.show(
                              context: context,
                              entityId: experience.id,
                              reportType: ReportType.experience,
                              entityTitle: experience.title,
                              onReportSubmitted: (report) {
                                ScaffoldMessenger.of(context).showSnackBar(
                                  const SnackBar(
                                    content:
                                        Text('Report submitted successfully'),
                                    backgroundColor: Colors.green,
                                  ),
                                );
                              },
                            );
                          },
                          icon: const Icon(Icons.flag, color: Colors.red),
                          label: const Text('Report',
                              style: TextStyle(color: Colors.red)),
                          style: OutlinedButton.styleFrom(
                            side: const BorderSide(color: Colors.red),
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 24),

                  // Guide Info
                  Row(
                    children: [
                      CircleAvatar(
                        backgroundImage: NetworkImage(experience.guideImageUrl),
                        radius: 24,
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Hosted by ${experience.guideName}',
                              style: theme.textTheme.titleMedium,
                            ),
                            Row(
                              children: [
                                const Icon(
                                  Icons.verified,
                                  size: 16,
                                  color: Colors.blue,
                                ),
                                const SizedBox(width: 4),
                                Text(
                                  'Verified Guide',
                                  style: theme.textTheme.bodyMedium?.copyWith(
                                    color: Colors.grey,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                      ContactGuideButton(
                        guide: _getMockGuide(experience),
                        experienceId: experience.id,
                        experienceName: experience.title,
                        showLabel: false,
                      ),
                      TextButton(
                        onPressed: () {
                          // Show guide profile dialog
                          showDialog(
                            context: context,
                            builder: (context) => AlertDialog(
                              title: Text('${experience.guideName}\'s Profile'),
                              content: SingleChildScrollView(
                                child: Column(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Stack(
                                      alignment: Alignment.bottomRight,
                                      children: [
                                        CircleAvatar(
                                          radius: 50,
                                          backgroundImage: NetworkImage(
                                              experience.guideImageUrl),
                                        ),
                                        Container(
                                          padding: const EdgeInsets.all(4),
                                          decoration: const BoxDecoration(
                                            color: Colors.blue,
                                            shape: BoxShape.circle,
                                          ),
                                          child: const Icon(
                                            Icons.verified,
                                            color: Colors.white,
                                            size: 20,
                                          ),
                                        ),
                                      ],
                                    ),
                                    const SizedBox(height: 16),
                                    Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.center,
                                      children: [
                                        const Icon(Icons.star,
                                            color: Colors.amber, size: 16),
                                        const SizedBox(width: 4),
                                        Text(
                                            '${experience.rating} (${experience.reviewCount} reviews)'),
                                      ],
                                    ),
                                    const SizedBox(height: 16),
                                    const Text(
                                      'Professional guide with expertise in cultural experiences. Fluent in multiple languages and passionate about sharing local culture with visitors.',
                                      textAlign: TextAlign.center,
                                    ),
                                    const SizedBox(height: 16),

                                    // Verification badges
                                    const Text(
                                      'Verifications',
                                      style: TextStyle(
                                        fontWeight: FontWeight.bold,
                                        fontSize: 16,
                                      ),
                                    ),
                                    const SizedBox(height: 8),
                                    Wrap(
                                      spacing: 8,
                                      runSpacing: 8,
                                      alignment: WrapAlignment.center,
                                      children: [
                                        _buildVerificationChip(
                                          'Identity Verified',
                                          Icons.badge,
                                          Colors.green,
                                        ),
                                        _buildVerificationChip(
                                          'Background Checked',
                                          Icons.security,
                                          Colors.blue,
                                        ),
                                        _buildVerificationChip(
                                          'Licensed Guide',
                                          Icons.card_membership,
                                          Colors.purple,
                                        ),
                                      ],
                                    ),
                                    const SizedBox(height: 16),

                                    // Specialties
                                    const Text(
                                      'Specialties',
                                      style: TextStyle(
                                        fontWeight: FontWeight.bold,
                                        fontSize: 16,
                                      ),
                                    ),
                                    const SizedBox(height: 8),
                                    Wrap(
                                      spacing: 8,
                                      alignment: WrapAlignment.center,
                                      children: [
                                        Chip(label: Text(experience.category)),
                                        const Chip(
                                            label: Text('Cultural Tours')),
                                        const Chip(
                                            label: Text('Local Cuisine')),
                                      ],
                                    ),

                                    // Trust score
                                    const SizedBox(height: 16),
                                    Container(
                                      padding: const EdgeInsets.all(12),
                                      decoration: BoxDecoration(
                                        color: Colors.green.withOpacity(0.1),
                                        borderRadius: BorderRadius.circular(8),
                                        border: Border.all(
                                          color: Colors.green.withOpacity(0.3),
                                        ),
                                      ),
                                      child: Row(
                                        mainAxisSize: MainAxisSize.min,
                                        children: [
                                          Container(
                                            padding: const EdgeInsets.all(8),
                                            decoration: const BoxDecoration(
                                              color: Colors.green,
                                              shape: BoxShape.circle,
                                            ),
                                            child: const Text(
                                              '92',
                                              style: TextStyle(
                                                color: Colors.white,
                                                fontWeight: FontWeight.bold,
                                              ),
                                            ),
                                          ),
                                          const SizedBox(width: 12),
                                          const Column(
                                            crossAxisAlignment:
                                                CrossAxisAlignment.start,
                                            children: [
                                              Text(
                                                'Exceptional',
                                                style: TextStyle(
                                                  fontWeight: FontWeight.bold,
                                                  color: Colors.green,
                                                ),
                                              ),
                                              Text(
                                                'Trust Score',
                                                style: TextStyle(
                                                  color: Colors.green,
                                                  fontSize: 12,
                                                ),
                                              ),
                                            ],
                                          ),
                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              actions: [
                                // Report button
                                TextButton.icon(
                                  onPressed: () {
                                    Navigator.pop(context);
                                    // Show report dialog
                                    ReportDialog.show(
                                      context: context,
                                      entityId: _getMockGuide(experience).id,
                                      reportType: ReportType.guide,
                                      entityTitle: experience.guideName,
                                      onReportSubmitted: (report) {
                                        ScaffoldMessenger.of(context)
                                            .showSnackBar(
                                          const SnackBar(
                                            content: Text(
                                                'Report submitted successfully'),
                                            backgroundColor: Colors.green,
                                          ),
                                        );
                                      },
                                    );
                                  },
                                  icon:
                                      const Icon(Icons.flag, color: Colors.red),
                                  label: const Text('Report',
                                      style: TextStyle(color: Colors.red)),
                                ),
                                TextButton(
                                  onPressed: () => Navigator.pop(context),
                                  child: const Text('Close'),
                                ),
                                ElevatedButton(
                                  onPressed: () {
                                    Navigator.pop(context);
                                    // Show contact guide dialog
                                    showDialog(
                                      context: context,
                                      builder: (context) => ContactGuideDialog(
                                        guide: _getMockGuide(experience),
                                        experienceId: experience.id,
                                        experienceName: experience.title,
                                      ),
                                    );
                                  },
                                  child: const Text('Contact'),
                                ),
                              ],
                            ),
                          );
                        },
                        child: const Text('View Profile'),
                      ),
                    ],
                  ),
                  const SizedBox(height: 24),

                  // Weather and Local Time
                  WeatherInfoCard(
                    coordinates: experience.coordinates,
                    showForecast: false,
                    showLocalTime: true,
                  ),
                  const SizedBox(height: 24),

                  // Description
                  Text(
                    'About this experience',
                    style: theme.textTheme.titleLarge,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    experience.description,
                    style: theme.textTheme.bodyLarge,
                  ),
                  const SizedBox(height: 24),

                  // Booking Calendar
                  Text(
                    'Select Date and Time',
                    style: theme.textTheme.titleLarge,
                  ),
                  const SizedBox(height: 8),
                  BookingCalendar(
                    experience: experience,
                    onBookingSelected: (date, timeSlot, participants) {
                      setState(() {
                        _selectedDate = date;
                        _selectedTimeSlot = timeSlot;
                        _selectedParticipants = participants;
                        _calculatedPrice = timeSlot.price * participants;
                      });
                    },
                  ),

                  // Price Breakdown (only shown when a time slot is selected)
                  if (_selectedTimeSlot != null) ...[
                    const SizedBox(height: 16),
                    Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: theme.colorScheme.surfaceContainerHighest,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Price Breakdown',
                            style: theme.textTheme.titleMedium?.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 12),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              const Text('Base price per person:'),
                              Text(
                                  '\$${_selectedTimeSlot!.price.toStringAsFixed(2)}'),
                            ],
                          ),
                          const SizedBox(height: 8),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              const Text('Number of participants:'),
                              Text('$_selectedParticipants'),
                            ],
                          ),
                          const Divider(height: 24),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              const Text(
                                'Total:',
                                style: TextStyle(fontWeight: FontWeight.bold),
                              ),
                              Text(
                                '\$${_calculatedPrice.toStringAsFixed(2)}',
                                style: const TextStyle(
                                    fontWeight: FontWeight.bold),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ],
                  const SizedBox(height: 24),

                  // What's Included
                  Text(
                    'What\'s included',
                    style: theme.textTheme.titleLarge,
                  ),
                  const SizedBox(height: 8),
                  ...experience.includedItems.map(
                    (item) => Padding(
                      padding: const EdgeInsets.only(bottom: 8),
                      child: Row(
                        children: [
                          const Icon(
                            Icons.check_circle,
                            color: Colors.green,
                            size: 20,
                          ),
                          const SizedBox(width: 8),
                          Expanded(child: Text(item)),
                        ],
                      ),
                    ),
                  ),
                  const SizedBox(height: 24),

                  // Requirements
                  Text(
                    'Requirements',
                    style: theme.textTheme.titleLarge,
                  ),
                  const SizedBox(height: 8),
                  ...experience.requirements.map(
                    (requirement) => Padding(
                      padding: const EdgeInsets.only(bottom: 8),
                      child: Row(
                        children: [
                          const Icon(
                            Icons.info_outline,
                            color: Colors.blue,
                            size: 20,
                          ),
                          const SizedBox(width: 8),
                          Expanded(child: Text(requirement)),
                        ],
                      ),
                    ),
                  ),
                  const SizedBox(height: 24),

                  // Safety Information
                  Text(
                    'Safety Information',
                    style: theme.textTheme.titleLarge,
                  ),
                  const SizedBox(height: 8),
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.blue.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: Colors.blue.withOpacity(0.3),
                      ),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            const Icon(
                              Icons.health_and_safety,
                              color: Colors.blue,
                              size: 24,
                            ),
                            const SizedBox(width: 8),
                            Text(
                              'Your Safety is Our Priority',
                              style: theme.textTheme.titleMedium?.copyWith(
                                color: Colors.blue,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 12),
                        _buildSafetyItem(
                          'Verified Guide',
                          'This guide has completed our identity verification process and background check.',
                          Icons.verified_user,
                        ),
                        _buildSafetyItem(
                          'Emergency Support',
                          '24/7 emergency support available through the app.',
                          Icons.emergency,
                        ),
                        _buildSafetyItem(
                          'COVID-19 Precautions',
                          'This experience follows local COVID-19 guidelines and safety protocols.',
                          Icons.coronavirus,
                        ),
                        _buildSafetyItem(
                          'Insurance Coverage',
                          'This experience includes basic insurance coverage for participants.',
                          Icons.health_and_safety,
                        ),
                        const SizedBox(height: 8),
                        OutlinedButton.icon(
                          onPressed: () {
                            // Show safety details dialog
                            showDialog(
                              context: context,
                              builder: (context) => AlertDialog(
                                title: const Text('Safety Details'),
                                content: const SingleChildScrollView(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      Text(
                                        'Emergency Contacts',
                                        style: TextStyle(
                                          fontWeight: FontWeight.bold,
                                          fontSize: 16,
                                        ),
                                      ),
                                      SizedBox(height: 8),
                                      Text('Local Emergency: 911'),
                                      Text('Tourist Police: +************'),
                                      Text(
                                          'CultureConnect Support: +1 ************'),
                                      SizedBox(height: 16),
                                      Text(
                                        'Health & Safety Measures',
                                        style: TextStyle(
                                          fontWeight: FontWeight.bold,
                                          fontSize: 16,
                                        ),
                                      ),
                                      SizedBox(height: 8),
                                      Text(
                                          '• Regular sanitization of equipment'),
                                      Text('• First aid kit available'),
                                      Text(
                                          '• Guide trained in emergency procedures'),
                                      Text(
                                          '• Adherence to local health guidelines'),
                                      SizedBox(height: 16),
                                      Text(
                                        'Insurance Coverage',
                                        style: TextStyle(
                                          fontWeight: FontWeight.bold,
                                          fontSize: 16,
                                        ),
                                      ),
                                      SizedBox(height: 8),
                                      Text(
                                          'This experience includes basic insurance coverage for participants. The policy covers:'),
                                      SizedBox(height: 8),
                                      Text(
                                          '• Emergency medical expenses up to \$10,000'),
                                      Text(
                                          '• Accidental injuries during the experience'),
                                      Text('• Emergency evacuation if needed'),
                                      SizedBox(height: 8),
                                      Text(
                                          'For full insurance details, please contact our support team.'),
                                    ],
                                  ),
                                ),
                                actions: [
                                  TextButton(
                                    onPressed: () => Navigator.pop(context),
                                    child: const Text('Close'),
                                  ),
                                ],
                              ),
                            );
                          },
                          icon: const Icon(Icons.info_outline),
                          label: const Text('View Safety Details'),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 24),

                  // Languages
                  Text(
                    'Languages',
                    style: theme.textTheme.titleLarge,
                  ),
                  const SizedBox(height: 8),
                  Wrap(
                    spacing: 8,
                    runSpacing: 8,
                    children: experience.languages
                        .map(
                          (language) => Chip(
                            label: Text(language),
                            backgroundColor:
                                theme.colorScheme.surfaceContainerHighest,
                          ),
                        )
                        .toList(),
                  ),
                  const SizedBox(height: 24),

                  // Location Map
                  Text(
                    'Location',
                    style: theme.textTheme.titleLarge,
                  ),
                  const SizedBox(height: 8),
                  Container(
                    height: 200,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(color: Colors.grey.shade300),
                    ),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(12),
                      child: GoogleMap(
                        initialCameraPosition: CameraPosition(
                          target: experience.coordinates,
                          zoom: 15,
                        ),
                        markers: {
                          Marker(
                            markerId: MarkerId(experience.id),
                            position: experience.coordinates,
                            infoWindow: InfoWindow(
                              title: experience.title,
                              snippet: experience.location,
                            ),
                          ),
                        },
                        zoomControlsEnabled: false,
                        mapToolbarEnabled: false,
                        myLocationEnabled: false,
                      ),
                    ),
                  ),
                  const SizedBox(height: 24),

                  // Reviews
                  Padding(
                    padding: EdgeInsets.symmetric(vertical: 8.h),
                    child: ReviewList(
                      experience: experience,
                      maxReviews: 3,
                      showSeeAllButton: true,
                      showWriteReviewButton: true,
                      showRatingDistribution: true,
                      showSortOptions: true,
                    ),
                  ),

                  const SizedBox(height: 24),

                  // Similar Experiences
                  SimilarExperiences(
                    experienceId: experience.id,
                    category: experience.category,
                    onExperienceTap: (similarExperience) {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => ExperienceDetailsScreen(
                            experience: similarExperience,
                          ),
                        ),
                      );
                    },
                  ),

                  const SizedBox(height: 100), // Space for bottom sheet
                ],
              ),
            ),
          ),
        ],
      ),
      bottomSheet: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: theme.colorScheme.surface,
          boxShadow: [
            BoxShadow(
              color: Colors.black.withAlpha(26), // 0.1 opacity
              blurRadius: 8,
              offset: const Offset(0, -2),
            ),
          ],
        ),
        child: SafeArea(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Price and Participants Info
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        _selectedTimeSlot != null
                            ? '\$${_calculatedPrice.toStringAsFixed(2)}'
                            : '\$${(experience.price * _selectedParticipants).toStringAsFixed(2)}',
                        style: theme.textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        '$_selectedParticipants ${_selectedParticipants == 1 ? 'person' : 'people'}',
                        style: theme.textTheme.bodyMedium?.copyWith(
                          color: Colors.grey,
                        ),
                      ),
                    ],
                  ),
                  if (_selectedDate != null)
                    Chip(
                      label: Text(
                        '${_selectedDate!.day}/${_selectedDate!.month}/${_selectedDate!.year}',
                      ),
                      avatar: const Icon(Icons.calendar_today, size: 16),
                      backgroundColor:
                          theme.colorScheme.surfaceContainerHighest,
                    ),
                ],
              ),
              const SizedBox(height: 16),
              // Book Button
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: _selectedDate == null
                      ? null
                      : () async {
                          // Create a booking
                          final bookingService = BookingService();

                          // Create a time slot for the booking
                          final startDateTime = DateTime(
                            _selectedDate!.year,
                            _selectedDate!.month,
                            _selectedDate!.day,
                            10, // 10 AM
                            0,
                          );
                          final endDateTime = DateTime(
                            _selectedDate!.year,
                            _selectedDate!.month,
                            _selectedDate!.day,
                            12, // 12 PM
                            0,
                          );

                          final timeSlot = booking_model.TimeSlot(
                            startTime: startDateTime,
                            endTime: endDateTime,
                          );

                          // Create the booking
                          final booking = await bookingService.createBooking(
                            experience: experience,
                            date: _selectedDate!,
                            timeSlot: timeSlot,
                            participantCount: _selectedParticipants,
                            specialRequirements: '',
                          );

                          if (mounted) {
                            // Navigate to payment confirmation screen
                            final result = await Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (context) => PaymentConfirmationScreen(
                                  experience: experience,
                                  booking: booking,
                                ),
                              ),
                            );

                            // Handle payment result
                            if (result == true && mounted) {
                              // Payment successful
                              ScaffoldMessenger.of(context).showSnackBar(
                                const SnackBar(
                                  content: Text(
                                    'Booking confirmed! Check your email for details.',
                                  ),
                                  backgroundColor: Colors.green,
                                ),
                              );
                            }
                          }
                        },
                  style: ElevatedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(vertical: 16),
                  ),
                  child: Text(
                    _selectedDate == null ? 'Select a Date' : 'Book Now',
                    style: theme.textTheme.titleMedium?.copyWith(
                      color: Colors.white,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
