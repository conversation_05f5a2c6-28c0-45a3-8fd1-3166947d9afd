import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:culture_connect/theme/app_theme.dart';

class ProfileScreenPlaceholder extends StatelessWidget {
  const ProfileScreenPlaceholder({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Profile'),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircleAvatar(
              radius: 60.r,
              backgroundColor: AppTheme.primaryColor.withAlpha(50),
              child: Icon(
                Icons.person,
                size: 80.r,
                color: AppTheme.primaryColor,
              ),
            ),
            Sized<PERSON><PERSON>(height: 24.h),
            Text(
              'Default Profile Image',
              style: TextStyle(
                fontSize: 20.sp,
                fontWeight: FontWeight.bold,
                color: AppTheme.textPrimaryColor,
              ),
            ),
            <PERSON><PERSON><PERSON><PERSON>(height: 8.h),
            Text(
              'This is a placeholder for the profile image',
              style: TextStyle(
                fontSize: 16.sp,
                color: AppTheme.textSecondaryColor,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
