import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:culture_connect/models/translation/language_model.dart';
import 'package:culture_connect/providers/rtl_provider.dart';
import 'package:culture_connect/providers/voice_translation_provider.dart';
import 'package:culture_connect/theme/app_theme.dart';
import 'package:culture_connect/utils/rtl_utils.dart';
import 'package:culture_connect/widgets/custom_app_bar.dart';

/// A screen for configuring RTL (Right-to-Left) language settings
class RTLSettingsScreen extends ConsumerWidget {
  /// Creates a new RTL settings screen
  const RTLSettingsScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final useRTLOverride = ref.watch(useRTLOverrideProvider);
    final rtlOverrideDirection = ref.watch(rtlOverrideDirectionProvider);
    final autoDetectRTL = ref.watch(autoDetectRTLProvider);
    final sourceLanguage = ref.watch(sourceLanguageProvider);
    final targetLanguage = ref.watch(targetLanguageProvider);
    
    final isSourceRTL = RTLUtils.isRTL(sourceLanguage.code);
    final isTargetRTL = RTLUtils.isRTL(targetLanguage.code);
    
    return Directionality(
      textDirection: rtlOverrideDirection,
      child: Scaffold(
        appBar: CustomAppBar(
          title: 'RTL Settings',
          showBackButton: true,
          onBackPressed: () => Navigator.pop(context),
        ),
        body: SingleChildScrollView(
          padding: EdgeInsets.all(16.r),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Current language information
              _buildLanguageInfo(
                title: 'Current Source Language',
                language: sourceLanguage,
                isRTL: isSourceRTL,
              ),
              
              SizedBox(height: 16.h),
              
              _buildLanguageInfo(
                title: 'Current Target Language',
                language: targetLanguage,
                isRTL: isTargetRTL,
              ),
              
              SizedBox(height: 24.h),
              
              // RTL settings
              Text(
                'RTL Settings',
                style: TextStyle(
                  fontSize: 18.sp,
                  fontWeight: FontWeight.bold,
                  color: AppTheme.textPrimaryColor,
                ),
              ),
              
              SizedBox(height: 16.h),
              
              // Auto-detect RTL
              SwitchListTile(
                title: const Text('Auto-detect RTL Languages'),
                subtitle: const Text(
                  'Automatically detect and apply RTL layout for RTL languages',
                ),
                value: autoDetectRTL,
                onChanged: (value) {
                  ref.read(autoDetectRTLProvider.notifier).setValue(value);
                },
                activeColor: AppTheme.primaryColor,
              ),
              
              const Divider(),
              
              // Manual RTL override
              SwitchListTile(
                title: const Text('Manual RTL Override'),
                subtitle: const Text(
                  'Override the automatic RTL detection and use a fixed text direction',
                ),
                value: useRTLOverride,
                onChanged: (value) {
                  ref.read(useRTLOverrideProvider.notifier).setValue(value);
                },
                activeColor: AppTheme.primaryColor,
              ),
              
              // RTL direction selector (only visible if override is enabled)
              if (useRTLOverride) ...[
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
                  child: Row(
                    children: [
                      const Text('Text Direction:'),
                      const Spacer(),
                      SegmentedButton<TextDirection>(
                        segments: const [
                          ButtonSegment<TextDirection>(
                            value: TextDirection.ltr,
                            label: Text('LTR'),
                            icon: Icon(Icons.format_align_left),
                          ),
                          ButtonSegment<TextDirection>(
                            value: TextDirection.rtl,
                            label: Text('RTL'),
                            icon: Icon(Icons.format_align_right),
                          ),
                        ],
                        selected: {rtlOverrideDirection},
                        onSelectionChanged: (Set<TextDirection> selection) {
                          if (selection.isNotEmpty) {
                            ref
                                .read(rtlOverrideDirectionProvider.notifier)
                                .setValue(selection.first);
                          }
                        },
                      ),
                    ],
                  ),
                ),
              ],
              
              SizedBox(height: 24.h),
              
              // RTL language examples
              Text(
                'RTL Language Examples',
                style: TextStyle(
                  fontSize: 18.sp,
                  fontWeight: FontWeight.bold,
                  color: AppTheme.textPrimaryColor,
                ),
              ),
              
              SizedBox(height: 16.h),
              
              // Arabic example
              _buildRTLExample(
                languageName: 'Arabic',
                languageCode: 'ar',
                text: 'مرحبا بكم في تطبيق كالتشر كونكت',
                translation: 'Welcome to CultureConnect app',
              ),
              
              SizedBox(height: 16.h),
              
              // Hebrew example
              _buildRTLExample(
                languageName: 'Hebrew',
                languageCode: 'he',
                text: 'ברוכים הבאים לאפליקציית קאלצ׳ר קונקט',
                translation: 'Welcome to CultureConnect app',
              ),
              
              SizedBox(height: 16.h),
              
              // Persian example
              _buildRTLExample(
                languageName: 'Persian',
                languageCode: 'fa',
                text: 'به برنامه کالچر کانکت خوش آمدید',
                translation: 'Welcome to CultureConnect app',
              ),
              
              SizedBox(height: 24.h),
              
              // RTL information
              Card(
                child: Padding(
                  padding: EdgeInsets.all(16.r),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'About RTL Support',
                        style: TextStyle(
                          fontSize: 16.sp,
                          fontWeight: FontWeight.bold,
                          color: AppTheme.textPrimaryColor,
                        ),
                      ),
                      SizedBox(height: 8.h),
                      const Text(
                        'Right-to-Left (RTL) languages like Arabic, Hebrew, and Persian are written and read from right to left. CultureConnect provides comprehensive RTL support to ensure proper display of these languages.',
                      ),
                      SizedBox(height: 8.h),
                      const Text(
                        'When using RTL languages, the app will automatically adjust the layout, text alignment, and UI elements to provide a natural experience for RTL language users.',
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
  
  /// Build a widget displaying language information
  Widget _buildLanguageInfo({
    required String title,
    required LanguageModel language,
    required bool isRTL,
  }) {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16.r),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: TextStyle(
                fontSize: 16.sp,
                fontWeight: FontWeight.bold,
                color: AppTheme.textPrimaryColor,
              ),
            ),
            SizedBox(height: 8.h),
            Row(
              children: [
                Text(
                  language.flag,
                  style: TextStyle(
                    fontSize: 24.sp,
                  ),
                ),
                SizedBox(width: 8.w),
                Text(
                  language.name,
                  style: TextStyle(
                    fontSize: 16.sp,
                    color: AppTheme.textPrimaryColor,
                  ),
                ),
                const Spacer(),
                Chip(
                  label: Text(
                    isRTL ? 'RTL' : 'LTR',
                    style: TextStyle(
                      fontSize: 12.sp,
                      color: Colors.white,
                    ),
                  ),
                  backgroundColor: isRTL
                      ? AppTheme.secondaryColor
                      : AppTheme.primaryColor,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
  
  /// Build a widget displaying an RTL language example
  Widget _buildRTLExample({
    required String languageName,
    required String languageCode,
    required String text,
    required String translation,
  }) {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16.r),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Text(
                  languageName,
                  style: TextStyle(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.bold,
                    color: AppTheme.textPrimaryColor,
                  ),
                ),
                const Spacer(),
                Chip(
                  label: Text(
                    'RTL',
                    style: TextStyle(
                      fontSize: 12.sp,
                      color: Colors.white,
                    ),
                  ),
                  backgroundColor: AppTheme.secondaryColor,
                ),
              ],
            ),
            SizedBox(height: 8.h),
            Directionality(
              textDirection: TextDirection.rtl,
              child: Text(
                text,
                style: TextStyle(
                  fontSize: 16.sp,
                  color: AppTheme.textPrimaryColor,
                ),
                textAlign: TextAlign.right,
              ),
            ),
            SizedBox(height: 4.h),
            Text(
              translation,
              style: TextStyle(
                fontSize: 14.sp,
                fontStyle: FontStyle.italic,
                color: AppTheme.textSecondaryColor,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
