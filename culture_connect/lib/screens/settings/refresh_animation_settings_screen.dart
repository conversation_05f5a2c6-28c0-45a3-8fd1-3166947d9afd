import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:culture_connect/utils/refresh_animation_utils.dart';
import 'package:culture_connect/providers/refresh_animation_provider.dart';
import 'package:culture_connect/widgets/common/animated_refresh_indicator.dart';
import 'package:culture_connect/widgets/common/custom_app_bar.dart';
import 'package:culture_connect/theme/app_theme.dart';

/// A screen for selecting and previewing refresh animations
class RefreshAnimationSettingsScreen extends ConsumerStatefulWidget {
  /// Creates a new refresh animation settings screen
  const RefreshAnimationSettingsScreen({super.key});

  @override
  ConsumerState<RefreshAnimationSettingsScreen> createState() => _RefreshAnimationSettingsScreenState();
}

class _RefreshAnimationSettingsScreenState extends ConsumerState<RefreshAnimationSettingsScreen> {
  final List<String> _demoItems = List.generate(
    20,
    (index) => 'Item ${index + 1}',
  );
  
  bool _isRefreshing = false;
  
  Future<void> _handleRefresh() async {
    if (_isRefreshing) return;
    
    setState(() {
      _isRefreshing = true;
    });
    
    // Simulate a refresh
    await Future.delayed(const Duration(seconds: 2));
    
    setState(() {
      _isRefreshing = false;
    });
  }
  
  void _selectAnimationType(RefreshAnimationType type) async {
    // Provide haptic feedback
    await HapticFeedback.mediumImpact();
    
    // Update the animation type
    await ref.read(refreshAnimationTypeProvider.notifier).setAnimationType(type);
    
    // Show a snackbar
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('${RefreshAnimationUtils.getDisplayName(type)} animation selected'),
          duration: const Duration(seconds: 1),
        ),
      );
    }
  }
  
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final selectedAnimationType = ref.watch(refreshAnimationTypeProvider);
    
    return Scaffold(
      appBar: const CustomAppBar(
        title: 'Pull-to-Refresh Animations',
        showBackButton: true,
      ),
      body: Column(
        children: [
          // Animation preview
          Container(
            padding: EdgeInsets.all(16.r),
            color: theme.colorScheme.surfaceContainerLow,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Preview',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(height: 8.h),
                Text(
                  'Pull down to see the ${RefreshAnimationUtils.getDisplayName(selectedAnimationType).toLowerCase()} animation in action.',
                  style: theme.textTheme.bodyMedium,
                ),
                SizedBox(height: 8.h),
                Text(
                  'Current: ${RefreshAnimationUtils.getDisplayName(selectedAnimationType)}',
                  style: theme.textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: theme.colorScheme.primary,
                  ),
                ),
              ],
            ),
          ),
          
          // Demo list with refresh indicator
          Expanded(
            child: AnimatedRefreshIndicator(
              onRefresh: _handleRefresh,
              animationType: selectedAnimationType,
              color: theme.colorScheme.primary,
              backgroundColor: theme.colorScheme.surface,
              triggerHapticFeedback: true,
              child: ListView.builder(
                padding: EdgeInsets.all(16.r),
                itemCount: _demoItems.length,
                itemBuilder: (context, index) {
                  return Card(
                    margin: EdgeInsets.only(bottom: 8.h),
                    child: ListTile(
                      title: Text(_demoItems[index]),
                      subtitle: Text('Pull down to refresh'),
                      leading: Icon(
                        RefreshAnimationUtils.getIcon(selectedAnimationType),
                        color: theme.colorScheme.primary,
                      ),
                    ),
                  );
                },
              ),
            ),
          ),
          
          // Animation type selector
          Container(
            padding: EdgeInsets.all(16.r),
            decoration: BoxDecoration(
              color: theme.colorScheme.surface,
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.1),
                  blurRadius: 4,
                  offset: const Offset(0, -2),
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Select Animation Style',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(height: 16.h),
                Wrap(
                  spacing: 12.w,
                  runSpacing: 12.h,
                  children: RefreshAnimationType.values.map((type) {
                    final isSelected = type == selectedAnimationType;
                    
                    return InkWell(
                      onTap: () => _selectAnimationType(type),
                      borderRadius: BorderRadius.circular(12.r),
                      child: Container(
                        padding: EdgeInsets.all(12.r),
                        decoration: BoxDecoration(
                          color: isSelected
                              ? theme.colorScheme.primaryContainer
                              : theme.colorScheme.surfaceContainerLow,
                          borderRadius: BorderRadius.circular(12.r),
                          border: Border.all(
                            color: isSelected
                                ? theme.colorScheme.primary
                                : theme.colorScheme.outline,
                            width: isSelected ? 2 : 1,
                          ),
                        ),
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              RefreshAnimationUtils.getIcon(type),
                              color: isSelected
                                  ? theme.colorScheme.primary
                                  : theme.colorScheme.onSurfaceVariant,
                              size: 32.r,
                            ),
                            SizedBox(height: 8.h),
                            Text(
                              RefreshAnimationUtils.getDisplayName(type),
                              style: theme.textTheme.bodyMedium?.copyWith(
                                fontWeight: isSelected
                                    ? FontWeight.bold
                                    : FontWeight.normal,
                                color: isSelected
                                    ? theme.colorScheme.primary
                                    : theme.colorScheme.onSurfaceVariant,
                              ),
                            ),
                          ],
                        ),
                      ),
                    );
                  }).toList(),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
