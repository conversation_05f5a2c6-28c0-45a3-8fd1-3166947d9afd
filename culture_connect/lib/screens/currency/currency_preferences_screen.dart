import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:culture_connect/models/currency/currency_model.dart';
import 'package:culture_connect/models/currency/currency_preference_model.dart';
import 'package:culture_connect/providers/currency/currency_providers.dart';
import 'package:culture_connect/widgets/currency/currency_selection_dropdown.dart';
import 'package:culture_connect/widgets/settings/settings_section.dart';
import 'package:culture_connect/widgets/settings/settings_switch_tile.dart';
import 'package:culture_connect/widgets/settings/settings_header.dart';
import 'package:culture_connect/theme/app_theme.dart';

/// A screen for managing currency preferences
///
/// This screen allows users to:
/// - Set their preferred currency for displaying prices
/// - Configure general currency display settings
/// - Manage favorite currencies for quick access
/// - View and manage recently used currencies
/// - Works in both online and offline modes
class CurrencyPreferencesScreen extends ConsumerStatefulWidget {
  /// Creates a new currency preferences screen
  const CurrencyPreferencesScreen({super.key});

  @override
  ConsumerState<CurrencyPreferencesScreen> createState() =>
      _CurrencyPreferencesScreenState();
}

class _CurrencyPreferencesScreenState
    extends ConsumerState<CurrencyPreferencesScreen> {
  CurrencyPreferenceModel? _preferences;
  bool _isLoading = false;
  bool _hasChanges = false;
  bool _isOffline = false;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _loadPreferences();
    _checkConnectivity();
  }

  /// Load the current currency preferences
  Future<void> _loadPreferences() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final preferences = ref.read(currentCurrencyPreferencesProvider);
      if (mounted) {
        setState(() {
          _preferences = preferences;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = 'Failed to load preferences: $e';
          _isLoading = false;
        });
      }
    }
  }

  /// Check the current connectivity status
  void _checkConnectivity() {
    try {
      // For simplicity, we'll just set a default value
      // In a real implementation, we would check the actual connectivity status
      setState(() {
        _isOffline = false;
      });
    } catch (e) {
      // Ignore connectivity check errors
    }
  }

  /// Save the preferences
  Future<void> _savePreferences() async {
    if (!_hasChanges) {
      // No changes to save
      return;
    }

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      await ref
          .read(currencyConversionServiceProvider)
          .updatePreferences(_preferences!);

      if (mounted) {
        setState(() {
          _isLoading = false;
          _hasChanges = false;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Preferences saved'),
            duration: Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
          _errorMessage = 'Failed to save preferences: $e';
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: $_errorMessage'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    }
  }

  /// Update preferences and mark as changed
  void _updatePreferences(CurrencyPreferenceModel newPreferences) {
    setState(() {
      _preferences = newPreferences;
      _hasChanges = true;
    });
  }

  @override
  Widget build(BuildContext context) {
    final allCurrencies = ref.watch(allCurrenciesProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Currency Preferences'),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
        actions: [
          // Offline indicator
          if (_isOffline)
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 8),
              child: Chip(
                label: const Text('Offline'),
                backgroundColor: Colors.amber.withAlpha(50),
                labelStyle: const TextStyle(
                  color: Colors.amber,
                  fontWeight: FontWeight.bold,
                ),
                avatar: const Icon(
                  Icons.cloud_off,
                  color: Colors.amber,
                  size: 16,
                ),
                padding: EdgeInsets.zero,
                visualDensity: VisualDensity.compact,
              ),
            ),
          if (_isLoading)
            const Padding(
              padding: EdgeInsets.all(16),
              child: SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  color: Colors.white,
                ),
              ),
            )
          else
            IconButton(
              icon: const Icon(Icons.save),
              onPressed: _hasChanges ? _savePreferences : null,
              tooltip: 'Save Preferences',
            ),
        ],
      ),
      body: _isLoading && _preferences == null
          ? const Center(child: CircularProgressIndicator())
          : _errorMessage != null && _preferences == null
              ? _buildErrorView()
              : _buildPreferencesView(allCurrencies),
    );
  }

  /// Build the error view when preferences can't be loaded
  Widget _buildErrorView() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              color: Colors.red,
              size: 64,
            ),
            const SizedBox(height: 16),
            const Text(
              'Failed to load currency preferences',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              _errorMessage ?? 'Unknown error',
              style: const TextStyle(
                fontSize: 14,
                color: Colors.red,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: _loadPreferences,
              icon: const Icon(Icons.refresh),
              label: const Text('Retry'),
            ),
          ],
        ),
      ),
    );
  }

  /// Build the main preferences view
  Widget _buildPreferencesView(List<CurrencyModel> allCurrencies) {
    // If preferences are null, show a loading indicator
    if (_preferences == null) {
      return const Center(child: CircularProgressIndicator());
    }

    // Use the non-null preferences
    final prefs = _preferences!;

    return ListView(
      padding: const EdgeInsets.all(16),
      children: [
        // Preferred currency
        const SettingsHeader(title: 'Preferred Currency'),
        Card(
          margin: EdgeInsets.zero,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Select your preferred currency for displaying prices',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey[600],
                  ),
                ),
                const SizedBox(height: 16),
                CurrencySelectionDropdown(
                  selectedCurrencyCode: prefs.preferredCurrency,
                  onCurrencySelected: (currencyCode) {
                    _updatePreferences(prefs.copyWith(
                      preferredCurrency: currencyCode,
                    ));
                  },
                  showName: true,
                  width: double.infinity,
                ),
              ],
            ),
          ),
        ),

        const SizedBox(height: 16),

        // General settings
        const SettingsHeader(title: 'General Settings'),
        SettingsSection(
          children: [
            SettingsSwitchTile(
              title: 'Auto-detect currency',
              subtitle:
                  'Automatically detect your local currency based on your location',
              value: prefs.autoDetectCurrency,
              onChanged: (value) {
                _updatePreferences(prefs.copyWith(
                  autoDetectCurrency: value,
                ));
              },
            ),
            SettingsSwitchTile(
              title: 'Show original price',
              subtitle: 'Show the original price alongside the converted price',
              value: prefs.showOriginalPrice,
              onChanged: (value) {
                _updatePreferences(prefs.copyWith(
                  showOriginalPrice: value,
                ));
              },
            ),
            SettingsSwitchTile(
              title: 'Show exchange rate',
              subtitle: 'Show the exchange rate used for conversion',
              value: prefs.showExchangeRate,
              onChanged: (value) {
                _updatePreferences(prefs.copyWith(
                  showExchangeRate: value,
                ));
              },
            ),
            SettingsSwitchTile(
              title: 'Show last updated',
              subtitle: 'Show when the exchange rate was last updated',
              value: prefs.showLastUpdated,
              onChanged: (value) {
                _updatePreferences(prefs.copyWith(
                  showLastUpdated: value,
                ));
              },
            ),
          ],
        ),

        const SizedBox(height: 16),

        // Favorite currencies
        const SettingsHeader(title: 'Favorite Currencies'),
        Card(
          margin: EdgeInsets.zero,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Select your favorite currencies for quick access',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey[600],
                  ),
                ),
                const SizedBox(height: 16),
                Wrap(
                  spacing: 8,
                  runSpacing: 8,
                  children: allCurrencies.map((currency) {
                    final isFavorite =
                        prefs.favoriteCurrencies.contains(currency.code);
                    return FilterChip(
                      label: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(currency.flag),
                          const SizedBox(width: 4),
                          Text(currency.code),
                        ],
                      ),
                      selected: isFavorite,
                      onSelected: (selected) {
                        List<String> updatedFavorites;
                        if (selected) {
                          updatedFavorites =
                              List<String>.from(prefs.favoriteCurrencies)
                                ..add(currency.code);
                        } else {
                          updatedFavorites =
                              List<String>.from(prefs.favoriteCurrencies)
                                ..remove(currency.code);
                        }
                        _updatePreferences(prefs.copyWith(
                          favoriteCurrencies: updatedFavorites,
                        ));
                      },
                      selectedColor: AppTheme.primaryColor
                          .withAlpha(51), // 0.2 opacity (51/255)
                      checkmarkColor: AppTheme.primaryColor,
                    );
                  }).toList(),
                ),
              ],
            ),
          ),
        ),

        const SizedBox(height: 16),

        // Recently used currencies
        const SettingsHeader(title: 'Recently Used Currencies'),
        Card(
          margin: EdgeInsets.zero,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Your recently used currencies',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey[600],
                  ),
                ),
                const SizedBox(height: 16),
                if (prefs.recentlyUsedCurrencies.isEmpty)
                  Text(
                    'No recently used currencies',
                    style: TextStyle(
                      fontSize: 14,
                      fontStyle: FontStyle.italic,
                      color: Colors.grey[500],
                    ),
                  )
                else
                  Column(
                    children: prefs.recentlyUsedCurrencies.map((currencyCode) {
                      final currency = allCurrencies.firstWhere(
                        (c) => c.code == currencyCode,
                        orElse: () => CurrencyModel(
                          code: currencyCode,
                          name: currencyCode,
                          symbol: currencyCode,
                          flag: '🏳️',
                        ),
                      );
                      return ListTile(
                        leading: Text(
                          currency.flag,
                          style: const TextStyle(fontSize: 20),
                        ),
                        title: Text(currency.code),
                        subtitle: Text(currency.name),
                        trailing: IconButton(
                          icon: const Icon(Icons.delete),
                          onPressed: () {
                            final updatedRecent =
                                List<String>.from(prefs.recentlyUsedCurrencies)
                                  ..remove(currencyCode);
                            _updatePreferences(prefs.copyWith(
                              recentlyUsedCurrencies: updatedRecent,
                            ));
                          },
                          tooltip: 'Remove from recently used',
                        ),
                      );
                    }).toList(),
                  ),
                const SizedBox(height: 8),
                if (prefs.recentlyUsedCurrencies.isNotEmpty)
                  Center(
                    child: TextButton.icon(
                      icon: const Icon(Icons.delete_sweep),
                      label: const Text('Clear All'),
                      onPressed: () {
                        _updatePreferences(prefs.copyWith(
                          recentlyUsedCurrencies: [],
                        ));
                      },
                    ),
                  ),
              ],
            ),
          ),
        ),

        // Save button at the bottom for easier access
        if (_hasChanges) ...[
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: _savePreferences,
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.primaryColor,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 12),
            ),
            child: _isLoading
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      color: Colors.white,
                    ),
                  )
                : const Text('SAVE CHANGES'),
          ),
        ],

        // Bottom padding for better scrolling
        const SizedBox(height: 24),
      ],
    );
  }
}
