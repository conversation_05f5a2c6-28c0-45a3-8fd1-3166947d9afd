// External package imports
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

// Project imports
import 'package:culture_connect/models/safety_model.dart';
import 'package:culture_connect/providers/auth_provider.dart';
import 'package:culture_connect/services/safety_service.dart';
import 'package:culture_connect/services/logging_service.dart';
import 'package:culture_connect/theme/app_theme.dart';
import 'package:culture_connect/widgets/common/custom_app_bar.dart';
import 'package:culture_connect/widgets/common/app_button.dart';
import 'package:culture_connect/screens/safety/emergency_contacts_screen.dart';
import 'package:culture_connect/screens/safety/safety_tips_screen.dart';
import 'package:culture_connect/screens/safety/safe_zones_screen.dart';

class SafetyCenterScreen extends ConsumerStatefulWidget {
  const SafetyCenterScreen({super.key});

  @override
  ConsumerState<SafetyCenterScreen> createState() => _SafetyCenterScreenState();
}

class _SafetyCenterScreenState extends ConsumerState<SafetyCenterScreen> {
  bool _isLoading = false;
  bool _isSosActive = false;
  String? _activeAlertId;

  @override
  void initState() {
    super.initState();
    _checkActiveAlerts();
  }

  /// Checks for any active SOS alerts when the screen loads
  Future<void> _checkActiveAlerts() async {
    try {
      final alerts = await ref.read(safetyServiceProvider).getUserAlerts();
      final activeAlerts = alerts
          .where((alert) =>
              alert.type == SafetyAlertType.sos &&
              alert.status == SafetyAlertStatus.active)
          .toList();

      if (activeAlerts.isNotEmpty && mounted) {
        setState(() {
          _isSosActive = true;
          _activeAlertId = activeAlerts.first.id;
        });
      }

      LoggingService().info('SafetyCenterScreen',
          'Active alerts checked: ${activeAlerts.length} found');
    } catch (e) {
      LoggingService()
          .error('SafetyCenterScreen', 'Error checking active alerts', e);
    }
  }

  /// Triggers the SOS emergency alert system
  Future<void> _triggerSos() async {
    if (_isSosActive || !mounted) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final alert = await ref.read(safetyServiceProvider).createSosAlert();

      if (mounted) {
        setState(() {
          _isSosActive = true;
          _activeAlertId = alert.id;
          _isLoading = false;
        });

        // Show confirmation
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('SOS alert sent to your emergency contacts'),
            backgroundColor: Colors.red,
            duration: Duration(seconds: 5),
          ),
        );
      }

      LoggingService()
          .info('SafetyCenterScreen', 'SOS alert triggered successfully');
    } catch (e) {
      LoggingService()
          .error('SafetyCenterScreen', 'Failed to trigger SOS alert', e);

      if (mounted) {
        setState(() {
          _isLoading = false;
        });

        // Show error
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error sending SOS alert: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// Cancels the active SOS emergency alert
  Future<void> _cancelSos() async {
    if (!_isSosActive || _activeAlertId == null || !mounted) return;

    setState(() {
      _isLoading = true;
    });

    try {
      await ref.read(safetyServiceProvider).cancelAlert(_activeAlertId!);

      if (mounted) {
        setState(() {
          _isSosActive = false;
          _activeAlertId = null;
          _isLoading = false;
        });

        // Show confirmation
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('SOS alert cancelled'),
            backgroundColor: Colors.green,
          ),
        );
      }

      LoggingService()
          .info('SafetyCenterScreen', 'SOS alert cancelled successfully');
    } catch (e) {
      LoggingService()
          .error('SafetyCenterScreen', 'Failed to cancel SOS alert', e);

      if (mounted) {
        setState(() {
          _isLoading = false;
        });

        // Show error
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error cancelling SOS alert: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// Sends a check-in alert to emergency contacts
  Future<void> _checkIn() async {
    if (!mounted) return;

    setState(() {
      _isLoading = true;
    });

    try {
      await ref.read(safetyServiceProvider).createCheckInAlert();

      if (mounted) {
        setState(() {
          _isLoading = false;
        });

        // Show confirmation
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Check-in sent to your emergency contacts'),
            backgroundColor: Colors.green,
          ),
        );
      }

      LoggingService()
          .info('SafetyCenterScreen', 'Check-in alert sent successfully');
    } catch (e) {
      LoggingService()
          .error('SafetyCenterScreen', 'Failed to send check-in alert', e);

      if (mounted) {
        setState(() {
          _isLoading = false;
        });

        // Show error
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error sending check-in: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// Navigates to the Emergency Contacts screen
  void _navigateToEmergencyContacts() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const EmergencyContactsScreen(),
      ),
    );
  }

  /// Navigates to the Safe Zones screen
  void _navigateToSafeZones() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const SafeZonesScreen(),
      ),
    );
  }

  /// Navigates to the Safety Tips screen
  void _navigateToSafetyTips() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const SafetyTipsScreen(),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final userAsync = ref.watch(currentUserModelProvider);

    return Scaffold(
      appBar: const CustomAppBar(
        title: 'Safety Center',
        showBackButton: true,
      ),
      body: userAsync.when(
        data: (user) {
          if (user == null) {
            return const Center(child: Text('User not found'));
          }

          return SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // SOS Button
                _buildSosButton(),
                const SizedBox(height: 24),

                // Safety Features
                const Text(
                  'Safety Features',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: AppTheme.textPrimaryColor,
                  ),
                ),
                const SizedBox(height: 16),
                _buildSafetyFeatureGrid(),
                const SizedBox(height: 24),

                // Emergency Contacts
                const Text(
                  'Emergency Contacts',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: AppTheme.textPrimaryColor,
                  ),
                ),
                const SizedBox(height: 16),
                _buildEmergencyContactsList(),
                const SizedBox(height: 24),

                // Safety Tips
                const Text(
                  'Safety Tips',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: AppTheme.textPrimaryColor,
                  ),
                ),
                const SizedBox(height: 16),
                _buildSafetyTipsList(),
              ],
            ),
          );
        },
        loading: () => const Center(child: CircularProgressIndicator()),
        error: (error, stack) => Center(
          child: Text('Error loading user: $error'),
        ),
      ),
    );
  }

  /// Builds the main SOS emergency button widget
  Widget _buildSosButton() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: _isSosActive ? Colors.red : Colors.red.withAlpha(26),
        borderRadius: BorderRadius.circular(16),
        boxShadow: _isSosActive
            ? [
                BoxShadow(
                  color: Colors.red.withAlpha(77),
                  blurRadius: 10,
                  spreadRadius: 2,
                ),
              ]
            : null,
      ),
      child: Column(
        children: [
          Text(
            _isSosActive ? 'SOS ACTIVE' : 'Emergency SOS',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: _isSosActive ? Colors.white : Colors.red,
            ),
          ),
          const SizedBox(height: 16),
          Text(
            _isSosActive
                ? 'Your emergency contacts have been notified. Help is on the way.'
                : 'Press the button below in case of emergency. Your location will be shared with your emergency contacts.',
            textAlign: TextAlign.center,
            style: TextStyle(
              fontSize: 14,
              color: _isSosActive ? Colors.white : AppTheme.textSecondaryColor,
            ),
          ),
          const SizedBox(height: 24),
          AppButton(
            text: _isSosActive ? 'CANCEL SOS' : 'ACTIVATE SOS',
            onPressed: _isSosActive ? _cancelSos : _triggerSos,
            isLoading: _isLoading,
            variant:
                _isSosActive ? ButtonVariant.outlined : ButtonVariant.filled,
          ),
        ],
      ),
    );
  }

  /// Builds the grid of safety feature cards
  Widget _buildSafetyFeatureGrid() {
    return GridView.count(
      crossAxisCount: 2,
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      mainAxisSpacing: 16,
      crossAxisSpacing: 16,
      children: [
        _buildFeatureCard(
          icon: Icons.check_circle_outline,
          title: 'Check In',
          description: 'Let your contacts know you\'re safe',
          onTap: _checkIn,
        ),
        _buildFeatureCard(
          icon: Icons.location_on_outlined,
          title: 'Safe Zones',
          description: 'Find nearby safe meeting points',
          onTap: _navigateToSafeZones,
        ),
        _buildFeatureCard(
          icon: Icons.contacts_outlined,
          title: 'Emergency Contacts',
          description: 'Manage your emergency contacts',
          onTap: _navigateToEmergencyContacts,
        ),
        _buildFeatureCard(
          icon: Icons.lightbulb_outline,
          title: 'Safety Tips',
          description: 'Learn how to stay safe while traveling',
          onTap: _navigateToSafetyTips,
        ),
      ],
    );
  }

  /// Builds an individual safety feature card
  Widget _buildFeatureCard({
    required IconData icon,
    required String title,
    required String description,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black
                  .withAlpha(13), // 0.05 * 255 = 12.75, rounded to 13
              blurRadius: 10,
              offset: const Offset(0, 5),
            ),
          ],
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              size: 40,
              color: AppTheme.primaryColor,
            ),
            const SizedBox(height: 12),
            Text(
              title,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: AppTheme.textPrimaryColor,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              description,
              textAlign: TextAlign.center,
              style: const TextStyle(
                fontSize: 12,
                color: AppTheme.textSecondaryColor,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Builds the emergency contacts list section
  Widget _buildEmergencyContactsList() {
    return Consumer(
      builder: (context, ref, child) {
        final contactsAsync = ref.watch(emergencyContactsProvider);

        return contactsAsync.when(
          data: (contacts) {
            if (contacts.isEmpty) {
              return _buildEmptyState(
                icon: Icons.contacts_outlined,
                title: 'No Emergency Contacts',
                description:
                    'Add emergency contacts to be notified in case of emergency',
                buttonText: 'Add Contacts',
                onPressed: _navigateToEmergencyContacts,
              );
            }

            return Column(
              children: [
                ...contacts
                    .take(3)
                    .map((contact) => _buildContactTile(contact)),
                if (contacts.length > 3)
                  TextButton(
                    onPressed: _navigateToEmergencyContacts,
                    child: Text(
                      'View All',
                      style: TextStyle(
                        fontSize: 14,
                        color: Theme.of(context).colorScheme.primary,
                      ),
                    ),
                  ),
              ],
            );
          },
          loading: () => const Center(child: CircularProgressIndicator()),
          error: (error, stack) => Center(
            child: Text('Error loading contacts: $error'),
          ),
        );
      },
    );
  }

  /// Builds an individual emergency contact tile
  Widget _buildContactTile(EmergencyContact contact) {
    return ListTile(
      leading: CircleAvatar(
        backgroundColor: Theme.of(context)
            .colorScheme
            .primary
            .withAlpha(26), // 0.1 * 255 = 25.5, rounded to 26
        child: Icon(
          Icons.person,
          color: Theme.of(context).colorScheme.primary,
        ),
      ),
      title: Text(
        contact.name,
        style: TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.bold,
          color: Theme.of(context).colorScheme.onSurface,
        ),
      ),
      subtitle: Text(
        contact.phoneNumber,
        style: TextStyle(
          fontSize: 14,
          color: Theme.of(context).colorScheme.onSurface.withAlpha(153),
        ),
      ),
      trailing: contact.isPrimary
          ? Container(
              padding: const EdgeInsets.symmetric(
                horizontal: 8,
                vertical: 4,
              ),
              decoration: BoxDecoration(
                color: AppTheme.primaryColor.withAlpha(26),
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Text(
                'Primary',
                style: TextStyle(
                  fontSize: 12,
                  color: AppTheme.primaryColor,
                  fontWeight: FontWeight.bold,
                ),
              ),
            )
          : null,
    );
  }

  /// Builds the safety tips list section
  Widget _buildSafetyTipsList() {
    // For demo purposes, we'll use hardcoded safety tips
    final tips = [
      SafetyTip(
        id: '1',
        title: 'Stay aware of your surroundings',
        content:
            'Always be mindful of your environment and the people around you.',
        category: 'general',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
      SafetyTip(
        id: '2',
        title: 'Share your itinerary',
        content: 'Let someone know your travel plans and check in regularly.',
        category: 'travel',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
    ];

    return Column(
      children: [
        ...tips.map((tip) => _buildTipTile(tip)),
        TextButton(
          onPressed: _navigateToSafetyTips,
          child: const Text(
            'View All Safety Tips',
            style: TextStyle(
              fontSize: 14,
              color: AppTheme.primaryColor,
            ),
          ),
        ),
      ],
    );
  }

  /// Builds an individual safety tip tile
  Widget _buildTipTile(SafetyTip tip) {
    return ListTile(
      leading: CircleAvatar(
        backgroundColor: AppTheme.primaryColor
            .withAlpha(26), // 0.1 * 255 = 25.5, rounded to 26
        child: const Icon(
          Icons.lightbulb_outline,
          color: AppTheme.primaryColor,
        ),
      ),
      title: Text(
        tip.title,
        style: const TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.bold,
          color: AppTheme.textPrimaryColor,
        ),
      ),
      subtitle: Text(
        tip.content,
        maxLines: 2,
        overflow: TextOverflow.ellipsis,
        style: const TextStyle(
          fontSize: 14,
          color: AppTheme.textSecondaryColor,
        ),
      ),
      onTap: _navigateToSafetyTips,
    );
  }

  /// Builds an empty state widget with icon, text, and action button
  Widget _buildEmptyState({
    required IconData icon,
    required String title,
    required String description,
    required String buttonText,
    required VoidCallback onPressed,
  }) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color:
                Colors.black.withAlpha(13), // 0.05 * 255 = 12.75, rounded to 13
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            icon,
            size: 48,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            title,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Theme.of(context).colorScheme.onSurface,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            description,
            textAlign: TextAlign.center,
            style: TextStyle(
              fontSize: 14,
              color: Theme.of(context).colorScheme.onSurface.withAlpha(153),
            ),
          ),
          const SizedBox(height: 16),
          AppButton(
            text: buttonText,
            onPressed: onPressed,
            variant: ButtonVariant.filled,
          ),
        ],
      ),
    );
  }
}
