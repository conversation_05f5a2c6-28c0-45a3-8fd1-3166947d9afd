// External package imports
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:url_launcher/url_launcher.dart';

// Project imports
import 'package:culture_connect/models/safety_model.dart';
import 'package:culture_connect/services/safety_service.dart';
import 'package:culture_connect/services/logging_service.dart';
import 'package:culture_connect/theme/app_theme.dart';
import 'package:culture_connect/widgets/common/custom_app_bar.dart';
import 'package:culture_connect/widgets/common/app_button.dart';

class EmergencyContactsScreen extends ConsumerStatefulWidget {
  const EmergencyContactsScreen({super.key});

  @override
  ConsumerState<EmergencyContactsScreen> createState() =>
      _EmergencyContactsScreenState();
}

class _EmergencyContactsScreenState
    extends ConsumerState<EmergencyContactsScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _phoneController = TextEditingController();
  final _emailController = TextEditingController();
  final _relationshipController = TextEditingController();
  final _notesController = TextEditingController();

  EmergencyContactType _selectedType = EmergencyContactType.personal;
  bool _isPrimary = false;
  bool _isEditing = false;
  String? _editingContactId;
  bool _isLoading = false;

  @override
  void dispose() {
    _nameController.dispose();
    _phoneController.dispose();
    _emailController.dispose();
    _relationshipController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  void _resetForm() {
    _nameController.clear();
    _phoneController.clear();
    _emailController.clear();
    _relationshipController.clear();
    _notesController.clear();
    setState(() {
      _selectedType = EmergencyContactType.personal;
      _isPrimary = false;
      _isEditing = false;
      _editingContactId = null;
    });
  }

  void _editContact(EmergencyContact contact) {
    _nameController.text = contact.name;
    _phoneController.text = contact.phoneNumber;
    _emailController.text = contact.email ?? '';
    _relationshipController.text = contact.relationship ?? '';
    _notesController.text = contact.notes ?? '';

    setState(() {
      _selectedType = contact.type;
      _isPrimary = contact.isPrimary;
      _isEditing = true;
      _editingContactId = contact.id;
    });

    _showAddContactBottomSheet();
  }

  /// Saves or updates an emergency contact
  Future<void> _saveContact() async {
    if (!_formKey.currentState!.validate()) return;

    if (!mounted) return;

    setState(() {
      _isLoading = true;
    });

    try {
      if (_isEditing && _editingContactId != null) {
        // Update existing contact
        final updatedContact = EmergencyContact(
          id: _editingContactId!,
          userId: '', // Will be set by the service
          name: _nameController.text,
          phoneNumber: _phoneController.text,
          email: _emailController.text.isEmpty ? null : _emailController.text,
          relationship: _relationshipController.text.isEmpty
              ? null
              : _relationshipController.text,
          type: _selectedType,
          notes: _notesController.text.isEmpty ? null : _notesController.text,
          isPrimary: _isPrimary,
          createdAt: DateTime.now(), // Will be preserved by the service
          updatedAt: DateTime.now(),
        );

        await ref
            .read(safetyServiceProvider)
            .updateEmergencyContact(updatedContact);
      } else {
        // Add new contact
        final newContact = EmergencyContact(
          id: '', // Will be generated by the service
          userId: '', // Will be set by the service
          name: _nameController.text,
          phoneNumber: _phoneController.text,
          email: _emailController.text.isEmpty ? null : _emailController.text,
          relationship: _relationshipController.text.isEmpty
              ? null
              : _relationshipController.text,
          type: _selectedType,
          notes: _notesController.text.isEmpty ? null : _notesController.text,
          isPrimary: _isPrimary,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        await ref.read(safetyServiceProvider).addEmergencyContact(newContact);
      }

      _resetForm();

      if (mounted) {
        Navigator.pop(context); // Close bottom sheet
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Emergency contact saved successfully'),
            backgroundColor: Colors.green,
          ),
        );
      }

      LoggingService().info('EmergencyContactsScreen',
          'Emergency contact ${_isEditing ? 'updated' : 'saved'} successfully');
    } catch (e) {
      LoggingService().error(
          'EmergencyContactsScreen', 'Error saving emergency contact', e);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error saving contact: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// Deletes an emergency contact
  Future<void> _deleteContact(String contactId) async {
    if (!mounted) return;

    setState(() {
      _isLoading = true;
    });

    try {
      await ref.read(safetyServiceProvider).deleteEmergencyContact(contactId);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Emergency contact deleted'),
            backgroundColor: Colors.green,
          ),
        );
      }

      LoggingService().info('EmergencyContactsScreen',
          'Emergency contact deleted successfully: $contactId');
    } catch (e) {
      LoggingService().error(
          'EmergencyContactsScreen', 'Error deleting emergency contact', e);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error deleting contact: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// Calls an emergency contact
  Future<void> _callContact(String phoneNumber) async {
    try {
      final Uri uri = Uri(scheme: 'tel', path: phoneNumber);
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri);
        LoggingService().info(
            'EmergencyContactsScreen', 'Phone call initiated to: $phoneNumber');
      } else {
        LoggingService().warning('EmergencyContactsScreen',
            'Could not launch phone app for: $phoneNumber');
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Could not launch phone app'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      LoggingService()
          .error('EmergencyContactsScreen', 'Error launching phone app', e);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error launching phone app: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _showAddContactBottomSheet() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        padding: EdgeInsets.only(
          bottom: MediaQuery.of(context).viewInsets.bottom,
        ),
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Form(
            key: _formKey,
            child: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        _isEditing ? 'Edit Contact' : 'Add Emergency Contact',
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: AppTheme.textPrimaryColor,
                        ),
                      ),
                      IconButton(
                        icon: const Icon(Icons.close),
                        onPressed: () => Navigator.pop(context),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),

                  // Name
                  TextFormField(
                    controller: _nameController,
                    decoration: const InputDecoration(
                      labelText: 'Name *',
                      prefixIcon: Icon(Icons.person_outline),
                    ),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Please enter a name';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 16),

                  // Phone
                  TextFormField(
                    controller: _phoneController,
                    decoration: const InputDecoration(
                      labelText: 'Phone Number *',
                      prefixIcon: Icon(Icons.phone_outlined),
                    ),
                    keyboardType: TextInputType.phone,
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Please enter a phone number';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 16),

                  // Email
                  TextFormField(
                    controller: _emailController,
                    decoration: const InputDecoration(
                      labelText: 'Email (Optional)',
                      prefixIcon: Icon(Icons.email_outlined),
                    ),
                    keyboardType: TextInputType.emailAddress,
                  ),
                  const SizedBox(height: 16),

                  // Relationship
                  TextFormField(
                    controller: _relationshipController,
                    decoration: const InputDecoration(
                      labelText: 'Relationship (Optional)',
                      prefixIcon: Icon(Icons.people_outline),
                    ),
                  ),
                  const SizedBox(height: 16),

                  // Contact Type
                  DropdownButtonFormField<EmergencyContactType>(
                    value: _selectedType,
                    decoration: const InputDecoration(
                      labelText: 'Contact Type',
                      prefixIcon: Icon(Icons.category_outlined),
                    ),
                    items: EmergencyContactType.values.map((type) {
                      return DropdownMenuItem<EmergencyContactType>(
                        value: type,
                        child: Text(_getContactTypeName(type)),
                      );
                    }).toList(),
                    onChanged: (value) {
                      if (value != null) {
                        setState(() {
                          _selectedType = value;
                        });
                      }
                    },
                  ),
                  const SizedBox(height: 16),

                  // Notes
                  TextFormField(
                    controller: _notesController,
                    decoration: const InputDecoration(
                      labelText: 'Notes (Optional)',
                      prefixIcon: Icon(Icons.note_outlined),
                    ),
                    maxLines: 3,
                  ),
                  const SizedBox(height: 16),

                  // Primary Contact
                  SwitchListTile(
                    title: const Text('Set as Primary Contact'),
                    subtitle: const Text(
                        'This contact will be notified first in emergencies'),
                    value: _isPrimary,
                    onChanged: (value) {
                      setState(() {
                        _isPrimary = value;
                      });
                    },
                  ),
                  const SizedBox(height: 24),

                  // Save Button
                  AppButton(
                    text: _isEditing ? 'Update Contact' : 'Save Contact',
                    onPressed: _saveContact,
                    isLoading: _isLoading,
                    variant: ButtonVariant.filled,
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  String _getContactTypeName(EmergencyContactType type) {
    switch (type) {
      case EmergencyContactType.personal:
        return 'Personal';
      case EmergencyContactType.local:
        return 'Local Contact';
      case EmergencyContactType.embassy:
        return 'Embassy';
      case EmergencyContactType.police:
        return 'Police';
      case EmergencyContactType.medical:
        return 'Medical';
      case EmergencyContactType.other:
        return 'Other';
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const CustomAppBar(
        title: 'Emergency Contacts',
        showBackButton: true,
      ),
      body: Consumer(
        builder: (context, ref, child) {
          final contactsAsync = ref.watch(emergencyContactsProvider);

          return contactsAsync.when(
            data: (contacts) {
              if (contacts.isEmpty) {
                return _buildEmptyState();
              }

              return ListView.builder(
                padding: const EdgeInsets.all(16),
                itemCount: contacts.length,
                itemBuilder: (context, index) {
                  final contact = contacts[index];
                  return _buildContactCard(contact);
                },
              );
            },
            loading: () => const Center(child: CircularProgressIndicator()),
            error: (error, stack) => Center(
              child: Text('Error loading contacts: $error'),
            ),
          );
        },
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          _resetForm();
          _showAddContactBottomSheet();
        },
        backgroundColor: AppTheme.primaryColor,
        child: const Icon(Icons.add, color: Colors.white),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.contacts_outlined,
              size: 80,
              color: Colors.grey[300],
            ),
            const SizedBox(height: 24),
            const Text(
              'No Emergency Contacts',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: AppTheme.textPrimaryColor,
              ),
            ),
            const SizedBox(height: 16),
            const Text(
              'Add emergency contacts to be notified in case of emergency. They will receive your location and alert message.',
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 16,
                color: AppTheme.textSecondaryColor,
              ),
            ),
            const SizedBox(height: 32),
            AppButton(
              text: 'Add Emergency Contact',
              onPressed: () {
                _resetForm();
                _showAddContactBottomSheet();
              },
              variant: ButtonVariant.filled,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildContactCard(EmergencyContact contact) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                CircleAvatar(
                  backgroundColor:
                      _getContactTypeColor(contact.type).withAlpha(26),
                  child: Icon(
                    _getContactTypeIcon(contact.type),
                    color: _getContactTypeColor(contact.type),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        contact.name,
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Theme.of(context).colorScheme.onSurface,
                        ),
                      ),
                      Text(
                        _getContactTypeName(contact.type),
                        style: TextStyle(
                          fontSize: 14,
                          color: Theme.of(context)
                              .colorScheme
                              .onSurface
                              .withAlpha(153),
                        ),
                      ),
                    ],
                  ),
                ),
                if (contact.isPrimary)
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: AppTheme.primaryColor.withAlpha(26),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Text(
                      'Primary',
                      style: TextStyle(
                        fontSize: 12,
                        color: AppTheme.primaryColor,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
              ],
            ),
            const SizedBox(height: 16),
            _buildContactInfoRow(
              icon: Icons.phone,
              text: contact.phoneNumber,
              onTap: () => _callContact(contact.phoneNumber),
            ),
            if (contact.email != null && contact.email!.isNotEmpty)
              _buildContactInfoRow(
                icon: Icons.email,
                text: contact.email!,
              ),
            if (contact.relationship != null &&
                contact.relationship!.isNotEmpty)
              _buildContactInfoRow(
                icon: Icons.people,
                text: contact.relationship!,
              ),
            if (contact.notes != null && contact.notes!.isNotEmpty) ...[
              const SizedBox(height: 8),
              const Text(
                'Notes:',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: AppTheme.textPrimaryColor,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                contact.notes!,
                style: const TextStyle(
                  fontSize: 14,
                  color: AppTheme.textSecondaryColor,
                ),
              ),
            ],
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton.icon(
                  onPressed: () => _editContact(contact),
                  icon: const Icon(Icons.edit),
                  label: const Text('Edit'),
                  style: TextButton.styleFrom(
                    foregroundColor: AppTheme.primaryColor,
                  ),
                ),
                const SizedBox(width: 8),
                TextButton.icon(
                  onPressed: () => _deleteContact(contact.id),
                  icon: const Icon(Icons.delete),
                  label: const Text('Delete'),
                  style: TextButton.styleFrom(
                    foregroundColor: Colors.red,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildContactInfoRow({
    required IconData icon,
    required String text,
    VoidCallback? onTap,
  }) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: InkWell(
        onTap: onTap,
        child: Row(
          children: [
            Icon(
              icon,
              size: 18,
              color: Theme.of(context).colorScheme.onSurface.withAlpha(153),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                text,
                style: TextStyle(
                  fontSize: 16,
                  color: onTap != null
                      ? Theme.of(context).colorScheme.primary
                      : Theme.of(context).colorScheme.onSurface,
                  decoration: onTap != null ? TextDecoration.underline : null,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  IconData _getContactTypeIcon(EmergencyContactType type) {
    switch (type) {
      case EmergencyContactType.personal:
        return Icons.person;
      case EmergencyContactType.local:
        return Icons.location_city;
      case EmergencyContactType.embassy:
        return Icons.account_balance;
      case EmergencyContactType.police:
        return Icons.local_police;
      case EmergencyContactType.medical:
        return Icons.local_hospital;
      case EmergencyContactType.other:
        return Icons.category;
    }
  }

  Color _getContactTypeColor(EmergencyContactType type) {
    switch (type) {
      case EmergencyContactType.personal:
        return Theme.of(context).colorScheme.primary;
      case EmergencyContactType.local:
        return Colors.green;
      case EmergencyContactType.embassy:
        return Colors.purple;
      case EmergencyContactType.police:
        return Colors.blue;
      case EmergencyContactType.medical:
        return Colors.red;
      case EmergencyContactType.other:
        return Colors.orange;
    }
  }
}
