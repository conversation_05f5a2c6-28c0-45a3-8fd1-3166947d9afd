import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:arcore_flutter_plugin/arcore_flutter_plugin.dart';
import 'package:vector_math/vector_math_64.dart' hide Colors;

import 'package:culture_connect/models/experience.dart';
import 'package:culture_connect/providers/ar_experience_provider.dart';
import 'package:culture_connect/theme/app_theme.dart';
import 'package:culture_connect/widgets/common/custom_app_bar.dart';
import 'package:culture_connect/widgets/common/loading_indicator.dart';

/// Screen for viewing an experience in AR
class ARExperienceViewScreen extends ConsumerStatefulWidget {
  /// The experience to view in AR
  final Experience experience;

  /// Creates a new AR experience view screen
  const ARExperienceViewScreen({
    super.key,
    required this.experience,
  });

  @override
  ConsumerState<ARExperienceViewScreen> createState() =>
      _ARExperienceViewScreenState();
}

class _ARExperienceViewScreenState extends ConsumerState<ARExperienceViewScreen>
    with WidgetsBindingObserver {
  ArCoreController? _arCoreController;
  bool _hasPlacedModel = false;
  bool _showPlacementInstructions = true;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    _initializeAR();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (state == AppLifecycleState.resumed) {
      _initializeAR();
    } else if (state == AppLifecycleState.paused) {
      _disposeAR();
    }
  }

  Future<void> _initializeAR() async {
    final arLoadingNotifier = ref.read(arExperienceLoadingProvider.notifier);
    await arLoadingNotifier.initialize();
    await arLoadingNotifier.loadExperience(widget.experience);
  }

  void _onArCoreViewCreated(ArCoreController controller) {
    _arCoreController = controller;

    final arService = ref.read(arExperienceServiceProvider);
    arService.onArCoreViewCreated(controller);

    // Set up plane tap handler
    _arCoreController!.onPlaneTap = _handlePlaneTap;
  }

  void _handlePlaneTap(List<ArCoreHitTestResult> hits) {
    if (hits.isEmpty || _hasPlacedModel) return;

    final hit = hits.first;
    final position = hit.pose.translation;

    _placeModel(Vector3(position.x, position.y, position.z));

    setState(() {
      _hasPlacedModel = true;
      _showPlacementInstructions = false;
    });
  }

  Future<void> _placeModel(Vector3 position) async {
    final arService = ref.read(arExperienceServiceProvider);
    await arService.placeARModel(widget.experience, position);

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('${widget.experience.title} placed in AR'),
          backgroundColor: Colors.green,
          duration: const Duration(seconds: 2),
        ),
      );
    }
  }

  void _resetPlacement() {
    if (_arCoreController == null) return;

    _arCoreController!
        .removeNode(nodeName: 'experience_${widget.experience.id}');

    setState(() {
      _hasPlacedModel = false;
      _showPlacementInstructions = true;
    });
  }

  void _disposeAR() {
    _arCoreController?.dispose();
    _arCoreController = null;
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _disposeAR();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final arLoadingState = ref.watch(arExperienceLoadingProvider);

    return Scaffold(
      extendBodyBehindAppBar: true,
      appBar: CustomAppBar(
        title: widget.experience.title,
        showBackButton: true,
        backgroundColor: Colors.black.withAlpha(100),
        foregroundColor: Colors.white,
      ),
      body: Stack(
        children: [
          // AR View
          if (arLoadingState.isInitialized && !arLoadingState.isLoading)
            ArCoreView(
              onArCoreViewCreated: _onArCoreViewCreated,
              enableTapRecognizer: true,
              enableUpdateListener: true,
            )
          else
            Container(
              color: Colors.black,
              child: Center(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const LoadingIndicator(),
                    const SizedBox(height: 16),
                    const Text(
                      'Loading AR Experience...',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      '${(arLoadingState.progress * 100).toInt()}%',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 14,
                      ),
                    ),
                  ],
                ),
              ),
            ),

          // Placement instructions
          if (_showPlacementInstructions && !arLoadingState.isLoading)
            Positioned(
              top: 100,
              left: 0,
              right: 0,
              child: Container(
                margin: const EdgeInsets.symmetric(horizontal: 24),
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.black.withAlpha(180),
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const Text(
                      'Tap on a surface to place the experience',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Move your device to detect surfaces',
                      style: TextStyle(
                        color: Colors.white.withAlpha(200),
                        fontSize: 14,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            ),

          // Error message
          if (arLoadingState.error != null)
            Positioned(
              bottom: 100,
              left: 0,
              right: 0,
              child: Container(
                margin: const EdgeInsets.symmetric(horizontal: 24),
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.red.withAlpha(200),
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const Text(
                      'Error',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      arLoadingState.error!,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 14,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            ),

          // Experience info card
          Positioned(
            bottom: 16,
            left: 16,
            right: 16,
            child: Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withAlpha(40),
                    blurRadius: 10,
                    offset: const Offset(0, 5),
                  ),
                ],
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    widget.experience.title,
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: AppTheme.textPrimaryColor,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    widget.experience.description,
                    style: const TextStyle(
                      fontSize: 14,
                      color: AppTheme.textSecondaryColor,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 16),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      // Reset button
                      if (_hasPlacedModel)
                        ElevatedButton.icon(
                          onPressed: _resetPlacement,
                          icon: const Icon(Icons.refresh),
                          label: const Text('Reset'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: AppTheme.secondaryColor,
                            foregroundColor: Colors.white,
                          ),
                        )
                      else
                        const SizedBox.shrink(),

                      // Book button
                      ElevatedButton.icon(
                        onPressed: () {
                          Navigator.pop(context);
                        },
                        icon: const Icon(Icons.book_online),
                        label: const Text('Book Now'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppTheme.primaryColor,
                          foregroundColor: Colors.white,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
