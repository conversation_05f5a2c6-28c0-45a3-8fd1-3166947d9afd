import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:image_picker/image_picker.dart';
import 'package:culture_connect/models/verification_model.dart';
import 'package:culture_connect/services/verification_service.dart';
import 'package:culture_connect/widgets/verification/verification_badge.dart';

/// A screen for guide verification
class GuideVerificationScreen extends ConsumerStatefulWidget {
  /// Creates a new guide verification screen
  const GuideVerificationScreen({super.key});

  @override
  ConsumerState<GuideVerificationScreen> createState() =>
      _GuideVerificationScreenState();
}

class _GuideVerificationScreenState
    extends ConsumerState<GuideVerificationScreen> {
  final _formKey = GlobalKey<FormState>();
  final _notesController = TextEditingController();

  VerificationType _selectedType = VerificationType.identity;
  final List<File> _documents = [];
  bool _isLoading = false;
  String? _errorMessage;
  String? _successMessage;

  @override
  void dispose() {
    _notesController.dispose();
    super.dispose();
  }

  Future<void> _submitVerificationRequest() async {
    if (_formKey.currentState?.validate() != true) {
      return;
    }

    if (_documents.isEmpty) {
      setState(() {
        _errorMessage = 'Please upload at least one document';
      });
      return;
    }

    setState(() {
      _isLoading = true;
      _errorMessage = null;
      _successMessage = null;
    });

    try {
      final verificationService = ref.read(verificationServiceProvider);

      await verificationService.submitVerificationRequest(
        type: _selectedType,
        documents: _documents,
        notes: _notesController.text,
      );

      setState(() {
        _successMessage = 'Verification request submitted successfully';
        _documents.clear();
        _notesController.clear();
      });
    } catch (e) {
      setState(() {
        _errorMessage = 'Failed to submit verification request: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _pickDocument() async {
    try {
      final picker = ImagePicker();
      final pickedFile = await picker.pickImage(source: ImageSource.gallery);

      if (pickedFile != null) {
        setState(() {
          _documents.add(File(pickedFile.path));
        });
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Failed to pick document: $e';
      });
    }
  }

  void _removeDocument(int index) {
    setState(() {
      _documents.removeAt(index);
    });
  }

  String _getVerificationTypeName(VerificationType type) {
    switch (type) {
      case VerificationType.identity:
        return 'Identity Verification';
      case VerificationType.professional:
        return 'Professional Certification';
      case VerificationType.background:
        return 'Background Check';
      case VerificationType.address:
        return 'Address Verification';
      case VerificationType.phone:
        return 'Phone Verification';
      case VerificationType.email:
        return 'Email Verification';
      case VerificationType.social:
        return 'Social Media Verification';
      case VerificationType.license:
        return 'Government License';
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final verificationBadgesAsync = ref.watch(verificationBadgesProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Guide Verification'),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Current verification badges
            Text(
              'Your Verification Badges',
              style: theme.textTheme.titleLarge,
            ),
            const SizedBox(height: 16),

            verificationBadgesAsync.when(
              data: (badges) {
                if (badges.isEmpty) {
                  return Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.grey.withAlpha(25),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Center(
                      child: Text(
                        'You don\'t have any verification badges yet. Submit a verification request below to get verified.',
                        textAlign: TextAlign.center,
                      ),
                    ),
                  );
                }

                return VerificationBadgesList(
                  badges: badges,
                  showDescription: true,
                  showStatus: true,
                  showExpiry: true,
                );
              },
              loading: () => const Center(
                child: CircularProgressIndicator(),
              ),
              error: (error, stackTrace) => Center(
                child: Text(
                  'Error loading verification badges: $error',
                  style: TextStyle(color: theme.colorScheme.error),
                ),
              ),
            ),

            const SizedBox(height: 24),
            const Divider(),
            const SizedBox(height: 24),

            // Submit verification request form
            Text(
              'Submit Verification Request',
              style: theme.textTheme.titleLarge,
            ),
            const SizedBox(height: 16),

            Form(
              key: _formKey,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Verification type
                  Text(
                    'Verification Type',
                    style: theme.textTheme.titleSmall,
                  ),
                  const SizedBox(height: 8),

                  DropdownButtonFormField<VerificationType>(
                    value: _selectedType,
                    decoration: const InputDecoration(
                      border: OutlineInputBorder(),
                      contentPadding: EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 8,
                      ),
                    ),
                    items: VerificationType.values.map((type) {
                      return DropdownMenuItem<VerificationType>(
                        value: type,
                        child: Text(_getVerificationTypeName(type)),
                      );
                    }).toList(),
                    onChanged: (value) {
                      setState(() {
                        _selectedType = value!;
                      });
                    },
                  ),

                  const SizedBox(height: 16),

                  // Documents
                  Text(
                    'Upload Documents',
                    style: theme.textTheme.titleSmall,
                  ),
                  const SizedBox(height: 8),

                  if (_documents.isNotEmpty)
                    SizedBox(
                      height: 150,
                      child: ListView.builder(
                        scrollDirection: Axis.horizontal,
                        itemCount: _documents.length,
                        itemBuilder: (context, index) {
                          return Stack(
                            children: [
                              Container(
                                margin: const EdgeInsets.only(right: 8),
                                width: 150,
                                height: 150,
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(8),
                                  image: DecorationImage(
                                    image: FileImage(_documents[index]),
                                    fit: BoxFit.cover,
                                  ),
                                ),
                              ),
                              Positioned(
                                top: 4,
                                right: 12,
                                child: GestureDetector(
                                  onTap: () => _removeDocument(index),
                                  child: Container(
                                    padding: const EdgeInsets.all(4),
                                    decoration: const BoxDecoration(
                                      color: Colors.red,
                                      shape: BoxShape.circle,
                                    ),
                                    child: const Icon(
                                      Icons.close,
                                      color: Colors.white,
                                      size: 16,
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          );
                        },
                      ),
                    ),

                  const SizedBox(height: 8),

                  OutlinedButton.icon(
                    onPressed: _pickDocument,
                    icon: const Icon(Icons.upload_file),
                    label: const Text('Upload Document'),
                  ),

                  const SizedBox(height: 16),

                  // Notes
                  Text(
                    'Additional Notes',
                    style: theme.textTheme.titleSmall,
                  ),
                  const SizedBox(height: 8),

                  TextFormField(
                    controller: _notesController,
                    decoration: const InputDecoration(
                      border: OutlineInputBorder(),
                      hintText: 'Provide any additional information',
                    ),
                    maxLines: 3,
                  ),

                  const SizedBox(height: 24),

                  // Error message
                  if (_errorMessage != null)
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: theme.colorScheme.error.withAlpha(25),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Row(
                        children: [
                          Icon(
                            Icons.error_outline,
                            color: theme.colorScheme.error,
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              _errorMessage!,
                              style: TextStyle(
                                color: theme.colorScheme.error,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),

                  // Success message
                  if (_successMessage != null)
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.green.withAlpha(25),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Row(
                        children: [
                          const Icon(
                            Icons.check_circle,
                            color: Colors.green,
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              _successMessage!,
                              style: const TextStyle(
                                color: Colors.green,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),

                  const SizedBox(height: 24),

                  // Submit button
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: _isLoading ? null : _submitVerificationRequest,
                      child: _isLoading
                          ? const SizedBox(
                              width: 20,
                              height: 20,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                              ),
                            )
                          : const Text('Submit Verification Request'),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
