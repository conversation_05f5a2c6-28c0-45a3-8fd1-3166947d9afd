import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';

import 'package:culture_connect/models/booking_model.dart';
import 'package:culture_connect/providers/booking_provider.dart';
import 'package:culture_connect/widgets/custom_app_bar.dart';
import 'package:culture_connect/widgets/common/loading_indicator.dart';

/// A screen that displays the details of a booking
class BookingDetailsScreen extends ConsumerStatefulWidget {
  /// The ID of the booking to display
  final String bookingId;

  /// Creates a new booking details screen
  const BookingDetailsScreen({
    super.key,
    required this.bookingId,
  });

  @override
  ConsumerState<BookingDetailsScreen> createState() =>
      _BookingDetailsScreenState();
}

class _BookingDetailsScreenState extends ConsumerState<BookingDetailsScreen> {
  @override
  void initState() {
    super.initState();
    // Load the booking details when the screen is initialized
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(bookingNotifierProvider.notifier).loadBooking(widget.bookingId);
    });
  }

  @override
  Widget build(BuildContext context) {
    final bookingAsync = ref.watch(bookingNotifierProvider);
    final dateFormat = DateFormat('MMM dd, yyyy');

    return Scaffold(
      appBar: const CustomAppBar(
        title: 'Booking Details',
        showBackButton: true,
      ),
      body: bookingAsync.when(
        data: (booking) {
          if (booking == null) {
            return const Center(
              child: Text('Booking not found'),
            );
          }
          return _buildBookingDetails(context, booking, dateFormat);
        },
        loading: () => const Center(
          child: LoadingIndicator(),
        ),
        error: (error, stackTrace) => Center(
          child: Text('Error: $error'),
        ),
      ),
    );
  }

  Widget _buildBookingDetails(
      BuildContext context, BookingModel booking, DateFormat dateFormat) {
    final theme = Theme.of(context);

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Booking status
          _buildStatusCard(booking, theme),
          const SizedBox(height: 16.0),

          // Experience details
          _buildExperienceCard(booking, theme),
          const SizedBox(height: 16.0),

          // Booking details
          _buildBookingInfoCard(booking, dateFormat, theme),
          const SizedBox(height: 16.0),

          // Payment details
          _buildPaymentCard(booking, theme),
          const SizedBox(height: 16.0),

          // Customer details
          _buildCustomerCard(booking, theme),
          const SizedBox(height: 24.0),

          // Actions
          if (booking.status == BookingStatus.approved)
            _buildActionButtons(booking),
        ],
      ),
    );
  }

  Widget _buildStatusCard(BookingModel booking, ThemeData theme) {
    final statusColor = _getStatusColor(booking.status);

    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8.0),
              decoration: BoxDecoration(
                color: statusColor.withAlpha(25),
                shape: BoxShape.circle,
              ),
              child: Icon(
                _getStatusIcon(booking.status),
                color: statusColor,
                size: 24.0,
              ),
            ),
            const SizedBox(width: 16.0),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Booking Status',
                    style: TextStyle(
                      fontSize: 14.0,
                      color: theme.colorScheme.onSurfaceVariant,
                    ),
                  ),
                  const SizedBox(height: 4.0),
                  Text(
                    _getStatusText(booking.status),
                    style: TextStyle(
                      fontSize: 18.0,
                      fontWeight: FontWeight.bold,
                      color: statusColor,
                    ),
                  ),
                ],
              ),
            ),
            Text(
              'ID: ${booking.id.substring(0, 8)}',
              style: TextStyle(
                fontSize: 12.0,
                color: theme.colorScheme.onSurfaceVariant,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildExperienceCard(BookingModel booking, ThemeData theme) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Experience',
              style: TextStyle(
                fontSize: 16.0,
                fontWeight: FontWeight.bold,
                color: theme.colorScheme.onSurface,
              ),
            ),
            const SizedBox(height: 12.0),
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                ClipRRect(
                  borderRadius: BorderRadius.circular(8.0),
                  child: booking.experience.imageUrl != null
                      ? Image.network(
                          booking.experience.imageUrl!,
                          width: 80.0,
                          height: 80.0,
                          fit: BoxFit.cover,
                        )
                      : Container(
                          width: 80.0,
                          height: 80.0,
                          color: Colors.grey[300],
                          child: Icon(
                            Icons.image,
                            size: 40.0,
                            color: Colors.grey[600],
                          ),
                        ),
                ),
                const SizedBox(width: 16.0),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        booking.experience.title,
                        style: TextStyle(
                          fontSize: 16.0,
                          fontWeight: FontWeight.bold,
                          color: theme.colorScheme.onSurface,
                        ),
                      ),
                      const SizedBox(height: 4.0),
                      Text(
                        booking.experience.category,
                        style: TextStyle(
                          fontSize: 14.0,
                          color: theme.colorScheme.onSurfaceVariant,
                        ),
                      ),
                      const SizedBox(height: 8.0),
                      Row(
                        children: [
                          const Icon(
                            Icons.access_time,
                            size: 16.0,
                            color: Colors.grey,
                          ),
                          const SizedBox(width: 4.0),
                          Text(
                            '${booking.experience.durationMinutes} min',
                            style: const TextStyle(
                              fontSize: 14.0,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBookingInfoCard(
      BookingModel booking, DateFormat dateFormat, ThemeData theme) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Booking Details',
              style: TextStyle(
                fontSize: 16.0,
                fontWeight: FontWeight.bold,
                color: theme.colorScheme.onSurface,
              ),
            ),
            const SizedBox(height: 12.0),
            _buildDetailRow(
                'Booking Date', dateFormat.format(booking.bookingDate)),
            _buildDetailRow(
                'Experience Date', dateFormat.format(booking.experienceDate)),
            _buildDetailRow('Start Time', booking.startTime),
            _buildDetailRow(
                'Participants', booking.participantCount.toString()),
            if (booking.specialRequirements != null &&
                booking.specialRequirements!.isNotEmpty)
              _buildDetailRow(
                  'Special Requirements', booking.specialRequirements!),
          ],
        ),
      ),
    );
  }

  Widget _buildPaymentCard(BookingModel booking, ThemeData theme) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Payment Details',
              style: TextStyle(
                fontSize: 16.0,
                fontWeight: FontWeight.bold,
                color: theme.colorScheme.onSurface,
              ),
            ),
            const SizedBox(height: 12.0),
            _buildDetailRow('Payment Status',
                _getPaymentStatusText(booking.payment.status)),
            _buildDetailRow('Amount',
                '${booking.payment.currency}${booking.payment.amount.toStringAsFixed(2)}'),
            if (booking.payment.paymentMethod != null)
              _buildDetailRow('Payment Method', booking.payment.paymentMethod!),
            if (booking.payment.transactionId != null)
              _buildDetailRow('Transaction ID', booking.payment.transactionId!),
            if (booking.payment.refundAmount != null)
              _buildDetailRow('Refund Amount',
                  '${booking.payment.currency}${booking.payment.refundAmount!.toStringAsFixed(2)}'),
            if (booking.payment.refundReason != null)
              _buildDetailRow('Refund Reason', booking.payment.refundReason!),
          ],
        ),
      ),
    );
  }

  Widget _buildCustomerCard(BookingModel booking, ThemeData theme) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Customer Details',
              style: TextStyle(
                fontSize: 16.0,
                fontWeight: FontWeight.bold,
                color: theme.colorScheme.onSurface,
              ),
            ),
            const SizedBox(height: 12.0),
            _buildDetailRow('Name', booking.customer.name),
            _buildDetailRow('Email', booking.customer.email),
            if (booking.customer.phone != null)
              _buildDetailRow('Phone', booking.customer.phone!),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButtons(BookingModel booking) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        OutlinedButton.icon(
          onPressed: () => _showCancellationDialog(booking),
          icon: const Icon(Icons.cancel, color: Colors.red),
          label: const Text('Cancel Booking'),
          style: OutlinedButton.styleFrom(
            foregroundColor: Colors.red,
            side: const BorderSide(color: Colors.red),
            padding:
                const EdgeInsets.symmetric(horizontal: 16.0, vertical: 12.0),
          ),
        ),
      ],
    );
  }

  Widget _buildDetailRow(String label, String value) {
    final theme = Theme.of(context);
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 140.0,
            child: Text(
              label,
              style: TextStyle(
                fontSize: 14.0,
                color: theme.colorScheme.onSurfaceVariant,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: TextStyle(
                fontSize: 14.0,
                fontWeight: FontWeight.w500,
                color: theme.colorScheme.onSurface,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _showCancellationDialog(BookingModel booking) async {
    final TextEditingController reasonController = TextEditingController();

    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Cancel Booking'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('Are you sure you want to cancel this booking?'),
            const SizedBox(height: 16.0),
            TextField(
              controller: reasonController,
              decoration: const InputDecoration(
                labelText: 'Reason for cancellation',
                border: OutlineInputBorder(),
              ),
              maxLines: 3,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('No'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('Yes, Cancel'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      final reason = reasonController.text.trim();
      if (reason.isEmpty) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Please provide a reason for cancellation'),
              backgroundColor: Colors.red,
            ),
          );
        }
        return;
      }

      try {
        // For now, just show a success message since we don't have a cancel method
        // In a real implementation, this would call a cancel booking API

        // Reload the booking to reflect the changes
        await ref
            .read(bookingNotifierProvider.notifier)
            .loadBooking(booking.id);

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Booking cancelled successfully'),
              backgroundColor: Colors.green,
            ),
          );
          Navigator.pop(context);
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Error cancelling booking: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }

    reasonController.dispose();
  }

  Color _getStatusColor(BookingStatus status) {
    switch (status) {
      case BookingStatus.pending:
        return Colors.orange;
      case BookingStatus.approved:
        return Colors.green;
      case BookingStatus.rejected:
        return Colors.red;
      case BookingStatus.cancelled:
        return Colors.red;
      case BookingStatus.completed:
        return Colors.blue;
      case BookingStatus.noShow:
        return Colors.purple;
    }
  }

  IconData _getStatusIcon(BookingStatus status) {
    switch (status) {
      case BookingStatus.pending:
        return Icons.pending;
      case BookingStatus.approved:
        return Icons.check_circle;
      case BookingStatus.rejected:
        return Icons.cancel;
      case BookingStatus.cancelled:
        return Icons.cancel;
      case BookingStatus.completed:
        return Icons.done_all;
      case BookingStatus.noShow:
        return Icons.person_off;
    }
  }

  String _getStatusText(BookingStatus status) {
    switch (status) {
      case BookingStatus.pending:
        return 'Pending';
      case BookingStatus.approved:
        return 'Approved';
      case BookingStatus.rejected:
        return 'Rejected';
      case BookingStatus.cancelled:
        return 'Cancelled';
      case BookingStatus.completed:
        return 'Completed';
      case BookingStatus.noShow:
        return 'No Show';
    }
  }

  String _getPaymentStatusText(PaymentStatus status) {
    switch (status) {
      case PaymentStatus.pending:
        return 'Pending';
      case PaymentStatus.paid:
        return 'Paid';
      case PaymentStatus.partiallyPaid:
        return 'Partially Paid';
      case PaymentStatus.failed:
        return 'Failed';
      case PaymentStatus.refunded:
        return 'Refunded';
      case PaymentStatus.partiallyRefunded:
        return 'Partially Refunded';
    }
  }
}
