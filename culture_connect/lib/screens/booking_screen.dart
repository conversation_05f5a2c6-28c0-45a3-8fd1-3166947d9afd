import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:culture_connect/models/experience.dart';
import 'package:culture_connect/models/booking.dart';
import 'package:culture_connect/models/payment_result.dart';
import 'package:culture_connect/services/booking_service.dart';
import 'package:culture_connect/screens/payment_screen.dart';
import 'package:intl/intl.dart';

class BookingScreen extends ConsumerStatefulWidget {
  final Experience experience;

  const BookingScreen({
    super.key,
    required this.experience,
  });

  @override
  ConsumerState<BookingScreen> createState() => _BookingScreenState();
}

class _BookingScreenState extends ConsumerState<BookingScreen> {
  final _bookingService = BookingService();
  DateTime? _selectedDate;
  TimeSlot? _selectedTimeSlot;
  int _participantCount = 1;
  final _specialRequirementsController = TextEditingController();
  bool _isLoading = false;

  @override
  void dispose() {
    _specialRequirementsController.dispose();
    super.dispose();
  }

  Future<void> _selectDate(BuildContext context) async {
    if (!mounted) return;

    final now = DateTime.now();
    final firstDate = DateTime(now.year, now.month, now.day);
    final lastDate = DateTime(now.year + 1, now.month, now.day);

    final picked = await showDatePicker(
      context: context,
      initialDate: _selectedDate ?? firstDate,
      firstDate: firstDate,
      lastDate: lastDate,
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: Theme.of(context).colorScheme.copyWith(
                  primary: Theme.of(context).colorScheme.primary,
                ),
          ),
          child: child!,
        );
      },
    );

    if (picked != null && mounted) {
      final isAvailable = await _bookingService.isDateAvailable(
        widget.experience,
        picked,
      );

      if (!mounted) return;

      if (isAvailable) {
        setState(() {
          _selectedDate = picked;
          _selectedTimeSlot = null;
        });
      } else if (mounted) {
        final scaffoldMessenger = ScaffoldMessenger.of(context);
        scaffoldMessenger.showSnackBar(
          const SnackBar(
            content: Text('Selected date is not available'),
          ),
        );
      }
    }
  }

  void _selectTimeSlot(TimeSlot timeSlot) {
    setState(() {
      _selectedTimeSlot = timeSlot;
    });
  }

  void _updateParticipantCount(int count) {
    if (count >= 1 && count <= widget.experience.maxParticipants) {
      setState(() {
        _participantCount = count;
      });
    }
  }

  Future<void> _createBooking() async {
    if (!mounted) return;

    if (_selectedDate == null || _selectedTimeSlot == null) {
      final scaffoldMessenger = ScaffoldMessenger.of(context);
      scaffoldMessenger.showSnackBar(
        const SnackBar(
          content: Text('Please select a date and time slot'),
        ),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final booking = await _bookingService.createBooking(
        experience: widget.experience,
        date: _selectedDate!,
        timeSlot: _selectedTimeSlot!,
        participantCount: _participantCount,
        specialRequirements: _specialRequirementsController.text,
      );

      if (!mounted) return;

      // Navigate to payment screen
      final result = await Navigator.of(context).push<PaymentResult>(
        MaterialPageRoute(
          builder: (context) => PaymentScreen(
            booking: booking,
            userEmail: '<EMAIL>', // TODO: Get from user profile
            userName: 'John Doe', // TODO: Get from user profile
          ),
        ),
      );

      if (!mounted) return;

      if (result != null && result.success) {
        // Payment successful, update booking status
        // TODO: Update booking status in backend
        Navigator.of(context).pop(booking);
      }
    } catch (e) {
      if (!mounted) return;

      final scaffoldMessenger = ScaffoldMessenger.of(context);
      scaffoldMessenger.showSnackBar(
        SnackBar(
          content: Text('Failed to create booking: $e'),
        ),
      );
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final currencyFormat = NumberFormat.currency(symbol: '\$');

    return Scaffold(
      appBar: AppBar(
        title: const Text('Book Experience'),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Experience Summary
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            widget.experience.title,
                            style: theme.textTheme.titleLarge,
                          ),
                          const SizedBox(height: 8),
                          Text(
                            widget.experience.location,
                            style: theme.textTheme.bodyMedium,
                          ),
                          const SizedBox(height: 16),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                'Price per person',
                                style: theme.textTheme.bodyMedium,
                              ),
                              Text(
                                currencyFormat.format(widget.experience.price),
                                style: theme.textTheme.titleMedium?.copyWith(
                                  color: theme.colorScheme.primary,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                  const SizedBox(height: 24),

                  // Date Selection
                  Text(
                    'Select Date',
                    style: theme.textTheme.titleMedium,
                  ),
                  const SizedBox(height: 8),
                  OutlinedButton.icon(
                    onPressed: () => _selectDate(context),
                    icon: const Icon(Icons.calendar_today),
                    label: Text(
                      _selectedDate == null
                          ? 'Choose a date'
                          : DateFormat('EEEE, MMMM d, y')
                              .format(_selectedDate!),
                    ),
                  ),
                  const SizedBox(height: 24),

                  // Time Slot Selection
                  if (_selectedDate != null) ...[
                    Text(
                      'Select Time',
                      style: theme.textTheme.titleMedium,
                    ),
                    const SizedBox(height: 8),
                    FutureBuilder<List<TimeSlot>>(
                      future: _bookingService.getAvailableTimeSlots(
                        widget.experience,
                        _selectedDate!,
                      ),
                      builder: (context, snapshot) {
                        if (snapshot.connectionState ==
                            ConnectionState.waiting) {
                          return const Center(
                              child: CircularProgressIndicator());
                        }

                        if (snapshot.hasError) {
                          return Text(
                            'Error loading time slots: ${snapshot.error}',
                            style: TextStyle(color: theme.colorScheme.error),
                          );
                        }

                        final timeSlots = snapshot.data ?? [];
                        return Wrap(
                          spacing: 8,
                          runSpacing: 8,
                          children: timeSlots.map((slot) {
                            final isSelected = _selectedTimeSlot == slot;
                            return ChoiceChip(
                              label: Text(slot.formattedTime),
                              selected: isSelected,
                              onSelected: slot.isAvailable
                                  ? (selected) {
                                      if (selected) {
                                        _selectTimeSlot(slot);
                                      }
                                    }
                                  : null,
                            );
                          }).toList(),
                        );
                      },
                    ),
                    const SizedBox(height: 24),
                  ],

                  // Participant Count
                  Text(
                    'Number of Participants',
                    style: theme.textTheme.titleMedium,
                  ),
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      IconButton(
                        onPressed: _participantCount > 1
                            ? () =>
                                _updateParticipantCount(_participantCount - 1)
                            : null,
                        icon: const Icon(Icons.remove),
                      ),
                      Text(
                        _participantCount.toString(),
                        style: theme.textTheme.titleLarge,
                      ),
                      IconButton(
                        onPressed: _participantCount <
                                widget.experience.maxParticipants
                            ? () =>
                                _updateParticipantCount(_participantCount + 1)
                            : null,
                        icon: const Icon(Icons.add),
                      ),
                    ],
                  ),
                  const SizedBox(height: 24),

                  // Special Requirements
                  Text(
                    'Special Requirements (Optional)',
                    style: theme.textTheme.titleMedium,
                  ),
                  const SizedBox(height: 8),
                  TextField(
                    controller: _specialRequirementsController,
                    maxLines: 3,
                    decoration: const InputDecoration(
                      hintText: 'Enter any special requirements or requests...',
                    ),
                  ),
                  const SizedBox(height: 24),

                  // Total Price
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            'Total Price',
                            style: theme.textTheme.titleMedium,
                          ),
                          Text(
                            currencyFormat.format(
                              widget.experience.price * _participantCount,
                            ),
                            style: theme.textTheme.titleLarge?.copyWith(
                              color: theme.colorScheme.primary,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  const SizedBox(height: 24),

                  // Book Button
                  SizedBox(
                    width: double.infinity,
                    child: FilledButton(
                      onPressed: _createBooking,
                      child: const Text('Book Now'),
                    ),
                  ),
                ],
              ),
            ),
    );
  }
}
