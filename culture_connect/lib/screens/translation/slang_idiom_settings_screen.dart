import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:culture_connect/models/translation/slang_idiom_model.dart';
import 'package:culture_connect/providers/slang_idiom_provider.dart';
import 'package:culture_connect/theme/app_theme.dart';
import 'package:culture_connect/widgets/custom_app_bar.dart';

/// A screen for slang and idiom settings
class SlangIdiomSettingsScreen extends ConsumerWidget {
  /// Creates a new slang and idiom settings screen
  const SlangIdiomSettingsScreen({super.key});
  
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final useSlangIdiomDetection = ref.watch(useSlangIdiomDetectionProvider);
    final showPotentiallyOffensiveContent = ref.watch(showPotentiallyOffensiveContentProvider);
    
    return Scaffold(
      appBar: const CustomAppBar(
        title: 'Slang & Idiom Settings',
        showBackButton: true,
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(16.r),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Introduction
            Text(
              'Slang & Idiom Detection',
              style: TextStyle(
                fontSize: 20.sp,
                fontWeight: FontWeight.bold,
                color: AppTheme.textPrimaryColor,
              ),
            ),
            
            SizedBox(height: 8.h),
            
            Text(
              'Slang and idiom detection helps you understand expressions that don\'t translate literally. This feature identifies slang, idioms, and other expressions to help you communicate more effectively across cultures.',
              style: TextStyle(
                fontSize: 14.sp,
                color: AppTheme.textSecondaryColor,
              ),
            ),
            
            SizedBox(height: 24.h),
            
            // Main settings
            Card(
              elevation: 2,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12.r),
              ),
              child: Padding(
                padding: EdgeInsets.all(16.r),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'General Settings',
                      style: TextStyle(
                        fontSize: 18.sp,
                        fontWeight: FontWeight.bold,
                        color: AppTheme.textPrimaryColor,
                      ),
                    ),
                    
                    SizedBox(height: 16.h),
                    
                    // Slang and idiom detection toggle
                    SwitchListTile(
                      title: Text(
                        'Enable Slang & Idiom Detection',
                        style: TextStyle(
                          fontSize: 16.sp,
                          color: AppTheme.textPrimaryColor,
                        ),
                      ),
                      subtitle: Text(
                        'Identify and explain slang, idioms, and expressions in translations',
                        style: TextStyle(
                          fontSize: 12.sp,
                          color: AppTheme.textSecondaryColor,
                        ),
                      ),
                      value: useSlangIdiomDetection,
                      onChanged: (value) {
                        ref.read(slangIdiomNotifierProvider.notifier).setUseSlangIdiomDetection(value);
                      },
                      activeColor: Colors.purple,
                    ),
                    
                    // Show potentially offensive content toggle
                    SwitchListTile(
                      title: Text(
                        'Show Potentially Offensive Content',
                        style: TextStyle(
                          fontSize: 16.sp,
                          color: AppTheme.textPrimaryColor,
                        ),
                      ),
                      subtitle: Text(
                        'Display slang and expressions that may be considered offensive',
                        style: TextStyle(
                          fontSize: 12.sp,
                          color: AppTheme.textSecondaryColor,
                        ),
                      ),
                      value: showPotentiallyOffensiveContent,
                      onChanged: useSlangIdiomDetection ? (value) {
                        ref.read(slangIdiomNotifierProvider.notifier).setShowPotentiallyOffensiveContent(value);
                      } : null,
                      activeColor: Colors.purple,
                    ),
                  ],
                ),
              ),
            ),
            
            SizedBox(height: 24.h),
            
            // Expression types
            Card(
              elevation: 2,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12.r),
              ),
              child: Padding(
                padding: EdgeInsets.all(16.r),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Types of Expressions',
                      style: TextStyle(
                        fontSize: 18.sp,
                        fontWeight: FontWeight.bold,
                        color: AppTheme.textPrimaryColor,
                      ),
                    ),
                    
                    SizedBox(height: 16.h),
                    
                    ...ExpressionType.values.map((type) {
                      return _buildExpressionTypeItem(type);
                    }).toList(),
                  ],
                ),
              ),
            ),
            
            SizedBox(height: 24.h),
            
            // Formality levels
            Card(
              elevation: 2,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12.r),
              ),
              child: Padding(
                padding: EdgeInsets.all(16.r),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Formality Levels',
                      style: TextStyle(
                        fontSize: 18.sp,
                        fontWeight: FontWeight.bold,
                        color: AppTheme.textPrimaryColor,
                      ),
                    ),
                    
                    SizedBox(height: 16.h),
                    
                    ...FormalityLevel.values.map((level) {
                      return _buildFormalityLevelItem(level);
                    }).toList(),
                  ],
                ),
              ),
            ),
            
            SizedBox(height: 24.h),
            
            // Benefits
            Card(
              elevation: 2,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12.r),
              ),
              child: Padding(
                padding: EdgeInsets.all(16.r),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Benefits of Slang & Idiom Detection',
                      style: TextStyle(
                        fontSize: 18.sp,
                        fontWeight: FontWeight.bold,
                        color: AppTheme.textPrimaryColor,
                      ),
                    ),
                    
                    SizedBox(height: 16.h),
                    
                    _buildBenefitItem(
                      Icons.translate,
                      'Better Understanding',
                      'Understand expressions that don\'t translate literally',
                    ),
                    
                    _buildBenefitItem(
                      Icons.error_outline,
                      'Avoid Misunderstandings',
                      'Prevent confusion from slang and idioms',
                    ),
                    
                    _buildBenefitItem(
                      Icons.school_outlined,
                      'Learn New Expressions',
                      'Expand your vocabulary with common expressions',
                    ),
                    
                    _buildBenefitItem(
                      Icons.people_outline,
                      'Sound More Natural',
                      'Communicate more like a native speaker',
                    ),
                  ],
                ),
              ),
            ),
            
            SizedBox(height: 24.h),
            
            // Clear cache button
            Center(
              child: ElevatedButton.icon(
                onPressed: () {
                  ref.read(slangIdiomNotifierProvider.notifier).clearCache();
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('Slang and idiom cache cleared'),
                      duration: Duration(seconds: 2),
                    ),
                  );
                },
                icon: const Icon(Icons.delete_outline),
                label: const Text('Clear Slang & Idiom Cache'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.red,
                  foregroundColor: Colors.white,
                  padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
  
  /// Build an expression type item
  Widget _buildExpressionTypeItem(ExpressionType type) {
    return Padding(
      padding: EdgeInsets.only(bottom: 16.h),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: EdgeInsets.all(8.r),
            decoration: BoxDecoration(
              color: type.color.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8.r),
            ),
            child: Icon(
              type.icon,
              size: 24.r,
              color: type.color,
            ),
          ),
          SizedBox(width: 16.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  type.displayName,
                  style: TextStyle(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.bold,
                    color: AppTheme.textPrimaryColor,
                  ),
                ),
                SizedBox(height: 4.h),
                Text(
                  _getExpressionTypeDescription(type),
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: AppTheme.textSecondaryColor,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
  
  /// Build a formality level item
  Widget _buildFormalityLevelItem(FormalityLevel level) {
    return Padding(
      padding: EdgeInsets.only(bottom: 16.h),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: EdgeInsets.all(8.r),
            decoration: BoxDecoration(
              color: level.color.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8.r),
            ),
            child: Icon(
              Icons.person_outline,
              size: 24.r,
              color: level.color,
            ),
          ),
          SizedBox(width: 16.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  level.displayName,
                  style: TextStyle(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.bold,
                    color: AppTheme.textPrimaryColor,
                  ),
                ),
                SizedBox(height: 4.h),
                Text(
                  _getFormalityLevelDescription(level),
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: AppTheme.textSecondaryColor,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
  
  /// Build a benefit item
  Widget _buildBenefitItem(IconData icon, String title, String description) {
    return Padding(
      padding: EdgeInsets.only(bottom: 16.h),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: EdgeInsets.all(8.r),
            decoration: BoxDecoration(
              color: Colors.purple.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8.r),
            ),
            child: Icon(
              icon,
              size: 24.r,
              color: Colors.purple,
            ),
          ),
          SizedBox(width: 16.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.bold,
                    color: AppTheme.textPrimaryColor,
                  ),
                ),
                SizedBox(height: 4.h),
                Text(
                  description,
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: AppTheme.textSecondaryColor,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
  
  /// Get the description for an expression type
  String _getExpressionTypeDescription(ExpressionType type) {
    switch (type) {
      case ExpressionType.slang:
        return 'Informal language used by particular groups, often changing rapidly.';
      case ExpressionType.idiom:
        return 'Expressions that cannot be understood from the individual meanings of their words.';
      case ExpressionType.colloquialism:
        return 'Informal expressions used in everyday conversation rather than formal speech or writing.';
      case ExpressionType.proverb:
        return 'Short, well-known sayings that express a truth or give advice.';
      case ExpressionType.metaphor:
        return 'Expressions that describe something by referring to something else with similar qualities.';
      case ExpressionType.euphemism:
        return 'Mild or indirect expressions used in place of ones considered harsh or blunt.';
      case ExpressionType.jargon:
        return 'Special words or expressions used by a profession or group that are difficult for others to understand.';
    }
  }
  
  /// Get the description for a formality level
  String _getFormalityLevelDescription(FormalityLevel level) {
    switch (level) {
      case FormalityLevel.veryInformal:
        return 'Used among close friends, family, or in very casual settings. May be inappropriate in formal contexts.';
      case FormalityLevel.informal:
        return 'Used in casual, everyday conversations with friends, family, and peers.';
      case FormalityLevel.neutral:
        return 'Appropriate for most everyday situations, neither particularly formal nor informal.';
      case FormalityLevel.formal:
        return 'Used in professional settings, with people you don\'t know well, or in official communications.';
      case FormalityLevel.veryFormal:
        return 'Used in highly formal situations, official documents, or ceremonial contexts.';
    }
  }
}
