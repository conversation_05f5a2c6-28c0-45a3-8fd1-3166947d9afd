// Flutter imports
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

// Package imports
import 'package:culture_connect/models/translation/message_translation_metadata.dart';
import 'package:culture_connect/models/translation/translation_feedback_model.dart';
import 'package:culture_connect/providers/translation_feedback_provider.dart';
import 'package:culture_connect/theme/app_theme.dart';
import 'package:culture_connect/utils/date_utils.dart' as app_date_utils;
import 'package:culture_connect/utils/language_utils.dart';
import 'package:culture_connect/widgets/custom_app_bar.dart';
import 'package:culture_connect/widgets/translation/translation_feedback_analytics.dart';

/// A screen for viewing translation feedback
class TranslationFeedbackScreen extends ConsumerStatefulWidget {
  /// Creates a new translation feedback screen
  const TranslationFeedbackScreen({super.key});

  @override
  ConsumerState<TranslationFeedbackScreen> createState() =>
      _TranslationFeedbackScreenState();
}

class _TranslationFeedbackScreenState
    extends ConsumerState<TranslationFeedbackScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const CustomAppBar(
        title: 'Translation Feedback',
        showBackButton: true,
      ),
      body: Column(
        children: [
          // Tab bar
          Container(
            color: Colors.white,
            child: TabBar(
              controller: _tabController,
              labelColor: AppTheme.primaryColor,
              unselectedLabelColor: Colors.grey,
              indicatorColor: AppTheme.primaryColor,
              tabs: const [
                Tab(text: 'Analytics'),
                Tab(text: 'My Feedback'),
              ],
            ),
          ),

          // Tab content
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: const [
                // Analytics tab
                TranslationFeedbackAnalytics(),

                // My feedback tab
                _UserFeedbackList(),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

/// A widget for displaying the user's feedback
class _UserFeedbackList extends ConsumerWidget {
  /// Creates a new user feedback list
  const _UserFeedbackList();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final userFeedbackAsync = ref.watch(userFeedbackProvider);

    return userFeedbackAsync.when(
      data: (feedbackList) => _buildContent(context, feedbackList),
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (error, stackTrace) => Center(
        child: Text(
          'Failed to load your feedback: $error',
          style: TextStyle(
            fontSize: 16.sp,
            color: Colors.red,
          ),
        ),
      ),
    );
  }

  /// Build the content
  Widget _buildContent(
      BuildContext context, List<TranslationFeedbackModel> feedbackList) {
    if (feedbackList.isEmpty) {
      return _buildEmptyState();
    }

    // Sort feedback by date (newest first)
    final sortedFeedback = List<TranslationFeedbackModel>.from(feedbackList)
      ..sort((a, b) => b.createdAt.compareTo(a.createdAt));

    return ListView.builder(
      padding: EdgeInsets.all(16.r),
      itemCount: sortedFeedback.length,
      itemBuilder: (context, index) {
        final feedback = sortedFeedback[index];
        return _buildFeedbackItem(context, feedback);
      },
    );
  }

  /// Build the empty state widget
  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.feedback_outlined,
            size: 64.r,
            color: Colors.grey[300],
          ),
          SizedBox(height: 16.h),
          Text(
            'No feedback provided yet',
            style: TextStyle(
              fontSize: 18.sp,
              fontWeight: FontWeight.bold,
              color: AppTheme.textPrimaryColor,
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            'Your feedback on translations will appear here',
            textAlign: TextAlign.center,
            style: TextStyle(
              fontSize: 14.sp,
              color: AppTheme.textSecondaryColor,
            ),
          ),
        ],
      ),
    );
  }

  /// Build a feedback item
  Widget _buildFeedbackItem(
      BuildContext context, TranslationFeedbackModel feedback) {
    return Card(
      margin: EdgeInsets.only(bottom: 16.h),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: Padding(
        padding: EdgeInsets.all(16.r),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              children: [
                Icon(
                  feedback.feedbackType.icon,
                  size: 20.r,
                  color: AppTheme.primaryColor,
                ),
                SizedBox(width: 8.w),
                Text(
                  feedback.feedbackType.displayName,
                  style: TextStyle(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.bold,
                    color: AppTheme.textPrimaryColor,
                  ),
                ),
                const Spacer(),
                Icon(
                  feedback.quality.icon,
                  size: 20.r,
                  color: feedback.quality.color,
                ),
                SizedBox(width: 4.w),
                Text(
                  feedback.quality.displayName,
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: feedback.quality.color,
                  ),
                ),
              ],
            ),

            SizedBox(height: 12.h),

            // Language pair
            Row(
              children: [
                Text(
                  '${LanguageUtils.getLanguageName(feedback.sourceLanguage)} → ${LanguageUtils.getLanguageName(feedback.targetLanguage)}',
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: AppTheme.textSecondaryColor,
                  ),
                ),
                const Spacer(),
                Text(
                  app_date_utils.DateUtils.formatRelativeDate(
                      feedback.createdAt),
                  style: TextStyle(
                    fontSize: 12.sp,
                    color: AppTheme.textSecondaryColor,
                  ),
                ),
              ],
            ),

            SizedBox(height: 12.h),

            // Original text
            Text(
              'Original:',
              style: TextStyle(
                fontSize: 12.sp,
                fontWeight: FontWeight.w500,
                color: AppTheme.textSecondaryColor,
              ),
            ),
            SizedBox(height: 4.h),
            Text(
              feedback.originalText,
              style: TextStyle(
                fontSize: 14.sp,
                color: AppTheme.textPrimaryColor,
              ),
            ),

            SizedBox(height: 8.h),

            // Translated text
            Text(
              'Translation:',
              style: TextStyle(
                fontSize: 12.sp,
                fontWeight: FontWeight.w500,
                color: AppTheme.textSecondaryColor,
              ),
            ),
            SizedBox(height: 4.h),
            Text(
              feedback.translatedText,
              style: TextStyle(
                fontSize: 14.sp,
                color: AppTheme.textPrimaryColor,
              ),
            ),

            // Suggested correction (if available)
            if (feedback.suggestedCorrection != null) ...[
              SizedBox(height: 8.h),
              Text(
                'Your Suggestion:',
                style: TextStyle(
                  fontSize: 12.sp,
                  fontWeight: FontWeight.w500,
                  color: AppTheme.textSecondaryColor,
                ),
              ),
              SizedBox(height: 4.h),
              Text(
                feedback.suggestedCorrection!,
                style: TextStyle(
                  fontSize: 14.sp,
                  color: AppTheme.primaryColor,
                ),
              ),
            ],

            // Comments (if available)
            if (feedback.comments != null) ...[
              SizedBox(height: 8.h),
              Text(
                'Comments:',
                style: TextStyle(
                  fontSize: 12.sp,
                  fontWeight: FontWeight.w500,
                  color: AppTheme.textSecondaryColor,
                ),
              ),
              SizedBox(height: 4.h),
              Text(
                feedback.comments!,
                style: TextStyle(
                  fontSize: 14.sp,
                  fontStyle: FontStyle.italic,
                  color: AppTheme.textPrimaryColor,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}
