import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:culture_connect/models/translation/pronunciation_model.dart';
import 'package:culture_connect/providers/pronunciation_provider.dart';
import 'package:culture_connect/theme/app_theme.dart';
import 'package:culture_connect/widgets/custom_app_bar.dart';

/// A screen for pronunciation settings
class PronunciationSettingsScreen extends ConsumerWidget {
  /// Creates a new pronunciation settings screen
  const PronunciationSettingsScreen({super.key});
  
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final usePronunciationGuidance = ref.watch(usePronunciationGuidanceProvider);
    final useIpaNotation = ref.watch(useIpaNotationProvider);
    final useSimplifiedPhonetics = ref.watch(useSimplifiedPhoneticsProvider);
    final useSyllableBreakdown = ref.watch(useSyllableBreakdownProvider);
    final showDifficultOnly = ref.watch(showDifficultOnlyProvider);
    final autoPlayPronunciation = ref.watch(autoPlayPronunciationProvider);
    
    return Scaffold(
      appBar: const CustomAppBar(
        title: 'Pronunciation Settings',
        showBackButton: true,
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(16.r),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Introduction
            Text(
              'Pronunciation Guidance',
              style: TextStyle(
                fontSize: 20.sp,
                fontWeight: FontWeight.bold,
                color: AppTheme.textPrimaryColor,
              ),
            ),
            
            SizedBox(height: 8.h),
            
            Text(
              'Pronunciation guidance helps you correctly pronounce words and phrases in different languages. This feature provides phonetic representations, audio pronunciations, and tips to improve your speaking skills.',
              style: TextStyle(
                fontSize: 14.sp,
                color: AppTheme.textSecondaryColor,
              ),
            ),
            
            SizedBox(height: 24.h),
            
            // Main settings
            Card(
              elevation: 2,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12.r),
              ),
              child: Padding(
                padding: EdgeInsets.all(16.r),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'General Settings',
                      style: TextStyle(
                        fontSize: 18.sp,
                        fontWeight: FontWeight.bold,
                        color: AppTheme.textPrimaryColor,
                      ),
                    ),
                    
                    SizedBox(height: 16.h),
                    
                    // Pronunciation guidance toggle
                    SwitchListTile(
                      title: Text(
                        'Enable Pronunciation Guidance',
                        style: TextStyle(
                          fontSize: 16.sp,
                          color: AppTheme.textPrimaryColor,
                        ),
                      ),
                      subtitle: Text(
                        'Show pronunciation guides for translations',
                        style: TextStyle(
                          fontSize: 12.sp,
                          color: AppTheme.textSecondaryColor,
                        ),
                      ),
                      value: usePronunciationGuidance,
                      onChanged: (value) {
                        ref.read(pronunciationNotifierProvider.notifier).setUsePronunciationGuidance(value);
                      },
                      activeColor: Colors.teal,
                    ),
                    
                    // Auto-play pronunciation toggle
                    SwitchListTile(
                      title: Text(
                        'Auto-Play Pronunciation',
                        style: TextStyle(
                          fontSize: 16.sp,
                          color: AppTheme.textPrimaryColor,
                        ),
                      ),
                      subtitle: Text(
                        'Automatically play audio pronunciation when available',
                        style: TextStyle(
                          fontSize: 12.sp,
                          color: AppTheme.textSecondaryColor,
                        ),
                      ),
                      value: autoPlayPronunciation,
                      onChanged: usePronunciationGuidance ? (value) {
                        ref.read(pronunciationNotifierProvider.notifier).setAutoPlayPronunciation(value);
                      } : null,
                      activeColor: Colors.teal,
                    ),
                    
                    // Show difficult only toggle
                    SwitchListTile(
                      title: Text(
                        'Focus on Difficult Pronunciations',
                        style: TextStyle(
                          fontSize: 16.sp,
                          color: AppTheme.textPrimaryColor,
                        ),
                      ),
                      subtitle: Text(
                        'Highlight words and phrases that are difficult to pronounce',
                        style: TextStyle(
                          fontSize: 12.sp,
                          color: AppTheme.textSecondaryColor,
                        ),
                      ),
                      value: showDifficultOnly,
                      onChanged: usePronunciationGuidance ? (value) {
                        ref.read(pronunciationNotifierProvider.notifier).setShowDifficultOnly(value);
                      } : null,
                      activeColor: Colors.teal,
                    ),
                  ],
                ),
              ),
            ),
            
            SizedBox(height: 24.h),
            
            // Pronunciation guide types
            Card(
              elevation: 2,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12.r),
              ),
              child: Padding(
                padding: EdgeInsets.all(16.r),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Pronunciation Guide Types',
                      style: TextStyle(
                        fontSize: 18.sp,
                        fontWeight: FontWeight.bold,
                        color: AppTheme.textPrimaryColor,
                      ),
                    ),
                    
                    SizedBox(height: 16.h),
                    
                    // IPA notation toggle
                    SwitchListTile(
                      title: Text(
                        'IPA Notation',
                        style: TextStyle(
                          fontSize: 16.sp,
                          color: AppTheme.textPrimaryColor,
                        ),
                      ),
                      subtitle: Text(
                        'Show International Phonetic Alphabet notation',
                        style: TextStyle(
                          fontSize: 12.sp,
                          color: AppTheme.textSecondaryColor,
                        ),
                      ),
                      value: useIpaNotation,
                      onChanged: usePronunciationGuidance ? (value) {
                        ref.read(pronunciationNotifierProvider.notifier).setUseIpaNotation(value);
                      } : null,
                      activeColor: Colors.teal,
                    ),
                    
                    // Simplified phonetics toggle
                    SwitchListTile(
                      title: Text(
                        'Simplified Phonetics',
                        style: TextStyle(
                          fontSize: 16.sp,
                          color: AppTheme.textPrimaryColor,
                        ),
                      ),
                      subtitle: Text(
                        'Show simplified phonetic spelling',
                        style: TextStyle(
                          fontSize: 12.sp,
                          color: AppTheme.textSecondaryColor,
                        ),
                      ),
                      value: useSimplifiedPhonetics,
                      onChanged: usePronunciationGuidance ? (value) {
                        ref.read(pronunciationNotifierProvider.notifier).setUseSimplifiedPhonetics(value);
                      } : null,
                      activeColor: Colors.teal,
                    ),
                    
                    // Syllable breakdown toggle
                    SwitchListTile(
                      title: Text(
                        'Syllable Breakdown',
                        style: TextStyle(
                          fontSize: 16.sp,
                          color: AppTheme.textPrimaryColor,
                        ),
                      ),
                      subtitle: Text(
                        'Show breakdown by syllables with stress marks',
                        style: TextStyle(
                          fontSize: 12.sp,
                          color: AppTheme.textSecondaryColor,
                        ),
                      ),
                      value: useSyllableBreakdown,
                      onChanged: usePronunciationGuidance ? (value) {
                        ref.read(pronunciationNotifierProvider.notifier).setUseSyllableBreakdown(value);
                      } : null,
                      activeColor: Colors.teal,
                    ),
                  ],
                ),
              ),
            ),
            
            SizedBox(height: 24.h),
            
            // Guide types
            Card(
              elevation: 2,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12.r),
              ),
              child: Padding(
                padding: EdgeInsets.all(16.r),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'About Pronunciation Guide Types',
                      style: TextStyle(
                        fontSize: 18.sp,
                        fontWeight: FontWeight.bold,
                        color: AppTheme.textPrimaryColor,
                      ),
                    ),
                    
                    SizedBox(height: 16.h),
                    
                    ...PronunciationGuideType.values.map((type) {
                      return _buildGuideTypeItem(type);
                    }).toList(),
                  ],
                ),
              ),
            ),
            
            SizedBox(height: 24.h),
            
            // Difficulty levels
            Card(
              elevation: 2,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12.r),
              ),
              child: Padding(
                padding: EdgeInsets.all(16.r),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Difficulty Levels',
                      style: TextStyle(
                        fontSize: 18.sp,
                        fontWeight: FontWeight.bold,
                        color: AppTheme.textPrimaryColor,
                      ),
                    ),
                    
                    SizedBox(height: 16.h),
                    
                    ...PronunciationDifficulty.values.map((difficulty) {
                      return _buildDifficultyLevelItem(difficulty);
                    }).toList(),
                  ],
                ),
              ),
            ),
            
            SizedBox(height: 24.h),
            
            // Clear cache button
            Center(
              child: ElevatedButton.icon(
                onPressed: () {
                  ref.read(pronunciationNotifierProvider.notifier).clearCache();
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('Pronunciation cache cleared'),
                      duration: Duration(seconds: 2),
                    ),
                  );
                },
                icon: const Icon(Icons.delete_outline),
                label: const Text('Clear Pronunciation Cache'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.red,
                  foregroundColor: Colors.white,
                  padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
  
  /// Build a guide type item
  Widget _buildGuideTypeItem(PronunciationGuideType type) {
    return Padding(
      padding: EdgeInsets.only(bottom: 16.h),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: EdgeInsets.all(8.r),
            decoration: BoxDecoration(
              color: Colors.teal.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8.r),
            ),
            child: Icon(
              type.icon,
              size: 24.r,
              color: Colors.teal,
            ),
          ),
          SizedBox(width: 16.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  type.displayName,
                  style: TextStyle(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.bold,
                    color: AppTheme.textPrimaryColor,
                  ),
                ),
                SizedBox(height: 4.h),
                Text(
                  type.description,
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: AppTheme.textSecondaryColor,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
  
  /// Build a difficulty level item
  Widget _buildDifficultyLevelItem(PronunciationDifficulty difficulty) {
    return Padding(
      padding: EdgeInsets.only(bottom: 16.h),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: EdgeInsets.all(8.r),
            decoration: BoxDecoration(
              color: difficulty.color.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8.r),
            ),
            child: Icon(
              difficulty.icon,
              size: 24.r,
              color: difficulty.color,
            ),
          ),
          SizedBox(width: 16.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  difficulty.displayName,
                  style: TextStyle(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.bold,
                    color: AppTheme.textPrimaryColor,
                  ),
                ),
                SizedBox(height: 4.h),
                Text(
                  _getDifficultyLevelDescription(difficulty),
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: AppTheme.textSecondaryColor,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
  
  /// Get the description for a difficulty level
  String _getDifficultyLevelDescription(PronunciationDifficulty difficulty) {
    switch (difficulty) {
      case PronunciationDifficulty.veryEasy:
        return 'Sounds that are very similar to English or are intuitive to pronounce.';
      case PronunciationDifficulty.easy:
        return 'Sounds that are somewhat similar to English with minor differences.';
      case PronunciationDifficulty.moderate:
        return 'Sounds that require some practice but are achievable with effort.';
      case PronunciationDifficulty.difficult:
        return 'Sounds that are significantly different from English and require dedicated practice.';
      case PronunciationDifficulty.veryDifficult:
        return 'Sounds that are not found in English and require extensive practice to master.';
    }
  }
}
