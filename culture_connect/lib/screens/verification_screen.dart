import 'dart:async';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:culture_connect/theme/app_theme.dart';
import 'package:culture_connect/widgets/custom_app_bar.dart';
import 'package:culture_connect/widgets/custom_button.dart';
import 'package:cloud_firestore/cloud_firestore.dart'; // Added missing import

class VerificationScreen extends StatefulWidget {
  const VerificationScreen({super.key});

  @override
  State<VerificationScreen> createState() => _VerificationScreenState();
}

class _VerificationScreenState extends State<VerificationScreen> {
  String? _errorMessage;
  String? _successMessage;
  bool _isVerified = false;
  bool _isLoading = false;
  bool _isRedirecting = false;
  Timer? _timer;
  Timer? _redirectTimer;
  final int _checkInterval = 2; // Check every 2 seconds for faster response
  final int _redirectDelay = 2; // Redirect after 2 seconds of verification

  @override
  void initState() {
    super.initState();
    // Start periodic verification check
    _startVerificationCheck();
  }

  @override
  void dispose() {
    _timer?.cancel();
    _redirectTimer?.cancel();
    super.dispose();
  }

  void _startVerificationCheck() {
    // Check immediately on start with multiple approaches
    _checkEmailVerification();

    // Force token refresh on initial check to ensure latest claims
    _forceTokenRefreshAndCheck();

    // Then set up periodic checks
    _timer = Timer.periodic(Duration(seconds: _checkInterval), (timer) {
      _checkEmailVerification();
    });
  }

  Future<void> _forceTokenRefreshAndCheck() async {
    try {
      User? user = FirebaseAuth.instance.currentUser;
      if (user == null) return;

      // Force token refresh to get latest claims
      await user.getIdToken(true);
      await _checkEmailVerification();
    } catch (e) {
      debugPrint('Token refresh failed: $e');
    }
  }

  Future<void> _checkEmailVerification() async {
    try {
      // Get current user
      User? user = FirebaseAuth.instance.currentUser;

      if (user == null) {
        debugPrint('No user is currently signed in');
        return;
      }

      try {
        // Reload user to get latest verification status
        await user.reload();
        user = FirebaseAuth.instance.currentUser; // Get refreshed user

        // Check if email is verified
        if (user!.emailVerified && !_isVerified) {
          _handleVerificationSuccess();
        }
      } catch (reloadError) {
        debugPrint('Error reloading user: $reloadError');
        debugPrint('Error type: ${reloadError.runtimeType}');

        // Special handling for PigeonUserInfo/PigeonUserDetails errors
        if (reloadError.toString().contains('PigeonUserInfo') ||
            reloadError.toString().contains('PigeonUserDetails') ||
            reloadError.toString().contains('List<object?>')) {
          debugPrint(
              'Detected PigeonUser type casting error during verification check');

          // Try alternative approach to check verification
          await _alternativeVerificationCheck();
        }
      }
    } catch (e) {
      debugPrint('Error in main verification check: $e');
      debugPrint('Error type: ${e.runtimeType}');
    }
  }

  // Alternative method to check verification when the standard method fails
  Future<void> _alternativeVerificationCheck() async {
    try {
      debugPrint('Attempting alternative verification check...');

      // Try multiple approaches to verify email status

      User? user = FirebaseAuth.instance.currentUser;
      if (user == null) return;

      // Approach 1: Get ID token result with force refresh
      try {
        debugPrint('Trying ID token result with force refresh...');
        final idTokenResult = await user.getIdTokenResult(true);
        final emailVerified = idTokenResult.claims?['email_verified'];

        if (emailVerified == true && !_isVerified) {
          debugPrint('Email verified via ID token claims');
          _handleVerificationSuccess();
          return;
        }
      } catch (tokenError) {
        debugPrint('ID token approach failed: $tokenError');
      }

      // Approach 2: Get unverified user token to check email status
      try {
        debugPrint('Trying unverified user token approach...');
        final unverifiedToken = await user.getIdToken(false);
        final idTokenResult = await user
            .getIdTokenResult(); // Replaced verifyIdToken with getIdTokenResult
        final emailVerified = idTokenResult.claims?['email_verified'] ?? false;

        if (emailVerified == true && !_isVerified) {
          debugPrint('Email verified via unverified token claims');
          _handleVerificationSuccess();
          return;
        }
      } catch (unverifiedError) {
        debugPrint('Unverified token approach failed: $unverifiedError');
      }

      // Approach 3: Check Firestore document if available
      try {
        debugPrint('Trying Firestore document check...');
        final userDoc = await FirebaseFirestore.instance
            .collection('users')
            .doc(user.uid)
            .get();

        final emailVerified = userDoc.data()?['emailVerified'] ?? false;
        if (emailVerified == true && !_isVerified) {
          debugPrint('Email verified via Firestore document');
          _handleVerificationSuccess();
          return;
        }
      } catch (firestoreError) {
        debugPrint('Firestore approach failed: $firestoreError');
      }
    } catch (e) {
      debugPrint('Error in alternative verification check: $e');
      debugPrint('Error type: ${e.runtimeType}');
    }
  }

  // Handle successful verification
  void _handleVerificationSuccess() {
    debugPrint('Email verified successfully!');

    // Update UI to show verification success
    setState(() {
      _isVerified = true;
      _successMessage = 'Email verified successfully!';
      _errorMessage = null;
    });

    // Cancel the periodic check timer
    _timer?.cancel();

    // Set up redirect timer
    _redirectTimer = Timer(Duration(seconds: _redirectDelay), () {
      setState(() {
        _isRedirecting = true;
      });

      // Navigate to home screen
      Navigator.pushReplacementNamed(context, '/home');
    });
  }

  Future<void> _manuallyCheckVerification() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
      _successMessage = null;
    });

    try {
      debugPrint('Manual verification check initiated by user');

      // Get current user
      User? user = FirebaseAuth.instance.currentUser;

      if (user == null) {
        setState(() {
          _errorMessage = 'No user is currently signed in';
          _isLoading = false;
        });
        return;
      }

      // Try multiple verification check approaches
      bool verificationSuccess = false;

      // Approach 1: Standard reload and check
      try {
        debugPrint('Trying standard verification check approach...');
        await user.reload();
        user = FirebaseAuth.instance.currentUser;

        if (user!.emailVerified) {
          verificationSuccess = true;
          debugPrint('Standard verification check successful');
        }
      } catch (standardError) {
        debugPrint('Standard verification check failed: $standardError');
        // Continue to next approach
      }

      // Approach 2: Token-based check if standard approach failed
      if (!verificationSuccess && user != null) {
        try {
          debugPrint('Trying token-based verification check...');
          final idTokenResult = await user.getIdTokenResult(true);
          final emailVerified = idTokenResult.claims?['email_verified'];

          if (emailVerified == true) {
            verificationSuccess = true;
            debugPrint('Token-based verification check successful');
          }
        } catch (tokenError) {
          debugPrint('Token-based verification check failed: $tokenError');
          // Continue to next approach
        }
      }

      // Approach 3: Force token refresh and check again
      if (!verificationSuccess && user != null) {
        try {
          debugPrint('Trying force token refresh approach...');
          await user.getIdToken(true); // Force refresh token
          final newIdTokenResult = await user.getIdTokenResult();
          final emailVerified = newIdTokenResult.claims?['email_verified'];

          if (emailVerified == true) {
            verificationSuccess = true;
            debugPrint('Force token refresh verification check successful');
          }
        } catch (refreshError) {
          debugPrint(
              'Force token refresh verification check failed: $refreshError');
        }
      }

      // Handle verification result
      if (verificationSuccess) {
        _handleVerificationSuccess();
      } else {
        setState(() {
          _errorMessage =
              'Email not verified yet. Please check your inbox and click the verification link.';
          _isLoading = false;
        });
      }
    } catch (e) {
      debugPrint('Error in manual verification check: $e');
      debugPrint('Error type: ${e.runtimeType}');

      setState(() {
        _errorMessage = 'Error checking verification status: $e';
        _isLoading = false;
      });
    }
  }

  Future<void> _resendVerificationEmail() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      User? user = FirebaseAuth.instance.currentUser;

      if (user == null) {
        setState(() {
          _errorMessage = 'No user is currently signed in';
        });
        return;
      }

      await user.sendEmailVerification();

      setState(() {
        _successMessage =
            'A new verification email has been sent to ${user.email}';
      });
    } catch (e) {
      setState(() {
        _errorMessage = 'Error sending verification email: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const CustomAppBar(
        title: 'Email Verification',
        showBackButton: false, // Prevent going back to registration
      ),
      body: SafeArea(
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: 24.w),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Email icon
              Icon(
                _isVerified ? Icons.mark_email_read : Icons.mark_email_unread,
                size: 80.sp,
                color:
                    _isVerified ? AppTheme.successColor : AppTheme.primaryColor,
              ),
              SizedBox(height: 24.h),

              // Title
              Text(
                _isVerified ? 'Email Verified!' : 'Verify Your Email',
                style: TextStyle(
                  fontSize: 24.sp,
                  fontWeight: FontWeight.bold,
                  color: AppTheme.textPrimaryColor,
                ),
                textAlign: TextAlign.center,
              ),
              SizedBox(height: 16.h),

              // Description
              Text(
                _isVerified
                    ? 'Your email has been verified successfully. You will be redirected to the home screen shortly.'
                    : 'We\'ve sent a verification email to your inbox. Please check your email and click the verification link to activate your account.',
                style: TextStyle(
                  fontSize: 16.sp,
                  color: AppTheme.textSecondaryColor,
                  height: 1.5,
                ),
                textAlign: TextAlign.center,
              ),
              SizedBox(height: 32.h),

              // Progress indicator for verification check
              if (_isVerified && _isRedirecting)
                Column(
                  children: [
                    const CircularProgressIndicator(
                      valueColor:
                          AlwaysStoppedAnimation<Color>(AppTheme.primaryColor),
                    ),
                    SizedBox(height: 16.h),
                    Text(
                      'Redirecting to home screen...',
                      style: TextStyle(
                        fontSize: 14.sp,
                        color: AppTheme.textSecondaryColor,
                      ),
                    ),
                  ],
                ),

              // Success message
              if (_successMessage != null && !_isVerified)
                Container(
                  padding: EdgeInsets.all(16.r),
                  margin: EdgeInsets.only(bottom: 24.h),
                  decoration: BoxDecoration(
                    color: AppTheme.successColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12.r),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.check_circle,
                        color: AppTheme.successColor,
                        size: 24.sp,
                      ),
                      SizedBox(width: 12.w),
                      Expanded(
                        child: Text(
                          _successMessage!,
                          style: TextStyle(
                            color: AppTheme.successColor,
                            fontSize: 14.sp,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),

              // Error message
              if (_errorMessage != null)
                Container(
                  padding: EdgeInsets.all(16.r),
                  margin: EdgeInsets.only(bottom: 24.h),
                  decoration: BoxDecoration(
                    color: AppTheme.errorColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12.r),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.error_outline,
                        color: AppTheme.errorColor,
                        size: 24.sp,
                      ),
                      SizedBox(width: 12.w),
                      Expanded(
                        child: Text(
                          _errorMessage!,
                          style: TextStyle(
                            color: AppTheme.errorColor,
                            fontSize: 14.sp,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),

              // Action buttons (only show if not verified)
              if (!_isVerified) ...[
                SizedBox(height: 16.h),

                // Check verification status button
                CustomButton(
                  text: 'Check Verification Status',
                  onPressed: _manuallyCheckVerification,
                  isLoading: _isLoading,
                  type: ButtonType.primary,
                ),
                SizedBox(height: 16.h),

                // Resend verification email button
                CustomButton(
                  text: 'Resend Verification Email',
                  onPressed: _resendVerificationEmail,
                  isLoading: _isLoading,
                  type: ButtonType.outlined,
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }
}
