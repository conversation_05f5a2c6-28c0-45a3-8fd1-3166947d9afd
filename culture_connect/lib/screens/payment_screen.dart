import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:culture_connect/models/booking.dart';
import 'package:culture_connect/models/payment_method.dart';
import 'package:culture_connect/models/payment/payment_method_model.dart';
import 'package:culture_connect/services/payment_service.dart';
import 'package:culture_connect/widgets/payment/payment_method_selector.dart';
import 'package:culture_connect/widgets/payment/payment_summary.dart';
import 'package:culture_connect/widgets/payment/payment_confirmation.dart';
import 'package:culture_connect/screens/booking_confirmation_screen.dart';

class PaymentScreen extends ConsumerStatefulWidget {
  final Booking booking;
  final String userEmail;
  final String userName;
  final String? userPhone;

  const PaymentScreen({
    super.key,
    required this.booking,
    required this.userEmail,
    required this.userName,
    this.userPhone,
  });

  @override
  ConsumerState<PaymentScreen> createState() => _PaymentScreenState();
}

class _PaymentScreenState extends ConsumerState<PaymentScreen> {
  final _paymentService = PaymentService();
  PaymentProvider _selectedProvider = PaymentProvider.stripe;
  PaymentMethod? _selectedPaymentMethod;
  bool _isProcessing = false;
  String? _errorMessage;
  bool _showConfirmation = false;
  String? _transactionId;
  String? _receiptId;

  @override
  void initState() {
    super.initState();
    _loadPaymentMethods();
  }

  Future<void> _loadPaymentMethods() async {
    // Payment method management has been simplified
    // In a real implementation, this would load saved payment methods
  }

  void _onPaymentMethodSelected(PaymentMethodModel paymentMethod) {
    setState(() {
      // Convert PaymentMethodModel to PaymentMethod if needed
      // For now, we'll store the model directly and update the field type
      _selectedPaymentMethod = paymentMethod as PaymentMethod?;
    });
  }

  Future<void> _processPayment() async {
    setState(() {
      _isProcessing = true;
      _errorMessage = null;
    });

    try {
      final result = await _paymentService.processPayment(
        booking: widget.booking,
        provider: _selectedProvider,
        userEmail: widget.userEmail,
        userName: widget.userName,
        userPhone: widget.userPhone,
        paymentMethodId: _selectedPaymentMethod?.id,
      );

      if (result.success) {
        setState(() {
          _showConfirmation = true;
          _transactionId = result.transactionId;
          _receiptId = result.additionalData?['receiptId'] as String?;
        });
      } else {
        setState(() {
          _errorMessage = result.errorMessage ?? 'Payment failed';
        });
      }
    } catch (e) {
      setState(() {
        _errorMessage = e.toString();
      });
    } finally {
      setState(() {
        _isProcessing = false;
      });
    }
  }

  void _viewReceipt() {
    if (_receiptId == null) return;

    // In a real app, you would navigate to a receipt view screen
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Receipt viewer not implemented yet'),
      ),
    );
  }

  Future<void> _shareReceipt() async {
    if (_receiptId == null) return;

    // Receipt sharing functionality has been simplified
    // In a real implementation, this would generate and share a PDF receipt
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
            content: Text('Receipt sharing not available in this version')),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Payment'),
      ),
      body: _showConfirmation ? _buildConfirmationView() : _buildPaymentView(),
    );
  }

  Widget _buildConfirmationView() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (_transactionId != null && _receiptId != null)
            PaymentConfirmation(
              transactionId: _transactionId!,
              receiptId: _receiptId!,
              onViewReceipt: _viewReceipt,
              onShareReceipt: _shareReceipt,
            ),
          const SizedBox(height: 24),
          Center(
            child: ElevatedButton(
              onPressed: () {
                Navigator.pushReplacement(
                  context,
                  MaterialPageRoute(
                    builder: (context) => BookingConfirmationScreen(
                      booking: widget.booking,
                      transactionId: _transactionId ?? 'unknown',
                    ),
                  ),
                );
              },
              child: const Text('View Booking Details'),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPaymentView() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Payment Summary
          PaymentSummary(
            booking: widget.booking,
            taxRate: 10.0, // Example tax rate
            currencySymbol: '\$',
          ),

          const SizedBox(height: 24),

          // Payment Method Selector
          PaymentMethodSelector(
            onPaymentMethodSelected: _onPaymentMethodSelected,
          ),

          const SizedBox(height: 24),

          // Payment Provider Selection
          Text(
            'Select Payment Provider',
            style: Theme.of(context).textTheme.titleMedium,
          ),
          const SizedBox(height: 16),
          Card(
            child: Column(
              children: [
                RadioListTile<PaymentProvider>(
                  title: const Text('Stripe (International)'),
                  subtitle:
                      const Text('Credit/Debit Cards, Apple Pay, Google Pay'),
                  value: PaymentProvider.stripe,
                  groupValue: _selectedProvider,
                  onChanged: (value) {
                    setState(() {
                      _selectedProvider = value!;
                    });
                  },
                ),
                RadioListTile<PaymentProvider>(
                  title: const Text('Flutterwave (Africa)'),
                  subtitle: const Text(
                      'Local Cards, USSD, Bank Transfers, Mobile Money'),
                  value: PaymentProvider.flutterwave,
                  groupValue: _selectedProvider,
                  onChanged: (value) {
                    setState(() {
                      _selectedProvider = value!;
                    });
                  },
                ),
                RadioListTile<PaymentProvider>(
                  title: const Text('PayPal'),
                  subtitle: const Text('PayPal Balance, Credit/Debit Cards'),
                  value: PaymentProvider.paypal,
                  groupValue: _selectedProvider,
                  onChanged: (value) {
                    setState(() {
                      _selectedProvider = value!;
                    });
                  },
                ),
                RadioListTile<PaymentProvider>(
                  title: const Text('M-Pesa'),
                  subtitle: const Text('Mobile Money (Kenya)'),
                  value: PaymentProvider.mpesa,
                  groupValue: _selectedProvider,
                  onChanged: (value) {
                    setState(() {
                      _selectedProvider = value!;
                    });
                  },
                ),
              ],
            ),
          ),
          const SizedBox(height: 24),

          // Error Message
          if (_errorMessage != null)
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.errorContainer,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.error_outline,
                    color: Theme.of(context).colorScheme.error,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      _errorMessage!,
                      style: TextStyle(
                        color: Theme.of(context).colorScheme.error,
                      ),
                    ),
                  ),
                ],
              ),
            ),

          const SizedBox(height: 24),

          // Pay Button
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: _isProcessing ? null : () => _processPayment(),
              style: ElevatedButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 16),
              ),
              child: _isProcessing
                  ? const Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            color: Colors.white,
                          ),
                        ),
                        SizedBox(width: 12),
                        Text('Processing...'),
                      ],
                    )
                  : const Text('Pay Now'),
            ),
          ),
        ],
      ),
    );
  }
}
