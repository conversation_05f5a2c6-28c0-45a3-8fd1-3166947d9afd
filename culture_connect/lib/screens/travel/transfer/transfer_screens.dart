/// This file exports all transfer-related screens
///
/// Note: We're hiding the type aliases to avoid conflicts between files.
/// This approach allows each file to define its own type aliases for clarity
/// while preventing naming conflicts when importing multiple files.
library culture_connect.screens.travel.transfer;

// Export all transfer-related screens, hiding their type aliases
export 'transfer_services_screen.dart' hide TransferService;
export 'transfer_details_screen.dart'
    hide TransferService, TransferVehicleType, TransferDriver;
export 'transfer_booking_screen.dart'
    hide
        TransferBooking,
        TransferBookingStatus,
        TransferLocation,
        TransferLocationType,
        TransferServiceModel;
export 'transfer_booking_details_screen.dart'
    hide TransferBooking, TransferBookingStatus;
export 'transfer_booking_confirmation_screen.dart'
    hide TransferBooking, TransferBookingStatus;
export 'transfer_bookings_screen.dart' hide TransferBooking;
export 'transfer_search_screen.dart' hide TransferService, TransferVehicleType;
