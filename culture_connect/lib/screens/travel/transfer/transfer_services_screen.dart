import 'package:flutter/material.dart';
import 'package:culture_connect/models/travel/transfer/transfers.dart'
    as models;
import 'package:culture_connect/providers/travel/transfer/transfer_provider.dart';
import 'package:culture_connect/providers/travel/transfer/transfer_location_provider.dart';
import 'package:culture_connect/screens/travel/transfer/transfer_details_screen.dart';
import 'package:culture_connect/screens/travel/transfer/transfer_bookings_screen.dart';
import 'package:culture_connect/screens/travel/transfer/transfer_search_screen.dart';
import 'package:culture_connect/theme/app_text_styles.dart';
import 'package:culture_connect/widgets/common/empty_state.dart';
import 'package:culture_connect/widgets/common/error_state.dart';
import 'package:culture_connect/widgets/travel/transfer/transfer_widgets.dart';
import 'package:provider/provider.dart';

// Type aliases to make the code more readable
typedef TransferService = models.TransferService;

/// A screen for displaying transfer services
class TransferServicesScreen extends StatefulWidget {
  /// Creates a new transfer services screen
  const TransferServicesScreen({super.key});

  @override
  State<TransferServicesScreen> createState() => _TransferServicesScreenState();
}

class _TransferServicesScreenState extends State<TransferServicesScreen> {
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  /// Load data from providers
  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final transferProvider =
          Provider.of<TransferProvider?>(context, listen: false);
      final locationProvider =
          Provider.of<TransferLocationProvider?>(context, listen: false);

      if (transferProvider != null && locationProvider != null) {
        await Future.wait([
          transferProvider.initialize(),
          locationProvider.initialize(),
        ]);
      }

      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Airport Transfers'),
        actions: [
          IconButton(
            icon: const Icon(Icons.history),
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const TransferBookingsScreen(),
                ),
              );
            },
            tooltip: 'My Bookings',
          ),
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const TransferSearchScreen(),
                ),
              );
            },
            tooltip: 'Search',
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : RefreshIndicator(
              onRefresh: _loadData,
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildSearchCard(),
                    const SizedBox(height: 24),
                    _buildFeaturedTransfers(),
                    const SizedBox(height: 24),
                    _buildTransfersOnSale(),
                    const SizedBox(height: 24),
                    _buildAllTransfers(),
                  ],
                ),
              ),
            ),
    );
  }

  /// Build the search card
  Widget _buildSearchCard() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Find Airport Transfers',
              style: AppTextStyles.headline6,
            ),
            const SizedBox(height: 16),
            TextField(
              decoration: const InputDecoration(
                hintText: 'Enter airport, hotel, or address',
                prefixIcon: Icon(Icons.search),
                border: OutlineInputBorder(),
              ),
              onTap: () {
                // Navigate to search screen
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const TransferSearchScreen(),
                  ),
                );
              },
              readOnly: true,
            ),
            const SizedBox(height: 16),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const TransferSearchScreen(),
                    ),
                  );
                },
                child: const Text('Search Transfers'),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Build the featured transfers section
  Widget _buildFeaturedTransfers() {
    return Consumer<TransferProvider?>(
      builder: (context, provider, child) {
        if (provider == null || provider.isLoading) {
          return const Center(child: CircularProgressIndicator());
        }

        if (provider.error != null) {
          return ErrorState(
            message: provider.error!,
            onRetry: () => provider.loadTransfers(),
          );
        }

        final featuredTransfers = provider.featuredTransfers;

        if (featuredTransfers.isEmpty) {
          return const SizedBox();
        }

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Featured Transfers',
                  style: AppTextStyles.headline6,
                ),
                TextButton(
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const TransferSearchScreen(),
                      ),
                    );
                  },
                  child: const Text('See All'),
                ),
              ],
            ),
            const SizedBox(height: 16),
            SizedBox(
              height: 320,
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                itemCount: featuredTransfers.length,
                itemBuilder: (context, index) {
                  final transfer = featuredTransfers[index];
                  return SizedBox(
                    width: 280,
                    child: Padding(
                      padding: const EdgeInsets.only(right: 16),
                      child: TransferCard(
                        transfer: transfer,
                        onTap: () {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => TransferDetailsScreen(
                                transferId: transfer.id,
                              ),
                            ),
                          );
                        },
                      ),
                    ),
                  );
                },
              ),
            ),
          ],
        );
      },
    );
  }

  /// Build the transfers on sale section
  Widget _buildTransfersOnSale() {
    return Consumer<TransferProvider?>(
      builder: (context, provider, child) {
        if (provider == null || provider.isLoading) {
          return const Center(child: CircularProgressIndicator());
        }

        if (provider.error != null) {
          return ErrorState(
            message: provider.error!,
            onRetry: () => provider.loadTransfers(),
          );
        }

        final transfersOnSale = provider.transfersOnSale;

        if (transfersOnSale.isEmpty) {
          return const SizedBox();
        }

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Special Offers',
                  style: AppTextStyles.headline6,
                ),
                TextButton(
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const TransferSearchScreen(),
                      ),
                    );
                  },
                  child: const Text('See All'),
                ),
              ],
            ),
            const SizedBox(height: 16),
            SizedBox(
              height: 320,
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                itemCount: transfersOnSale.length,
                itemBuilder: (context, index) {
                  final transfer = transfersOnSale[index];
                  return SizedBox(
                    width: 280,
                    child: Padding(
                      padding: const EdgeInsets.only(right: 16),
                      child: TransferCard(
                        transfer: transfer,
                        onTap: () {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => TransferDetailsScreen(
                                transferId: transfer.id,
                              ),
                            ),
                          );
                        },
                      ),
                    ),
                  );
                },
              ),
            ),
          ],
        );
      },
    );
  }

  /// Build the all transfers section
  Widget _buildAllTransfers() {
    return Consumer<TransferProvider?>(
      builder: (context, provider, child) {
        if (provider == null || provider.isLoading) {
          return const Center(child: CircularProgressIndicator());
        }

        if (provider.error != null) {
          return ErrorState(
            message: provider.error!,
            onRetry: () => provider.loadTransfers(),
          );
        }

        final transfers = provider.transfers;

        if (transfers.isEmpty) {
          return const EmptyState(
            icon: Icons.airport_shuttle,
            title: 'No Transfers Available',
            message: 'There are no transfer services available at the moment.',
          );
        }

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'All Transfers',
                  style: AppTextStyles.headline6,
                ),
                TextButton(
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const TransferSearchScreen(),
                      ),
                    );
                  },
                  child: const Text('See All'),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ListView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: transfers.length > 3 ? 3 : transfers.length,
              itemBuilder: (context, index) {
                final transfer = transfers[index];
                return Padding(
                  padding: const EdgeInsets.only(bottom: 16),
                  child: TransferCard(
                    transfer: transfer,
                    onTap: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => TransferDetailsScreen(
                            transferId: transfer.id,
                          ),
                        ),
                      );
                    },
                  ),
                );
              },
            ),
            if (transfers.length > 3)
              Center(
                child: TextButton(
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const TransferSearchScreen(),
                      ),
                    );
                  },
                  child: const Text('View More'),
                ),
              ),
          ],
        );
      },
    );
  }
}
