import 'package:flutter/material.dart';
import 'package:culture_connect/models/travel/transfer/transfers.dart'
    as models;
import 'package:culture_connect/providers/travel/transfer/transfer_provider.dart';
import 'package:culture_connect/screens/travel/transfer/transfer_booking_details_screen.dart';
import 'package:culture_connect/theme/app_colors.dart';
import 'package:culture_connect/theme/app_text_styles.dart';
import 'package:culture_connect/widgets/common/empty_state.dart';
import 'package:culture_connect/widgets/common/error_state.dart';
import 'package:culture_connect/widgets/travel/transfer/transfer_booking_card.dart';
import 'package:provider/provider.dart';

// Type aliases to make the code more readable
typedef TransferBooking = models.TransferBooking;

/// A screen for displaying transfer bookings
class TransferBookingsScreen extends StatefulWidget {
  /// Creates a new transfer bookings screen
  const TransferBookingsScreen({super.key});

  @override
  State<TransferBookingsScreen> createState() => _TransferBookingsScreenState();
}

class _TransferBookingsScreenState extends State<TransferBookingsScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  /// Load data from providers
  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final transferProvider =
          Provider.of<TransferProvider>(context, listen: false);
      await transferProvider.loadBookings();

      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('My Bookings'),
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'Upcoming'),
            Tab(text: 'Past'),
            Tab(text: 'Cancelled'),
          ],
        ),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : TabBarView(
              controller: _tabController,
              children: [
                _buildUpcomingBookingsTab(),
                _buildPastBookingsTab(),
                _buildCancelledBookingsTab(),
              ],
            ),
    );
  }

  /// Build the upcoming bookings tab
  Widget _buildUpcomingBookingsTab() {
    return Consumer<TransferProvider>(
      builder: (context, provider, child) {
        if (provider.isLoading) {
          return const Center(child: CircularProgressIndicator());
        }

        if (provider.error != null) {
          return ErrorState(
            message: provider.error!,
            onRetry: () => provider.loadBookings(),
          );
        }

        final upcomingBookings = provider.upcomingBookings;

        if (upcomingBookings.isEmpty) {
          return const EmptyState(
            icon: Icons.airport_shuttle,
            title: 'No Upcoming Bookings',
            message: 'You don\'t have any upcoming transfer bookings.',
          );
        }

        return RefreshIndicator(
          onRefresh: () => provider.loadBookings(),
          child: ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: upcomingBookings.length,
            itemBuilder: (context, index) {
              final booking = upcomingBookings[index];
              return Padding(
                padding: const EdgeInsets.only(bottom: 16),
                child: TransferBookingCard(
                  booking: booking,
                  onTap: () {
                    _navigateToBookingDetails(booking);
                  },
                  onCancel: () {
                    _showCancelConfirmationDialog(booking);
                  },
                ),
              );
            },
          ),
        );
      },
    );
  }

  /// Build the past bookings tab
  Widget _buildPastBookingsTab() {
    return Consumer<TransferProvider>(
      builder: (context, provider, child) {
        if (provider.isLoading) {
          return const Center(child: CircularProgressIndicator());
        }

        if (provider.error != null) {
          return ErrorState(
            message: provider.error!,
            onRetry: () => provider.loadBookings(),
          );
        }

        final pastBookings = provider.pastBookings;

        if (pastBookings.isEmpty) {
          return const EmptyState(
            icon: Icons.history,
            title: 'No Past Bookings',
            message: 'You don\'t have any past transfer bookings.',
          );
        }

        return RefreshIndicator(
          onRefresh: () => provider.loadBookings(),
          child: ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: pastBookings.length,
            itemBuilder: (context, index) {
              final booking = pastBookings[index];
              return Padding(
                padding: const EdgeInsets.only(bottom: 16),
                child: TransferBookingCard(
                  booking: booking,
                  onTap: () {
                    _navigateToBookingDetails(booking);
                  },
                  onCancel: null,
                ),
              );
            },
          ),
        );
      },
    );
  }

  /// Build the cancelled bookings tab
  Widget _buildCancelledBookingsTab() {
    return Consumer<TransferProvider>(
      builder: (context, provider, child) {
        if (provider.isLoading) {
          return const Center(child: CircularProgressIndicator());
        }

        if (provider.error != null) {
          return ErrorState(
            message: provider.error!,
            onRetry: () => provider.loadBookings(),
          );
        }

        final cancelledBookings = provider.cancelledBookings;

        if (cancelledBookings.isEmpty) {
          return const EmptyState(
            icon: Icons.cancel,
            title: 'No Cancelled Bookings',
            message: 'You don\'t have any cancelled transfer bookings.',
          );
        }

        return RefreshIndicator(
          onRefresh: () => provider.loadBookings(),
          child: ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: cancelledBookings.length,
            itemBuilder: (context, index) {
              final booking = cancelledBookings[index];
              return Padding(
                padding: const EdgeInsets.only(bottom: 16),
                child: TransferBookingCard(
                  booking: booking,
                  onTap: () {
                    _navigateToBookingDetails(booking);
                  },
                  onCancel: null,
                ),
              );
            },
          ),
        );
      },
    );
  }

  /// Navigate to booking details
  void _navigateToBookingDetails(TransferBooking booking) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => TransferBookingDetailsScreen(
          booking: booking,
        ),
      ),
    );
  }

  /// Show a confirmation dialog before cancelling
  void _showCancelConfirmationDialog(TransferBooking booking) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Cancel Booking'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Are you sure you want to cancel this booking?',
              style: AppTextStyles.body1,
            ),
            const SizedBox(height: 16),
            Text(
              'Cancellation Policy:',
              style: AppTextStyles.subtitle2.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              booking.transferService?.cancellationPolicy ??
                  'Free cancellation up to 24 hours before pickup.',
              style: AppTextStyles.body2,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('KEEP BOOKING'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _cancelBooking(booking);
            },
            style: TextButton.styleFrom(
              foregroundColor: AppColors.error,
            ),
            child: const Text('CANCEL BOOKING'),
          ),
        ],
      ),
    );
  }

  /// Cancel a booking
  Future<void> _cancelBooking(TransferBooking booking) async {
    setState(() {
      _isLoading = true;
    });

    try {
      final transferProvider =
          Provider.of<TransferProvider>(context, listen: false);
      await transferProvider.cancelBooking(booking.id);

      setState(() {
        _isLoading = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Booking cancelled successfully'),
            backgroundColor: AppColors.success,
          ),
        );
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: $e'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }
}
