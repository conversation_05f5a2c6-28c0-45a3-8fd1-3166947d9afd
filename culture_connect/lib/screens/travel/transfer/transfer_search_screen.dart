import 'package:flutter/material.dart';
import 'package:culture_connect/models/travel/transfer/transfers.dart'
    as models;
import 'package:culture_connect/providers/travel/transfer/transfer_provider.dart';
import 'package:culture_connect/providers/travel/transfer/transfer_location_provider.dart';
import 'package:culture_connect/screens/travel/transfer/transfer_details_screen.dart';
import 'package:culture_connect/theme/app_colors.dart';
import 'package:culture_connect/theme/app_text_styles.dart';
import 'package:culture_connect/widgets/common/empty_state.dart';
import 'package:culture_connect/widgets/common/error_state.dart';
import 'package:culture_connect/widgets/travel/transfer/transfer_widgets.dart';
import 'package:provider/provider.dart';

// Type aliases to make the code more readable
typedef TransferService = models.TransferService;
typedef TransferVehicleType = models.TransferVehicleType;

/// A screen for searching transfer services
class TransferSearchScreen extends StatefulWidget {
  /// Creates a new transfer search screen
  const TransferSearchScreen({super.key});

  @override
  State<TransferSearchScreen> createState() => _TransferSearchScreenState();
}

class _TransferSearchScreenState extends State<TransferSearchScreen> {
  final _searchController = TextEditingController();
  bool _isLoading = false;
  bool _isSearching = false;
  List<TransferService> _searchResults = [];
  String? _error;

  // Filter options
  String? _location;
  TransferVehicleType? _vehicleType;
  int? _minPassengerCapacity;
  int? _minLuggageCapacity;
  bool? _isPrivate;
  bool? _includesMeetAndGreet;
  bool? _includesFlightTracking;
  double? _maxPrice;

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  /// Load data from providers
  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final transferProvider =
          Provider.of<TransferProvider>(context, listen: false);
      final locationProvider =
          Provider.of<TransferLocationProvider>(context, listen: false);

      await Future.wait([
        transferProvider.initialize(),
        locationProvider.initialize(),
      ]);

      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
        _error = 'Failed to load data: $e';
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Search Transfers'),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _error != null
              ? ErrorState(
                  message: _error!,
                  onRetry: _loadData,
                )
              : Column(
                  children: [
                    _buildSearchBar(),
                    Expanded(
                      child: _isSearching
                          ? const Center(child: CircularProgressIndicator())
                          : _searchResults.isNotEmpty
                              ? _buildSearchResults()
                              : _buildInitialState(),
                    ),
                  ],
                ),
    );
  }

  /// Build the search bar
  Widget _buildSearchBar() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(25),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: TextField(
                  controller: _searchController,
                  decoration: const InputDecoration(
                    hintText: 'Enter airport, hotel, or address',
                    prefixIcon: Icon(Icons.search),
                    border: OutlineInputBorder(),
                  ),
                  onChanged: (value) {
                    setState(() {
                      _location = value.isNotEmpty ? value : null;
                    });
                  },
                ),
              ),
              const SizedBox(width: 16),
              IconButton(
                icon: const Icon(Icons.filter_list),
                onPressed: _showFilterDialog,
                tooltip: 'Filter',
              ),
            ],
          ),
          const SizedBox(height: 16),
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: _searchTransfers,
              child: const Text('Search'),
            ),
          ),
        ],
      ),
    );
  }

  /// Build the initial state
  Widget _buildInitialState() {
    return const EmptyState(
      icon: Icons.search,
      title: 'Search Transfers',
      message: 'Enter a location to search for transfer services.',
    );
  }

  /// Build the search results
  Widget _buildSearchResults() {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _searchResults.length,
      itemBuilder: (context, index) {
        final transfer = _searchResults[index];
        return Padding(
          padding: const EdgeInsets.only(bottom: 16),
          child: TransferCard(
            transfer: transfer,
            onTap: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => TransferDetailsScreen(
                    transferId: transfer.id,
                  ),
                ),
              );
            },
          ),
        );
      },
    );
  }

  /// Show the filter dialog
  void _showFilterDialog() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return DraggableScrollableSheet(
              initialChildSize: 0.7,
              minChildSize: 0.5,
              maxChildSize: 0.9,
              expand: false,
              builder: (context, scrollController) {
                return Column(
                  children: [
                    Padding(
                      padding: const EdgeInsets.all(16),
                      child: Row(
                        children: [
                          Expanded(
                            child: Text(
                              'Filter Transfers',
                              style: AppTextStyles.headline6,
                            ),
                          ),
                          IconButton(
                            icon: const Icon(Icons.close),
                            onPressed: () => Navigator.pop(context),
                          ),
                        ],
                      ),
                    ),
                    Expanded(
                      child: ListView(
                        controller: scrollController,
                        padding: const EdgeInsets.all(16),
                        children: [
                          Text(
                            'Vehicle Type',
                            style: AppTextStyles.subtitle1.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 16),
                          VehicleSelector(
                            initialVehicleType: _vehicleType,
                            onVehicleSelected: (vehicleType) {
                              setState(() {
                                _vehicleType = vehicleType;
                              });
                            },
                          ),
                          const SizedBox(height: 24),
                          Text(
                            'Passenger Capacity',
                            style: AppTextStyles.subtitle1.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 16),
                          Slider(
                            value: (_minPassengerCapacity ?? 1).toDouble(),
                            min: 1,
                            max: 16,
                            divisions: 15,
                            label: '${_minPassengerCapacity ?? 1} passengers',
                            onChanged: (value) {
                              setState(() {
                                _minPassengerCapacity = value.toInt();
                              });
                            },
                          ),
                          const SizedBox(height: 24),
                          Text(
                            'Luggage Capacity',
                            style: AppTextStyles.subtitle1.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 16),
                          Slider(
                            value: (_minLuggageCapacity ?? 1).toDouble(),
                            min: 1,
                            max: 10,
                            divisions: 9,
                            label: '${_minLuggageCapacity ?? 1} luggage',
                            onChanged: (value) {
                              setState(() {
                                _minLuggageCapacity = value.toInt();
                              });
                            },
                          ),
                          const SizedBox(height: 24),
                          Text(
                            'Maximum Price',
                            style: AppTextStyles.subtitle1.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 16),
                          Slider(
                            value: (_maxPrice ?? 500).toDouble(),
                            min: 50,
                            max: 500,
                            divisions: 9,
                            label:
                                '\$${_maxPrice?.toStringAsFixed(0) ?? '500'}',
                            onChanged: (value) {
                              setState(() {
                                _maxPrice = value;
                              });
                            },
                          ),
                          const SizedBox(height: 24),
                          Text(
                            'Features',
                            style: AppTextStyles.subtitle1.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 16),
                          CheckboxListTile(
                            title: const Text('Private Transfer'),
                            value: _isPrivate ?? false,
                            onChanged: (value) {
                              setState(() {
                                _isPrivate = value;
                              });
                            },
                          ),
                          CheckboxListTile(
                            title: const Text('Meet & Greet'),
                            value: _includesMeetAndGreet ?? false,
                            onChanged: (value) {
                              setState(() {
                                _includesMeetAndGreet = value;
                              });
                            },
                          ),
                          CheckboxListTile(
                            title: const Text('Flight Tracking'),
                            value: _includesFlightTracking ?? false,
                            onChanged: (value) {
                              setState(() {
                                _includesFlightTracking = value;
                              });
                            },
                          ),
                          const SizedBox(height: 24),
                          Row(
                            children: [
                              Expanded(
                                child: OutlinedButton(
                                  onPressed: () {
                                    setState(() {
                                      _vehicleType = null;
                                      _minPassengerCapacity = null;
                                      _minLuggageCapacity = null;
                                      _isPrivate = null;
                                      _includesMeetAndGreet = null;
                                      _includesFlightTracking = null;
                                      _maxPrice = null;
                                    });
                                  },
                                  child: const Text('Reset'),
                                ),
                              ),
                              const SizedBox(width: 16),
                              Expanded(
                                child: ElevatedButton(
                                  onPressed: () {
                                    Navigator.pop(context);
                                    _searchTransfers();
                                  },
                                  child: const Text('Apply'),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ],
                );
              },
            );
          },
        );
      },
    );
  }

  /// Search for transfers
  Future<void> _searchTransfers() async {
    if (_location == null &&
        _vehicleType == null &&
        _minPassengerCapacity == null &&
        _minLuggageCapacity == null &&
        _isPrivate == null &&
        _includesMeetAndGreet == null &&
        _includesFlightTracking == null &&
        _maxPrice == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please enter a location or select filters'),
          backgroundColor: AppColors.error,
        ),
      );
      return;
    }

    setState(() {
      _isSearching = true;
      _error = null;
    });

    try {
      final transferProvider =
          Provider.of<TransferProvider>(context, listen: false);
      final results = await transferProvider.searchTransfers(
        location: _location,
        vehicleType: _vehicleType,
        passengerCount: _minPassengerCapacity,
        luggageCount: _minLuggageCapacity,
        isPrivate: _isPrivate,
        maxPrice: _maxPrice,
      );

      setState(() {
        _searchResults = results;
        _isSearching = false;
      });

      if (_searchResults.isEmpty && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('No transfers found matching your criteria'),
          ),
        );
      }
    } catch (e) {
      setState(() {
        _error = 'Failed to search transfers: $e';
        _isSearching = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: $e'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }
}
