import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:culture_connect/models/travel/ai_recommendation.dart';
import 'package:culture_connect/providers/travel/itinerary_providers.dart';
import 'package:culture_connect/theme/app_colors.dart';
import 'package:culture_connect/widgets/common/custom_app_bar.dart';
import 'package:culture_connect/widgets/offline/offline_banner.dart';
import 'package:culture_connect/widgets/travel/itinerary/itinerary_step_indicator.dart';
import 'package:culture_connect/widgets/travel/itinerary/destination_selection_step.dart';
import 'package:culture_connect/widgets/travel/itinerary/date_selection_step.dart';
import 'package:culture_connect/widgets/travel/itinerary/preferences_step.dart';
import 'package:culture_connect/widgets/travel/itinerary/ai_recommendations_step.dart';
import 'package:culture_connect/widgets/travel/itinerary/itinerary_review_step.dart';

/// Steps in the itinerary builder
enum ItineraryBuilderStep {
  /// Destination selection
  destination,

  /// Date selection
  dates,

  /// Preferences selection
  preferences,

  /// AI recommendations
  recommendations,

  /// Review and finalize
  review,
}

/// Extension for itinerary builder steps
extension ItineraryBuilderStepExtension on ItineraryBuilderStep {
  /// Get the display name for the step
  String get displayName {
    switch (this) {
      case ItineraryBuilderStep.destination:
        return 'Destination';
      case ItineraryBuilderStep.dates:
        return 'Dates';
      case ItineraryBuilderStep.preferences:
        return 'Preferences';
      case ItineraryBuilderStep.recommendations:
        return 'Recommendations';
      case ItineraryBuilderStep.review:
        return 'Review';
    }
  }

  /// Get the icon for the step
  IconData get icon {
    switch (this) {
      case ItineraryBuilderStep.destination:
        return Icons.location_on;
      case ItineraryBuilderStep.dates:
        return Icons.calendar_today;
      case ItineraryBuilderStep.preferences:
        return Icons.tune;
      case ItineraryBuilderStep.recommendations:
        return Icons.recommend;
      case ItineraryBuilderStep.review:
        return Icons.check_circle;
    }
  }
}

/// A screen for building a new itinerary
class ItineraryBuilderScreen extends ConsumerStatefulWidget {
  /// Creates a new itinerary builder screen
  const ItineraryBuilderScreen({super.key});

  @override
  ConsumerState<ItineraryBuilderScreen> createState() =>
      _ItineraryBuilderScreenState();
}

class _ItineraryBuilderScreenState extends ConsumerState<ItineraryBuilderScreen>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  ItineraryBuilderStep _currentStep = ItineraryBuilderStep.destination;
  bool _isForwardNavigation = true;

  // Form data
  String _destination = '';
  DateTime? _startDate;
  DateTime? _endDate;
  double? _budgetAmount;
  String _budgetCurrency = 'USD';
  final Map<ItineraryItemType, bool> _preferredItemTypes = {
    ItineraryItemType.accommodation: true,
    ItineraryItemType.transportation: true,
    ItineraryItemType.activity: true,
    ItineraryItemType.food: true,
    ItineraryItemType.custom: false,
  };
  final List<String> _preferredCategories = [];
  final List<AIRecommendation> _acceptedRecommendations = [];

  @override
  void initState() {
    super.initState();

    // Initialize animation controller
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );

    // Create animations
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(1.0, 0.0),
      end: Offset.zero,
    ).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    // Start animation
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  /// Navigate to the next step
  void _nextStep() {
    if (_currentStep == ItineraryBuilderStep.review) {
      _finishItinerary();
      return;
    }

    final nextStep = ItineraryBuilderStep.values[_currentStep.index + 1];
    _navigateToStep(nextStep, true);
  }

  /// Navigate to the previous step
  void _previousStep() {
    if (_currentStep == ItineraryBuilderStep.destination) {
      Navigator.of(context).pop();
      return;
    }

    final previousStep = ItineraryBuilderStep.values[_currentStep.index - 1];
    _navigateToStep(previousStep, false);
  }

  /// Navigate to a specific step
  void _navigateToStep(ItineraryBuilderStep step, bool isForward) {
    setState(() {
      _isForwardNavigation = isForward;
    });

    _animationController.reverse().then((_) {
      setState(() {
        _currentStep = step;
      });

      _animationController.forward();
    });
  }

  /// Create the itinerary
  Future<void> _createItinerary() async {
    if (_destination.isEmpty || _startDate == null || _endDate == null) {
      return;
    }

    try {
      await ref.read(currentItineraryProvider.notifier).createItinerary(
            title: 'Trip to $_destination',
            destination: _destination,
            startDate: _startDate!,
            endDate: _endDate!,
            budgetAmount: _budgetAmount,
            budgetCurrency: _budgetCurrency,
          );
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to create itinerary: $e')),
        );
      }
    }
  }

  /// Add accepted recommendations to the itinerary
  Future<void> _addRecommendationsToItinerary() async {
    final currentItineraryAsync = ref.read(currentItineraryProvider);

    if (currentItineraryAsync is! AsyncData<Itinerary?> ||
        currentItineraryAsync.value == null) {
      return;
    }

    final itinerary = currentItineraryAsync.value!;

    try {
      for (final recommendation in _acceptedRecommendations) {
        // Find the appropriate day for this recommendation
        ItineraryDay? targetDay;

        // For accommodation, add to the first day
        if (recommendation.item.type == ItineraryItemType.accommodation) {
          targetDay = itinerary.days.first;
        }
        // For transportation, add to the first day
        else if (recommendation.item.type == ItineraryItemType.transportation) {
          targetDay = itinerary.days.first;
        }
        // For activities and food, distribute across days
        else {
          // Simple distribution - just use the first day for now
          // In a real app, this would be more sophisticated
          targetDay = itinerary.days.first;
        }

        await ref.read(currentItineraryProvider.notifier).addItemToDay(
              targetDay.id,
              recommendation.item,
            );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to add recommendations: $e')),
        );
      }
    }
  }

  /// Finish the itinerary creation process
  Future<void> _finishItinerary() async {
    // Add accepted recommendations to the itinerary
    await _addRecommendationsToItinerary();

    // Navigate to the itinerary details screen
    if (mounted) {
      final currentItineraryAsync = ref.read(currentItineraryProvider);

      if (currentItineraryAsync is AsyncData<Itinerary?> &&
          currentItineraryAsync.value != null) {
        final itinerary = currentItineraryAsync.value!;

        Navigator.of(context).pushReplacementNamed(
          '/itinerary/details',
          arguments: itinerary.id,
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title: 'Create Itinerary',
        showBackButton: true,
        onBackPressed: _previousStep,
      ),
      body: OfflineBannerWrapper(
        message: 'You are offline. Some features may be limited.',
        actionText: 'CONTINUE',
        child: Column(
          children: [
            // Step indicator
            ItineraryStepIndicator(
              currentStep: _currentStep,
              onStepTapped: (step) {
                if (step.index < _currentStep.index) {
                  _navigateToStep(step, false);
                }
              },
            ),

            // Main content
            Expanded(
              child: AnimatedBuilder(
                animation: _animationController,
                builder: (context, child) {
                  return FadeTransition(
                    opacity: _fadeAnimation,
                    child: SlideTransition(
                      position: _isForwardNavigation
                          ? _slideAnimation
                          : Tween<Offset>(
                              begin: const Offset(-1.0, 0.0),
                              end: Offset.zero,
                            ).animate(
                              CurvedAnimation(
                                parent: _animationController,
                                curve: Curves.easeInOut,
                              ),
                            ),
                      child: _buildCurrentStep(),
                    ),
                  );
                },
              ),
            ),

            // Bottom navigation
            _buildBottomNavigation(),
          ],
        ),
      ),
    );
  }

  /// Build the current step
  Widget _buildCurrentStep() {
    switch (_currentStep) {
      case ItineraryBuilderStep.destination:
        return DestinationSelectionStep(
          initialDestination: _destination,
          onDestinationChanged: (destination) {
            setState(() {
              _destination = destination;
            });
          },
        );

      case ItineraryBuilderStep.dates:
        return DateSelectionStep(
          initialStartDate: _startDate,
          initialEndDate: _endDate,
          onDatesChanged: (startDate, endDate) {
            setState(() {
              _startDate = startDate;
              _endDate = endDate;
            });
          },
          initialBudget: _budgetAmount,
          initialCurrency: _budgetCurrency,
          onBudgetChanged: (amount, currency) {
            setState(() {
              _budgetAmount = amount;
              _budgetCurrency = currency;
            });
          },
        );

      case ItineraryBuilderStep.preferences:
        return PreferencesStep(
          initialItemTypes: _preferredItemTypes,
          initialCategories: _preferredCategories,
          onItemTypesChanged: (itemTypes) {
            setState(() {
              _preferredItemTypes.clear();
              _preferredItemTypes.addAll(itemTypes);
            });
          },
          onCategoriesChanged: (categories) {
            setState(() {
              _preferredCategories.clear();
              _preferredCategories.addAll(categories);
            });
          },
        );

      case ItineraryBuilderStep.recommendations:
        return AIRecommendationsStep(
          destination: _destination,
          startDate: _startDate!,
          endDate: _endDate!,
          preferredItemTypes: _preferredItemTypes.entries
              .where((e) => e.value)
              .map((e) => e.key)
              .toList(),
          preferredCategories: _preferredCategories,
          onRecommendationsAccepted: (recommendations) {
            setState(() {
              _acceptedRecommendations.clear();
              _acceptedRecommendations.addAll(recommendations);
            });
          },
        );

      case ItineraryBuilderStep.review:
        return ItineraryReviewStep(
          destination: _destination,
          startDate: _startDate!,
          endDate: _endDate!,
          budgetAmount: _budgetAmount,
          budgetCurrency: _budgetCurrency,
          acceptedRecommendations: _acceptedRecommendations,
        );
    }
  }

  /// Build the bottom navigation
  Widget _buildBottomNavigation() {
    return Container(
      padding: EdgeInsets.all(16.r),
      decoration: const BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Color.fromRGBO(0, 0, 0, 0.05),
            blurRadius: 10,
            offset: Offset(0, -5),
          ),
        ],
      ),
      child: SafeArea(
        child: Row(
          children: [
            // Back button
            OutlinedButton.icon(
              onPressed: _previousStep,
              icon: const Icon(Icons.arrow_back),
              label: const Text('Back'),
              style: OutlinedButton.styleFrom(
                padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
              ),
            ),

            SizedBox(width: 16.w),

            // Next/Finish button
            Expanded(
              child: ElevatedButton.icon(
                onPressed: _isNextButtonEnabled() ? _nextStep : null,
                icon: Icon(_currentStep == ItineraryBuilderStep.review
                    ? Icons.check
                    : Icons.arrow_forward),
                label: Text(_currentStep == ItineraryBuilderStep.review
                    ? 'Finish'
                    : 'Next'),
                style: ElevatedButton.styleFrom(
                  padding:
                      EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
                  backgroundColor: AppColors.primary,
                  foregroundColor: Colors.white,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Check if the next button should be enabled
  bool _isNextButtonEnabled() {
    switch (_currentStep) {
      case ItineraryBuilderStep.destination:
        return _destination.isNotEmpty;

      case ItineraryBuilderStep.dates:
        return _startDate != null && _endDate != null;

      case ItineraryBuilderStep.preferences:
        return _preferredItemTypes.values.any((enabled) => enabled);

      case ItineraryBuilderStep.recommendations:
        // Create the itinerary when moving to the review step
        _createItinerary();
        return true;

      case ItineraryBuilderStep.review:
        return true;
    }
  }
}
