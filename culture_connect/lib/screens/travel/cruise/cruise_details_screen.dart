import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:culture_connect/models/travel/travel.dart';
import 'package:culture_connect/widgets/common/rating_display.dart';

/// A screen that displays details about a cruise
class CruiseDetailsScreen extends StatefulWidget {
  /// The cruise to display
  final Cruise cruise;

  /// Creates a new cruise details screen
  const CruiseDetailsScreen({
    super.key,
    required this.cruise,
  });

  @override
  State<CruiseDetailsScreen> createState() => _CruiseDetailsScreenState();
}

class _CruiseDetailsScreenState extends State<CruiseDetailsScreen> {
  final ScrollController _scrollController = ScrollController();

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Cruise Details'),
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.directions_boat,
              size: 64,
              color: theme.colorScheme.primary,
            ),
            SizedBox(height: 16.h),
            Text(
              widget.cruise.name,
              style: theme.textTheme.headlineSmall,
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 8.h),
            Text(
              '${widget.cruise.cruiseLine} - ${widget.cruise.shipName}',
              style: theme.textTheme.titleMedium,
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 16.h),
            RatingDisplay(
              rating: widget.cruise.rating,
              size: 24,
            ),
            SizedBox(height: 24.h),
            Text(
              'This screen is under construction.',
              style: theme.textTheme.bodyLarge,
            ),
            SizedBox(height: 8.h),
            Text(
              'Please check back later for the full cruise details experience.',
              style: theme.textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 32.h),
            ElevatedButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Go Back'),
            ),
          ],
        ),
      ),
    );
  }
}
