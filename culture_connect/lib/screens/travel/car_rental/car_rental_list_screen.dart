import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:culture_connect/models/travel/travel.dart';
import 'package:culture_connect/providers/travel/travel_services_provider.dart';
import 'package:culture_connect/screens/travel/car_rental/car_rental_details_screen.dart';
import 'package:culture_connect/widgets/common/loading_indicator.dart';
import 'package:culture_connect/widgets/common/error_view.dart';
import 'package:culture_connect/widgets/travel/travel_service_card.dart';

/// Screen for displaying a list of car rentals
class CarRentalListScreen extends ConsumerStatefulWidget {
  /// Creates a new car rental list screen
  const CarRentalListScreen({super.key});

  @override
  ConsumerState<CarRentalListScreen> createState() => _CarRentalListScreenState();
}

class _CarRentalListScreenState extends ConsumerState<CarRentalListScreen> {
  final ScrollController _scrollController = ScrollController();
  bool _isSearching = false;
  final TextEditingController _searchController = TextEditingController();
  
  @override
  void dispose() {
    _scrollController.dispose();
    _searchController.dispose();
    super.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Scaffold(
      appBar: AppBar(
        title: _isSearching
            ? TextField(
                controller: _searchController,
                decoration: InputDecoration(
                  hintText: 'Search car rentals...',
                  border: InputBorder.none,
                  hintStyle: TextStyle(color: theme.colorScheme.onSurface.withOpacity(0.6)),
                ),
                style: TextStyle(color: theme.colorScheme.onSurface),
                autofocus: true,
                onChanged: (value) {
                  // Implement search functionality
                },
              )
            : const Text('Car Rentals'),
        actions: [
          IconButton(
            icon: Icon(_isSearching ? Icons.close : Icons.search),
            onPressed: () {
              setState(() {
                _isSearching = !_isSearching;
                if (!_isSearching) {
                  _searchController.clear();
                }
              });
            },
          ),
        ],
      ),
      body: _buildCarRentalList(),
    );
  }
  
  Widget _buildCarRentalList() {
    final carRentalsAsyncValue = ref.watch(carRentalsProvider);
    
    return carRentalsAsyncValue.when(
      data: (carRentals) {
        if (carRentals.isEmpty) {
          return const Center(
            child: Text('No car rentals available'),
          );
        }
        
        return RefreshIndicator(
          onRefresh: () async {
            // Refresh data
            ref.refresh(carRentalsProvider);
          },
          child: ListView.builder(
            controller: _scrollController,
            padding: EdgeInsets.all(16.r),
            itemCount: carRentals.length,
            itemBuilder: (context, index) {
              final carRental = carRentals[index];
              return Padding(
                padding: EdgeInsets.only(bottom: 16.h),
                child: TravelServiceCard(
                  travelService: carRental,
                  onTap: () => _navigateToCarRentalDetails(carRental),
                ),
              );
            },
          ),
        );
      },
      loading: () => const Center(child: LoadingIndicator()),
      error: (error, stackTrace) => Center(
        child: ErrorView(
          error: error.toString(),
          onRetry: () => ref.refresh(carRentalsProvider),
        ),
      ),
    );
  }
  
  void _navigateToCarRentalDetails(CarRental carRental) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => CarRentalDetailsScreen(carRental: carRental),
      ),
    );
  }
}
