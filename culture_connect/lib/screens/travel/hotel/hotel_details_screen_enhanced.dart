import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';

// Models
import 'package:culture_connect/models/travel/travel.dart';

// Services
import 'package:culture_connect/services/logging_service.dart';

// Widgets
import 'package:culture_connect/widgets/common/animated_section.dart';
import 'package:culture_connect/widgets/common/image_gallery.dart';
import 'package:culture_connect/widgets/common/rating_display.dart';
import 'package:culture_connect/widgets/booking/instant_booking_button.dart';
import 'package:culture_connect/widgets/travel/hotel_comparison_dialog.dart';
import 'package:culture_connect/widgets/travel/hotel_map_view.dart';
import 'package:culture_connect/widgets/travel/hotel_review_list.dart';
import 'package:culture_connect/widgets/travel/share_travel_service_dialog.dart';

/// Enhanced screen for displaying hotel details with animations and additional features
class HotelDetailsScreenEnhanced extends ConsumerStatefulWidget {
  /// The hotel to display
  final Hotel hotel;

  /// Creates a new hotel details screen
  const HotelDetailsScreenEnhanced({
    super.key,
    required this.hotel,
  });

  @override
  ConsumerState<HotelDetailsScreenEnhanced> createState() =>
      _HotelDetailsScreenEnhancedState();
}

class _HotelDetailsScreenEnhancedState
    extends ConsumerState<HotelDetailsScreenEnhanced>
    with SingleTickerProviderStateMixin {
  final ScrollController _scrollController = ScrollController();
  late TabController _tabController;
  DateTime _checkInDate = DateTime.now().add(const Duration(days: 1));
  DateTime _checkOutDate = DateTime.now().add(const Duration(days: 3));
  int _nights = 2;
  HotelRoom? _selectedRoom;
  double _totalPrice = 0;
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late LoggingService _loggingService;

  @override
  void initState() {
    super.initState();
    _loggingService = LoggingService();
    _tabController = TabController(length: 5, vsync: this);

    try {
      if (widget.hotel.rooms.isNotEmpty) {
        _selectedRoom = widget.hotel.rooms.first;
      }
      _calculateTotalPrice();

      // Initialize animation controller
      _animationController = AnimationController(
        duration: const Duration(milliseconds: 800),
        vsync: this,
      );

      _fadeAnimation = CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeInOut,
      );

      // Start the animation
      _animationController.forward();

      _loggingService.debug(
        'HotelDetailsScreenEnhanced',
        'Screen initialized for hotel: ${widget.hotel.id}',
      );
    } catch (e, stackTrace) {
      _loggingService.error(
        'HotelDetailsScreenEnhanced',
        'Error initializing hotel details screen',
        e,
        stackTrace,
      );
    }
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _tabController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  void _calculateTotalPrice() {
    _nights = _checkOutDate.difference(_checkInDate).inDays;
    if (_nights < 1) _nights = 1;
    if (_selectedRoom != null) {
      _totalPrice = _selectedRoom!.pricePerNight * _nights;
    } else {
      _totalPrice = widget.hotel.price * _nights;
    }
  }

  Future<void> _selectCheckInDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _checkInDate,
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );

    if (picked != null && picked != _checkInDate) {
      setState(() {
        _checkInDate = picked;
        if (_checkInDate.isAfter(_checkOutDate)) {
          _checkOutDate = _checkInDate.add(const Duration(days: 1));
        }
        _calculateTotalPrice();
      });
    }
  }

  Future<void> _selectCheckOutDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _checkOutDate,
      firstDate: _checkInDate.add(const Duration(days: 1)),
      lastDate: _checkInDate.add(const Duration(days: 30)),
    );

    if (picked != null && picked != _checkOutDate) {
      setState(() {
        _checkOutDate = picked;
        _calculateTotalPrice();
      });
    }
  }

  void _selectRoom(HotelRoom room) {
    setState(() {
      _selectedRoom = room;
      _calculateTotalPrice();
    });
  }

  void _showHotelComparisonDialog() {
    showDialog(
      context: context,
      builder: (context) => HotelComparisonDialog(
        hotel: widget.hotel,
        onHotelSelected: (hotel) {
          // Navigate to the selected hotel's details screen
          Navigator.pushReplacement(
            context,
            MaterialPageRoute(
              builder: (context) => HotelDetailsScreenEnhanced(hotel: hotel),
            ),
          );
        },
      ),
    );
  }

  void _showShareDialog() {
    showDialog(
      context: context,
      builder: (context) => ShareTravelServiceDialog(
        travelService: widget.hotel,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      body: FadeTransition(
        opacity: _fadeAnimation,
        child: CustomScrollView(
          controller: _scrollController,
          slivers: [
            // App bar with image
            SliverAppBar(
              expandedHeight: 250.h,
              pinned: true,
              actions: [
                IconButton(
                  icon: const Icon(Icons.share),
                  onPressed: _showShareDialog,
                  tooltip: 'Share this hotel',
                ),
                IconButton(
                  icon: const Icon(Icons.compare_arrows),
                  onPressed: _showHotelComparisonDialog,
                  tooltip: 'Compare with other hotels',
                ),
                IconButton(
                  icon: const Icon(Icons.favorite_border),
                  onPressed: () {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('Added to favorites'),
                      ),
                    );
                  },
                  tooltip: 'Add to favorites',
                ),
              ],
              flexibleSpace: FlexibleSpaceBar(
                background: Hero(
                  tag: 'hotel_image_${widget.hotel.id}',
                  child: FadeInImage.assetNetwork(
                    placeholder: 'assets/images/placeholder_hotel.png',
                    image: widget.hotel.imageUrl,
                    fit: BoxFit.cover,
                    fadeInDuration: const Duration(milliseconds: 300),
                    imageErrorBuilder: (context, error, stackTrace) {
                      _loggingService.error(
                        'HotelDetailsScreenEnhanced',
                        'Error loading hotel image: ${widget.hotel.imageUrl}',
                        error,
                        stackTrace,
                      );
                      return Container(
                        color: theme.colorScheme.surfaceContainerHighest,
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.image_not_supported,
                              size: 48.r,
                              color: theme.colorScheme.onSurfaceVariant,
                            ),
                            SizedBox(height: 8.h),
                            Text(
                              'Image unavailable',
                              style: theme.textTheme.bodyMedium?.copyWith(
                                color: theme.colorScheme.onSurfaceVariant,
                              ),
                            ),
                          ],
                        ),
                      );
                    },
                  ),
                ),
              ),
            ),

            // Content
            SliverToBoxAdapter(
              child: Padding(
                padding: EdgeInsets.all(16.r),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Title and rating
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                widget.hotel.name,
                                style: theme.textTheme.headlineSmall?.copyWith(
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              SizedBox(height: 4.h),
                              Row(
                                children: [
                                  ...List.generate(
                                    _getStarRatingValue(
                                        widget.hotel.starRating),
                                    (index) => Icon(
                                      Icons.star,
                                      size: 16.r,
                                      color: Colors.amber,
                                    ),
                                  ),
                                ],
                              ),
                              SizedBox(height: 4.h),
                              Text(
                                widget.hotel.location,
                                style: theme.textTheme.titleMedium?.copyWith(
                                  color: theme.colorScheme.onSurfaceVariant,
                                ),
                              ),
                            ],
                          ),
                        ),
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.end,
                          children: [
                            RatingDisplay(
                              rating: widget.hotel.rating,
                              size: 20.r,
                            ),
                            SizedBox(height: 4.h),
                            Text(
                              '${widget.hotel.reviewCount} reviews',
                              style: theme.textTheme.bodySmall,
                            ),
                          ],
                        ),
                      ],
                    ),

                    SizedBox(height: 16.h),

                    // Price
                    Row(
                      children: [
                        if (widget.hotel.isOnSale &&
                            widget.hotel.originalPrice != null) ...[
                          Text(
                            widget.hotel.formattedOriginalPrice!,
                            style: theme.textTheme.titleMedium?.copyWith(
                              decoration: TextDecoration.lineThrough,
                              color: theme.colorScheme.onSurfaceVariant,
                            ),
                          ),
                          SizedBox(width: 8.w),
                        ],
                        Text(
                          '${widget.hotel.formattedPrice} / night',
                          style: theme.textTheme.titleLarge?.copyWith(
                            color: theme.colorScheme.primary,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),

                    SizedBox(height: 24.h),

                    // Photo Gallery
                    AnimatedSection(
                      title: 'Photo Gallery',
                      content: ImageGallery(
                        images: [
                          widget.hotel.imageUrl,
                          ...widget.hotel.additionalImages
                        ],
                        height: 200.h,
                      ),
                    ),

                    SizedBox(height: 16.h),

                    // Tabs
                    TabBar(
                      controller: _tabController,
                      tabs: const [
                        Tab(text: 'Details'),
                        Tab(text: 'Rooms'),
                        Tab(text: 'Amenities'),
                        Tab(text: 'Reviews'),
                        Tab(text: 'Prices'),
                      ],
                      labelColor: theme.colorScheme.primary,
                      unselectedLabelColor: theme.colorScheme.onSurface,
                      indicatorColor: theme.colorScheme.primary,
                      isScrollable: true,
                    ),

                    // Tab content
                    SizedBox(
                      height: 600.h, // Fixed height for tab content
                      child: TabBarView(
                        controller: _tabController,
                        children: [
                          _buildDetailsTab(),
                          _buildRoomsTab(),
                          _buildAmenitiesTab(),
                          _buildReviewsTab(),
                          _buildPricesTab(),
                        ],
                      ),
                    ),

                    SizedBox(height: 100.h), // Space for the bottom button
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
      bottomSheet: Container(
        padding: EdgeInsets.all(16.r),
        decoration: BoxDecoration(
          color: theme.colorScheme.surface,
          boxShadow: [
            BoxShadow(
              color: Colors.black.withAlpha(26), // Equivalent to opacity 0.1
              blurRadius: 10,
              offset: const Offset(0, -5),
            ),
          ],
        ),
        child: SafeArea(
          child: Row(
            children: [
              // Regular booking button
              Expanded(
                flex: 3,
                child: ElevatedButton(
                  onPressed: _selectedRoom == null
                      ? null
                      : () {
                          // Navigate to payment screen
                          // This would be replaced with your actual payment screen
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(
                              content: Text('Regular booking flow initiated'),
                            ),
                          );
                        },
                  style: ElevatedButton.styleFrom(
                    padding: EdgeInsets.symmetric(vertical: 16.h),
                    backgroundColor: theme.colorScheme.primary,
                    foregroundColor: theme.colorScheme.onPrimary,
                  ),
                  child: Text(
                    'Book Now - ${widget.hotel.currency}${_totalPrice.toStringAsFixed(2)}',
                    style: theme.textTheme.titleMedium?.copyWith(
                      color: theme.colorScheme.onPrimary,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),

              SizedBox(width: 8.w),

              // Instant booking button
              Expanded(
                flex: 2,
                child: InstantBookingButton(
                  travelService: widget.hotel,
                  serviceDate: _checkInDate,
                  participantCount: _selectedRoom?.maxGuests ?? 2,
                  totalAmount: _totalPrice,
                  currency: widget.hotel.currency,
                  additionalDetails: {
                    'checkInDate': _checkInDate.toIso8601String(),
                    'checkOutDate': _checkOutDate.toIso8601String(),
                    'nights': _nights,
                    'roomType': _selectedRoom?.type.displayName ?? '',
                    'roomId': _selectedRoom?.id ?? '',
                    'specialRequirements': '',
                  },
                  buttonText: 'Instant',
                  buttonIcon: Icons.flash_on,
                  buttonColor: Colors.orange,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDetailsTab() {
    return SingleChildScrollView(
      padding: EdgeInsets.all(16.r),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Description
          AnimatedSection(
            title: 'Description',
            content: Card(
              child: Padding(
                padding: EdgeInsets.all(16.r),
                child: Text(
                  widget.hotel.description,
                  style: Theme.of(context).textTheme.bodyMedium,
                ),
              ),
            ),
          ),

          SizedBox(height: 16.h),

          // Check-in/out times
          AnimatedSection(
            title: 'Check-in/out Times',
            content: Card(
              child: Padding(
                padding: EdgeInsets.all(16.r),
                child: Row(
                  children: [
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Check-in',
                            style: Theme.of(context)
                                .textTheme
                                .titleMedium
                                ?.copyWith(
                                  fontWeight: FontWeight.bold,
                                ),
                          ),
                          SizedBox(height: 8.h),
                          Text(
                            'From ${widget.hotel.formattedCheckInTime}',
                            style: Theme.of(context).textTheme.bodyMedium,
                          ),
                        ],
                      ),
                    ),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Check-out',
                            style: Theme.of(context)
                                .textTheme
                                .titleMedium
                                ?.copyWith(
                                  fontWeight: FontWeight.bold,
                                ),
                          ),
                          SizedBox(height: 8.h),
                          Text(
                            'Until ${widget.hotel.formattedCheckOutTime}',
                            style: Theme.of(context).textTheme.bodyMedium,
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),

          SizedBox(height: 16.h),

          // Location
          AnimatedSection(
            title: 'Location',
            content: HotelMapView(
              hotel: widget.hotel,
            ),
          ),

          SizedBox(height: 16.h),

          // Booking details
          AnimatedSection(
            title: 'Booking Details',
            content: Card(
              child: Padding(
                padding: EdgeInsets.all(16.r),
                child: Column(
                  children: [
                    // Check-in date
                    ListTile(
                      title: const Text('Check-in Date'),
                      subtitle: Text(
                        DateFormat('EEE, MMM d, yyyy').format(_checkInDate),
                        style: Theme.of(context).textTheme.titleMedium,
                      ),
                      trailing: const Icon(Icons.calendar_today),
                      onTap: _selectCheckInDate,
                    ),
                    const Divider(),

                    // Check-out date
                    ListTile(
                      title: const Text('Check-out Date'),
                      subtitle: Text(
                        DateFormat('EEE, MMM d, yyyy').format(_checkOutDate),
                        style: Theme.of(context).textTheme.titleMedium,
                      ),
                      trailing: const Icon(Icons.calendar_today),
                      onTap: _selectCheckOutDate,
                    ),
                    const Divider(),

                    // Duration
                    ListTile(
                      title: const Text('Duration'),
                      subtitle: Text(
                        '$_nights nights',
                        style: Theme.of(context).textTheme.titleMedium,
                      ),
                    ),
                    const Divider(),

                    // Selected room
                    ListTile(
                      title: const Text('Selected Room'),
                      subtitle: Text(
                        _selectedRoom != null
                            ? _selectedRoom!.type.displayName
                            : 'No room selected',
                        style: Theme.of(context).textTheme.titleMedium,
                      ),
                    ),
                    const Divider(),

                    // Total price
                    ListTile(
                      title: const Text('Total Price'),
                      subtitle: Text(
                        '${widget.hotel.currency}${_totalPrice.toStringAsFixed(2)}',
                        style:
                            Theme.of(context).textTheme.titleMedium?.copyWith(
                                  color: Theme.of(context).colorScheme.primary,
                                  fontWeight: FontWeight.bold,
                                ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRoomsTab() {
    return ListView.builder(
      padding: EdgeInsets.all(16.r),
      itemCount: widget.hotel.rooms.length,
      itemBuilder: (context, index) {
        final room = widget.hotel.rooms[index];
        final isSelected = _selectedRoom?.id == room.id;

        return Card(
          margin: EdgeInsets.only(bottom: 16.h),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12.r),
            side: BorderSide(
              color: isSelected
                  ? Theme.of(context).colorScheme.primary
                  : Colors.transparent,
              width: 2.w,
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Room image
              ClipRRect(
                borderRadius: BorderRadius.vertical(top: Radius.circular(12.r)),
                child: FadeInImage.assetNetwork(
                  placeholder: 'assets/images/placeholder_room.png',
                  image: room.imageUrl,
                  height: 200.h,
                  width: double.infinity,
                  fit: BoxFit.cover,
                  fadeInDuration: const Duration(milliseconds: 300),
                  imageErrorBuilder: (context, error, stackTrace) {
                    _loggingService.error(
                      'HotelDetailsScreenEnhanced',
                      'Error loading room image: ${room.imageUrl}',
                      error,
                      stackTrace,
                    );
                    return Container(
                      height: 200.h,
                      color:
                          Theme.of(context).colorScheme.surfaceContainerHighest,
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.image_not_supported,
                            size: 32.r,
                            color:
                                Theme.of(context).colorScheme.onSurfaceVariant,
                          ),
                          SizedBox(height: 8.h),
                          Text(
                            'Room image unavailable',
                            style:
                                Theme.of(context).textTheme.bodySmall?.copyWith(
                                      color: Theme.of(context)
                                          .colorScheme
                                          .onSurfaceVariant,
                                    ),
                          ),
                        ],
                      ),
                    );
                  },
                ),
              ),

              // Room details
              Padding(
                padding: EdgeInsets.all(16.r),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Room type and price
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          room.type.displayName,
                          style:
                              Theme.of(context).textTheme.titleMedium?.copyWith(
                                    fontWeight: FontWeight.bold,
                                  ),
                        ),
                        Text(
                          '${room.formattedPricePerNight} / night',
                          style: Theme.of(context)
                              .textTheme
                              .titleMedium
                              ?.copyWith(
                                color: Theme.of(context).colorScheme.primary,
                                fontWeight: FontWeight.bold,
                              ),
                        ),
                      ],
                    ),
                    SizedBox(height: 8.h),

                    // Room description
                    Text(
                      room.description,
                      style: Theme.of(context).textTheme.bodyMedium,
                    ),
                    SizedBox(height: 8.h),

                    // Room details
                    Wrap(
                      spacing: 16.w,
                      runSpacing: 8.h,
                      children: [
                        _buildRoomFeature(
                            Icons.people, '${room.maxGuests} guests'),
                        _buildRoomFeature(Icons.bed,
                            '${room.bedCount} ${room.bedType} bed${room.bedCount > 1 ? 's' : ''}'),
                        _buildRoomFeature(Icons.square_foot, room.roomSize),
                        if (room.hasView)
                          _buildRoomFeature(
                              Icons.visibility, room.viewType ?? 'View'),
                        if (room.hasBalcony)
                          _buildRoomFeature(Icons.balcony, 'Balcony'),
                        if (room.hasPrivateBathroom)
                          _buildRoomFeature(Icons.bathroom, 'Private Bathroom'),
                      ],
                    ),
                    SizedBox(height: 16.h),

                    // Room amenities
                    Text(
                      'Amenities',
                      style: Theme.of(context).textTheme.titleSmall?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                    ),
                    SizedBox(height: 8.h),
                    Wrap(
                      spacing: 16.w,
                      runSpacing: 8.h,
                      children: [
                        if (room.hasAirConditioning)
                          _buildRoomFeature(Icons.ac_unit, 'Air Conditioning'),
                        if (room.hasTV) _buildRoomFeature(Icons.tv, 'TV'),
                        if (room.hasMinibar)
                          _buildRoomFeature(Icons.local_bar, 'Minibar'),
                        if (room.hasSafe) _buildRoomFeature(Icons.lock, 'Safe'),
                        if (room.hasFreeWifi)
                          _buildRoomFeature(Icons.wifi, 'Free WiFi'),
                        if (room.isNonSmoking)
                          _buildRoomFeature(Icons.smoke_free, 'Non-Smoking'),
                        if (room.isAccessible)
                          _buildRoomFeature(Icons.accessible, 'Accessible'),
                      ],
                    ),
                    SizedBox(height: 16.h),

                    // Select button
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: () => _selectRoom(room),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: isSelected
                              ? Theme.of(context).colorScheme.primary
                              : null,
                          foregroundColor: isSelected
                              ? Theme.of(context).colorScheme.onPrimary
                              : null,
                        ),
                        child: Text(isSelected ? 'Selected' : 'Select'),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildAmenitiesTab() {
    return SingleChildScrollView(
      padding: EdgeInsets.all(16.r),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Main amenities
          AnimatedSection(
            title: 'Main Amenities',
            content: Card(
              child: Padding(
                padding: EdgeInsets.all(16.r),
                child: Wrap(
                  spacing: 16.w,
                  runSpacing: 16.h,
                  children: [
                    if (widget.hotel.hasRestaurant)
                      _buildAmenityItem(Icons.restaurant, 'Restaurant'),
                    if (widget.hotel.hasBar)
                      _buildAmenityItem(Icons.local_bar, 'Bar'),
                    if (widget.hotel.hasPool)
                      _buildAmenityItem(Icons.pool, 'Swimming Pool'),
                    if (widget.hotel.hasSpa)
                      _buildAmenityItem(Icons.spa, 'Spa'),
                    if (widget.hotel.hasGym)
                      _buildAmenityItem(Icons.fitness_center, 'Fitness Center'),
                    if (widget.hotel.hasFreeWifi)
                      _buildAmenityItem(Icons.wifi, 'Free WiFi'),
                    if (widget.hotel.hasFreeParking)
                      _buildAmenityItem(Icons.local_parking, 'Free Parking'),
                    if (widget.hotel.hasRoomService)
                      _buildAmenityItem(Icons.room_service, 'Room Service'),
                    if (widget.hotel.hasBusinessCenter)
                      _buildAmenityItem(
                          Icons.business_center, 'Business Center'),
                    if (widget.hotel.hasConferenceRoom)
                      _buildAmenityItem(Icons.meeting_room, 'Conference Room'),
                    if (widget.hotel.hasKidsClub)
                      _buildAmenityItem(Icons.child_care, 'Kids Club'),
                    if (widget.hotel.hasConciergeService)
                      _buildAmenityItem(
                          Icons.support_agent, 'Concierge Service'),
                    if (widget.hotel.hasLaundryService)
                      _buildAmenityItem(
                          Icons.local_laundry_service, 'Laundry Service'),
                    if (widget.hotel.hasShuttleService)
                      _buildAmenityItem(
                          Icons.airport_shuttle, 'Shuttle Service'),
                    if (widget.hotel.has24HrFrontDesk)
                      _buildAmenityItem(
                          Icons.access_time, '24-Hour Front Desk'),
                  ],
                ),
              ),
            ),
          ),

          SizedBox(height: 16.h),

          // Additional amenities
          AnimatedSection(
            title: 'Additional Amenities',
            content: Card(
              child: Padding(
                padding: EdgeInsets.all(16.r),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    for (final amenity in widget.hotel.amenities)
                      Padding(
                        padding: EdgeInsets.only(bottom: 8.h),
                        child: Row(
                          children: [
                            Icon(
                              Icons.check_circle,
                              size: 16.r,
                              color: Theme.of(context).colorScheme.primary,
                            ),
                            SizedBox(width: 8.w),
                            Expanded(
                              child: Text(
                                amenity,
                                style: Theme.of(context).textTheme.bodyMedium,
                              ),
                            ),
                          ],
                        ),
                      ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildReviewsTab() {
    return SingleChildScrollView(
      padding: EdgeInsets.all(16.r),
      child: HotelReviewList(
        hotel: widget.hotel,
        maxReviews: 3,
        showSeeAllButton: true,
      ),
    );
  }

  Widget _buildRoomFeature(IconData icon, String text) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(
          icon,
          size: 16.r,
          color: Theme.of(context).colorScheme.primary,
        ),
        SizedBox(width: 4.w),
        Text(
          text,
          style: Theme.of(context).textTheme.bodySmall,
        ),
      ],
    );
  }

  Widget _buildAmenityItem(IconData icon, String text) {
    return SizedBox(
      width: 100.w,
      child: Column(
        children: [
          Icon(
            icon,
            size: 32.r,
            color: Theme.of(context).colorScheme.primary,
          ),
          SizedBox(height: 8.h),
          Text(
            text,
            style: Theme.of(context).textTheme.bodySmall,
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  int _getStarRatingValue(HotelStarRating rating) {
    switch (rating) {
      case HotelStarRating.oneStar:
        return 1;
      case HotelStarRating.twoStar:
        return 2;
      case HotelStarRating.threeStar:
        return 3;
      case HotelStarRating.fourStar:
        return 4;
      case HotelStarRating.fiveStar:
        return 5;
    }
  }

  Widget _buildPricesTab() {
    return SingleChildScrollView(
      padding: EdgeInsets.all(16.r),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Price comparison section
          Card(
            child: Padding(
              padding: EdgeInsets.all(16.r),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Price Comparison',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                  ),
                  SizedBox(height: 16.h),
                  Text(
                    'Compare prices across different booking platforms',
                    style: Theme.of(context).textTheme.bodyMedium,
                  ),
                  SizedBox(height: 16.h),
                  // Placeholder for price comparison data
                  ListView.builder(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    itemCount: 3,
                    itemBuilder: (context, index) {
                      final platforms = [
                        'Booking.com',
                        'Expedia',
                        'Hotels.com'
                      ];
                      final prices = [
                        widget.hotel.price * 0.95,
                        widget.hotel.price,
                        widget.hotel.price * 1.05
                      ];

                      return ListTile(
                        leading: Icon(
                          Icons.business,
                          color: Theme.of(context).colorScheme.primary,
                        ),
                        title: Text(platforms[index]),
                        trailing: Text(
                          '${widget.hotel.currency}${prices[index].toStringAsFixed(2)}',
                          style: Theme.of(context)
                              .textTheme
                              .titleMedium
                              ?.copyWith(
                                fontWeight: FontWeight.bold,
                                color: index == 0
                                    ? Colors.green
                                    : Theme.of(context).colorScheme.onSurface,
                              ),
                        ),
                      );
                    },
                  ),
                ],
              ),
            ),
          ),

          SizedBox(height: 24.h),

          // Price history section
          Card(
            child: Padding(
              padding: EdgeInsets.all(16.r),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Price History',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                  ),
                  SizedBox(height: 16.h),
                  Text(
                    'Track price changes over time',
                    style: Theme.of(context).textTheme.bodyMedium,
                  ),
                  SizedBox(height: 24.h),
                  // Placeholder for price history chart
                  Container(
                    height: 200.h,
                    width: double.infinity,
                    decoration: BoxDecoration(
                      color: Theme.of(context).colorScheme.surfaceContainerLow,
                      borderRadius: BorderRadius.circular(8.r),
                    ),
                    child: Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.bar_chart,
                            size: 48.r,
                            color: Theme.of(context).colorScheme.primary,
                          ),
                          SizedBox(height: 16.h),
                          Text(
                            'Price history chart coming soon',
                            style: Theme.of(context).textTheme.bodyMedium,
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),

          SizedBox(height: 24.h),

          // View full comparison button
          Center(
            child: ElevatedButton.icon(
              onPressed: () {
                _loggingService.debug(
                  'HotelDetailsScreenEnhanced',
                  'User tapped View Full Price Comparison button',
                );
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Price comparison feature coming soon'),
                    duration: Duration(seconds: 2),
                  ),
                );
              },
              icon: const Icon(Icons.compare_arrows),
              label: const Text('View Full Price Comparison'),
              style: ElevatedButton.styleFrom(
                padding: EdgeInsets.symmetric(horizontal: 24.w, vertical: 12.h),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
