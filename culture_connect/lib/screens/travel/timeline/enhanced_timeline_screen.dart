import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_animate/flutter_animate.dart';

import 'package:culture_connect/models/travel/timeline.dart';
import 'package:culture_connect/models/travel/timeline_event.dart';
import 'package:culture_connect/models/ar/ar_content_marker.dart';
import 'package:culture_connect/providers/travel/timeline_providers.dart';
import 'package:culture_connect/providers/ar/ar_content_providers.dart';
import 'package:culture_connect/screens/ar/ar_content_preview_screen.dart';
import 'package:culture_connect/screens/travel/timeline/timeline_event_details_screen.dart';
import 'package:culture_connect/services/logging_service.dart';
import 'package:culture_connect/widgets/common/custom_app_bar.dart';
import 'package:culture_connect/widgets/common/error_view.dart';
import 'package:culture_connect/widgets/common/loading_indicator.dart';
import 'package:culture_connect/widgets/offline/offline_banner.dart';
import 'package:culture_connect/widgets/travel/timeline/ar_timeline_view.dart';

/// A screen for displaying an enhanced timeline with AR integration
class EnhancedTimelineScreen extends ConsumerStatefulWidget {
  /// The timeline ID
  final String timelineId;

  /// Creates a new enhanced timeline screen
  const EnhancedTimelineScreen({
    super.key,
    required this.timelineId,
  });

  @override
  ConsumerState<EnhancedTimelineScreen> createState() =>
      _EnhancedTimelineScreenState();
}

class _EnhancedTimelineScreenState extends ConsumerState<EnhancedTimelineScreen>
    with SingleTickerProviderStateMixin {
  /// Animation controller for AR content animations
  late AnimationController _animationController;

  /// Whether AR content is being previewed
  bool _isPreviewingAR = false;

  /// The currently selected event for AR preview
  TimelineEvent? _selectedEvent;

  /// Logging service
  final LoggingService _loggingService = LoggingService();

  @override
  void initState() {
    super.initState();

    // Initialize animation controller
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 500),
    );

    // Load the timeline
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref
          .read(currentTimelineProvider.notifier)
          .loadTimeline(widget.timelineId);
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  /// Build the app bar
  PreferredSizeWidget _buildAppBar(Timeline timeline) {
    return CustomAppBar(
      title: timeline.title,
      showBackButton: true,
      actions: [
        // AR content filter button
        IconButton(
          icon: const Icon(Icons.view_in_ar),
          tooltip: 'Show AR Content Only',
          onPressed: () {
            // Toggle AR content filter
            ref.read(timelineARFilterProvider.notifier).state =
                !ref.read(timelineARFilterProvider);
          },
        ),

        // Share button
        IconButton(
          icon: const Icon(Icons.share),
          tooltip: 'Share Timeline',
          onPressed: () {
            // Share timeline
          },
        ),
      ],
    );
  }

  /// Handle event tap
  void _handleEventTap(TimelineEvent event) {
    try {
      Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) => TimelineEventDetailsScreen(
            event: event,
          ),
        ),
      );
    } catch (e, stackTrace) {
      // Log the error
      _loggingService.error(
        'EnhancedTimelineScreen',
        'Error navigating to event details',
        e,
        stackTrace,
      );

      // Show a snackbar to the user
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Failed to open event details. Please try again.'),
        ),
      );
    }
  }

  /// Handle AR content tap
  void _handleARContentTap(TimelineEvent event) {
    if (event.arContentId == null) return;

    try {
      if (!mounted) return;
      setState(() {
        _isPreviewingAR = true;
        _selectedEvent = event;
      });

      // Load AR content
      ref
          .read(currentARContentMarkerProvider.notifier)
          .loadARContentMarker(event.arContentId!);

      // Start animation
      _animationController.forward();
    } catch (e, stackTrace) {
      _loggingService.error(
        'EnhancedTimelineScreen',
        'Error handling AR content tap',
        e,
        stackTrace,
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Failed to load AR content. Please try again.'),
          ),
        );
      }
    }
  }

  /// Handle AR preview close
  void _handleARPreviewClose() {
    try {
      // Reverse animation and then hide preview
      _animationController.reverse().then((_) {
        if (!mounted) return;
        setState(() {
          _isPreviewingAR = false;
          _selectedEvent = null;
        });
      });
    } catch (e, stackTrace) {
      _loggingService.error(
        'EnhancedTimelineScreen',
        'Error closing AR preview',
        e,
        stackTrace,
      );
    }
  }

  /// Navigate to full AR preview
  void _navigateToFullARPreview(String arContentId) {
    try {
      // Close the preview overlay first
      _handleARPreviewClose();

      // Navigate to full AR preview
      Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) => ARContentPreviewScreen(
            arContentId: arContentId,
          ),
        ),
      );
    } catch (e, stackTrace) {
      _loggingService.error(
        'EnhancedTimelineScreen',
        'Error navigating to full AR preview',
        e,
        stackTrace,
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Failed to open AR preview. Please try again.'),
          ),
        );
      }
    }
  }

  /// Build the AR preview overlay
  Widget _buildARPreviewOverlay() {
    if (!_isPreviewingAR || _selectedEvent?.arContentId == null) {
      return const SizedBox.shrink();
    }

    final arContentMarkerAsync = ref.watch(currentARContentMarkerProvider);

    return Positioned.fill(
      child: GestureDetector(
        onTap: _handleARPreviewClose,
        child: Container(
          color: Colors.black.withAlpha(179),
          child: Center(
            child: arContentMarkerAsync.when(
              data: (marker) {
                if (marker == null) {
                  return const Text(
                    'AR content not found',
                    style: TextStyle(color: Colors.white),
                  );
                }

                return _buildARPreviewContent(marker);
              },
              loading: () => const LoadingIndicator(),
              error: (error, stackTrace) => ErrorView(
                error: error.toString(),
                onRetry: () {
                  if (_selectedEvent?.arContentId != null) {
                    ref
                        .read(currentARContentMarkerProvider.notifier)
                        .loadARContentMarker(_selectedEvent!.arContentId!);
                  }
                },
              ),
            ),
          ),
        ),
      ),
    ).animate(controller: _animationController).fadeIn();
  }

  /// Build the AR preview content
  Widget _buildARPreviewContent(ARContentMarker marker) {
    return Container(
      width: 300.w,
      padding: EdgeInsets.all(16.r),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16.r),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Header
          Row(
            children: [
              // AR type icon
              Container(
                width: 40.r,
                height: 40.r,
                decoration: BoxDecoration(
                  color: marker.contentType.color.withAlpha(25),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  marker.contentType.icon,
                  color: marker.contentType.color,
                  size: 20.r,
                ),
              ),

              SizedBox(width: 12.w),

              // Title
              Expanded(
                child: Text(
                  marker.title,
                  style: TextStyle(
                    fontSize: 18.sp,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),

              // Close button
              IconButton(
                icon: const Icon(Icons.close),
                onPressed: _handleARPreviewClose,
              ),
            ],
          ),

          SizedBox(height: 16.h),

          // Preview image or placeholder
          Container(
            height: 150.h,
            width: double.infinity,
            decoration: BoxDecoration(
              color: Colors.grey[200],
              borderRadius: BorderRadius.circular(8.r),
              image: marker.thumbnailUrl != null
                  ? DecorationImage(
                      image: NetworkImage(marker.thumbnailUrl!),
                      fit: BoxFit.cover,
                    )
                  : null,
            ),
            child: marker.thumbnailUrl == null
                ? Center(
                    child: Icon(
                      marker.contentType.icon,
                      size: 48.r,
                      color: Colors.grey[400],
                    ),
                  )
                : null,
          ),

          SizedBox(height: 16.h),

          // Description
          if (marker.description != null) ...[
            Text(
              marker.description!,
              style: TextStyle(
                fontSize: 14.sp,
                color: Colors.grey[700],
              ),
              maxLines: 3,
              overflow: TextOverflow.ellipsis,
            ),
            SizedBox(height: 16.h),
          ],

          // Actions
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              // View in AR button
              ElevatedButton.icon(
                onPressed: () {
                  if (_selectedEvent?.arContentId != null) {
                    _navigateToFullARPreview(_selectedEvent!.arContentId!);
                  }
                },
                icon: const Icon(Icons.view_in_ar),
                label: const Text('View in AR'),
              ),
            ],
          ),
        ],
      ),
    ).animate().scale(
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOutBack,
        );
  }

  @override
  Widget build(BuildContext context) {
    final timelineAsync = ref.watch(currentTimelineProvider);
    final showARContentOnly = ref.watch(timelineARFilterProvider);

    return Scaffold(
      body: timelineAsync.when(
        data: (timeline) {
          if (timeline == null) {
            return const Center(
              child: Text('Timeline not found'),
            );
          }

          return Stack(
            children: [
              Scaffold(
                appBar: _buildAppBar(timeline),
                body: OfflineBannerWrapper(
                  message: 'You are offline. Some features may be limited.',
                  actionText: 'CONTINUE',
                  child: ARTimelineView(
                    timeline: timeline,
                    showARContentOnly: showARContentOnly,
                    onEventTap: _handleEventTap,
                    onARContentTap: _handleARContentTap,
                  ),
                ),
                floatingActionButton: FloatingActionButton(
                  onPressed: () {
                    // Navigate to add event screen
                  },
                  child: const Icon(Icons.add),
                ),
              ),

              // AR preview overlay
              _buildARPreviewOverlay(),
            ],
          );
        },
        loading: () => const Center(child: LoadingIndicator()),
        error: (error, stackTrace) => Center(
          child: ErrorView(
            error: error.toString(),
            onRetry: () {
              ref
                  .read(currentTimelineProvider.notifier)
                  .loadTimeline(widget.timelineId);
            },
          ),
        ),
      ),
    );
  }
}
