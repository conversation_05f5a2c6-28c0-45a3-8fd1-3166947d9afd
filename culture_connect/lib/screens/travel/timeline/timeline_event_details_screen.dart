import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';

import 'package:culture_connect/models/travel/timeline_event.dart';
import 'package:culture_connect/models/ar/ar_content_marker.dart';
import 'package:culture_connect/providers/ar/ar_content_providers.dart';
import 'package:culture_connect/widgets/common/custom_app_bar.dart';
import 'package:culture_connect/widgets/common/loading_indicator.dart';
import 'package:culture_connect/widgets/common/error_view.dart';
import 'package:culture_connect/widgets/travel/timeline/ar_content_badge.dart';
import 'package:culture_connect/screens/ar/ar_content_preview_screen.dart';

/// A screen for displaying timeline event details
class TimelineEventDetailsScreen extends ConsumerStatefulWidget {
  /// The event to display
  final TimelineEvent event;
  
  /// Creates a new timeline event details screen
  const TimelineEventDetailsScreen({
    super.key,
    required this.event,
  });
  
  @override
  ConsumerState<TimelineEventDetailsScreen> createState() => _TimelineEventDetailsScreenState();
}

class _TimelineEventDetailsScreenState extends ConsumerState<TimelineEventDetailsScreen> {
  /// The date format
  final DateFormat _dateFormat = DateFormat('EEEE, MMMM d, yyyy');
  
  @override
  void initState() {
    super.initState();
    
    // Load AR content if available
    if (widget.event.hasARContent && widget.event.arContentId != null) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        ref.read(currentARContentMarkerProvider.notifier).loadARContentMarker(widget.event.arContentId!);
      });
    }
  }
  
  /// Build the event header
  Widget _buildEventHeader() {
    return Container(
      padding: EdgeInsets.all(16.r),
      decoration: BoxDecoration(
        color: widget.event.typeColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8.r),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title and icon
          Row(
            children: [
              // Event icon
              Container(
                width: 48.r,
                height: 48.r,
                decoration: BoxDecoration(
                  color: widget.event.typeColor.withOpacity(0.2),
                  shape: BoxShape.circle,
                ),
                child: Center(
                  child: Icon(
                    widget.event.typeIcon,
                    color: widget.event.typeColor,
                    size: 24.r,
                  ),
                ),
              ),
              
              SizedBox(width: 16.w),
              
              // Title
              Expanded(
                child: Text(
                  widget.event.title,
                  style: TextStyle(
                    fontSize: 20.sp,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              
              // AR content badge
              if (widget.event.hasARContent) ...[
                ARContentBadge(
                  arContentId: widget.event.arContentId,
                  size: 32.r,
                  onTap: () {
                    if (widget.event.arContentId != null) {
                      _navigateToARPreview(widget.event.arContentId!);
                    }
                  },
                ),
              ],
            ],
          ),
          
          SizedBox(height: 16.h),
          
          // Date and time
          Row(
            children: [
              Icon(
                Icons.calendar_today,
                size: 16.sp,
                color: Colors.grey[600],
              ),
              
              SizedBox(width: 8.w),
              
              Text(
                _dateFormat.format(widget.event.eventDate),
                style: TextStyle(
                  fontSize: 14.sp,
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
          
          SizedBox(height: 8.h),
          
          // Time
          if (widget.event.eventTime != null) ...[
            Row(
              children: [
                Icon(
                  Icons.access_time,
                  size: 16.sp,
                  color: Colors.grey[600],
                ),
                
                SizedBox(width: 8.w),
                
                Text(
                  widget.event.formattedTimeRange ?? widget.event.formattedTime!,
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
            
            SizedBox(height: 8.h),
          ],
          
          // Location
          if (widget.event.location != null) ...[
            Row(
              children: [
                Icon(
                  Icons.location_on,
                  size: 16.sp,
                  color: Colors.grey[600],
                ),
                
                SizedBox(width: 8.w),
                
                Expanded(
                  child: Text(
                    widget.event.location!,
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: Colors.grey[600],
                    ),
                  ),
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }
  
  /// Build the event details
  Widget _buildEventDetails() {
    return Container(
      padding: EdgeInsets.all(16.r),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Section title
          Text(
            'Details',
            style: TextStyle(
              fontSize: 18.sp,
              fontWeight: FontWeight.bold,
            ),
          ),
          
          SizedBox(height: 16.h),
          
          // Description
          if (widget.event.description != null) ...[
            Text(
              widget.event.description!,
              style: TextStyle(
                fontSize: 16.sp,
              ),
            ),
            
            SizedBox(height: 16.h),
          ],
          
          // Status
          Row(
            children: [
              Text(
                'Status:',
                style: TextStyle(
                  fontSize: 14.sp,
                  fontWeight: FontWeight.bold,
                ),
              ),
              
              SizedBox(width: 8.w),
              
              Container(
                padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
                decoration: BoxDecoration(
                  color: widget.event.statusColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(4.r),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      widget.event.statusIcon,
                      size: 14.sp,
                      color: widget.event.statusColor,
                    ),
                    
                    SizedBox(width: 4.w),
                    
                    Text(
                      widget.event.status.toUpperCase(),
                      style: TextStyle(
                        fontSize: 12.sp,
                        fontWeight: FontWeight.bold,
                        color: widget.event.statusColor,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          
          SizedBox(height: 8.h),
          
          // Type
          Row(
            children: [
              Text(
                'Type:',
                style: TextStyle(
                  fontSize: 14.sp,
                  fontWeight: FontWeight.bold,
                ),
              ),
              
              SizedBox(width: 8.w),
              
              Container(
                padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
                decoration: BoxDecoration(
                  color: widget.event.typeColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(4.r),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      widget.event.typeIcon,
                      size: 14.sp,
                      color: widget.event.typeColor,
                    ),
                    
                    SizedBox(width: 4.w),
                    
                    Text(
                      widget.event.eventType.toUpperCase(),
                      style: TextStyle(
                        fontSize: 12.sp,
                        fontWeight: FontWeight.bold,
                        color: widget.event.typeColor,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          
          // Duration
          if (widget.event.durationMinutes != null) ...[
            SizedBox(height: 8.h),
            
            Row(
              children: [
                Text(
                  'Duration:',
                  style: TextStyle(
                    fontSize: 14.sp,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                
                SizedBox(width: 8.w),
                
                Text(
                  widget.event.formattedDuration!,
                  style: TextStyle(
                    fontSize: 14.sp,
                  ),
                ),
              ],
            ),
          ],
          
          // Booking reference
          if (widget.event.bookingReference != null) ...[
            SizedBox(height: 8.h),
            
            Row(
              children: [
                Text(
                  'Booking Reference:',
                  style: TextStyle(
                    fontSize: 14.sp,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                
                SizedBox(width: 8.w),
                
                Text(
                  widget.event.bookingReference!,
                  style: TextStyle(
                    fontSize: 14.sp,
                  ),
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }
  
  /// Build the AR content section
  Widget _buildARContentSection() {
    if (!widget.event.hasARContent || widget.event.arContentId == null) {
      return const SizedBox.shrink();
    }
    
    final arContentMarkerAsync = ref.watch(currentARContentMarkerProvider);
    
    return arContentMarkerAsync.when(
      data: (marker) {
        if (marker == null) {
          return const SizedBox.shrink();
        }
        
        return Container(
          padding: EdgeInsets.all(16.r),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(8.r),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.05),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Section title
              Row(
                children: [
                  Icon(
                    Icons.view_in_ar,
                    size: 20.sp,
                    color: marker.contentType.color,
                  ),
                  
                  SizedBox(width: 8.w),
                  
                  Text(
                    'AR Content',
                    style: TextStyle(
                      fontSize: 18.sp,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
              
              SizedBox(height: 16.h),
              
              // AR content details
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Thumbnail
                  Container(
                    width: 80.r,
                    height: 80.r,
                    decoration: BoxDecoration(
                      color: marker.contentType.color.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8.r),
                      image: marker.thumbnailUrl != null
                          ? DecorationImage(
                              image: NetworkImage(marker.thumbnailUrl!),
                              fit: BoxFit.cover,
                            )
                          : null,
                    ),
                    child: marker.thumbnailUrl == null
                        ? Center(
                            child: Icon(
                              marker.contentType.icon,
                              size: 40.r,
                              color: marker.contentType.color,
                            ),
                          )
                        : null,
                  ),
                  
                  SizedBox(width: 16.w),
                  
                  // Content details
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Title
                        Text(
                          marker.title,
                          style: TextStyle(
                            fontSize: 16.sp,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        
                        SizedBox(height: 4.h),
                        
                        // Type
                        Row(
                          children: [
                            Icon(
                              marker.contentType.icon,
                              size: 14.sp,
                              color: marker.contentType.color,
                            ),
                            
                            SizedBox(width: 4.w),
                            
                            Text(
                              marker.contentType.displayName,
                              style: TextStyle(
                                fontSize: 12.sp,
                                color: Colors.grey[600],
                              ),
                            ),
                          ],
                        ),
                        
                        SizedBox(height: 4.h),
                        
                        // Size
                        if (marker.contentSize != null) ...[
                          Row(
                            children: [
                              Icon(
                                Icons.storage,
                                size: 14.sp,
                                color: Colors.grey[600],
                              ),
                              
                              SizedBox(width: 4.w),
                              
                              Text(
                                marker.formattedSize!,
                                style: TextStyle(
                                  fontSize: 12.sp,
                                  color: Colors.grey[600],
                                ),
                              ),
                            ],
                          ),
                        ],
                        
                        SizedBox(height: 4.h),
                        
                        // Offline status
                        Row(
                          children: [
                            Icon(
                              marker.isAvailableOffline ? Icons.offline_bolt : Icons.cloud_download,
                              size: 14.sp,
                              color: marker.isAvailableOffline ? Colors.green : Colors.blue,
                            ),
                            
                            SizedBox(width: 4.w),
                            
                            Text(
                              marker.isAvailableOffline ? 'Available Offline' : 'Online Only',
                              style: TextStyle(
                                fontSize: 12.sp,
                                color: marker.isAvailableOffline ? Colors.green : Colors.blue,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              
              SizedBox(height: 16.h),
              
              // Action buttons
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  // Preview button
                  ElevatedButton.icon(
                    onPressed: () {
                      _navigateToARPreview(marker.id);
                    },
                    icon: const Icon(Icons.view_in_ar),
                    label: const Text('Preview'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: marker.contentType.color,
                      foregroundColor: Colors.white,
                    ),
                  ),
                  
                  // Download/Remove button
                  ElevatedButton.icon(
                    onPressed: () {
                      if (marker.isAvailableOffline) {
                        _removeARContentFromOfflineStorage();
                      } else {
                        _downloadARContentForOfflineUse();
                      }
                    },
                    icon: Icon(
                      marker.isAvailableOffline ? Icons.delete : Icons.download,
                    ),
                    label: Text(
                      marker.isAvailableOffline ? 'Remove Offline' : 'Download',
                    ),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: marker.isAvailableOffline ? Colors.red : Colors.blue,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ],
              ),
            ],
          ),
        );
      },
      loading: () => const Center(child: LoadingIndicator()),
      error: (error, stackTrace) => Center(
        child: ErrorView(
          error: error.toString(),
          onRetry: () {
            if (widget.event.arContentId != null) {
              ref.read(currentARContentMarkerProvider.notifier).loadARContentMarker(widget.event.arContentId!);
            }
          },
        ),
      ),
    );
  }
  
  /// Navigate to AR preview screen
  void _navigateToARPreview(String arContentId) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => ARContentPreviewScreen(
          arContentId: arContentId,
        ),
      ),
    );
  }
  
  /// Download AR content for offline use
  Future<void> _downloadARContentForOfflineUse() async {
    final result = await ref.read(currentARContentMarkerProvider.notifier).downloadForOfflineUse();
    
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            result ? 'AR content downloaded for offline use' : 'Failed to download AR content',
          ),
          backgroundColor: result ? Colors.green : Colors.red,
        ),
      );
    }
  }
  
  /// Remove AR content from offline storage
  Future<void> _removeARContentFromOfflineStorage() async {
    final result = await ref.read(currentARContentMarkerProvider.notifier).removeFromOfflineStorage();
    
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            result ? 'AR content removed from offline storage' : 'Failed to remove AR content',
          ),
          backgroundColor: result ? Colors.green : Colors.red,
        ),
      );
    }
  }
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title: 'Event Details',
        showBackButton: true,
        actions: [
          // Edit button
          IconButton(
            icon: const Icon(Icons.edit),
            onPressed: () {
              // Navigate to edit screen
            },
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(16.r),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Event header
            _buildEventHeader(),
            
            SizedBox(height: 16.h),
            
            // Event details
            _buildEventDetails(),
            
            SizedBox(height: 16.h),
            
            // AR content section
            _buildARContentSection(),
          ],
        ),
      ),
    );
  }
}
