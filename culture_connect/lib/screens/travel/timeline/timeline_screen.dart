import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'package:culture_connect/models/travel/timeline.dart';
import 'package:culture_connect/models/travel/timeline_event.dart';
import 'package:culture_connect/providers/travel/timeline_providers.dart';
import 'package:culture_connect/widgets/common/custom_app_bar.dart';
import 'package:culture_connect/widgets/common/loading_indicator.dart';
import 'package:culture_connect/widgets/common/error_view.dart';
import 'package:culture_connect/widgets/travel/timeline/timeline_view.dart';
import 'package:culture_connect/screens/travel/timeline/timeline_event_details_screen.dart';
import 'package:culture_connect/widgets/offline/offline_banner.dart';

/// A screen for displaying a timeline
class TimelineScreen extends ConsumerStatefulWidget {
  /// The timeline ID
  final String timelineId;

  /// Creates a new timeline screen
  const TimelineScreen({
    super.key,
    required this.timelineId,
  });

  @override
  ConsumerState<TimelineScreen> createState() => _TimelineScreenState();
}

class _TimelineScreenState extends ConsumerState<TimelineScreen> {
  @override
  void initState() {
    super.initState();

    // Load the timeline
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref
          .read(currentTimelineProvider.notifier)
          .loadTimeline(widget.timelineId);
    });
  }

  /// Handle event tap
  void _handleEventTap(TimelineEvent event) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => TimelineEventDetailsScreen(
          event: event,
        ),
      ),
    );
  }

  /// Handle event long press
  void _handleEventLongPress(TimelineEvent event) {
    // Show options dialog
    showModalBottomSheet(
      context: context,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(16.r),
        ),
      ),
      builder: (context) {
        return SafeArea(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Header
              Padding(
                padding: EdgeInsets.all(16.r),
                child: Text(
                  'Event Options',
                  style: TextStyle(
                    fontSize: 18.sp,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),

              const Divider(),

              // View details
              ListTile(
                leading: const Icon(Icons.info_outline),
                title: const Text('View Details'),
                onTap: () {
                  Navigator.of(context).pop();
                  _handleEventTap(event);
                },
              ),

              // Edit event
              ListTile(
                leading: const Icon(Icons.edit),
                title: const Text('Edit Event'),
                onTap: () {
                  Navigator.of(context).pop();
                  // Navigate to edit screen
                },
              ),

              // Delete event
              ListTile(
                leading: const Icon(Icons.delete, color: Colors.red),
                title: const Text('Delete Event',
                    style: TextStyle(color: Colors.red)),
                onTap: () {
                  Navigator.of(context).pop();
                  _showDeleteConfirmation(event);
                },
              ),

              SizedBox(height: 16.h),
            ],
          ),
        );
      },
    );
  }

  /// Handle event drag end
  void _handleEventDragEnd(TimelineEvent event, DateTime newDate) {
    // Update the event with the new date
    final updatedEvent = event.copyWith(
      eventDate: newDate,
    );

    // Update the event in the timeline
    ref.read(currentTimelineProvider.notifier).updateEvent(updatedEvent);
  }

  /// Show delete confirmation dialog
  void _showDeleteConfirmation(TimelineEvent event) {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('Delete Event'),
          content: Text('Are you sure you want to delete "${event.title}"?'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                ref
                    .read(currentTimelineProvider.notifier)
                    .removeEvent(event.id);
              },
              child: const Text('Delete', style: TextStyle(color: Colors.red)),
            ),
          ],
        );
      },
    );
  }

  /// Build the app bar
  PreferredSizeWidget _buildAppBar(Timeline timeline) {
    return CustomAppBar(
      title: timeline.title,
      showBackButton: true,
      actions: [
        // AR Timeline button
        IconButton(
          icon: const Icon(Icons.view_in_ar),
          tooltip: 'View AR Timeline',
          onPressed: () {
            // Navigate to AR Timeline screen
            Navigator.of(context).pushNamed(
              '/timeline/ar',
              arguments: {'timelineId': widget.timelineId},
            );
          },
        ),

        // Share button
        IconButton(
          icon: const Icon(Icons.share),
          tooltip: 'Share Timeline',
          onPressed: () {
            // Show share options
          },
        ),

        // More options
        PopupMenuButton<String>(
          onSelected: (value) {
            switch (value) {
              case 'edit':
                // Navigate to edit screen
                break;
              case 'delete':
                _showDeleteTimelineConfirmation(timeline);
                break;
              case 'export':
                // Export timeline
                break;
            }
          },
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'edit',
              child: Text('Edit Timeline'),
            ),
            const PopupMenuItem(
              value: 'export',
              child: Text('Export Timeline'),
            ),
            const PopupMenuItem(
              value: 'delete',
              child:
                  Text('Delete Timeline', style: TextStyle(color: Colors.red)),
            ),
          ],
        ),
      ],
    );
  }

  /// Show delete timeline confirmation dialog
  void _showDeleteTimelineConfirmation(Timeline timeline) {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('Delete Timeline'),
          content: Text('Are you sure you want to delete "${timeline.title}"?'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                ref.read(currentTimelineProvider.notifier).deleteTimeline();
                Navigator.of(context).pop(); // Go back to previous screen
              },
              child: const Text('Delete', style: TextStyle(color: Colors.red)),
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    final timelineAsync = ref.watch(currentTimelineProvider);

    return Scaffold(
      body: timelineAsync.when(
        data: (timeline) {
          if (timeline == null) {
            return const Center(
              child: Text('Timeline not found'),
            );
          }

          return Scaffold(
            appBar: _buildAppBar(timeline),
            body: OfflineBannerWrapper(
              message: 'You are offline. Some features may be limited.',
              actionText: 'CONTINUE',
              child: TimelineView(
                timeline: timeline,
                allowEditing: true,
                onEventTap: _handleEventTap,
                onEventLongPress: _handleEventLongPress,
                onEventDragEnd: _handleEventDragEnd,
              ),
            ),
            floatingActionButton: FloatingActionButton(
              onPressed: () {
                // Navigate to add event screen
              },
              child: const Icon(Icons.add),
            ),
          );
        },
        loading: () => const Center(child: LoadingIndicator()),
        error: (error, stackTrace) => Center(
          child: ErrorView(
            error: error.toString(),
            onRetry: () {
              ref
                  .read(currentTimelineProvider.notifier)
                  .loadTimeline(widget.timelineId);
            },
          ),
        ),
      ),
    );
  }
}
