import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';

import 'package:culture_connect/models/booking.dart';
import 'package:culture_connect/models/location/geo_location.dart';
import 'package:culture_connect/models/travel/travel.dart';

// Mock data for security bookings
final mockSecurityBookingsProvider = Provider<List<SecurityBooking>>((ref) {
  return [
    SecurityBooking(
      id: 'SEC-**********',
      securityService: PrivateSecurity(
        id: 'sec1',
        name: 'Personal Bodyguard Service',
        description:
            'Professional bodyguard service for personal protection during your stay.',
        price: 150.0,
        currency: 'USD',
        rating: 4.9,
        reviewCount: 75,
        imageUrl: 'https://example.com/security/bodyguard.jpg',
        additionalImages: [],
        provider: 'Elite Security',
        location: 'Lagos, Nigeria',
        coordinates: const GeoLocation(latitude: 6.5244, longitude: 3.3792),
        isAvailable: true,
        isFeatured: true,
        isOnSale: false,
        tags: ['bodyguard', 'personal', 'protection'],
        amenities: ['24/7 support', 'communication equipment'],
        cancellationPolicy:
            'Free cancellation up to 24 hours before the service starts.',
        createdAt: DateTime.now().subtract(const Duration(days: 30)),
        updatedAt: DateTime.now().subtract(const Duration(days: 30)),
        serviceType: SecurityServiceType.personalBodyguard,
        personnelCount: 1,
        trainingLevel: SecurityTrainingLevel.expert,
        isArmed: false,
        isUniformed: true,
        hasVehicle: true,
        vehicleType: 'SUV',
        hasCommunicationEquipment: true,
        languages: ['English', 'French'],
        securityCompany: 'Elite Security',
        securityCompanyLogoUrl: 'https://example.com/logos/elite_security.png',
        includesBackgroundChecks: true,
        includesRiskAssessment: true,
        includesEmergencyResponse: true,
        includes24HrSupport: true,
      ),
      startDate: DateTime.now().add(const Duration(days: 5)),
      endDate: DateTime.now().add(const Duration(days: 8)),
      personnelCount: 1,
      hasVehicle: true,
      vehicleType: 'SUV',
      specialInstructions: 'Need to visit several tourist locations.',
      totalPrice: 450.0,
      status: BookingStatus.confirmed,
      createdAt: DateTime.now().subtract(const Duration(days: 2)),
    ),
    SecurityBooking(
      id: 'SEC-**********',
      securityService: PrivateSecurity(
        id: 'sec2',
        name: 'Executive Protection Team',
        description:
            'Full team of security professionals for high-profile clients.',
        price: 350.0,
        currency: 'USD',
        rating: 4.8,
        reviewCount: 42,
        imageUrl: 'https://example.com/security/executive.jpg',
        additionalImages: [],
        provider: 'Sentinel Security',
        location: 'Nairobi, Kenya',
        coordinates: const GeoLocation(latitude: -1.2921, longitude: 36.8219),
        isAvailable: true,
        isFeatured: false,
        isOnSale: false,
        tags: ['executive', 'team', 'high-profile'],
        amenities: ['24/7 support', 'armored vehicle', 'risk assessment'],
        cancellationPolicy:
            'Free cancellation up to 48 hours before the service starts.',
        createdAt: DateTime.now().subtract(const Duration(days: 60)),
        updatedAt: DateTime.now().subtract(const Duration(days: 45)),
        serviceType: SecurityServiceType.executiveProtection,
        personnelCount: 3,
        trainingLevel: SecurityTrainingLevel.expert,
        isArmed: true,
        isUniformed: false,
        hasVehicle: true,
        vehicleType: 'Armored SUV',
        hasCommunicationEquipment: true,
        languages: ['English', 'Swahili', 'French'],
        securityCompany: 'Sentinel Security',
        securityCompanyLogoUrl:
            'https://example.com/logos/sentinel_security.png',
        includesBackgroundChecks: true,
        includesRiskAssessment: true,
        includesEmergencyResponse: true,
        includes24HrSupport: true,
      ),
      startDate: DateTime.now().subtract(const Duration(days: 10)),
      endDate: DateTime.now().subtract(const Duration(days: 5)),
      personnelCount: 2,
      hasVehicle: true,
      vehicleType: 'Armored SUV',
      specialInstructions: 'Business meetings in downtown area.',
      totalPrice: 1750.0,
      status: BookingStatus.completed,
      createdAt: DateTime.now().subtract(const Duration(days: 20)),
    ),
  ];
});

/// Model for security bookings
class SecurityBooking {
  /// Unique identifier for the booking
  final String id;

  /// The security service that was booked
  final PrivateSecurity securityService;

  /// Start date of the service
  final DateTime startDate;

  /// End date of the service
  final DateTime endDate;

  /// Number of security personnel
  final int personnelCount;

  /// Whether a vehicle is included
  final bool hasVehicle;

  /// Type of vehicle (if applicable)
  final String? vehicleType;

  /// Special instructions for the service
  final String specialInstructions;

  /// Total price of the booking
  final double totalPrice;

  /// Status of the booking
  final BookingStatus status;

  /// When the booking was created
  final DateTime createdAt;

  /// Creates a new security booking
  const SecurityBooking({
    required this.id,
    required this.securityService,
    required this.startDate,
    required this.endDate,
    required this.personnelCount,
    required this.hasVehicle,
    this.vehicleType,
    required this.specialInstructions,
    required this.totalPrice,
    required this.status,
    required this.createdAt,
  });
}

/// Screen for managing private security bookings
class PrivateSecurityManagementScreen extends ConsumerWidget {
  /// Creates a new private security management screen
  const PrivateSecurityManagementScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final bookings = ref.watch(mockSecurityBookingsProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('My Security Bookings'),
      ),
      body: bookings.isEmpty
          ? Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.security,
                    size: 64,
                    color: theme.colorScheme.onSurfaceVariant,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'No security bookings yet',
                    style: theme.textTheme.titleLarge,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Your security service bookings will appear here',
                    style: theme.textTheme.bodyMedium,
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            )
          : ListView.builder(
              padding: const EdgeInsets.all(16),
              itemCount: bookings.length,
              itemBuilder: (context, index) {
                final booking = bookings[index];
                return _buildBookingCard(context, booking);
              },
            ),
    );
  }

  Widget _buildBookingCard(BuildContext context, SecurityBooking booking) {
    final theme = Theme.of(context);
    final days = booking.endDate.difference(booking.startDate).inDays;

    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      clipBehavior: Clip.antiAlias,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Status banner
          Container(
            color: _getStatusColor(booking.status, theme),
            width: double.infinity,
            padding: const EdgeInsets.symmetric(vertical: 6, horizontal: 16),
            child: Text(
              _getStatusText(booking.status),
              style: theme.textTheme.labelLarge?.copyWith(
                color: theme.colorScheme.onPrimary,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),

          // Booking details
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Service name and booking ID
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        booking.securityService.name,
                        style: theme.textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    Text(
                      'ID: ${booking.id.substring(0, 8)}',
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: theme.colorScheme.onSurfaceVariant,
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 8),

                // Service type
                Text(
                  booking.securityService.serviceType.displayName,
                  style: theme.textTheme.titleMedium?.copyWith(
                    color: theme.colorScheme.onSurfaceVariant,
                  ),
                ),

                const SizedBox(height: 16),

                // Service period
                Row(
                  children: [
                    Icon(
                      Icons.calendar_today,
                      size: 16,
                      color: theme.colorScheme.primary,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        '${DateFormat('MMM d').format(booking.startDate)} - ${DateFormat('MMM d, yyyy').format(booking.endDate)} ($days ${days == 1 ? 'day' : 'days'})',
                        style: theme.textTheme.bodyMedium,
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 8),

                // Personnel count
                Row(
                  children: [
                    Icon(
                      Icons.person,
                      size: 16,
                      color: theme.colorScheme.primary,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        '${booking.personnelCount} ${booking.personnelCount == 1 ? 'person' : 'people'}',
                        style: theme.textTheme.bodyMedium,
                      ),
                    ),
                  ],
                ),

                if (booking.hasVehicle) ...[
                  const SizedBox(height: 8),

                  // Vehicle
                  Row(
                    children: [
                      Icon(
                        Icons.directions_car,
                        size: 16,
                        color: theme.colorScheme.primary,
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          booking.vehicleType ?? 'Vehicle included',
                          style: theme.textTheme.bodyMedium,
                        ),
                      ),
                    ],
                  ),
                ],

                const SizedBox(height: 16),

                // Price
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Total Price:',
                      style: theme.textTheme.titleMedium,
                    ),
                    Text(
                      '${booking.securityService.currency}${booking.totalPrice.toStringAsFixed(2)}',
                      style: theme.textTheme.titleLarge?.copyWith(
                        color: theme.colorScheme.primary,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 16),

                // Action buttons
                Row(
                  children: [
                    if (booking.status == BookingStatus.confirmed ||
                        booking.status == BookingStatus.pending)
                      Expanded(
                        child: OutlinedButton.icon(
                          onPressed: () {
                            // Show cancellation dialog
                            _showCancellationDialog(context);
                          },
                          icon: const Icon(Icons.cancel),
                          label: const Text('Cancel'),
                          style: OutlinedButton.styleFrom(
                            foregroundColor: theme.colorScheme.error,
                          ),
                        ),
                      ),
                    if (booking.status == BookingStatus.confirmed ||
                        booking.status == BookingStatus.pending)
                      const SizedBox(width: 16),
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: () {
                          // Show booking details
                          _showBookingDetails(context, booking);
                        },
                        icon: const Icon(Icons.visibility),
                        label: const Text('Details'),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Color _getStatusColor(BookingStatus status, ThemeData theme) {
    switch (status) {
      case BookingStatus.pending:
        return Colors.orange;
      case BookingStatus.confirmed:
        return theme.colorScheme.primary;
      case BookingStatus.cancelled:
        return theme.colorScheme.error;
      case BookingStatus.completed:
        return Colors.green;
      default:
        return theme.colorScheme.primary;
    }
  }

  String _getStatusText(BookingStatus status) {
    switch (status) {
      case BookingStatus.pending:
        return 'PENDING';
      case BookingStatus.confirmed:
        return 'CONFIRMED';
      case BookingStatus.cancelled:
        return 'CANCELLED';
      case BookingStatus.completed:
        return 'COMPLETED';
      default:
        return 'UNKNOWN';
    }
  }

  void _showCancellationDialog(BuildContext context) {
    try {
      showDialog(
        context: context,
        builder: (dialogContext) => AlertDialog(
          title: const Text('Cancel Booking'),
          content: const Text(
            'Are you sure you want to cancel this security service booking? Cancellation fees may apply based on the cancellation policy.',
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(dialogContext),
              child: const Text('No, Keep Booking'),
            ),
            TextButton(
              onPressed: () {
                try {
                  Navigator.pop(dialogContext);
                  if (context.mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('Booking cancelled successfully'),
                      ),
                    );
                  }
                } catch (e) {
                  // Log the error using LoggingService in a real app
                  debugPrint('Error cancelling booking: $e');
                }
              },
              style: TextButton.styleFrom(
                foregroundColor: Theme.of(dialogContext).colorScheme.error,
              ),
              child: const Text('Yes, Cancel'),
            ),
          ],
        ),
      );
    } catch (e) {
      // Log the error using LoggingService in a real app
      debugPrint('Error showing cancellation dialog: $e');
    }
  }

  void _showBookingDetails(BuildContext context, SecurityBooking booking) {
    try {
      // In a real app, this would navigate to a detailed view
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Booking details view not implemented yet'),
          ),
        );
      }
    } catch (e) {
      // Log the error using LoggingService in a real app
      debugPrint('Error showing booking details: $e');
    }
  }
}
