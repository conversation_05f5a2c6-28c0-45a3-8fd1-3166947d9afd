import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:culture_connect/models/travel/travel.dart';
import 'package:culture_connect/providers/travel/travel_services_provider.dart';
import 'package:culture_connect/screens/travel/private_security/private_security_details_screen.dart';
import 'package:culture_connect/widgets/common/loading_indicator.dart';
import 'package:culture_connect/widgets/common/error_view.dart';
import 'package:culture_connect/widgets/travel/travel_service_card.dart';

/// Screen for displaying a list of private security services
class PrivateSecurityListScreen extends ConsumerStatefulWidget {
  /// Creates a new private security list screen
  const PrivateSecurityListScreen({super.key});

  @override
  ConsumerState<PrivateSecurityListScreen> createState() => _PrivateSecurityListScreenState();
}

class _PrivateSecurityListScreenState extends ConsumerState<PrivateSecurityListScreen> {
  final ScrollController _scrollController = ScrollController();
  bool _isSearching = false;
  final TextEditingController _searchController = TextEditingController();
  
  @override
  void dispose() {
    _scrollController.dispose();
    _searchController.dispose();
    super.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Scaffold(
      appBar: AppBar(
        title: _isSearching
            ? TextField(
                controller: _searchController,
                decoration: InputDecoration(
                  hintText: 'Search security services...',
                  border: InputBorder.none,
                  hintStyle: TextStyle(color: theme.colorScheme.onSurface.withOpacity(0.6)),
                ),
                style: TextStyle(color: theme.colorScheme.onSurface),
                autofocus: true,
                onChanged: (value) {
                  // Implement search functionality
                },
              )
            : const Text('Private Security'),
        actions: [
          IconButton(
            icon: Icon(_isSearching ? Icons.close : Icons.search),
            onPressed: () {
              setState(() {
                _isSearching = !_isSearching;
                if (!_isSearching) {
                  _searchController.clear();
                }
              });
            },
          ),
        ],
      ),
      body: _buildPrivateSecurityList(),
    );
  }
  
  Widget _buildPrivateSecurityList() {
    final privateSecurityServicesAsyncValue = ref.watch(privateSecurityServicesProvider);
    
    return privateSecurityServicesAsyncValue.when(
      data: (privateSecurityServices) {
        if (privateSecurityServices.isEmpty) {
          return const Center(
            child: Text('No private security services available'),
          );
        }
        
        return RefreshIndicator(
          onRefresh: () async {
            // Refresh data
            ref.refresh(privateSecurityServicesProvider);
          },
          child: ListView.builder(
            controller: _scrollController,
            padding: EdgeInsets.all(16.r),
            itemCount: privateSecurityServices.length,
            itemBuilder: (context, index) {
              final privateSecurity = privateSecurityServices[index];
              return Padding(
                padding: EdgeInsets.only(bottom: 16.h),
                child: TravelServiceCard(
                  travelService: privateSecurity,
                  onTap: () => _navigateToPrivateSecurityDetails(privateSecurity),
                ),
              );
            },
          ),
        );
      },
      loading: () => const Center(child: LoadingIndicator()),
      error: (error, stackTrace) => Center(
        child: ErrorView(
          error: error.toString(),
          onRetry: () => ref.refresh(privateSecurityServicesProvider),
        ),
      ),
    );
  }
  
  void _navigateToPrivateSecurityDetails(PrivateSecurity privateSecurity) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => PrivateSecurityDetailsScreen(privateSecurity: privateSecurity),
      ),
    );
  }
}
