import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:culture_connect/models/travel/loyalty_program.dart';
import 'package:culture_connect/services/logging_service.dart';
import 'package:culture_connect/services/error_handling_service.dart';
import 'package:culture_connect/screens/loyalty/loyalty_dashboard_screen.dart';

/// Screen for displaying loyalty program details
class LoyaltyProgramScreen extends ConsumerStatefulWidget {
  /// The loyalty program to display
  final LoyaltyProgram loyaltyProgram;

  /// Creates a new loyalty program screen
  const LoyaltyProgramScreen({
    super.key,
    required this.loyaltyProgram,
  });

  @override
  ConsumerState<LoyaltyProgramScreen> createState() =>
      _LoyaltyProgramScreenState();
}

class _LoyaltyProgramScreenState extends ConsumerState<LoyaltyProgramScreen>
    with SingleTickerProviderStateMixin {
  final ScrollController _scrollController = ScrollController();
  late TabController _tabController;
  final LoggingService _loggingService = LoggingService();
  late ErrorHandlingService _errorHandlingService;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _errorHandlingService = ref.read(errorHandlingServiceProvider);
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(widget.loyaltyProgram.name),
      ),
      body: Column(
        children: [
          // Program summary
          Container(
            padding: const EdgeInsets.all(16),
            color: theme.colorScheme.primaryContainer,
            child: Row(
              children: [
                CircleAvatar(
                  backgroundImage:
                      NetworkImage(widget.loyaltyProgram.companyLogoUrl),
                  radius: 24,
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        widget.loyaltyProgram.name,
                        style: theme.textTheme.titleMedium,
                      ),
                      Text(
                        widget.loyaltyProgram.companyName,
                        style: theme.textTheme.bodyMedium,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),

          // Tabs
          TabBar(
            controller: _tabController,
            tabs: const [
              Tab(text: 'Overview'),
              Tab(text: 'Benefits'),
              Tab(text: 'Activity'),
            ],
            labelColor: theme.colorScheme.primary,
            unselectedLabelColor: theme.colorScheme.onSurface,
            indicatorColor: theme.colorScheme.primary,
          ),

          // Tab content
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildOverviewTab(),
                _buildBenefitsTab(),
                _buildActivityTab(),
              ],
            ),
          ),
        ],
      ),
      bottomNavigationBar: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: theme.colorScheme.surface,
          boxShadow: [
            BoxShadow(
              color: Colors.black.withAlpha(25),
              blurRadius: 4,
              offset: const Offset(0, -2),
            ),
          ],
        ),
        child: SafeArea(
          child: SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: _navigateToLoyaltyDashboard,
              style: ElevatedButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 16),
                backgroundColor: theme.colorScheme.primary,
                foregroundColor: theme.colorScheme.onPrimary,
              ),
              child: Text(
                'Go to Loyalty Dashboard',
                style: theme.textTheme.titleMedium?.copyWith(
                  color: theme.colorScheme.onPrimary,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildOverviewTab() {
    final theme = Theme.of(context);

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Points summary
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Points Summary',
                    style: theme.textTheme.titleMedium,
                  ),
                  const SizedBox(height: 16),
                  Row(
                    children: [
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Current Points',
                              style: theme.textTheme.bodySmall,
                            ),
                            Text(
                              '${widget.loyaltyProgram.currentPoints}',
                              style: theme.textTheme.titleLarge,
                            ),
                          ],
                        ),
                      ),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Current Tier',
                              style: theme.textTheme.bodySmall,
                            ),
                            Text(
                              widget.loyaltyProgram.currentTier.displayName,
                              style: theme.textTheme.titleMedium?.copyWith(
                                color: widget.loyaltyProgram.currentTier.color,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                  if (widget.loyaltyProgram.nextTier != null) ...[
                    const SizedBox(height: 16),
                    Text(
                      widget.loyaltyProgram.formattedProgressToNextTier,
                      style: theme.textTheme.bodySmall,
                    ),
                    const SizedBox(height: 4),
                    LinearProgressIndicator(
                      value: widget.loyaltyProgram.progressToNextTier,
                      backgroundColor:
                          theme.colorScheme.surfaceContainerHighest,
                      valueColor: AlwaysStoppedAnimation<Color>(
                        widget.loyaltyProgram.nextTier!.color,
                      ),
                    ),
                  ],
                  if (widget.loyaltyProgram.pointsExpiryDate != null) ...[
                    const SizedBox(height: 16),
                    Text(
                      widget.loyaltyProgram.formattedPointsExpiry ?? '',
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: theme.colorScheme.error,
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ),

          const SizedBox(height: 16),

          // Membership details
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Membership Details',
                    style: theme.textTheme.titleMedium,
                  ),
                  const SizedBox(height: 16),
                  _buildDetailRow('Membership Number',
                      widget.loyaltyProgram.membershipNumber),
                  const SizedBox(height: 8),
                  _buildDetailRow('Member Since',
                      widget.loyaltyProgram.formattedMembershipStartDate),
                  if (widget.loyaltyProgram.membershipExpiryDate != null) ...[
                    const SizedBox(height: 8),
                    _buildDetailRow(
                        'Expires On',
                        widget.loyaltyProgram.formattedMembershipExpiryDate ??
                            ''),
                  ],
                ],
              ),
            ),
          ),

          const SizedBox(height: 16),

          // Program description
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Program Description',
                    style: theme.textTheme.titleMedium,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    widget.loyaltyProgram.description,
                    style: theme.textTheme.bodyMedium,
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBenefitsTab() {
    final theme = Theme.of(context);

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: widget.loyaltyProgram.benefits.length,
      itemBuilder: (context, index) {
        final benefit = widget.loyaltyProgram.benefits[index];
        return Card(
          margin: const EdgeInsets.only(bottom: 16),
          child: ListTile(
            leading: Icon(
              benefit.icon,
              color: benefit.tier.color,
            ),
            title: Text(benefit.name),
            subtitle: Text(benefit.description),
            trailing: Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: benefit.tier.color.withAlpha(25),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                benefit.tier.displayName,
                style: theme.textTheme.bodySmall?.copyWith(
                  color: benefit.tier.color,
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildActivityTab() {
    final theme = Theme.of(context);

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: widget.loyaltyProgram.recentActivities.length,
      itemBuilder: (context, index) {
        final activity = widget.loyaltyProgram.recentActivities[index];
        return Card(
          margin: const EdgeInsets.only(bottom: 16),
          child: ListTile(
            leading: Icon(
              activity.type.icon,
              color: activity.type.color,
            ),
            title: Text(activity.description),
            subtitle: Text(activity.formattedDate),
            trailing: Text(
              activity.formattedPoints,
              style: theme.textTheme.titleMedium?.copyWith(
                color: activity.type.color,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildDetailRow(String label, String value) {
    final theme = Theme.of(context);
    return Row(
      children: [
        Expanded(
          flex: 2,
          child: Text(
            label,
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
            ),
          ),
        ),
        Expanded(
          flex: 3,
          child: Text(
            value,
            style: theme.textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
      ],
    );
  }

  void _navigateToLoyaltyDashboard() {
    try {
      _loggingService.info(
        'LoyaltyProgramScreen',
        'Navigating to loyalty dashboard',
      );

      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => const LoyaltyDashboardScreen(),
        ),
      );
    } catch (e, stackTrace) {
      _errorHandlingService.handleError(
        error: e,
        context: 'LoyaltyProgramScreen._navigateToLoyaltyDashboard',
        stackTrace: stackTrace,
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text(
                'Failed to navigate to loyalty dashboard. Please try again.'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    }
  }
}
