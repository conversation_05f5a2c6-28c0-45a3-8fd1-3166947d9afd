import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';
import 'package:culture_connect/models/travel/travel.dart';
import 'package:culture_connect/models/travel/restaurant_reservation.dart';
import 'package:culture_connect/providers/travel/restaurant_reservation_provider.dart';
import 'package:culture_connect/screens/travel/restaurant/restaurant_details_screen_enhanced.dart';
import 'package:culture_connect/screens/travel/restaurant/reservation_management_screen.dart';
import 'package:culture_connect/widgets/common/loading_indicator.dart';
import 'package:culture_connect/widgets/common/error_view.dart';
import 'package:lottie/lottie.dart';

/// Screen for displaying reservation confirmation
class ReservationConfirmationScreen extends ConsumerStatefulWidget {
  /// The confirmed reservation
  final RestaurantReservation reservation;
  
  /// The restaurant for the reservation
  final Restaurant restaurant;

  /// Creates a new reservation confirmation screen
  const ReservationConfirmationScreen({
    super.key,
    required this.reservation,
    required this.restaurant,
  });

  @override
  ConsumerState<ReservationConfirmationScreen> createState() => _ReservationConfirmationScreenState();
}

class _ReservationConfirmationScreenState extends ConsumerState<ReservationConfirmationScreen> with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  
  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 2),
    );
    
    // Play the animation once
    _animationController.forward();
  }
  
  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Scaffold(
      appBar: AppBar(
        title: const Text('Reservation Confirmed'),
        automaticallyImplyLeading: false,
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(16.r),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            // Success animation
            SizedBox(
              height: 200.h,
              child: Center(
                child: Lottie.network(
                  'https://assets10.lottiefiles.com/packages/lf20_s2lryxtd.json',
                  controller: _animationController,
                  repeat: false,
                ),
              ),
            ),
            
            SizedBox(height: 16.h),
            
            // Confirmation message
            Text(
              'Your reservation has been confirmed!',
              style: theme.textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: theme.colorScheme.primary,
              ),
              textAlign: TextAlign.center,
            ),
            
            SizedBox(height: 8.h),
            
            Text(
              'We\'ve sent a confirmation to ${widget.reservation.contactEmail}',
              style: theme.textTheme.bodyLarge,
              textAlign: TextAlign.center,
            ),
            
            SizedBox(height: 32.h),
            
            // Reservation details card
            Card(
              elevation: 2,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16.r),
              ),
              child: Padding(
                padding: EdgeInsets.all(16.r),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(
                          Icons.restaurant,
                          color: theme.colorScheme.primary,
                          size: 24.r,
                        ),
                        SizedBox(width: 8.w),
                        Expanded(
                          child: Text(
                            widget.restaurant.name,
                            style: theme.textTheme.titleLarge?.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ],
                    ),
                    
                    Divider(height: 32.h),
                    
                    // Reservation ID
                    _buildDetailRow(
                      context,
                      'Reservation ID',
                      widget.reservation.id.substring(0, 8).toUpperCase(),
                    ),
                    
                    SizedBox(height: 16.h),
                    
                    // Date and time
                    _buildDetailRow(
                      context,
                      'Date',
                      DateFormat('EEEE, MMMM d, yyyy').format(widget.reservation.date),
                    ),
                    
                    SizedBox(height: 16.h),
                    
                    _buildDetailRow(
                      context,
                      'Time',
                      widget.reservation.timeSlot.formatted,
                    ),
                    
                    SizedBox(height: 16.h),
                    
                    // Party size
                    _buildDetailRow(
                      context,
                      'Party Size',
                      '${widget.reservation.partySize} ${widget.reservation.partySize == 1 ? 'person' : 'people'}',
                    ),
                    
                    // Special requests (if any)
                    if (widget.reservation.specialRequests.isNotEmpty) ...[
                      SizedBox(height: 16.h),
                      _buildDetailRow(
                        context,
                        'Special Requests',
                        widget.reservation.specialRequests,
                      ),
                    ],
                    
                    Divider(height: 32.h),
                    
                    // Status
                    Row(
                      children: [
                        Container(
                          padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
                          decoration: BoxDecoration(
                            color: widget.reservation.status.color.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(16.r),
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(
                                widget.reservation.status.icon,
                                size: 16.r,
                                color: widget.reservation.status.color,
                              ),
                              SizedBox(width: 4.w),
                              Text(
                                widget.reservation.status.displayName,
                                style: theme.textTheme.bodyMedium?.copyWith(
                                  color: widget.reservation.status.color,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
            
            SizedBox(height: 32.h),
            
            // Action buttons
            Row(
              children: [
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: () {
                      Navigator.pushReplacement(
                        context,
                        MaterialPageRoute(
                          builder: (context) => RestaurantDetailsScreenEnhanced(
                            restaurant: widget.restaurant,
                          ),
                        ),
                      );
                    },
                    icon: const Icon(Icons.restaurant),
                    label: const Text('Restaurant Details'),
                    style: OutlinedButton.styleFrom(
                      padding: EdgeInsets.symmetric(vertical: 12.h),
                    ),
                  ),
                ),
                SizedBox(width: 16.w),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () {
                      Navigator.pushReplacement(
                        context,
                        MaterialPageRoute(
                          builder: (context) => const ReservationManagementScreen(),
                        ),
                      );
                    },
                    icon: const Icon(Icons.calendar_month),
                    label: const Text('My Reservations'),
                    style: ElevatedButton.styleFrom(
                      padding: EdgeInsets.symmetric(vertical: 12.h),
                      backgroundColor: theme.colorScheme.primary,
                      foregroundColor: theme.colorScheme.onPrimary,
                    ),
                  ),
                ),
              ],
            ),
            
            SizedBox(height: 16.h),
            
            // Home button
            SizedBox(
              width: double.infinity,
              child: TextButton.icon(
                onPressed: () {
                  // Navigate to home screen
                  Navigator.popUntil(context, (route) => route.isFirst);
                },
                icon: const Icon(Icons.home),
                label: const Text('Back to Home'),
              ),
            ),
          ],
        ),
      ),
    );
  }
  
  Widget _buildDetailRow(BuildContext context, String label, String value) {
    final theme = Theme.of(context);
    
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          width: 120.w,
          child: Text(
            label,
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
            ),
          ),
        ),
        Expanded(
          child: Text(
            value,
            style: theme.textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
      ],
    );
  }
}
