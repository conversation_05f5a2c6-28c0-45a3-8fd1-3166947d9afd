// Dart imports
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

// Package imports
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

// Project imports
import 'package:culture_connect/models/travel/restaurant_reservation.dart';
import 'package:culture_connect/models/travel/travel.dart';
import 'package:culture_connect/providers/auth_provider.dart';
import 'package:culture_connect/providers/travel/restaurant_reservation_provider.dart';
import 'package:culture_connect/screens/travel/restaurant/reservation_confirmation_screen.dart';
import 'package:culture_connect/services/logging_service.dart';
import 'package:culture_connect/widgets/common/error_view.dart';
import 'package:culture_connect/widgets/common/loading_indicator.dart';
import 'package:culture_connect/widgets/travel/restaurant/calendar_day_picker.dart';
import 'package:culture_connect/widgets/travel/restaurant/party_size_selector.dart';
import 'package:culture_connect/widgets/travel/restaurant/time_slot_selector.dart';

/// Screen for making a restaurant reservation
class RestaurantReservationScreen extends ConsumerStatefulWidget {
  /// The restaurant to make a reservation for
  final Restaurant restaurant;

  /// Creates a new restaurant reservation screen
  const RestaurantReservationScreen({
    super.key,
    required this.restaurant,
  });

  @override
  ConsumerState<RestaurantReservationScreen> createState() =>
      _RestaurantReservationScreenState();
}

class _RestaurantReservationScreenState
    extends ConsumerState<RestaurantReservationScreen> {
  final _formKey = GlobalKey<FormState>();
  final _specialRequestsController = TextEditingController();
  final _nameController = TextEditingController();
  final _phoneController = TextEditingController();
  final _emailController = TextEditingController();
  final _logger = LoggingService();

  int _currentStep = 0;
  bool _isLoading = false;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();

    // Set initial values for providers
    WidgetsBinding.instance.addPostFrameCallback((_) {
      try {
        _logger.info('RestaurantReservationScreen',
            'Initializing reservation screen for restaurant: ${widget.restaurant.id}');

        // Set selected date to today
        ref.read(selectedReservationDateProvider.notifier).state =
            DateTime.now();

        // Set selected party size to 2
        ref.read(selectedPartySizeProvider.notifier).state = 2;

        // Clear selected time slot
        ref.read(selectedTimeSlotProvider.notifier).state = null;

        // Clear special requests
        ref.read(specialRequestsProvider.notifier).state = '';

        // Pre-fill user information if available
        final user = ref.read(authStateProvider).user;
        if (user != null) {
          _nameController.text = user.fullName;
          _emailController.text = user.email;
          _phoneController.text = user.phoneNumber;

          _logger.info('RestaurantReservationScreen',
              'Pre-filled user information for ${user.id}');
        }
      } catch (e, stackTrace) {
        _logger.error('RestaurantReservationScreen',
            'Error initializing reservation screen', e, stackTrace);

        if (mounted) {
          setState(() {
            _errorMessage =
                'Failed to initialize reservation screen: ${e.toString()}';
          });
        }
      }
    });
  }

  @override
  void dispose() {
    _specialRequestsController.dispose();
    _nameController.dispose();
    _phoneController.dispose();
    _emailController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Make a Reservation'),
      ),
      body: _isLoading
          ? const Center(child: LoadingIndicator())
          : _errorMessage != null
              ? ErrorView(
                  error: _errorMessage!,
                  onRetry: () => setState(() => _errorMessage = null),
                )
              : Stepper(
                  currentStep: _currentStep,
                  onStepContinue: _handleContinue,
                  onStepCancel: _handleCancel,
                  controlsBuilder: (context, details) {
                    return Padding(
                      padding: EdgeInsets.only(top: 16.h),
                      child: Row(
                        children: [
                          Expanded(
                            child: ElevatedButton(
                              onPressed: details.onStepContinue,
                              style: ElevatedButton.styleFrom(
                                backgroundColor: theme.colorScheme.primary,
                                foregroundColor: theme.colorScheme.onPrimary,
                                padding: EdgeInsets.symmetric(vertical: 12.h),
                              ),
                              child: Text(
                                _currentStep == 2
                                    ? 'Complete Reservation'
                                    : 'Continue',
                                style: theme.textTheme.titleMedium?.copyWith(
                                  color: theme.colorScheme.onPrimary,
                                ),
                              ),
                            ),
                          ),
                          SizedBox(width: 16.w),
                          if (_currentStep > 0)
                            Expanded(
                              child: OutlinedButton(
                                onPressed: details.onStepCancel,
                                style: OutlinedButton.styleFrom(
                                  padding: EdgeInsets.symmetric(vertical: 12.h),
                                ),
                                child: Text(
                                  'Back',
                                  style: theme.textTheme.titleMedium,
                                ),
                              ),
                            ),
                        ],
                      ),
                    );
                  },
                  steps: [
                    _buildDateAndTimeStep(),
                    _buildPartyDetailsStep(),
                    _buildContactInfoStep(),
                  ],
                ),
    );
  }

  Step _buildDateAndTimeStep() {
    final selectedDate = ref.watch(selectedReservationDateProvider);
    final selectedTimeSlot = ref.watch(selectedTimeSlotProvider);

    return Step(
      title: const Text('Select Date & Time'),
      content: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Date picker
          Text(
            'Date',
            style: Theme.of(context).textTheme.titleMedium,
          ),
          SizedBox(height: 8.h),
          CalendarDayPicker(
            selectedDate: selectedDate,
            onDateSelected: (date) {
              ref.read(selectedReservationDateProvider.notifier).state = date;
              // Clear selected time slot when date changes
              ref.read(selectedTimeSlotProvider.notifier).state = null;
            },
            minDate: DateTime.now(),
            maxDate: DateTime.now().add(const Duration(days: 30)),
          ),
          SizedBox(height: 24.h),

          // Time slot picker
          Text(
            'Time',
            style: Theme.of(context).textTheme.titleMedium,
          ),
          SizedBox(height: 8.h),
          Consumer(
            builder: (context, ref, child) {
              final availableSlotsAsync = ref.watch(
                availableTimeSlotsProvider((
                  restaurantId: widget.restaurant.id,
                  date: selectedDate,
                )),
              );

              return availableSlotsAsync.when(
                data: (slots) {
                  if (slots.isEmpty) {
                    return const Center(
                      child: Padding(
                        padding: EdgeInsets.all(16.0),
                        child: Text('No available time slots for this date'),
                      ),
                    );
                  }

                  return TimeSlotSelector(
                    timeSlots: slots,
                    selectedTimeSlot: selectedTimeSlot,
                    onTimeSlotSelected: (slot) {
                      ref.read(selectedTimeSlotProvider.notifier).state = slot;
                    },
                  );
                },
                loading: () => const Center(child: LoadingIndicator()),
                error: (error, stackTrace) => ErrorView(
                  error: 'Failed to load time slots: $error',
                  onRetry: () {
                    _logger.warning(
                        'RestaurantReservationScreen',
                        'Failed to load time slots, retrying',
                        error,
                        stackTrace);
                    ref.invalidate(
                      availableTimeSlotsProvider((
                        restaurantId: widget.restaurant.id,
                        date: selectedDate,
                      )),
                    );
                  },
                ),
              );
            },
          ),
        ],
      ),
      isActive: _currentStep == 0,
      state: _currentStep > 0 ? StepState.complete : StepState.indexed,
    );
  }

  Step _buildPartyDetailsStep() {
    final selectedPartySize = ref.watch(selectedPartySizeProvider);

    return Step(
      title: const Text('Party Details'),
      content: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Party size selector
          Text(
            'Number of People',
            style: Theme.of(context).textTheme.titleMedium,
          ),
          SizedBox(height: 8.h),
          PartySizeSelector(
            partySize: selectedPartySize,
            onPartySizeChanged: (size) {
              ref.read(selectedPartySizeProvider.notifier).state = size;
            },
            minSize: 1,
            maxSize: 20,
          ),
          SizedBox(height: 24.h),

          // Special requests
          Text(
            'Special Requests (Optional)',
            style: Theme.of(context).textTheme.titleMedium,
          ),
          SizedBox(height: 8.h),
          TextFormField(
            controller: _specialRequestsController,
            decoration: const InputDecoration(
              hintText:
                  'E.g., Window seat, birthday celebration, dietary needs...',
              border: OutlineInputBorder(),
            ),
            maxLines: 3,
            onChanged: (value) {
              ref.read(specialRequestsProvider.notifier).state = value;
            },
          ),
        ],
      ),
      isActive: _currentStep == 1,
      state: _currentStep > 1 ? StepState.complete : StepState.indexed,
    );
  }

  Step _buildContactInfoStep() {
    return Step(
      title: const Text('Contact Information'),
      content: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Name
            TextFormField(
              controller: _nameController,
              decoration: const InputDecoration(
                labelText: 'Full Name',
                border: OutlineInputBorder(),
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please enter your name';
                }
                return null;
              },
            ),
            SizedBox(height: 16.h),

            // Phone
            TextFormField(
              controller: _phoneController,
              decoration: const InputDecoration(
                labelText: 'Phone Number',
                border: OutlineInputBorder(),
              ),
              keyboardType: TextInputType.phone,
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please enter your phone number';
                }
                return null;
              },
            ),
            SizedBox(height: 16.h),

            // Email
            TextFormField(
              controller: _emailController,
              decoration: const InputDecoration(
                labelText: 'Email',
                border: OutlineInputBorder(),
              ),
              keyboardType: TextInputType.emailAddress,
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please enter your email';
                }
                if (!value.contains('@') || !value.contains('.')) {
                  return 'Please enter a valid email';
                }
                return null;
              },
            ),
            SizedBox(height: 16.h),

            // Reservation summary
            _buildReservationSummary(),
          ],
        ),
      ),
      isActive: _currentStep == 2,
      state: _currentStep > 2 ? StepState.complete : StepState.indexed,
    );
  }

  Widget _buildReservationSummary() {
    final theme = Theme.of(context);
    final selectedDate = ref.watch(selectedReservationDateProvider);
    final selectedTimeSlot = ref.watch(selectedTimeSlotProvider);
    final selectedPartySize = ref.watch(selectedPartySizeProvider);
    final specialRequests = ref.watch(specialRequestsProvider);

    return Card(
      margin: EdgeInsets.symmetric(vertical: 16.h),
      child: Padding(
        padding: EdgeInsets.all(16.r),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Reservation Summary',
              style: theme.textTheme.titleLarge,
            ),
            SizedBox(height: 16.h),
            _buildSummaryRow('Restaurant', widget.restaurant.name),
            _buildSummaryRow(
                'Date', DateFormat('EEEE, MMMM d, yyyy').format(selectedDate)),
            if (selectedTimeSlot != null)
              _buildSummaryRow('Time', selectedTimeSlot.formatted),
            _buildSummaryRow('Party Size',
                '$selectedPartySize ${selectedPartySize == 1 ? 'person' : 'people'}'),
            if (specialRequests.isNotEmpty)
              _buildSummaryRow('Special Requests', specialRequests),
          ],
        ),
      ),
    );
  }

  Widget _buildSummaryRow(String label, String value) {
    final theme = Theme.of(context);

    return Padding(
      padding: EdgeInsets.only(bottom: 8.h),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120.w,
            child: Text(
              '$label:',
              style: theme.textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: theme.textTheme.bodyMedium,
            ),
          ),
        ],
      ),
    );
  }

  void _handleContinue() {
    try {
      _logger.info('RestaurantReservationScreen',
          'Handling continue button at step $_currentStep');

      // Validate current step
      if (_currentStep == 0) {
        // Validate date and time
        final selectedTimeSlot = ref.read(selectedTimeSlotProvider);
        if (selectedTimeSlot == null) {
          _logger.warning(
              'RestaurantReservationScreen', 'Time slot not selected');

          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Please select a time slot')),
          );
          return;
        }
      } else if (_currentStep == 2) {
        // Validate contact info and submit reservation
        if (_formKey.currentState?.validate() ?? false) {
          _submitReservation();
          return;
        } else {
          _logger.warning(
              'RestaurantReservationScreen', 'Contact form validation failed');
          return;
        }
      }

      // Move to next step
      setState(() {
        _currentStep += 1;
      });

      _logger.info(
          'RestaurantReservationScreen', 'Advanced to step $_currentStep');
    } catch (e, stackTrace) {
      _logger.error('RestaurantReservationScreen',
          'Error handling continue button', e, stackTrace);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('An error occurred: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _handleCancel() {
    try {
      _logger.info('RestaurantReservationScreen',
          'Handling cancel button at step $_currentStep');

      if (_currentStep > 0) {
        setState(() {
          _currentStep -= 1;
        });

        _logger.info(
            'RestaurantReservationScreen', 'Returned to step $_currentStep');
      } else {
        _logger.info('RestaurantReservationScreen',
            'Already at first step, cannot go back');
      }
    } catch (e, stackTrace) {
      _logger.error('RestaurantReservationScreen',
          'Error handling cancel button', e, stackTrace);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('An error occurred: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _submitReservation() async {
    _logger.info('RestaurantReservationScreen',
        'Submitting reservation for restaurant: ${widget.restaurant.id}');

    final selectedDate = ref.read(selectedReservationDateProvider);
    final selectedTimeSlot = ref.read(selectedTimeSlotProvider);
    final selectedPartySize = ref.read(selectedPartySizeProvider);
    final specialRequests = ref.read(specialRequestsProvider);
    final user = ref.read(authStateProvider).user;

    if (selectedTimeSlot == null || user == null) {
      final errorMsg = 'Missing required information for reservation: '
          '${selectedTimeSlot == null ? 'time slot not selected' : ''}'
          '${user == null ? 'user not logged in' : ''}';

      _logger.warning('RestaurantReservationScreen', errorMsg);

      if (mounted) {
        setState(() {
          _errorMessage = 'Missing required information for reservation';
        });
      }
      return;
    }

    if (mounted) {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
      });
    }

    try {
      _logger.info('RestaurantReservationScreen',
          'Creating reservation for ${user.id} at ${widget.restaurant.name} on ${selectedDate.toIso8601String()} at ${selectedTimeSlot.formatted}');

      await ref
          .read(restaurantReservationNotifierProvider.notifier)
          .createReservation(
            restaurantId: widget.restaurant.id,
            restaurantName: widget.restaurant.name,
            date: selectedDate,
            timeSlot: selectedTimeSlot,
            partySize: selectedPartySize,
            specialRequests: specialRequests,
            userId: user.id,
            userName: _nameController.text,
            contactPhone: _phoneController.text,
            contactEmail: _emailController.text,
          );

      final reservation = ref.read(restaurantReservationNotifierProvider);

      if (!mounted) {
        _logger.warning('RestaurantReservationScreen',
            'Widget unmounted after creating reservation');
        return;
      }

      if (reservation.hasValue && reservation.value != null) {
        _logger.info('RestaurantReservationScreen',
            'Reservation created successfully with ID: ${reservation.value!.id}');

        // Navigate to confirmation screen
        Navigator.pushReplacement(
          context,
          MaterialPageRoute(
            builder: (context) => ReservationConfirmationScreen(
              reservation: reservation.value!,
              restaurant: widget.restaurant,
            ),
          ),
        );
      } else {
        _logger.error(
            'RestaurantReservationScreen',
            'Failed to create reservation - provider returned null or error state',
            reservation.error);

        setState(() {
          _isLoading = false;
          _errorMessage = 'Failed to create reservation';
        });
      }
    } catch (e, stackTrace) {
      _logger.error('RestaurantReservationScreen', 'Error creating reservation',
          e, stackTrace);

      if (mounted) {
        setState(() {
          _isLoading = false;
          _errorMessage = 'Error: ${e.toString()}';
        });
      }
    }
  }
}
