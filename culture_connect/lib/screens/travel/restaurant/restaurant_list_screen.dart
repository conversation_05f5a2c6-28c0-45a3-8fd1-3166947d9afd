import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:culture_connect/models/travel/travel.dart';
import 'package:culture_connect/providers/travel/travel_services_provider.dart'
    as travel_services;
import 'package:culture_connect/providers/travel/restaurant_provider.dart'
    as restaurant_provider;
import 'package:culture_connect/screens/travel/restaurant/restaurant_details_screen_enhanced.dart';
import 'package:culture_connect/widgets/common/loading_indicator.dart';
import 'package:culture_connect/widgets/common/error_view.dart';
import 'package:culture_connect/widgets/travel/travel_service_card.dart';

/// Screen for displaying a list of restaurants
class RestaurantListScreen extends ConsumerStatefulWidget {
  /// Creates a new restaurant list screen
  const RestaurantListScreen({super.key});

  @override
  ConsumerState<RestaurantListScreen> createState() =>
      _RestaurantListScreenState();
}

class _RestaurantListScreenState extends ConsumerState<RestaurantListScreen>
    with SingleTickerProviderStateMixin {
  final ScrollController _scrollController = ScrollController();
  bool _isSearching = false;
  bool _isGridView = false;
  bool _isFilterVisible = false;
  final TextEditingController _searchController = TextEditingController();

  // Filter state
  CuisineType? _selectedCuisineType;
  double _minPrice = 0;
  double _maxPrice = 500;
  double _minRating = 0;

  // Animation controller for filter panel
  late AnimationController _filterAnimationController;
  late Animation<double> _filterAnimation;

  @override
  void initState() {
    super.initState();
    _filterAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );
    _filterAnimation = CurvedAnimation(
      parent: _filterAnimationController,
      curve: Curves.easeInOut,
    );
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _searchController.dispose();
    _filterAnimationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: _isSearching
            ? TextField(
                controller: _searchController,
                decoration: InputDecoration(
                  hintText: 'Search restaurants...',
                  border: InputBorder.none,
                  hintStyle: TextStyle(
                      color: theme.colorScheme.onSurface.withAlpha(153)),
                ),
                style: TextStyle(color: theme.colorScheme.onSurface),
                autofocus: true,
                onChanged: (value) {
                  // Implement search functionality
                  setState(() {});
                },
              )
            : const Text('Restaurants'),
        actions: [
          // Toggle grid/list view
          IconButton(
            icon: Icon(_isGridView ? Icons.view_list : Icons.grid_view),
            tooltip: _isGridView ? 'List View' : 'Grid View',
            onPressed: () {
              setState(() {
                _isGridView = !_isGridView;
              });
            },
          ),
          // Filter button
          IconButton(
            icon: const Icon(Icons.filter_list),
            tooltip: 'Filter',
            onPressed: () {
              setState(() {
                _isFilterVisible = !_isFilterVisible;
                if (_isFilterVisible) {
                  _filterAnimationController.forward();
                } else {
                  _filterAnimationController.reverse();
                }
              });
            },
          ),
          // Search button
          IconButton(
            icon: Icon(_isSearching ? Icons.close : Icons.search),
            tooltip: _isSearching ? 'Cancel' : 'Search',
            onPressed: () {
              setState(() {
                _isSearching = !_isSearching;
                if (!_isSearching) {
                  _searchController.clear();
                }
              });
            },
          ),
        ],
      ),
      body: Column(
        children: [
          // Filter panel
          SizeTransition(
            sizeFactor: _filterAnimation,
            child: _buildFilterPanel(),
          ),
          // Restaurant list
          Expanded(
            child: _buildRestaurantList(),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterPanel() {
    final theme = Theme.of(context);

    return Container(
      color: theme.colorScheme.surfaceContainerLow,
      padding: EdgeInsets.all(16.r),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Cuisine type filter
          Text(
            'Cuisine Type',
            style: theme.textTheme.titleMedium,
          ),
          SizedBox(height: 8.h),
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              children: [
                _buildCuisineFilterChip(null, 'All'),
                _buildCuisineFilterChip(CuisineType.italian, 'Italian'),
                _buildCuisineFilterChip(CuisineType.japanese, 'Japanese'),
                _buildCuisineFilterChip(CuisineType.chinese, 'Chinese'),
                _buildCuisineFilterChip(CuisineType.indian, 'Indian'),
                _buildCuisineFilterChip(CuisineType.mexican, 'Mexican'),
                _buildCuisineFilterChip(CuisineType.african, 'African'),
              ],
            ),
          ),

          SizedBox(height: 16.h),

          // Price range filter
          Row(
            children: [
              Text(
                'Price Range',
                style: theme.textTheme.titleMedium,
              ),
              const Spacer(),
              Text(
                '\$${_minPrice.toInt()} - \$${_maxPrice.toInt()}',
                style: theme.textTheme.bodyMedium,
              ),
            ],
          ),
          SizedBox(height: 8.h),
          RangeSlider(
            values: RangeValues(_minPrice, _maxPrice),
            min: 0,
            max: 500,
            divisions: 10,
            labels: RangeLabels(
              '\$${_minPrice.toInt()}',
              '\$${_maxPrice.toInt()}',
            ),
            onChanged: (values) {
              setState(() {
                _minPrice = values.start;
                _maxPrice = values.end;
              });
            },
          ),

          SizedBox(height: 16.h),

          // Rating filter
          Row(
            children: [
              Text(
                'Minimum Rating',
                style: theme.textTheme.titleMedium,
              ),
              const Spacer(),
              Text(
                '${_minRating.toInt()} ★',
                style: theme.textTheme.bodyMedium,
              ),
            ],
          ),
          SizedBox(height: 8.h),
          Slider(
            value: _minRating,
            min: 0,
            max: 5,
            divisions: 5,
            label: '${_minRating.toInt()} ★',
            onChanged: (value) {
              setState(() {
                _minRating = value;
              });
            },
          ),

          SizedBox(height: 16.h),

          // Apply and reset buttons
          Row(
            children: [
              Expanded(
                child: OutlinedButton(
                  onPressed: _resetFilters,
                  child: const Text('Reset'),
                ),
              ),
              SizedBox(width: 16.w),
              Expanded(
                child: ElevatedButton(
                  onPressed: () {
                    setState(() {
                      _isFilterVisible = false;
                      _filterAnimationController.reverse();
                    });
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: theme.colorScheme.primary,
                    foregroundColor: theme.colorScheme.onPrimary,
                  ),
                  child: const Text('Apply'),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildCuisineFilterChip(CuisineType? cuisineType, String label) {
    return Padding(
      padding: EdgeInsets.only(right: 8.w),
      child: FilterChip(
        label: Text(label),
        selected: _selectedCuisineType == cuisineType,
        onSelected: (selected) {
          setState(() {
            _selectedCuisineType = selected ? cuisineType : null;
          });
        },
      ),
    );
  }

  void _resetFilters() {
    setState(() {
      _selectedCuisineType = null;
      _minPrice = 0;
      _maxPrice = 500;
      _minRating = 0;
    });
  }

  Widget _buildRestaurantList() {
    // Use the travel services provider for now, but we could switch to our custom provider
    final restaurantsAsyncValue =
        ref.watch(travel_services.restaurantsProvider);

    return restaurantsAsyncValue.when(
      data: (restaurants) {
        if (restaurants.isEmpty) {
          return const Center(
            child: Text('No restaurants available'),
          );
        }

        // For demo purposes, also add our sample restaurants
        final sampleRestaurants =
            ref.watch(restaurant_provider.restaurantsProvider);
        final allRestaurants = [...restaurants, ...sampleRestaurants];

        // Apply filters
        final filteredRestaurants = allRestaurants.where((restaurant) {
          // Filter by search query
          if (_searchController.text.isNotEmpty) {
            final query = _searchController.text.toLowerCase();
            if (!restaurant.name.toLowerCase().contains(query) &&
                !restaurant.description.toLowerCase().contains(query)) {
              return false;
            }
          }

          // Filter by cuisine type
          if (_selectedCuisineType != null) {
            if (!restaurant.cuisineTypes.contains(_selectedCuisineType)) {
              return false;
            }
          }

          // Filter by price
          if (restaurant.price < _minPrice || restaurant.price > _maxPrice) {
            return false;
          }

          // Filter by rating
          if (restaurant.rating < _minRating) {
            return false;
          }

          return true;
        }).toList();

        if (filteredRestaurants.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.restaurant_outlined,
                  size: 64.r,
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                ),
                SizedBox(height: 16.h),
                const Text('No restaurants match your filters'),
                SizedBox(height: 16.h),
                ElevatedButton(
                  onPressed: _resetFilters,
                  child: const Text('Reset Filters'),
                ),
              ],
            ),
          );
        }

        return RefreshIndicator(
          onRefresh: () async {
            // Refresh data
            await Future.wait([
              ref.refresh(travel_services.restaurantsProvider.future),
            ]);
          },
          child: _isGridView
              ? GridView.builder(
                  controller: _scrollController,
                  padding: EdgeInsets.all(16.r),
                  gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: 2,
                    childAspectRatio: 0.75,
                    crossAxisSpacing: 16.w,
                    mainAxisSpacing: 16.h,
                  ),
                  itemCount: filteredRestaurants.length,
                  itemBuilder: (context, index) {
                    final restaurant = filteredRestaurants[index];
                    return _buildGridItem(restaurant);
                  },
                )
              : ListView.builder(
                  controller: _scrollController,
                  padding: EdgeInsets.all(16.r),
                  itemCount: filteredRestaurants.length,
                  itemBuilder: (context, index) {
                    final restaurant = filteredRestaurants[index];
                    return Padding(
                      padding: EdgeInsets.only(bottom: 16.h),
                      child: TravelServiceCard(
                        travelService: restaurant,
                        onTap: () => _navigateToRestaurantDetails(restaurant),
                      ),
                    );
                  },
                ),
        );
      },
      loading: () => const Center(child: LoadingIndicator()),
      error: (error, stackTrace) => Center(
        child: ErrorView(
          error: error.toString(),
          onRetry: () => ref.refresh(travel_services.restaurantsProvider),
        ),
      ),
    );
  }

  Widget _buildGridItem(Restaurant restaurant) {
    final theme = Theme.of(context);

    return GestureDetector(
      onTap: () => _navigateToRestaurantDetails(restaurant),
      child: Card(
        clipBehavior: Clip.antiAlias,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Restaurant image
            AspectRatio(
              aspectRatio: 1.5,
              child: Hero(
                tag: 'restaurant_image_${restaurant.id}',
                child: Image.network(
                  restaurant.imageUrl,
                  fit: BoxFit.cover,
                  errorBuilder: (context, error, stackTrace) {
                    return Container(
                      color: theme.colorScheme.surfaceContainerHighest,
                      child: Center(
                        child: Icon(
                          Icons.image_not_supported,
                          color: theme.colorScheme.onSurfaceVariant,
                        ),
                      ),
                    );
                  },
                ),
              ),
            ),

            // Restaurant details
            Padding(
              padding: EdgeInsets.all(8.r),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Name
                  Text(
                    restaurant.name,
                    style: theme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  SizedBox(height: 4.h),

                  // Cuisine types
                  Text(
                    restaurant.formattedCuisineTypes,
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: theme.colorScheme.onSurfaceVariant,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  SizedBox(height: 4.h),

                  // Rating
                  Row(
                    children: [
                      Icon(
                        Icons.star,
                        size: 16.r,
                        color: Colors.amber,
                      ),
                      SizedBox(width: 4.w),
                      Text(
                        restaurant.rating.toString(),
                        style: theme.textTheme.bodyMedium,
                      ),
                      SizedBox(width: 8.w),
                      Text(
                        '(${restaurant.reviewCount})',
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: theme.colorScheme.onSurfaceVariant,
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 4.h),

                  // Price
                  Text(
                    restaurant.formattedPrice,
                    style: theme.textTheme.titleSmall?.copyWith(
                      color: theme.colorScheme.primary,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _navigateToRestaurantDetails(Restaurant restaurant) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) =>
            RestaurantDetailsScreenEnhanced(restaurant: restaurant),
      ),
    );
  }
}
