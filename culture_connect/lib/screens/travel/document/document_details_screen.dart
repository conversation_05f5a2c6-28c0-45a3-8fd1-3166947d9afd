import 'package:flutter/material.dart';
import 'package:culture_connect/models/travel/document/travel_documents.dart';
import 'package:culture_connect/providers/travel/document/document_providers.dart';
import 'package:culture_connect/screens/travel/document/document_upload_screen.dart';
import 'package:culture_connect/theme/app_colors.dart';
import 'package:culture_connect/theme/app_text_styles.dart';
import 'package:culture_connect/widgets/common/error_state.dart';
import 'package:culture_connect/widgets/common/image_viewer.dart';
import 'package:provider/provider.dart';
import 'package:share_plus/share_plus.dart';

/// A screen for displaying document details
class DocumentDetailsScreen extends StatefulWidget {
  /// The ID of the document to display
  final String documentId;
  
  /// Creates a new document details screen
  const DocumentDetailsScreen({
    Key? key,
    required this.documentId,
  }) : super(key: key);

  @override
  State<DocumentDetailsScreen> createState() => _DocumentDetailsScreenState();
}

class _DocumentDetailsScreenState extends State<DocumentDetailsScreen> {
  TravelDocument? _document;
  bool _isLoading = true;
  String? _error;
  
  @override
  void initState() {
    super.initState();
    _loadDocument();
  }
  
  /// Load the document
  Future<void> _loadDocument() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });
    
    try {
      final documentProvider = Provider.of<TravelDocumentProvider>(context, listen: false);
      final document = await documentProvider.getDocument(widget.documentId);
      
      setState(() {
        _document = document;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = 'Failed to load document: $e';
        _isLoading = false;
      });
    }
  }
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_document?.name ?? 'Document Details'),
        actions: [
          if (_document != null) ...[
            IconButton(
              icon: const Icon(Icons.edit),
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => DocumentUploadScreen(
                      documentType: _document!.type,
                      document: _document,
                    ),
                  ),
                ).then((_) => _loadDocument());
              },
              tooltip: 'Edit',
            ),
            IconButton(
              icon: const Icon(Icons.share),
              onPressed: () {
                _shareDocument();
              },
              tooltip: 'Share',
            ),
          ],
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _error != null
              ? ErrorState(
                  message: _error!,
                  onRetry: _loadDocument,
                )
              : _document != null
                  ? _buildDocumentDetails()
                  : const Center(
                      child: Text('Document not found'),
                    ),
    );
  }
  
  /// Build the document details
  Widget _buildDocumentDetails() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildStatusCard(),
          const SizedBox(height: 24),
          _buildDocumentImages(),
          const SizedBox(height: 24),
          _buildDocumentInfo(),
          const SizedBox(height: 24),
          if (_document is Passport)
            _buildPassportInfo(_document as Passport)
          else if (_document is Visa)
            _buildVisaInfo(_document as Visa),
          const SizedBox(height: 24),
          _buildNotes(),
        ],
      ),
    );
  }
  
  /// Build the status card
  Widget _buildStatusCard() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: _document!.calculatedStatus.color.withOpacity(0.2),
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    _document!.calculatedStatus.icon,
                    color: _document!.calculatedStatus.color,
                    size: 32,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        _document!.calculatedStatus.displayName,
                        style: AppTextStyles.headline6.copyWith(
                          color: _document!.calculatedStatus.color,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        _getStatusMessage(),
                        style: AppTextStyles.body2,
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            LinearProgressIndicator(
              value: _getExpiryProgress(),
              backgroundColor: AppColors.border,
              valueColor: AlwaysStoppedAnimation<Color>(_getExpiryProgressColor()),
            ),
            const SizedBox(height: 8),
            Text(
              _getExpiryText(),
              style: AppTextStyles.caption.copyWith(
                color: _getExpiryProgressColor(),
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
  
  /// Build the document images
  Widget _buildDocumentImages() {
    if (_document!.documentImageUrls.isEmpty) {
      return const SizedBox();
    }
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Document Images',
          style: AppTextStyles.headline6,
        ),
        const SizedBox(height: 16),
        SizedBox(
          height: 200,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            itemCount: _document!.documentImageUrls.length,
            itemBuilder: (context, index) {
              return GestureDetector(
                onTap: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => ImageViewer(
                        imageUrls: _document!.documentImageUrls,
                        initialIndex: index,
                      ),
                    ),
                  );
                },
                child: Container(
                  width: 150,
                  margin: const EdgeInsets.only(right: 16),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8),
                    image: DecorationImage(
                      image: NetworkImage(_document!.documentImageUrls[index]),
                      fit: BoxFit.cover,
                    ),
                  ),
                ),
              );
            },
          ),
        ),
      ],
    );
  }
  
  /// Build the document info
  Widget _buildDocumentInfo() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Document Information',
          style: AppTextStyles.headline6,
        ),
        const SizedBox(height: 16),
        _buildInfoRow('Document Type', _document!.type.displayName),
        _buildInfoRow('Document Number', _document!.documentNumber),
        _buildInfoRow('Issued By', _document!.issuedBy),
        _buildInfoRow('Issue Date', _document!.formattedIssuedDate),
        _buildInfoRow('Expiry Date', _document!.formattedExpiryDate),
      ],
    );
  }
  
  /// Build passport-specific info
  Widget _buildPassportInfo(Passport passport) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Passport Information',
          style: AppTextStyles.headline6,
        ),
        const SizedBox(height: 16),
        _buildInfoRow('Nationality', passport.nationality),
        _buildInfoRow('Country Code', passport.countryCode),
        _buildInfoRow('Place of Birth', passport.placeOfBirth),
        _buildInfoRow('Date of Birth', passport.formattedDateOfBirth),
        _buildInfoRow('Gender', _getGenderText(passport.gender)),
        _buildInfoRow('Age', passport.age.toString()),
        if (passport.mrzLine1 != null)
          _buildInfoRow('MRZ Line 1', passport.mrzLine1!),
        if (passport.mrzLine2 != null)
          _buildInfoRow('MRZ Line 2', passport.mrzLine2!),
      ],
    );
  }
  
  /// Build visa-specific info
  Widget _buildVisaInfo(Visa visa) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Visa Information',
          style: AppTextStyles.headline6,
        ),
        const SizedBox(height: 16),
        _buildInfoRow('Visa Type', visa.visaType.displayName),
        _buildInfoRow('Entry Type', visa.entryType.displayName),
        _buildInfoRow('Country of Issue', visa.countryOfIssue),
        _buildInfoRow('Country Valid For', visa.countryValidFor),
        _buildInfoRow('Maximum Stay', visa.formattedMaxStayDuration),
        if (visa.numberOfEntries != null)
          _buildInfoRow('Number of Entries', visa.numberOfEntries.toString()),
        if (visa.processingTime != null)
          _buildInfoRow('Processing Time', '${visa.processingTime} days'),
        if (visa.applicationReference != null)
          _buildInfoRow('Application Reference', visa.applicationReference!),
      ],
    );
  }
  
  /// Build the notes section
  Widget _buildNotes() {
    if (_document!.notes == null || _document!.notes!.isEmpty) {
      return const SizedBox();
    }
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Notes',
          style: AppTextStyles.headline6,
        ),
        const SizedBox(height: 16),
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: AppColors.surface,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: AppColors.border),
          ),
          child: Text(
            _document!.notes!,
            style: AppTextStyles.body2,
          ),
        ),
      ],
    );
  }
  
  /// Build an info row
  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: AppTextStyles.body2.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: AppTextStyles.body2.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }
  
  /// Get the status message
  String _getStatusMessage() {
    if (_document!.isExpired) {
      return 'This document has expired and is no longer valid.';
    } else if (_document!.isExpiringSoon) {
      return 'This document will expire soon. Consider renewing it.';
    } else {
      return 'This document is valid and up to date.';
    }
  }
  
  /// Get the expiry progress (0.0 to 1.0)
  double _getExpiryProgress() {
    if (_document!.isExpired) {
      return 0.0;
    }
    
    final totalDays = _document!.expiryDate.difference(_document!.issuedDate).inDays;
    final remainingDays = _document!.expiryDate.difference(DateTime.now()).inDays;
    
    return remainingDays / totalDays;
  }
  
  /// Get the color for the expiry progress
  Color _getExpiryProgressColor() {
    if (_document!.isExpired) {
      return AppColors.error;
    } else if (_document!.isExpiringSoon) {
      return AppColors.warning;
    } else {
      return AppColors.success;
    }
  }
  
  /// Get the expiry text
  String _getExpiryText() {
    if (_document!.isExpired) {
      return 'Expired';
    } else {
      return 'Expires in ${_document!.daysUntilExpiry} days';
    }
  }
  
  /// Get the gender text
  String _getGenderText(String gender) {
    switch (gender) {
      case 'M':
        return 'Male';
      case 'F':
        return 'Female';
      case 'X':
        return 'Other';
      default:
        return gender;
    }
  }
  
  /// Share the document
  void _shareDocument() {
    final text = 'Document: ${_document!.name}\n'
        'Type: ${_document!.type.displayName}\n'
        'Number: ${_document!.documentNumber}\n'
        'Issued By: ${_document!.issuedBy}\n'
        'Issue Date: ${_document!.formattedIssuedDate}\n'
        'Expiry Date: ${_document!.formattedExpiryDate}\n';
    
    Share.share(text, subject: _document!.name);
  }
}
