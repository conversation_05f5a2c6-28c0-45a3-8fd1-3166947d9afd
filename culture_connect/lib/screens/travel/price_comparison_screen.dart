import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:culture_connect/models/travel/travel.dart';
import 'package:culture_connect/providers/price_comparison_provider.dart';
import 'package:culture_connect/theme/app_theme.dart';
import 'package:culture_connect/widgets/custom_app_bar.dart';
import 'package:culture_connect/widgets/travel/price_comparison_list.dart';
import 'package:culture_connect/widgets/travel/price_history_chart.dart';

/// A screen that displays price comparisons for a travel service
class PriceComparisonScreen extends ConsumerStatefulWidget {
  /// The travel service to compare prices for
  final TravelService travelService;
  
  /// Creates a new price comparison screen
  const PriceComparisonScreen({
    super.key,
    required this.travelService,
  });
  
  @override
  ConsumerState<PriceComparisonScreen> createState() => _PriceComparisonScreenState();
}

class _PriceComparisonScreenState extends ConsumerState<PriceComparisonScreen> with SingleTickerProviderStateMixin {
  late TabController _tabController;
  
  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
  }
  
  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final serviceType = _getServiceType(widget.travelService);
    
    return Scaffold(
      appBar: CustomAppBar(
        title: 'Price Comparison',
        showBackButton: true,
      ),
      body: Column(
        children: [
          // Service info
          Padding(
            padding: EdgeInsets.all(16.r),
            child: Row(
              children: [
                // Service image
                Container(
                  width: 80.r,
                  height: 80.r,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8.r),
                    image: DecorationImage(
                      image: NetworkImage(widget.travelService.imageUrl),
                      fit: BoxFit.cover,
                    ),
                  ),
                ),
                SizedBox(width: 16.w),
                // Service details
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        widget.travelService.name,
                        style: TextStyle(
                          fontSize: 18.sp,
                          fontWeight: FontWeight.bold,
                          color: AppTheme.textPrimaryColor,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                      SizedBox(height: 4.h),
                      Row(
                        children: [
                          Icon(
                            widget.travelService.icon,
                            size: 16.r,
                            color: widget.travelService.color,
                          ),
                          SizedBox(width: 4.w),
                          Text(
                            _getServiceTypeName(widget.travelService),
                            style: TextStyle(
                              fontSize: 14.sp,
                              color: AppTheme.textSecondaryColor,
                            ),
                          ),
                        ],
                      ),
                      SizedBox(height: 4.h),
                      Row(
                        children: [
                          Icon(
                            Icons.location_on,
                            size: 16.r,
                            color: AppTheme.textSecondaryColor,
                          ),
                          SizedBox(width: 4.w),
                          Expanded(
                            child: Text(
                              widget.travelService.location,
                              style: TextStyle(
                                fontSize: 14.sp,
                                color: AppTheme.textSecondaryColor,
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          
          // Tab bar
          TabBar(
            controller: _tabController,
            tabs: const [
              Tab(text: 'Price Comparison'),
              Tab(text: 'Price History'),
            ],
            labelColor: AppTheme.primaryColor,
            unselectedLabelColor: AppTheme.textSecondaryColor,
            indicatorColor: AppTheme.primaryColor,
          ),
          
          // Tab content
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                // Price comparison tab
                SingleChildScrollView(
                  child: Padding(
                    padding: EdgeInsets.symmetric(vertical: 16.h),
                    child: PriceComparisonList(
                      serviceType: serviceType,
                      serviceId: widget.travelService.id,
                      serviceName: widget.travelService.name,
                      showPriceBreakdown: true,
                      showBookingButton: true,
                      showRefreshButton: true,
                    ),
                  ),
                ),
                
                // Price history tab
                SingleChildScrollView(
                  child: Padding(
                    padding: EdgeInsets.symmetric(vertical: 16.h),
                    child: PriceHistoryChart(
                      serviceType: serviceType,
                      serviceId: widget.travelService.id,
                      serviceName: widget.travelService.name,
                      showForecast: true,
                      showTrend: true,
                      showRefreshButton: true,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
  
  String _getServiceType(TravelService travelService) {
    if (travelService is CarRental) {
      return 'car';
    } else if (travelService is PrivateSecurity) {
      return 'security';
    } else if (travelService is Hotel) {
      return 'hotel';
    } else if (travelService is Restaurant) {
      return 'restaurant';
    } else if (travelService is Flight) {
      return 'flight';
    } else if (travelService is Cruise) {
      return 'cruise';
    } else {
      return 'unknown';
    }
  }
  
  String _getServiceTypeName(TravelService travelService) {
    if (travelService is CarRental) {
      return 'Car Rental';
    } else if (travelService is PrivateSecurity) {
      return 'Private Security';
    } else if (travelService is Hotel) {
      return 'Hotel';
    } else if (travelService is Restaurant) {
      return 'Restaurant';
    } else if (travelService is Flight) {
      return 'Flight';
    } else if (travelService is Cruise) {
      return 'Cruise';
    } else {
      return 'Travel Service';
    }
  }
}
