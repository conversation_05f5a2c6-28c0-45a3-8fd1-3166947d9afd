import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';

// Project imports
import 'package:culture_connect/models/travel/insurance/insurance.dart';
import 'package:culture_connect/providers/travel/insurance/insurance_providers.dart';
import 'package:culture_connect/widgets/common/custom_app_bar.dart';
import 'package:culture_connect/widgets/common/loading_indicator.dart';
import 'package:culture_connect/widgets/common/error_view.dart';
import 'package:culture_connect/widgets/travel/insurance/insurance_coverage_card.dart';

/// Screen for displaying insurance policy details
class InsurancePolicyDetailsScreen extends ConsumerWidget {
  /// The ID of the policy to display
  final String policyId;

  /// Creates a new insurance policy details screen
  const InsurancePolicyDetailsScreen({
    super.key,
    required this.policyId,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final policyAsync = ref.watch(policyProvider(policyId));
    final claimsAsync = ref.watch(claimsByPolicyProvider(policyId));

    return Scaffold(
      appBar: const CustomAppBar(
        title: 'Policy Details',
        showBackButton: true,
      ),
      body: policyAsync.when(
        data: (policy) {
          if (policy == null) {
            return Center(
              child: Text(
                'Policy not found',
                style: theme.textTheme.titleMedium,
              ),
            );
          }

          return SingleChildScrollView(
            padding: EdgeInsets.all(16.r),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Policy header
                _buildPolicyHeader(context, theme, policy),

                SizedBox(height: 24.h),

                // Policy details
                _buildPolicyDetails(theme, policy),

                SizedBox(height: 24.h),

                // Coverage details
                Text(
                  'Coverage Details',
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(height: 16.h),

                ...policy.coverages.map((coverage) {
                  return Padding(
                    padding: EdgeInsets.only(bottom: 16.h),
                    child: InsuranceCoverageCard(
                      coverage: coverage,
                      showDetails: true,
                      showAmount: true,
                      showDeductible: true,
                      showMaximumBenefit: true,
                    ),
                  );
                }).toList(),

                SizedBox(height: 24.h),

                // Claims section
                if (policy.policyNumber != null) ...[
                  Text(
                    'Claims',
                    style: theme.textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  SizedBox(height: 16.h),
                  claimsAsync.when(
                    data: (claims) {
                      if (claims.isEmpty) {
                        return Column(
                          children: [
                            Center(
                              child: Text(
                                'No claims filed for this policy',
                                style: theme.textTheme.bodyMedium,
                              ),
                            ),
                            SizedBox(height: 16.h),
                            ElevatedButton.icon(
                              onPressed: () {
                                Navigator.pushNamed(
                                  context,
                                  '/travel/insurance/claim/new',
                                  arguments: {'policyId': policy.id},
                                );
                              },
                              icon: const Icon(Icons.add),
                              label: const Text('File a Claim'),
                              style: ElevatedButton.styleFrom(
                                backgroundColor: theme.colorScheme.primary,
                                foregroundColor: theme.colorScheme.onPrimary,
                              ),
                            ),
                          ],
                        );
                      }

                      return Column(
                        children: [
                          ...claims.map((claim) {
                            return ListTile(
                              leading: Container(
                                padding: EdgeInsets.all(8.r),
                                decoration: BoxDecoration(
                                  color: claim.status.color.withOpacity(0.1),
                                  borderRadius: BorderRadius.circular(8.r),
                                ),
                                child: Icon(
                                  claim.coverageType.icon,
                                  color: claim.status.color,
                                  size: 24.r,
                                ),
                              ),
                              title: Text(
                                claim.coverageType.displayName,
                                style: theme.textTheme.titleSmall?.copyWith(
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              subtitle: Text(
                                'Ref: ${claim.referenceNumber} • ${_formatDate(claim.submittedDate)}',
                                style: theme.textTheme.bodySmall,
                              ),
                              trailing: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                crossAxisAlignment: CrossAxisAlignment.end,
                                children: [
                                  Text(
                                    claim.formattedClaimAmount,
                                    style: theme.textTheme.titleSmall?.copyWith(
                                      fontWeight: FontWeight.bold,
                                      color: theme.colorScheme.primary,
                                    ),
                                  ),
                                  SizedBox(height: 4.h),
                                  Container(
                                    padding: EdgeInsets.symmetric(
                                      horizontal: 8.w,
                                      vertical: 2.h,
                                    ),
                                    decoration: BoxDecoration(
                                      color: claim.status.color
                                          .withAlpha(26), // 0.1 * 255 = 26
                                      borderRadius: BorderRadius.circular(4.r),
                                    ),
                                    child: Text(
                                      claim.status.displayName,
                                      style:
                                          theme.textTheme.bodySmall?.copyWith(
                                        color: claim.status.color,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                              onTap: () {
                                Navigator.pushNamed(
                                  context,
                                  '/travel/insurance/claim',
                                  arguments: claim.id,
                                );
                              },
                            );
                          }).toList(),
                          SizedBox(height: 16.h),
                          ElevatedButton.icon(
                            onPressed: () {
                              Navigator.pushNamed(
                                context,
                                '/travel/insurance/claim/new',
                                arguments: {'policyId': policy.id},
                              );
                            },
                            icon: const Icon(Icons.add),
                            label: const Text('File a New Claim'),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: theme.colorScheme.primary,
                              foregroundColor: theme.colorScheme.onPrimary,
                            ),
                          ),
                        ],
                      );
                    },
                    loading: () => const Center(
                      child: CircularProgressIndicator(),
                    ),
                    error: (error, stackTrace) => Center(
                      child: Text(
                        'Error loading claims: $error',
                        style: TextStyle(color: theme.colorScheme.error),
                      ),
                    ),
                  ),
                  SizedBox(height: 24.h),
                ],

                // Provider information
                Text(
                  'Provider Information',
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(height: 16.h),

                Card(
                  elevation: 2,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12.r),
                  ),
                  child: Padding(
                    padding: EdgeInsets.all(16.r),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            ClipRRect(
                              borderRadius: BorderRadius.circular(8.r),
                              child: Image.network(
                                policy.provider.logoUrl,
                                width: 48.r,
                                height: 48.r,
                                fit: BoxFit.cover,
                                errorBuilder: (context, error, stackTrace) {
                                  return Container(
                                    width: 48.r,
                                    height: 48.r,
                                    color: Colors.grey[300],
                                    child: Icon(
                                      Icons.business,
                                      size: 24.r,
                                      color: Colors.grey[600],
                                    ),
                                  );
                                },
                              ),
                            ),
                            SizedBox(width: 16.w),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    policy.provider.name,
                                    style:
                                        theme.textTheme.titleMedium?.copyWith(
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                  SizedBox(height: 4.h),
                                  Row(
                                    children: [
                                      Icon(
                                        Icons.star,
                                        size: 16.r,
                                        color: Colors.amber,
                                      ),
                                      SizedBox(width: 4.w),
                                      Text(
                                        '${policy.provider.rating} (${policy.provider.reviewCount} reviews)',
                                        style: theme.textTheme.bodySmall,
                                      ),
                                    ],
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                        SizedBox(height: 16.h),
                        Text(
                          policy.provider.description,
                          style: theme.textTheme.bodyMedium,
                        ),
                        SizedBox(height: 16.h),
                        Row(
                          children: [
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Row(
                                    children: [
                                      Icon(
                                        Icons.phone,
                                        size: 16.r,
                                        color:
                                            theme.colorScheme.onSurfaceVariant,
                                      ),
                                      SizedBox(width: 8.w),
                                      Text(
                                        policy.provider.phoneNumber,
                                        style:
                                            theme.textTheme.bodySmall?.copyWith(
                                          color: theme
                                              .colorScheme.onSurfaceVariant,
                                        ),
                                      ),
                                    ],
                                  ),
                                  SizedBox(height: 8.h),
                                  Row(
                                    children: [
                                      Icon(
                                        Icons.email,
                                        size: 16.r,
                                        color:
                                            theme.colorScheme.onSurfaceVariant,
                                      ),
                                      SizedBox(width: 8.w),
                                      Text(
                                        policy.provider.email,
                                        style:
                                            theme.textTheme.bodySmall?.copyWith(
                                          color: theme
                                              .colorScheme.onSurfaceVariant,
                                        ),
                                      ),
                                    ],
                                  ),
                                ],
                              ),
                            ),
                            SizedBox(width: 16.w),
                            ElevatedButton(
                              onPressed: () {
                                Navigator.pushNamed(
                                  context,
                                  '/travel/insurance/provider',
                                  arguments: policy.provider.id,
                                );
                              },
                              child: const Text('View Provider'),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),

                SizedBox(height: 24.h),

                // Actions
                if (policy.policyNumber == null) ...[
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: () {
                        Navigator.pushNamed(
                          context,
                          '/travel/insurance/purchase',
                          arguments: policy.id,
                        );
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: theme.colorScheme.primary,
                        foregroundColor: theme.colorScheme.onPrimary,
                        padding: EdgeInsets.symmetric(vertical: 16.h),
                      ),
                      child: const Text('Purchase This Policy'),
                    ),
                  ),
                ] else if (policy.status == InsurancePolicyStatus.active) ...[
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: () {
                        Navigator.pushNamed(
                          context,
                          '/travel/insurance/claim/new',
                          arguments: {'policyId': policy.id},
                        );
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: theme.colorScheme.primary,
                        foregroundColor: theme.colorScheme.onPrimary,
                        padding: EdgeInsets.symmetric(vertical: 16.h),
                      ),
                      child: const Text('File a Claim'),
                    ),
                  ),
                  SizedBox(height: 16.h),
                  SizedBox(
                    width: double.infinity,
                    child: OutlinedButton(
                      onPressed: () {
                        _showCancelConfirmationDialog(context, ref, policy);
                      },
                      style: OutlinedButton.styleFrom(
                        padding: EdgeInsets.symmetric(vertical: 16.h),
                      ),
                      child: const Text('Cancel Policy'),
                    ),
                  ),
                ],

                SizedBox(height: 32.h),
              ],
            ),
          );
        },
        loading: () => const Center(
          child: LoadingIndicator(),
        ),
        error: (error, stackTrace) => Center(
          child: ErrorView(
            error: error.toString(),
            onRetry: () => ref.refresh(policyProvider(policyId)),
          ),
        ),
      ),
    );
  }

  Widget _buildPolicyHeader(
      BuildContext context, ThemeData theme, InsurancePolicy policy) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16.r),
      ),
      child: Container(
        width: double.infinity,
        padding: EdgeInsets.all(24.r),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16.r),
          gradient: LinearGradient(
            colors: [
              theme.colorScheme.primary,
              theme.colorScheme.tertiary,
            ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        policy.name,
                        style: theme.textTheme.headlineSmall?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                      SizedBox(height: 4.h),
                      Text(
                        policy.type.displayName,
                        style: theme.textTheme.titleMedium?.copyWith(
                          color: Colors.white.withOpacity(0.9),
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  padding: EdgeInsets.all(12.r),
                  decoration: BoxDecoration(
                    color: Colors.white.withAlpha(51), // 0.2 * 255 = 51
                    borderRadius: BorderRadius.circular(12.r),
                  ),
                  child: Icon(
                    policy.type.icon,
                    color: Colors.white,
                    size: 32.r,
                  ),
                ),
              ],
            ),
            SizedBox(height: 24.h),
            if (policy.policyNumber != null) ...[
              Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Policy Number',
                          style: theme.textTheme.bodySmall?.copyWith(
                            color:
                                Colors.white.withAlpha(179), // 0.7 * 255 = 179
                          ),
                        ),
                        SizedBox(height: 4.h),
                        Text(
                          policy.policyNumber!,
                          style: theme.textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                        ),
                      ],
                    ),
                  ),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Status',
                          style: theme.textTheme.bodySmall?.copyWith(
                            color:
                                Colors.white.withAlpha(179), // 0.7 * 255 = 179
                          ),
                        ),
                        SizedBox(height: 4.h),
                        Container(
                          padding: EdgeInsets.symmetric(
                            horizontal: 8.w,
                            vertical: 4.h,
                          ),
                          decoration: BoxDecoration(
                            color: Colors.white.withAlpha(51), // 0.2 * 255 = 51
                            borderRadius: BorderRadius.circular(4.r),
                          ),
                          child: Text(
                            policy.status.displayName,
                            style: theme.textTheme.bodyMedium?.copyWith(
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              SizedBox(height: 16.h),
              if (policy.startDate != null && policy.endDate != null) ...[
                Row(
                  children: [
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Start Date',
                            style: theme.textTheme.bodySmall?.copyWith(
                              color: Colors.white
                                  .withAlpha(179), // 0.7 * 255 = 179
                            ),
                          ),
                          SizedBox(height: 4.h),
                          Text(
                            _formatDate(policy.startDate!),
                            style: theme.textTheme.titleSmall?.copyWith(
                              color: Colors.white,
                            ),
                          ),
                        ],
                      ),
                    ),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'End Date',
                            style: theme.textTheme.bodySmall?.copyWith(
                              color: Colors.white
                                  .withAlpha(179), // 0.7 * 255 = 179
                            ),
                          ),
                          SizedBox(height: 4.h),
                          Text(
                            _formatDate(policy.endDate!),
                            style: theme.textTheme.titleSmall?.copyWith(
                              color: Colors.white,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ] else ...[
              Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Price',
                          style: theme.textTheme.bodySmall?.copyWith(
                            color:
                                Colors.white.withAlpha(179), // 0.7 * 255 = 179
                          ),
                        ),
                        SizedBox(height: 4.h),
                        Text(
                          policy.formattedPrice,
                          style: theme.textTheme.headlineSmall?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                        ),
                      ],
                    ),
                  ),
                  TextButton.icon(
                    onPressed: () {
                      Navigator.pushNamed(
                        context,
                        '/travel/insurance/purchase',
                        arguments: policy.id,
                      );
                    },
                    icon: const Icon(Icons.shopping_cart, color: Colors.white),
                    label: Text(
                      'Purchase',
                      style: theme.textTheme.titleSmall?.copyWith(
                        color: Colors.white,
                      ),
                    ),
                    style: TextButton.styleFrom(
                      backgroundColor:
                          Colors.white.withAlpha(51), // 0.2 * 255 = 51
                      padding: EdgeInsets.symmetric(
                        horizontal: 16.w,
                        vertical: 8.h,
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildPolicyDetails(ThemeData theme, InsurancePolicy policy) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Policy Details',
          style: theme.textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        SizedBox(height: 16.h),
        Card(
          elevation: 2,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12.r),
          ),
          child: Padding(
            padding: EdgeInsets.all(16.r),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  policy.description,
                  style: theme.textTheme.bodyMedium,
                ),
                SizedBox(height: 16.h),
                if (policy.destinationCountries.isNotEmpty) ...[
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Icon(
                        Icons.location_on,
                        size: 20.r,
                        color: theme.colorScheme.onSurfaceVariant,
                      ),
                      SizedBox(width: 8.w),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Destination Countries',
                              style: theme.textTheme.titleSmall?.copyWith(
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            SizedBox(height: 4.h),
                            Text(
                              policy.destinationCountries.join(', '),
                              style: theme.textTheme.bodyMedium,
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 16.h),
                ],
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Icon(
                      Icons.people,
                      size: 20.r,
                      color: theme.colorScheme.onSurfaceVariant,
                    ),
                    SizedBox(width: 8.w),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Travelers',
                            style: theme.textTheme.titleSmall?.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          SizedBox(height: 4.h),
                          Text(
                            policy.formattedTravelerCount,
                            style: theme.textTheme.bodyMedium,
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 16.h),
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Icon(
                      Icons.policy,
                      size: 20.r,
                      color: theme.colorScheme.onSurfaceVariant,
                    ),
                    SizedBox(width: 8.w),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Refund Policy',
                            style: theme.textTheme.titleSmall?.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          SizedBox(height: 4.h),
                          Text(
                            policy.isRefundable
                                ? policy.refundPolicy
                                : 'Non-refundable',
                            style: theme.textTheme.bodyMedium,
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Future<void> _showCancelConfirmationDialog(
    BuildContext context,
    WidgetRef ref,
    InsurancePolicy policy,
  ) async {
    final theme = Theme.of(context);

    return showDialog<void>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Cancel Policy'),
          content: SingleChildScrollView(
            child: ListBody(
              children: <Widget>[
                Text(
                  'Are you sure you want to cancel this policy?',
                  style: theme.textTheme.bodyLarge,
                ),
                SizedBox(height: 16.h),
                if (policy.isRefundable) ...[
                  Text(
                    'Refund Policy:',
                    style: theme.textTheme.titleSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  SizedBox(height: 4.h),
                  Text(
                    policy.refundPolicy,
                    style: theme.textTheme.bodyMedium,
                  ),
                ] else ...[
                  Text(
                    'This policy is non-refundable. You will not receive any refund if you cancel.',
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: theme.colorScheme.error,
                    ),
                  ),
                ],
              ],
            ),
          ),
          actions: <Widget>[
            TextButton(
              child: const Text('No, Keep Policy'),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
            TextButton(
              child: const Text('Yes, Cancel Policy'),
              onPressed: () async {
                // Store the context for later use
                final currentContext = context;
                Navigator.of(currentContext).pop();

                // Show loading dialog
                if (!currentContext.mounted) return;

                showDialog(
                  context: currentContext,
                  barrierDismissible: false,
                  builder: (BuildContext context) {
                    return const AlertDialog(
                      content: Row(
                        children: [
                          CircularProgressIndicator(),
                          SizedBox(width: 16),
                          Text('Cancelling policy...'),
                        ],
                      ),
                    );
                  },
                );

                try {
                  // Cancel the policy
                  final insuranceService = ref.read(insuranceServiceProvider);
                  await insuranceService.cancelPolicy(policy.id);

                  // Refresh the policy
                  final _ = ref.refresh(policyProvider(policy.id));

                  // Check if the widget is still mounted
                  if (!currentContext.mounted) return;

                  // Close the loading dialog
                  Navigator.of(currentContext).pop();

                  // Show success dialog
                  if (!currentContext.mounted) return;

                  showDialog(
                    context: currentContext,
                    builder: (BuildContext context) {
                      return AlertDialog(
                        title: const Text('Policy Cancelled'),
                        content: const Text(
                          'Your policy has been successfully cancelled.',
                        ),
                        actions: <Widget>[
                          TextButton(
                            child: const Text('OK'),
                            onPressed: () {
                              Navigator.of(context).pop();
                            },
                          ),
                        ],
                      );
                    },
                  );
                } catch (e) {
                  // Check if the widget is still mounted
                  if (!currentContext.mounted) return;

                  // Close the loading dialog
                  Navigator.of(currentContext).pop();

                  // Show error dialog
                  if (!currentContext.mounted) return;

                  showDialog(
                    context: currentContext,
                    builder: (BuildContext context) {
                      return AlertDialog(
                        title: const Text('Error'),
                        content: Text(
                          'Failed to cancel policy: $e',
                        ),
                        actions: <Widget>[
                          TextButton(
                            child: const Text('OK'),
                            onPressed: () {
                              Navigator.of(context).pop();
                            },
                          ),
                        ],
                      );
                    },
                  );
                }
              },
            ),
          ],
        );
      },
    );
  }

  String _formatDate(DateTime date) {
    return DateFormat('MMM d, yyyy').format(date);
  }
}
