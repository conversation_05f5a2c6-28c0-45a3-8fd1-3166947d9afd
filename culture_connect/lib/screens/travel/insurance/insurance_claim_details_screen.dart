import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';

// Package imports
import 'package:timeline_tile/timeline_tile.dart';

// Project imports
import 'package:culture_connect/models/travel/insurance/insurance.dart'
    hide InsuranceCoverageType, InsuranceClaimStatus;
import 'package:culture_connect/models/travel/insurance/insurance_claim_status.dart';
import 'package:culture_connect/providers/travel/insurance/insurance_providers.dart';
import 'package:culture_connect/widgets/common/custom_app_bar.dart';
import 'package:culture_connect/widgets/common/loading_indicator.dart';
import 'package:culture_connect/widgets/common/error_view.dart';

/// A screen for displaying insurance claim details
class InsuranceClaimDetailsScreen extends ConsumerWidget {
  /// The ID of the claim to display
  final String claimId;

  /// Creates a new insurance claim details screen
  const InsuranceClaimDetailsScreen({
    super.key,
    required this.claimId,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final claimAsync = ref.watch(claimProvider(claimId));

    return Scaffold(
      appBar: const CustomAppBar(
        title: 'Claim Details',
        showBackButton: true,
      ),
      body: claimAsync.when(
        data: (claim) {
          if (claim == null) {
            return Center(
              child: Text(
                'Claim not found',
                style: theme.textTheme.titleMedium,
              ),
            );
          }

          return SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Claim header
                _buildClaimHeader(theme, claim),
                const SizedBox(height: 24),

                // Claim status timeline
                Text(
                  'Claim Status',
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 16),
                _buildClaimStatusTimeline(theme, claim),
                const SizedBox(height: 24),

                // Claim details
                Text(
                  'Claim Details',
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 16),
                Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        _buildDetailItem(
                          theme,
                          'Reference Number',
                          claim.referenceNumber,
                        ),
                        const SizedBox(height: 16),
                        _buildDetailItem(
                          theme,
                          'Incident Date',
                          DateFormat('MMM d, yyyy').format(claim.incidentDate),
                        ),
                        const SizedBox(height: 16),
                        _buildDetailItem(
                          theme,
                          'Incident Location',
                          claim.incidentLocation,
                        ),
                        const SizedBox(height: 16),
                        _buildDetailItem(
                          theme,
                          'Claim Amount',
                          claim.formattedClaimAmount,
                        ),
                        if (claim.approvedAmount != null) ...[
                          const SizedBox(height: 16),
                          _buildDetailItem(
                            theme,
                            'Approved Amount',
                            claim.formattedApprovedAmount!,
                            valueColor: Colors.green,
                          ),
                        ],
                        const SizedBox(height: 16),
                        _buildDetailItem(
                          theme,
                          'Submitted Date',
                          DateFormat('MMM d, yyyy').format(claim.submittedDate),
                        ),
                        if (claim.resolvedDate != null) ...[
                          const SizedBox(height: 16),
                          _buildDetailItem(
                            theme,
                            'Resolved Date',
                            DateFormat('MMM d, yyyy')
                                .format(claim.resolvedDate!),
                          ),
                        ],
                      ],
                    ),
                  ),
                ),
                const SizedBox(height: 24),

                // Incident description
                Text(
                  'Incident Description',
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 16),
                Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Text(
                      claim.incidentDescription,
                      style: theme.textTheme.bodyMedium,
                    ),
                  ),
                ),
                const SizedBox(height: 24),

                // Supporting documents
                Text(
                  'Supporting Documents',
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 16),
                _buildDocumentsList(context, theme, claim),
                const SizedBox(height: 24),

                // Additional information requested
                if (claim.additionalInfoRequested != null) ...[
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.amber.withAlpha(25),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: Colors.amber,
                        width: 1,
                      ),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Icon(
                              Icons.info_outline,
                              color: Colors.amber[800],
                              size: 24,
                            ),
                            const SizedBox(width: 8),
                            Text(
                              'Additional Information Requested',
                              style: theme.textTheme.titleMedium?.copyWith(
                                fontWeight: FontWeight.bold,
                                color: Colors.amber[800],
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 8),
                        Text(
                          claim.additionalInfoRequested!,
                          style: theme.textTheme.bodyMedium?.copyWith(
                            color: Colors.amber[800],
                          ),
                        ),
                        const SizedBox(height: 16),
                        SizedBox(
                          width: double.infinity,
                          child: ElevatedButton(
                            onPressed: () {
                              Navigator.pushNamed(
                                context,
                                '/travel/insurance/claim/update',
                                arguments: claim.id,
                              );
                            },
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.amber[800],
                              foregroundColor: Colors.white,
                            ),
                            child: const Text('Provide Information'),
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 24),
                ],

                // Denial reason
                if (claim.denialReason != null) ...[
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.red.withAlpha(25),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: Colors.red,
                        width: 1,
                      ),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            const Icon(
                              Icons.error_outline,
                              color: Colors.red,
                              size: 24,
                            ),
                            const SizedBox(width: 8),
                            Text(
                              'Claim Denied',
                              style: theme.textTheme.titleMedium?.copyWith(
                                fontWeight: FontWeight.bold,
                                color: Colors.red,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 8),
                        Text(
                          claim.denialReason!,
                          style: theme.textTheme.bodyMedium?.copyWith(
                            color: Colors.red,
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 24),
                ],

                // Policy information
                Text(
                  'Policy Information',
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 16),
                Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        _buildDetailItem(
                          theme,
                          'Policy Name',
                          claim.policy.name,
                        ),
                        const SizedBox(height: 16),
                        _buildDetailItem(
                          theme,
                          'Policy Number',
                          claim.policy.policyNumber ?? 'N/A',
                        ),
                        const SizedBox(height: 16),
                        _buildDetailItem(
                          theme,
                          'Provider',
                          claim.policy.provider.name,
                        ),
                        const SizedBox(height: 16),
                        _buildDetailItem(
                          theme,
                          'Coverage Type',
                          claim.coverageType.displayName,
                        ),
                        const SizedBox(height: 16),
                        ElevatedButton.icon(
                          onPressed: () {
                            Navigator.pushNamed(
                              context,
                              '/travel/insurance/policy',
                              arguments: claim.policy.id,
                            );
                          },
                          icon: const Icon(Icons.policy),
                          label: const Text('View Policy'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: theme.colorScheme.primary,
                            foregroundColor: theme.colorScheme.onPrimary,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                const SizedBox(height: 24),

                // Actions
                if (claim.status == InsuranceClaimStatus.infoRequested) ...[
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: () {
                        Navigator.pushNamed(
                          context,
                          '/travel/insurance/claim/update',
                          arguments: claim.id,
                        );
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: theme.colorScheme.primary,
                        foregroundColor: theme.colorScheme.onPrimary,
                        padding: const EdgeInsets.symmetric(vertical: 16),
                      ),
                      child: const Text('Update Claim'),
                    ),
                  ),
                ],
              ],
            ),
          );
        },
        loading: () => const Center(
          child: LoadingIndicator(),
        ),
        error: (error, stackTrace) => Center(
          child: ErrorView(
            error: error.toString(),
            onRetry: () => ref.refresh(claimProvider(claimId)),
          ),
        ),
      ),
    );
  }

  Widget _buildClaimHeader(ThemeData theme, InsuranceClaim claim) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(24),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          gradient: LinearGradient(
            colors: [
              claim.status.color.withAlpha(204), // 0.8 * 255 = 204
              claim.status.color.withAlpha(153), // 0.6 * 255 = 153
            ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        claim.coverageType.displayName,
                        style: theme.textTheme.headlineSmall?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'Ref: ${claim.referenceNumber}',
                        style: theme.textTheme.titleMedium?.copyWith(
                          color: Colors.white.withAlpha(230), // 0.9 * 255 = 230
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.white.withAlpha(51), // 0.2 * 255 = 51
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    claim.coverageType.icon,
                    color: Colors.white,
                    size: 32,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 24),
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Status',
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: Colors.white.withAlpha(179), // 0.7 * 255 = 179
                        ),
                      ),
                      const SizedBox(height: 4),
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.white.withAlpha(51), // 0.2 * 255 = 51
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: Text(
                          claim.status.displayName,
                          style: theme.textTheme.bodyMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Claim Amount',
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: Colors.white.withAlpha(179), // 0.7 * 255 = 179
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        claim.formattedClaimAmount,
                        style: theme.textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            if (claim.approvedAmount != null) ...[
              const SizedBox(height: 16),
              Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Approved Amount',
                          style: theme.textTheme.bodySmall?.copyWith(
                            color:
                                Colors.white.withAlpha(179), // 0.7 * 255 = 179
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          claim.formattedApprovedAmount!,
                          style: theme.textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildClaimStatusTimeline(ThemeData theme, InsuranceClaim claim) {
    final statuses = [
      InsuranceClaimStatus.submitted,
      InsuranceClaimStatus.inReview,
      if (claim.additionalInfoRequested != null)
        InsuranceClaimStatus.infoRequested,
      if (claim.status == InsuranceClaimStatus.approved ||
          claim.status == InsuranceClaimStatus.partiallyPaid ||
          claim.status == InsuranceClaimStatus.paid)
        claim.status == InsuranceClaimStatus.partiallyPaid
            ? InsuranceClaimStatus.partiallyPaid
            : InsuranceClaimStatus.approved,
      if (claim.status == InsuranceClaimStatus.denied)
        InsuranceClaimStatus.denied,
      if (claim.status == InsuranceClaimStatus.paid) InsuranceClaimStatus.paid,
    ];

    final currentStatusIndex = statuses.indexOf(claim.status);

    return SizedBox(
      height: 100,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: statuses.length,
        itemBuilder: (context, index) {
          final status = statuses[index];
          final isActive = index <= currentStatusIndex;
          final isLast = index == statuses.length - 1;

          return SizedBox(
            width: 120,
            child: TimelineTile(
              axis: TimelineAxis.horizontal,
              alignment: TimelineAlign.center,
              isFirst: index == 0,
              isLast: isLast,
              indicatorStyle: IndicatorStyle(
                width: 24,
                height: 24,
                indicator: Container(
                  decoration: BoxDecoration(
                    color: isActive ? status.color : Colors.grey[300],
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    status.icon,
                    color: isActive ? Colors.white : Colors.grey[500],
                    size: 16,
                  ),
                ),
              ),
              beforeLineStyle: LineStyle(
                color: isActive ? status.color : Colors.grey.shade300,
                thickness: 2,
              ),
              afterLineStyle: LineStyle(
                color: index < currentStatusIndex
                    ? status.color
                    : Colors.grey.shade300,
                thickness: 2,
              ),
              endChild: Padding(
                padding: const EdgeInsets.only(top: 8),
                child: Text(
                  status.displayName,
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: isActive ? status.color : Colors.grey[500],
                    fontWeight: isActive ? FontWeight.bold : FontWeight.normal,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
              startChild: Padding(
                padding: const EdgeInsets.only(bottom: 8),
                child: Text(
                  _getStatusDate(claim, status),
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: isActive ? Colors.black87 : Colors.grey[500],
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  String _getStatusDate(InsuranceClaim claim, InsuranceClaimStatus status) {
    switch (status) {
      case InsuranceClaimStatus.submitted:
        return DateFormat('MM/dd/yy').format(claim.submittedDate);
      case InsuranceClaimStatus.inReview:
        if (claim.status == InsuranceClaimStatus.submitted) return '';
        // In a real app, this would come from the claim history
        return DateFormat('MM/dd/yy')
            .format(claim.submittedDate.add(const Duration(days: 1)));
      case InsuranceClaimStatus.infoRequested:
        if (claim.status == InsuranceClaimStatus.submitted ||
            claim.status == InsuranceClaimStatus.inReview) {
          return '';
        }
        // In a real app, this would come from the claim history
        return DateFormat('MM/dd/yy')
            .format(claim.submittedDate.add(const Duration(days: 2)));
      case InsuranceClaimStatus.approved:
      case InsuranceClaimStatus.partiallyPaid:
      case InsuranceClaimStatus.denied:
        if (claim.resolvedDate == null) return '';
        return DateFormat('MM/dd/yy').format(claim.resolvedDate!);
      case InsuranceClaimStatus.paid:
        if (claim.resolvedDate == null) return '';
        // In a real app, this would come from the claim history
        return DateFormat('MM/dd/yy')
            .format(claim.resolvedDate!.add(const Duration(days: 2)));
      case InsuranceClaimStatus.appealed:
      case InsuranceClaimStatus.closed:
        if (claim.resolvedDate == null) return '';
        return DateFormat('MM/dd/yy').format(claim.resolvedDate!);
    }
  }

  Widget _buildDetailItem(
    ThemeData theme,
    String label,
    String value, {
    Color? valueColor,
  }) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Expanded(
          flex: 2,
          child: Text(
            label,
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
            ),
          ),
        ),
        Expanded(
          flex: 3,
          child: Text(
            value,
            style: theme.textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: valueColor,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildDocumentsList(
      BuildContext context, ThemeData theme, InsuranceClaim claim) {
    if (claim.documentUrls.isEmpty) {
      return Card(
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Center(
            child: Text(
              'No documents uploaded',
              style: theme.textTheme.bodyMedium,
            ),
          ),
        ),
      );
    }

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: claim.documentUrls.asMap().entries.map((entry) {
            final index = entry.key;
            final url = entry.value;

            return ListTile(
              leading: Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: theme.colorScheme.primaryContainer,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  Icons.description,
                  color: theme.colorScheme.primary,
                  size: 24,
                ),
              ),
              title: Text(
                'Document ${index + 1}',
                style: theme.textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              subtitle: Text(
                url.split('/').last,
                style: theme.textTheme.bodySmall,
              ),
              trailing: IconButton(
                icon: const Icon(Icons.visibility),
                onPressed: () {
                  // In a real app, this would open the document
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('Viewing document: ${url.split('/').last}'),
                      duration: const Duration(seconds: 2),
                    ),
                  );
                },
              ),
            );
          }).toList(),
        ),
      ),
    );
  }
}
