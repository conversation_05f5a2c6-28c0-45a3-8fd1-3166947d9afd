import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';

// Project imports
import 'package:culture_connect/models/travel/insurance/insurance.dart';
import 'package:culture_connect/models/payment/payment_method_model.dart';
import 'package:culture_connect/providers/travel/insurance/insurance_providers.dart';
import 'package:culture_connect/widgets/common/custom_app_bar.dart';
import 'package:culture_connect/widgets/common/loading_indicator.dart';
import 'package:culture_connect/widgets/common/error_view.dart';

/// A screen for purchasing an insurance policy
class InsurancePurchaseScreen extends ConsumerStatefulWidget {
  /// The ID of the policy to purchase
  final String policyId;

  /// Creates a new insurance purchase screen
  const InsurancePurchaseScreen({
    super.key,
    required this.policyId,
  });

  @override
  ConsumerState<InsurancePurchaseScreen> createState() =>
      _InsurancePurchaseScreenState();
}

class _InsurancePurchaseScreenState
    extends ConsumerState<InsurancePurchaseScreen> {
  // Stepper
  int _currentStep = 0;

  // Form data
  late DateTime _startDate;
  late DateTime _endDate;
  late int _travelerCount;
  late List<String> _destinationCountries;
  late PaymentMethodModel? _selectedPaymentMethod;

  // Form keys
  final _formKeys = [
    GlobalKey<FormState>(),
    GlobalKey<FormState>(),
    GlobalKey<FormState>(),
  ];

  // Loading state
  bool _isLoading = false;
  String? _error;

  // Traveler information
  final List<Map<String, String>> _travelers = [];

  @override
  void initState() {
    super.initState();

    // Initialize form data with defaults
    final now = DateTime.now();
    _startDate = now.add(const Duration(days: 7));
    _endDate = now.add(const Duration(days: 14));
    _travelerCount = 1;
    _destinationCountries = [];
    _selectedPaymentMethod = null;

    // Initialize traveler information
    _updateTravelersList();
  }

  void _updateTravelersList() {
    // Adjust the travelers list based on the traveler count
    if (_travelers.length < _travelerCount) {
      // Add new travelers
      for (int i = _travelers.length; i < _travelerCount; i++) {
        _travelers.add({
          'firstName': '',
          'lastName': '',
          'email': '',
          'phone': '',
          'dateOfBirth': '',
          'passportNumber': '',
        });
      }
    } else if (_travelers.length > _travelerCount) {
      // Remove excess travelers
      _travelers.removeRange(_travelerCount, _travelers.length);
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final policyAsync = ref.watch(policyProvider(widget.policyId));

    return Scaffold(
      appBar: const CustomAppBar(
        title: 'Purchase Insurance',
        showBackButton: true,
      ),
      body: policyAsync.when(
        data: (policy) {
          if (policy == null) {
            return Center(
              child: Text(
                'Policy not found',
                style: theme.textTheme.titleMedium,
              ),
            );
          }

          return _buildPurchaseForm(theme, policy);
        },
        loading: () => const Center(
          child: LoadingIndicator(),
        ),
        error: (error, stackTrace) => Center(
          child: ErrorView(
            error: error.toString(),
            onRetry: () => ref.refresh(policyProvider(widget.policyId)),
          ),
        ),
      ),
    );
  }

  Widget _buildPurchaseForm(ThemeData theme, InsurancePolicy policy) {
    return Column(
      children: [
        // Policy summary
        Container(
          width: double.infinity,
          padding: EdgeInsets.all(16.r),
          color: theme.colorScheme.surfaceContainerLow,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Policy Summary',
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              SizedBox(height: 8.h),
              Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          policy.name,
                          style: theme.textTheme.titleSmall?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        SizedBox(height: 4.h),
                        Text(
                          policy.provider.name,
                          style: theme.textTheme.bodySmall,
                        ),
                      ],
                    ),
                  ),
                  Text(
                    policy.formattedPrice,
                    style: theme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: theme.colorScheme.primary,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),

        // Stepper
        Expanded(
          child: Stepper(
            currentStep: _currentStep,
            onStepTapped: (step) {
              setState(() {
                _currentStep = step;
              });
            },
            onStepContinue: () {
              if (_currentStep < 2) {
                // Validate current step
                if (_formKeys[_currentStep].currentState!.validate()) {
                  _formKeys[_currentStep].currentState!.save();
                  setState(() {
                    _currentStep += 1;
                  });
                }
              } else {
                // Final step - purchase policy
                _purchasePolicy(policy);
              }
            },
            onStepCancel: () {
              if (_currentStep > 0) {
                setState(() {
                  _currentStep -= 1;
                });
              } else {
                Navigator.of(context).pop();
              }
            },
            steps: [
              Step(
                title: const Text('Trip Details'),
                content: _buildTripDetailsStep(theme),
                isActive: _currentStep >= 0,
              ),
              Step(
                title: const Text('Traveler Information'),
                content: _buildTravelerInformationStep(theme),
                isActive: _currentStep >= 1,
              ),
              Step(
                title: const Text('Payment'),
                content: _buildPaymentStep(theme),
                isActive: _currentStep >= 2,
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildTripDetailsStep(ThemeData theme) {
    return Form(
      key: _formKeys[0],
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Travel dates
          Text(
            'Travel Dates',
            style: theme.textTheme.titleSmall?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: 8.h),
          Row(
            children: [
              Expanded(
                child: TextFormField(
                  decoration: const InputDecoration(
                    labelText: 'Start Date',
                    suffixIcon: Icon(Icons.calendar_today),
                  ),
                  readOnly: true,
                  initialValue: DateFormat('MMM d, yyyy').format(_startDate),
                  onTap: () => _selectStartDate(context),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please select a start date';
                    }
                    return null;
                  },
                ),
              ),
              SizedBox(width: 16.w),
              Expanded(
                child: TextFormField(
                  decoration: const InputDecoration(
                    labelText: 'End Date',
                    suffixIcon: Icon(Icons.calendar_today),
                  ),
                  readOnly: true,
                  initialValue: DateFormat('MMM d, yyyy').format(_endDate),
                  onTap: () => _selectEndDate(context),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please select an end date';
                    }
                    return null;
                  },
                ),
              ),
            ],
          ),
          SizedBox(height: 16.h),

          // Destination countries
          Text(
            'Destination Countries',
            style: theme.textTheme.titleSmall?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: 8.h),
          Wrap(
            spacing: 8.w,
            runSpacing: 8.h,
            children: [
              // Add country chips here
              ActionChip(
                label: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      Icons.add,
                      size: 16.r,
                    ),
                    SizedBox(width: 4.w),
                    const Text('Add Country'),
                  ],
                ),
                onPressed: () => _selectCountry(context),
              ),
              ..._destinationCountries.map((country) {
                return Chip(
                  label: Text(country),
                  onDeleted: () {
                    setState(() {
                      _destinationCountries.remove(country);
                    });
                  },
                );
              }),
            ],
          ),
          if (_destinationCountries.isEmpty)
            Padding(
              padding: EdgeInsets.only(top: 8.h),
              child: Text(
                'Please select at least one destination country',
                style: theme.textTheme.bodySmall?.copyWith(
                  color: theme.colorScheme.error,
                ),
              ),
            ),
          SizedBox(height: 16.h),

          // Traveler count
          Text(
            'Number of Travelers',
            style: theme.textTheme.titleSmall?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: 8.h),
          Row(
            children: [
              IconButton(
                icon: const Icon(Icons.remove),
                onPressed: _travelerCount > 1
                    ? () {
                        setState(() {
                          _travelerCount--;
                          _updateTravelersList();
                        });
                      }
                    : null,
              ),
              SizedBox(width: 16.w),
              Text(
                '$_travelerCount',
                style: theme.textTheme.titleMedium,
              ),
              SizedBox(width: 16.w),
              IconButton(
                icon: const Icon(Icons.add),
                onPressed: _travelerCount < 10
                    ? () {
                        setState(() {
                          _travelerCount++;
                          _updateTravelersList();
                        });
                      }
                    : null,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildTravelerInformationStep(ThemeData theme) {
    return Form(
      key: _formKeys[1],
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          for (int i = 0; i < _travelers.length; i++) ...[
            Text(
              'Traveler ${i + 1}',
              style: theme.textTheme.titleSmall?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 8.h),
            Row(
              children: [
                Expanded(
                  child: TextFormField(
                    decoration: const InputDecoration(
                      labelText: 'First Name',
                    ),
                    initialValue: _travelers[i]['firstName'],
                    onChanged: (value) {
                      setState(() {
                        _travelers[i]['firstName'] = value;
                      });
                    },
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Please enter a first name';
                      }
                      return null;
                    },
                  ),
                ),
                SizedBox(width: 16.w),
                Expanded(
                  child: TextFormField(
                    decoration: const InputDecoration(
                      labelText: 'Last Name',
                    ),
                    initialValue: _travelers[i]['lastName'],
                    onChanged: (value) {
                      setState(() {
                        _travelers[i]['lastName'] = value;
                      });
                    },
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Please enter a last name';
                      }
                      return null;
                    },
                  ),
                ),
              ],
            ),
            SizedBox(height: 16.h),
            TextFormField(
              decoration: const InputDecoration(
                labelText: 'Email',
              ),
              initialValue: _travelers[i]['email'],
              onChanged: (value) {
                setState(() {
                  _travelers[i]['email'] = value;
                });
              },
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please enter an email';
                }
                if (!value.contains('@')) {
                  return 'Please enter a valid email';
                }
                return null;
              },
            ),
            SizedBox(height: 16.h),
            TextFormField(
              decoration: const InputDecoration(
                labelText: 'Phone Number',
              ),
              initialValue: _travelers[i]['phone'],
              onChanged: (value) {
                setState(() {
                  _travelers[i]['phone'] = value;
                });
              },
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please enter a phone number';
                }
                return null;
              },
            ),
            SizedBox(height: 16.h),
            TextFormField(
              decoration: const InputDecoration(
                labelText: 'Date of Birth',
                suffixIcon: Icon(Icons.calendar_today),
              ),
              readOnly: true,
              initialValue: _travelers[i]['dateOfBirth'],
              onTap: () => _selectDateOfBirth(context, i),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please select a date of birth';
                }
                return null;
              },
            ),
            SizedBox(height: 16.h),
            TextFormField(
              decoration: const InputDecoration(
                labelText: 'Passport Number (Optional)',
              ),
              initialValue: _travelers[i]['passportNumber'],
              onChanged: (value) {
                setState(() {
                  _travelers[i]['passportNumber'] = value;
                });
              },
            ),
            SizedBox(height: 24.h),
          ],
        ],
      ),
    );
  }

  Widget _buildPaymentStep(ThemeData theme) {
    return Form(
      key: _formKeys[2],
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Payment method selector
          Text(
            'Payment Method',
            style: theme.textTheme.titleSmall?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: 8.h),
          // In a real app, this would be a proper payment method selector
          // For now, we'll use a simple dropdown
          DropdownButtonFormField<String>(
            decoration: const InputDecoration(
              labelText: 'Payment Method',
              border: OutlineInputBorder(),
            ),
            value: _selectedPaymentMethod?.id,
            items: [
              DropdownMenuItem(
                value: 'card_visa',
                child: Row(
                  children: [
                    const Icon(Icons.credit_card, color: Colors.blue),
                    SizedBox(width: 8.w),
                    const Text('Visa ending in 1234'),
                  ],
                ),
              ),
              DropdownMenuItem(
                value: 'card_mc',
                child: Row(
                  children: [
                    const Icon(Icons.credit_card, color: Colors.red),
                    SizedBox(width: 8.w),
                    const Text('MasterCard ending in 5678'),
                  ],
                ),
              ),
              DropdownMenuItem(
                value: 'paypal',
                child: Row(
                  children: [
                    const Icon(Icons.account_balance_wallet,
                        color: Colors.blue),
                    SizedBox(width: 8.w),
                    const Text('PayPal'),
                  ],
                ),
              ),
            ],
            onChanged: (value) {
              if (value != null) {
                setState(() {
                  // Create a simple payment method model
                  _selectedPaymentMethod = PaymentMethodModel(
                    id: value,
                    type: PaymentMethodType.creditCard, // Use creditCard type
                    name: value == 'card_visa'
                        ? 'Visa ending in 1234'
                        : value == 'card_mc'
                            ? 'MasterCard ending in 5678'
                            : 'PayPal',
                  );
                });
              }
            },
          ),
          if (_selectedPaymentMethod == null)
            Padding(
              padding: EdgeInsets.only(top: 8.h),
              child: Text(
                'Please select a payment method',
                style: theme.textTheme.bodySmall?.copyWith(
                  color: theme.colorScheme.error,
                ),
              ),
            ),
          SizedBox(height: 16.h),

          // Terms and conditions
          CheckboxListTile(
            title: Text(
              'I agree to the terms and conditions',
              style: theme.textTheme.bodyMedium,
            ),
            value: true, // In a real app, this would be a state variable
            onChanged: (value) {
              // In a real app, this would update a state variable
            },
            controlAffinity: ListTileControlAffinity.leading,
            contentPadding: EdgeInsets.zero,
          ),

          // Error message
          if (_error != null)
            Padding(
              padding: EdgeInsets.only(top: 16.h),
              child: Text(
                _error!,
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: theme.colorScheme.error,
                ),
              ),
            ),

          // Loading indicator
          if (_isLoading)
            Padding(
              padding: EdgeInsets.only(top: 16.h),
              child: const Center(
                child: CircularProgressIndicator(),
              ),
            ),
        ],
      ),
    );
  }

  Future<void> _selectStartDate(BuildContext context) async {
    final initialDate = _startDate;
    final firstDate = DateTime.now();
    final lastDate = DateTime.now().add(const Duration(days: 365));

    final newDate = await showDatePicker(
      context: context,
      initialDate: initialDate,
      firstDate: firstDate,
      lastDate: lastDate,
    );

    if (newDate != null && mounted) {
      setState(() {
        _startDate = newDate;

        // Ensure end date is not before start date
        if (_endDate.isBefore(_startDate)) {
          _endDate = _startDate.add(const Duration(days: 7));
        }
      });
    }
  }

  Future<void> _selectEndDate(BuildContext context) async {
    final initialDate = _endDate.isBefore(_startDate)
        ? _startDate.add(const Duration(days: 1))
        : _endDate;
    final firstDate = _startDate;
    final lastDate = _startDate.add(const Duration(days: 365));

    final newDate = await showDatePicker(
      context: context,
      initialDate: initialDate,
      firstDate: firstDate,
      lastDate: lastDate,
    );

    if (newDate != null && mounted) {
      setState(() {
        _endDate = newDate;
      });
    }
  }

  Future<void> _selectDateOfBirth(
      BuildContext context, int travelerIndex) async {
    final initialDate = DateTime.now().subtract(const Duration(days: 365 * 30));
    final firstDate = DateTime.now().subtract(const Duration(days: 365 * 100));
    final lastDate = DateTime.now();

    final newDate = await showDatePicker(
      context: context,
      initialDate: initialDate,
      firstDate: firstDate,
      lastDate: lastDate,
    );

    if (newDate != null && mounted) {
      setState(() {
        _travelers[travelerIndex]['dateOfBirth'] =
            DateFormat('MMM d, yyyy').format(newDate);
      });
    }
  }

  Future<void> _selectCountry(BuildContext context) async {
    // In a real app, this would show a country picker
    // For now, we'll just add a few hardcoded countries
    final countries = [
      'United States',
      'Canada',
      'United Kingdom',
      'France',
      'Germany',
      'Italy',
      'Spain',
      'Japan',
      'Australia',
      'Brazil',
    ];

    final selectedCountry = await showDialog<String>(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('Select Country'),
          content: SizedBox(
            width: double.maxFinite,
            child: ListView.builder(
              shrinkWrap: true,
              itemCount: countries.length,
              itemBuilder: (context, index) {
                final country = countries[index];
                return ListTile(
                  title: Text(country),
                  onTap: () {
                    Navigator.of(context).pop(country);
                  },
                );
              },
            ),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: const Text('Cancel'),
            ),
          ],
        );
      },
    );

    if (selectedCountry != null &&
        !_destinationCountries.contains(selectedCountry) &&
        mounted) {
      setState(() {
        _destinationCountries.add(selectedCountry);
      });
    }
  }

  Future<void> _purchasePolicy(InsurancePolicy policy) async {
    // Validate form
    if (!_formKeys[2].currentState!.validate()) {
      return;
    }

    // Check if payment method is selected
    if (_selectedPaymentMethod == null) {
      setState(() {
        _error = 'Please select a payment method';
      });
      return;
    }

    // Check if destination countries are selected
    if (_destinationCountries.isEmpty) {
      setState(() {
        _error = 'Please select at least one destination country';
        _currentStep = 0; // Go back to trip details step
      });
      return;
    }

    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      // Purchase policy
      final insuranceService = ref.read(insuranceServiceProvider);
      final purchasedPolicy = await insuranceService.purchasePolicy(
        policy: policy,
        startDate: _startDate,
        endDate: _endDate,
        destinationCountries: _destinationCountries,
        travelerCount: _travelerCount,
        paymentMethod: _selectedPaymentMethod!,
      );

      if (!mounted) return;

      setState(() {
        _isLoading = false;
      });

      // Show success dialog
      _showPurchaseSuccessDialog(purchasedPolicy);
    } catch (e) {
      if (!mounted) return;

      setState(() {
        _isLoading = false;
        _error = e.toString();
      });
    }
  }

  void _showPurchaseSuccessDialog(InsurancePolicy policy) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) {
        return AlertDialog(
          title: const Text('Purchase Successful'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text('Your policy has been successfully purchased.'),
              SizedBox(height: 16.h),
              Text('Policy Number: ${policy.policyNumber}'),
              SizedBox(height: 8.h),
              Text(
                  'Start Date: ${DateFormat('MMM d, yyyy').format(policy.startDate!)}'),
              SizedBox(height: 8.h),
              Text(
                  'End Date: ${DateFormat('MMM d, yyyy').format(policy.endDate!)}'),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                Navigator.of(context).pushReplacementNamed(
                  '/travel/insurance/policy',
                  arguments: policy.id,
                );
              },
              child: const Text('View Policy'),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                Navigator.of(context).pushReplacementNamed('/travel/insurance');
              },
              child: const Text('Done'),
            ),
          ],
        );
      },
    );
  }
}
