import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:culture_connect/models/travel/insurance/insurance.dart';
import 'package:culture_connect/providers/travel/insurance/insurance_providers.dart';
import 'package:culture_connect/widgets/common/custom_app_bar.dart';
import 'package:culture_connect/widgets/common/animated_refresh_indicator.dart';
import 'package:culture_connect/widgets/travel/insurance/insurance_policy_card.dart';
import 'package:culture_connect/widgets/travel/insurance/insurance_provider_card.dart';
import 'package:culture_connect/widgets/travel/insurance/insurance_claim_card.dart';
import 'package:culture_connect/theme/app_theme.dart';
import 'package:culture_connect/utils/refresh_animation_utils.dart';

/// The main screen for insurance features
class InsuranceHomeScreen extends ConsumerStatefulWidget {
  /// Creates a new insurance home screen
  const InsuranceHomeScreen({super.key});

  @override
  ConsumerState<InsuranceHomeScreen> createState() => _InsuranceHomeScreenState();
}

class _InsuranceHomeScreenState extends ConsumerState<InsuranceHomeScreen> with SingleTickerProviderStateMixin {
  late TabController _tabController;
  
  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }
  
  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }
  
  Future<void> _refreshData() async {
    // Refresh all providers
    ref.refresh(availablePoliciesProvider);
    ref.refresh(purchasedPoliciesProvider);
    ref.refresh(activePoliciesProvider);
    ref.refresh(claimsProvider);
    ref.refresh(insuranceProvidersProvider);
    ref.refresh(featuredProvidersProvider);
    
    // Wait for a moment to simulate network request
    await Future.delayed(const Duration(seconds: 1));
  }
  
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Scaffold(
      appBar: CustomAppBar(
        title: 'Travel Insurance',
        showBackButton: true,
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'Explore'),
            Tab(text: 'My Policies'),
            Tab(text: 'Claims'),
          ],
        ),
      ),
      body: AnimatedRefreshIndicator(
        onRefresh: _refreshData,
        animationType: RefreshAnimationType.liquid,
        child: TabBarView(
          controller: _tabController,
          children: [
            _buildExploreTab(theme),
            _buildMyPoliciesTab(theme),
            _buildClaimsTab(theme),
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () {
          Navigator.pushNamed(context, '/travel/insurance/search');
        },
        icon: const Icon(Icons.search),
        label: const Text('Find Insurance'),
        backgroundColor: theme.colorScheme.primary,
        foregroundColor: theme.colorScheme.onPrimary,
      ),
    );
  }
  
  Widget _buildExploreTab(ThemeData theme) {
    final featuredProvidersAsync = ref.watch(featuredProvidersProvider);
    final availablePoliciesAsync = ref.watch(availablePoliciesProvider);
    
    return SingleChildScrollView(
      padding: EdgeInsets.all(16.r),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Hero section
          _buildHeroSection(theme),
          
          SizedBox(height: 24.h),
          
          // Featured providers section
          Text(
            'Featured Providers',
            style: theme.textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: 16.h),
          
          featuredProvidersAsync.when(
            data: (providers) {
              if (providers.isEmpty) {
                return Center(
                  child: Text(
                    'No featured providers available',
                    style: theme.textTheme.bodyMedium,
                  ),
                );
              }
              
              return Column(
                children: providers.map((provider) {
                  return Padding(
                    padding: EdgeInsets.only(bottom: 16.h),
                    child: InsuranceProviderCard(
                      provider: provider,
                      showDetails: true,
                      showRating: true,
                      showContact: false,
                      showCountries: false,
                      showActions: true,
                      onTap: () {
                        Navigator.pushNamed(
                          context,
                          '/travel/insurance/provider',
                          arguments: provider.id,
                        );
                      },
                      onViewPolicies: () {
                        Navigator.pushNamed(
                          context,
                          '/travel/insurance/provider/policies',
                          arguments: provider.id,
                        );
                      },
                      onContact: () {
                        Navigator.pushNamed(
                          context,
                          '/travel/insurance/provider/contact',
                          arguments: provider.id,
                        );
                      },
                    ),
                  );
                }).toList(),
              );
            },
            loading: () => const Center(
              child: CircularProgressIndicator(),
            ),
            error: (error, stackTrace) => Center(
              child: Text(
                'Error loading providers: $error',
                style: TextStyle(color: theme.colorScheme.error),
              ),
            ),
          ),
          
          SizedBox(height: 24.h),
          
          // Popular policies section
          Text(
            'Popular Policies',
            style: theme.textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: 16.h),
          
          availablePoliciesAsync.when(
            data: (policies) {
              if (policies.isEmpty) {
                return Center(
                  child: Text(
                    'No policies available',
                    style: theme.textTheme.bodyMedium,
                  ),
                );
              }
              
              // Sort by price
              final sortedPolicies = List<InsurancePolicy>.from(policies)
                ..sort((a, b) => a.price.compareTo(b.price));
              
              return Column(
                children: sortedPolicies.take(3).map((policy) {
                  return Padding(
                    padding: EdgeInsets.only(bottom: 16.h),
                    child: InsurancePolicyCard(
                      policy: policy,
                      showDetails: true,
                      showStatus: false,
                      showProvider: true,
                      showCoverage: true,
                      showDates: false,
                      showPrice: true,
                      showActions: true,
                      onTap: () {
                        Navigator.pushNamed(
                          context,
                          '/travel/insurance/policy',
                          arguments: policy.id,
                        );
                      },
                      onViewDetails: () {
                        Navigator.pushNamed(
                          context,
                          '/travel/insurance/policy',
                          arguments: policy.id,
                        );
                      },
                      onPurchase: () {
                        Navigator.pushNamed(
                          context,
                          '/travel/insurance/purchase',
                          arguments: policy.id,
                        );
                      },
                    ),
                  );
                }).toList(),
              );
            },
            loading: () => const Center(
              child: CircularProgressIndicator(),
            ),
            error: (error, stackTrace) => Center(
              child: Text(
                'Error loading policies: $error',
                style: TextStyle(color: theme.colorScheme.error),
              ),
            ),
          ),
          
          SizedBox(height: 24.h),
          
          // Insurance types section
          Text(
            'Insurance Types',
            style: theme.textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: 16.h),
          
          _buildInsuranceTypesGrid(theme),
          
          SizedBox(height: 24.h),
          
          // Why get travel insurance section
          _buildWhyGetInsuranceSection(theme),
          
          SizedBox(height: 24.h),
        ],
      ),
    );
  }
  
  Widget _buildMyPoliciesTab(ThemeData theme) {
    final purchasedPoliciesAsync = ref.watch(purchasedPoliciesProvider);
    
    return SingleChildScrollView(
      padding: EdgeInsets.all(16.r),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Active policies section
          Text(
            'My Policies',
            style: theme.textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: 16.h),
          
          purchasedPoliciesAsync.when(
            data: (policies) {
              if (policies.isEmpty) {
                return Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.policy_outlined,
                        size: 64.r,
                        color: Colors.grey[400],
                      ),
                      SizedBox(height: 16.h),
                      Text(
                        'No policies found',
                        style: theme.textTheme.titleMedium,
                      ),
                      SizedBox(height: 8.h),
                      Text(
                        'You haven\'t purchased any insurance policies yet.',
                        style: theme.textTheme.bodyMedium,
                        textAlign: TextAlign.center,
                      ),
                      SizedBox(height: 24.h),
                      ElevatedButton.icon(
                        onPressed: () {
                          Navigator.pushNamed(context, '/travel/insurance/search');
                        },
                        icon: const Icon(Icons.search),
                        label: const Text('Find Insurance'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: theme.colorScheme.primary,
                          foregroundColor: theme.colorScheme.onPrimary,
                        ),
                      ),
                    ],
                  ),
                );
              }
              
              // Group policies by status
              final activePolicies = policies.where((p) => p.status == InsurancePolicyStatus.active).toList();
              final expiredPolicies = policies.where((p) => p.status == InsurancePolicyStatus.expired).toList();
              final cancelledPolicies = policies.where((p) => p.status == InsurancePolicyStatus.cancelled).toList();
              final pendingPolicies = policies.where((p) => p.status == InsurancePolicyStatus.pending).toList();
              
              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  if (activePolicies.isNotEmpty) ...[
                    Text(
                      'Active Policies',
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    SizedBox(height: 8.h),
                    ...activePolicies.map((policy) {
                      return Padding(
                        padding: EdgeInsets.only(bottom: 16.h),
                        child: InsurancePolicyCard(
                          policy: policy,
                          showDetails: false,
                          showStatus: true,
                          showProvider: true,
                          showCoverage: false,
                          showDates: true,
                          showPrice: false,
                          showActions: true,
                          onTap: () {
                            Navigator.pushNamed(
                              context,
                              '/travel/insurance/policy',
                              arguments: policy.id,
                            );
                          },
                          onViewDetails: () {
                            Navigator.pushNamed(
                              context,
                              '/travel/insurance/policy',
                              arguments: policy.id,
                            );
                          },
                        ),
                      );
                    }).toList(),
                    SizedBox(height: 24.h),
                  ],
                  
                  if (pendingPolicies.isNotEmpty) ...[
                    Text(
                      'Pending Policies',
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    SizedBox(height: 8.h),
                    ...pendingPolicies.map((policy) {
                      return Padding(
                        padding: EdgeInsets.only(bottom: 16.h),
                        child: InsurancePolicyCard(
                          policy: policy,
                          showDetails: false,
                          showStatus: true,
                          showProvider: true,
                          showCoverage: false,
                          showDates: false,
                          showPrice: true,
                          showActions: true,
                          onTap: () {
                            Navigator.pushNamed(
                              context,
                              '/travel/insurance/policy',
                              arguments: policy.id,
                            );
                          },
                          onViewDetails: () {
                            Navigator.pushNamed(
                              context,
                              '/travel/insurance/policy',
                              arguments: policy.id,
                            );
                          },
                        ),
                      );
                    }).toList(),
                    SizedBox(height: 24.h),
                  ],
                  
                  if (expiredPolicies.isNotEmpty || cancelledPolicies.isNotEmpty) ...[
                    Text(
                      'Past Policies',
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    SizedBox(height: 8.h),
                    ...[...expiredPolicies, ...cancelledPolicies].map((policy) {
                      return Padding(
                        padding: EdgeInsets.only(bottom: 16.h),
                        child: InsurancePolicyCard(
                          policy: policy,
                          showDetails: false,
                          showStatus: true,
                          showProvider: true,
                          showCoverage: false,
                          showDates: true,
                          showPrice: false,
                          showActions: true,
                          onTap: () {
                            Navigator.pushNamed(
                              context,
                              '/travel/insurance/policy',
                              arguments: policy.id,
                            );
                          },
                          onViewDetails: () {
                            Navigator.pushNamed(
                              context,
                              '/travel/insurance/policy',
                              arguments: policy.id,
                            );
                          },
                        ),
                      );
                    }).toList(),
                  ],
                ],
              );
            },
            loading: () => const Center(
              child: CircularProgressIndicator(),
            ),
            error: (error, stackTrace) => Center(
              child: Text(
                'Error loading policies: $error',
                style: TextStyle(color: theme.colorScheme.error),
              ),
            ),
          ),
        ],
      ),
    );
  }
  
  Widget _buildClaimsTab(ThemeData theme) {
    final claimsAsync = ref.watch(claimsProvider);
    
    return SingleChildScrollView(
      padding: EdgeInsets.all(16.r),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Claims section
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'My Claims',
                style: theme.textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              TextButton.icon(
                onPressed: () {
                  Navigator.pushNamed(context, '/travel/insurance/claim/new');
                },
                icon: const Icon(Icons.add),
                label: const Text('New Claim'),
              ),
            ],
          ),
          SizedBox(height: 16.h),
          
          claimsAsync.when(
            data: (claims) {
              if (claims.isEmpty) {
                return Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.description_outlined,
                        size: 64.r,
                        color: Colors.grey[400],
                      ),
                      SizedBox(height: 16.h),
                      Text(
                        'No claims found',
                        style: theme.textTheme.titleMedium,
                      ),
                      SizedBox(height: 8.h),
                      Text(
                        'You haven\'t filed any insurance claims yet.',
                        style: theme.textTheme.bodyMedium,
                        textAlign: TextAlign.center,
                      ),
                      SizedBox(height: 24.h),
                      ElevatedButton.icon(
                        onPressed: () {
                          Navigator.pushNamed(context, '/travel/insurance/claim/new');
                        },
                        icon: const Icon(Icons.add),
                        label: const Text('File a Claim'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: theme.colorScheme.primary,
                          foregroundColor: theme.colorScheme.onPrimary,
                        ),
                      ),
                    ],
                  ),
                );
              }
              
              // Group claims by status
              final activeClaims = claims.where((c) => 
                c.status == InsuranceClaimStatus.submitted || 
                c.status == InsuranceClaimStatus.underReview || 
                c.status == InsuranceClaimStatus.infoRequested
              ).toList();
              
              final resolvedClaims = claims.where((c) => 
                c.status == InsuranceClaimStatus.approved || 
                c.status == InsuranceClaimStatus.partiallyApproved || 
                c.status == InsuranceClaimStatus.denied || 
                c.status == InsuranceClaimStatus.paid
              ).toList();
              
              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  if (activeClaims.isNotEmpty) ...[
                    Text(
                      'Active Claims',
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    SizedBox(height: 8.h),
                    ...activeClaims.map((claim) {
                      return Padding(
                        padding: EdgeInsets.only(bottom: 16.h),
                        child: InsuranceClaimCard(
                          claim: claim,
                          showDetails: true,
                          showStatus: true,
                          showPolicy: true,
                          showDates: true,
                          showAmount: true,
                          showActions: true,
                          onTap: () {
                            Navigator.pushNamed(
                              context,
                              '/travel/insurance/claim',
                              arguments: claim.id,
                            );
                          },
                          onViewDetails: () {
                            Navigator.pushNamed(
                              context,
                              '/travel/insurance/claim',
                              arguments: claim.id,
                            );
                          },
                          onUpdate: claim.status == InsuranceClaimStatus.infoRequested
                              ? () {
                                  Navigator.pushNamed(
                                    context,
                                    '/travel/insurance/claim/update',
                                    arguments: claim.id,
                                  );
                                }
                              : null,
                        ),
                      );
                    }).toList(),
                    SizedBox(height: 24.h),
                  ],
                  
                  if (resolvedClaims.isNotEmpty) ...[
                    Text(
                      'Resolved Claims',
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    SizedBox(height: 8.h),
                    ...resolvedClaims.map((claim) {
                      return Padding(
                        padding: EdgeInsets.only(bottom: 16.h),
                        child: InsuranceClaimCard(
                          claim: claim,
                          showDetails: false,
                          showStatus: true,
                          showPolicy: true,
                          showDates: true,
                          showAmount: true,
                          showActions: true,
                          onTap: () {
                            Navigator.pushNamed(
                              context,
                              '/travel/insurance/claim',
                              arguments: claim.id,
                            );
                          },
                          onViewDetails: () {
                            Navigator.pushNamed(
                              context,
                              '/travel/insurance/claim',
                              arguments: claim.id,
                            );
                          },
                        ),
                      );
                    }).toList(),
                  ],
                ],
              );
            },
            loading: () => const Center(
              child: CircularProgressIndicator(),
            ),
            error: (error, stackTrace) => Center(
              child: Text(
                'Error loading claims: $error',
                style: TextStyle(color: theme.colorScheme.error),
              ),
            ),
          ),
        ],
      ),
    );
  }
  
  Widget _buildHeroSection(ThemeData theme) {
    return Container(
      width: double.infinity,
      height: 200.h,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16.r),
        gradient: LinearGradient(
          colors: [
            theme.colorScheme.primary,
            theme.colorScheme.tertiary,
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Stack(
        children: [
          // Background pattern
          Positioned.fill(
            child: ClipRRect(
              borderRadius: BorderRadius.circular(16.r),
              child: Opacity(
                opacity: 0.1,
                child: Image.network(
                  'https://via.placeholder.com/800x400?text=Travel+Insurance',
                  fit: BoxFit.cover,
                ),
              ),
            ),
          ),
          
          // Content
          Padding(
            padding: EdgeInsets.all(24.r),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  'Travel with Confidence',
                  style: theme.textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                SizedBox(height: 8.h),
                Text(
                  'Get comprehensive travel insurance for your next adventure',
                  style: theme.textTheme.titleMedium?.copyWith(
                    color: Colors.white.withOpacity(0.9),
                  ),
                ),
                SizedBox(height: 16.h),
                ElevatedButton(
                  onPressed: () {
                    Navigator.pushNamed(context, '/travel/insurance/search');
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.white,
                    foregroundColor: theme.colorScheme.primary,
                    padding: EdgeInsets.symmetric(
                      horizontal: 24.w,
                      vertical: 12.h,
                    ),
                  ),
                  child: const Text('Get a Quote'),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
  
  Widget _buildInsuranceTypesGrid(ThemeData theme) {
    final types = InsurancePolicyType.values;
    
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        crossAxisSpacing: 16.w,
        mainAxisSpacing: 16.h,
        childAspectRatio: 1.5,
      ),
      itemCount: types.length,
      itemBuilder: (context, index) {
        final type = types[index];
        
        return InkWell(
          onTap: () {
            Navigator.pushNamed(
              context,
              '/travel/insurance/search',
              arguments: {'type': type},
            );
          },
          borderRadius: BorderRadius.circular(12.r),
          child: Container(
            padding: EdgeInsets.all(16.r),
            decoration: BoxDecoration(
              color: theme.colorScheme.surfaceContainerLow,
              borderRadius: BorderRadius.circular(12.r),
              border: Border.all(
                color: theme.colorScheme.outlineVariant,
                width: 1,
              ),
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  type.icon,
                  size: 32.r,
                  color: theme.colorScheme.primary,
                ),
                SizedBox(height: 8.h),
                Text(
                  type.displayName,
                  style: theme.textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        );
      },
    );
  }
  
  Widget _buildWhyGetInsuranceSection(ThemeData theme) {
    final reasons = [
      {
        'icon': Icons.medical_services,
        'title': 'Medical Coverage',
        'description': 'Get coverage for medical emergencies abroad',
      },
      {
        'icon': Icons.cancel,
        'title': 'Trip Cancellation',
        'description': 'Recover costs if you need to cancel your trip',
      },
      {
        'icon': Icons.luggage,
        'title': 'Baggage Protection',
        'description': 'Coverage for lost, stolen, or damaged baggage',
      },
      {
        'icon': Icons.emergency,
        'title': 'Emergency Assistance',
        'description': '24/7 emergency assistance services',
      },
    ];
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Why Get Travel Insurance?',
          style: theme.textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        SizedBox(height: 16.h),
        ...reasons.map((reason) {
          return Padding(
            padding: EdgeInsets.only(bottom: 16.h),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  padding: EdgeInsets.all(12.r),
                  decoration: BoxDecoration(
                    color: theme.colorScheme.primaryContainer,
                    borderRadius: BorderRadius.circular(8.r),
                  ),
                  child: Icon(
                    reason['icon'] as IconData,
                    color: theme.colorScheme.primary,
                    size: 24.r,
                  ),
                ),
                SizedBox(width: 16.w),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        reason['title'] as String,
                        style: theme.textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      SizedBox(height: 4.h),
                      Text(
                        reason['description'] as String,
                        style: theme.textTheme.bodyMedium,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          );
        }).toList(),
      ],
    );
  }
}
