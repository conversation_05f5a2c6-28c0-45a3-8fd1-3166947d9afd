import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';
import 'package:culture_connect/models/travel/insurance/insurance.dart'
    hide InsuranceCoverageType, InsuranceClaimStatus;
import 'package:culture_connect/models/travel/insurance/insurance_coverage_type.dart';
import 'package:culture_connect/providers/travel/insurance/insurance_providers.dart';
import 'package:culture_connect/widgets/common/custom_app_bar.dart';
import 'package:culture_connect/widgets/common/loading_indicator.dart';
import 'package:culture_connect/widgets/common/error_view.dart';
import 'package:culture_connect/widgets/travel/insurance/insurance_policy_card.dart';

/// A screen for searching and filtering insurance policies
class InsuranceSearchScreen extends ConsumerStatefulWidget {
  /// Optional initial filter parameters
  final Map<String, dynamic>? initialFilters;

  /// Creates a new insurance search screen
  const InsuranceSearchScreen({
    super.key,
    this.initialFilters,
  });

  @override
  ConsumerState<InsuranceSearchScreen> createState() =>
      _InsuranceSearchScreenState();
}

class _InsuranceSearchScreenState extends ConsumerState<InsuranceSearchScreen> {
  // Search parameters
  late DateTime _startDate;
  late DateTime _endDate;
  late int _travelerCount;
  late List<String> _destinationCountries;
  late RangeValues _priceRange;
  late List<InsurancePolicyType> _selectedPolicyTypes;
  late List<InsuranceCoverageType> _selectedCoverageTypes;
  late List<String> _selectedProviderIds;

  // Filter visibility
  bool _showFilters = false;

  // Search results
  List<InsurancePolicy> _searchResults = [];
  bool _isSearching = false;
  String? _searchError;

  @override
  void initState() {
    super.initState();

    // Initialize search parameters with defaults or initial filters
    final now = DateTime.now();
    _startDate =
        widget.initialFilters?['startDate'] ?? now.add(const Duration(days: 7));
    _endDate =
        widget.initialFilters?['endDate'] ?? now.add(const Duration(days: 14));
    _travelerCount = widget.initialFilters?['travelerCount'] ?? 1;
    _destinationCountries =
        widget.initialFilters?['destinationCountries'] ?? <String>[];
    _priceRange =
        widget.initialFilters?['priceRange'] ?? const RangeValues(0, 500);

    // Initialize selected policy types
    if (widget.initialFilters?['type'] != null) {
      _selectedPolicyTypes = [
        widget.initialFilters!['type'] as InsurancePolicyType
      ];
    } else {
      _selectedPolicyTypes =
          widget.initialFilters?['policyTypes'] ?? <InsurancePolicyType>[];
    }

    // Initialize selected coverage types
    _selectedCoverageTypes =
        widget.initialFilters?['coverageTypes'] ?? <InsuranceCoverageType>[];

    // Initialize selected provider IDs
    _selectedProviderIds = widget.initialFilters?['providerIds'] ?? <String>[];

    // Perform initial search
    _performSearch();
  }

  Future<void> _performSearch() async {
    setState(() {
      _isSearching = true;
      _searchError = null;
    });

    try {
      // Get recommended policies
      final policies =
          await ref.read(insuranceServiceProvider).getRecommendedPolicies(
                destinationCountries: _destinationCountries,
                startDate: _startDate,
                endDate: _endDate,
                travelerCount: _travelerCount,
                desiredCoverageTypes: _selectedCoverageTypes.isNotEmpty
                    ? _selectedCoverageTypes
                    : null,
              );

      // Apply additional filters
      final filteredPolicies = policies.where((policy) {
        // Filter by price range
        if (policy.price < _priceRange.start ||
            policy.price > _priceRange.end) {
          return false;
        }

        // Filter by policy type
        if (_selectedPolicyTypes.isNotEmpty &&
            !_selectedPolicyTypes.contains(policy.type)) {
          return false;
        }

        // Filter by provider
        if (_selectedProviderIds.isNotEmpty &&
            !_selectedProviderIds.contains(policy.provider.id)) {
          return false;
        }

        return true;
      }).toList();

      setState(() {
        _searchResults = filteredPolicies;
        _isSearching = false;
      });
    } catch (e) {
      setState(() {
        _searchError = e.toString();
        _isSearching = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      appBar: CustomAppBar(
        title: 'Find Insurance',
        showBackButton: true,
        actions: [
          IconButton(
            icon:
                Icon(_showFilters ? Icons.filter_list_off : Icons.filter_list),
            onPressed: () {
              setState(() {
                _showFilters = !_showFilters;
              });
            },
          ),
        ],
      ),
      body: Column(
        children: [
          // Search form
          AnimatedContainer(
            duration: const Duration(milliseconds: 300),
            height: _showFilters ? null : 120.h,
            child: Card(
              margin: EdgeInsets.all(16.r),
              child: Padding(
                padding: EdgeInsets.all(16.r),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Basic search form (always visible)
                    Row(
                      children: [
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Travel Dates',
                                style: theme.textTheme.titleSmall?.copyWith(
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              SizedBox(height: 8.h),
                              InkWell(
                                onTap: () => _selectDateRange(context),
                                child: Container(
                                  padding: EdgeInsets.symmetric(
                                    horizontal: 12.w,
                                    vertical: 8.h,
                                  ),
                                  decoration: BoxDecoration(
                                    border: Border.all(
                                      color: theme.colorScheme.outline,
                                    ),
                                    borderRadius: BorderRadius.circular(4.r),
                                  ),
                                  child: Row(
                                    children: [
                                      Icon(
                                        Icons.calendar_today,
                                        size: 16.r,
                                        color: theme.colorScheme.primary,
                                      ),
                                      SizedBox(width: 8.w),
                                      Expanded(
                                        child: Text(
                                          '${DateFormat('MMM d').format(_startDate)} - ${DateFormat('MMM d, yyyy').format(_endDate)}',
                                          style: theme.textTheme.bodyMedium,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                        SizedBox(width: 16.w),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Travelers',
                                style: theme.textTheme.titleSmall?.copyWith(
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              SizedBox(height: 8.h),
                              Container(
                                padding: EdgeInsets.symmetric(
                                  horizontal: 12.w,
                                  vertical: 8.h,
                                ),
                                decoration: BoxDecoration(
                                  border: Border.all(
                                    color: theme.colorScheme.outline,
                                  ),
                                  borderRadius: BorderRadius.circular(4.r),
                                ),
                                child: Row(
                                  children: [
                                    Icon(
                                      Icons.people,
                                      size: 16.r,
                                      color: theme.colorScheme.primary,
                                    ),
                                    SizedBox(width: 8.w),
                                    Expanded(
                                      child: Text(
                                        '$_travelerCount ${_travelerCount == 1 ? 'traveler' : 'travelers'}',
                                        style: theme.textTheme.bodyMedium,
                                      ),
                                    ),
                                    IconButton(
                                      icon: const Icon(Icons.remove),
                                      padding: EdgeInsets.zero,
                                      constraints: const BoxConstraints(),
                                      onPressed: _travelerCount > 1
                                          ? () {
                                              setState(() {
                                                _travelerCount--;
                                              });
                                            }
                                          : null,
                                    ),
                                    SizedBox(width: 8.w),
                                    IconButton(
                                      icon: const Icon(Icons.add),
                                      padding: EdgeInsets.zero,
                                      constraints: const BoxConstraints(),
                                      onPressed: _travelerCount < 10
                                          ? () {
                                              setState(() {
                                                _travelerCount++;
                                              });
                                            }
                                          : null,
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),

                    // Advanced filters (only visible when _showFilters is true)
                    if (_showFilters) ...[
                      SizedBox(height: 16.h),
                      const Divider(),
                      SizedBox(height: 16.h),

                      // Destination countries
                      Text(
                        'Destination Countries',
                        style: theme.textTheme.titleSmall?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      SizedBox(height: 8.h),
                      Wrap(
                        spacing: 8.w,
                        runSpacing: 8.h,
                        children: [
                          // Add country chips here
                          ActionChip(
                            label: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Icon(
                                  Icons.add,
                                  size: 16.r,
                                ),
                                SizedBox(width: 4.w),
                                const Text('Add Country'),
                              ],
                            ),
                            onPressed: () => _selectCountry(context),
                          ),
                          ..._destinationCountries.map((country) {
                            return Chip(
                              label: Text(country),
                              onDeleted: () {
                                setState(() {
                                  _destinationCountries.remove(country);
                                });
                              },
                            );
                          }),
                        ],
                      ),

                      SizedBox(height: 16.h),

                      // Price range
                      Text(
                        'Price Range',
                        style: theme.textTheme.titleSmall?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      SizedBox(height: 8.h),
                      Row(
                        children: [
                          Text(
                            '\$${_priceRange.start.toInt()}',
                            style: theme.textTheme.bodyMedium,
                          ),
                          Expanded(
                            child: RangeSlider(
                              values: _priceRange,
                              min: 0,
                              max: 500,
                              divisions: 50,
                              labels: RangeLabels(
                                '\$${_priceRange.start.toInt()}',
                                '\$${_priceRange.end.toInt()}',
                              ),
                              onChanged: (values) {
                                setState(() {
                                  _priceRange = values;
                                });
                              },
                            ),
                          ),
                          Text(
                            '\$${_priceRange.end.toInt()}',
                            style: theme.textTheme.bodyMedium,
                          ),
                        ],
                      ),

                      SizedBox(height: 16.h),

                      // Policy types
                      Text(
                        'Policy Types',
                        style: theme.textTheme.titleSmall?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      SizedBox(height: 8.h),
                      Wrap(
                        spacing: 8.w,
                        runSpacing: 8.h,
                        children: InsurancePolicyType.values.map((type) {
                          final isSelected =
                              _selectedPolicyTypes.contains(type);
                          return FilterChip(
                            label: Text(type.displayName),
                            selected: isSelected,
                            onSelected: (selected) {
                              setState(() {
                                if (selected) {
                                  _selectedPolicyTypes.add(type);
                                } else {
                                  _selectedPolicyTypes.remove(type);
                                }
                              });
                            },
                          );
                        }).toList(),
                      ),

                      SizedBox(height: 16.h),

                      // Coverage types
                      Text(
                        'Coverage Types',
                        style: theme.textTheme.titleSmall?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      SizedBox(height: 8.h),
                      Wrap(
                        spacing: 8.w,
                        runSpacing: 8.h,
                        children: InsuranceCoverageType.values.map((type) {
                          final isSelected =
                              _selectedCoverageTypes.contains(type);
                          return FilterChip(
                            label: Text(type.displayName),
                            selected: isSelected,
                            onSelected: (selected) {
                              setState(() {
                                if (selected) {
                                  _selectedCoverageTypes.add(type);
                                } else {
                                  _selectedCoverageTypes.remove(type);
                                }
                              });
                            },
                          );
                        }).toList(),
                      ),

                      // Provider selection would go here, but we'll load it dynamically

                      SizedBox(height: 16.h),
                    ],

                    // Search button
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: _performSearch,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: theme.colorScheme.primary,
                          foregroundColor: theme.colorScheme.onPrimary,
                        ),
                        child: const Text('Search'),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),

          // Results
          Expanded(
            child: _isSearching
                ? const Center(
                    child: LoadingIndicator(),
                  )
                : _searchError != null
                    ? Center(
                        child: ErrorView(
                          error: _searchError!,
                          onRetry: _performSearch,
                        ),
                      )
                    : _searchResults.isEmpty
                        ? Center(
                            child: Text(
                              'No policies found matching your criteria',
                              style: theme.textTheme.titleMedium,
                            ),
                          )
                        : ListView.builder(
                            padding: EdgeInsets.all(16.r),
                            itemCount: _searchResults.length,
                            itemBuilder: (context, index) {
                              final policy = _searchResults[index];
                              return Padding(
                                padding: EdgeInsets.only(bottom: 16.h),
                                child: InsurancePolicyCard(
                                  policy: policy,
                                  showDetails: true,
                                  showStatus: false,
                                  showProvider: true,
                                  showCoverage: true,
                                  showDates: false,
                                  showPrice: true,
                                  showActions: true,
                                  onTap: () {
                                    Navigator.pushNamed(
                                      context,
                                      '/travel/insurance/policy',
                                      arguments: policy.id,
                                    );
                                  },
                                  onViewDetails: () {
                                    Navigator.pushNamed(
                                      context,
                                      '/travel/insurance/policy',
                                      arguments: policy.id,
                                    );
                                  },
                                  onPurchase: () {
                                    Navigator.pushNamed(
                                      context,
                                      '/travel/insurance/purchase',
                                      arguments: policy.id,
                                    );
                                  },
                                ),
                              );
                            },
                          ),
          ),
        ],
      ),
    );
  }

  Future<void> _selectDateRange(BuildContext context) async {
    final initialDateRange = DateTimeRange(
      start: _startDate,
      end: _endDate,
    );

    final newDateRange = await showDateRangePicker(
      context: context,
      initialDateRange: initialDateRange,
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365)),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: Theme.of(context).colorScheme.copyWith(
                  primary: Theme.of(context).colorScheme.primary,
                ),
          ),
          child: child!,
        );
      },
    );

    if (newDateRange != null) {
      setState(() {
        _startDate = newDateRange.start;
        _endDate = newDateRange.end;
      });
    }
  }

  Future<void> _selectCountry(BuildContext context) async {
    // In a real app, this would show a country picker
    // For now, we'll just add a few hardcoded countries
    final countries = [
      'United States',
      'Canada',
      'United Kingdom',
      'France',
      'Germany',
      'Italy',
      'Spain',
      'Japan',
      'Australia',
      'Brazil',
    ];

    final selectedCountry = await showDialog<String>(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('Select Country'),
          content: SizedBox(
            width: double.maxFinite,
            child: ListView.builder(
              shrinkWrap: true,
              itemCount: countries.length,
              itemBuilder: (context, index) {
                final country = countries[index];
                return ListTile(
                  title: Text(country),
                  onTap: () {
                    Navigator.of(context).pop(country);
                  },
                );
              },
            ),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: const Text('Cancel'),
            ),
          ],
        );
      },
    );

    if (selectedCountry != null &&
        !_destinationCountries.contains(selectedCountry)) {
      setState(() {
        _destinationCountries.add(selectedCountry);
      });
    }
  }
}
