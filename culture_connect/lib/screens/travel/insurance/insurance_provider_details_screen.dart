import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

// Package imports
import 'package:url_launcher/url_launcher.dart';

// Project imports
import 'package:culture_connect/models/travel/insurance/insurance.dart';
import 'package:culture_connect/providers/travel/insurance/insurance_providers.dart';
import 'package:culture_connect/widgets/common/custom_app_bar.dart';
import 'package:culture_connect/widgets/common/loading_indicator.dart';
import 'package:culture_connect/widgets/common/error_view.dart';
import 'package:culture_connect/widgets/travel/insurance/insurance_policy_card.dart';

/// A screen for displaying insurance provider details
class InsuranceProviderDetailsScreen extends ConsumerWidget {
  /// The ID of the provider to display
  final String providerId;

  /// Creates a new insurance provider details screen
  const InsuranceProviderDetailsScreen({
    super.key,
    required this.providerId,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final providerAsync = ref.watch(providerProvider(providerId));
    final availablePoliciesAsync = ref.watch(availablePoliciesProvider);

    return Scaffold(
      appBar: const CustomAppBar(
        title: 'Provider Details',
        showBackButton: true,
      ),
      body: providerAsync.when(
        data: (provider) {
          if (provider == null) {
            return Center(
              child: Text(
                'Provider not found',
                style: theme.textTheme.titleMedium,
              ),
            );
          }

          return SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Provider header
                _buildProviderHeader(context, theme, provider),

                // Provider details
                Padding(
                  padding: EdgeInsets.all(16.r),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Description
                      Text(
                        'About',
                        style: theme.textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      SizedBox(height: 8.h),
                      Text(
                        provider.description,
                        style: theme.textTheme.bodyMedium,
                      ),
                      SizedBox(height: 24.h),

                      // Contact information
                      Text(
                        'Contact Information',
                        style: theme.textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      SizedBox(height: 16.h),
                      _buildContactItem(
                        theme,
                        Icons.phone,
                        'Phone',
                        provider.phoneNumber,
                        () => _launchUrl('tel:${provider.phoneNumber}'),
                      ),
                      SizedBox(height: 16.h),
                      _buildContactItem(
                        theme,
                        Icons.email,
                        'Email',
                        provider.email,
                        () => _launchUrl('mailto:${provider.email}'),
                      ),
                      SizedBox(height: 16.h),
                      _buildContactItem(
                        theme,
                        Icons.language,
                        'Website',
                        provider.websiteUrl,
                        () => _launchUrl(provider.websiteUrl),
                      ),
                      SizedBox(height: 24.h),

                      // Countries
                      Text(
                        'Available Countries',
                        style: theme.textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      SizedBox(height: 16.h),
                      Wrap(
                        spacing: 8.w,
                        runSpacing: 8.h,
                        children: provider.countries.map((country) {
                          return Chip(
                            label: Text(country),
                            backgroundColor:
                                theme.colorScheme.surfaceContainerLow,
                          );
                        }).toList(),
                      ),
                      SizedBox(height: 24.h),

                      // Available policies
                      Text(
                        'Available Policies',
                        style: theme.textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      SizedBox(height: 16.h),

                      availablePoliciesAsync.when(
                        data: (policies) {
                          final providerPolicies = policies
                              .where(
                                  (policy) => policy.provider.id == provider.id)
                              .toList();

                          if (providerPolicies.isEmpty) {
                            return Center(
                              child: Text(
                                'No policies available from this provider',
                                style: theme.textTheme.bodyMedium,
                              ),
                            );
                          }

                          return Column(
                            children: providerPolicies.map((policy) {
                              return Padding(
                                padding: EdgeInsets.only(bottom: 16.h),
                                child: InsurancePolicyCard(
                                  policy: policy,
                                  showDetails: true,
                                  showStatus: false,
                                  showProvider: false,
                                  showCoverage: true,
                                  showDates: false,
                                  showPrice: true,
                                  showActions: true,
                                  onTap: () {
                                    Navigator.pushNamed(
                                      context,
                                      '/travel/insurance/policy',
                                      arguments: policy.id,
                                    );
                                  },
                                  onViewDetails: () {
                                    Navigator.pushNamed(
                                      context,
                                      '/travel/insurance/policy',
                                      arguments: policy.id,
                                    );
                                  },
                                  onPurchase: () {
                                    Navigator.pushNamed(
                                      context,
                                      '/travel/insurance/purchase',
                                      arguments: policy.id,
                                    );
                                  },
                                ),
                              );
                            }).toList(),
                          );
                        },
                        loading: () => const Center(
                          child: CircularProgressIndicator(),
                        ),
                        error: (error, stackTrace) => Center(
                          child: Text(
                            'Error loading policies: $error',
                            style: TextStyle(color: theme.colorScheme.error),
                          ),
                        ),
                      ),

                      SizedBox(height: 24.h),

                      // Reviews
                      Text(
                        'Reviews',
                        style: theme.textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      SizedBox(height: 16.h),
                      _buildReviewSummary(theme, provider),
                      SizedBox(height: 16.h),

                      // Sample reviews
                      _buildSampleReviews(theme),

                      SizedBox(height: 24.h),
                    ],
                  ),
                ),
              ],
            ),
          );
        },
        loading: () => const Center(
          child: LoadingIndicator(),
        ),
        error: (error, stackTrace) => Center(
          child: ErrorView(
            error: error.toString(),
            onRetry: () => ref.refresh(providerProvider(providerId)),
          ),
        ),
      ),
    );
  }

  Widget _buildProviderHeader(
      BuildContext context, ThemeData theme, InsuranceProvider provider) {
    return Container(
      width: double.infinity,
      color: theme.colorScheme.surfaceContainerHigh,
      padding: EdgeInsets.all(24.r),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          // Logo
          ClipRRect(
            borderRadius: BorderRadius.circular(16.r),
            child: Image.network(
              provider.logoUrl,
              width: 120.r,
              height: 120.r,
              fit: BoxFit.cover,
              errorBuilder: (context, error, stackTrace) {
                return Container(
                  width: 120.r,
                  height: 120.r,
                  color: provider.primaryColor.withAlpha(25), // 0.1 * 255 = 25
                  child: Icon(
                    Icons.business,
                    size: 64.r,
                    color: provider.primaryColor,
                  ),
                );
              },
            ),
          ),
          SizedBox(height: 16.h),

          // Name
          Text(
            provider.name,
            style: theme.textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 8.h),

          // Rating
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              ...List.generate(5, (index) {
                return Icon(
                  index < provider.rating.floor()
                      ? Icons.star
                      : index < provider.rating
                          ? Icons.star_half
                          : Icons.star_border,
                  color: Colors.amber,
                  size: 24.r,
                );
              }),
              SizedBox(width: 8.w),
              Text(
                '${provider.rating} (${provider.reviewCount} reviews)',
                style: theme.textTheme.bodyMedium,
              ),
            ],
          ),
          SizedBox(height: 16.h),

          // Tags
          Wrap(
            alignment: WrapAlignment.center,
            spacing: 8.w,
            runSpacing: 8.h,
            children: [
              if (provider.isPartner)
                Chip(
                  label: const Text('Partner'),
                  backgroundColor: theme.colorScheme.primaryContainer,
                  labelStyle: TextStyle(
                    color: theme.colorScheme.onPrimaryContainer,
                  ),
                ),
              if (provider.isFeatured)
                Chip(
                  label: const Text('Featured'),
                  backgroundColor: Colors.amber.withAlpha(51), // 0.2 * 255 = 51
                  labelStyle: TextStyle(
                    color: Colors.amber[800],
                  ),
                ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildContactItem(
    ThemeData theme,
    IconData icon,
    String label,
    String value,
    VoidCallback onTap,
  ) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8.r),
      child: Padding(
        padding: EdgeInsets.all(8.r),
        child: Row(
          children: [
            Container(
              padding: EdgeInsets.all(12.r),
              decoration: BoxDecoration(
                color: theme.colorScheme.primaryContainer,
                borderRadius: BorderRadius.circular(8.r),
              ),
              child: Icon(
                icon,
                color: theme.colorScheme.primary,
                size: 24.r,
              ),
            ),
            SizedBox(width: 16.w),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    label,
                    style: theme.textTheme.titleSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  SizedBox(height: 4.h),
                  Text(
                    value,
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: theme.colorScheme.primary,
                    ),
                  ),
                ],
              ),
            ),
            Icon(
              Icons.arrow_forward_ios,
              size: 16.r,
              color: theme.colorScheme.onSurfaceVariant,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildReviewSummary(ThemeData theme, InsuranceProvider provider) {
    return Row(
      children: [
        Container(
          padding: EdgeInsets.all(16.r),
          decoration: BoxDecoration(
            color: theme.colorScheme.surfaceContainerLow,
            borderRadius: BorderRadius.circular(8.r),
          ),
          child: Column(
            children: [
              Text(
                provider.rating.toString(),
                style: theme.textTheme.headlineMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: theme.colorScheme.primary,
                ),
              ),
              SizedBox(height: 4.h),
              Row(
                children: List.generate(5, (index) {
                  return Icon(
                    index < provider.rating.floor()
                        ? Icons.star
                        : index < provider.rating
                            ? Icons.star_half
                            : Icons.star_border,
                    color: Colors.amber,
                    size: 16.r,
                  );
                }),
              ),
              SizedBox(height: 4.h),
              Text(
                '${provider.reviewCount} reviews',
                style: theme.textTheme.bodySmall,
              ),
            ],
          ),
        ),
        SizedBox(width: 16.w),
        Expanded(
          child: Column(
            children: [
              _buildRatingBar(theme, 5, 0.7),
              SizedBox(height: 8.h),
              _buildRatingBar(theme, 4, 0.2),
              SizedBox(height: 8.h),
              _buildRatingBar(theme, 3, 0.05),
              SizedBox(height: 8.h),
              _buildRatingBar(theme, 2, 0.03),
              SizedBox(height: 8.h),
              _buildRatingBar(theme, 1, 0.02),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildRatingBar(ThemeData theme, int rating, double percentage) {
    return Row(
      children: [
        Text(
          '$rating',
          style: theme.textTheme.bodySmall,
        ),
        SizedBox(width: 4.w),
        Icon(
          Icons.star,
          size: 12.r,
          color: Colors.amber,
        ),
        SizedBox(width: 8.w),
        Expanded(
          child: ClipRRect(
            borderRadius: BorderRadius.circular(2.r),
            child: LinearProgressIndicator(
              value: percentage,
              backgroundColor: theme.colorScheme.surfaceContainerLowest,
              valueColor: AlwaysStoppedAnimation<Color>(
                rating >= 4
                    ? Colors.green
                    : rating >= 3
                        ? Colors.amber
                        : Colors.red,
              ),
              minHeight: 8.h,
            ),
          ),
        ),
        SizedBox(width: 8.w),
        Text(
          '${(percentage * 100).toInt()}%',
          style: theme.textTheme.bodySmall,
        ),
      ],
    );
  }

  Widget _buildSampleReviews(ThemeData theme) {
    // In a real app, these would come from an API
    final sampleReviews = [
      {
        'name': 'John D.',
        'rating': 5,
        'date': '2 months ago',
        'comment':
            'Excellent service! The claim process was quick and hassle-free. Highly recommended.',
      },
      {
        'name': 'Sarah M.',
        'rating': 4,
        'date': '3 months ago',
        'comment':
            'Good coverage for the price. Customer service was helpful when I needed assistance.',
      },
      {
        'name': 'Robert L.',
        'rating': 5,
        'date': '1 month ago',
        'comment':
            'Saved me a lot of trouble when my flight was cancelled. Will definitely use again.',
      },
    ];

    return Column(
      children: sampleReviews.map((review) {
        return Padding(
          padding: EdgeInsets.only(bottom: 16.h),
          child: Card(
            child: Padding(
              padding: EdgeInsets.all(16.r),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      CircleAvatar(
                        backgroundColor: theme.colorScheme.primary,
                        child: Text(
                          (review['name'] as String).substring(0, 1),
                          style: TextStyle(
                            color: theme.colorScheme.onPrimary,
                          ),
                        ),
                      ),
                      SizedBox(width: 8.w),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              review['name'] as String,
                              style: theme.textTheme.titleSmall?.copyWith(
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            Text(
                              review['date'] as String,
                              style: theme.textTheme.bodySmall,
                            ),
                          ],
                        ),
                      ),
                      Row(
                        children: List.generate(5, (index) {
                          return Icon(
                            index < (review['rating'] as int)
                                ? Icons.star
                                : Icons.star_border,
                            color: Colors.amber,
                            size: 16.r,
                          );
                        }),
                      ),
                    ],
                  ),
                  SizedBox(height: 8.h),
                  Text(
                    review['comment'] as String,
                    style: theme.textTheme.bodyMedium,
                  ),
                ],
              ),
            ),
          ),
        );
      }).toList(),
    );
  }

  Future<void> _launchUrl(String url) async {
    final Uri uri = Uri.parse(url);
    try {
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri);
      } else {
        debugPrint('Could not launch $url');
      }
    } catch (e) {
      debugPrint('Error launching URL: $e');
    }
  }
}
