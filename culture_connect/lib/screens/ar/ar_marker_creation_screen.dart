import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:image_picker/image_picker.dart';
import 'package:path/path.dart' as path;
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:location/location.dart';

import 'package:culture_connect/models/ar/ar_content_marker.dart';
import 'package:culture_connect/providers/ar/ar_content_providers.dart';
import 'package:culture_connect/widgets/common/custom_app_bar.dart';
import 'package:culture_connect/widgets/common/loading_indicator.dart';
import 'package:culture_connect/widgets/common/error_view.dart';
import 'package:culture_connect/screens/ar/ar_content_preview_screen.dart';
import 'package:culture_connect/utils/validation_utils.dart';

/// A screen for creating AR markers at specific locations
class ARMarkerCreationScreen extends ConsumerStatefulWidget {
  /// The initial location name (optional)
  final String? initialLocationName;

  /// The initial coordinates (optional)
  final LatLng? initialCoordinates;

  /// Creates a new AR marker creation screen
  const ARMarkerCreationScreen({
    super.key,
    this.initialLocationName,
    this.initialCoordinates,
  });

  @override
  ConsumerState<ARMarkerCreationScreen> createState() =>
      _ARMarkerCreationScreenState();
}

class _ARMarkerCreationScreenState
    extends ConsumerState<ARMarkerCreationScreen> {
  final _formKey = GlobalKey<FormState>();

  // Form fields
  final _titleController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _locationController = TextEditingController();
  ARContentType _selectedContentType = ARContentType.model;
  File? _markerImageFile;
  File? _contentFile;
  LatLng? _coordinates;

  // Map controller
  GoogleMapController? _mapController;
  Set<Marker> _markers = {};

  // Loading state
  bool _isLoading = false;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();

    // Initialize with provided values
    if (widget.initialLocationName != null) {
      _locationController.text = widget.initialLocationName!;
    }

    if (widget.initialCoordinates != null) {
      _coordinates = widget.initialCoordinates;
      _updateMarkers();
    } else {
      _getCurrentLocation();
    }
  }

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    _locationController.dispose();
    _mapController?.dispose();
    super.dispose();
  }

  /// Get the current location
  Future<void> _getCurrentLocation() async {
    try {
      final location = Location();

      bool serviceEnabled = await location.serviceEnabled();
      if (!serviceEnabled) {
        serviceEnabled = await location.requestService();
        if (!serviceEnabled) {
          return;
        }
      }

      PermissionStatus permissionStatus = await location.hasPermission();
      if (permissionStatus == PermissionStatus.denied) {
        permissionStatus = await location.requestPermission();
        if (permissionStatus != PermissionStatus.granted) {
          return;
        }
      }

      final currentLocation = await location.getLocation();

      if (mounted) {
        setState(() {
          _coordinates = LatLng(
            currentLocation.latitude!,
            currentLocation.longitude!,
          );
          _updateMarkers();
        });
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Error getting current location: $e';
      });
    }
  }

  /// Update map markers
  void _updateMarkers() {
    if (_coordinates == null) return;

    setState(() {
      _markers = {
        Marker(
          markerId: const MarkerId('ar_marker'),
          position: _coordinates!,
          draggable: true,
          onDragEnd: (newPosition) {
            setState(() {
              _coordinates = newPosition;
            });
          },
        ),
      };
    });
  }

  /// Handle map creation
  void _onMapCreated(GoogleMapController controller) {
    _mapController = controller;
  }

  /// Handle map tap
  void _onMapTap(LatLng position) {
    setState(() {
      _coordinates = position;
      _updateMarkers();
    });
  }

  /// Pick a marker image
  Future<void> _pickMarkerImage() async {
    try {
      final picker = ImagePicker();
      final pickedFile = await picker.pickImage(source: ImageSource.gallery);

      if (pickedFile != null) {
        setState(() {
          _markerImageFile = File(pickedFile.path);
        });
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Error picking marker image: $e';
      });
    }
  }

  /// Pick a content file
  Future<void> _pickContentFile() async {
    try {
      final picker = ImagePicker();
      final pickedFile = await picker.pickImage(source: ImageSource.gallery);

      if (pickedFile != null) {
        setState(() {
          _contentFile = File(pickedFile.path);
        });
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Error picking content file: $e';
      });
    }
  }

  /// Create AR marker
  Future<void> _createARMarker() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    if (_coordinates == null) {
      setState(() {
        _errorMessage = 'Please select a location on the map';
      });
      return;
    }

    if (_markerImageFile == null) {
      setState(() {
        _errorMessage = 'Please select a marker image';
      });
      return;
    }

    if (_contentFile == null) {
      setState(() {
        _errorMessage = 'Please select a content file';
      });
      return;
    }

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      // In a real app, you would upload the files to a server and get URLs
      // For this example, we'll use fake URLs based on the file names
      final markerImageUrl =
          'https://example.com/ar_markers/${path.basename(_markerImageFile!.path)}';
      final contentUrl =
          'https://example.com/ar_content/${path.basename(_contentFile!.path)}';

      // Create the AR content marker
      await ref
          .read(currentARContentMarkerProvider.notifier)
          .createARContentMarker(
        title: _titleController.text,
        description: _descriptionController.text,
        contentType: _selectedContentType,
        contentUrl: contentUrl,
        thumbnailUrl: markerImageUrl,
        contentSize: _contentFile?.lengthSync(),
        location: _locationController.text,
        coordinates: {
          'latitude': _coordinates!.latitude,
          'longitude': _coordinates!.longitude,
        },
      );

      if (mounted) {
        // Show success message
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('AR marker created successfully'),
            backgroundColor: Colors.green,
          ),
        );

        // Navigate to the preview screen
        final arContentMarker = ref.read(currentARContentMarkerProvider).value;
        if (arContentMarker != null) {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => ARContentPreviewScreen(
                arContentId: arContentMarker.id,
              ),
            ),
          );
        } else {
          Navigator.pop(context);
        }
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Error creating AR marker: $e';
      });
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const CustomAppBar(
        title: 'Create AR Marker',
        showBackButton: true,
      ),
      body: _isLoading
          ? const Center(child: LoadingIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Error message
                    if (_errorMessage != null)
                      Padding(
                        padding: const EdgeInsets.only(bottom: 16.0),
                        child: ErrorView(
                          error: _errorMessage!,
                          onRetry: () {
                            setState(() {
                              _errorMessage = null;
                            });
                          },
                        ),
                      ),

                    // Title
                    TextFormField(
                      controller: _titleController,
                      decoration: const InputDecoration(
                        labelText: 'Title',
                        hintText: 'Enter a title for your AR marker',
                      ),
                      validator: ValidationUtils.validateRequired,
                    ),

                    const SizedBox(height: 16.0),

                    // Description
                    TextFormField(
                      controller: _descriptionController,
                      decoration: const InputDecoration(
                        labelText: 'Description',
                        hintText: 'Enter a description for your AR marker',
                      ),
                      maxLines: 3,
                    ),

                    const SizedBox(height: 16.0),

                    // Location
                    TextFormField(
                      controller: _locationController,
                      decoration: const InputDecoration(
                        labelText: 'Location Name',
                        hintText: 'Enter a name for this location',
                      ),
                      validator: ValidationUtils.validateRequired,
                    ),

                    const SizedBox(height: 16.0),

                    // Content type
                    DropdownButtonFormField<ARContentType>(
                      value: _selectedContentType,
                      decoration: const InputDecoration(
                        labelText: 'Content Type',
                      ),
                      items: ARContentType.values.map((type) {
                        return DropdownMenuItem<ARContentType>(
                          value: type,
                          child: Text(type.displayName),
                        );
                      }).toList(),
                      onChanged: (value) {
                        setState(() {
                          _selectedContentType = value!;
                        });
                      },
                    ),

                    const SizedBox(height: 24.0),

                    // Map
                    Text(
                      'Select Location',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    const SizedBox(height: 8.0),
                    Container(
                      height: 200.0,
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.grey),
                        borderRadius: BorderRadius.circular(8.0),
                      ),
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(8.0),
                        child: _coordinates == null
                            ? const Center(child: LoadingIndicator())
                            : GoogleMap(
                                onMapCreated: _onMapCreated,
                                initialCameraPosition: CameraPosition(
                                  target: _coordinates!,
                                  zoom: 15,
                                ),
                                markers: _markers,
                                onTap: _onMapTap,
                                myLocationEnabled: true,
                                myLocationButtonEnabled: true,
                              ),
                      ),
                    ),

                    const SizedBox(height: 24.0),

                    // Marker image
                    Text(
                      'Marker Image',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    const SizedBox(height: 8.0),
                    _markerImageFile != null
                        ? _buildImagePreview(
                            _markerImageFile!, _pickMarkerImage)
                        : _buildImagePicker(
                            'Select a marker image', _pickMarkerImage),

                    const SizedBox(height: 24.0),

                    // Content file
                    Text(
                      'Content File',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    const SizedBox(height: 8.0),
                    _contentFile != null
                        ? _buildImagePreview(_contentFile!, _pickContentFile)
                        : _buildImagePicker(
                            'Select a content file', _pickContentFile),

                    const SizedBox(height: 32.0),

                    // Create button
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: _createARMarker,
                        style: ElevatedButton.styleFrom(
                          padding: const EdgeInsets.symmetric(vertical: 16.0),
                        ),
                        child: const Text('Create AR Marker'),
                      ),
                    ),
                  ],
                ),
              ),
            ),
    );
  }

  /// Build an image picker
  Widget _buildImagePicker(String label, VoidCallback onTap) {
    return InkWell(
      onTap: onTap,
      child: Container(
        width: double.infinity,
        height: 120.0,
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey),
          borderRadius: BorderRadius.circular(8.0),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.add_photo_alternate,
              size: 48.0,
              color: Colors.grey,
            ),
            const SizedBox(height: 8.0),
            Text(
              label,
              style: Theme.of(context)
                  .textTheme
                  .bodyMedium
                  ?.copyWith(color: Colors.grey),
            ),
          ],
        ),
      ),
    );
  }

  /// Build an image preview
  Widget _buildImagePreview(File file, VoidCallback onTap) {
    return Stack(
      children: [
        Container(
          width: double.infinity,
          height: 200.0,
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey),
            borderRadius: BorderRadius.circular(8.0),
            image: DecorationImage(
              image: FileImage(file),
              fit: BoxFit.cover,
            ),
          ),
        ),
        Positioned(
          top: 8.0,
          right: 8.0,
          child: IconButton(
            icon: const Icon(Icons.edit),
            onPressed: onTap,
            color: Colors.white,
            tooltip: 'Change image',
          ),
        ),
      ],
    );
  }
}
