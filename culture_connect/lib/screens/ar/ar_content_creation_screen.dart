import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:image_picker/image_picker.dart';
import 'package:file_picker/file_picker.dart';
import 'package:path/path.dart' as path;

import 'package:culture_connect/models/ar/ar_content_marker.dart';
import 'package:culture_connect/providers/ar/ar_content_providers.dart';
import 'package:culture_connect/widgets/common/custom_app_bar.dart';
import 'package:culture_connect/widgets/common/loading_indicator.dart';
import 'package:culture_connect/widgets/common/error_view.dart';
import 'package:culture_connect/screens/ar/ar_content_preview_screen.dart';
import 'package:culture_connect/utils/validation_utils.dart';

/// A screen for creating AR content
class ARContentCreationScreen extends ConsumerStatefulWidget {
  /// The location name (optional)
  final String? location;

  /// The coordinates (optional)
  final Map<String, double>? coordinates;

  /// Creates a new AR content creation screen
  const ARContentCreationScreen({
    super.key,
    this.location,
    this.coordinates,
  });

  @override
  ConsumerState<ARContentCreationScreen> createState() =>
      _ARContentCreationScreenState();
}

class _ARContentCreationScreenState
    extends ConsumerState<ARContentCreationScreen> {
  final _formKey = GlobalKey<FormState>();

  // Form fields
  final _titleController = TextEditingController();
  final _descriptionController = TextEditingController();
  ARContentType _selectedContentType = ARContentType.model;
  File? _contentFile;
  File? _thumbnailFile;

  // Loading state
  bool _isLoading = false;
  String? _errorMessage;

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  /// Pick a content file
  Future<void> _pickContentFile() async {
    try {
      FilePickerResult? result;

      switch (_selectedContentType) {
        case ARContentType.model:
          result = await FilePicker.platform.pickFiles(
            type: FileType.custom,
            allowedExtensions: ['glb', 'gltf', 'obj', 'fbx'],
          );
          break;
        case ARContentType.image:
          final picker = ImagePicker();
          final pickedFile =
              await picker.pickImage(source: ImageSource.gallery);
          if (pickedFile != null) {
            setState(() {
              _contentFile = File(pickedFile.path);
            });
          }
          return;
        case ARContentType.video:
          final picker = ImagePicker();
          final pickedFile =
              await picker.pickVideo(source: ImageSource.gallery);
          if (pickedFile != null) {
            setState(() {
              _contentFile = File(pickedFile.path);
            });
          }
          return;
        case ARContentType.audio:
          result = await FilePicker.platform.pickFiles(
            type: FileType.audio,
          );
          break;
        case ARContentType.text:
          // No file needed for text
          return;
        case ARContentType.interactive:
          result = await FilePicker.platform.pickFiles(
            type: FileType.custom,
            allowedExtensions: ['zip', 'glb', 'gltf'],
          );
          break;
      }

      if (result != null && result.files.isNotEmpty) {
        final filePath = result.files.first.path;
        if (filePath != null) {
          setState(() {
            _contentFile = File(filePath);
          });
        }
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Error picking file: $e';
      });
    }
  }

  /// Pick a thumbnail image
  Future<void> _pickThumbnailImage() async {
    try {
      final picker = ImagePicker();
      final pickedFile = await picker.pickImage(source: ImageSource.gallery);

      if (pickedFile != null) {
        setState(() {
          _thumbnailFile = File(pickedFile.path);
        });
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Error picking thumbnail: $e';
      });
    }
  }

  /// Create AR content
  Future<void> _createARContent() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    if (_contentFile == null && _selectedContentType != ARContentType.text) {
      setState(() {
        _errorMessage = 'Please select a content file';
      });
      return;
    }

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      // In a real app, you would upload the files to a server and get URLs
      // For this example, we'll use fake URLs based on the file names
      final contentUrl = _contentFile != null
          ? 'https://example.com/ar_content/${path.basename(_contentFile!.path)}'
          : 'https://example.com/ar_content/text_content_${DateTime.now().millisecondsSinceEpoch}.txt';

      final thumbnailUrl = _thumbnailFile != null
          ? 'https://example.com/ar_thumbnails/${path.basename(_thumbnailFile!.path)}'
          : null;

      // Create the AR content marker
      await ref
          .read(currentARContentMarkerProvider.notifier)
          .createARContentMarker(
            title: _titleController.text,
            description: _descriptionController.text,
            contentType: _selectedContentType,
            contentUrl: contentUrl,
            thumbnailUrl: thumbnailUrl,
            contentSize: _contentFile?.lengthSync(),
            location: widget.location,
            coordinates: widget.coordinates,
          );

      if (mounted) {
        // Show success message
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('AR content created successfully'),
            backgroundColor: Colors.green,
          ),
        );

        // Navigate to the preview screen
        final arContentMarker = ref.read(currentARContentMarkerProvider).value;
        if (arContentMarker != null) {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => ARContentPreviewScreen(
                arContentId: arContentMarker.id,
              ),
            ),
          );
        } else {
          Navigator.pop(context);
        }
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Error creating AR content: $e';
      });
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const CustomAppBar(
        title: 'Create AR Content',
        showBackButton: true,
      ),
      body: _isLoading
          ? const Center(child: LoadingIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Error message
                    if (_errorMessage != null)
                      Padding(
                        padding: const EdgeInsets.only(bottom: 16.0),
                        child: ErrorView(
                          error: _errorMessage!,
                          onRetry: () {
                            setState(() {
                              _errorMessage = null;
                            });
                          },
                        ),
                      ),

                    // Title
                    TextFormField(
                      controller: _titleController,
                      decoration: const InputDecoration(
                        labelText: 'Title',
                        hintText: 'Enter a title for your AR content',
                      ),
                      validator: ValidationUtils.validateRequired,
                    ),

                    const SizedBox(height: 16.0),

                    // Description
                    TextFormField(
                      controller: _descriptionController,
                      decoration: const InputDecoration(
                        labelText: 'Description',
                        hintText: 'Enter a description for your AR content',
                      ),
                      maxLines: 3,
                    ),

                    const SizedBox(height: 16.0),

                    // Content type
                    DropdownButtonFormField<ARContentType>(
                      value: _selectedContentType,
                      decoration: const InputDecoration(
                        labelText: 'Content Type',
                      ),
                      items: ARContentType.values.map((type) {
                        return DropdownMenuItem<ARContentType>(
                          value: type,
                          child: Text(type.displayName),
                        );
                      }).toList(),
                      onChanged: (value) {
                        setState(() {
                          _selectedContentType = value!;
                          _contentFile = null;
                        });
                      },
                    ),

                    const SizedBox(height: 24.0),

                    // Content file
                    if (_selectedContentType != ARContentType.text)
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Content File',
                            style: Theme.of(context).textTheme.titleMedium,
                          ),
                          const SizedBox(height: 8.0),
                          _contentFile != null
                              ? _buildFilePreview(_contentFile!)
                              : _buildFilePicker(),
                        ],
                      ),

                    const SizedBox(height: 24.0),

                    // Thumbnail
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Thumbnail (Optional)',
                          style: Theme.of(context).textTheme.titleMedium,
                        ),
                        const SizedBox(height: 8.0),
                        _thumbnailFile != null
                            ? _buildThumbnailPreview()
                            : _buildThumbnailPicker(),
                      ],
                    ),

                    const SizedBox(height: 32.0),

                    // Create button
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: _createARContent,
                        style: ElevatedButton.styleFrom(
                          padding: const EdgeInsets.symmetric(vertical: 16.0),
                        ),
                        child: const Text('Create AR Content'),
                      ),
                    ),
                  ],
                ),
              ),
            ),
    );
  }

  /// Build a file picker button
  Widget _buildFilePicker() {
    return InkWell(
      onTap: _pickContentFile,
      child: Container(
        width: double.infinity,
        height: 120.0,
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey),
          borderRadius: BorderRadius.circular(8.0),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.upload_file,
              size: 48.0,
              color: Colors.grey,
            ),
            const SizedBox(height: 8.0),
            Text(
              'Tap to select a file',
              style: Theme.of(context)
                  .textTheme
                  .bodyMedium
                  ?.copyWith(color: Colors.grey),
            ),
          ],
        ),
      ),
    );
  }

  /// Build a file preview
  Widget _buildFilePreview(File file) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey),
        borderRadius: BorderRadius.circular(8.0),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            path.basename(file.path),
            style: Theme.of(context).textTheme.bodyLarge,
          ),
          const SizedBox(height: 8.0),
          Text(
            '${(file.lengthSync() / 1024).toStringAsFixed(2)} KB',
            style: Theme.of(context)
                .textTheme
                .bodyMedium
                ?.copyWith(color: Colors.grey),
          ),
          const SizedBox(height: 16.0),
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              TextButton(
                onPressed: _pickContentFile,
                child: const Text('Change'),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// Build a thumbnail picker
  Widget _buildThumbnailPicker() {
    return InkWell(
      onTap: _pickThumbnailImage,
      child: Container(
        width: double.infinity,
        height: 120.0,
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey),
          borderRadius: BorderRadius.circular(8.0),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.image,
              size: 48.0,
              color: Colors.grey,
            ),
            const SizedBox(height: 8.0),
            Text(
              'Tap to select a thumbnail',
              style: Theme.of(context)
                  .textTheme
                  .bodyMedium
                  ?.copyWith(color: Colors.grey),
            ),
          ],
        ),
      ),
    );
  }

  /// Build a thumbnail preview
  Widget _buildThumbnailPreview() {
    return Stack(
      children: [
        Container(
          width: double.infinity,
          height: 200.0,
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey),
            borderRadius: BorderRadius.circular(8.0),
            image: DecorationImage(
              image: FileImage(_thumbnailFile!),
              fit: BoxFit.cover,
            ),
          ),
        ),
        Positioned(
          top: 8.0,
          right: 8.0,
          child: IconButton(
            icon: const Icon(Icons.edit),
            onPressed: _pickThumbnailImage,
            color: Colors.white,
            tooltip: 'Change thumbnail',
          ),
        ),
      ],
    );
  }
}
