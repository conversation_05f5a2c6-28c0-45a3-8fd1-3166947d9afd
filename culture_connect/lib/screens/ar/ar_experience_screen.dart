import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:vector_math/vector_math_64.dart' hide Colors;
import 'package:arcore_flutter_plugin/arcore_flutter_plugin.dart';

import 'package:culture_connect/models/experience.dart';
import 'package:culture_connect/models/ar/ar_content_marker.dart' as ar_marker;
import 'package:culture_connect/providers/ar_experience_provider.dart';
import 'package:culture_connect/widgets/common/custom_app_bar.dart';
import 'package:culture_connect/widgets/ar/ar_controls.dart';
import 'package:culture_connect/widgets/ar/ar_info_overlay.dart';

/// A screen for viewing AR experiences
class ARExperienceScreen extends ConsumerStatefulWidget {
  /// The experience to view
  final Experience experience;

  /// Creates a new AR experience screen
  const ARExperienceScreen({
    super.key,
    required this.experience,
  });

  @override
  ConsumerState<ARExperienceScreen> createState() => _ARExperienceScreenState();
}

class _ARExperienceScreenState extends ConsumerState<ARExperienceScreen>
    with WidgetsBindingObserver {
  ArCoreController? _arCoreController;
  bool _hasPlacedModel = false;
  bool _showPlacementInstructions = true;
  bool _showInfoOverlay = false;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    _initializeAR();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (state == AppLifecycleState.resumed) {
      _initializeAR();
    } else if (state == AppLifecycleState.paused) {
      _disposeAR();
    }
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _disposeAR();
    super.dispose();
  }

  /// Initialize AR
  Future<void> _initializeAR() async {
    final arLoadingNotifier = ref.read(arExperienceLoadingProvider.notifier);
    await arLoadingNotifier.initialize();
    await arLoadingNotifier.loadExperience(widget.experience);
  }

  /// Handle AR core view creation
  void _onArCoreViewCreated(ArCoreController controller) {
    _arCoreController = controller;

    final arService = ref.read(arExperienceServiceProvider);
    arService.onArCoreViewCreated(controller);

    // Set up plane tap handler
    _arCoreController!.onPlaneTap = _handlePlaneTap;
  }

  /// Handle plane tap
  void _handlePlaneTap(List<ArCoreHitTestResult> hits) {
    if (hits.isEmpty || _hasPlacedModel) return;

    final hit = hits.first;
    final position = hit.pose.translation;

    _placeModel(Vector3(position.x, position.y, position.z));

    setState(() {
      _hasPlacedModel = true;
      _showPlacementInstructions = false;
    });
  }

  /// Place the AR model
  Future<void> _placeModel(Vector3 position) async {
    final arService = ref.read(arExperienceServiceProvider);
    await arService.placeARModel(widget.experience, position);

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('${widget.experience.title} placed in AR'),
          backgroundColor: Colors.green,
          duration: const Duration(seconds: 2),
        ),
      );
    }
  }

  /// Reset the AR placement
  void _resetPlacement() {
    if (_arCoreController == null) return;

    _arCoreController!
        .removeNode(nodeName: 'experience_${widget.experience.id}');

    setState(() {
      _hasPlacedModel = false;
      _showPlacementInstructions = true;
    });
  }

  /// Dispose AR resources
  void _disposeAR() {
    _arCoreController?.dispose();
    _arCoreController = null;
  }

  /// Build the AR view
  Widget _buildARView() {
    return ArCoreView(
      onArCoreViewCreated: _onArCoreViewCreated,
      enableTapRecognizer: true,
    );
  }

  /// Build the placement instructions
  Widget _buildPlacementInstructions() {
    if (!_showPlacementInstructions) {
      return const SizedBox.shrink();
    }

    return Container(
      padding: const EdgeInsets.all(16.0),
      margin: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: Colors.black.withAlpha(150),
        borderRadius: BorderRadius.circular(8.0),
      ),
      child: const Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.touch_app,
            color: Colors.white,
            size: 32.0,
          ),
          SizedBox(height: 8.0),
          Text(
            'Tap on a surface to place the experience',
            style: TextStyle(
              color: Colors.white,
              fontSize: 16.0,
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// Build the AR controls
  Widget _buildARControls() {
    return ARControls(
      onInfoToggle: () {
        setState(() {
          _showInfoOverlay = !_showInfoOverlay;
        });
      },
      onShare: () {
        // Share AR experience
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Sharing not implemented yet'),
          ),
        );
      },
      onDownload: _hasPlacedModel ? _resetPlacement : null,
      isAvailableOffline: false, // This would come from the experience
    );
  }

  /// Build the AR info overlay
  Widget _buildARInfoOverlay() {
    if (!_showInfoOverlay) {
      return const SizedBox.shrink();
    }

    return ARInfoOverlay(
      title: widget.experience.title,
      description: widget.experience.description,
      // This would be the actual content type from the experience
      contentType: ar_marker.ARContentType.model,
      onClose: () {
        setState(() {
          _showInfoOverlay = false;
        });
      },
    );
  }

  /// Build the body based on loading state
  Widget _buildBody(ARExperienceLoadingState state) {
    if (state.error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              size: 64.0,
              color: Colors.red,
            ),
            const SizedBox(height: 16.0),
            Text(
              'Error: ${state.error}',
              style: const TextStyle(fontSize: 16.0),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16.0),
            ElevatedButton(
              onPressed: _initializeAR,
              child: const Text('Retry'),
            ),
          ],
        ),
      );
    }

    if (state.isLoading) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const CircularProgressIndicator(),
            const SizedBox(height: 16.0),
            const Text(
              'Loading AR Experience...',
              style: TextStyle(
                fontSize: 16.0,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8.0),
            LinearProgressIndicator(
              value: state.progress,
              backgroundColor: Colors.grey.withAlpha(50),
              valueColor:
                  AlwaysStoppedAnimation<Color>(Theme.of(context).primaryColor),
            ),
            const SizedBox(height: 8.0),
            Text(
              '${(state.progress * 100).toInt()}%',
              style: const TextStyle(
                fontSize: 14.0,
                color: Colors.grey,
              ),
            ),
          ],
        ),
      );
    }

    return Stack(
      children: [
        // AR View
        _buildARView(),

        // Placement instructions
        Center(
          child: _buildPlacementInstructions(),
        ),

        // AR controls
        Positioned(
          bottom: 16.0,
          left: 0,
          right: 0,
          child: _buildARControls(),
        ),

        // AR info overlay
        _buildARInfoOverlay(),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    final arLoadingState = ref.watch(arExperienceLoadingProvider);

    return Scaffold(
      extendBodyBehindAppBar: true,
      appBar: const CustomAppBar(
        title: 'AR Experience',
        showBackButton: true,
        backgroundColor: Colors.transparent,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: _buildBody(arLoadingState),
    );
  }
}
