import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:culture_connect/models/ar/ar_content_marker.dart';
import 'package:culture_connect/providers/ar/ar_content_providers.dart';
import 'package:culture_connect/widgets/common/custom_app_bar.dart';
import 'package:culture_connect/widgets/common/loading_indicator.dart';
import 'package:culture_connect/widgets/common/error_view.dart';
import 'package:culture_connect/widgets/common/empty_state.dart';
import 'package:culture_connect/screens/ar/ar_content_preview_screen.dart';
import 'package:culture_connect/screens/ar/ar_content_creation_screen.dart';

/// A screen for displaying a gallery of AR content
class ARGalleryScreen extends ConsumerStatefulWidget {
  /// Creates a new AR gallery screen
  const ARGalleryScreen({super.key});

  @override
  ConsumerState<ARGalleryScreen> createState() => _ARGalleryScreenState();
}

class _ARGalleryScreenState extends ConsumerState<ARGalleryScreen> {
  // Filter state
  ARContentType? _selectedContentType;
  bool _showOnlyOfflineContent = false;
  String _searchQuery = '';

  // Search controller
  final _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();

    // Load AR content markers
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(arContentMarkersProvider.notifier).loadAllMarkers();
    });

    _searchController.addListener(_onSearchChanged);
  }

  @override
  void dispose() {
    _searchController.removeListener(_onSearchChanged);
    _searchController.dispose();
    super.dispose();
  }

  /// Handle search query changes
  void _onSearchChanged() {
    setState(() {
      _searchQuery = _searchController.text;
    });
  }

  /// Filter AR content markers
  List<ARContentMarker> _filterMarkers(List<ARContentMarker> markers) {
    return markers.where((marker) {
      // Filter by content type
      if (_selectedContentType != null &&
          marker.contentType != _selectedContentType) {
        return false;
      }

      // Filter by offline availability
      if (_showOnlyOfflineContent && !marker.isAvailableOffline) {
        return false;
      }

      // Filter by search query
      if (_searchQuery.isNotEmpty) {
        final query = _searchQuery.toLowerCase();
        return marker.title.toLowerCase().contains(query) ||
            (marker.description?.toLowerCase().contains(query) ?? false) ||
            (marker.location?.toLowerCase().contains(query) ?? false);
      }

      return true;
    }).toList();
  }

  /// Navigate to the AR content preview screen
  void _navigateToPreview(ARContentMarker marker) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ARContentPreviewScreen(
          arContentId: marker.id,
        ),
      ),
    );
  }

  /// Navigate to the AR content creation screen
  void _navigateToCreation() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const ARContentCreationScreen(),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final arContentMarkersAsync = ref.watch(arContentMarkersProvider);

    return Scaffold(
      appBar: const CustomAppBar(
        title: 'AR Gallery',
        showBackButton: true,
      ),
      body: Column(
        children: [
          // Search and filter bar
          _buildSearchAndFilterBar(),

          // Content list
          Expanded(
            child: arContentMarkersAsync.when(
              data: (markers) {
                final filteredMarkers = _filterMarkers(markers);

                if (filteredMarkers.isEmpty) {
                  return EmptyState(
                    icon: Icons.view_in_ar,
                    title: 'No AR Content Found',
                    message: _searchQuery.isNotEmpty ||
                            _selectedContentType != null ||
                            _showOnlyOfflineContent
                        ? 'Try changing your filters or search query'
                        : 'Create your first AR content by tapping the + button',
                    actionText: 'Create AR Content',
                    onAction: _navigateToCreation,
                  );
                }

                return ListView.builder(
                  padding: const EdgeInsets.all(16.0),
                  itemCount: filteredMarkers.length,
                  itemBuilder: (context, index) {
                    final marker = filteredMarkers[index];
                    return _buildARContentCard(marker);
                  },
                );
              },
              loading: () => const Center(child: LoadingIndicator()),
              error: (error, stackTrace) => Center(
                child: ErrorView(
                  error: error.toString(),
                  onRetry: () {
                    ref
                        .read(arContentMarkersProvider.notifier)
                        .loadAllMarkers();
                  },
                ),
              ),
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _navigateToCreation,
        tooltip: 'Create AR Content',
        child: const Icon(Icons.add),
      ),
    );
  }

  /// Build the search and filter bar
  Widget _buildSearchAndFilterBar() {
    return Container(
      padding: const EdgeInsets.all(16.0),
      decoration: const BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black12,
            blurRadius: 4,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // Search bar
          TextField(
            controller: _searchController,
            decoration: InputDecoration(
              hintText: 'Search AR content',
              prefixIcon: const Icon(Icons.search),
              suffixIcon: _searchQuery.isNotEmpty
                  ? IconButton(
                      icon: const Icon(Icons.clear),
                      onPressed: () {
                        _searchController.clear();
                      },
                    )
                  : null,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8.0),
                borderSide: BorderSide.none,
              ),
              filled: true,
              fillColor: Colors.grey.withAlpha(20),
              contentPadding: const EdgeInsets.symmetric(vertical: 8.0),
            ),
          ),

          const SizedBox(height: 16.0),

          // Filter options
          Row(
            children: [
              // Content type filter
              Expanded(
                child: DropdownButtonFormField<ARContentType?>(
                  value: _selectedContentType,
                  decoration: InputDecoration(
                    labelText: 'Content Type',
                    contentPadding: const EdgeInsets.symmetric(
                        horizontal: 12.0, vertical: 8.0),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8.0),
                    ),
                  ),
                  items: [
                    const DropdownMenuItem<ARContentType?>(
                      value: null,
                      child: Text('All Types'),
                    ),
                    ...ARContentType.values.map((type) {
                      return DropdownMenuItem<ARContentType?>(
                        value: type,
                        child: Text(type.displayName),
                      );
                    }),
                  ],
                  onChanged: (value) {
                    setState(() {
                      _selectedContentType = value;
                    });
                  },
                ),
              ),

              const SizedBox(width: 16.0),

              // Offline filter
              FilterChip(
                label: const Text('Offline Only'),
                selected: _showOnlyOfflineContent,
                onSelected: (selected) {
                  setState(() {
                    _showOnlyOfflineContent = selected;
                  });
                },
                backgroundColor: Colors.grey.withAlpha(20),
                selectedColor: Theme.of(context).primaryColor.withAlpha(50),
                checkmarkColor: Theme.of(context).primaryColor,
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// Build an AR content card
  Widget _buildARContentCard(ARContentMarker marker) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16.0),
      child: InkWell(
        onTap: () => _navigateToPreview(marker),
        borderRadius: BorderRadius.circular(8.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Thumbnail
            if (marker.thumbnailUrl != null)
              ClipRRect(
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(8.0),
                  topRight: Radius.circular(8.0),
                ),
                child: AspectRatio(
                  aspectRatio: 16 / 9,
                  child: Image.network(
                    marker.thumbnailUrl!,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) {
                      return Container(
                        color: Colors.grey.withAlpha(50),
                        child: const Center(
                          child: Icon(
                            Icons.image_not_supported,
                            size: 48.0,
                            color: Colors.grey,
                          ),
                        ),
                      );
                    },
                  ),
                ),
              )
            else
              ClipRRect(
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(8.0),
                  topRight: Radius.circular(8.0),
                ),
                child: AspectRatio(
                  aspectRatio: 16 / 9,
                  child: Container(
                    color: Colors.grey.withAlpha(50),
                    child: Center(
                      child: Icon(
                        marker.contentType.icon,
                        size: 48.0,
                        color: marker.contentType.color,
                      ),
                    ),
                  ),
                ),
              ),

            // Content details
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Title and type
                  Row(
                    children: [
                      Expanded(
                        child: Text(
                          marker.title,
                          style: Theme.of(context).textTheme.titleLarge,
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                      const SizedBox(width: 8.0),
                      Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 8.0, vertical: 4.0),
                        decoration: BoxDecoration(
                          color: marker.contentType.color.withAlpha(50),
                          borderRadius: BorderRadius.circular(4.0),
                        ),
                        child: Text(
                          marker.contentType.displayName,
                          style:
                              Theme.of(context).textTheme.bodySmall?.copyWith(
                                    color: marker.contentType.color,
                                  ),
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: 8.0),

                  // Description
                  if (marker.description != null)
                    Text(
                      marker.description!,
                      style: Theme.of(context).textTheme.bodyMedium,
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),

                  const SizedBox(height: 8.0),

                  // Location and offline status
                  Row(
                    children: [
                      if (marker.location != null) ...[
                        const Icon(
                          Icons.location_on,
                          size: 16.0,
                          color: Colors.grey,
                        ),
                        const SizedBox(width: 4.0),
                        Expanded(
                          child: Text(
                            marker.location!,
                            style:
                                Theme.of(context).textTheme.bodySmall?.copyWith(
                                      color: Colors.grey,
                                    ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ],
                      if (marker.isAvailableOffline)
                        Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 8.0, vertical: 4.0),
                          decoration: BoxDecoration(
                            color: Colors.green.withAlpha(50),
                            borderRadius: BorderRadius.circular(4.0),
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              const Icon(
                                Icons.offline_pin,
                                size: 12.0,
                                color: Colors.green,
                              ),
                              const SizedBox(width: 4.0),
                              Text(
                                'Available Offline',
                                style: Theme.of(context)
                                    .textTheme
                                    .bodySmall
                                    ?.copyWith(
                                      color: Colors.green,
                                    ),
                              ),
                            ],
                          ),
                        ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
