import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:arcore_flutter_plugin/arcore_flutter_plugin.dart';
import 'package:vector_math/vector_math_64.dart' hide Colors;

// Models
import 'package:culture_connect/models/landmark.dart';

// Providers
import 'package:culture_connect/providers/ar_lazy_loading_provider.dart';

// Services
import 'package:culture_connect/services/ar_voice_command_service.dart';
import 'package:culture_connect/services/ar_backend_service.dart';
import 'package:culture_connect/services/ar_recording_service.dart';
import 'package:culture_connect/services/ar_lazy_loading_service.dart';

// Screens
import 'package:culture_connect/screens/ar/ar_settings_screen.dart';

/// AR Explore Screen with interactive elements, performance optimization,
/// and content management features.
class ARExploreScreen extends ConsumerStatefulWidget {
  final Landmark? initialLandmark;
  final bool startNavigation;

  const ARExploreScreen({
    super.key,
    this.initialLandmark,
    this.startNavigation = false,
  });

  @override
  ConsumerState<ARExploreScreen> createState() => _ARExploreScreenState();
}

/// Enum for animation types
enum AnimationType { rotate, scale, translate }

class _ARExploreScreenState extends ConsumerState<ARExploreScreen>
    with TickerProviderStateMixin {
  ArCoreController? _arController;
  late ARVoiceCommandService _voiceCommandService;
  late ARBackendService _arBackendService;
  late ARRecordingService _recordingService;
  late ARLazyLoadingService _arLazyLoadingService;
  bool _isLoading = true;
  String? _error;
  bool _isNavigating = false;
  Landmark? _selectedLandmark;
  bool _showVoiceCommandUI = false;
  bool _showInfo = false;
  bool _showRecordingControls = false;
  bool _arFeaturesLoaded = false;

  // UI control flags
  bool _showPerformanceOverlay = false;
  bool _showDebugInfo = false;
  bool _showSettings = false;
  bool _showMap = false;

  // Animation controllers
  late AnimationController _pulseAnimation;

  // Landmarks from backend
  List<Landmark> _nearbyLandmarks = [];

  @override
  void initState() {
    super.initState();

    // Initialize animation controllers
    _pulseAnimation = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1000),
      lowerBound: 0.8,
      upperBound: 1.2,
    )..repeat(reverse: true);
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();

    // Initialize services
    _voiceCommandService = ref.read(arVoiceCommandServiceProvider);
    _arBackendService = ref.read(arBackendServiceProvider);
    _recordingService = ref.read(arRecordingServiceProvider);
    _arLazyLoadingService = ref.read(arLazyLoadingServiceProvider);

    _initializeVoiceCommands();
    _initializeBackendService();
    _initializeRecordingService();
    _initializeARLazyLoading();

    _initializeAR();

    if (widget.initialLandmark != null && widget.startNavigation) {
      _startNavigation(widget.initialLandmark!);
    }
  }

  @override
  void dispose() {
    _arController?.dispose();
    _pulseAnimation.dispose();
    super.dispose();
  }

  Future<void> _initializeBackendService() async {
    try {
      await _arBackendService.initialize();
      _fetchNearbyLandmarks();
    } catch (e) {
      if (mounted) {
        setState(() {
          _error = 'Failed to initialize AR backend service: $e';
        });
      }
    }
  }

  Future<void> _initializeRecordingService() async {
    try {
      await _recordingService.initialize();
    } catch (e) {
      if (mounted) {
        setState(() {
          _error = 'Failed to initialize AR recording service: $e';
        });
      }
    }
  }

  Future<void> _initializeARLazyLoading() async {
    try {
      final result = await _arLazyLoadingService.initializeARFeatures();
      if (mounted) {
        setState(() {
          _arFeaturesLoaded = result;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _error = 'Failed to initialize AR lazy loading service: $e';
        });
      }
    }
  }

  void _initializeVoiceCommands() async {
    try {
      await _voiceCommandService.initialize();

      // Register command handlers
      _voiceCommandService.registerCommands({
        'zoom in': _zoomIn,
        'zoom out': _zoomOut,
        'rotate left': _rotateLeft,
        'rotate right': _rotateRight,
        'show info': _showLandmarkInfo,
        'hide info': _hideLandmarkInfo,
        'take photo': _takeScreenshot,
        'show settings': _toggleSettings,
        'show map': _showMapView,
        'hide map': _hideMapView,
        'navigate to': _startNavigation,
        'stop navigation': _stopNavigation,
        'help': _toggleVoiceCommandUI,
        'go back': _goBack,
      });
    } catch (e) {
      if (mounted) {
        setState(() {
          _error = 'Failed to initialize voice commands: $e';
        });
      }
    }
  }

  Future<void> _initializeAR() async {
    if (mounted) {
      setState(() {
        _isLoading = true;
        _error = null;
      });
    }

    try {
      // Check AR availability
      final available = await ArCoreController.checkArCoreAvailability();
      if (!available) {
        if (mounted) {
          setState(() {
            _error = 'ARCore is not available on this device';
            _isLoading = false;
          });
        }
        return;
      }

      // Check AR compatibility
      final installed = await ArCoreController.checkIsArCoreInstalled();
      if (!installed) {
        if (mounted) {
          setState(() {
            _error = 'ARCore is not installed on this device';
            _isLoading = false;
          });
        }
        return;
      }

      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _error = 'Failed to initialize AR: $e';
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _fetchNearbyLandmarks() async {
    try {
      if (mounted) {
        setState(() {
          _isLoading = true;
        });
      }

      // Mock location for demo purposes
      final landmarks = await _arBackendService.getLandmarks(
        latitude: 37.7749,
        longitude: -122.4194,
        radius: 1000,
      );

      if (mounted) {
        setState(() {
          _nearbyLandmarks = landmarks;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _error = 'Failed to fetch nearby landmarks: $e';
          _isLoading = false;
        });
      }
    }
  }

  void _onArCoreViewCreated(ArCoreController controller) {
    _arController = controller;
    _setupARScene();

    // Load nearby landmarks
    _loadNearbyLandmarks();
  }

  void _setupARScene() {
    if (_arController == null) return;

    // Set up tap handlers
    _arController!.onNodeTap = (name) {
      // The name parameter is the name of the tapped node
      final landmark = _findLandmarkById(name);

      if (landmark != null) {
        if (_isNavigating) {
          _addNavigationPoint(landmark);
        } else {
          _showLandmarkDetails(landmark);
        }
      }
    };

    // Set up plane tap handler
    _arController!.onPlaneTap = (hits) {
      if (hits.isEmpty || !_isNavigating) return;

      final hit = hits.first;
      _addNavigationPointAtPosition(hit.pose.translation);
    };
  }

  Future<void> _loadNearbyLandmarks() async {
    if (_arController == null) return;
    if (_nearbyLandmarks.isEmpty) return;

    try {
      for (final landmark in _nearbyLandmarks) {
        // Add a node for each landmark
        final node = ArCoreNode(
          name: landmark.id,
          shape: ArCoreSphere(
            materials: [
              ArCoreMaterial(
                color: Colors.blue.withAlpha(200),
              ),
            ],
            radius: 0.1,
          ),
          position: _getPositionForLandmark(landmark),
        );

        _arController!.addArCoreNode(node);
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _error = 'Failed to load landmarks: $e';
        });
      }
    }
  }

  Vector3 _getPositionForLandmark(Landmark landmark) {
    // In a real app, you would calculate the position based on GPS coordinates
    // For this demo, we'll use random positions
    final random = DateTime.now().millisecondsSinceEpoch % landmark.id.hashCode;

    return Vector3(
      (random % 10) / 10 - 0.5, // X: -0.5 to 0.5
      (random % 5) / 10, // Y: 0 to 0.5
      -1 - (random % 10) / 5, // Z: -1 to -3
    );
  }

  Landmark? _findLandmarkById(String id) {
    try {
      return _nearbyLandmarks.firstWhere((landmark) => landmark.id == id);
    } catch (e) {
      return null;
    }
  }

  void _addNavigationPoint(Landmark landmark) {
    if (_arController == null) return;

    final node = ArCoreNode(
      name: 'nav_${landmark.id}',
      shape: ArCoreSphere(
        materials: [
          ArCoreMaterial(
            color: Colors.green.withAlpha(200),
          ),
        ],
        radius: 0.05,
      ),
      position: _getPositionForLandmark(landmark),
    );

    _arController!.addArCoreNode(node);

    // Show a message
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Added navigation point for ${landmark.name}'),
        duration: const Duration(seconds: 2),
      ),
    );
  }

  void _addNavigationPointAtPosition(Vector3 position) {
    if (_arController == null) return;

    final node = ArCoreNode(
      name: 'nav_point_${DateTime.now().millisecondsSinceEpoch}',
      shape: ArCoreCylinder(
        materials: [
          ArCoreMaterial(
            color: Colors.green.withAlpha(200),
          ),
        ],
        radius: 0.05,
        height: 0.1,
      ),
      position: position,
    );

    _arController!.addArCoreNode(node);
  }

  Future<void> _showLandmarkDetails(Landmark landmark) async {
    if (mounted) {
      setState(() {
        _selectedLandmark = landmark;
        _isLoading = true;
      });
    }

    String? successMessage;
    String? errorMessage;

    try {
      // Fetch AR model from backend
      final arModel = await _arBackendService.getARModel(landmark.id);

      if (arModel != null) {
        // Download AR model file if needed
        final modelUrl = landmark.arContent['modelUrl'] as String? ?? '';
        if (modelUrl.isNotEmpty) {
          final modelFile =
              await _arBackendService.downloadARModelFile(modelUrl);

          if (modelFile != null) {
            successMessage = 'AR model loaded for ${landmark.name}';
          }
        }
      }
    } catch (e) {
      errorMessage = 'Failed to load AR model: $e';
    }

    if (mounted) {
      setState(() {
        _isLoading = false;
      });

      // Show landmark details dialog
      showDialog(
        context: context,
        builder: (context) =>
            _buildLandmarkDetailsDialog(landmark, successMessage, errorMessage),
      );
    }
  }

  Widget _buildLandmarkDetailsDialog(
    Landmark landmark,
    String? successMessage,
    String? errorMessage,
  ) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      elevation: 0,
      backgroundColor: Colors.transparent,
      child: Stack(
        children: [
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white,
              shape: BoxShape.rectangle,
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withAlpha(51),
                  blurRadius: 10,
                  offset: const Offset(0, 5),
                ),
              ],
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  landmark.name,
                  style: Theme.of(context).textTheme.headlineSmall,
                ),
                const SizedBox(height: 8),
                if (landmark.imageUrl.isNotEmpty)
                  ClipRRect(
                    borderRadius: BorderRadius.circular(8),
                    child: Image.network(
                      landmark.imageUrl,
                      height: 150,
                      width: double.infinity,
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) {
                        return Container(
                          height: 150,
                          color: Colors.grey.withAlpha(51),
                          child: const Icon(Icons.image_not_supported),
                        );
                      },
                    ),
                  ),
                const SizedBox(height: 16),
                Text(
                  landmark.description,
                  style: Theme.of(context).textTheme.bodyMedium,
                ),
                if (successMessage != null) ...[
                  const SizedBox(height: 16),
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.green.withAlpha(51),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Text(
                      successMessage,
                      style: const TextStyle(color: Colors.green),
                    ),
                  ),
                ],
                if (errorMessage != null) ...[
                  const SizedBox(height: 16),
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.red.withAlpha(51),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Text(
                      errorMessage,
                      style: const TextStyle(color: Colors.red),
                    ),
                  ),
                ],
                const SizedBox(height: 24),

                // Action buttons
                Row(
                  children: [
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: () {
                          Navigator.pop(context);
                          _startNavigation(landmark);
                        },
                        icon: const Icon(Icons.directions),
                        label: const Text('Navigate in AR'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Theme.of(context).primaryColor,
                          foregroundColor: Colors.white,
                        ),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: OutlinedButton.icon(
                        onPressed: () {
                          Navigator.pop(context);
                          _triggerAnimation(landmark.id, AnimationType.rotate);
                        },
                        icon: const Icon(Icons.view_in_ar),
                        label: const Text('View in 3D'),
                        style: OutlinedButton.styleFrom(
                          foregroundColor: Theme.of(context).primaryColor,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _startNavigation(Landmark landmark) {
    if (mounted) {
      setState(() {
        _isNavigating = true;
        _selectedLandmark = landmark;
      });
    }

    // Show a message
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Starting navigation to ${landmark.name}'),
        duration: const Duration(seconds: 2),
      ),
    );
  }

  void _stopNavigation() {
    if (mounted) {
      setState(() {
        _isNavigating = false;
        _selectedLandmark = null;
      });
    }

    // Clear navigation points
    if (_arController != null) {
      _arController!.removeNode(nodeName: 'navigation_path');
    }

    // Show a message
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Navigation stopped'),
        duration: Duration(seconds: 2),
      ),
    );
  }

  void _triggerAnimation(String landmarkId, AnimationType type) {
    // For demo purposes, we'll just show a message
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
            'Triggered ${type.toString().split('.').last} animation on landmark $landmarkId'),
        duration: const Duration(seconds: 2),
      ),
    );
  }

  void _togglePerformanceOverlay() {
    if (mounted) {
      setState(() {
        _showPerformanceOverlay = !_showPerformanceOverlay;
      });
    }
  }

  void _toggleSettings() {
    // Navigate to the AR settings screen
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const ARSettingsScreen(),
      ),
    );
  }

  void _toggleVoiceCommandUI() {
    if (mounted) {
      setState(() {
        _showVoiceCommandUI = !_showVoiceCommandUI;
      });
    }
  }

  void _toggleRecordingControls() {
    if (mounted) {
      setState(() {
        _showRecordingControls = !_showRecordingControls;
      });
    }
  }

  // Map visibility methods used by voice commands
  void _showMapView() {
    if (mounted) {
      setState(() {
        _showMap = true;
      });
    }
  }

  void _hideMapView() {
    if (mounted) {
      setState(() {
        _showMap = false;
      });
    }
  }

  void _goBack() {
    Navigator.of(context).pop();
  }

  void _zoomIn() {
    // Implementation for zoom in voice command
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Zooming in'),
        duration: Duration(seconds: 1),
      ),
    );
  }

  void _zoomOut() {
    // Implementation for zoom out voice command
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Zooming out'),
        duration: Duration(seconds: 1),
      ),
    );
  }

  void _rotateLeft() {
    // Implementation for rotate left voice command
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Rotating left'),
        duration: Duration(seconds: 1),
      ),
    );
  }

  void _rotateRight() {
    // Implementation for rotate right voice command
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Rotating right'),
        duration: Duration(seconds: 1),
      ),
    );
  }

  void _showLandmarkInfo() {
    if (_selectedLandmark != null) {
      if (mounted) {
        setState(() {
          _showInfo = true;
        });
      }
    }
  }

  void _hideLandmarkInfo() {
    if (mounted) {
      setState(() {
        _showInfo = false;
      });
    }
  }

  Future<void> _takeScreenshot() async {
    try {
      if (_arController != null) {
        await _recordingService.takeScreenshot();

        // Show a message
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Screenshot taken'),
              duration: Duration(seconds: 2),
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to take screenshot: $e'),
            duration: const Duration(seconds: 2),
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_isNavigating ? 'AR Navigation' : 'AR Explore'),
        actions: [
          IconButton(
            icon: const Icon(Icons.mic),
            onPressed: _toggleVoiceCommandUI,
            tooltip: 'Voice Commands',
          ),
          IconButton(
            icon: Icon(_recordingService.isRecording
                ? Icons.stop_circle
                : Icons.videocam),
            onPressed: _toggleRecordingControls,
            tooltip: _recordingService.isRecording
                ? 'Stop Recording'
                : 'Record AR Experience',
            color: _recordingService.isRecording ? Colors.red : null,
          ),
          if (_isNavigating)
            IconButton(
              icon: const Icon(Icons.close),
              onPressed: _stopNavigation,
            ),
          IconButton(
            icon: const Icon(Icons.speed),
            onPressed: _togglePerformanceOverlay,
          ),
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: _toggleSettings,
          ),
        ],
      ),
      body: _buildBody(),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          if (_voiceCommandService.isListening) {
            _voiceCommandService.stopListening();
          } else {
            _voiceCommandService.startListening();
          }
        },
        backgroundColor: _voiceCommandService.isListening
            ? Colors.red
            : Theme.of(context).primaryColor,
        tooltip: _voiceCommandService.isListening
            ? 'Stop Listening'
            : 'Start Listening',
        child: Icon(
          _voiceCommandService.isListening ? Icons.mic_off : Icons.mic,
        ),
      ),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (_error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              color: Colors.red,
              size: 60,
            ),
            const SizedBox(height: 16),
            Text(
              'Error',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 32),
              child: Text(
                _error!,
                textAlign: TextAlign.center,
                style: Theme.of(context).textTheme.bodyMedium,
              ),
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: _initializeAR,
              child: const Text('Retry'),
            ),
          ],
        ),
      );
    }

    return Stack(
      children: [
        // AR View
        ArCoreView(
          onArCoreViewCreated: _onArCoreViewCreated,
          enableTapRecognizer: true,
          enableUpdateListener: true,
        ),

        // Voice command UI
        if (_showVoiceCommandUI) _buildVoiceCommandUI(),

        // Recording controls
        if (_showRecordingControls) _buildRecordingControls(),

        // Performance overlay
        if (_showPerformanceOverlay) _buildPerformanceOverlay(),

        // Navigation UI
        if (_isNavigating) _buildNavigationUI(),

        // Info overlay
        if (_showInfo && _selectedLandmark != null) _buildInfoOverlay(),
      ],
    );
  }

  Widget _buildVoiceCommandUI() {
    return Positioned(
      top: 16,
      left: 16,
      right: 16,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withAlpha(51),
              blurRadius: 10,
              offset: const Offset(0, 5),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Voice Commands',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
                IconButton(
                  icon: const Icon(Icons.close),
                  onPressed: _toggleVoiceCommandUI,
                ),
              ],
            ),
            const Divider(),
            const SizedBox(height: 8),
            const Text('Available commands:'),
            const SizedBox(height: 8),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: [
                _buildCommandChip('zoom in'),
                _buildCommandChip('zoom out'),
                _buildCommandChip('rotate left'),
                _buildCommandChip('rotate right'),
                _buildCommandChip('show info'),
                _buildCommandChip('hide info'),
                _buildCommandChip('take photo'),
                _buildCommandChip('show settings'),
                _buildCommandChip('navigate to'),
                _buildCommandChip('stop navigation'),
                _buildCommandChip('help'),
                _buildCommandChip('go back'),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCommandChip(String command) {
    return Chip(
      label: Text(command),
      backgroundColor: Theme.of(context).primaryColor.withAlpha(51),
    );
  }

  Widget _buildRecordingControls() {
    return Positioned(
      bottom: 16,
      left: 16,
      right: 16,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withAlpha(51),
              blurRadius: 10,
              offset: const Offset(0, 5),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Recording',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
                IconButton(
                  icon: const Icon(Icons.close),
                  onPressed: _toggleRecordingControls,
                ),
              ],
            ),
            const Divider(),
            const SizedBox(height: 8),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(
                    _recordingService.isRecording
                        ? Icons.stop_circle
                        : Icons.fiber_manual_record,
                    color: _recordingService.isRecording ? Colors.red : null,
                    size: 32,
                  ),
                  onPressed: () {
                    if (_recordingService.isRecording) {
                      _recordingService.stopRecording();
                    } else {
                      _recordingService.startRecording();
                    }
                    setState(() {});
                  },
                  tooltip: _recordingService.isRecording
                      ? 'Stop Recording'
                      : 'Start Recording',
                ),
                IconButton(
                  icon: const Icon(
                    Icons.camera_alt,
                    size: 32,
                  ),
                  onPressed: _takeScreenshot,
                  tooltip: 'Take Screenshot',
                ),
                IconButton(
                  icon: const Icon(
                    Icons.videocam,
                    size: 32,
                  ),
                  onPressed: () async {
                    await _recordingService.recordVideo();
                  },
                  tooltip: 'Record Video',
                ),
              ],
            ),
            if (_recordingService.isRecording) ...[
              const SizedBox(height: 16),
              Center(
                child: Text(
                  'Recording: ${_recordingService.recordingDurationFormatted}',
                  style: const TextStyle(
                    color: Colors.red,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildPerformanceOverlay() {
    return Positioned(
      top: 16,
      right: 16,
      child: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: Colors.black.withAlpha(128),
          borderRadius: BorderRadius.circular(8),
        ),
        child: const Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Performance',
              style:
                  TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 4),
            Text(
              'FPS: 60',
              style: TextStyle(color: Colors.white),
            ),
            Text(
              'Memory: 120MB',
              style: TextStyle(color: Colors.white),
            ),
            Text(
              'CPU: 15%',
              style: TextStyle(color: Colors.white),
            ),
            Text(
              'GPU: 25%',
              style: TextStyle(color: Colors.white),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNavigationUI() {
    return Positioned(
      bottom: 16,
      left: 16,
      right: 16,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withAlpha(51),
              blurRadius: 10,
              offset: const Offset(0, 5),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                AnimatedBuilder(
                  animation: _pulseAnimation,
                  builder: (context, child) {
                    return Transform.scale(
                      scale: _pulseAnimation.value,
                      child: const Icon(
                        Icons.navigation,
                        color: Colors.blue,
                        size: 24,
                      ),
                    );
                  },
                ),
                const SizedBox(width: 8),
                Text(
                  'Navigation Mode',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
              ],
            ),
            const SizedBox(height: 8),
            if (_selectedLandmark != null)
              Text(
                'Navigating to: ${_selectedLandmark!.name}',
                style: Theme.of(context).textTheme.bodyMedium,
              ),
            const SizedBox(height: 8),
            Text(
              'Tap on the ground to add navigation points',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton.icon(
                  onPressed: _stopNavigation,
                  icon: const Icon(Icons.close),
                  label: const Text('Stop Navigation'),
                  style: TextButton.styleFrom(
                    foregroundColor: Colors.red,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoOverlay() {
    if (_selectedLandmark == null) return const SizedBox.shrink();

    return Positioned(
      top: 16,
      left: 16,
      right: 16,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withAlpha(51),
              blurRadius: 10,
              offset: const Offset(0, 5),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  _selectedLandmark!.name,
                  style: Theme.of(context).textTheme.titleLarge,
                ),
                IconButton(
                  icon: const Icon(Icons.close),
                  onPressed: _hideLandmarkInfo,
                ),
              ],
            ),
            const Divider(),
            const SizedBox(height: 8),
            Text(
              _selectedLandmark!.description,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
          ],
        ),
      ),
    );
  }
}
