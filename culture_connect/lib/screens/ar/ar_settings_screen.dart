import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'package:culture_connect/providers/ar_settings_provider.dart';
import 'package:culture_connect/widgets/common/custom_app_bar.dart';
import 'package:culture_connect/theme/app_colors.dart';
import 'package:culture_connect/theme/app_text_styles.dart';

/// A screen for configuring AR settings
class ARSettingsScreen extends ConsumerStatefulWidget {
  /// Creates a new AR settings screen
  const ARSettingsScreen({super.key});
  
  @override
  ConsumerState<ARSettingsScreen> createState() => _ARSettingsScreenState();
}

class _ARSettingsScreenState extends ConsumerState<ARSettingsScreen> {
  // AR quality settings
  String _selectedQuality = 'medium';

  // AR feature toggles
  bool _enableAnimations = true;
  bool _enableOfflineMode = true;
  bool _enableLocationTracking = true;
  bool _enableBackgroundDownloads = true;
  bool _enableHapticFeedback = true;

  // AR content settings
  double _maxCacheSize = 500; // MB
  int _maxVisibleLandmarks = 10;
  double _renderDistance = 1000; // meters

  // AR performance settings
  double _targetFrameRate = 30;
  bool _enableFrustumCulling = true;
  bool _enableLOD = true;

  // AR appearance settings
  String _selectedTheme = 'auto';
  bool _showDebugInfo = false;
  bool _showPerformanceMetrics = false;
  
  @override
  void initState() {
    super.initState();
    
    // Load settings from provider
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadSettings();
    });
  }
  
  /// Load settings from provider
  void _loadSettings() {
    final settings = ref.read(arSettingsProvider);
    
    setState(() {
      _selectedQuality = settings.quality;
      _enableAnimations = settings.enableAnimations;
      _enableOfflineMode = settings.enableOfflineMode;
      _enableLocationTracking = settings.enableLocationTracking;
      _enableBackgroundDownloads = settings.enableBackgroundDownloads;
      _enableHapticFeedback = settings.enableHapticFeedback;
      _maxCacheSize = settings.maxCacheSize;
      _maxVisibleLandmarks = settings.maxVisibleLandmarks;
      _renderDistance = settings.renderDistance;
      _targetFrameRate = settings.targetFrameRate;
      _enableFrustumCulling = settings.enableFrustumCulling;
      _enableLOD = settings.enableLOD;
      _selectedTheme = settings.theme;
      _showDebugInfo = settings.showDebugInfo;
      _showPerformanceMetrics = settings.showPerformanceMetrics;
    });
  }
  
  /// Save settings to provider
  void _saveSettings() {
    final notifier = ref.read(arSettingsProvider.notifier);
    
    notifier.updateSettings(
      quality: _selectedQuality,
      enableAnimations: _enableAnimations,
      enableOfflineMode: _enableOfflineMode,
      enableLocationTracking: _enableLocationTracking,
      enableBackgroundDownloads: _enableBackgroundDownloads,
      enableHapticFeedback: _enableHapticFeedback,
      maxCacheSize: _maxCacheSize,
      maxVisibleLandmarks: _maxVisibleLandmarks,
      renderDistance: _renderDistance,
      targetFrameRate: _targetFrameRate,
      enableFrustumCulling: _enableFrustumCulling,
      enableLOD: _enableLOD,
      theme: _selectedTheme,
      showDebugInfo: _showDebugInfo,
      showPerformanceMetrics: _showPerformanceMetrics,
    );
    
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Settings saved'),
        backgroundColor: Colors.green,
      ),
    );
  }
  
  /// Reset settings to defaults
  void _resetSettings() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Reset Settings'),
        content: const Text('Are you sure you want to reset all AR settings to their default values?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              ref.read(arSettingsProvider.notifier).resetToDefaults();
              _loadSettings();
              
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Settings reset to defaults'),
                  backgroundColor: Colors.green,
                ),
              );
            },
            child: const Text('Reset'),
          ),
        ],
      ),
    );
  }
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const CustomAppBar(
        title: 'AR Settings',
        showBackButton: true,
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(16.r),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Quality settings
            _buildSectionHeader('Quality Settings'),
            _buildRadioTile(
              title: 'Low Quality',
              subtitle: 'Better performance, lower visual quality',
              value: 'low',
              groupValue: _selectedQuality,
              onChanged: (value) => setState(() => _selectedQuality = value!),
            ),
            _buildRadioTile(
              title: 'Medium Quality',
              subtitle: 'Balanced performance and visual quality',
              value: 'medium',
              groupValue: _selectedQuality,
              onChanged: (value) => setState(() => _selectedQuality = value!),
            ),
            _buildRadioTile(
              title: 'High Quality',
              subtitle: 'Best visual quality, may affect performance',
              value: 'high',
              groupValue: _selectedQuality,
              onChanged: (value) => setState(() => _selectedQuality = value!),
            ),
            
            const SizedBox(height: 24),
            
            // Feature toggles
            _buildSectionHeader('Features'),
            _buildSwitchTile(
              title: 'Animations',
              subtitle: 'Enable animations in AR experiences',
              value: _enableAnimations,
              onChanged: (value) => setState(() => _enableAnimations = value),
            ),
            _buildSwitchTile(
              title: 'Offline Mode',
              subtitle: 'Download AR content for offline use',
              value: _enableOfflineMode,
              onChanged: (value) => setState(() => _enableOfflineMode = value),
            ),
            _buildSwitchTile(
              title: 'Location Tracking',
              subtitle: 'Use location for AR experiences',
              value: _enableLocationTracking,
              onChanged: (value) => setState(() => _enableLocationTracking = value),
            ),
            _buildSwitchTile(
              title: 'Background Downloads',
              subtitle: 'Download AR content in the background',
              value: _enableBackgroundDownloads,
              onChanged: (value) => setState(() => _enableBackgroundDownloads = value),
            ),
            _buildSwitchTile(
              title: 'Haptic Feedback',
              subtitle: 'Enable vibration for AR interactions',
              value: _enableHapticFeedback,
              onChanged: (value) => setState(() => _enableHapticFeedback = value),
            ),
            
            const SizedBox(height: 24),
            
            // Content settings
            _buildSectionHeader('Content Settings'),
            _buildSliderTile(
              title: 'Max Cache Size',
              subtitle: '${_maxCacheSize.toInt()} MB',
              value: _maxCacheSize,
              min: 100,
              max: 2000,
              divisions: 19,
              onChanged: (value) => setState(() => _maxCacheSize = value),
            ),
            _buildSliderTile(
              title: 'Max Visible Landmarks',
              subtitle: _maxVisibleLandmarks.toString(),
              value: _maxVisibleLandmarks.toDouble(),
              min: 5,
              max: 30,
              divisions: 25,
              onChanged: (value) => setState(() => _maxVisibleLandmarks = value.toInt()),
            ),
            _buildSliderTile(
              title: 'Render Distance',
              subtitle: '${_renderDistance.toInt()} meters',
              value: _renderDistance,
              min: 100,
              max: 5000,
              divisions: 49,
              onChanged: (value) => setState(() => _renderDistance = value),
            ),
            
            const SizedBox(height: 24),
            
            // Performance settings
            _buildSectionHeader('Performance Settings'),
            _buildSliderTile(
              title: 'Target Frame Rate',
              subtitle: '${_targetFrameRate.toInt()} FPS',
              value: _targetFrameRate,
              min: 15,
              max: 60,
              divisions: 9,
              onChanged: (value) => setState(() => _targetFrameRate = value),
            ),
            _buildSwitchTile(
              title: 'Frustum Culling',
              subtitle: 'Only render objects in view',
              value: _enableFrustumCulling,
              onChanged: (value) => setState(() => _enableFrustumCulling = value),
            ),
            _buildSwitchTile(
              title: 'Level of Detail (LOD)',
              subtitle: 'Reduce detail for distant objects',
              value: _enableLOD,
              onChanged: (value) => setState(() => _enableLOD = value),
            ),
            
            const SizedBox(height: 24),
            
            // Appearance settings
            _buildSectionHeader('Appearance Settings'),
            _buildRadioTile(
              title: 'Auto Theme',
              subtitle: 'Follow system theme',
              value: 'auto',
              groupValue: _selectedTheme,
              onChanged: (value) => setState(() => _selectedTheme = value!),
            ),
            _buildRadioTile(
              title: 'Light Theme',
              subtitle: 'Use light theme for AR UI',
              value: 'light',
              groupValue: _selectedTheme,
              onChanged: (value) => setState(() => _selectedTheme = value!),
            ),
            _buildRadioTile(
              title: 'Dark Theme',
              subtitle: 'Use dark theme for AR UI',
              value: 'dark',
              groupValue: _selectedTheme,
              onChanged: (value) => setState(() => _selectedTheme = value!),
            ),
            _buildSwitchTile(
              title: 'Debug Information',
              subtitle: 'Show debug info in AR view',
              value: _showDebugInfo,
              onChanged: (value) => setState(() => _showDebugInfo = value),
            ),
            _buildSwitchTile(
              title: 'Performance Metrics',
              subtitle: 'Show FPS and memory usage',
              value: _showPerformanceMetrics,
              onChanged: (value) => setState(() => _showPerformanceMetrics = value),
            ),
            
            const SizedBox(height: 32),
            
            // Action buttons
            Row(
              children: [
                Expanded(
                  child: OutlinedButton(
                    onPressed: _resetSettings,
                    style: OutlinedButton.styleFrom(
                      padding: EdgeInsets.symmetric(vertical: 16.h),
                    ),
                    child: const Text('Reset to Defaults'),
                  ),
                ),
                SizedBox(width: 16.w),
                Expanded(
                  child: ElevatedButton(
                    onPressed: _saveSettings,
                    style: ElevatedButton.styleFrom(
                      padding: EdgeInsets.symmetric(vertical: 16.h),
                    ),
                    child: const Text('Save Settings'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
  
  /// Build a section header
  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: EdgeInsets.only(bottom: 8.h),
      child: Text(
        title,
        style: AppTextStyles.headline6,
      ),
    );
  }
  
  /// Build a radio tile
  Widget _buildRadioTile({
    required String title,
    required String subtitle,
    required String value,
    required String groupValue,
    required ValueChanged<String?> onChanged,
  }) {
    return RadioListTile<String>(
      title: Text(title),
      subtitle: Text(subtitle),
      value: value,
      groupValue: groupValue,
      onChanged: onChanged,
      activeColor: AppColors.primary,
      contentPadding: EdgeInsets.zero,
    );
  }
  
  /// Build a switch tile
  Widget _buildSwitchTile({
    required String title,
    required String subtitle,
    required bool value,
    required ValueChanged<bool> onChanged,
  }) {
    return SwitchListTile(
      title: Text(title),
      subtitle: Text(subtitle),
      value: value,
      onChanged: onChanged,
      activeColor: AppColors.primary,
      contentPadding: EdgeInsets.zero,
    );
  }
  
  /// Build a slider tile
  Widget _buildSliderTile({
    required String title,
    required String subtitle,
    required double value,
    required double min,
    required double max,
    required int divisions,
    required ValueChanged<double> onChanged,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(title),
            Text(
              subtitle,
              style: AppTextStyles.body2.copyWith(color: Colors.grey),
            ),
          ],
        ),
        Slider(
          value: value,
          min: min,
          max: max,
          divisions: divisions,
          onChanged: onChanged,
          activeColor: AppColors.primary,
        ),
      ],
    );
  }
}
