import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:lottie/lottie.dart';
import 'package:culture_connect/models/booking.dart';
import 'package:culture_connect/models/experience.dart';
import 'package:culture_connect/models/receipt.dart';
import 'package:culture_connect/screens/home_screen.dart';
import 'package:culture_connect/screens/payment/receipt_screen.dart';
import 'package:culture_connect/theme/app_theme.dart';
import 'package:culture_connect/widgets/custom_app_bar.dart';

/// Screen shown after a successful payment
class PaymentSuccessScreen extends ConsumerWidget {
  /// The experience that was booked
  final Experience experience;

  /// The booking details
  final Booking booking;

  /// The receipt for the payment
  final Receipt receipt;

  /// Creates a new payment success screen
  const PaymentSuccessScreen({
    super.key,
    required this.experience,
    required this.booking,
    required this.receipt,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return WillPopScope(
      onWillPop: () async {
        // Navigate to home screen when back button is pressed
        _navigateToHome(context);
        return false;
      },
      child: Scaffold(
        appBar: const CustomAppBar(
          title: 'Payment Successful',
          showBackButton: false,
        ),
        body: SingleChildScrollView(
          child: Padding(
            padding: EdgeInsets.all(24.r),
            child: Column(
              children: [
                // Success animation
                Lottie.asset(
                  'assets/animations/payment_success.json',
                  width: 200.w,
                  height: 200.h,
                  repeat: false,
                ),
                SizedBox(height: 24.h),

                // Success message
                Text(
                  'Payment Successful!',
                  style: TextStyle(
                    fontSize: 24.sp,
                    fontWeight: FontWeight.bold,
                    color: AppTheme.primaryColor,
                  ),
                ),
                SizedBox(height: 16.h),
                Text(
                  'Your booking for ${experience.title} has been confirmed.',
                  style: TextStyle(
                    fontSize: 16.sp,
                    color: Colors.grey.withAlpha(179), // shade700 equivalent
                  ),
                  textAlign: TextAlign.center,
                ),
                SizedBox(height: 32.h),

                // Booking details
                _buildBookingDetails(),
                SizedBox(height: 32.h),

                // Receipt button
                OutlinedButton.icon(
                  onPressed: () => _viewReceipt(context),
                  icon: const Icon(Icons.receipt),
                  label: const Text('View Receipt'),
                  style: OutlinedButton.styleFrom(
                    foregroundColor: AppTheme.primaryColor,
                    side: const BorderSide(color: AppTheme.primaryColor),
                    padding: EdgeInsets.symmetric(
                      horizontal: 24.w,
                      vertical: 12.h,
                    ),
                  ),
                ),
                SizedBox(height: 16.h),

                // Continue button
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: () => _navigateToHome(context),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppTheme.primaryColor,
                      foregroundColor: Colors.white,
                      padding: EdgeInsets.symmetric(vertical: 16.h),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8.r),
                      ),
                    ),
                    child: Text(
                      'Continue to Home',
                      style: TextStyle(
                        fontSize: 16.sp,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildBookingDetails() {
    return Container(
      padding: EdgeInsets.all(16.r),
      decoration: BoxDecoration(
        color: Colors.grey.withAlpha(13), // shade50 equivalent
        borderRadius: BorderRadius.circular(8.r),
        border:
            Border.all(color: Colors.grey.withAlpha(51)), // shade200 equivalent
      ),
      child: Column(
        children: [
          // Booking ID
          _buildDetailRow(
            'Booking ID',
            booking.id.substring(0, 8).toUpperCase(),
          ),
          SizedBox(height: 12.h),

          // Experience
          _buildDetailRow(
            'Experience',
            experience.title,
          ),
          SizedBox(height: 12.h),

          // Date
          _buildDetailRow(
            'Date',
            _formatDate(booking.date),
          ),
          SizedBox(height: 12.h),

          // Time
          _buildDetailRow(
            'Time',
            booking.timeSlot.formattedTime,
          ),
          SizedBox(height: 12.h),

          // Participants
          _buildDetailRow(
            'Participants',
            '${booking.participantCount} ${booking.participantCount == 1 ? 'person' : 'people'}',
          ),
          SizedBox(height: 12.h),

          // Amount paid
          _buildDetailRow(
            'Amount Paid',
            _formatAmount(booking.totalAmount),
            isHighlighted: true,
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value,
      {bool isHighlighted = false}) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: 14.sp,
            color: Colors.grey.withAlpha(179), // shade700 equivalent
          ),
        ),
        Text(
          value,
          style: TextStyle(
            fontSize: 14.sp,
            fontWeight: isHighlighted ? FontWeight.bold : FontWeight.normal,
            color: isHighlighted ? AppTheme.primaryColor : Colors.black,
          ),
        ),
      ],
    );
  }

  String _formatDate(DateTime date) {
    final months = [
      'January',
      'February',
      'March',
      'April',
      'May',
      'June',
      'July',
      'August',
      'September',
      'October',
      'November',
      'December'
    ];

    return '${months[date.month - 1]} ${date.day}, ${date.year}';
  }

  String _formatAmount(double amount) {
    return '\$${amount.toStringAsFixed(2)}';
  }

  void _viewReceipt(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ReceiptScreen(
          receipt: receipt,
          experience: experience,
          booking: booking,
        ),
      ),
    );
  }

  void _navigateToHome(BuildContext context) {
    Navigator.pushAndRemoveUntil(
      context,
      MaterialPageRoute(builder: (context) => const HomeScreen()),
      (route) => false,
    );
  }
}
