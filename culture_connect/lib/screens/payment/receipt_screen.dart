import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
// TODO: Add pdf dependency to pubspec.yaml to enable PDF generation
// import 'package:share_plus/share_plus.dart';
// import 'package:path_provider/path_provider.dart';
// import 'dart:io';
// import 'package:pdf/pdf.dart';
// import 'package:pdf/widgets.dart' as pw;
import 'package:culture_connect/models/booking.dart';
import 'package:culture_connect/models/experience.dart';
import 'package:culture_connect/models/receipt.dart';
import 'package:culture_connect/theme/app_theme.dart';
import 'package:culture_connect/widgets/custom_app_bar.dart';

/// Screen for displaying a receipt
class ReceiptScreen extends ConsumerStatefulWidget {
  /// The receipt to display
  final Receipt receipt;

  /// The experience associated with the receipt
  final Experience experience;

  /// The booking associated with the receipt
  final Booking booking;

  /// Creates a new receipt screen
  const ReceiptScreen({
    super.key,
    required this.receipt,
    required this.experience,
    required this.booking,
  });

  @override
  ConsumerState<ReceiptScreen> createState() => _ReceiptScreenState();
}

class _ReceiptScreenState extends ConsumerState<ReceiptScreen> {
  bool _isGeneratingPdf = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title: 'Receipt',
        showBackButton: true,
        actions: [
          IconButton(
            icon: const Icon(Icons.share),
            onPressed: _shareReceipt,
            tooltip: 'Share Receipt',
          ),
          IconButton(
            icon: const Icon(Icons.download),
            onPressed: _downloadReceipt,
            tooltip: 'Download Receipt',
          ),
        ],
      ),
      body: Stack(
        children: [
          SingleChildScrollView(
            child: Padding(
              padding: const EdgeInsets.all(24.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Receipt header
                  _buildReceiptHeader(),
                  const SizedBox(height: 24.0),

                  // Receipt details
                  _buildReceiptDetails(),
                  const SizedBox(height: 24.0),

                  // Payment details
                  _buildPaymentDetails(),
                  const SizedBox(height: 24.0),

                  // Terms and conditions
                  _buildTermsAndConditions(),
                  const SizedBox(height: 24.0),

                  // Support information
                  _buildSupportInfo(),
                ],
              ),
            ),
          ),

          // Loading overlay
          if (_isGeneratingPdf)
            Container(
              color: Colors.black.withAlpha(128), // withOpacity(0.5) equivalent
              child: const Center(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    CircularProgressIndicator(
                      color: Colors.white,
                    ),
                    SizedBox(height: 16.0),
                    Text(
                      'Generating PDF...',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 16.0,
                      ),
                    ),
                  ],
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildReceiptHeader() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'RECEIPT',
              style: TextStyle(
                fontSize: 24.0,
                fontWeight: FontWeight.bold,
                color: AppTheme.primaryColor,
              ),
            ),
            Text(
              'PAID',
              style: TextStyle(
                fontSize: 18.0,
                fontWeight: FontWeight.bold,
                color: Colors.green,
              ),
            ),
          ],
        ),
        SizedBox(height: 8.0),
        Text(
          'Receipt #${widget.receipt.receiptNumber}',
          style: TextStyle(
            fontSize: 16.0,
            color: Colors.grey.withAlpha(179), // shade700 equivalent
          ),
        ),
        SizedBox(height: 4.0),
        Text(
          'Date: ${_formatDate(widget.receipt.createdAt)}',
          style: TextStyle(
            fontSize: 14.0,
            color: Colors.grey.withAlpha(179), // shade700 equivalent
          ),
        ),
      ],
    );
  }

  Widget _buildReceiptDetails() {
    return Container(
      padding: EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8.0),
        border:
            Border.all(color: Colors.grey.withAlpha(51)), // shade200 equivalent
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(13), // withOpacity(0.05) equivalent
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Experience Details',
            style: TextStyle(
              fontSize: 16.0,
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: 16.0),

          // Experience name
          _buildDetailRow('Experience', widget.experience.title),
          SizedBox(height: 8.0),

          // Date
          _buildDetailRow('Date', _formatDate(widget.booking.date)),
          SizedBox(height: 8.0),

          // Time
          _buildDetailRow('Time', widget.booking.timeSlot.formattedTime),
          SizedBox(height: 8.0),

          // Location
          _buildDetailRow('Location', widget.experience.location),
          SizedBox(height: 8.0),

          // Participants
          _buildDetailRow(
            'Participants',
            '${widget.booking.participantCount} ${widget.booking.participantCount == 1 ? 'person' : 'people'}',
          ),

          // Divider
          Padding(
            padding: EdgeInsets.symmetric(vertical: 16.0),
            child: Divider(
                height: 1,
                color: Colors.grey.withAlpha(77)), // shade300 equivalent
          ),

          // Price breakdown
          Text(
            'Price Breakdown',
            style: TextStyle(
              fontSize: 16.0,
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: 16.0),

          // Base price
          _buildDetailRow(
            'Base Price',
            _formatAmount(widget.experience.price),
          ),
          SizedBox(height: 8.0),

          // Participants
          _buildDetailRow(
            'Participants',
            '× ${widget.booking.participantCount}',
          ),
          SizedBox(height: 8.0),

          // Subtotal
          _buildDetailRow(
            'Subtotal',
            _formatAmount(
                widget.experience.price * widget.booking.participantCount),
          ),
          SizedBox(height: 8.0),

          // Service fee (calculated as 10% of subtotal)
          _buildDetailRow(
            'Service Fee',
            _formatAmount(_calculateServiceFee()),
          ),
          SizedBox(height: 8.0),

          // Taxes (calculated as 5% of subtotal)
          _buildDetailRow(
            'Taxes',
            _formatAmount(_calculateTaxAmount()),
          ),

          // Divider
          Padding(
            padding: EdgeInsets.symmetric(vertical: 16.0),
            child: Divider(
                height: 1,
                color: Colors.grey.withAlpha(77)), // shade300 equivalent
          ),

          // Total
          _buildDetailRow(
            'Total',
            _formatAmount(widget.booking.totalAmount),
            isTotal: true,
          ),
        ],
      ),
    );
  }

  Widget _buildPaymentDetails() {
    return Container(
      padding: EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8.0),
        border:
            Border.all(color: Colors.grey.withAlpha(51)), // shade200 equivalent
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(13), // withOpacity(0.05) equivalent
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Payment Details',
            style: TextStyle(
              fontSize: 16.0,
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: 16.0),

          // Payment method
          _buildDetailRow(
            'Payment Method',
            widget.receipt.paymentMethodName,
          ),
          SizedBox(height: 8.0),

          // Transaction ID
          _buildDetailRow(
            'Transaction ID',
            widget.receipt.transactionId,
          ),
          SizedBox(height: 8.0),

          // Date
          _buildDetailRow(
            'Payment Date',
            _formatDate(widget.receipt.createdAt),
          ),
          SizedBox(height: 8.0),

          // Status
          _buildDetailRow(
            'Status',
            'Paid',
            valueColor: Colors.green,
          ),
        ],
      ),
    );
  }

  Widget _buildTermsAndConditions() {
    return Container(
      padding: EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: Colors.grey.withAlpha(13), // shade50 equivalent
        borderRadius: BorderRadius.circular(8.0),
        border:
            Border.all(color: Colors.grey.withAlpha(51)), // shade200 equivalent
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Terms & Conditions',
            style: TextStyle(
              fontSize: 16.0,
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: 8.0),
          Text(
            'Cancellation Policy: Free cancellation up to 24 hours before the experience. '
            'No refunds for cancellations made less than 24 hours before the scheduled time.',
            style: TextStyle(
              fontSize: 12.0,
              color: Colors.grey.withAlpha(179), // shade700 equivalent
            ),
          ),
          SizedBox(height: 8.0),
          Text(
            'For full terms and conditions, please visit our website.',
            style: TextStyle(
              fontSize: 12.0,
              color: Colors.grey.withAlpha(179), // shade700 equivalent
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSupportInfo() {
    return Container(
      padding: EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: Colors.grey.withAlpha(13), // shade50 equivalent
        borderRadius: BorderRadius.circular(8.0),
        border:
            Border.all(color: Colors.grey.withAlpha(51)), // shade200 equivalent
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Need Help?',
            style: TextStyle(
              fontSize: 16.0,
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: 8.0),
          Text(
            'If you have any questions or need assistance, please contact our support team:',
            style: TextStyle(
              fontSize: 12.0,
              color: Colors.grey.withAlpha(179), // shade700 equivalent
            ),
          ),
          SizedBox(height: 8.0),
          Row(
            children: [
              Icon(
                Icons.email,
                size: 16.0,
                color: AppTheme.primaryColor,
              ),
              SizedBox(width: 8.0),
              Text(
                '<EMAIL>',
                style: TextStyle(
                  fontSize: 12.0,
                  color: AppTheme.primaryColor,
                ),
              ),
            ],
          ),
          SizedBox(height: 4.0),
          Row(
            children: [
              Icon(
                Icons.phone,
                size: 16.0,
                color: AppTheme.primaryColor,
              ),
              SizedBox(width: 8.0),
              Text(
                '+****************',
                style: TextStyle(
                  fontSize: 12.0,
                  color: AppTheme.primaryColor,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value,
      {bool isTotal = false, Color? valueColor}) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: isTotal ? 16.0 : 14.0,
            fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
            color: Colors.grey.withAlpha(179), // shade700 equivalent
          ),
        ),
        Text(
          value,
          style: TextStyle(
            fontSize: isTotal ? 16.0 : 14.0,
            fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
            color:
                valueColor ?? (isTotal ? AppTheme.primaryColor : Colors.black),
          ),
        ),
      ],
    );
  }

  String _formatDate(DateTime date) {
    final months = [
      'January',
      'February',
      'March',
      'April',
      'May',
      'June',
      'July',
      'August',
      'September',
      'October',
      'November',
      'December'
    ];

    return '${months[date.month - 1]} ${date.day}, ${date.year}';
  }

  String _formatAmount(double amount) {
    return '\$${amount.toStringAsFixed(2)}';
  }

  /// Calculate the service fee (10% of subtotal)
  double _calculateServiceFee() {
    final subtotal = widget.experience.price * widget.booking.participantCount;
    return subtotal * 0.10; // 10% service fee
  }

  /// Calculate the tax amount (5% of subtotal)
  double _calculateTaxAmount() {
    final subtotal = widget.experience.price * widget.booking.participantCount;
    return subtotal * 0.05; // 5% tax
  }

  Future<void> _shareReceipt() async {
    // TODO: Implement PDF generation when pdf dependency is added
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text(
            'PDF generation not available - add pdf dependency to pubspec.yaml'),
        backgroundColor: Colors.orange,
      ),
    );
  }

  Future<void> _downloadReceipt() async {
    // TODO: Implement PDF generation when pdf dependency is added
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text(
            'PDF generation not available - add pdf dependency to pubspec.yaml'),
        backgroundColor: Colors.orange,
      ),
    );
  }

  // TODO: Uncomment when pdf dependency is added to pubspec.yaml
  /*
  Future<File?> _generatePdf() async {
    // For simplicity, we'll just create a basic PDF without the complex styling
    // that was causing issues with PdfColors
    final pdf = pw.Document();

    try {
      // Add logo image
      final ByteData logoData = await rootBundle.load('assets/images/logo.png');
      final Uint8List logoBytes = logoData.buffer.asUint8List();

      // Calculate values
      final subtotal =
          widget.experience.price * widget.booking.participantCount;
      final serviceFee = _calculateServiceFee();
      final taxAmount = _calculateTaxAmount();

      pdf.addPage(
        pw.Page(
          build: (pw.Context context) {
            return pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.start,
              children: [
                // Header with logo
                pw.Row(
                  mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                  children: [
                    pw.Image(
                      pw.MemoryImage(logoBytes),
                      width: 100,
                      height: 50,
                    ),
                    pw.Column(
                      crossAxisAlignment: pw.CrossAxisAlignment.end,
                      children: [
                        pw.Text(
                          'RECEIPT',
                          style: pw.TextStyle(
                            fontSize: 24,
                            fontWeight: pw.FontWeight.bold,
                          ),
                        ),
                        pw.Text(
                          'Receipt #${widget.receipt.receiptNumber}',
                          style: pw.TextStyle(
                            fontSize: 14,
                          ),
                        ),
                        pw.Text(
                          'Date: ${_formatDate(widget.receipt.createdAt)}',
                          style: pw.TextStyle(
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),

                pw.SizedBox(height: 20),

                // Experience details
                pw.Container(
                  padding: pw.EdgeInsets.all(10),
                  decoration: pw.BoxDecoration(
                    border: pw.Border.all(),
                    borderRadius: pw.BorderRadius.circular(5),
                  ),
                  child: pw.Column(
                    crossAxisAlignment: pw.CrossAxisAlignment.start,
                    children: [
                      pw.Text(
                        'Experience Details',
                        style: pw.TextStyle(
                          fontSize: 16,
                          fontWeight: pw.FontWeight.bold,
                        ),
                      ),
                      pw.SizedBox(height: 10),

                      // Details rows
                      _buildPdfDetailRow('Experience', widget.experience.title),
                      _buildPdfDetailRow(
                          'Date', _formatDate(widget.booking.date)),
                      _buildPdfDetailRow(
                          'Time', widget.booking.timeSlot.formattedTime),
                      _buildPdfDetailRow(
                          'Location', widget.experience.location),
                      _buildPdfDetailRow(
                        'Participants',
                        '${widget.booking.participantCount} ${widget.booking.participantCount == 1 ? 'person' : 'people'}',
                      ),
                    ],
                  ),
                ),

                pw.SizedBox(height: 20),

                // Price breakdown
                pw.Container(
                  padding: pw.EdgeInsets.all(10),
                  decoration: pw.BoxDecoration(
                    border: pw.Border.all(),
                    borderRadius: pw.BorderRadius.circular(5),
                  ),
                  child: pw.Column(
                    crossAxisAlignment: pw.CrossAxisAlignment.start,
                    children: [
                      pw.Text(
                        'Price Breakdown',
                        style: pw.TextStyle(
                          fontSize: 16,
                          fontWeight: pw.FontWeight.bold,
                        ),
                      ),
                      pw.SizedBox(height: 10),

                      // Price rows
                      _buildPdfDetailRow(
                        'Base Price',
                        _formatAmount(widget.experience.price),
                      ),
                      _buildPdfDetailRow(
                        'Participants',
                        '× ${widget.booking.participantCount}',
                      ),
                      _buildPdfDetailRow(
                        'Subtotal',
                        _formatAmount(subtotal),
                      ),
                      _buildPdfDetailRow(
                        'Service Fee',
                        _formatAmount(serviceFee),
                      ),
                      _buildPdfDetailRow(
                        'Taxes',
                        _formatAmount(taxAmount),
                      ),

                      pw.Divider(),

                      _buildPdfDetailRow(
                        'Total',
                        _formatAmount(widget.booking.totalAmount),
                        isTotal: true,
                      ),
                    ],
                  ),
                ),

                pw.SizedBox(height: 20),

                // Payment details
                pw.Container(
                  padding: pw.EdgeInsets.all(10),
                  decoration: pw.BoxDecoration(
                    border: pw.Border.all(),
                    borderRadius: pw.BorderRadius.circular(5),
                  ),
                  child: pw.Column(
                    crossAxisAlignment: pw.CrossAxisAlignment.start,
                    children: [
                      pw.Text(
                        'Payment Details',
                        style: pw.TextStyle(
                          fontSize: 16,
                          fontWeight: pw.FontWeight.bold,
                        ),
                      ),
                      pw.SizedBox(height: 10),
                      _buildPdfDetailRow(
                        'Payment Method',
                        widget.receipt.paymentMethodName,
                      ),
                      _buildPdfDetailRow(
                        'Transaction ID',
                        widget.receipt.transactionId,
                      ),
                      _buildPdfDetailRow(
                        'Payment Date',
                        _formatDate(widget.receipt.createdAt),
                      ),
                      _buildPdfDetailRow(
                        'Status',
                        'Paid',
                      ),
                    ],
                  ),
                ),

                pw.SizedBox(height: 20),

                // Footer
                pw.Container(
                  alignment: pw.Alignment.center,
                  child: pw.Column(
                    children: [
                      pw.Text(
                        'Thank you for your purchase!',
                        style: pw.TextStyle(
                          fontSize: 14,
                          fontWeight: pw.FontWeight.bold,
                        ),
                      ),
                      pw.SizedBox(height: 5),
                      pw.Text(
                        'For any questions, <NAME_EMAIL>',
                        style: pw.TextStyle(
                          fontSize: 10,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            );
          },
        ),
      );

      // Save the PDF
      final output = await getTemporaryDirectory();
      final file =
          File('${output.path}/receipt_${widget.receipt.receiptNumber}.pdf');
      await file.writeAsBytes(await pdf.save());

      return file;
    } catch (e) {
      // Log error in a production app
      return null;
    }
  }

  pw.Widget _buildPdfDetailRow(String label, String value,
      {bool isTotal = false}) {
    return pw.Padding(
      padding: pw.EdgeInsets.symmetric(vertical: 2),
      child: pw.Row(
        mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
        children: [
          pw.Text(
            label,
            style: pw.TextStyle(
              fontSize: isTotal ? 14 : 12,
              fontWeight: isTotal ? pw.FontWeight.bold : pw.FontWeight.normal,
            ),
          ),
          pw.Text(
            value,
            style: pw.TextStyle(
              fontSize: isTotal ? 14 : 12,
              fontWeight: isTotal ? pw.FontWeight.bold : pw.FontWeight.normal,
            ),
          ),
        ],
      ),
    );
  }

  pw.Widget _buildPdfDetailRow(String label, String value,
      {bool isTotal = false}) {
    return pw.Padding(
      padding: pw.EdgeInsets.symmetric(vertical: 2),
      child: pw.Row(
        mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
        children: [
          pw.Text(
            label,
            style: pw.TextStyle(
              fontSize: isTotal ? 14 : 12,
              fontWeight: isTotal ? pw.FontWeight.bold : pw.FontWeight.normal,
            ),
          ),
          pw.Text(
            value,
            style: pw.TextStyle(
              fontSize: isTotal ? 14 : 12,
              fontWeight: isTotal ? pw.FontWeight.bold : pw.FontWeight.normal,
            ),
          ),
        ],
      ),
    );
  }
  */
}
