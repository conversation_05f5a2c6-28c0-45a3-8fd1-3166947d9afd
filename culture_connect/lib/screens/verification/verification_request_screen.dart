// Dart SDK imports
import 'dart:io';

// External package imports
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:image_picker/image_picker.dart';

// Project imports
import 'package:culture_connect/models/verification_model.dart';
import 'package:culture_connect/providers/user_verification_provider.dart';
import 'package:culture_connect/services/logging_service.dart';
import 'package:culture_connect/widgets/common/custom_app_bar.dart';
import 'package:culture_connect/widgets/common/app_button.dart';

/// A screen for submitting verification requests
class VerificationRequestScreen extends ConsumerStatefulWidget {
  /// The verification type to request
  final VerificationType verificationType;

  /// Creates a new verification request screen
  const VerificationRequestScreen({
    super.key,
    required this.verificationType,
  });

  @override
  ConsumerState<VerificationRequestScreen> createState() =>
      _VerificationRequestScreenState();
}

class _VerificationRequestScreenState
    extends ConsumerState<VerificationRequestScreen> {
  final _formKey = GlobalKey<FormState>();
  final _notesController = TextEditingController();

  final List<File> _documents = [];
  bool _isLoading = false;
  String? _errorMessage;
  String? _successMessage;

  @override
  void dispose() {
    _notesController.dispose();
    super.dispose();
  }

  /// Submits a verification request with the provided documents and notes
  Future<void> _submitVerificationRequest() async {
    if (_formKey.currentState?.validate() != true) {
      return;
    }

    if (_documents.isEmpty) {
      if (mounted) {
        setState(() {
          _errorMessage = 'Please upload at least one document';
        });
      }
      return;
    }

    if (!mounted) return;

    setState(() {
      _isLoading = true;
      _errorMessage = null;
      _successMessage = null;
    });

    try {
      await ref
          .read(userVerificationProvider.notifier)
          .submitVerificationRequest(
            type: widget.verificationType,
            documents: _documents,
            notes: _notesController.text,
          );

      if (mounted) {
        setState(() {
          _successMessage = 'Verification request submitted successfully';
          _documents.clear();
          _notesController.clear();
        });
      }

      // Refresh verification status
      await ref
          .read(userVerificationProvider.notifier)
          .refreshVerificationStatus();

      LoggingService().info('VerificationRequestScreen',
          'Verification request submitted successfully for type: ${widget.verificationType}');
    } catch (e) {
      LoggingService().error('VerificationRequestScreen',
          'Failed to submit verification request', e);
      if (mounted) {
        setState(() {
          _errorMessage = 'Failed to submit verification request: $e';
        });
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// Picks a document from the device gallery
  Future<void> _pickDocument() async {
    try {
      final picker = ImagePicker();
      final pickedFile = await picker.pickImage(source: ImageSource.gallery);

      if (pickedFile != null && mounted) {
        setState(() {
          _documents.add(File(pickedFile.path));
        });
      }
    } catch (e) {
      LoggingService()
          .error('VerificationRequestScreen', 'Failed to pick document', e);
      if (mounted) {
        setState(() {
          _errorMessage = 'Failed to pick document: $e';
        });
      }
    }
  }

  void _removeDocument(int index) {
    setState(() {
      _documents.removeAt(index);
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      appBar: CustomAppBar(
        title: _getVerificationTypeName(widget.verificationType),
        showBackButton: true,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Info card
              Card(
                elevation: 2,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Verification Information',
                        style: theme.textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 16),
                      Text(
                        _getVerificationDescription(widget.verificationType),
                        style: theme.textTheme.bodyMedium,
                      ),
                    ],
                  ),
                ),
              ),

              const SizedBox(height: 24),

              // Document upload
              Text(
                'Required Documents',
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                _getDocumentRequirements(widget.verificationType),
                style: theme.textTheme.bodyMedium,
              ),
              const SizedBox(height: 16),

              // Document list
              if (_documents.isNotEmpty) ...[
                ListView.builder(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  itemCount: _documents.length,
                  itemBuilder: (context, index) {
                    final document = _documents[index];
                    return ListTile(
                      leading: Image.file(
                        document,
                        width: 50,
                        height: 50,
                        fit: BoxFit.cover,
                      ),
                      title: Text('Document ${index + 1}'),
                      trailing: IconButton(
                        icon: const Icon(Icons.delete),
                        onPressed: () => _removeDocument(index),
                      ),
                    );
                  },
                ),
                const SizedBox(height: 16),
              ],

              // Upload button
              Center(
                child: AppButton(
                  text: 'Upload Document',
                  onPressed: _pickDocument,
                  variant: ButtonVariant.outlined,
                  icon: Icons.upload_file,
                ),
              ),

              const SizedBox(height: 24),

              // Notes
              Text(
                'Additional Notes',
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              TextFormField(
                controller: _notesController,
                decoration: const InputDecoration(
                  hintText:
                      'Add any additional information that might help with verification',
                  border: OutlineInputBorder(),
                ),
                maxLines: 3,
              ),

              const SizedBox(height: 24),

              // Error message
              if (_errorMessage != null) ...[
                Text(
                  _errorMessage!,
                  style: TextStyle(
                    color: theme.colorScheme.error,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 16),
              ],

              // Success message
              if (_successMessage != null) ...[
                Text(
                  _successMessage!,
                  style: TextStyle(
                    color: Colors.green[700],
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 16),
              ],

              // Submit button
              Center(
                child: AppButton(
                  text: 'Submit Verification Request',
                  onPressed: _submitVerificationRequest,
                  isLoading: _isLoading,
                  variant: ButtonVariant.filled,
                ),
              ),

              const SizedBox(height: 24),

              // Privacy notice
              Card(
                elevation: 2,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Privacy Notice',
                        style: theme.textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'Your documents will be securely stored and only used for verification purposes. '
                        'They will be reviewed by our verification team and will not be shared with third parties. '
                        'By submitting this form, you consent to the verification process.',
                        style: theme.textTheme.bodyMedium,
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  String _getVerificationTypeName(VerificationType type) {
    switch (type) {
      case VerificationType.identity:
        return 'Identity Verification';
      case VerificationType.professional:
        return 'Professional Certification';
      case VerificationType.background:
        return 'Background Check';
      case VerificationType.address:
        return 'Address Verification';
      case VerificationType.phone:
        return 'Phone Verification';
      case VerificationType.email:
        return 'Email Verification';
      case VerificationType.social:
        return 'Social Media Verification';
      case VerificationType.license:
        return 'Government License';
    }
  }

  String _getVerificationDescription(VerificationType type) {
    switch (type) {
      case VerificationType.identity:
        return 'Identity verification confirms your identity using government-issued ID. '
            'This is required for the Standard verification level and helps build trust with other users.';
      case VerificationType.professional:
        return 'Professional certification verifies your professional qualifications and credentials. '
            'This is required for the Certified verification level and helps establish your expertise.';
      case VerificationType.background:
        return 'Background checks verify your identity and check for any criminal records. '
            'This is required for the Premium verification level and helps ensure safety.';
      case VerificationType.address:
        return 'Address verification confirms your current residential address. '
            'This is required for the Enhanced verification level and helps establish trust.';
      case VerificationType.phone:
        return 'Phone verification confirms that you have access to the phone number on your account. '
            'This is required for the Basic verification level and helps secure your account.';
      case VerificationType.email:
        return 'Email verification confirms that you have access to the email address on your account. '
            'This is required for the Basic verification level and helps secure your account.';
      case VerificationType.social:
        return 'Social media verification links your account to your social media profiles. '
            'This helps establish your online presence and build trust with other users.';
      case VerificationType.license:
        return 'Government license verification confirms that you hold valid professional licenses. '
            'This helps establish your credentials and qualifications.';
    }
  }

  String _getDocumentRequirements(VerificationType type) {
    switch (type) {
      case VerificationType.identity:
        return 'Please upload a clear photo of your government-issued ID (passport, driver\'s license, or national ID card). '
            'Make sure all information is clearly visible.';
      case VerificationType.professional:
        return 'Please upload your professional certificates, diplomas, or other credentials. '
            'Include any relevant licenses or certifications.';
      case VerificationType.background:
        return 'Please upload a government-issued ID and any additional documents requested by the background check provider.';
      case VerificationType.address:
        return 'Please upload a recent utility bill, bank statement, or government document showing your name and current address. '
            'The document should be less than 3 months old.';
      case VerificationType.phone:
        return 'Please follow the instructions sent to your phone to complete verification.';
      case VerificationType.email:
        return 'Please follow the instructions sent to your email to complete verification.';
      case VerificationType.social:
        return 'Please upload screenshots showing your social media profiles with your name and photo visible.';
      case VerificationType.license:
        return 'Please upload clear photos of your professional licenses or permits. '
            'Make sure all information, including expiration dates, is clearly visible.';
    }
  }
}
