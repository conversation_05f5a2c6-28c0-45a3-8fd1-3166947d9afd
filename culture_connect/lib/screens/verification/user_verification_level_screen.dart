// External package imports
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

// Project imports
import 'package:culture_connect/models/user_verification_level.dart';
import 'package:culture_connect/models/verification_model.dart';
import 'package:culture_connect/providers/user_verification_provider.dart';
import 'package:culture_connect/widgets/verification/verification_badge.dart';
import 'package:culture_connect/widgets/common/custom_app_bar.dart';
import 'package:culture_connect/widgets/common/app_button.dart';

/// A screen for managing user verification levels
class UserVerificationLevelScreen extends ConsumerWidget {
  /// Creates a new user verification level screen
  const UserVerificationLevelScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final verificationStatus = ref.watch(userVerificationProvider);
    final verificationLevel = ref.watch(userVerificationLevelProvider);
    final canUpgrade = ref.watch(canUpgradeVerificationProvider);

    return Scaffold(
      appBar: const CustomAppBar(
        title: 'Verification Level',
        showBackButton: true,
      ),
      body: verificationStatus == null
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Current verification level
                  _buildVerificationLevelCard(context, verificationLevel),

                  const SizedBox(height: 24),

                  // Verification progress
                  _buildVerificationProgressCard(context, verificationLevel),

                  const SizedBox(height: 24),

                  // Completed verifications
                  _buildCompletedVerificationsCard(
                      context, verificationStatus.badges),

                  const SizedBox(height: 24),

                  // Pending verifications
                  if (verificationStatus.pendingRequests.isNotEmpty)
                    _buildPendingVerificationsCard(
                        context, verificationStatus.pendingRequests),

                  const SizedBox(height: 24),

                  // Upgrade button
                  if (canUpgrade)
                    _buildUpgradeButton(context, ref, verificationLevel),

                  const SizedBox(height: 24),

                  // Verification levels explanation
                  _buildVerificationLevelsExplanation(context),
                ],
              ),
            ),
    );
  }

  Widget _buildVerificationLevelCard(
      BuildContext context, UserVerificationLevel level) {
    final theme = Theme.of(context);

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  level.icon,
                  size: 48,
                  color: level.color,
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Current Level',
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: Colors.grey[600],
                        ),
                      ),
                      Text(
                        level.displayName,
                        style: theme.textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: level.color,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Text(
              level.description,
              style: theme.textTheme.bodyMedium,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildVerificationProgressCard(
      BuildContext context, UserVerificationLevel level) {
    final theme = Theme.of(context);
    final nextLevel = level.nextLevel;

    // Check if this is the highest level
    if (level == UserVerificationLevel.certified) {
      return Card(
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Verification Progress',
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),
              Text(
                'Congratulations! You have reached the highest verification level.',
                style: theme.textTheme.bodyMedium,
              ),
            ],
          ),
        ),
      );
    }

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Verification Progress',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Icon(
                  level.icon,
                  size: 24,
                  color: level.color,
                ),
                Expanded(
                  child: Container(
                    height: 4,
                    margin: const EdgeInsets.symmetric(horizontal: 8),
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [
                          level.color,
                          nextLevel.color.withAlpha(76),
                        ],
                      ),
                      borderRadius: BorderRadius.circular(2),
                    ),
                  ),
                ),
                Icon(
                  nextLevel.icon,
                  size: 24,
                  color: nextLevel.color.withAlpha(128),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Text(
              'Next Level: ${nextLevel.displayName}',
              style: theme.textTheme.titleSmall?.copyWith(
                color: nextLevel.color,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              nextLevel.description,
              style: theme.textTheme.bodyMedium,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCompletedVerificationsCard(
      BuildContext context, List<VerificationBadge> badges) {
    final theme = Theme.of(context);

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Completed Verifications',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            badges.isEmpty
                ? Text(
                    'You have not completed any verifications yet.',
                    style: theme.textTheme.bodyMedium,
                  )
                : VerificationBadgesList(
                    badges: badges,
                    showDescription: true,
                    showStatus: true,
                    showExpiry: true,
                    emptyState: Text(
                      'You have not completed any verifications yet.',
                      style: theme.textTheme.bodyMedium,
                    ),
                  ),
          ],
        ),
      ),
    );
  }

  Widget _buildPendingVerificationsCard(
      BuildContext context, List<VerificationRequest> requests) {
    final theme = Theme.of(context);

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Pending Verifications',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            ListView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: requests.length,
              itemBuilder: (context, index) {
                final request = requests[index];
                return ListTile(
                  leading: Icon(
                    _getVerificationTypeIcon(request.type),
                    color: Colors.orange,
                  ),
                  title: Text(
                    _getVerificationTypeName(request.type),
                    style: theme.textTheme.titleSmall,
                  ),
                  subtitle: Text(
                    'Submitted on ${_formatDate(request.submittedAt)}',
                    style: theme.textTheme.bodySmall,
                  ),
                  trailing: const Chip(
                    label: Text('Pending'),
                    backgroundColor: Colors.orange,
                    labelStyle: TextStyle(color: Colors.white),
                  ),
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildUpgradeButton(
      BuildContext context, WidgetRef ref, UserVerificationLevel level) {
    final nextLevel = level.nextLevel;

    final nextVerificationType =
        _getNextRequiredVerificationType(level, nextLevel);
    if (nextVerificationType == null) return const SizedBox.shrink();

    return Center(
      child: AppButton(
        text: 'Upgrade to ${nextLevel.displayName}',
        onPressed: () {
          _handleUpgrade(context, ref, nextVerificationType);
        },
        variant: ButtonVariant.filled,
      ),
    );
  }

  void _handleUpgrade(
      BuildContext context, WidgetRef ref, VerificationType type) {
    switch (type) {
      case VerificationType.identity:
      case VerificationType.address:
      case VerificationType.professional:
      case VerificationType.license:
      case VerificationType.social:
        Navigator.pushNamed(context, '/verification/request', arguments: type);
        break;
      case VerificationType.background:
        Navigator.pushNamed(context, '/verification/background-check');
        break;
      case VerificationType.email:
        Navigator.pushNamed(context, '/verification/email');
        break;
      case VerificationType.phone:
        Navigator.pushNamed(context, '/verification/phone');
        break;
    }
  }

  Widget _buildVerificationLevelsExplanation(BuildContext context) {
    final theme = Theme.of(context);

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Verification Levels Explained',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            ...UserVerificationLevel.values.map((level) => Padding(
                  padding: const EdgeInsets.only(bottom: 16),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Icon(
                        level.icon,
                        size: 24,
                        color: level.color,
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              level.displayName,
                              style: theme.textTheme.titleSmall?.copyWith(
                                color: level.color,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              level.description,
                              style: theme.textTheme.bodySmall,
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                )),
          ],
        ),
      ),
    );
  }

  VerificationType? _getNextRequiredVerificationType(
    UserVerificationLevel currentLevel,
    UserVerificationLevel nextLevel,
  ) {
    final currentRequiredTypes = currentLevel.requiredVerifications.toSet();
    final nextRequiredTypes = nextLevel.requiredVerifications.toSet();

    // Find the verification types that are required for the next level but not for the current level
    final newRequiredTypes = nextRequiredTypes.difference(currentRequiredTypes);

    // Return the first new required verification type
    return newRequiredTypes.isNotEmpty ? newRequiredTypes.first : null;
  }

  IconData _getVerificationTypeIcon(VerificationType type) {
    switch (type) {
      case VerificationType.identity:
        return Icons.badge;
      case VerificationType.professional:
        return Icons.school;
      case VerificationType.background:
        return Icons.security;
      case VerificationType.address:
        return Icons.home;
      case VerificationType.phone:
        return Icons.phone;
      case VerificationType.email:
        return Icons.email;
      case VerificationType.social:
        return Icons.people;
      case VerificationType.license:
        return Icons.card_membership;
    }
  }

  String _getVerificationTypeName(VerificationType type) {
    switch (type) {
      case VerificationType.identity:
        return 'Identity Verification';
      case VerificationType.professional:
        return 'Professional Certification';
      case VerificationType.background:
        return 'Background Check';
      case VerificationType.address:
        return 'Address Verification';
      case VerificationType.phone:
        return 'Phone Verification';
      case VerificationType.email:
        return 'Email Verification';
      case VerificationType.social:
        return 'Social Media Verification';
      case VerificationType.license:
        return 'Government License';
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}
