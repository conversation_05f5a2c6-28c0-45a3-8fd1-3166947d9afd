// Dart SDK imports
import 'dart:io';

// External package imports
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:image_picker/image_picker.dart';
import 'package:url_launcher/url_launcher.dart';

// Project imports
import 'package:culture_connect/services/background_check_service.dart';
import 'package:culture_connect/services/logging_service.dart';
import 'package:culture_connect/providers/user_verification_provider.dart';
import 'package:culture_connect/providers/auth_provider.dart';
import 'package:culture_connect/widgets/common/custom_app_bar.dart';
import 'package:culture_connect/widgets/common/app_button.dart';

/// A screen for requesting and managing background checks
class BackgroundCheckScreen extends ConsumerStatefulWidget {
  /// Creates a new background check screen
  const BackgroundCheckScreen({super.key});

  @override
  ConsumerState<BackgroundCheckScreen> createState() =>
      _BackgroundCheckScreenState();
}

class _BackgroundCheckScreenState extends ConsumerState<BackgroundCheckScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final _formKey = GlobalKey<FormState>();

  BackgroundCheckProvider _selectedProvider = BackgroundCheckProvider.checkr;
  BackgroundCheckType _selectedType = BackgroundCheckType.basic;
  final List<File> _documents = [];
  final Map<String, TextEditingController> _controllers = {
    'firstName': TextEditingController(),
    'lastName': TextEditingController(),
    'dateOfBirth': TextEditingController(),
    'ssn': TextEditingController(),
    'address': TextEditingController(),
    'city': TextEditingController(),
    'state': TextEditingController(),
    'zipCode': TextEditingController(),
  };

  bool _isLoading = false;
  String? _errorMessage;
  String? _successMessage;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);

    // Pre-fill form with user data
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final user = ref.read(authStateProvider).user;
      if (user != null) {
        _controllers['firstName']!.text = user.firstName;
        _controllers['lastName']!.text = user.lastName;
      }
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    for (final controller in _controllers.values) {
      controller.dispose();
    }
    super.dispose();
  }

  /// Requests a background check with the provided information
  Future<void> _requestBackgroundCheck() async {
    if (_formKey.currentState?.validate() != true) {
      return;
    }

    if (_documents.isEmpty) {
      if (mounted) {
        setState(() {
          _errorMessage = 'Please upload at least one identification document';
        });
      }
      return;
    }

    if (!mounted) return;

    setState(() {
      _isLoading = true;
      _errorMessage = null;
      _successMessage = null;
    });

    try {
      // Upload documents and get URLs
      // In a real app, we would upload the documents to a server
      // For this example, we'll use mock URLs
      final documentUrls = _documents
          .map((doc) =>
              'https://example.com/documents/${DateTime.now().millisecondsSinceEpoch}_${doc.path.split('/').last}')
          .toList();

      // Prepare user data
      final userData = {
        'firstName': _controllers['firstName']!.text,
        'lastName': _controllers['lastName']!.text,
        'dateOfBirth': _controllers['dateOfBirth']!.text,
        'ssn': _controllers['ssn']!.text,
        'address': _controllers['address']!.text,
        'city': _controllers['city']!.text,
        'state': _controllers['state']!.text,
        'zipCode': _controllers['zipCode']!.text,
      };

      // Request background check
      await ref.read(userVerificationProvider.notifier).requestBackgroundCheck(
            provider: _selectedProvider,
            type: _selectedType,
            userData: userData,
            documentUrls: documentUrls,
          );

      if (mounted) {
        setState(() {
          _successMessage = 'Background check request submitted successfully';
          _documents.clear();
          _tabController.animateTo(1); // Switch to history tab
        });
      }

      // Refresh verification status
      await ref
          .read(userVerificationProvider.notifier)
          .refreshVerificationStatus();

      LoggingService().info('BackgroundCheckScreen',
          'Background check request submitted successfully');
    } catch (e) {
      LoggingService().error(
          'BackgroundCheckScreen', 'Failed to request background check', e);
      if (mounted) {
        setState(() {
          _errorMessage = 'Failed to request background check: $e';
        });
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// Picks a document from the device gallery
  Future<void> _pickDocument() async {
    try {
      final picker = ImagePicker();
      final pickedFile = await picker.pickImage(source: ImageSource.gallery);

      if (pickedFile != null && mounted) {
        setState(() {
          _documents.add(File(pickedFile.path));
        });
      }
    } catch (e) {
      LoggingService()
          .error('BackgroundCheckScreen', 'Failed to pick document', e);
      if (mounted) {
        setState(() {
          _errorMessage = 'Failed to pick document: $e';
        });
      }
    }
  }

  void _removeDocument(int index) {
    setState(() {
      _documents.removeAt(index);
    });
  }

  Future<void> _openReport(String reportUrl) async {
    final Uri url = Uri.parse(reportUrl);
    if (await canLaunchUrl(url)) {
      await launchUrl(url);
    } else {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Could not open report: $reportUrl')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      appBar: CustomAppBar(
        title: 'Background Check',
        showBackButton: true,
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'Request'),
            Tab(text: 'History'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildRequestTab(theme),
          _buildHistoryTab(theme),
        ],
      ),
    );
  }

  Widget _buildRequestTab(ThemeData theme) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Info card
            Card(
              elevation: 2,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Background Check Information',
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'A background check is required for Premium verification level. '
                      'The check will verify your identity and check for any criminal records. '
                      'This process typically takes 3-5 business days.',
                      style: theme.textTheme.bodyMedium,
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 24),

            // Provider selection
            Text(
              'Background Check Provider',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            DropdownButtonFormField<BackgroundCheckProvider>(
              value: _selectedProvider,
              decoration: const InputDecoration(
                border: OutlineInputBorder(),
                contentPadding:
                    EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              ),
              items: BackgroundCheckProvider.values.map((provider) {
                return DropdownMenuItem<BackgroundCheckProvider>(
                  value: provider,
                  child: Text(_getProviderName(provider)),
                );
              }).toList(),
              onChanged: (value) {
                if (value != null) {
                  setState(() {
                    _selectedProvider = value;
                  });
                }
              },
            ),

            const SizedBox(height: 16),

            // Type selection
            Text(
              'Background Check Type',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            DropdownButtonFormField<BackgroundCheckType>(
              value: _selectedType,
              decoration: const InputDecoration(
                border: OutlineInputBorder(),
                contentPadding:
                    EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              ),
              items: BackgroundCheckType.values.map((type) {
                return DropdownMenuItem<BackgroundCheckType>(
                  value: type,
                  child: Text(_getTypeName(type)),
                );
              }).toList(),
              onChanged: (value) {
                if (value != null) {
                  setState(() {
                    _selectedType = value;
                  });
                }
              },
            ),

            const SizedBox(height: 24),

            // Personal information
            Text(
              'Personal Information',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),

            // First name
            TextFormField(
              controller: _controllers['firstName'],
              decoration: const InputDecoration(
                labelText: 'First Name',
                border: OutlineInputBorder(),
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please enter your first name';
                }
                return null;
              },
            ),

            const SizedBox(height: 16),

            // Last name
            TextFormField(
              controller: _controllers['lastName'],
              decoration: const InputDecoration(
                labelText: 'Last Name',
                border: OutlineInputBorder(),
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please enter your last name';
                }
                return null;
              },
            ),

            const SizedBox(height: 16),

            // Date of birth
            TextFormField(
              controller: _controllers['dateOfBirth'],
              decoration: const InputDecoration(
                labelText: 'Date of Birth (MM/DD/YYYY)',
                border: OutlineInputBorder(),
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please enter your date of birth';
                }
                // Simple date format validation
                final dateRegex = RegExp(r'^\d{2}/\d{2}/\d{4}$');
                if (!dateRegex.hasMatch(value)) {
                  return 'Please enter a valid date in MM/DD/YYYY format';
                }
                return null;
              },
            ),

            const SizedBox(height: 16),

            // SSN (last 4 digits)
            TextFormField(
              controller: _controllers['ssn'],
              decoration: const InputDecoration(
                labelText: 'Last 4 digits of SSN',
                border: OutlineInputBorder(),
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please enter the last 4 digits of your SSN';
                }
                if (value.length != 4 || !RegExp(r'^\d{4}$').hasMatch(value)) {
                  return 'Please enter exactly 4 digits';
                }
                return null;
              },
              keyboardType: TextInputType.number,
              maxLength: 4,
            ),

            const SizedBox(height: 16),

            // Address
            TextFormField(
              controller: _controllers['address'],
              decoration: const InputDecoration(
                labelText: 'Street Address',
                border: OutlineInputBorder(),
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please enter your street address';
                }
                return null;
              },
            ),

            const SizedBox(height: 16),

            // City
            TextFormField(
              controller: _controllers['city'],
              decoration: const InputDecoration(
                labelText: 'City',
                border: OutlineInputBorder(),
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please enter your city';
                }
                return null;
              },
            ),

            const SizedBox(height: 16),

            // State
            TextFormField(
              controller: _controllers['state'],
              decoration: const InputDecoration(
                labelText: 'State',
                border: OutlineInputBorder(),
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please enter your state';
                }
                return null;
              },
            ),

            const SizedBox(height: 16),

            // Zip code
            TextFormField(
              controller: _controllers['zipCode'],
              decoration: const InputDecoration(
                labelText: 'Zip Code',
                border: OutlineInputBorder(),
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please enter your zip code';
                }
                if (!RegExp(r'^\d{5}(-\d{4})?$').hasMatch(value)) {
                  return 'Please enter a valid zip code';
                }
                return null;
              },
              keyboardType: TextInputType.number,
            ),

            const SizedBox(height: 24),

            // Document upload
            Text(
              'Identification Documents',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Please upload a government-issued ID (passport, driver\'s license, etc.)',
              style: theme.textTheme.bodyMedium,
            ),
            const SizedBox(height: 16),

            // Document list
            if (_documents.isNotEmpty) ...[
              ListView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: _documents.length,
                itemBuilder: (context, index) {
                  final document = _documents[index];
                  return ListTile(
                    leading: Image.file(
                      document,
                      width: 50,
                      height: 50,
                      fit: BoxFit.cover,
                    ),
                    title: Text('Document ${index + 1}'),
                    trailing: IconButton(
                      icon: const Icon(Icons.delete),
                      onPressed: () => _removeDocument(index),
                    ),
                  );
                },
              ),
              const SizedBox(height: 16),
            ],

            // Upload button
            Center(
              child: AppButton(
                text: 'Upload Document',
                onPressed: _pickDocument,
                variant: ButtonVariant.outlined,
                icon: Icons.upload_file,
              ),
            ),

            const SizedBox(height: 24),

            // Error message
            if (_errorMessage != null) ...[
              Text(
                _errorMessage!,
                style: TextStyle(
                  color: theme.colorScheme.error,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),
            ],

            // Success message
            if (_successMessage != null) ...[
              Text(
                _successMessage!,
                style: TextStyle(
                  color: Colors.green[700],
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),
            ],

            // Submit button
            Center(
              child: AppButton(
                text: 'Request Background Check',
                onPressed: _requestBackgroundCheck,
                isLoading: _isLoading,
                variant: ButtonVariant.filled,
              ),
            ),

            const SizedBox(height: 24),

            // Privacy notice
            Card(
              elevation: 2,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Privacy Notice',
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'Your personal information will be securely transmitted to the background check provider. '
                      'We do not store your SSN or other sensitive information. '
                      'By submitting this form, you consent to a background check being performed.',
                      style: theme.textTheme.bodyMedium,
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHistoryTab(ThemeData theme) {
    final backgroundChecksAsync = ref.watch(backgroundChecksProvider);

    return backgroundChecksAsync.when(
      data: (backgroundChecks) {
        if (backgroundChecks.isEmpty) {
          return Center(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.history,
                    size: 64,
                    color: Colors.grey[400],
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'No background checks found',
                    style: theme.textTheme.titleMedium,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'You have not requested any background checks yet.',
                    style: theme.textTheme.bodyMedium,
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          );
        }

        return ListView.builder(
          padding: const EdgeInsets.all(16),
          itemCount: backgroundChecks.length,
          itemBuilder: (context, index) {
            final check = backgroundChecks[index];
            return Card(
              elevation: 2,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(
                          _getStatusIcon(check.status),
                          color: _getStatusColor(check.status),
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            '${_getTypeName(check.type)} Background Check',
                            style: theme.textTheme.titleMedium?.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                        Chip(
                          label: Text(_getStatusName(check.status)),
                          backgroundColor:
                              _getStatusColor(check.status).withAlpha(25),
                          labelStyle: TextStyle(
                            color: _getStatusColor(check.status),
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'Provider: ${_getProviderName(check.provider)}',
                      style: theme.textTheme.bodyMedium,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Requested: ${_formatDate(check.requestedAt)}',
                      style: theme.textTheme.bodyMedium,
                    ),
                    if (check.completedAt != null) ...[
                      const SizedBox(height: 8),
                      Text(
                        'Completed: ${_formatDate(check.completedAt!)}',
                        style: theme.textTheme.bodyMedium,
                      ),
                    ],
                    if (check.result != null) ...[
                      const SizedBox(height: 8),
                      Text(
                        'Result: ${check.result!.toUpperCase()}',
                        style: theme.textTheme.bodyMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: check.result == 'pass'
                              ? Colors.green[700]
                              : check.result == 'consider'
                                  ? Colors.orange[700]
                                  : Colors.red[700],
                        ),
                      ),
                    ],
                    if (check.reportUrl != null) ...[
                      const SizedBox(height: 16),
                      Center(
                        child: AppButton(
                          text: 'View Report',
                          onPressed: () => _openReport(check.reportUrl!),
                          variant: ButtonVariant.outlined,
                          icon: Icons.description,
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            );
          },
        );
      },
      loading: () => const Center(
        child: CircularProgressIndicator(),
      ),
      error: (error, stackTrace) => Center(
        child: Text(
          'Error loading background checks: $error',
          style: TextStyle(color: theme.colorScheme.error),
        ),
      ),
    );
  }

  String _getProviderName(BackgroundCheckProvider provider) {
    switch (provider) {
      case BackgroundCheckProvider.checkr:
        return 'Checkr';
      case BackgroundCheckProvider.sterling:
        return 'Sterling';
      case BackgroundCheckProvider.hireRight:
        return 'HireRight';
      case BackgroundCheckProvider.goodHire:
        return 'GoodHire';
    }
  }

  String _getTypeName(BackgroundCheckType type) {
    switch (type) {
      case BackgroundCheckType.basic:
        return 'Basic';
      case BackgroundCheckType.standard:
        return 'Standard';
      case BackgroundCheckType.enhanced:
        return 'Enhanced';
      case BackgroundCheckType.comprehensive:
        return 'Comprehensive';
    }
  }

  String _getStatusName(BackgroundCheckStatus status) {
    switch (status) {
      case BackgroundCheckStatus.pending:
        return 'Pending';
      case BackgroundCheckStatus.inProgress:
        return 'In Progress';
      case BackgroundCheckStatus.completed:
        return 'Completed';
      case BackgroundCheckStatus.failed:
        return 'Failed';
      case BackgroundCheckStatus.disputed:
        return 'Disputed';
    }
  }

  IconData _getStatusIcon(BackgroundCheckStatus status) {
    switch (status) {
      case BackgroundCheckStatus.pending:
        return Icons.hourglass_empty;
      case BackgroundCheckStatus.inProgress:
        return Icons.hourglass_bottom;
      case BackgroundCheckStatus.completed:
        return Icons.check_circle;
      case BackgroundCheckStatus.failed:
        return Icons.error;
      case BackgroundCheckStatus.disputed:
        return Icons.gavel;
    }
  }

  Color _getStatusColor(BackgroundCheckStatus status) {
    switch (status) {
      case BackgroundCheckStatus.pending:
        return Colors.orange;
      case BackgroundCheckStatus.inProgress:
        return Colors.blue;
      case BackgroundCheckStatus.completed:
        return Colors.green;
      case BackgroundCheckStatus.failed:
        return Colors.red;
      case BackgroundCheckStatus.disputed:
        return Colors.purple;
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}
