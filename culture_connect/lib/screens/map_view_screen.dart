import 'dart:async';
import 'dart:ui' as ui;
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:culture_connect/widgets/experience_card.dart';
import 'package:culture_connect/providers/experience_provider.dart';
import 'package:culture_connect/screens/experience_details_screen.dart';
import 'package:culture_connect/models/experience.dart';
import 'package:culture_connect/services/voice_service.dart';
import 'package:culture_connect/services/marker_service.dart';
import 'package:culture_connect/services/cluster_service.dart';
import 'package:culture_connect/services/location_service.dart';
import 'package:culture_connect/widgets/custom_info_window.dart';
import 'package:culture_connect/widgets/map_style_selector.dart';
import 'package:culture_connect/widgets/location_permission_dialog.dart';
import 'package:connectivity_plus/connectivity_plus.dart';

/// A screen that displays cultural experiences on an interactive map.
///
/// The MapViewScreen provides a map-based interface for exploring cultural experiences
/// in different locations. It includes features such as:
///
/// - Interactive Google Maps integration with custom markers
/// - Marker clustering for better visualization when zoomed out
/// - Experience filtering by category and search terms
/// - Custom info windows for selected experiences
/// - Location tracking and permissions management
/// - Offline map caching for use without internet connection
/// - Route planning and directions to experiences
/// - Map style customization options
///
/// This screen is one of the main navigation tabs in the app and provides
/// a geographic way to discover cultural experiences.
class MapViewScreen extends ConsumerStatefulWidget {
  /// Creates a MapViewScreen.
  ///
  /// This screen doesn't require any parameters as it loads experiences
  /// from the global state providers.
  const MapViewScreen({super.key});

  @override
  ConsumerState<MapViewScreen> createState() => _MapViewScreenState();
}

/// The state for the MapViewScreen.
///
/// This class manages the state and behavior of the MapViewScreen, including
/// map interactions, experience filtering, location tracking, and offline features.
class _MapViewScreenState extends ConsumerState<MapViewScreen> {
  /// Controller for the Google Map widget
  GoogleMapController? _mapController;

  /// Set of markers displayed on the map
  final Set<Marker> _markers = {};

  /// Set of polylines displayed on the map (for routes)
  final Set<Polyline> _polylines = {};

  /// Whether to show the map view (true) or list view (false)
  bool _isMapView = true;

  /// Controller for the search text field
  final TextEditingController _searchController = TextEditingController();

  /// Currently selected category filter
  String _selectedCategory = 'All';

  /// Service for voice search functionality
  final VoiceService _voiceService = VoiceService();

  /// Whether voice listening is currently active
  bool _isListening = false;

  /// Currently selected experience (for displaying info window)
  Experience? _selectedExperience;

  /// Current camera position of the map
  CameraPosition? _currentCameraPosition;

  /// Current zoom level of the map
  double _currentZoomLevel = 12.0;

  /// Whether the device is currently offline
  bool _isOffline = false;

  /// Whether location permission has been requested
  bool _isLocationPermissionRequested = false;

  /// Subscription for connectivity status changes
  StreamSubscription<ConnectivityResult>? _connectivitySubscription;

  /// Timer for periodic location updates
  Timer? _locationUpdateTimer;

  final List<String> _categories = [
    'All',
    'Cultural Tours',
    'Cooking Classes',
    'Art & Craft',
    'Music & Dance',
    'Language Exchange',
    'Local Events',
  ];

  @override
  void initState() {
    super.initState();
    _initializeVoiceService();
    _initializeLocationService();
    _checkConnectivity();
    _setupConnectivityListener();
    _loadMapStyle();
  }

  Future<void> _initializeVoiceService() async {
    await _voiceService.initialize();
    _voiceService.recognizedWords.listen((text) {
      if (text.isNotEmpty) {
        setState(() {
          _searchController.text = text;
        });
      }
    });
  }

  Future<void> _initializeLocationService() async {
    final locationService = ref.read(locationServiceProvider);

    // Start location tracking
    final success = await locationService.startLocationTracking();

    if (!success && !_isLocationPermissionRequested) {
      _isLocationPermissionRequested = true;

      // Show permission dialog
      if (mounted) {
        final result = await showDialog<bool>(
          context: context,
          builder: (context) => const LocationPermissionDialog(),
        );

        if (result == true) {
          // User granted permission, try again
          await locationService.startLocationTracking();
        }
      }
    }

    // Listen for location updates
    _locationUpdateTimer = Timer.periodic(const Duration(seconds: 5), (_) {
      if (mounted && _mapController != null) {
        _updateCurrentLocation();
      }
    });
  }

  Future<void> _updateCurrentLocation() async {
    final locationService = ref.read(locationServiceProvider);
    final position = await locationService.getCurrentPosition();

    if (mounted && _mapController != null) {
      // Update camera position if in follow mode
      if (_isFollowingUser) {
        _mapController!.animateCamera(
          CameraUpdate.newLatLng(
            LatLng(position.latitude, position.longitude),
          ),
        );
      }
    }
  }

  Future<void> _checkConnectivity() async {
    final connectivityResult = await Connectivity().checkConnectivity();
    setState(() {
      _isOffline = connectivityResult == ConnectivityResult.none;
    });
  }

  void _setupConnectivityListener() {
    _connectivitySubscription =
        Connectivity().onConnectivityChanged.listen((result) {
      setState(() {
        _isOffline = result == ConnectivityResult.none;
      });

      if (!_isOffline) {
        // Refresh data when back online
        ref.read(experiencesProvider.notifier).refreshExperiences();
      }
    });
  }

  Future<void> _loadMapStyle() async {
    if (_mapController == null) return;

    final locationService = ref.read(locationServiceProvider);
    final selectedStyle = ref.read(selectedMapStyleProvider);

    final style = await locationService.loadMapStyle(selectedStyle);
    if (style != null) {
      await _mapController!.setMapStyle(style);
    }
  }

  Future<void> _startVoiceSearch() async {
    if (!_isListening) {
      setState(() {
        _isListening = true;
      });
      await _voiceService.startListening();
    } else {
      _voiceService.stopListening();
      setState(() {
        _isListening = false;
      });
    }
  }

  bool get _isFollowingUser => false; // This will be implemented later

  @override
  void dispose() {
    _searchController.dispose();
    _voiceService.stopListening();
    _connectivitySubscription?.cancel();
    _locationUpdateTimer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final experiencesAsync = ref.watch(experiencesProvider);

    return Scaffold(
      body: Stack(
        children: [
          // Map View
          experiencesAsync.when(
            data: (experiences) {
              // Filter experiences based on search and category
              final filteredExperiences = _filterExperiences(experiences);
              _updateMarkers(filteredExperiences);

              return GoogleMap(
                initialCameraPosition: const CameraPosition(
                  target: LatLng(6.5244, 3.3792), // Lagos coordinates
                  zoom: 12,
                ),
                markers: _markers,
                polylines: _polylines, // Add polylines for routes
                onMapCreated: (controller) {
                  _mapController = controller;
                  _loadMapStyle(); // Apply map style when map is created
                },
                onCameraMove: (position) {
                  _currentCameraPosition = position;
                  _currentZoomLevel = position.zoom;
                  if (_selectedExperience != null) {
                    setState(() {
                      _selectedExperience = null;
                    });
                  }
                },
                onCameraIdle: () {
                  if (_currentCameraPosition != null) {
                    _updateMarkers(filteredExperiences);
                  }
                },
                myLocationEnabled: true, // Show current location
                myLocationButtonEnabled: false, // We'll use our custom button
                zoomControlsEnabled: false,
                mapToolbarEnabled: false,
                compassEnabled: true,
                trafficEnabled: false,
                indoorViewEnabled: true,
                buildingsEnabled: true,
              );
            },
            loading: () => const Center(
              child: CircularProgressIndicator(),
            ),
            error: (error, stackTrace) => Center(
              child: Text('Error: $error'),
            ),
          ),

          // Map Controls
          Positioned(
            top: 100,
            right: 16,
            child: Column(
              children: [
                // Current location button
                FloatingActionButton.small(
                  heroTag: 'current_location',
                  onPressed: _goToCurrentLocation,
                  backgroundColor: theme.colorScheme.surface,
                  child: Icon(
                    Icons.my_location,
                    color: theme.colorScheme.primary,
                  ),
                ),
                const SizedBox(height: 8),

                // Route planning button
                FloatingActionButton.small(
                  heroTag: 'route_planning',
                  onPressed: _toggleRoutePlanning,
                  backgroundColor: theme.colorScheme.surface,
                  child: Icon(
                    Icons.directions,
                    color: theme.colorScheme.primary,
                  ),
                ),
                const SizedBox(height: 8),

                // Map style button
                FloatingActionButton.small(
                  heroTag: 'map_style',
                  onPressed: () {
                    showModalBottomSheet(
                      context: context,
                      builder: (context) {
                        return Padding(
                          padding: const EdgeInsets.all(16.0),
                          child: Column(
                            mainAxisSize: MainAxisSize.min,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const Text(
                                'Map Style',
                                style: TextStyle(
                                  fontSize: 20,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              const SizedBox(height: 16),
                              SizedBox(
                                height: 100,
                                child: ListView(
                                  scrollDirection: Axis.horizontal,
                                  children: [
                                    'standard',
                                    'silver',
                                    'retro',
                                    'dark',
                                    'night',
                                    'aubergine',
                                  ].map((style) {
                                    final isSelected =
                                        ref.read(selectedMapStyleProvider) ==
                                            style;
                                    return Padding(
                                      padding: const EdgeInsets.only(right: 8),
                                      child: GestureDetector(
                                        onTap: () {
                                          ref
                                              .read(selectedMapStyleProvider
                                                  .notifier)
                                              .state = style;
                                          _loadMapStyle();
                                          Navigator.pop(context);
                                        },
                                        child: Column(
                                          children: [
                                            Container(
                                              width: 80,
                                              height: 60,
                                              decoration: BoxDecoration(
                                                color: _getMapStyleColor(style),
                                                borderRadius:
                                                    BorderRadius.circular(8),
                                                border: Border.all(
                                                  color: isSelected
                                                      ? theme
                                                          .colorScheme.primary
                                                      : Colors.transparent,
                                                  width: 2,
                                                ),
                                              ),
                                            ),
                                            const SizedBox(height: 4),
                                            Text(
                                              _getMapStyleName(style),
                                              style: TextStyle(
                                                color: isSelected
                                                    ? theme.colorScheme.primary
                                                    : null,
                                                fontWeight: isSelected
                                                    ? FontWeight.bold
                                                    : null,
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    );
                                  }).toList(),
                                ),
                              ),
                            ],
                          ),
                        );
                      },
                    );
                  },
                  backgroundColor: theme.colorScheme.surface,
                  child: Icon(
                    Icons.layers,
                    color: theme.colorScheme.primary,
                  ),
                ),
                const SizedBox(height: 8),

                // Offline map caching button
                FloatingActionButton.small(
                  heroTag: 'offline_maps',
                  onPressed: _showOfflineMapOptions,
                  backgroundColor: theme.colorScheme.surface,
                  child: Icon(
                    Icons.download,
                    color: theme.colorScheme.primary,
                  ),
                ),
              ],
            ),
          ),

          // Custom Info Window
          if (_selectedExperience != null)
            Positioned(
              bottom: 16,
              left: 16,
              right: 16,
              child: CustomInfoWindow(
                experience: _selectedExperience!,
                onTap: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => ExperienceDetailsScreen(
                        experience: _selectedExperience!,
                      ),
                    ),
                  );
                },
              ),
            ),

          // Search Bar
          Positioned(
            top: 16,
            left: 16,
            right: 16,
            child: Container(
              decoration: BoxDecoration(
                color: theme.colorScheme.surface,
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: TextField(
                controller: _searchController,
                decoration: InputDecoration(
                  hintText: 'Search experiences...',
                  prefixIcon: const Icon(Icons.search),
                  suffixIcon: IconButton(
                    icon: Icon(
                      _isListening ? Icons.mic : Icons.mic_none,
                      color: _isListening ? theme.colorScheme.primary : null,
                    ),
                    onPressed: _startVoiceSearch,
                  ),
                  border: InputBorder.none,
                  contentPadding: const EdgeInsets.symmetric(horizontal: 16),
                ),
                onChanged: (value) {
                  setState(() {});
                },
              ),
            ),
          ),

          // Category Filter
          Positioned(
            top: 80,
            left: 16,
            right: 16,
            child: Container(
              height: 40,
              decoration: BoxDecoration(
                color: theme.colorScheme.surface,
                borderRadius: BorderRadius.circular(20),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                padding: const EdgeInsets.symmetric(horizontal: 8),
                itemCount: _categories.length,
                itemBuilder: (context, index) {
                  final category = _categories[index];
                  final isSelected = category == _selectedCategory;
                  return Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 4),
                    child: ChoiceChip(
                      label: Text(category),
                      selected: isSelected,
                      onSelected: (selected) {
                        setState(() {
                          _selectedCategory = category;
                        });
                      },
                    ),
                  );
                },
              ),
            ),
          ),

          // Bottom Sheet with Experience Cards
          DraggableScrollableSheet(
            initialChildSize: 0.3,
            minChildSize: 0.1,
            maxChildSize: 0.9,
            builder: (context, scrollController) {
              return Container(
                decoration: BoxDecoration(
                  color: theme.colorScheme.surface,
                  borderRadius: const BorderRadius.vertical(
                    top: Radius.circular(20),
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.1),
                      blurRadius: 10,
                      offset: const Offset(0, -2),
                    ),
                  ],
                ),
                child: Column(
                  children: [
                    // Handle
                    Container(
                      margin: const EdgeInsets.symmetric(vertical: 8),
                      width: 40,
                      height: 4,
                      decoration: BoxDecoration(
                        color: Colors.grey.shade300,
                        borderRadius: BorderRadius.circular(2),
                      ),
                    ),
                    // View Toggle
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      child: Row(
                        children: [
                          Expanded(
                            child: ChoiceChip(
                              label: const Text('Map View'),
                              selected: _isMapView,
                              onSelected: (selected) {
                                setState(() {
                                  _isMapView = selected;
                                });
                              },
                            ),
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: ChoiceChip(
                              label: const Text('List View'),
                              selected: !_isMapView,
                              onSelected: (selected) {
                                setState(() {
                                  _isMapView = !selected;
                                });
                              },
                            ),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 8),
                    // Experience Cards
                    Expanded(
                      child: experiencesAsync.when(
                        data: (experiences) {
                          final filteredExperiences =
                              _filterExperiences(experiences);

                          if (filteredExperiences.isEmpty) {
                            return Center(
                              child: Text(
                                'No experiences found',
                                style: theme.textTheme.titleMedium?.copyWith(
                                  color: Colors.grey,
                                ),
                              ),
                            );
                          }

                          return ListView.builder(
                            controller: scrollController,
                            padding: const EdgeInsets.symmetric(horizontal: 16),
                            itemCount: filteredExperiences.length,
                            itemBuilder: (context, index) {
                              final experience = filteredExperiences[index];
                              return Padding(
                                padding: const EdgeInsets.only(bottom: 16),
                                child: ExperienceCard(
                                  title: experience.title,
                                  imageUrl: experience.imageUrl,
                                  rating: experience.rating,
                                  reviewCount: experience.reviewCount,
                                  price: experience.price.toString(),
                                  category: experience.category,
                                  location: experience.location,
                                  isHorizontal: true,
                                  onTap: () {
                                    Navigator.push(
                                      context,
                                      MaterialPageRoute(
                                        builder: (context) =>
                                            ExperienceDetailsScreen(
                                          experience: experience,
                                        ),
                                      ),
                                    );
                                  },
                                ),
                              );
                            },
                          );
                        },
                        loading: () => const Center(
                          child: CircularProgressIndicator(),
                        ),
                        error: (error, stackTrace) => Center(
                          child: Text('Error: $error'),
                        ),
                      ),
                    ),
                  ],
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  List<Experience> _filterExperiences(List<Experience> experiences) {
    return experiences.where((experience) {
      final matchesSearch = _searchController.text.isEmpty ||
          experience.title
              .toLowerCase()
              .contains(_searchController.text.toLowerCase()) ||
          experience.description
              .toLowerCase()
              .contains(_searchController.text.toLowerCase()) ||
          experience.location
              .toLowerCase()
              .contains(_searchController.text.toLowerCase());

      final matchesCategory = _selectedCategory == 'All' ||
          experience.category == _selectedCategory;

      return matchesSearch && matchesCategory;
    }).toList();
  }

  Future<void> _updateMarkers(List<Experience> experiences) async {
    if (_currentCameraPosition == null) return;

    final clusters = ClusterService.createClusters(
      experiences,
      _currentCameraPosition!,
      _currentZoomLevel,
    );

    final markers = <Marker>{};

    for (final cluster in clusters) {
      if (cluster.isMultiple) {
        // Create cluster marker
        final marker = Marker(
          markerId: MarkerId(
              'cluster_${cluster.position.latitude}_${cluster.position.longitude}'),
          position: cluster.position,
          icon: await _createClusterIcon(cluster.count),
          onTap: () {
            // Zoom in when cluster is tapped
            _mapController?.animateCamera(
              CameraUpdate.newLatLngZoom(
                cluster.position,
                _currentZoomLevel + 1,
              ),
            );
          },
        );
        markers.add(marker);
      } else {
        // Create individual experience marker
        final experience = cluster.experiences.first;
        final marker = await MarkerService.createMarker(
          experience,
          onTap: () {
            setState(() {
              _selectedExperience = experience;
            });
          },
        );
        markers.add(marker);
      }
    }

    setState(() {
      _markers.clear();
      _markers.addAll(markers);
    });
  }

  /// Go to the user's current location
  Future<void> _goToCurrentLocation() async {
    final locationService = ref.read(locationServiceProvider);
    final position = await locationService.getCurrentPosition();

    if (_mapController != null) {
      _mapController!.animateCamera(
        CameraUpdate.newLatLngZoom(
          LatLng(position.latitude, position.longitude),
          15, // Zoom level
        ),
      );
    }
  }

  /// Toggle route planning mode
  void _toggleRoutePlanning() {
    // Implementation for route planning
    // This would typically involve selecting origin and destination points
    // and then calculating a route between them

    // For now, we'll just show a snackbar
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Route planning mode toggled'),
        duration: Duration(seconds: 2),
      ),
    );
  }

  /// Show offline map options
  void _showOfflineMapOptions() {
    showModalBottomSheet(
      context: context,
      builder: (context) {
        return Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Offline Maps',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),
              ListTile(
                leading: const Icon(Icons.download),
                title: const Text('Download current area'),
                subtitle: const Text('Save this area for offline use'),
                onTap: () {
                  Navigator.pop(context);
                  _downloadCurrentMapArea();
                },
              ),
              ListTile(
                leading: const Icon(Icons.map),
                title: const Text('Manage offline maps'),
                subtitle: const Text('View and delete saved areas'),
                onTap: () {
                  Navigator.pop(context);
                  _manageOfflineMaps();
                },
              ),
            ],
          ),
        );
      },
    );
  }

  /// Download the current map area for offline use
  Future<void> _downloadCurrentMapArea() async {
    if (_mapController == null) return;

    // Get the current visible region
    final visibleRegion = await _mapController!.getVisibleRegion();

    // Show a confirmation dialog
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Download Map Area'),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'This will download map data for the current visible area. '
              'The download size depends on the area and zoom levels.',
            ),
            SizedBox(height: 16),
            Text(
              'Estimated size: 5-10 MB',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            child: const Text('Download'),
          ),
        ],
      ),
    );

    if (confirmed != true) return;

    // Show a progress dialog
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const AlertDialog(
        title: Text('Downloading Map Area'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            LinearProgressIndicator(),
            SizedBox(height: 16),
            Text('This may take a few minutes...'),
          ],
        ),
      ),
    );

    try {
      // In a real app, this would use the MapCacheManager to download the area
      // For now, we'll just simulate a download
      await Future.delayed(const Duration(seconds: 3));

      if (context.mounted) {
        Navigator.pop(context); // Close the progress dialog

        // Show a success message
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Map area downloaded successfully'),
            duration: Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      if (context.mounted) {
        Navigator.pop(context); // Close the progress dialog

        // Show an error message
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error downloading map area: $e'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    }
  }

  /// Manage offline maps
  void _manageOfflineMaps() {
    // In a real app, this would show a list of downloaded map areas
    // and allow the user to delete them

    // For now, we'll just show a snackbar
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Offline maps management not implemented yet'),
        duration: Duration(seconds: 2),
      ),
    );
  }

  /// Calculate the distance to an experience
  Future<double> _calculateDistanceToExperience(Experience experience) async {
    final locationService = ref.read(locationServiceProvider);
    final position = await locationService.getCurrentPosition();

    // Parse the location string to get coordinates
    // In a real app, the Experience model would have proper latitude and longitude fields
    // For now, we'll just generate a random distance
    return 100.0 + (DateTime.now().millisecondsSinceEpoch % 5000);
  }

  /// Get directions to an experience
  Future<void> _getDirectionsToExperience(Experience experience) async {
    final locationService = ref.read(locationServiceProvider);
    final position = await locationService.getCurrentPosition();

    // In a real app, this would calculate a route between the user's location
    // and the experience location, and display it on the map

    // For now, we'll just show a snackbar
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Getting directions to ${experience.title}'),
        duration: const Duration(seconds: 2),
      ),
    );
  }

  /// Get the color for a map style
  Color _getMapStyleColor(String style) {
    switch (style) {
      case 'standard':
        return Colors.blue.shade100;
      case 'silver':
        return Colors.grey.shade300;
      case 'retro':
        return Colors.brown.shade200;
      case 'dark':
        return Colors.grey.shade800;
      case 'night':
        return Colors.indigo.shade900;
      case 'aubergine':
        return Colors.deepPurple.shade900;
      default:
        return Colors.blue.shade100;
    }
  }

  /// Get the display name for a map style
  String _getMapStyleName(String style) {
    switch (style) {
      case 'standard':
        return 'Standard';
      case 'silver':
        return 'Silver';
      case 'retro':
        return 'Retro';
      case 'dark':
        return 'Dark';
      case 'night':
        return 'Night';
      case 'aubergine':
        return 'Aubergine';
      default:
        return style;
    }
  }

  Future<BitmapDescriptor> _createClusterIcon(int count) async {
    final recorder = ui.PictureRecorder();
    final canvas = Canvas(recorder);
    const size = Size(48, 48);
    final paint = Paint()
      ..color =
          Colors.blue.withAlpha(230) // Using withAlpha instead of withOpacity
      ..style = PaintingStyle.fill;

    // Draw circle
    canvas.drawCircle(
      Offset(size.width / 2, size.height / 2),
      size.width / 2,
      paint,
    );

    // Draw border
    canvas.drawCircle(
      Offset(size.width / 2, size.height / 2),
      size.width / 2,
      Paint()
        ..color = Colors.white
        ..style = PaintingStyle.stroke
        ..strokeWidth = 2,
    );

    // Draw text
    final textPainter = TextPainter(
      text: TextSpan(
        text: count.toString(),
        style: const TextStyle(
          color: Colors.white,
          fontSize: 20,
          fontWeight: FontWeight.bold,
        ),
      ),
      textDirection: TextDirection.ltr,
    );
    textPainter.layout();
    textPainter.paint(
      canvas,
      Offset(
        (size.width - textPainter.width) / 2,
        (size.height - textPainter.height) / 2,
      ),
    );

    final picture = recorder.endRecording();
    final image = await picture.toImage(
      size.width.toInt(),
      size.height.toInt(),
    );
    final bytes = await image.toByteData(format: ui.ImageByteFormat.png);

    // Use BitmapDescriptor.bytes instead of fromBytes
    return BitmapDescriptor.fromBytes(bytes!.buffer.asUint8List());
  }
}
