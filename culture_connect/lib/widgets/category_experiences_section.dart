import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:culture_connect/models/experience.dart';
import 'package:culture_connect/models/filter_options.dart';
import 'package:culture_connect/providers/experience_provider.dart';
import 'package:culture_connect/screens/experience_details_screen.dart';

/// A widget for displaying experiences from a specific category
class CategoryExperiencesSection extends ConsumerWidget {
  final String category;
  final bool showTitle;
  final bool showViewAll;
  final int maxItems;
  final ScrollPhysics? physics;

  const CategoryExperiencesSection({
    super.key,
    required this.category,
    this.showTitle = true,
    this.showViewAll = true,
    this.maxItems = 10,
    this.physics,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final experiencesAsyncValue = ref.watch(experiencesProvider);
    final theme = Theme.of(context);

    return experiencesAsyncValue.when(
      data: (experiences) {
        final categoryExperiences =
            experiences.where((exp) => exp.category == category).toList();

        if (categoryExperiences.isEmpty) {
          return const SizedBox.shrink();
        }

        final displayedExperiences =
            categoryExperiences.take(maxItems).toList();

        return Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (showTitle) ...[
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      category,
                      style: theme.textTheme.titleLarge,
                    ),
                    if (showViewAll && categoryExperiences.length > maxItems)
                      TextButton(
                        onPressed: () {
                          // Set filter to this category and navigate to explore screen
                          ref.read(filterOptionsProvider.notifier).state =
                              FilterOptions(category: category);
                          // TODO: Navigate to explore screen with this category filter
                        },
                        child: const Text('View All'),
                      ),
                  ],
                ),
                const SizedBox(height: 8),
              ],
              SizedBox(
                height: 280,
                child: ListView.builder(
                  scrollDirection: Axis.horizontal,
                  physics: physics ?? const BouncingScrollPhysics(),
                  itemCount: displayedExperiences.length,
                  itemBuilder: (context, index) {
                    final experience = displayedExperiences[index];
                    return SizedBox(
                      width: 220,
                      child: Padding(
                        padding: const EdgeInsets.only(right: 16),
                        child: _buildCategoryExperienceCard(
                            context, experience, ref),
                      ),
                    );
                  },
                ),
              ),
            ],
          ),
        );
      },
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (error, stackTrace) => Center(
        child: Text('Error loading experiences: $error'),
      ),
    );
  }

  Widget _buildCategoryExperienceCard(
      BuildContext context, Experience experience, WidgetRef ref) {
    return Card(
      clipBehavior: Clip.antiAlias,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      elevation: 4,
      child: InkWell(
        onTap: () {
          ref.read(experiencesProvider.notifier).markAsViewed(experience.id);
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => ExperienceDetailsScreen(
                experience: experience,
              ),
            ),
          );
        },
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Image
            Stack(
              children: [
                Image.network(
                  experience.imageUrl,
                  height: 120,
                  width: double.infinity,
                  fit: BoxFit.cover,
                ),
                Positioned(
                  top: 8,
                  right: 8,
                  child: IconButton(
                    icon: Icon(
                      experience.isSaved
                          ? Icons.bookmark
                          : Icons.bookmark_border,
                      color: Colors.white,
                    ),
                    onPressed: () {
                      ref
                          .read(experiencesProvider.notifier)
                          .toggleSaved(experience.id);
                    },
                  ),
                ),
              ],
            ),
            // Content
            Padding(
              padding: const EdgeInsets.all(12),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    experience.title,
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 4),
                  Row(
                    children: [
                      const Icon(
                        Icons.star,
                        size: 16,
                        color: Colors.amber,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        experience.rating.toStringAsFixed(1),
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(width: 4),
                      Text(
                        '(${experience.reviewCount})',
                        style: TextStyle(
                          color: Colors.grey[600],
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 4),
                  Row(
                    children: [
                      const Icon(
                        Icons.location_on,
                        size: 16,
                        color: Colors.red,
                      ),
                      const SizedBox(width: 4),
                      Expanded(
                        child: Text(
                          experience.location,
                          style: TextStyle(
                            color: Colors.grey[600],
                            fontSize: 12,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        '\$${experience.price.toStringAsFixed(0)}',
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
                      Row(
                        children: [
                          const Icon(
                            Icons.access_time,
                            size: 16,
                            color: Colors.grey,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            '${experience.durationHours}h',
                            style: TextStyle(
                              color: Colors.grey[600],
                              fontSize: 12,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
