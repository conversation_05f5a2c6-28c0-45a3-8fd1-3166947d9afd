import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// A widget for displaying a section title
class SectionTitle extends StatelessWidget {
  /// The title text
  final String title;
  
  /// The style of the title
  final TextStyle? style;
  
  /// Whether to show a divider after the title
  final bool showDivider;
  
  /// The padding around the title
  final EdgeInsetsGeometry padding;
  
  /// Creates a new section title
  const SectionTitle({
    super.key,
    required this.title,
    this.style,
    this.showDivider = false,
    this.padding = EdgeInsets.zero,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final titleStyle = style ?? theme.textTheme.titleLarge;
    
    return Padding(
      padding: padding,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: titleStyle,
          ),
          if (showDivider) ...[
            SizedBox(height: 4.h),
            Divider(
              color: theme.colorScheme.outlineVariant,
              thickness: 1,
            ),
          ],
        ],
      ),
    );
  }
}
