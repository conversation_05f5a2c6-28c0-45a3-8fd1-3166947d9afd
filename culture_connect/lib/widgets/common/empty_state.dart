import 'package:flutter/material.dart';
import 'package:culture_connect/theme/app_colors.dart';
import 'package:culture_connect/theme/app_text_styles.dart';
import 'package:culture_connect/widgets/common/custom_button.dart';

/// A widget that displays an empty state
class EmptyState extends StatelessWidget {
  /// The icon to display
  final IconData icon;

  /// The title to display
  final String title;

  /// The message to display
  final String message;

  /// The action button text
  final String? actionText;

  /// The callback when the action button is pressed
  final VoidCallback? onAction;

  /// The icon size
  final double iconSize;

  /// The icon color
  final Color? iconColor;

  /// Creates a new empty state
  const EmptyState({
    super.key,
    required this.icon,
    required this.title,
    required this.message,
    this.actionText,
    this.onAction,
    this.iconSize = 80,
    this.iconColor,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              size: iconSize,
              color: iconColor ?? AppColors.primary.withAlpha(128),
            ),
            const SizedBox(height: 24),
            Text(
              title,
              style: AppTextStyles.headline6,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            Text(
              message,
              style: AppTextStyles.body2.copyWith(
                color: theme.textTheme.bodySmall?.color,
              ),
              textAlign: TextAlign.center,
            ),
            if (actionText != null && onAction != null) ...[
              const SizedBox(height: 32),
              CustomButton(
                text: actionText!,
                onPressed: onAction,
                icon: Icons.add,
              ),
            ],
          ],
        ),
      ),
    );
  }
}
