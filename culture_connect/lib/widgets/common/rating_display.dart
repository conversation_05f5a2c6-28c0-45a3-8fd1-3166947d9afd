import 'package:flutter/material.dart';

/// A widget for displaying a rating
class RatingDisplay extends StatelessWidget {
  /// The rating to display (0-5)
  final double rating;

  /// The size of the stars
  final double size;

  /// The color of the filled stars
  final Color? filledColor;

  /// The color of the empty stars
  final Color? emptyColor;

  /// Creates a new rating display
  const RatingDisplay({
    super.key,
    required this.rating,
    this.size = 24.0,
    this.filledColor,
    this.emptyColor,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final filledStarColor = filledColor ?? Colors.amber;
    final emptyStarColor = emptyColor ??
        theme.colorScheme.onSurfaceVariant
            .withAlpha(76); // 76 is approximately 0.3 opacity

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: List.generate(5, (index) {
        final starValue = index + 1;

        // Full star
        if (starValue <= rating) {
          return Icon(
            Icons.star,
            color: filledStarColor,
            size: size,
          );
        }
        // Half star
        else if (starValue - 0.5 <= rating) {
          return Icon(
            Icons.star_half,
            color: filledStarColor,
            size: size,
          );
        }
        // Empty star
        else {
          return Icon(
            Icons.star_border,
            color: emptyStarColor,
            size: size,
          );
        }
      }),
    );
  }
}
