import 'package:flutter/material.dart';

/// A custom button widget
class CustomButton extends StatelessWidget {
  /// The button text
  final String text;

  /// The callback when the button is pressed
  final VoidCallback? onPressed;

  /// Whether the button is loading
  final bool isLoading;

  /// The button color
  final Color? color;

  /// The text color
  final Color? textColor;

  /// The button width
  final double? width;

  /// The button height
  final double? height;

  /// The button border radius
  final double borderRadius;

  /// The button icon
  final IconData? icon;

  /// Whether the icon is on the right
  final bool iconOnRight;

  /// Creates a new custom button
  const CustomButton({
    super.key,
    required this.text,
    this.onPressed,
    this.isLoading = false,
    this.color,
    this.textColor,
    this.width,
    this.height = 50,
    this.borderRadius = 8,
    this.icon,
    this.iconOnRight = false,
  });

  @override
  Widget build(BuildContext context) {
    final buttonColor = color ?? Theme.of(context).colorScheme.primary;
    final buttonTextColor = textColor ?? Colors.white;

    return SizedBox(
      width: width,
      height: height,
      child: ElevatedButton(
        onPressed: isLoading ? null : onPressed,
        style: ElevatedButton.styleFrom(
          backgroundColor: buttonColor,
          foregroundColor: buttonTextColor,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(borderRadius),
          ),
          padding: const EdgeInsets.symmetric(horizontal: 16),
          elevation: 2,
        ),
        child: isLoading
            ? const SizedBox(
                width: 24,
                height: 24,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              )
            : icon != null
                ? Row(
                    mainAxisSize: MainAxisSize.min,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: iconOnRight
                        ? [
                            Text(text),
                            const SizedBox(width: 8),
                            Icon(icon, size: 18),
                          ]
                        : [
                            Icon(icon, size: 18),
                            const SizedBox(width: 8),
                            Text(text),
                          ],
                  )
                : Text(text),
      ),
    );
  }
}
