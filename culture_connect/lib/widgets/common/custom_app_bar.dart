import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// A custom app bar widget
class CustomAppBar extends StatelessWidget implements PreferredSizeWidget {
  /// The title of the app bar
  final String title;

  /// Whether to show the back button
  final bool showBackButton;

  /// The callback when the back button is pressed
  final VoidCallback? onBackPressed;

  /// The actions to display on the right side of the app bar
  final List<Widget>? actions;

  /// The bottom widget of the app bar
  final PreferredSizeWidget? bottom;

  /// The background color of the app bar
  final Color? backgroundColor;

  /// The foreground color of the app bar
  final Color? foregroundColor;

  /// The elevation of the app bar
  final double elevation;

  /// Whether the app bar is centered
  final bool centerTitle;

  /// Creates a new custom app bar
  const CustomAppBar({
    super.key,
    required this.title,
    this.showBackButton = false,
    this.onBackPressed,
    this.actions,
    this.bottom,
    this.backgroundColor,
    this.foregroundColor,
    this.elevation = 0.0,
    this.centerTitle = true,
  });

  @override
  Widget build(BuildContext context) {
    return AppBar(
      title: Text(
        title,
        style: TextStyle(
          fontSize: 18.sp,
          fontWeight: FontWeight.bold,
          color: foregroundColor ?? Colors.black,
        ),
      ),
      centerTitle: centerTitle,
      backgroundColor: backgroundColor ?? Colors.white,
      foregroundColor: foregroundColor ?? Colors.black,
      elevation: elevation,
      leading: showBackButton
          ? IconButton(
              icon: const Icon(Icons.arrow_back),
              onPressed: onBackPressed ?? () => Navigator.of(context).pop(),
            )
          : null,
      actions: actions,
      bottom: bottom,
    );
  }

  @override
  Size get preferredSize => Size.fromHeight(
        kToolbarHeight + (bottom?.preferredSize.height ?? 0.0),
      );
}
