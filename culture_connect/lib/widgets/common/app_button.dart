import 'package:flutter/material.dart';
import 'package:culture_connect/theme/app_theme.dart';

/// Button variants
enum ButtonVariant {
  /// Filled button (primary)
  filled,

  /// Outlined button (secondary)
  outlined,

  /// Text button (tertiary)
  text,
}

/// Button types for backward compatibility
enum ButtonType {
  /// Primary filled button
  primary,

  /// Secondary filled button with different color
  secondary,

  /// Outlined button
  outlined,

  /// Text button
  text,
}

/// A reusable button widget with consistent styling
class AppButton extends StatelessWidget {
  /// The text to display on the button
  final String text;

  /// The callback when the button is pressed
  final VoidCallback onPressed;

  /// The button variant (filled, outlined, text)
  final ButtonVariant variant;

  /// The button color (defaults to primary color)
  final Color? color;

  /// The icon to display before the text
  final IconData? icon;

  /// Whether the button is disabled
  final bool isDisabled;

  /// Whether the button is loading
  final bool isLoading;

  /// The width of the button (null for auto)
  final double? width;

  /// The height of the button
  final double height;

  /// The text style for the button text
  final TextStyle? textStyle;

  /// Creates a new app button
  const AppButton({
    super.key,
    required this.text,
    required this.onPressed,
    this.variant = ButtonVariant.filled,
    this.color,
    this.icon,
    this.isDisabled = false,
    this.isLoading = false,
    this.width,
    this.height = 48.0,
    this.textStyle,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final buttonColor = color ?? theme.colorScheme.primary;

    // Determine the button style based on the variant
    Widget button;

    switch (variant) {
      case ButtonVariant.filled:
        button = _buildFilledButton(context, buttonColor);
        break;
      case ButtonVariant.outlined:
        button = _buildOutlinedButton(context, buttonColor);
        break;
      case ButtonVariant.text:
        button = _buildTextButton(context, buttonColor);
        break;
    }

    return SizedBox(
      width: width,
      height: height,
      child: button,
    );
  }

  Widget _buildFilledButton(BuildContext context, Color buttonColor) {
    return ElevatedButton(
      onPressed: isDisabled || isLoading ? null : onPressed,
      style: ElevatedButton.styleFrom(
        backgroundColor: buttonColor,
        foregroundColor: Colors.white,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
      child: _buildButtonContent(context, Colors.white),
    );
  }

  Widget _buildOutlinedButton(BuildContext context, Color buttonColor) {
    return OutlinedButton(
      onPressed: isDisabled || isLoading ? null : onPressed,
      style: OutlinedButton.styleFrom(
        foregroundColor: buttonColor,
        side: BorderSide(color: buttonColor),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
      child: _buildButtonContent(context, buttonColor),
    );
  }

  Widget _buildTextButton(BuildContext context, Color buttonColor) {
    return TextButton(
      onPressed: isDisabled || isLoading ? null : onPressed,
      style: TextButton.styleFrom(
        foregroundColor: buttonColor,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
      child: _buildButtonContent(context, buttonColor),
    );
  }

  Widget _buildButtonContent(BuildContext context, Color textColor) {
    if (isLoading) {
      return SizedBox(
        width: 24,
        height: 24,
        child: CircularProgressIndicator(
          strokeWidth: 2,
          valueColor: AlwaysStoppedAnimation<Color>(textColor),
        ),
      );
    }

    if (icon != null) {
      return Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 18),
          const SizedBox(width: 8),
          Text(
            text,
            style: textStyle?.copyWith(color: textColor) ??
                TextStyle(
                  fontWeight: FontWeight.bold,
                  color: textColor,
                ),
          ),
        ],
      );
    }

    return Text(
      text,
      style: textStyle?.copyWith(color: textColor) ??
          TextStyle(
            fontWeight: FontWeight.bold,
            color: textColor,
          ),
    );
  }
}

/// A reusable button widget with consistent styling (legacy API)
///
/// This class is provided for backward compatibility with existing code.
/// New code should use the AppButton class instead.
class CustomButton extends StatelessWidget {
  final String text;
  final VoidCallback onPressed;
  final ButtonType type;
  final bool isFullWidth;
  final bool isLoading;
  final IconData? icon;
  final double? width;
  final double height;
  final EdgeInsets? padding;
  final double borderRadius;

  const CustomButton({
    super.key,
    required this.text,
    required this.onPressed,
    this.type = ButtonType.primary,
    this.isFullWidth = true,
    this.isLoading = false,
    this.icon,
    this.width,
    this.height = 56,
    this.padding,
    this.borderRadius = 12,
  });

  @override
  Widget build(BuildContext context) {
    // Convert ButtonType to ButtonVariant
    ButtonVariant variant;
    Color? color;

    switch (type) {
      case ButtonType.primary:
        variant = ButtonVariant.filled;
        color = null; // Use default primary color
        break;
      case ButtonType.secondary:
        variant = ButtonVariant.filled;
        color = AppTheme.secondaryColor;
        break;
      case ButtonType.outlined:
        variant = ButtonVariant.outlined;
        color = null;
        break;
      case ButtonType.text:
        variant = ButtonVariant.text;
        color = null;
        break;
    }

    return AppButton(
      text: text,
      onPressed: onPressed,
      variant: variant,
      color: color,
      icon: icon,
      isLoading: isLoading,
      width: isFullWidth ? double.infinity : width,
      height: height,
      textStyle: const TextStyle(
        fontSize: 16,
        fontWeight: FontWeight.w600,
      ),
    );
  }
}
