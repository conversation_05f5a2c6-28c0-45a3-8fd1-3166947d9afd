import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

/// A widget for picking dates with a text field interface
class DatePickerField extends StatefulWidget {
  /// The label for the field
  final String label;

  /// The hint text
  final String? hintText;

  /// The initial date value
  final DateTime? initialDate;

  /// The earliest date that can be selected
  final DateTime? firstDate;

  /// The latest date that can be selected
  final DateTime? lastDate;

  /// Callback when a date is selected
  final Function(DateTime date) onDateSelected;

  /// Whether the field is required
  final bool isRequired;

  /// Whether the field is enabled
  final bool enabled;

  /// The date format to display
  final String dateFormat;

  /// Custom validator function
  final String? Function(DateTime?)? validator;

  /// The icon to display
  final IconData? icon;

  /// Whether to show the clear button
  final bool showClearButton;

  /// Creates a new date picker field
  const DatePickerField({
    super.key,
    required this.label,
    this.hintText,
    this.initialDate,
    this.firstDate,
    this.lastDate,
    required this.onDateSelected,
    this.isRequired = false,
    this.enabled = true,
    this.dateFormat = 'MMM dd, yyyy',
    this.validator,
    this.icon,
    this.showClearButton = true,
  });

  @override
  State<DatePickerField> createState() => _DatePickerFieldState();
}

class _DatePickerFieldState extends State<DatePickerField> {
  late TextEditingController _controller;
  DateTime? _selectedDate;
  String? _errorText;

  @override
  void initState() {
    super.initState();
    _selectedDate = widget.initialDate;
    _controller = TextEditingController(
      text: _selectedDate != null
          ? DateFormat(widget.dateFormat).format(_selectedDate!)
          : '',
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  void didUpdateWidget(DatePickerField oldWidget) {
    super.didUpdateWidget(oldWidget);

    if (widget.initialDate != oldWidget.initialDate) {
      _selectedDate = widget.initialDate;
      _controller.text = _selectedDate != null
          ? DateFormat(widget.dateFormat).format(_selectedDate!)
          : '';
    }
  }

  /// Show the date picker
  Future<void> _showDatePicker() async {
    if (!widget.enabled) return;

    final DateTime now = DateTime.now();
    final DateTime firstDate = widget.firstDate ?? DateTime(1900);
    final DateTime lastDate = widget.lastDate ?? DateTime(2100);
    final DateTime initialDate = _selectedDate ??
        (now.isAfter(firstDate) && now.isBefore(lastDate) ? now : firstDate);

    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: initialDate,
      firstDate: firstDate,
      lastDate: lastDate,
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: Theme.of(context).colorScheme.copyWith(
                  primary: Theme.of(context).colorScheme.primary,
                  onPrimary: Theme.of(context).colorScheme.onPrimary,
                  surface: Theme.of(context).colorScheme.surface,
                  onSurface: Theme.of(context).colorScheme.onSurface,
                ),
          ),
          child: child!,
        );
      },
    );

    if (picked != null && picked != _selectedDate) {
      setState(() {
        _selectedDate = picked;
        _controller.text = DateFormat(widget.dateFormat).format(picked);
        _errorText = widget.validator?.call(picked);
      });

      widget.onDateSelected(picked);
    }
  }

  /// Clear the selected date
  void _clearDate() {
    if (!widget.enabled) return;

    setState(() {
      _selectedDate = null;
      _controller.clear();
      _errorText = widget.validator?.call(null);
    });
  }

  /// Validate the field
  String? _validate() {
    if (widget.isRequired && _selectedDate == null) {
      return '${widget.label} is required';
    }

    return widget.validator?.call(_selectedDate);
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (widget.label.isNotEmpty) ...[
          RichText(
            text: TextSpan(
              text: widget.label,
              style: Theme.of(context).textTheme.labelMedium,
              children: [
                if (widget.isRequired)
                  TextSpan(
                    text: ' *',
                    style: Theme.of(context).textTheme.labelMedium?.copyWith(
                          color: Theme.of(context).colorScheme.error,
                        ),
                  ),
              ],
            ),
          ),
          const SizedBox(height: 8),
        ],
        TextFormField(
          controller: _controller,
          enabled: widget.enabled,
          readOnly: true,
          onTap: _showDatePicker,
          validator: (_) => _validate(),
          decoration: InputDecoration(
            hintText: widget.hintText ?? 'Select ${widget.label.toLowerCase()}',
            prefixIcon: widget.icon != null
                ? Icon(
                    widget.icon,
                    color: widget.enabled
                        ? Theme.of(context).colorScheme.onSurface.withAlpha(153)
                        : Theme.of(context).colorScheme.onSurface.withAlpha(97),
                  )
                : Icon(
                    Icons.calendar_today,
                    color: widget.enabled
                        ? Theme.of(context).colorScheme.onSurface.withAlpha(153)
                        : Theme.of(context).colorScheme.onSurface.withAlpha(97),
                  ),
            suffixIcon: widget.showClearButton && _selectedDate != null
                ? IconButton(
                    icon: Icon(
                      Icons.clear,
                      color: Theme.of(context)
                          .colorScheme
                          .onSurface
                          .withAlpha(153),
                    ),
                    onPressed: widget.enabled ? _clearDate : null,
                  )
                : Icon(
                    Icons.arrow_drop_down,
                    color: widget.enabled
                        ? Theme.of(context).colorScheme.onSurface.withAlpha(153)
                        : Theme.of(context).colorScheme.onSurface.withAlpha(97),
                  ),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(
                color: Theme.of(context).colorScheme.outline,
              ),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(
                color: Theme.of(context).colorScheme.outline,
              ),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(
                color: Theme.of(context).colorScheme.primary,
                width: 2,
              ),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(
                color: Theme.of(context).colorScheme.error,
              ),
            ),
            focusedErrorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(
                color: Theme.of(context).colorScheme.error,
                width: 2,
              ),
            ),
            disabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(
                color: Theme.of(context).colorScheme.outline.withAlpha(97),
              ),
            ),
            filled: true,
            fillColor: widget.enabled
                ? Theme.of(context).colorScheme.surface
                : Theme.of(context).colorScheme.surface.withAlpha(97),
            contentPadding: const EdgeInsets.symmetric(
              horizontal: 16,
              vertical: 12,
            ),
            errorText: _errorText,
          ),
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: widget.enabled
                    ? Theme.of(context).colorScheme.onSurface
                    : Theme.of(context).colorScheme.onSurface.withAlpha(97),
              ),
        ),
      ],
    );
  }
}

/// A simplified date picker field for quick use
class SimpleDatePickerField extends StatelessWidget {
  /// The label for the field
  final String label;

  /// The selected date
  final DateTime? selectedDate;

  /// Callback when a date is selected
  final Function(DateTime date) onDateSelected;

  /// Whether the field is required
  final bool isRequired;

  /// Creates a new simple date picker field
  const SimpleDatePickerField({
    super.key,
    required this.label,
    this.selectedDate,
    required this.onDateSelected,
    this.isRequired = false,
  });

  @override
  Widget build(BuildContext context) {
    return DatePickerField(
      label: label,
      initialDate: selectedDate,
      onDateSelected: onDateSelected,
      isRequired: isRequired,
      firstDate: DateTime(1900),
      lastDate: DateTime(2100),
    );
  }
}
