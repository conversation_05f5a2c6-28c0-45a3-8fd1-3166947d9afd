import 'package:flutter/material.dart';

/// A reusable loading indicator widget
class LoadingIndicator extends StatelessWidget {
  /// The size of the loading indicator
  final double size;

  /// The color of the loading indicator
  final Color? color;

  /// The stroke width of the loading indicator
  final double strokeWidth;

  /// Creates a loading indicator widget
  const LoadingIndicator({
    super.key,
    this.size = 36.0,
    this.color,
    this.strokeWidth = 4.0,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: size,
      height: size,
      child: CircularProgressIndicator(
        strokeWidth: strokeWidth,
        valueColor: color != null
            ? AlwaysStoppedAnimation<Color>(color!)
            : AlwaysStoppedAnimation<Color>(
                Theme.of(context).colorScheme.primary),
      ),
    );
  }
}
