import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:share_plus/share_plus.dart';
import 'package:culture_connect/models/experience.dart';
import 'package:culture_connect/services/logging_service.dart';
import 'package:culture_connect/services/error_handling_service.dart';
import 'package:culture_connect/services/analytics_service.dart';

// Define ShareStatus enum to match the share_plus package
enum ShareStatus {
  success,
  dismissed,
  unavailable,
}

// Define ShareResult class to match the share_plus package
class ShareResult {
  final ShareStatus status;

  const ShareResult(this.status);
}

/// A widget for sharing an experience
class ShareExperienceButton extends ConsumerWidget {
  final Experience experience;
  final Color? color;
  final bool showLabel;
  final bool isOutlined;
  final VoidCallback? onShareComplete;

  const ShareExperienceButton({
    super.key,
    required this.experience,
    this.color,
    this.showLabel = true,
    this.isOutlined = false,
    this.onShareComplete,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final loggingService = ref.watch(loggingServiceProvider);
    final errorHandlingService = ref.watch(errorHandlingServiceProvider);
    final analyticsService = ref.watch(analyticsServiceProvider);

    return isOutlined
        ? OutlinedButton.icon(
            onPressed: () => _shareExperience(
              context,
              loggingService,
              errorHandlingService,
              analyticsService,
            ),
            icon: const Icon(Icons.share),
            label: showLabel ? const Text('Share') : const SizedBox.shrink(),
            style: OutlinedButton.styleFrom(
              foregroundColor: color ?? theme.colorScheme.primary,
            ),
          )
        : ElevatedButton.icon(
            onPressed: () => _shareExperience(
              context,
              loggingService,
              errorHandlingService,
              analyticsService,
            ),
            icon: const Icon(Icons.share),
            label: showLabel ? const Text('Share') : const SizedBox.shrink(),
            style: ElevatedButton.styleFrom(
              backgroundColor: color ?? theme.colorScheme.primary,
              foregroundColor: theme.colorScheme.onPrimary,
            ),
          );
  }

  Future<void> _shareExperience(
    BuildContext context,
    LoggingService? loggingService,
    ErrorHandlingService? errorHandlingService,
    AnalyticsService? analyticsService,
  ) async {
    try {
      // Log the share attempt
      loggingService?.info(
        'ShareExperienceButton',
        'Sharing experience: ${experience.id}',
      );

      // Track the share event
      analyticsService?.logEvent(
        name: 'share_experience',
        category: AnalyticsCategory.userAction,
        parameters: {
          'experience_id': experience.id,
          'experience_name': experience.title,
          'experience_category': experience.category,
        },
      );

      // Create the share text
      final shareText = _createShareText();

      // Create the share subject
      final shareSubject = 'Check out this experience: ${experience.title}';

      // Show the share dialog
      await Share.share(
        shareText,
        subject: shareSubject,
      );

      // Handle the share result
      const shareResult = ShareResult(ShareStatus.success);
      _handleShareResult(shareResult, loggingService, analyticsService);

      // Call the onShareComplete callback
      onShareComplete?.call();
    } catch (e, stackTrace) {
      // Log the error
      loggingService?.error(
        'ShareExperienceButton',
        'Error sharing experience',
        e,
        stackTrace,
      );

      // Handle the error
      errorHandlingService?.handleError(
        error: e,
        stackTrace: stackTrace,
        context: 'ShareExperienceButton._shareExperience',
        type: ErrorType.unknown,
        severity: ErrorSeverity.medium,
      );

      // Show an error message
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to share: ${e.toString()}'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    }
  }

  String _createShareText() {
    // Create a share text with the experience details
    final buffer = StringBuffer();

    // Add the experience name
    buffer.writeln('Check out this amazing experience: ${experience.title}');
    buffer.writeln();

    // Add the experience description
    buffer.writeln(experience.description);
    buffer.writeln();

    // Add the experience details
    buffer.writeln('Category: ${experience.category}');
    buffer.writeln('Price: \$${experience.price.toStringAsFixed(2)}');
    buffer.writeln('Duration: ${experience.durationHours} hours');
    buffer.writeln();

    // Add the languages
    if (experience.languages.isNotEmpty) {
      buffer.writeln('Languages: ${experience.languages.join(', ')}');
      buffer.writeln();
    }

    // Add the app link
    buffer.writeln('Download CultureConnect to book this experience:');
    buffer.writeln('https://cultureconnect.app/experience/${experience.id}');

    return buffer.toString();
  }

  void _handleShareResult(
    ShareResult result,
    LoggingService? loggingService,
    AnalyticsService? analyticsService,
  ) {
    switch (result.status) {
      case ShareStatus.success:
        loggingService?.info(
          'ShareExperienceButton',
          'Share successful: ${experience.id}',
        );

        analyticsService?.logEvent(
          name: 'share_experience_success',
          category: AnalyticsCategory.userAction,
          parameters: {
            'experience_id': experience.id,
            'experience_name': experience.title,
          },
        );
        break;

      case ShareStatus.dismissed:
        loggingService?.info(
          'ShareExperienceButton',
          'Share dismissed: ${experience.id}',
        );

        analyticsService?.logEvent(
          name: 'share_experience_dismissed',
          category: AnalyticsCategory.userAction,
          parameters: {
            'experience_id': experience.id,
            'experience_name': experience.title,
          },
        );
        break;

      case ShareStatus.unavailable:
        loggingService?.warning(
          'ShareExperienceButton',
          'Share unavailable: ${experience.id}',
        );

        analyticsService?.logEvent(
          name: 'share_experience_unavailable',
          category: AnalyticsCategory.userAction,
          parameters: {
            'experience_id': experience.id,
            'experience_name': experience.title,
          },
        );
        break;
    }
  }
}

/// A widget for showing a share experience dialog
class ShareExperienceDialog extends ConsumerWidget {
  final Experience experience;

  const ShareExperienceDialog({
    super.key,
    required this.experience,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);

    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'Share Experience',
              style: theme.textTheme.titleLarge,
            ),
            const SizedBox(height: 16),
            Text(
              'Share "${experience.title}" with your friends and family!',
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                _buildShareOption(
                  context,
                  Icons.message,
                  'Message',
                  () => _shareVia(context, ref, 'message'),
                ),
                _buildShareOption(
                  context,
                  Icons.email,
                  'Email',
                  () => _shareVia(context, ref, 'email'),
                ),
                _buildShareOption(
                  context,
                  Icons.facebook,
                  'Facebook',
                  () => _shareVia(context, ref, 'facebook'),
                ),
                _buildShareOption(
                  context,
                  Icons.link,
                  'Copy Link',
                  () => _shareVia(context, ref, 'copy_link'),
                ),
              ],
            ),
            const SizedBox(height: 16),
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildShareOption(
    BuildContext context,
    IconData icon,
    String label,
    VoidCallback onTap,
  ) {
    final theme = Theme.of(context);

    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Padding(
        padding: const EdgeInsets.all(8),
        child: Column(
          children: [
            CircleAvatar(
              backgroundColor: theme.colorScheme.primary
                  .withAlpha(26), // 0.1 opacity is approximately 26 alpha
              radius: 24,
              child: Icon(
                icon,
                color: theme.colorScheme.primary,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              label,
              style: theme.textTheme.bodySmall,
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _shareVia(
    BuildContext context,
    WidgetRef ref,
    String method,
  ) async {
    final loggingService = ref.read(loggingServiceProvider);
    final analyticsService = ref.read(analyticsServiceProvider);

    // Track the share method
    analyticsService.logEvent(
      name: 'share_experience_via',
      category: AnalyticsCategory.userAction,
      parameters: {
        'experience_id': experience.id,
        'experience_name': experience.title,
        'method': method,
      },
    );

    // Log the share method
    loggingService.info(
      'ShareExperienceDialog',
      'Sharing experience via $method: ${experience.id}',
    );

    // Close the dialog
    Navigator.of(context).pop();

    // Share the experience
    final shareButton = ShareExperienceButton(experience: experience);
    await shareButton._shareExperience(
      context,
      loggingService,
      ref.read(errorHandlingServiceProvider),
      analyticsService,
    );
  }
}
