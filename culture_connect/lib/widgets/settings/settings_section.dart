import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// A section widget for settings screens
class SettingsSection extends StatelessWidget {
  /// The title of the section
  final String? title;

  /// The list of widgets to display in the section
  final List<Widget> children;

  /// Optional margin around the section
  final EdgeInsetsGeometry? margin;

  /// Optional padding inside the section
  final EdgeInsetsGeometry? padding;

  /// Optional background color
  final Color? backgroundColor;

  /// Optional border radius
  final BorderRadius? borderRadius;

  /// Optional elevation
  final double? elevation;

  /// Optional divider color
  final Color? dividerColor;

  /// Whether to show dividers between items
  final bool showDividers;

  /// Creates a settings section widget
  const SettingsSection({
    super.key,
    this.title,
    required this.children,
    this.margin,
    this.padding,
    this.backgroundColor,
    this.borderRadius,
    this.elevation,
    this.dividerColor,
    this.showDividers = true,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    final List<Widget> sectionChildren = [];

    // Add title if provided
    if (title != null) {
      sectionChildren.add(
        Padding(
          padding: EdgeInsets.only(
            left: 16.w,
            right: 16.w,
            top: 16.h,
            bottom: 8.h,
          ),
          child: Text(
            title!,
            style: theme.textTheme.titleSmall?.copyWith(
              color: theme.colorScheme.primary,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
      );
    }

    // Add children with dividers if needed
    for (int i = 0; i < children.length; i++) {
      sectionChildren.add(children[i]);

      if (showDividers && i < children.length - 1) {
        sectionChildren.add(
          Divider(
            height: 1.h,
            thickness: 0.5.h,
            color: dividerColor ?? theme.dividerColor.withAlpha(128),
            indent: 16.w,
            endIndent: 16.w,
          ),
        );
      }
    }

    return Container(
      margin: margin ?? EdgeInsets.symmetric(vertical: 8.h, horizontal: 16.w),
      decoration: BoxDecoration(
        color: backgroundColor ?? theme.colorScheme.surface,
        borderRadius: borderRadius ?? BorderRadius.circular(12.r),
        boxShadow: elevation != null && elevation! > 0
            ? [
                BoxShadow(
                  color: theme.shadowColor.withAlpha(26),
                  blurRadius: elevation! * 2,
                  offset: Offset(0, elevation! / 2),
                ),
              ]
            : null,
      ),
      child: ClipRRect(
        borderRadius: borderRadius ?? BorderRadius.circular(12.r),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: sectionChildren,
        ),
      ),
    );
  }
}
