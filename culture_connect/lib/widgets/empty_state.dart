import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:lottie/lottie.dart';

/// A widget that displays an empty state
class EmptyState extends StatelessWidget {
  /// The title of the empty state
  final String title;
  
  /// The message of the empty state
  final String message;
  
  /// The icon to display
  final IconData? icon;
  
  /// The animation asset to display
  final String? animationAsset;
  
  /// The action button text
  final String? actionText;
  
  /// The action button callback
  final VoidCallback? onAction;
  
  /// Creates a new empty state
  const EmptyState({
    super.key,
    required this.title,
    required this.message,
    this.icon,
    this.animationAsset,
    this.actionText,
    this.onAction,
  }) : assert(icon != null || animationAsset != null, 'Either icon or animationAsset must be provided');
  
  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: EdgeInsets.all(24.r),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            if (animationAsset != null)
              Lottie.asset(
                animationAsset!,
                width: 150.r,
                height: 150.r,
                repeat: true,
              )
            else if (icon != null)
              Icon(
                icon,
                size: 80.r,
                color: Colors.grey[400],
              ),
            SizedBox(height: 24.h),
            Text(
              title,
              style: TextStyle(
                fontSize: 20.sp,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 8.h),
            Text(
              message,
              style: TextStyle(
                fontSize: 16.sp,
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
            if (actionText != null && onAction != null) ...[
              SizedBox(height: 24.h),
              ElevatedButton(
                onPressed: onAction,
                child: Text(actionText!),
              ),
            ],
          ],
        ),
      ),
    );
  }
}
