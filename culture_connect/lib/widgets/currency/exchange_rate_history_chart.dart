import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:intl/intl.dart';

import 'package:culture_connect/models/currency/currency_conversion_history_model.dart';
import 'package:culture_connect/theme/app_theme.dart';

/// A widget for displaying a chart of historical exchange rates
///
/// This widget provides:
/// - Interactive line chart showing exchange rate trends
/// - Customizable styling and colors
/// - Error handling and loading states
/// - Support for offline cached data
/// - Min/max values and trend indicators
class ExchangeRateHistoryChart extends StatelessWidget {
  /// The historical exchange rate data
  final CurrencyConversionHistoryModel historyData;

  /// The height of the chart
  final double height;

  /// The width of the chart (null for full width)
  final double? width;

  /// Whether to show the chart title
  final bool showTitle;

  /// Whether to show min/max values
  final bool showMinMax;

  /// Whether to show trend indicator
  final bool showTrend;

  /// The color of the line
  final Color? lineColor;

  /// The color of the gradient fill
  final Color? gradientColor;

  /// The color of the tooltip
  final Color? tooltipColor;

  /// Creates a new exchange rate history chart
  const ExchangeRateHistoryChart({
    super.key,
    required this.historyData,
    this.height = 200,
    this.width,
    this.showTitle = true,
    this.showMinMax = true,
    this.showTrend = true,
    this.lineColor,
    this.gradientColor,
    this.tooltipColor,
  });

  @override
  Widget build(BuildContext context) {
    // If no data is available, show a message
    if (historyData.dataPoints.isEmpty) {
      return SizedBox(
        height: height,
        width: width,
        child: const Center(
          child: Text(
            'No historical data available',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey,
            ),
          ),
        ),
      );
    }

    return Container(
      height: height,
      width: width,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(13),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title and legend
          if (showTitle) ...[
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Expanded(
                  child: Text(
                    'Exchange Rate History',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                Row(
                  children: [
                    Container(
                      width: 12,
                      height: 4,
                      decoration: BoxDecoration(
                        color: lineColor ?? AppTheme.primaryColor,
                        borderRadius: BorderRadius.circular(2),
                      ),
                    ),
                    const SizedBox(width: 4),
                    Text(
                      '${historyData.baseCurrency}/${historyData.targetCurrency}',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 8),
          ],

          // Chart
          Expanded(
            child: LineChart(
              LineChartData(
                gridData: FlGridData(
                  show: true,
                  drawVerticalLine: false,
                  horizontalInterval: null,
                  getDrawingHorizontalLine: (value) {
                    return FlLine(
                      color: Colors.grey.withAlpha(51),
                      strokeWidth: 1,
                    );
                  },
                ),
                titlesData: FlTitlesData(
                  show: true,
                  rightTitles: const AxisTitles(
                    sideTitles: SideTitles(showTitles: false),
                  ),
                  topTitles: const AxisTitles(
                    sideTitles: SideTitles(showTitles: false),
                  ),
                  bottomTitles: AxisTitles(
                    sideTitles: SideTitles(
                      showTitles: true,
                      reservedSize: 30,
                      interval: historyData.dataPoints.length > 10
                          ? (historyData.dataPoints.length / 5)
                              .ceil()
                              .toDouble()
                          : 1,
                      getTitlesWidget: (value, meta) {
                        final index = value.toInt();
                        if (index >= 0 &&
                            index < historyData.dataPoints.length) {
                          final date = historyData.dataPoints[index].date;
                          return SideTitleWidget(
                            axisSide: meta.axisSide,
                            child: Text(
                              DateFormat('MM/dd').format(date),
                              style: TextStyle(
                                fontSize: 10,
                                color: Colors.grey[600],
                              ),
                            ),
                          );
                        }
                        return const Text('');
                      },
                    ),
                  ),
                  leftTitles: AxisTitles(
                    sideTitles: SideTitles(
                      showTitles: true,
                      reservedSize: 40,
                      getTitlesWidget: (value, meta) {
                        return SideTitleWidget(
                          axisSide: meta.axisSide,
                          child: Text(
                            value.toStringAsFixed(4),
                            style: TextStyle(
                              fontSize: 10,
                              color: Colors.grey[600],
                            ),
                          ),
                        );
                      },
                    ),
                  ),
                ),
                borderData: FlBorderData(
                  show: true,
                  border: Border.all(
                    color: Colors.grey.withAlpha(51),
                    width: 1,
                  ),
                ),
                minX: 0,
                maxX: (historyData.dataPoints.length - 1).toDouble(),
                minY: historyData.minRate * 0.999,
                maxY: historyData.maxRate * 1.001,
                lineTouchData: LineTouchData(
                  enabled: true,
                  touchTooltipData: LineTouchTooltipData(
                    tooltipBgColor:
                        tooltipColor ?? Colors.blueGrey.withAlpha(204),
                    getTooltipItems: (touchedSpots) {
                      return touchedSpots.map((spot) {
                        final index = spot.x.toInt();
                        if (index >= 0 &&
                            index < historyData.dataPoints.length) {
                          final dataPoint = historyData.dataPoints[index];
                          return LineTooltipItem(
                            '${DateFormat('MMM dd').format(dataPoint.date)}\n${dataPoint.rate.toStringAsFixed(4)}',
                            const TextStyle(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                            ),
                          );
                        }
                        return null;
                      }).toList();
                    },
                  ),
                ),
                lineBarsData: [
                  LineChartBarData(
                    spots: historyData.dataPoints
                        .asMap()
                        .entries
                        .map((entry) => FlSpot(
                              entry.key.toDouble(),
                              entry.value.rate,
                            ))
                        .toList(),
                    isCurved: true,
                    color: lineColor ?? AppTheme.primaryColor,
                    barWidth: 2,
                    isStrokeCapRound: true,
                    dotData: const FlDotData(show: false),
                    belowBarData: BarAreaData(
                      show: true,
                      gradient: LinearGradient(
                        colors: [
                          (gradientColor ?? AppTheme.primaryColor)
                              .withAlpha(102),
                          (gradientColor ?? AppTheme.primaryColor).withAlpha(0),
                        ],
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),

          // Min, max, and trend
          if (showMinMax || showTrend) ...[
            const SizedBox(height: 8),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                if (showMinMax)
                  Text(
                    'Min: ${historyData.minRate.toStringAsFixed(4)} | Max: ${historyData.maxRate.toStringAsFixed(4)}',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey[600],
                    ),
                  ),
                if (showTrend)
                  Row(
                    children: [
                      Icon(
                        historyData.isTrendingUp
                            ? Icons.trending_up
                            : historyData.isTrendingDown
                                ? Icons.trending_down
                                : Icons.trending_flat,
                        size: 16,
                        color: historyData.isTrendingUp
                            ? Colors.green
                            : historyData.isTrendingDown
                                ? Colors.red
                                : Colors.grey[600],
                      ),
                      const SizedBox(width: 4),
                      Text(
                        historyData.isTrendingUp
                            ? 'Trending Up'
                            : historyData.isTrendingDown
                                ? 'Trending Down'
                                : 'Stable',
                        style: TextStyle(
                          fontSize: 12,
                          color: historyData.isTrendingUp
                              ? Colors.green
                              : historyData.isTrendingDown
                                  ? Colors.red
                                  : Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
              ],
            ),
          ],

          // Cached data indicator
          if (historyData.isFromCache) ...[
            const SizedBox(height: 4),
            Row(
              children: [
                Icon(
                  Icons.cloud_off,
                  size: 12,
                  color: Colors.amber[700],
                ),
                const SizedBox(width: 4),
                Text(
                  'Using cached historical data',
                  style: TextStyle(
                    fontSize: 10,
                    color: Colors.amber[700],
                    fontStyle: FontStyle.italic,
                  ),
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }
}
