import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:culture_connect/models/message_model.dart';
import 'package:culture_connect/models/translation/message_translation_metadata.dart';
import 'package:culture_connect/providers/message_translation_provider.dart';
import 'package:culture_connect/theme/app_theme.dart';
import 'package:culture_connect/widgets/messaging/message_translation_toggle.dart';
import 'package:culture_connect/widgets/translation/cultural_context_dialog.dart';
import 'package:culture_connect/widgets/translation/cultural_context_indicator.dart';
import 'package:culture_connect/widgets/translation/pronunciation_dialog.dart';
import 'package:culture_connect/widgets/translation/pronunciation_indicator.dart';
import 'package:culture_connect/widgets/translation/slang_idiom_dialog.dart';
import 'package:culture_connect/widgets/translation/slang_idiom_indicator.dart';
import 'package:culture_connect/widgets/translation/translation_confidence_indicator.dart';
import 'package:culture_connect/widgets/translation/translation_feedback_form.dart';

/// A widget for displaying a translated message bubble
class TranslatedMessageBubble extends ConsumerStatefulWidget {
  /// The message to display
  final MessageModel message;

  /// The translation metadata
  final MessageTranslationMetadata translationMetadata;

  /// Whether this is the user's message
  final bool isMe;

  /// Creates a new translated message bubble
  const TranslatedMessageBubble({
    super.key,
    required this.message,
    required this.translationMetadata,
    required this.isMe,
  });

  @override
  ConsumerState<TranslatedMessageBubble> createState() =>
      _TranslatedMessageBubbleState();
}

class _TranslatedMessageBubbleState
    extends ConsumerState<TranslatedMessageBubble> {
  bool _showOriginal = false;
  bool _isPlaying = false;

  @override
  Widget build(BuildContext context) {
    final translationMetadata = widget.translationMetadata;
    final message = widget.message;
    final isMe = widget.isMe;

    return Column(
      crossAxisAlignment:
          isMe ? CrossAxisAlignment.end : CrossAxisAlignment.start,
      children: [
        // Message bubble
        Container(
          margin: const EdgeInsets.only(
            top: 4,
            bottom: 4,
            left: 64,
            right: 0,
          ).copyWith(
            left: isMe ? 64 : 0,
            right: isMe ? 0 : 64,
          ),
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          decoration: BoxDecoration(
            color: isMe ? AppTheme.primaryColor : Colors.grey[200],
            borderRadius: BorderRadius.circular(16).copyWith(
              bottomRight:
                  isMe ? const Radius.circular(0) : const Radius.circular(16),
              bottomLeft:
                  isMe ? const Radius.circular(16) : const Radius.circular(0),
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Translation indicator with confidence
              Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Icon(
                    Icons.translate,
                    size: 12,
                    color: Colors.white70,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    'Translated from ${_getLanguageName(translationMetadata.sourceLanguage)}',
                    style: TextStyle(
                      fontSize: 10,
                      fontStyle: FontStyle.italic,
                      color: isMe ? Colors.white70 : Colors.grey,
                    ),
                  ),

                  // Show confidence indicator if available
                  if (translationMetadata.confidence != null) ...[
                    const SizedBox(width: 8),
                    TranslationConfidenceIndicator(
                      confidence: translationMetadata.confidence!,
                      compact: true,
                      useLight: isMe,
                    ),
                  ],

                  // Show cultural context indicator if available
                  if (translationMetadata.culturalContext != null &&
                      translationMetadata
                          .culturalContext!.hasContextInformation) ...[
                    const SizedBox(width: 8),
                    CulturalContextIndicator(
                      culturalContext: translationMetadata.culturalContext!,
                      compact: true,
                      useLight: isMe,
                      onTap: _showCulturalContextDialog,
                    ),
                  ],

                  // Show slang and idiom indicator if available
                  if (translationMetadata.slangIdiom != null &&
                      translationMetadata.slangIdiom!.hasExpressions) ...[
                    const SizedBox(width: 8),
                    SlangIdiomIndicator(
                      slangIdiom: translationMetadata.slangIdiom!,
                      compact: true,
                      useLight: isMe,
                      onTap: _showSlangIdiomDialog,
                    ),
                  ],

                  // Show pronunciation indicator if available
                  if (translationMetadata.pronunciation != null &&
                      translationMetadata
                          .pronunciation!.hasPronunciationGuides) ...[
                    const SizedBox(width: 8),
                    PronunciationIndicator(
                      pronunciation: translationMetadata.pronunciation!,
                      compact: true,
                      useLight: isMe,
                      onTap: _showPronunciationDialog,
                    ),
                  ],
                ],
              ),

              const SizedBox(height: 4),

              // Message text
              Text(
                _showOriginal
                    ? translationMetadata.originalText
                    : translationMetadata.translatedText,
                style: TextStyle(
                  fontSize: 16,
                  color: isMe ? Colors.white : Colors.black,
                ),
              ),

              // Audio playback button (if available)
              if (translationMetadata.isAudioAvailable)
                Padding(
                  padding: const EdgeInsets.only(top: 8),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      GestureDetector(
                        onTap: _toggleAudio,
                        child: Container(
                          padding: const EdgeInsets.all(6),
                          decoration: BoxDecoration(
                            color: isMe ? Colors.white24 : Colors.grey[300],
                            borderRadius: BorderRadius.circular(16),
                          ),
                          child: Icon(
                            _isPlaying ? Icons.stop : Icons.volume_up,
                            size: 16,
                            color: isMe ? Colors.white : Colors.black54,
                          ),
                        ),
                      ),
                      const SizedBox(width: 8),
                      Text(
                        _isPlaying ? 'Stop' : 'Listen',
                        style: TextStyle(
                          fontSize: 12,
                          color: isMe ? Colors.white70 : Colors.grey,
                        ),
                      ),
                    ],
                  ),
                ),
            ],
          ),
        ),

        // Translation toggle
        Padding(
          padding: EdgeInsets.only(
            left: isMe ? 0 : 12,
            right: isMe ? 12 : 0,
            bottom: 8,
          ),
          child: MessageTranslationToggle(
            message: message,
            isTranslated: !_showOriginal,
            onToggle: (value) {
              setState(() {
                _showOriginal = !value;
              });
            },
          ),
        ),

        // Feedback button
        if (!_showOriginal)
          Padding(
            padding: EdgeInsets.only(
              left: isMe ? 0 : 12,
              right: isMe ? 12 : 0,
              bottom: 8,
            ),
            child: GestureDetector(
              onTap: _showRatingDialog,
              child: const Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    Icons.rate_review_outlined,
                    size: 12,
                    color: Colors.grey,
                  ),
                  SizedBox(width: 4),
                  Text(
                    'Provide Feedback',
                    style: TextStyle(
                      fontSize: 10,
                      color: Colors.grey,
                    ),
                  ),
                ],
              ),
            ),
          ),
      ],
    );
  }

  /// Toggle audio playback
  Future<void> _toggleAudio() async {
    if (_isPlaying) {
      await ref.read(messageTranslationNotifierProvider.notifier).stopAudio();
      setState(() {
        _isPlaying = false;
      });
    } else {
      if (widget.translationMetadata.translatedAudioPath != null) {
        await ref
            .read(messageTranslationNotifierProvider.notifier)
            .playTranslatedAudio(
                widget.translationMetadata.translatedAudioPath!);
        setState(() {
          _isPlaying = true;
        });

        // Auto-stop after playback completes (simulated for demo)
        Future.delayed(const Duration(seconds: 3), () {
          if (mounted) {
            setState(() {
              _isPlaying = false;
            });
          }
        });
      }
    }
  }

  /// Show the feedback form
  void _showRatingDialog() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (context) => Padding(
        padding: EdgeInsets.only(
          bottom: MediaQuery.of(context).viewInsets.bottom,
        ),
        child: SizedBox(
          height: MediaQuery.of(context).size.height * 0.8,
          child: TranslationFeedbackForm(
            message: widget.message,
            translationMetadata: widget.translationMetadata,
            onFeedbackSubmitted: () {
              if (mounted) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Thank you for your detailed feedback!'),
                    backgroundColor: AppTheme.primaryColor,
                    duration: Duration(seconds: 2),
                  ),
                );
              }
            },
          ),
        ),
      ),
    );
  }

  /// Show the cultural context dialog
  void _showCulturalContextDialog() {
    if (widget.translationMetadata.culturalContext == null ||
        !widget.translationMetadata.culturalContext!.hasContextInformation) {
      return;
    }

    showDialog(
      context: context,
      builder: (context) => CulturalContextDialog(
        culturalContext: widget.translationMetadata.culturalContext!,
      ),
    );
  }

  /// Show the slang and idiom dialog
  void _showSlangIdiomDialog() {
    if (widget.translationMetadata.slangIdiom == null ||
        !widget.translationMetadata.slangIdiom!.hasExpressions) {
      return;
    }

    showDialog(
      context: context,
      builder: (context) => SlangIdiomDialog(
        slangIdiom: widget.translationMetadata.slangIdiom!,
      ),
    );
  }

  /// Show the pronunciation dialog
  void _showPronunciationDialog() {
    if (widget.translationMetadata.pronunciation == null ||
        !widget.translationMetadata.pronunciation!.hasPronunciationGuides) {
      return;
    }

    showDialog(
      context: context,
      builder: (context) => PronunciationDialog(
        pronunciation: widget.translationMetadata.pronunciation!,
      ),
    );
  }

  /// Get the language name from a language code
  String _getLanguageName(String code) {
    switch (code) {
      case 'en':
        return 'English';
      case 'fr':
        return 'French';
      case 'yo':
        return 'Yoruba';
      case 'ig':
        return 'Igbo';
      case 'ha':
        return 'Hausa';
      case 'sw':
        return 'Swahili';
      default:
        return code;
    }
  }
}
