import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:culture_connect/models/message_model.dart';
import 'package:culture_connect/providers/chat_provider.dart';
import 'package:culture_connect/theme/app_theme.dart';

/// A widget for displaying message reactions
class MessageReactionsWidget extends ConsumerWidget {
  /// The message to display reactions for
  final MessageModel message;

  /// Whether the message is from the current user
  final bool isMe;

  /// Callback when a reaction is added
  final Function(ReactionType)? onReactionAdded;

  /// Callback when a reaction is removed
  final Function()? onReactionRemoved;

  /// Creates a new message reactions widget
  const MessageReactionsWidget({
    super.key,
    required this.message,
    required this.isMe,
    this.onReactionAdded,
    this.onReactionRemoved,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // If there are no reactions, return an empty container
    if (message.reactions == null || message.reactions!.isEmpty) {
      return const SizedBox.shrink();
    }

    // Group reactions by type
    final reactionCounts = <String, int>{};
    for (final reaction in message.reactions!.values) {
      final type = reaction['type'] ?? '';
      reactionCounts[type] = (reactionCounts[type] ?? 0) + 1;
    }

    return Container(
      margin: EdgeInsets.only(
        top: 4.h,
        left: isMe ? 0 : 16.w,
        right: isMe ? 16.w : 0,
      ),
      child: Align(
        alignment: isMe ? Alignment.centerRight : Alignment.centerLeft,
        child: Wrap(
          spacing: 4.w,
          children: reactionCounts.entries.map((entry) {
            final reactionType = entry.key;
            final count = entry.value;

            // Find the emoji for this reaction type
            String emoji = '👍';
            for (final type in ReactionType.values) {
              if (type.toString().split('.').last == reactionType) {
                emoji = type.emoji;
                break;
              }
            }

            return Container(
              padding: EdgeInsets.symmetric(
                horizontal: 6.w,
                vertical: 2.h,
              ),
              decoration: BoxDecoration(
                color: AppTheme.surfaceColor,
                borderRadius: BorderRadius.circular(12.r),
                border: Border.all(
                  color: Colors.grey.withAlpha(77),
                ),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    emoji,
                    style: TextStyle(
                      fontSize: 12.sp,
                    ),
                  ),
                  SizedBox(width: 2.w),
                  Text(
                    count.toString(),
                    style: TextStyle(
                      fontSize: 12.sp,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            );
          }).toList(),
        ),
      ),
    );
  }
}

/// A widget for adding reactions to a message
class MessageReactionSelector extends ConsumerStatefulWidget {
  /// The message to add reactions to
  final MessageModel message;

  /// Callback when the selector is closed
  final VoidCallback onClose;

  /// Creates a new message reaction selector
  const MessageReactionSelector({
    super.key,
    required this.message,
    required this.onClose,
  });

  @override
  ConsumerState<MessageReactionSelector> createState() =>
      _MessageReactionSelectorState();
}

class _MessageReactionSelectorState
    extends ConsumerState<MessageReactionSelector> {
  bool _isLoading = false;

  Future<void> _addReaction(ReactionType reactionType) async {
    if (_isLoading) return;

    setState(() {
      _isLoading = true;
    });

    try {
      await ref.read(chatProvider.notifier).addReaction(
            widget.message,
            reactionType,
          );

      if (mounted) {
        widget.onClose();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to add reaction: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: 12.w,
        vertical: 8.h,
      ),
      decoration: BoxDecoration(
        color: AppTheme.surfaceColor,
        borderRadius: BorderRadius.circular(24.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(25),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: _isLoading
          ? const SizedBox(
              width: 24,
              height: 24,
              child: CircularProgressIndicator(
                strokeWidth: 2,
              ),
            )
          : Row(
              mainAxisSize: MainAxisSize.min,
              children: ReactionType.values.map((type) {
                return GestureDetector(
                  onTap: () => _addReaction(type),
                  child: Container(
                    margin: EdgeInsets.symmetric(horizontal: 4.w),
                    padding: EdgeInsets.all(8.r),
                    decoration: const BoxDecoration(
                      shape: BoxShape.circle,
                    ),
                    child: Text(
                      type.emoji,
                      style: TextStyle(
                        fontSize: 20.sp,
                      ),
                    ),
                  ),
                );
              }).toList(),
            ),
    );
  }
}
