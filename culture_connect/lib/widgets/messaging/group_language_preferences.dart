// Flutter imports
import 'package:flutter/material.dart';

// Package imports
import 'package:flutter_riverpod/flutter_riverpod.dart';

// Project imports
import 'package:culture_connect/models/translation/language_model.dart';
import 'package:culture_connect/models/translation/group_translation_model.dart';
import 'package:culture_connect/providers/group_translation_provider.dart';
import 'package:culture_connect/providers/auth_provider.dart';
import 'package:culture_connect/theme/app_theme.dart';

/// A widget for managing language preferences in a group chat
class GroupLanguagePreferences extends ConsumerStatefulWidget {
  /// The group chat ID
  final String groupId;

  /// Creates a new group language preferences widget
  const GroupLanguagePreferences({
    super.key,
    required this.groupId,
  });

  @override
  ConsumerState<GroupLanguagePreferences> createState() =>
      _GroupLanguagePreferencesState();
}

class _GroupLanguagePreferencesState
    extends ConsumerState<GroupLanguagePreferences> {
  @override
  Widget build(BuildContext context) {
    final currentUserAsync = ref.watch(currentUserProvider);
    final currentUserId = currentUserAsync.when(
      data: (user) => user?.uid ?? '',
      loading: () => '',
      error: (_, __) => '',
    );

    final groupSettingsAsync =
        ref.watch(groupTranslationSettingsProvider(widget.groupId));
    final participantPreferenceAsync = ref.watch(
      participantLanguagePreferenceProvider((
        groupId: widget.groupId,
        userId: currentUserId,
      )),
    );
    final supportedLanguages = ref.watch(supportedLanguagesProvider);

    return groupSettingsAsync.when(
      data: (settings) {
        return participantPreferenceAsync.when(
          data: (preference) {
            return _buildPreferencesContent(
              settings,
              preference,
              supportedLanguages,
              currentUserId,
            );
          },
          loading: () => const Center(child: CircularProgressIndicator()),
          error: (_, __) => const Center(
            child: Text('Error loading language preferences'),
          ),
        );
      },
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (_, __) => const Center(
        child: Text('Error loading group settings'),
      ),
    );
  }

  /// Build the preferences content
  Widget _buildPreferencesContent(
    GroupTranslationSettings settings,
    ParticipantLanguagePreference? preference,
    List<LanguageModel> supportedLanguages,
    String currentUserId,
  ) {
    // If no preference exists, create a default one
    final userPreference = preference ??
        ParticipantLanguagePreference(
          userId: currentUserId,
          displayName: 'You',
          preferredLanguage: supportedLanguages.firstWhere(
            (lang) => lang.code == 'en',
            orElse: () => supportedLanguages.first,
          ),
        );

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Your language preferences
          const Text(
            'Your Language Preferences',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AppTheme.textPrimaryColor,
            ),
          ),

          const SizedBox(height: 16),

          // Preferred language
          _buildLanguageSelector(
            'Preferred Language',
            'Select the language you want to use in this group',
            userPreference.preferredLanguage,
            supportedLanguages,
            (language) {
              ref
                  .read(groupTranslationNotifierProvider.notifier)
                  .updatePreferredLanguageForParticipant(
                    widget.groupId,
                    currentUserId,
                    language,
                  );
            },
          ),

          const SizedBox(height: 16),

          // Auto-translate toggle
          SwitchListTile(
            title: const Text(
              'Auto-Translate Messages',
              style: TextStyle(
                fontSize: 16,
                color: AppTheme.textPrimaryColor,
              ),
            ),
            subtitle: const Text(
              'Automatically translate messages to your preferred language',
              style: TextStyle(
                fontSize: 12,
                color: AppTheme.textSecondaryColor,
              ),
            ),
            value: userPreference.autoTranslate,
            onChanged: (value) {
              ref
                  .read(groupTranslationNotifierProvider.notifier)
                  .toggleAutoTranslateForParticipant(
                    widget.groupId,
                    currentUserId,
                    value,
                  );
            },
            activeColor: AppTheme.primaryColor,
          ),

          // Show original text toggle
          SwitchListTile(
            title: const Text(
              'Show Original Text',
              style: TextStyle(
                fontSize: 16,
                color: AppTheme.textPrimaryColor,
              ),
            ),
            subtitle: const Text(
              'Show the original text alongside translations',
              style: TextStyle(
                fontSize: 12,
                color: AppTheme.textSecondaryColor,
              ),
            ),
            value: userPreference.showOriginalText,
            onChanged: (value) {
              ref
                  .read(groupTranslationNotifierProvider.notifier)
                  .toggleShowOriginalTextForParticipant(
                    widget.groupId,
                    currentUserId,
                    value,
                  );
            },
            activeColor: AppTheme.primaryColor,
          ),

          // Dialect recognition toggle
          SwitchListTile(
            title: const Text(
              'Use Dialect Recognition',
              style: TextStyle(
                fontSize: 16,
                color: AppTheme.textPrimaryColor,
              ),
            ),
            subtitle: const Text(
              'Recognize and adapt to specific dialects of your language',
              style: TextStyle(
                fontSize: 12,
                color: AppTheme.textSecondaryColor,
              ),
            ),
            value: userPreference.useDialectRecognition,
            onChanged: (value) {
              final updatedPreference = userPreference.copyWith(
                useDialectRecognition: value,
              );
              ref
                  .read(groupTranslationNotifierProvider.notifier)
                  .updateParticipantPreference(
                    widget.groupId,
                    updatedPreference,
                  );
            },
            activeColor: AppTheme.primaryColor,
          ),

          const SizedBox(height: 24),

          // Group language settings
          const Text(
            'Group Translation Settings',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AppTheme.textPrimaryColor,
            ),
          ),

          const SizedBox(height: 16),

          // Auto-detect languages toggle
          SwitchListTile(
            title: const Text(
              'Auto-Detect Languages',
              style: TextStyle(
                fontSize: 16,
                color: AppTheme.textPrimaryColor,
              ),
            ),
            subtitle: const Text(
              'Automatically detect the language of messages',
              style: TextStyle(
                fontSize: 12,
                color: AppTheme.textSecondaryColor,
              ),
            ),
            value: settings.autoDetectLanguages,
            onChanged: (value) {
              ref
                  .read(groupTranslationNotifierProvider.notifier)
                  .toggleFeatureForGroup(
                    widget.groupId,
                    'autoDetectLanguages',
                    value,
                  );
            },
            activeColor: AppTheme.primaryColor,
          ),

          // Show translation indicators toggle
          SwitchListTile(
            title: const Text(
              'Show Translation Indicators',
              style: TextStyle(
                fontSize: 16,
                color: AppTheme.textPrimaryColor,
              ),
            ),
            subtitle: const Text(
              'Show indicators for translated messages',
              style: TextStyle(
                fontSize: 12,
                color: AppTheme.textSecondaryColor,
              ),
            ),
            value: settings.showTranslationIndicators,
            onChanged: (value) {
              ref
                  .read(groupTranslationNotifierProvider.notifier)
                  .toggleFeatureForGroup(
                    widget.groupId,
                    'showTranslationIndicators',
                    value,
                  );
            },
            activeColor: AppTheme.primaryColor,
          ),

          // Real-time translation toggle
          SwitchListTile(
            title: const Text(
              'Real-Time Translation',
              style: TextStyle(
                fontSize: 16,
                color: AppTheme.textPrimaryColor,
              ),
            ),
            subtitle: const Text(
              'Translate messages in real-time as they arrive',
              style: TextStyle(
                fontSize: 12,
                color: AppTheme.textSecondaryColor,
              ),
            ),
            value: settings.enableRealTimeTranslation,
            onChanged: (value) {
              ref
                  .read(groupTranslationNotifierProvider.notifier)
                  .toggleFeatureForGroup(
                    widget.groupId,
                    'enableRealTimeTranslation',
                    value,
                  );
            },
            activeColor: AppTheme.primaryColor,
          ),

          // Translate media captions toggle
          SwitchListTile(
            title: const Text(
              'Translate Media Captions',
              style: TextStyle(
                fontSize: 16,
                color: AppTheme.textPrimaryColor,
              ),
            ),
            subtitle: const Text(
              'Translate captions for images, videos, and other media',
              style: TextStyle(
                fontSize: 12,
                color: AppTheme.textSecondaryColor,
              ),
            ),
            value: settings.translateMediaCaptions,
            onChanged: (value) {
              ref
                  .read(groupTranslationNotifierProvider.notifier)
                  .toggleFeatureForGroup(
                    widget.groupId,
                    'translateMediaCaptions',
                    value,
                  );
            },
            activeColor: AppTheme.primaryColor,
          ),

          const SizedBox(height: 24),

          // Enhanced translation features
          const Text(
            'Enhanced Translation Features',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AppTheme.textPrimaryColor,
            ),
          ),

          const SizedBox(height: 16),

          // Cultural context toggle
          SwitchListTile(
            title: const Text(
              'Cultural Context',
              style: TextStyle(
                fontSize: 16,
                color: AppTheme.textPrimaryColor,
              ),
            ),
            subtitle: const Text(
              'Show cultural context information for translations',
              style: TextStyle(
                fontSize: 12,
                color: AppTheme.textSecondaryColor,
              ),
            ),
            value: settings.enableCulturalContext,
            onChanged: (value) {
              ref
                  .read(groupTranslationNotifierProvider.notifier)
                  .toggleFeatureForGroup(
                    widget.groupId,
                    'enableCulturalContext',
                    value,
                  );
            },
            activeColor: AppTheme.primaryColor,
          ),

          // Slang and idiom detection toggle
          SwitchListTile(
            title: const Text(
              'Slang & Idiom Detection',
              style: TextStyle(
                fontSize: 16,
                color: AppTheme.textPrimaryColor,
              ),
            ),
            subtitle: const Text(
              'Detect and explain slang and idioms in translations',
              style: TextStyle(
                fontSize: 12,
                color: AppTheme.textSecondaryColor,
              ),
            ),
            value: settings.enableSlangIdiomDetection,
            onChanged: (value) {
              ref
                  .read(groupTranslationNotifierProvider.notifier)
                  .toggleFeatureForGroup(
                    widget.groupId,
                    'enableSlangIdiomDetection',
                    value,
                  );
            },
            activeColor: AppTheme.primaryColor,
          ),

          // Pronunciation guidance toggle
          SwitchListTile(
            title: const Text(
              'Pronunciation Guidance',
              style: TextStyle(
                fontSize: 16,
                color: AppTheme.textPrimaryColor,
              ),
            ),
            subtitle: const Text(
              'Show pronunciation guides for translations',
              style: TextStyle(
                fontSize: 12,
                color: AppTheme.textSecondaryColor,
              ),
            ),
            value: settings.enablePronunciationGuidance,
            onChanged: (value) {
              ref
                  .read(groupTranslationNotifierProvider.notifier)
                  .toggleFeatureForGroup(
                    widget.groupId,
                    'enablePronunciationGuidance',
                    value,
                  );
            },
            activeColor: AppTheme.primaryColor,
          ),

          const SizedBox(height: 24),

          // Group language statistics
          _buildGroupLanguageStatistics(settings),

          const SizedBox(height: 24),

          // Clear cache button
          Center(
            child: ElevatedButton.icon(
              onPressed: () {
                ref
                    .read(groupTranslationNotifierProvider.notifier)
                    .clearGroupTranslationCache(
                      widget.groupId,
                    );
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Translation cache cleared'),
                    duration: Duration(seconds: 2),
                  ),
                );
              },
              icon: const Icon(Icons.delete_outline),
              label: const Text('Clear Translation Cache'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Build a language selector
  Widget _buildLanguageSelector(
    String title,
    String subtitle,
    LanguageModel selectedLanguage,
    List<LanguageModel> languages,
    Function(LanguageModel) onChanged,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: AppTheme.textPrimaryColor,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          subtitle,
          style: const TextStyle(
            fontSize: 12,
            color: AppTheme.textSecondaryColor,
          ),
        ),
        const SizedBox(height: 8),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 12),
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey[300]!),
            borderRadius: BorderRadius.circular(8),
          ),
          child: DropdownButtonHideUnderline(
            child: DropdownButton<String>(
              value: selectedLanguage.code,
              isExpanded: true,
              icon: const Icon(Icons.arrow_drop_down),
              iconSize: 24,
              elevation: 16,
              style: const TextStyle(
                fontSize: 16,
                color: AppTheme.textPrimaryColor,
              ),
              onChanged: (String? value) {
                if (value != null) {
                  final language = languages.firstWhere(
                    (lang) => lang.code == value,
                  );
                  onChanged(language);
                }
              },
              items: languages
                  .map<DropdownMenuItem<String>>((LanguageModel language) {
                return DropdownMenuItem<String>(
                  value: language.code,
                  child: Text(language.name),
                );
              }).toList(),
            ),
          ),
        ),
      ],
    );
  }

  /// Build group language statistics
  Widget _buildGroupLanguageStatistics(GroupTranslationSettings settings) {
    final uniqueLanguages = settings.uniqueLanguages;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Languages Used in This Group',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: AppTheme.textPrimaryColor,
          ),
        ),
        const SizedBox(height: 16),
        if (uniqueLanguages.isEmpty)
          const Text(
            'No language preferences set by group members yet.',
            style: TextStyle(
              fontSize: 14,
              fontStyle: FontStyle.italic,
              color: AppTheme.textSecondaryColor,
            ),
          )
        else
          ListView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: uniqueLanguages.length,
            itemBuilder: (context, index) {
              final language = uniqueLanguages[index];
              final count =
                  settings.getParticipantCountForLanguage(language.code);

              return ListTile(
                leading: CircleAvatar(
                  backgroundColor: AppTheme.primaryColor,
                  child: Text(
                    language.code.toUpperCase(),
                    style: const TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                ),
                title: Text(
                  language.name,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: AppTheme.textPrimaryColor,
                  ),
                ),
                subtitle: Text(
                  '$count ${count == 1 ? 'member' : 'members'} using this language',
                  style: const TextStyle(
                    fontSize: 12,
                    color: AppTheme.textSecondaryColor,
                  ),
                ),
                trailing: Text(
                  '${(count / settings.participantPreferences.length * 100).toStringAsFixed(0)}%',
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: AppTheme.primaryColor,
                  ),
                ),
              );
            },
          ),
      ],
    );
  }
}
