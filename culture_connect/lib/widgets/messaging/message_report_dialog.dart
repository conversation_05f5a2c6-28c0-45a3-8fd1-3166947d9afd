import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:culture_connect/models/message_model.dart';
import 'package:culture_connect/models/report_model.dart';
import 'package:culture_connect/providers/chat_provider.dart';
import 'package:culture_connect/services/report_service.dart';
import 'package:culture_connect/theme/app_theme.dart';

/// A dialog for reporting messages
class MessageReportDialog extends ConsumerStatefulWidget {
  /// The message to report
  final MessageModel message;

  /// The name of the sender
  final String senderName;

  /// Callback when the report is submitted
  final Function(Report)? onReportSubmitted;

  /// Creates a new message report dialog
  const MessageReportDialog({
    super.key,
    required this.message,
    required this.senderName,
    this.onReportSubmitted,
  });

  /// Shows the message report dialog
  static Future<Report?> show({
    required BuildContext context,
    required MessageModel message,
    required String senderName,
    Function(Report)? onReportSubmitted,
  }) {
    return showDialog<Report>(
      context: context,
      builder: (context) => MessageReportDialog(
        message: message,
        senderName: senderName,
        onReportSubmitted: onReportSubmitted,
      ),
    );
  }

  @override
  ConsumerState<MessageReportDialog> createState() =>
      _MessageReportDialogState();
}

class _MessageReportDialogState extends ConsumerState<MessageReportDialog> {
  final _formKey = GlobalKey<FormState>();
  final _additionalInfoController = TextEditingController();

  String? _selectedReason;
  bool _isLoading = false;
  String? _errorMessage;

  final List<String> _reportReasons = [
    'Inappropriate content',
    'Harassment or bullying',
    'Spam or scam',
    'Hate speech',
    'Threatening or violent content',
    'Personal information sharing',
    'Other',
  ];

  @override
  void dispose() {
    _additionalInfoController.dispose();
    super.dispose();
  }

  Future<void> _submitReport() async {
    if (_formKey.currentState?.validate() != true) {
      return;
    }

    if (_selectedReason == null) {
      setState(() {
        _errorMessage = 'Please select a reason for reporting';
      });
      return;
    }

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final reportService = ref.read(reportServiceProvider);

      // Create a report for the message
      final report = await reportService.submitReport(
        reportedEntityId: widget.message.id,
        type: ReportType.message,
        reason: _selectedReason!,
        description: _additionalInfoController.text,
        severity: ReportSeverity.medium,
        evidence: [],
      );

      // Mark the message as reported
      await ref.read(chatProvider.notifier).reportMessage(widget.message);

      if (mounted) {
        widget.onReportSubmitted?.call(report);
        Navigator.of(context).pop(report);
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Failed to submit report: $e';
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Form(
          key: _formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Dialog title
              Row(
                children: [
                  Icon(
                    Icons.report_problem,
                    color: theme.colorScheme.error,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'Report Message',
                      style: theme.textTheme.titleLarge,
                    ),
                  ),
                  IconButton(
                    icon: const Icon(Icons.close),
                    onPressed: () => Navigator.of(context).pop(),
                  ),
                ],
              ),

              // Message preview
              Container(
                margin: const EdgeInsets.symmetric(vertical: 16),
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: AppTheme.secondaryColor.withAlpha(25),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: AppTheme.secondaryColor.withAlpha(77),
                  ),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Message from ${widget.senderName}',
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 14,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      widget.message.text,
                      style: const TextStyle(fontSize: 14),
                      maxLines: 3,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),

              // Report reason
              const Text(
                'Reason for reporting',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                ),
              ),
              const SizedBox(height: 8),

              // Reason selection
              Container(
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey.shade300),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: DropdownButtonHideUnderline(
                  child: DropdownButton<String>(
                    value: _selectedReason,
                    isExpanded: true,
                    hint: const Padding(
                      padding: EdgeInsets.symmetric(horizontal: 16),
                      child: Text('Select a reason'),
                    ),
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    borderRadius: BorderRadius.circular(8),
                    items: _reportReasons.map((reason) {
                      return DropdownMenuItem<String>(
                        value: reason,
                        child: Text(reason),
                      );
                    }).toList(),
                    onChanged: (value) {
                      setState(() {
                        _selectedReason = value;
                      });
                    },
                  ),
                ),
              ),

              // Additional information
              const SizedBox(height: 16),
              const Text(
                'Additional information',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                ),
              ),
              const SizedBox(height: 8),
              TextFormField(
                controller: _additionalInfoController,
                decoration: InputDecoration(
                  hintText: 'Provide more details about the issue',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                maxLines: 3,
              ),

              // Error message
              if (_errorMessage != null) ...[
                const SizedBox(height: 16),
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: theme.colorScheme.error.withAlpha(25),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.error_outline,
                        color: theme.colorScheme.error,
                        size: 20,
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          _errorMessage!,
                          style: TextStyle(
                            color: theme.colorScheme.error,
                            fontSize: 14,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],

              // Action buttons
              const SizedBox(height: 16),
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  TextButton(
                    onPressed:
                        _isLoading ? null : () => Navigator.of(context).pop(),
                    child: const Text('Cancel'),
                  ),
                  const SizedBox(width: 8),
                  ElevatedButton(
                    onPressed: _isLoading ? null : _submitReport,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: theme.colorScheme.error,
                      foregroundColor: Colors.white,
                    ),
                    child: _isLoading
                        ? const SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor:
                                  AlwaysStoppedAnimation<Color>(Colors.white),
                            ),
                          )
                        : const Text('Submit Report'),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
