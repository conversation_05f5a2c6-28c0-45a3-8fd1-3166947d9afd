import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:culture_connect/models/message_model.dart';
import 'package:culture_connect/models/translation/message_translation_metadata.dart';
import 'package:culture_connect/providers/group_translation_provider.dart';
import 'package:culture_connect/providers/auth_provider.dart';
import 'package:culture_connect/theme/app_theme.dart';
import 'package:culture_connect/widgets/messaging/message_translation_toggle.dart';
import 'package:culture_connect/widgets/translation/cultural_context_dialog.dart';
import 'package:culture_connect/widgets/translation/cultural_context_indicator.dart';
import 'package:culture_connect/widgets/translation/pronunciation_dialog.dart';
import 'package:culture_connect/widgets/translation/pronunciation_indicator.dart';
import 'package:culture_connect/widgets/translation/slang_idiom_dialog.dart';
import 'package:culture_connect/widgets/translation/slang_idiom_indicator.dart';
import 'package:culture_connect/widgets/translation/translation_confidence_indicator.dart';
import 'package:culture_connect/widgets/translation/offline_translation_indicator.dart';

/// A widget for displaying a translated message in a group chat
class GroupTranslatedMessageBubble extends ConsumerStatefulWidget {
  /// The message to display
  final MessageModel message;

  /// The group chat ID
  final String groupId;

  /// The sender's name
  final String senderName;

  /// The sender's avatar URL
  final String? senderAvatarUrl;

  /// Whether the message is from the current user
  final bool isMe;

  /// Callback when the message is long-pressed
  final VoidCallback? onLongPress;

  /// Creates a new group translated message bubble
  const GroupTranslatedMessageBubble({
    super.key,
    required this.message,
    required this.groupId,
    required this.senderName,
    this.senderAvatarUrl,
    required this.isMe,
    this.onLongPress,
  });

  @override
  ConsumerState<GroupTranslatedMessageBubble> createState() =>
      _GroupTranslatedMessageBubbleState();
}

class _GroupTranslatedMessageBubbleState
    extends ConsumerState<GroupTranslatedMessageBubble> {
  bool _showOriginal = false;

  @override
  Widget build(BuildContext context) {
    final currentUserAsync = ref.watch(currentUserProvider);
    final currentUserId = currentUserAsync.value?.uid ?? '';

    // Get translation metadata for the current user
    final translationMetadataAsync = ref.watch(
      translationMetadataForUserProvider((
        message: widget.message,
        userId: currentUserId,
        groupId: widget.groupId,
      )),
    );

    // Get translated text for the current user
    final translatedTextAsync = ref.watch(
      translatedTextForUserProvider((
        message: widget.message,
        userId: currentUserId,
        groupId: widget.groupId,
      )),
    );

    // Get participant language preference
    final participantPreferenceAsync = ref.watch(
      participantLanguagePreferenceProvider((
        groupId: widget.groupId,
        userId: currentUserId,
      )),
    );

    return translationMetadataAsync.when(
      data: (translationMetadata) {
        // If no translation metadata, show original message
        if (translationMetadata == null) {
          return _buildMessageBubble(
            widget.message.text,
            null,
            false,
          );
        }

        return translatedTextAsync.when(
          data: (translatedText) {
            return participantPreferenceAsync.when(
              data: (preference) {
                final showOriginalText = preference?.showOriginalText ?? false;

                return _buildMessageBubble(
                  translatedText,
                  translationMetadata,
                  showOriginalText,
                );
              },
              loading: () => _buildLoadingBubble(),
              error: (_, __) => _buildMessageBubble(
                widget.message.text,
                null,
                false,
              ),
            );
          },
          loading: () => _buildLoadingBubble(),
          error: (_, __) => _buildMessageBubble(
            widget.message.text,
            null,
            false,
          ),
        );
      },
      loading: () => _buildLoadingBubble(),
      error: (_, __) => _buildMessageBubble(
        widget.message.text,
        null,
        false,
      ),
    );
  }

  /// Build a loading bubble
  Widget _buildLoadingBubble() {
    return Container(
      margin: EdgeInsets.symmetric(vertical: 4.h),
      child: Row(
        mainAxisAlignment:
            widget.isMe ? MainAxisAlignment.end : MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (!widget.isMe) ...[
            CircleAvatar(
              radius: 16.r,
              backgroundImage: widget.senderAvatarUrl != null
                  ? NetworkImage(widget.senderAvatarUrl!)
                  : null,
              child: widget.senderAvatarUrl == null
                  ? Icon(Icons.person, size: 16.r)
                  : null,
            ),
            SizedBox(width: 8.w),
          ],
          Container(
            padding: EdgeInsets.all(12.r),
            constraints: BoxConstraints(
              maxWidth: MediaQuery.of(context).size.width * 0.7,
            ),
            decoration: BoxDecoration(
              color: widget.isMe ? AppTheme.primaryColor : Colors.grey[200],
              borderRadius: BorderRadius.circular(16.r),
            ),
            child: Center(
              child: SizedBox(
                width: 20.w,
                height: 20.h,
                child: CircularProgressIndicator(
                  strokeWidth: 2.w,
                  valueColor: AlwaysStoppedAnimation<Color>(
                    widget.isMe ? Colors.white : AppTheme.primaryColor,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Build a message bubble
  Widget _buildMessageBubble(
    String text,
    MessageTranslationMetadata? translationMetadata,
    bool showOriginalText,
  ) {
    final isTranslated = translationMetadata != null;
    final originalText = isTranslated ? translationMetadata.originalText : text;
    final translatedText =
        isTranslated ? translationMetadata.translatedText : text;

    return Container(
      margin: EdgeInsets.symmetric(vertical: 4.h),
      child: Row(
        mainAxisAlignment:
            widget.isMe ? MainAxisAlignment.end : MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (!widget.isMe) ...[
            CircleAvatar(
              radius: 16.r,
              backgroundImage: widget.senderAvatarUrl != null
                  ? NetworkImage(widget.senderAvatarUrl!)
                  : null,
              child: widget.senderAvatarUrl == null
                  ? Icon(Icons.person, size: 16.r)
                  : null,
            ),
            SizedBox(width: 8.w),
          ],
          Flexible(
            child: Column(
              crossAxisAlignment: widget.isMe
                  ? CrossAxisAlignment.end
                  : CrossAxisAlignment.start,
              children: [
                // Sender name (only for group chats and not the current user)
                if (!widget.isMe)
                  Padding(
                    padding: EdgeInsets.only(left: 8.w, bottom: 2.h),
                    child: Text(
                      widget.senderName,
                      style: TextStyle(
                        fontSize: 12.sp,
                        fontWeight: FontWeight.bold,
                        color: AppTheme.textSecondaryColor,
                      ),
                    ),
                  ),

                // Message bubble
                GestureDetector(
                  onLongPress: widget.onLongPress,
                  child: Container(
                    padding: EdgeInsets.all(12.r),
                    decoration: BoxDecoration(
                      color: widget.isMe
                          ? AppTheme.primaryColor
                          : Colors.grey[200],
                      borderRadius: BorderRadius.circular(16.r),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Original language indicator
                        if (isTranslated)
                          Container(
                            padding: EdgeInsets.symmetric(
                              horizontal: 6.w,
                              vertical: 2.h,
                            ),
                            margin: EdgeInsets.only(bottom: 4.h),
                            decoration: BoxDecoration(
                              color: widget.isMe
                                  ? Colors.white.withAlpha(50)
                                  : Colors.grey[300],
                              borderRadius: BorderRadius.circular(4.r),
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Icon(
                                  Icons.translate,
                                  size: 12.r,
                                  color: widget.isMe
                                      ? Colors.white
                                      : AppTheme.textSecondaryColor,
                                ),
                                SizedBox(width: 4.w),
                                Text(
                                  'Translated from ${_getLanguageName(translationMetadata.sourceLanguage)}',
                                  style: TextStyle(
                                    fontSize: 10.sp,
                                    color: widget.isMe
                                        ? Colors.white
                                        : AppTheme.textSecondaryColor,
                                  ),
                                ),
                              ],
                            ),
                          ),

                        // Message text
                        Text(
                          _showOriginal ? originalText : translatedText,
                          style: TextStyle(
                            fontSize: 14.sp,
                            color: widget.isMe
                                ? Colors.white
                                : AppTheme.textPrimaryColor,
                          ),
                        ),

                        // Show original text if enabled
                        if (isTranslated &&
                            showOriginalText &&
                            !_showOriginal) ...[
                          SizedBox(height: 8.h),
                          Container(
                            width: double.infinity,
                            padding: EdgeInsets.all(8.r),
                            decoration: BoxDecoration(
                              color: widget.isMe
                                  ? Colors.white.withAlpha(25)
                                  : Colors.grey[100],
                              borderRadius: BorderRadius.circular(8.r),
                              border: Border.all(
                                color: widget.isMe
                                    ? Colors.white.withAlpha(50)
                                    : Colors.grey[300]!,
                                width: 1.w,
                              ),
                            ),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'Original (${_getLanguageName(translationMetadata.sourceLanguage)}):',
                                  style: TextStyle(
                                    fontSize: 10.sp,
                                    fontWeight: FontWeight.bold,
                                    color: widget.isMe
                                        ? Colors.white.withAlpha(204)
                                        : AppTheme.textSecondaryColor,
                                  ),
                                ),
                                SizedBox(height: 4.h),
                                Text(
                                  originalText,
                                  style: TextStyle(
                                    fontSize: 12.sp,
                                    fontStyle: FontStyle.italic,
                                    color: widget.isMe
                                        ? Colors.white.withAlpha(230)
                                        : AppTheme.textPrimaryColor,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],

                        // Translation indicators
                        if (isTranslated) ...[
                          SizedBox(height: 8.h),
                          Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              // Translation toggle
                              MessageTranslationToggle(
                                message: widget.message,
                                isTranslated: true,
                                showOriginal: _showOriginal,
                                onToggle: (bool value) {
                                  setState(() {
                                    _showOriginal = value;
                                  });
                                },
                                useLight: widget.isMe,
                              ),

                              // Show offline indicator if available
                              SizedBox(width: 8.w),
                              Consumer(
                                builder: (context, ref, child) {
                                  final syncStatus = ref.watch(
                                      messageSyncStatusProvider(
                                          widget.message.id));
                                  return OfflineTranslationIndicator(
                                    messageId: widget.message.id,
                                    syncStatus: syncStatus,
                                    size: 14.r,
                                    color: widget.isMe
                                        ? Colors.white
                                        : AppTheme.primaryColor,
                                  );
                                },
                              ),

                              // Show confidence indicator if available
                              if (translationMetadata.confidence != null) ...[
                                SizedBox(width: 8.w),
                                TranslationConfidenceIndicator(
                                  confidence: translationMetadata.confidence!,
                                  compact: true,
                                  useLight: widget.isMe,
                                ),
                              ],

                              // Show cultural context indicator if available
                              if (translationMetadata.culturalContext != null &&
                                  translationMetadata.culturalContext!
                                      .hasContextInformation) ...[
                                SizedBox(width: 8.w),
                                CulturalContextIndicator(
                                  culturalContext:
                                      translationMetadata.culturalContext!,
                                  compact: true,
                                  useLight: widget.isMe,
                                  onTap: () => _showCulturalContextDialog(
                                      translationMetadata),
                                ),
                              ],

                              // Show slang and idiom indicator if available
                              if (translationMetadata.slangIdiom != null &&
                                  translationMetadata
                                      .slangIdiom!.hasExpressions) ...[
                                SizedBox(width: 8.w),
                                SlangIdiomIndicator(
                                  slangIdiom: translationMetadata.slangIdiom!,
                                  compact: true,
                                  useLight: widget.isMe,
                                  onTap: () => _showSlangIdiomDialog(
                                      translationMetadata),
                                ),
                              ],

                              // Show pronunciation indicator if available
                              if (translationMetadata.pronunciation != null &&
                                  translationMetadata.pronunciation!
                                      .hasPronunciationGuides) ...[
                                SizedBox(width: 8.w),
                                PronunciationIndicator(
                                  pronunciation:
                                      translationMetadata.pronunciation!,
                                  compact: true,
                                  useLight: widget.isMe,
                                  onTap: () => _showPronunciationDialog(
                                      translationMetadata),
                                ),
                              ],
                            ],
                          ),
                        ],
                      ],
                    ),
                  ),
                ),

                // Timestamp
                Padding(
                  padding: EdgeInsets.only(
                    top: 4.h,
                    left: widget.isMe ? 0 : 8.w,
                    right: widget.isMe ? 8.w : 0,
                  ),
                  child: Text(
                    _formatTimestamp(widget.message.timestamp),
                    style: TextStyle(
                      fontSize: 10.sp,
                      color: AppTheme.textSecondaryColor,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Format a timestamp
  String _formatTimestamp(DateTime timestamp) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final messageDate =
        DateTime(timestamp.year, timestamp.month, timestamp.day);

    if (messageDate == today) {
      // Today, show time only
      return '${timestamp.hour.toString().padLeft(2, '0')}:${timestamp.minute.toString().padLeft(2, '0')}';
    } else if (messageDate == today.subtract(const Duration(days: 1))) {
      // Yesterday
      return 'Yesterday, ${timestamp.hour.toString().padLeft(2, '0')}:${timestamp.minute.toString().padLeft(2, '0')}';
    } else {
      // Other days, show date and time
      return '${timestamp.day}/${timestamp.month}/${timestamp.year}, ${timestamp.hour.toString().padLeft(2, '0')}:${timestamp.minute.toString().padLeft(2, '0')}';
    }
  }

  /// Get the language name from a language code
  String _getLanguageName(String code) {
    switch (code) {
      case 'en':
        return 'English';
      case 'fr':
        return 'French';
      case 'es':
        return 'Spanish';
      case 'yo':
        return 'Yoruba';
      case 'ig':
        return 'Igbo';
      case 'ha':
        return 'Hausa';
      case 'sw':
        return 'Swahili';
      default:
        return code;
    }
  }

  /// Show the cultural context dialog
  void _showCulturalContextDialog(MessageTranslationMetadata metadata) {
    if (metadata.culturalContext == null ||
        !metadata.culturalContext!.hasContextInformation) {
      return;
    }

    showDialog(
      context: context,
      builder: (context) => CulturalContextDialog(
        culturalContext: metadata.culturalContext!,
      ),
    );
  }

  /// Show the slang and idiom dialog
  void _showSlangIdiomDialog(MessageTranslationMetadata metadata) {
    if (metadata.slangIdiom == null || !metadata.slangIdiom!.hasExpressions) {
      return;
    }

    showDialog(
      context: context,
      builder: (context) => SlangIdiomDialog(
        slangIdiom: metadata.slangIdiom!,
      ),
    );
  }

  /// Show the pronunciation dialog
  void _showPronunciationDialog(MessageTranslationMetadata metadata) {
    if (metadata.pronunciation == null ||
        !metadata.pronunciation!.hasPronunciationGuides) {
      return;
    }

    showDialog(
      context: context,
      builder: (context) => PronunciationDialog(
        pronunciation: metadata.pronunciation!,
      ),
    );
  }
}
