import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:culture_connect/services/offline_message_service.dart';

/// Widget to display the offline message status
class OfflineMessageStatus extends ConsumerWidget {
  /// Creates a new offline message status widget
  const OfflineMessageStatus({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final isOnline = ref.watch(isOnlineProvider);
    final syncStatusAsync = ref.watch(syncStatusProvider);
    final syncStatusStreamAsync = ref.watch(syncStatusStreamProvider);

    return syncStatusAsync.when(
      data: (syncStatus) {
        final pendingCount = syncStatus['pendingCount'] as int;
        final isSyncing = syncStatus['isSyncing'] as bool;

        if (!isOnline) {
          return _buildOfflineIndicator(context, pendingCount);
        }

        if (pendingCount > 0) {
          return syncStatusStreamAsync.when(
            data: (streamStatus) {
              if (streamStatus == SyncStatus.syncing || isSyncing) {
                return _buildSyncingIndicator(context, pendingCount);
              } else {
                return _buildPendingIndicator(context, pendingCount);
              }
            },
            loading: () => _buildPendingIndicator(context, pendingCount),
            error: (_, __) => _buildPendingIndicator(context, pendingCount),
          );
        }

        return const SizedBox.shrink();
      },
      loading: () => const SizedBox.shrink(),
      error: (_, __) => const SizedBox.shrink(),
    );
  }

  Widget _buildOfflineIndicator(BuildContext context, int pendingCount) {
    final theme = Theme.of(context);

    return Container(
      padding: EdgeInsets.symmetric(vertical: 4.h, horizontal: 8.w),
      color: Colors.orange.shade700,
      child: Row(
        children: [
          Icon(
            Icons.wifi_off,
            size: 16.r,
            color: Colors.white,
          ),
          SizedBox(width: 8.w),
          Expanded(
            child: Text(
              pendingCount > 0
                  ? 'You\'re offline. $pendingCount ${pendingCount == 1 ? 'message' : 'messages'} will be sent when you reconnect.'
                  : 'You\'re offline. Messages will be sent when you reconnect.',
              style: theme.textTheme.bodySmall?.copyWith(
                color: Colors.white,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSyncingIndicator(BuildContext context, int pendingCount) {
    final theme = Theme.of(context);

    return Container(
      padding: EdgeInsets.symmetric(vertical: 4.h, horizontal: 8.w),
      color: Colors.blue.shade700,
      child: Row(
        children: [
          SizedBox(
            width: 16.r,
            height: 16.r,
            child: CircularProgressIndicator(
              strokeWidth: 2.r,
              valueColor: const AlwaysStoppedAnimation<Color>(Colors.white),
            ),
          ),
          SizedBox(width: 8.w),
          Expanded(
            child: Text(
              'Syncing $pendingCount ${pendingCount == 1 ? 'message' : 'messages'}...',
              style: theme.textTheme.bodySmall?.copyWith(
                color: Colors.white,
              ),
            ),
          ),
          TextButton(
            onPressed: null, // Disabled during sync
            style: TextButton.styleFrom(
              padding: EdgeInsets.symmetric(horizontal: 8.w),
              minimumSize: Size.zero,
              tapTargetSize: MaterialTapTargetSize.shrinkWrap,
              foregroundColor: Colors.white.withAlpha(128),
            ),
            child: const Text('SYNCING'),
          ),
        ],
      ),
    );
  }

  Widget _buildPendingIndicator(BuildContext context, int pendingCount) {
    final theme = Theme.of(context);

    return Container(
      padding: EdgeInsets.symmetric(vertical: 4.h, horizontal: 8.w),
      color: Colors.blue.shade600,
      child: Row(
        children: [
          Icon(
            Icons.sync,
            size: 16.r,
            color: Colors.white,
          ),
          SizedBox(width: 8.w),
          Expanded(
            child: Text(
              '$pendingCount ${pendingCount == 1 ? 'message' : 'messages'} pending to be sent',
              style: theme.textTheme.bodySmall?.copyWith(
                color: Colors.white,
              ),
            ),
          ),
          Consumer(
            builder: (context, ref, child) {
              return TextButton(
                onPressed: () {
                  ref.read(offlineMessageServiceProvider).manualSync();
                },
                style: TextButton.styleFrom(
                  padding: EdgeInsets.symmetric(horizontal: 8.w),
                  minimumSize: Size.zero,
                  tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                  foregroundColor: Colors.white,
                ),
                child: const Text('SYNC NOW'),
              );
            },
          ),
        ],
      ),
    );
  }
}
