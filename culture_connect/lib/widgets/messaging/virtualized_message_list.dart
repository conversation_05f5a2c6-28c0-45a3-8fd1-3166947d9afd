// Flutter imports
import 'package:flutter/material.dart';

// Package imports
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:scrollable_positioned_list/scrollable_positioned_list.dart';

// Project imports
import 'package:culture_connect/models/message_model.dart';
import 'package:culture_connect/models/user_model.dart';
import 'package:culture_connect/providers/auth_provider.dart';
import 'package:culture_connect/providers/user_provider.dart';
import 'package:culture_connect/widgets/messaging/group_translated_message_bubble.dart';

/// A virtualized list view for displaying messages in a chat
class VirtualizedMessageList extends ConsumerStatefulWidget {
  /// The ID of the group chat
  final String groupId;

  /// The list of messages to display
  final List<MessageModel> messages;

  /// The scroll controller
  final ItemScrollController scrollController;

  /// The scroll position listener
  final ItemPositionsListener positionsListener;

  /// Callback when a message is long-pressed
  final Function(MessageModel)? onMessageLongPress;

  /// Creates a new virtualized message list
  const VirtualizedMessageList({
    super.key,
    required this.groupId,
    required this.messages,
    required this.scrollController,
    required this.positionsListener,
    this.onMessageLongPress,
  });

  @override
  ConsumerState<VirtualizedMessageList> createState() =>
      _VirtualizedMessageListState();
}

class _VirtualizedMessageListState
    extends ConsumerState<VirtualizedMessageList> {
  /// The map of user IDs to user models
  final Map<String, UserModel?> _userCache = {};

  /// The list of visible message indices
  List<int> _visibleItems = [];

  @override
  void initState() {
    super.initState();

    // Listen for position changes
    widget.positionsListener.itemPositions.addListener(_updateVisibleItems);
  }

  @override
  void dispose() {
    widget.positionsListener.itemPositions.removeListener(_updateVisibleItems);
    super.dispose();
  }

  /// Updates the list of visible items
  void _updateVisibleItems() {
    final positions = widget.positionsListener.itemPositions.value;
    if (positions.isEmpty) return;

    // Get the range of visible items
    final firstVisible = positions.first.index;
    final lastVisible = positions.last.index;

    // Include a buffer of items before and after the visible range
    const bufferSize = 10;
    final firstIndex =
        (firstVisible - bufferSize).clamp(0, widget.messages.length - 1);
    final lastIndex =
        (lastVisible + bufferSize).clamp(0, widget.messages.length - 1);

    // Update the list of visible items
    setState(() {
      _visibleItems =
          List.generate(lastIndex - firstIndex + 1, (i) => firstIndex + i);
    });
  }

  /// Gets the user info for a sender
  Future<UserModel?> _getUserInfo(String userId) async {
    // Check if we already have the user in the cache
    if (_userCache.containsKey(userId)) {
      return _userCache[userId];
    }

    // Get the user from the provider
    final user = await ref.read(userProvider(userId).future);

    // Cache the user
    if (user != null) {
      _userCache[userId] = user;
    }

    return user;
  }

  /// Formats a date for display
  Widget _buildDateSeparator(DateTime date) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 16),
      child: Row(
        children: [
          Expanded(
            child: Divider(
              color: Colors.grey[300],
              thickness: 1,
            ),
          ),
          const SizedBox(width: 8),
          Text(
            _formatDate(date),
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Divider(
              color: Colors.grey[300],
              thickness: 1,
            ),
          ),
        ],
      ),
    );
  }

  /// Formats a date
  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final yesterday = today.subtract(const Duration(days: 1));
    final messageDate = DateTime(date.year, date.month, date.day);

    if (messageDate == today) {
      return 'Today';
    } else if (messageDate == yesterday) {
      return 'Yesterday';
    } else {
      return '${date.day}/${date.month}/${date.year}';
    }
  }

  /// Checks if two dates are the same day
  bool _isSameDay(DateTime date1, DateTime date2) {
    return date1.year == date2.year &&
        date1.month == date2.month &&
        date1.day == date2.day;
  }

  @override
  Widget build(BuildContext context) {
    // Get the current user
    final currentUserAsync = ref.watch(currentUserModelProvider);

    return currentUserAsync.when(
      data: (currentUser) {
        if (widget.messages.isEmpty) {
          return Center(
            child: Text(
              'No messages yet',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey[600],
              ),
            ),
          );
        }

        return ScrollablePositionedList.builder(
          itemScrollController: widget.scrollController,
          itemPositionsListener: widget.positionsListener,
          itemCount: widget.messages.length,
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
          itemBuilder: (context, index) {
            final message = widget.messages[index];
            final isMe = message.senderId == currentUser?.id;
            final showDate = index == 0 ||
                !_isSameDay(
                    widget.messages[index - 1].timestamp, message.timestamp);

            // Skip rendering if the item is not visible or in the buffer
            if (!_visibleItems.contains(index)) {
              // Return an empty container with the same size as the message
              // to maintain scroll position
              return Container(
                height: 50, // Approximate height of a message
                margin: const EdgeInsets.symmetric(vertical: 4),
              );
            }

            return FutureBuilder<UserModel?>(
              future: _getUserInfo(message.senderId),
              builder: (context, snapshot) {
                final sender = snapshot.data;

                return Column(
                  children: [
                    if (showDate) _buildDateSeparator(message.timestamp),
                    GroupTranslatedMessageBubble(
                      message: message,
                      groupId: widget.groupId,
                      senderName: sender != null ? sender.fullName : 'Unknown',
                      senderAvatarUrl: sender?.profilePicture,
                      isMe: isMe,
                      onLongPress: widget.onMessageLongPress != null
                          ? () => widget.onMessageLongPress!(message)
                          : null,
                    ),
                  ],
                );
              },
            );
          },
        );
      },
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (error, stack) => Center(
        child: Text('Error loading user: $error'),
      ),
    );
  }
}
