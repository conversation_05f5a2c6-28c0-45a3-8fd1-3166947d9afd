import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:culture_connect/models/translation/language_model.dart';
import 'package:culture_connect/providers/message_translation_provider.dart';
import 'package:culture_connect/providers/voice_translation_enhanced_provider.dart';
import 'package:culture_connect/theme/app_theme.dart';

/// A widget for message translation settings
class MessageTranslationSettings extends ConsumerWidget {
  /// Creates a new message translation settings widget
  const MessageTranslationSettings({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final autoTranslate = ref.watch(autoTranslateMessagesProvider);
    final targetLanguage = ref.watch(messageTranslationTargetLanguageProvider);
    final useOfflineMode = ref.watch(useOfflineModeProvider);
    final useDialectRecognition = ref.watch(useDialectRecognitionProvider);
    final useCustomVocabulary = ref.watch(useCustomVocabularyProvider);

    return Container(
      padding: EdgeInsets.all(16.r),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Text(
            'Translation Settings',
            style: TextStyle(
              fontSize: 18.sp,
              fontWeight: FontWeight.bold,
              color: AppTheme.textPrimaryColor,
            ),
          ),

          SizedBox(height: 16.h),

          // Auto-translate toggle
          SwitchListTile(
            title: Text(
              'Auto-translate incoming messages',
              style: TextStyle(
                fontSize: 16.sp,
                color: AppTheme.textPrimaryColor,
              ),
            ),
            subtitle: Text(
              'Automatically translate messages in other languages',
              style: TextStyle(
                fontSize: 12.sp,
                color: AppTheme.textSecondaryColor,
              ),
            ),
            value: autoTranslate,
            onChanged: (value) {
              ref.read(autoTranslateMessagesProvider.notifier).state = value;
            },
            activeColor: AppTheme.primaryColor,
          ),

          SizedBox(height: 16.h),

          // Target language selector
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 16.w),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Translate to:',
                  style: TextStyle(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.bold,
                    color: AppTheme.textPrimaryColor,
                  ),
                ),
                SizedBox(height: 8.h),
                _buildLanguageSelector(ref, targetLanguage),
              ],
            ),
          ),

          SizedBox(height: 16.h),

          // Cultural Context Settings
          ListTile(
            title: Text(
              'Cultural Context Settings',
              style: TextStyle(
                fontSize: 16.sp,
                color: AppTheme.textPrimaryColor,
              ),
            ),
            subtitle: Text(
              'Manage cultural context awareness and settings',
              style: TextStyle(
                fontSize: 12.sp,
                color: AppTheme.textSecondaryColor,
              ),
            ),
            trailing: const Icon(Icons.arrow_forward_ios, size: 16),
            onTap: () {
              Navigator.pushNamed(context, '/cultural_context_settings');
            },
          ),

          SizedBox(height: 16.h),

          // Slang & Idiom Settings
          ListTile(
            title: Text(
              'Slang & Idiom Settings',
              style: TextStyle(
                fontSize: 16.sp,
                color: AppTheme.textPrimaryColor,
              ),
            ),
            subtitle: Text(
              'Manage slang and idiom detection settings',
              style: TextStyle(
                fontSize: 12.sp,
                color: AppTheme.textSecondaryColor,
              ),
            ),
            trailing: const Icon(Icons.arrow_forward_ios, size: 16),
            onTap: () {
              Navigator.pushNamed(context, '/slang_idiom_settings');
            },
          ),

          SizedBox(height: 16.h),

          // Pronunciation Settings
          ListTile(
            title: Text(
              'Pronunciation Settings',
              style: TextStyle(
                fontSize: 16.sp,
                color: AppTheme.textPrimaryColor,
              ),
            ),
            subtitle: Text(
              'Manage pronunciation guidance settings',
              style: TextStyle(
                fontSize: 12.sp,
                color: AppTheme.textSecondaryColor,
              ),
            ),
            trailing: const Icon(Icons.arrow_forward_ios, size: 16),
            onTap: () {
              Navigator.pushNamed(context, '/pronunciation_settings');
            },
          ),

          SizedBox(height: 16.h),

          // Advanced settings
          ExpansionTile(
            title: Text(
              'Advanced Settings',
              style: TextStyle(
                fontSize: 16.sp,
                color: AppTheme.textPrimaryColor,
              ),
            ),
            children: [
              // Offline mode
              SwitchListTile(
                title: Text(
                  'Offline Mode',
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: AppTheme.textPrimaryColor,
                  ),
                ),
                subtitle: Text(
                  'Use downloaded language packs when available',
                  style: TextStyle(
                    fontSize: 12.sp,
                    color: AppTheme.textSecondaryColor,
                  ),
                ),
                value: useOfflineMode,
                onChanged: (value) {
                  // Update offline mode setting
                  ref.read(useOfflineModeProvider.notifier).state = value;
                },
                activeColor: AppTheme.primaryColor,
                dense: true,
              ),

              // Dialect recognition
              SwitchListTile(
                title: Text(
                  'Dialect Recognition',
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: AppTheme.textPrimaryColor,
                  ),
                ),
                subtitle: Text(
                  'Recognize and adapt to regional dialects',
                  style: TextStyle(
                    fontSize: 12.sp,
                    color: AppTheme.textSecondaryColor,
                  ),
                ),
                value: useDialectRecognition,
                onChanged: (value) {
                  // Update dialect recognition setting
                  ref.read(useDialectRecognitionProvider.notifier).state =
                      value;
                },
                activeColor: AppTheme.primaryColor,
                dense: true,
              ),

              // Custom vocabulary
              SwitchListTile(
                title: Text(
                  'Custom Vocabulary',
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: AppTheme.textPrimaryColor,
                  ),
                ),
                subtitle: Text(
                  'Use your custom vocabulary for translations',
                  style: TextStyle(
                    fontSize: 12.sp,
                    color: AppTheme.textSecondaryColor,
                  ),
                ),
                value: useCustomVocabulary,
                onChanged: (value) {
                  // Update custom vocabulary setting
                  ref.read(useCustomVocabularyProvider.notifier).state = value;
                },
                activeColor: AppTheme.primaryColor,
                dense: true,
              ),
            ],
          ),

          SizedBox(height: 16.h),

          // Buttons
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              TextButton(
                onPressed: () {
                  ref
                      .read(messageTranslationNotifierProvider.notifier)
                      .clearCache();
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('Translation cache cleared'),
                      duration: Duration(seconds: 2),
                    ),
                  );
                },
                child: Text(
                  'Clear Cache',
                  style: TextStyle(
                    color: Colors.red,
                    fontSize: 14.sp,
                  ),
                ),
              ),
              SizedBox(width: 8.w),
              ElevatedButton(
                onPressed: () => Navigator.pop(context),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppTheme.primaryColor,
                  foregroundColor: Colors.white,
                ),
                child: Text(
                  'Done',
                  style: TextStyle(
                    fontSize: 14.sp,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// Build the language selector
  Widget _buildLanguageSelector(WidgetRef ref, LanguageModel currentLanguage) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.h),
      decoration: BoxDecoration(
        color: Colors.grey[200],
        borderRadius: BorderRadius.circular(8.r),
      ),
      child: DropdownButton<LanguageModel>(
        value: currentLanguage,
        isExpanded: true,
        underline: const SizedBox(),
        onChanged: (LanguageModel? newValue) {
          if (newValue != null) {
            ref.read(messageTranslationTargetLanguageProvider.notifier).state =
                newValue;
          }
        },
        items: supportedLanguages
            .map<DropdownMenuItem<LanguageModel>>((LanguageModel language) {
          return DropdownMenuItem<LanguageModel>(
            value: language,
            child: Text(
              language.name,
              style: TextStyle(
                fontSize: 14.sp,
                color: AppTheme.textPrimaryColor,
              ),
            ),
          );
        }).toList(),
      ),
    );
  }
}
