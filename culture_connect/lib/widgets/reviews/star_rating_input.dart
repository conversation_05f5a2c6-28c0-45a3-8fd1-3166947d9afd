import 'package:flutter/material.dart';

/// A widget for inputting a star rating with animation
class StarRatingInput extends StatefulWidget {
  /// The initial rating value (1-5)
  final double initialRating;

  /// The size of each star
  final double starSize;

  /// The spacing between stars
  final double spacing;

  /// Whether the rating is read-only
  final bool readOnly;

  /// The color of the filled stars
  final Color? filledColor;

  /// The color of the unfilled stars
  final Color? unfilledColor;

  /// Callback when the rating changes
  final ValueChanged<double>? onChanged;

  /// Creates a new star rating input widget
  const StarRatingInput({
    super.key,
    this.initialRating = 0,
    this.starSize = 40,
    this.spacing = 4,
    this.readOnly = false,
    this.filledColor,
    this.unfilledColor,
    this.onChanged,
  });

  @override
  State<StarRatingInput> createState() => _StarRatingInputState();
}

class _StarRatingInputState extends State<StarRatingInput>
    with SingleTickerProviderStateMixin {
  late double _rating;
  late AnimationController _controller;
  final List<Animation<double>> _starAnimations = [];

  @override
  void initState() {
    super.initState();
    _rating = widget.initialRating;

    // Initialize animation controller
    _controller = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );

    // Create animations for each star
    for (int i = 0; i < 5; i++) {
      final delay = i * 0.1;
      final begin = delay;
      final end = delay + 0.5;

      _starAnimations.add(
        CurvedAnimation(
          parent: _controller,
          curve: Interval(begin, end, curve: Curves.elasticOut),
        ),
      );
    }

    // Start animation if there's an initial rating
    if (_rating > 0) {
      _controller.forward();
    }
  }

  @override
  void didUpdateWidget(StarRatingInput oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.initialRating != oldWidget.initialRating) {
      _rating = widget.initialRating;
      if (_rating > 0) {
        _controller.forward(from: 0);
      }
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void _updateRating(double newRating) {
    if (widget.readOnly) return;

    setState(() {
      _rating = newRating;
    });

    widget.onChanged?.call(newRating);

    // Animate stars
    _controller.forward(from: 0);
  }

  @override
  Widget build(BuildContext context) {
    final filledColor = widget.filledColor ?? Colors.amber;
    final unfilledColor = widget.unfilledColor ?? Colors.grey.shade300;

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: List.generate(5, (index) {
        final starValue = index + 1.0;
        final isFilled = starValue <= _rating;

        return Padding(
          padding: EdgeInsets.symmetric(horizontal: widget.spacing / 2),
          child: AnimatedBuilder(
            animation: _controller,
            builder: (context, child) {
              final scale = isFilled
                  ? Tween<double>(begin: 1.0, end: 1.2)
                      .animate(_starAnimations[index])
                      .value
                  : 1.0;

              return Transform.scale(
                scale: scale,
                child: child,
              );
            },
            child: GestureDetector(
              onTap: widget.readOnly ? null : () => _updateRating(starValue),
              child: Icon(
                isFilled ? Icons.star_rounded : Icons.star_border_rounded,
                color: isFilled ? filledColor : unfilledColor,
                size: widget.starSize,
              ),
            ),
          ),
        );
      }),
    );
  }
}

/// A widget for displaying a star rating (read-only)
class StarRatingDisplay extends StatelessWidget {
  /// The rating value (1-5)
  final double rating;

  /// The size of each star
  final double starSize;

  /// The spacing between stars
  final double spacing;

  /// The color of the filled stars
  final Color? filledColor;

  /// The color of the unfilled stars
  final Color? unfilledColor;

  /// Whether to show the rating value
  final bool showRating;

  /// The text style for the rating value
  final TextStyle? ratingTextStyle;

  /// Creates a new star rating display widget
  const StarRatingDisplay({
    super.key,
    required this.rating,
    this.starSize = 16,
    this.spacing = 2,
    this.filledColor,
    this.unfilledColor,
    this.showRating = false,
    this.ratingTextStyle,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final filledColor = this.filledColor ?? Colors.amber;
    final unfilledColor = this.unfilledColor ?? Colors.grey.shade300;

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Row(
          mainAxisSize: MainAxisSize.min,
          children: List.generate(5, (index) {
            final starValue = index + 1.0;
            final isFilled = starValue <= rating;
            final isHalfFilled = !isFilled && starValue - 0.5 <= rating;

            return Padding(
              padding: EdgeInsets.symmetric(horizontal: spacing / 2),
              child: Icon(
                isFilled
                    ? Icons.star_rounded
                    : isHalfFilled
                        ? Icons.star_half_rounded
                        : Icons.star_border_rounded,
                color: isFilled || isHalfFilled ? filledColor : unfilledColor,
                size: starSize,
              ),
            );
          }),
        ),
        if (showRating) ...[
          const SizedBox(width: 4),
          Text(
            rating.toStringAsFixed(1),
            style: ratingTextStyle ??
                TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: theme.textTheme.bodyMedium?.color,
                ),
          ),
        ],
      ],
    );
  }
}
