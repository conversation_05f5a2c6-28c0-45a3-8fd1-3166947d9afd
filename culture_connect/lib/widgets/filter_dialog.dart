import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:culture_connect/models/filter_options.dart';
import 'package:culture_connect/providers/experience_provider.dart';

/// A dialog for filtering experiences
class FilterDialog extends ConsumerStatefulWidget {
  const FilterDialog({super.key});

  @override
  ConsumerState<FilterDialog> createState() => _FilterDialogState();
}

class _FilterDialogState extends ConsumerState<FilterDialog> {
  late FilterOptions _filterOptions;
  final List<String> _allCategories = [
    'All',
    'Cultural Tours',
    'Cooking Classes',
    'Art & Craft',
    'Music & Dance',
    'Language Exchange',
    'Local Events',
    'Nature & Wildlife',
    'Food & Drink',
  ];

  final List<String> _allLanguages = [
    'English',
    'French',
    'Yoruba',
    'Igbo',
    'Hausa',
    'Swahili',
    'Zulu',
    'Xhosa',
    'Afrikaans',
    'Arabic',
  ];

  final List<String> _allLocations = [
    'Lagos, Nigeria',
    'Abuja, Nigeria',
    'Ibadan, Nigeria',
    'Nairobi, Kenya',
    'Mombasa, Kenya',
    'Cape Town, South Africa',
    'Johannesburg, South Africa',
    'Durban, South Africa',
  ];

  // Selected values
  String? _selectedCategory;
  RangeValues _priceRange = const RangeValues(0, 200);
  double _minRating = 0.0;
  RangeValues _durationRange = const RangeValues(0, 12);
  List<String> _selectedLanguages = [];
  List<String> _selectedLocations = [];
  int _participants = 1;
  bool _onlyAvailable = false;
  bool _onlyAccessible = false;
  SortOption _sortOption = SortOption.recommended;

  @override
  void initState() {
    super.initState();
    // Initialize with current filter options
    _filterOptions = ref.read(filterOptionsProvider);
    _initializeFromFilterOptions();
  }

  void _initializeFromFilterOptions() {
    _selectedCategory = _filterOptions.category;
    _priceRange = _filterOptions.priceRange ?? const RangeValues(0, 200);
    _minRating = _filterOptions.minRating ?? 0.0;
    _durationRange = _filterOptions.durationRange ?? const RangeValues(0, 12);
    _selectedLanguages = _filterOptions.languages ?? [];
    _selectedLocations = _filterOptions.locations ?? [];
    _participants = _filterOptions.participants ?? 1;
    _onlyAvailable = _filterOptions.onlyAvailable ?? false;
    _onlyAccessible = _filterOptions.onlyAccessible ?? false;
    _sortOption = _filterOptions.sortOption;
  }

  void _applyFilters() {
    final newFilterOptions = FilterOptions(
      category: _selectedCategory,
      priceRange: _priceRange,
      minRating: _minRating,
      durationRange: _durationRange,
      languages: _selectedLanguages.isEmpty ? null : _selectedLanguages,
      locations: _selectedLocations.isEmpty ? null : _selectedLocations,
      participants: _participants,
      onlyAvailable: _onlyAvailable,
      onlyAccessible: _onlyAccessible,
      sortOption: _sortOption,
    );

    ref.read(filterOptionsProvider.notifier).state = newFilterOptions;
    Navigator.of(context).pop();
  }

  void _resetFilters() {
    setState(() {
      _selectedCategory = null;
      _priceRange = const RangeValues(0, 200);
      _minRating = 0.0;
      _durationRange = const RangeValues(0, 12);
      _selectedLanguages = [];
      _selectedLocations = [];
      _participants = 1;
      _onlyAvailable = false;
      _onlyAccessible = false;
      _sortOption = SortOption.recommended;
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Container(
        width: double.maxFinite,
        constraints: BoxConstraints(
          maxHeight: MediaQuery.of(context).size.height * 0.8,
        ),
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Filter Experiences',
                  style: theme.textTheme.titleLarge,
                ),
                IconButton(
                  icon: const Icon(Icons.close),
                  onPressed: () => Navigator.of(context).pop(),
                ),
              ],
            ),
            const Divider(),
            Expanded(
              child: ListView(
                children: [
                  // Category filter
                  Text(
                    'Category',
                    style: theme.textTheme.titleMedium,
                  ),
                  const SizedBox(height: 8),
                  Wrap(
                    spacing: 8,
                    runSpacing: 8,
                    children: _allCategories.map((category) {
                      final isSelected = _selectedCategory == category;
                      return ChoiceChip(
                        label: Text(category),
                        selected: isSelected,
                        onSelected: (selected) {
                          setState(() {
                            _selectedCategory = selected ? category : null;
                          });
                        },
                      );
                    }).toList(),
                  ),
                  const SizedBox(height: 16),

                  // Price range filter
                  Text(
                    'Price Range',
                    style: theme.textTheme.titleMedium,
                  ),
                  const SizedBox(height: 8),
                  RangeSlider(
                    values: _priceRange,
                    min: 0,
                    max: 200,
                    divisions: 20,
                    labels: RangeLabels(
                      '\$${_priceRange.start.round()}',
                      '\$${_priceRange.end.round()}',
                    ),
                    onChanged: (values) {
                      setState(() {
                        _priceRange = values;
                      });
                    },
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text('\$${_priceRange.start.round()}'),
                      Text('\$${_priceRange.end.round()}'),
                    ],
                  ),
                  const SizedBox(height: 16),

                  // Rating filter
                  Text(
                    'Minimum Rating',
                    style: theme.textTheme.titleMedium,
                  ),
                  const SizedBox(height: 8),
                  Slider(
                    value: _minRating,
                    min: 0,
                    max: 5,
                    divisions: 10,
                    label: _minRating.toStringAsFixed(1),
                    onChanged: (value) {
                      setState(() {
                        _minRating = value;
                      });
                    },
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const Text('Any'),
                      Row(
                        children: [
                          Text(_minRating.toStringAsFixed(1)),
                          const Icon(Icons.star, size: 16),
                        ],
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),

                  // Duration filter
                  Text(
                    'Duration (hours)',
                    style: theme.textTheme.titleMedium,
                  ),
                  const SizedBox(height: 8),
                  RangeSlider(
                    values: _durationRange,
                    min: 0,
                    max: 12,
                    divisions: 12,
                    labels: RangeLabels(
                      '${_durationRange.start.round()}h',
                      '${_durationRange.end.round()}h',
                    ),
                    onChanged: (values) {
                      setState(() {
                        _durationRange = values;
                      });
                    },
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text('${_durationRange.start.round()}h'),
                      Text('${_durationRange.end.round()}h'),
                    ],
                  ),
                  const SizedBox(height: 16),

                  // Languages filter
                  Text(
                    'Languages',
                    style: theme.textTheme.titleMedium,
                  ),
                  const SizedBox(height: 8),
                  Wrap(
                    spacing: 8,
                    runSpacing: 8,
                    children: _allLanguages.map((language) {
                      final isSelected = _selectedLanguages.contains(language);
                      return FilterChip(
                        label: Text(language),
                        selected: isSelected,
                        onSelected: (selected) {
                          setState(() {
                            if (selected) {
                              _selectedLanguages.add(language);
                            } else {
                              _selectedLanguages.remove(language);
                            }
                          });
                        },
                      );
                    }).toList(),
                  ),
                  const SizedBox(height: 16),

                  // Locations filter
                  Text(
                    'Locations',
                    style: theme.textTheme.titleMedium,
                  ),
                  const SizedBox(height: 8),
                  Wrap(
                    spacing: 8,
                    runSpacing: 8,
                    children: _allLocations.map((location) {
                      final isSelected = _selectedLocations.contains(location);
                      return FilterChip(
                        label: Text(location),
                        selected: isSelected,
                        onSelected: (selected) {
                          setState(() {
                            if (selected) {
                              _selectedLocations.add(location);
                            } else {
                              _selectedLocations.remove(location);
                            }
                          });
                        },
                      );
                    }).toList(),
                  ),
                  const SizedBox(height: 16),

                  // Participants filter
                  Text(
                    'Number of Participants',
                    style: theme.textTheme.titleMedium,
                  ),
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      IconButton(
                        icon: const Icon(Icons.remove),
                        onPressed: _participants > 1
                            ? () {
                                setState(() {
                                  _participants--;
                                });
                              }
                            : null,
                      ),
                      Text(
                        '$_participants',
                        style: theme.textTheme.titleMedium,
                      ),
                      IconButton(
                        icon: const Icon(Icons.add),
                        onPressed: _participants < 20
                            ? () {
                                setState(() {
                                  _participants++;
                                });
                              }
                            : null,
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),

                  // Availability and accessibility filters
                  SwitchListTile(
                    title: const Text('Show only available experiences'),
                    value: _onlyAvailable,
                    onChanged: (value) {
                      setState(() {
                        _onlyAvailable = value;
                      });
                    },
                  ),
                  SwitchListTile(
                    title: const Text('Show only accessible experiences'),
                    value: _onlyAccessible,
                    onChanged: (value) {
                      setState(() {
                        _onlyAccessible = value;
                      });
                    },
                  ),
                  const SizedBox(height: 16),

                  // Sort options
                  Text(
                    'Sort By',
                    style: theme.textTheme.titleMedium,
                  ),
                  const SizedBox(height: 8),
                  DropdownButtonFormField<SortOption>(
                    value: _sortOption,
                    decoration: const InputDecoration(
                      border: OutlineInputBorder(),
                      contentPadding:
                          EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    ),
                    items: SortOption.values.map((option) {
                      return DropdownMenuItem<SortOption>(
                        value: option,
                        child: Text(option.label),
                      );
                    }).toList(),
                    onChanged: (value) {
                      if (value != null) {
                        setState(() {
                          _sortOption = value;
                        });
                      }
                    },
                  ),
                ],
              ),
            ),
            const Divider(),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                TextButton(
                  onPressed: _resetFilters,
                  child: const Text('Reset'),
                ),
                ElevatedButton(
                  onPressed: _applyFilters,
                  child: const Text('Apply Filters'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
