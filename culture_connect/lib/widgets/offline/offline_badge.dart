import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:culture_connect/models/offline/sync_status.dart';
import 'package:culture_connect/models/offline/offline_content.dart';
import 'package:culture_connect/providers/offline_mode_provider.dart';
import 'package:culture_connect/theme/app_colors.dart';

/// A badge that indicates whether content is available offline
class OfflineBadge extends ConsumerWidget {
  /// The ID of the content
  final String contentId;

  /// The size of the badge
  final double size;

  /// Whether to show the label
  final bool showLabel;

  /// The position of the badge
  final OfflineBadgePosition position;

  /// Creates a new offline badge
  const OfflineBadge({
    super.key,
    required this.contentId,
    this.size = 16.0,
    this.showLabel = false,
    this.position = OfflineBadgePosition.topRight,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Get the sync status of the content
    final syncStatus = ref.watch(contentSyncStatusProvider(contentId));

    // Only show the badge if the content is available offline
    if (syncStatus != SyncStatus.synced) {
      return const SizedBox.shrink();
    }

    return _buildBadge(syncStatus);
  }

  /// Build the badge
  Widget _buildBadge(SyncStatus status) {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: showLabel ? 8 : 4,
        vertical: 4,
      ),
      decoration: BoxDecoration(
        color: Colors.green,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black
                .withAlpha(26), // 0.1 opacity is approximately 26 alpha
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.offline_pin,
            size: size,
            color: Colors.white,
          ),
          if (showLabel) ...[
            const SizedBox(width: 4),
            const Text(
              'Available Offline',
              style: TextStyle(
                fontSize: 10,
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ],
      ),
    );
  }
}

/// A widget that positions an offline badge over a child widget
class OfflineBadgeOverlay extends ConsumerWidget {
  /// The child widget
  final Widget child;

  /// The ID of the content
  final String contentId;

  /// The size of the badge
  final double badgeSize;

  /// Whether to show the label
  final bool showLabel;

  /// The position of the badge
  final OfflineBadgePosition position;

  /// The padding around the badge
  final EdgeInsets padding;

  /// Creates a new offline badge overlay
  const OfflineBadgeOverlay({
    super.key,
    required this.child,
    required this.contentId,
    this.badgeSize = 16.0,
    this.showLabel = false,
    this.position = OfflineBadgePosition.topRight,
    this.padding = const EdgeInsets.all(8.0),
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Get the sync status of the content
    final syncStatus = ref.watch(contentSyncStatusProvider(contentId));

    // Only show the badge if the content is available offline
    if (syncStatus != SyncStatus.synced) {
      return child;
    }

    return Stack(
      clipBehavior: Clip.none,
      children: [
        // Child widget
        child,

        // Badge
        Positioned(
          left: position == OfflineBadgePosition.topLeft ||
                  position == OfflineBadgePosition.bottomLeft
              ? padding.left
              : null,
          right: position == OfflineBadgePosition.topRight ||
                  position == OfflineBadgePosition.bottomRight
              ? padding.right
              : null,
          top: position == OfflineBadgePosition.topLeft ||
                  position == OfflineBadgePosition.topRight
              ? padding.top
              : null,
          bottom: position == OfflineBadgePosition.bottomLeft ||
                  position == OfflineBadgePosition.bottomRight
              ? padding.bottom
              : null,
          child: OfflineBadge(
            contentId: contentId,
            size: badgeSize,
            showLabel: showLabel,
            position: position,
          ),
        ),
      ],
    );
  }
}

/// A widget that displays a download button for offline content
class OfflineDownloadButton extends ConsumerStatefulWidget {
  /// The content to download
  final String contentId;

  /// The title of the content
  final String contentTitle;

  /// The type of content
  final String contentType;

  /// The size of the button
  final double size;

  /// Whether to show the label
  final bool showLabel;

  /// The callback when the download starts
  final VoidCallback? onDownloadStart;

  /// The callback when the download completes
  final VoidCallback? onDownloadComplete;

  /// The callback when the download fails
  final Function(String)? onDownloadFail;

  /// Creates a new offline download button
  const OfflineDownloadButton({
    super.key,
    required this.contentId,
    required this.contentTitle,
    required this.contentType,
    this.size = 24.0,
    this.showLabel = true,
    this.onDownloadStart,
    this.onDownloadComplete,
    this.onDownloadFail,
  });

  @override
  ConsumerState<OfflineDownloadButton> createState() =>
      _OfflineDownloadButtonState();
}

class _OfflineDownloadButtonState extends ConsumerState<OfflineDownloadButton> {
  bool _isDownloading = false;
  double? _progress;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();

    // Listen for sync status updates
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.listen<AsyncValue<SyncStatusUpdate>>(
        syncStatusUpdatesProvider,
        (previous, next) {
          next.whenData((update) {
            if (update.contentId == widget.contentId) {
              setState(() {
                _isDownloading = update.status.isLoading;
                _progress = update.progress;
                _errorMessage =
                    update.status == SyncStatus.failed ? update.message : null;
              });

              // Call the appropriate callback
              if (update.status == SyncStatus.syncing &&
                  widget.onDownloadStart != null) {
                widget.onDownloadStart!();
              } else if (update.status == SyncStatus.synced &&
                  widget.onDownloadComplete != null) {
                widget.onDownloadComplete!();
              } else if (update.status == SyncStatus.failed &&
                  widget.onDownloadFail != null) {
                widget.onDownloadFail!(update.message);
              }
            }
          });
        },
      );
    });
  }

  @override
  Widget build(BuildContext context) {
    // Get the sync status of the content
    final syncStatus = ref.watch(contentSyncStatusProvider(widget.contentId));

    // Determine the button appearance based on the sync status
    IconData icon;
    String label;
    Color color;
    VoidCallback? onPressed;

    switch (syncStatus) {
      case SyncStatus.notSynced:
        icon = Icons.download;
        label = 'Download';
        color = AppColors.primary;
        onPressed = _downloadContent;
        break;
      case SyncStatus.pending:
        icon = Icons.pending;
        label = 'Pending';
        color = Colors.orange;
        onPressed = null;
        break;
      case SyncStatus.syncing:
        icon = Icons.sync;
        label = 'Downloading...';
        color = Colors.blue;
        onPressed = null;
        break;
      case SyncStatus.synced:
        icon = Icons.offline_pin;
        label = 'Downloaded';
        color = Colors.green;
        onPressed = _removeContent;
        break;
      case SyncStatus.failed:
        icon = Icons.error_outline;
        label = 'Retry';
        color = Colors.red;
        onPressed = _downloadContent;
        break;
    }

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Button
        IconButton(
          icon: Icon(icon),
          onPressed: onPressed,
          color: color,
          iconSize: widget.size,
          tooltip: label,
        ),

        // Label
        if (widget.showLabel)
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              color: color,
              fontWeight: FontWeight.w500,
            ),
          ),

        // Error message
        if (_errorMessage != null)
          Padding(
            padding: const EdgeInsets.only(top: 4),
            child: Text(
              _errorMessage!,
              style: const TextStyle(
                fontSize: 10,
                color: Colors.red,
              ),
              textAlign: TextAlign.center,
            ),
          ),

        // Progress indicator
        if (_isDownloading && _progress != null)
          Padding(
            padding: const EdgeInsets.only(top: 4),
            child: SizedBox(
              width: 48,
              child: LinearProgressIndicator(
                value: _progress,
                backgroundColor: Colors.grey[300],
                valueColor: const AlwaysStoppedAnimation<Color>(Colors.blue),
              ),
            ),
          ),
      ],
    );
  }

  /// Download the content for offline use
  Future<void> _downloadContent() async {
    // Create the offline content
    final content = OfflineContent(
      id: widget.contentId,
      title: widget.contentTitle,
      contentType: widget.contentType,
    );

    // Add the content for offline use
    await ref
        .read(offlineContentNotifierProvider.notifier)
        .addContentForOfflineUse(content);
  }

  /// Remove the content from offline storage
  Future<void> _removeContent() async {
    // Show a confirmation dialog
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Remove from Offline Storage'),
        content: Text(
            'Are you sure you want to remove "${widget.contentTitle}" from offline storage?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('Remove'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      // Remove the content from offline storage
      await ref
          .read(offlineContentNotifierProvider.notifier)
          .removeOfflineContent(widget.contentId);
    }
  }
}

/// The position of an offline badge
enum OfflineBadgePosition {
  /// Top left corner
  topLeft,

  /// Top right corner
  topRight,

  /// Bottom left corner
  bottomLeft,

  /// Bottom right corner
  bottomRight,
}
