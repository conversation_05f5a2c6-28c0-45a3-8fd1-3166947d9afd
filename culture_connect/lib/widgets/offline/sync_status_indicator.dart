import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'package:culture_connect/models/offline/sync_status.dart';

/// A widget that displays the sync status of content
class SyncStatusIndicator extends ConsumerWidget {
  /// The sync status to display
  final SyncStatus status;

  /// The size of the indicator
  final double size;

  /// Whether to show the label
  final bool showLabel;

  /// Whether to show the background
  final bool showBackground;

  /// Whether to animate the indicator
  final bool animate;

  /// The color of the indicator (overrides the status color)
  final Color? color;

  /// Creates a new sync status indicator
  const SyncStatusIndicator({
    super.key,
    required this.status,
    this.size = 24.0,
    this.showLabel = false,
    this.showBackground = true,
    this.animate = true,
    this.color,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        _buildIndicator(),
        if (showLabel) ...[
          SizedBox(width: 8.w),
          Text(
            status.displayName,
            style: TextStyle(
              fontSize: 12.sp,
              color: color ?? status.color,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ],
    );
  }

  /// Build the indicator icon
  Widget _buildIndicator() {
    // Use the provided color or the status color
    final iconColor = color ?? status.color;

    // Create the base icon
    Widget icon = Icon(
      status.icon,
      size: size,
      color: iconColor,
    );

    // Add animation if needed
    if (animate && status.isLoading) {
      icon = _AnimatedSyncIcon(
        icon: icon,
        color: iconColor,
      );
    }

    // Add background if needed
    if (showBackground) {
      return Container(
        width: size * 1.5,
        height: size * 1.5,
        decoration: BoxDecoration(
          color: status.backgroundColor,
          shape: BoxShape.circle,
          border: Border.all(
            color: status.borderColor,
            width: 1.5,
          ),
        ),
        child: Center(child: icon),
      );
    }

    return icon;
  }
}

/// A widget that displays an animated sync icon
class _AnimatedSyncIcon extends StatefulWidget {
  /// The icon to animate
  final Widget icon;

  /// The color of the icon
  final Color color;

  /// Creates a new animated sync icon
  const _AnimatedSyncIcon({
    required this.icon,
    required this.color,
  });

  @override
  State<_AnimatedSyncIcon> createState() => _AnimatedSyncIconState();
}

class _AnimatedSyncIconState extends State<_AnimatedSyncIcon>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _rotationAnimation;

  @override
  void initState() {
    super.initState();

    // Create the animation controller
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 2),
    );

    // Create the rotation animation
    _rotationAnimation = Tween<double>(
      begin: 0.0,
      end: 2 * 3.14159, // 360 degrees in radians
    ).animate(
      CurvedAnimation(
        parent: _controller,
        curve: Curves.linear,
      ),
    );

    // Start the animation
    _controller.repeat();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _rotationAnimation,
      builder: (context, child) {
        return Transform.rotate(
          angle: _rotationAnimation.value,
          child: widget.icon,
        );
      },
    );
  }
}

/// A widget that displays a pulsating sync status indicator
class PulsatingSyncStatusIndicator extends StatefulWidget {
  /// The sync status to display
  final SyncStatus status;

  /// The size of the indicator
  final double size;

  /// Whether to show the label
  final bool showLabel;

  /// The color of the indicator (overrides the status color)
  final Color? color;

  /// Creates a new pulsating sync status indicator
  const PulsatingSyncStatusIndicator({
    super.key,
    required this.status,
    this.size = 24.0,
    this.showLabel = false,
    this.color,
  });

  @override
  State<PulsatingSyncStatusIndicator> createState() =>
      _PulsatingSyncStatusIndicatorState();
}

class _PulsatingSyncStatusIndicatorState
    extends State<PulsatingSyncStatusIndicator>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();

    // Create the animation controller
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1000),
    );

    // Create the scale animation
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 1.2,
    ).animate(
      CurvedAnimation(
        parent: _controller,
        curve: Curves.easeInOut,
      ),
    );

    // Start the animation
    _controller.repeat(reverse: true);
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _scaleAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: SyncStatusIndicator(
            status: widget.status,
            size: widget.size,
            showLabel: widget.showLabel,
            color: widget.color,
          ),
        );
      },
    );
  }
}

/// A widget that displays a progress indicator with the sync status
class SyncProgressIndicator extends StatelessWidget {
  /// The sync status to display
  final SyncStatus status;

  /// The progress value (0.0 to 1.0)
  final double? progress;

  /// The size of the indicator
  final double size;

  /// Whether to show the label
  final bool showLabel;

  /// The color of the indicator (overrides the status color)
  final Color? color;

  /// Creates a new sync progress indicator
  const SyncProgressIndicator({
    super.key,
    required this.status,
    this.progress,
    this.size = 24.0,
    this.showLabel = false,
    this.color,
  });

  @override
  Widget build(BuildContext context) {
    // Use the provided color or the status color
    final indicatorColor = color ?? status.color;

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        SizedBox(
          width: size * 1.5,
          height: size * 1.5,
          child: Stack(
            alignment: Alignment.center,
            children: [
              // Progress indicator
              if (progress != null)
                CircularProgressIndicator(
                  value: progress,
                  valueColor: AlwaysStoppedAnimation<Color>(indicatorColor),
                  strokeWidth: 2.0,
                ),

              // Status icon
              Icon(
                status.icon,
                size: size,
                color: indicatorColor,
              ),
            ],
          ),
        ),
        if (showLabel) ...[
          SizedBox(width: 8.w),
          Text(
            status.displayName,
            style: TextStyle(
              fontSize: 12.sp,
              color: indicatorColor,
              fontWeight: FontWeight.w500,
            ),
          ),
          if (progress != null) ...[
            SizedBox(width: 4.w),
            Text(
              '(${(progress! * 100).toInt()}%)',
              style: TextStyle(
                fontSize: 12.sp,
                color: indicatorColor,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ],
      ],
    );
  }
}
