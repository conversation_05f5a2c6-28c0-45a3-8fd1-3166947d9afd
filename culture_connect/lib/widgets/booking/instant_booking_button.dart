import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:culture_connect/models/payment/payment_method_model.dart';
import 'package:culture_connect/models/travel/travel.dart';
import 'package:culture_connect/providers/instant_booking_provider.dart';
import 'package:culture_connect/screens/booking/instant_booking_confirmation_screen.dart';
import 'package:culture_connect/theme/app_theme.dart';
import 'package:culture_connect/widgets/payment/payment_method_selection.dart';

/// A widget that displays an instant booking button
class InstantBookingButton extends ConsumerStatefulWidget {
  /// The travel service to book
  final TravelService? travelService;

  /// The experience ID to book
  final String? experienceId;

  /// The experience name to book
  final String? experienceName;

  /// The experience image URL to book
  final String? experienceImageUrl;

  /// The service date
  final DateTime serviceDate;

  /// The number of participants
  final int participantCount;

  /// The total amount
  final double totalAmount;

  /// The currency
  final String currency;

  /// Additional details
  final Map<String, dynamic> additionalDetails;

  /// The button text
  final String buttonText;

  /// The button icon
  final IconData buttonIcon;

  /// The button color
  final Color? buttonColor;

  /// The button text color
  final Color? buttonTextColor;

  /// Whether to show the loading indicator
  final bool showLoading;

  /// Creates a new instant booking button
  const InstantBookingButton({
    super.key,
    this.travelService,
    this.experienceId,
    this.experienceName,
    this.experienceImageUrl,
    required this.serviceDate,
    required this.participantCount,
    required this.totalAmount,
    required this.currency,
    this.additionalDetails = const {},
    this.buttonText = 'Book Now',
    this.buttonIcon = Icons.flash_on,
    this.buttonColor,
    this.buttonTextColor,
    this.showLoading = true,
  }) : assert(
          (travelService != null) ||
              (experienceId != null &&
                  experienceName != null &&
                  experienceImageUrl != null),
          'Either travelService or experienceId, experienceName, and experienceImageUrl must be provided',
        );

  @override
  ConsumerState<InstantBookingButton> createState() =>
      _InstantBookingButtonState();
}

class _InstantBookingButtonState extends ConsumerState<InstantBookingButton> {
  bool _isProcessing = false;

  Future<void> _showInstantBookingDialog() async {
    if (!mounted) return;

    // Always show payment method selection dialog
    await _showPaymentMethodSelectionDialog();
  }

  Future<void> _showPaymentMethodSelectionDialog() async {
    PaymentMethodModel? selectedPaymentMethod;

    await showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Select Payment Method'),
        content: SizedBox(
          width: 300,
          child: PaymentMethodSelection(
            onPaymentMethodSelected: (method) {
              selectedPaymentMethod = method as PaymentMethodModel?;
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              if (selectedPaymentMethod != null) {
                Navigator.pop(context);
                _showConfirmationDialog(selectedPaymentMethod!);
              } else {
                if (!mounted) return;
                final scaffoldMessenger = ScaffoldMessenger.of(context);
                scaffoldMessenger.showSnackBar(
                  const SnackBar(
                    content: Text('Please select a payment method'),
                    backgroundColor: Colors.red,
                  ),
                );
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.primaryColor,
              foregroundColor: Colors.white,
            ),
            child: const Text('Continue'),
          ),
        ],
      ),
    );
  }

  Future<void> _showConfirmationDialog(PaymentMethodModel paymentMethod) async {
    if (!mounted) return;

    await showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Confirm Instant Booking'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'You are about to instantly book:',
              style: TextStyle(
                fontSize: 14,
                color: AppTheme.textSecondaryColor,
              ),
            ),
            const SizedBox(height: 16),
            _buildConfirmationItem(
              'Service',
              widget.travelService?.name ?? widget.experienceName!,
            ),
            _buildConfirmationItem(
              'Date',
              '${widget.serviceDate.year}-${widget.serviceDate.month.toString().padLeft(2, '0')}-${widget.serviceDate.day.toString().padLeft(2, '0')}',
            ),
            _buildConfirmationItem(
              'Participants',
              widget.participantCount.toString(),
            ),
            _buildConfirmationItem(
              'Total Amount',
              '${widget.currency}${widget.totalAmount.toStringAsFixed(2)}',
            ),
            _buildConfirmationItem(
              'Payment Method',
              paymentMethod.name,
            ),
            const SizedBox(height: 16),
            const Text(
              'This will immediately process your payment and confirm your booking.',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color: AppTheme.textPrimaryColor,
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _processInstantBooking(paymentMethod);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.primaryColor,
              foregroundColor: Colors.white,
            ),
            child: const Text('Confirm Booking'),
          ),
        ],
      ),
    );
  }

  Future<void> _processInstantBooking(PaymentMethodModel paymentMethod) async {
    if (!mounted) return;

    setState(() {
      _isProcessing = true;
    });

    try {
      if (widget.travelService != null) {
        // Book travel service
        await ref
            .read(instantBookingNotifierProvider.notifier)
            .createTravelServiceInstantBooking(
              travelService: widget.travelService!,
              serviceDate: widget.serviceDate,
              participantCount: widget.participantCount,
              totalAmount: widget.totalAmount,
              currency: widget.currency,
              paymentMethod: paymentMethod,
              additionalDetails: widget.additionalDetails,
            );
      } else {
        // Book experience
        await ref
            .read(instantBookingNotifierProvider.notifier)
            .createExperienceInstantBooking(
              experienceId: widget.experienceId!,
              experienceName: widget.experienceName!,
              experienceImageUrl: widget.experienceImageUrl!,
              serviceDate: widget.serviceDate,
              participantCount: widget.participantCount,
              totalAmount: widget.totalAmount,
              currency: widget.currency,
              paymentMethod: paymentMethod,
              additionalDetails: widget.additionalDetails,
            );
      }

      if (!mounted) return;

      final booking = ref.read(instantBookingNotifierProvider).value;

      if (booking != null) {
        // Navigate to confirmation screen
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => InstantBookingConfirmationScreen(
              booking: booking,
            ),
          ),
        );
      }
    } catch (e) {
      if (!mounted) return;

      final scaffoldMessenger = ScaffoldMessenger.of(context);
      scaffoldMessenger.showSnackBar(
        SnackBar(
          content: Text('Error processing booking: $e'),
          backgroundColor: Colors.red,
        ),
      );
    } finally {
      if (mounted) {
        setState(() {
          _isProcessing = false;
        });
      }

      // Reset the notifier
      ref.read(instantBookingNotifierProvider.notifier).reset();
    }
  }

  Widget _buildConfirmationItem(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: const TextStyle(
              fontSize: 14,
              color: AppTheme.textSecondaryColor,
            ),
          ),
          Text(
            value,
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: AppTheme.textPrimaryColor,
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final buttonColor = widget.buttonColor ?? AppTheme.primaryColor;
    final buttonTextColor = widget.buttonTextColor ?? Colors.white;

    return SizedBox(
      width: double.infinity,
      child: ElevatedButton.icon(
        onPressed: _isProcessing ? null : _showInstantBookingDialog,
        icon: _isProcessing && widget.showLoading
            ? SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(buttonTextColor),
                ),
              )
            : Icon(widget.buttonIcon),
        label: Text(
          _isProcessing && widget.showLoading
              ? 'Processing...'
              : widget.buttonText,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
        style: ElevatedButton.styleFrom(
          backgroundColor: buttonColor,
          foregroundColor: buttonTextColor,
          padding: const EdgeInsets.symmetric(vertical: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
          disabledBackgroundColor:
              buttonColor.withAlpha(153), // 0.6 * 255 = 153
          disabledForegroundColor:
              buttonTextColor.withAlpha(153), // 0.6 * 255 = 153
        ),
      ),
    );
  }
}
