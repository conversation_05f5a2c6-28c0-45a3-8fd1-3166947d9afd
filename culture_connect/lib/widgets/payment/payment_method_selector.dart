import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:culture_connect/models/payment/payment_method_model.dart';
import 'package:culture_connect/services/payment_service.dart';

/// A widget for selecting a payment method
class PaymentMethodSelector extends ConsumerStatefulWidget {
  /// Callback when a payment method is selected
  final Function(PaymentMethodModel) onPaymentMethodSelected;

  /// Whether to show the add payment method button
  final bool showAddButton;

  /// Whether to show the remove payment method button
  final bool showRemoveButton;

  /// Whether to show the set default payment method button
  final bool showSetDefaultButton;

  /// Creates a new payment method selector
  const PaymentMethodSelector({
    super.key,
    required this.onPaymentMethodSelected,
    this.showAddButton = true,
    this.showRemoveButton = true,
    this.showSetDefaultButton = true,
  });

  @override
  ConsumerState<PaymentMethodSelector> createState() =>
      _PaymentMethodSelectorState();
}

class _PaymentMethodSelectorState extends ConsumerState<PaymentMethodSelector> {
  final PaymentService _paymentService = PaymentService();
  List<PaymentMethodModel> _paymentMethods = [];
  PaymentMethodModel? _selectedPaymentMethod;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadPaymentMethods();
  }

  Future<void> _loadPaymentMethods() async {
    if (!mounted) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final paymentMethods = await _paymentService.getSavedPaymentMethods();

      if (!mounted) return;

      setState(() {
        _paymentMethods = paymentMethods;
        // Select the default payment method if available
        if (_paymentMethods.isNotEmpty) {
          _selectedPaymentMethod = _paymentMethods.firstWhere(
            (pm) => pm.isDefault,
            orElse: () => _paymentMethods.first,
          );
        } else {
          _selectedPaymentMethod = null;
        }

        if (_selectedPaymentMethod != null) {
          widget.onPaymentMethodSelected(_selectedPaymentMethod!);
        }

        _isLoading = false;
      });
    } catch (e) {
      if (!mounted) return;

      setState(() {
        _isLoading = false;
      });

      final scaffoldMessenger = ScaffoldMessenger.of(context);
      scaffoldMessenger.showSnackBar(
        SnackBar(
          content: Text('Failed to load payment methods: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  Future<void> _addPaymentMethod() async {
    if (!mounted) return;

    // Show a dialog to add a new payment method
    final result = await showDialog<PaymentMethodModel>(
      context: context,
      builder: (context) => const AddPaymentMethodDialog(),
    );

    if (result != null && mounted) {
      setState(() {
        _paymentMethods.add(result);
        _selectedPaymentMethod = result;
      });
      widget.onPaymentMethodSelected(result);
    }
  }

  Future<void> _removePaymentMethod(PaymentMethodModel paymentMethod) async {
    if (!mounted) return;

    // Show a confirmation dialog
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Remove Payment Method'),
        content: Text('Are you sure you want to remove ${paymentMethod.name}?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            child: const Text('Remove'),
          ),
        ],
      ),
    );

    if (!mounted) return;

    if (confirmed == true) {
      try {
        final success =
            await _paymentService.removePaymentMethod(paymentMethod.id);
        if (success) {
          setState(() {
            _paymentMethods.removeWhere((pm) => pm.id == paymentMethod.id);
            if (_selectedPaymentMethod?.id == paymentMethod.id) {
              _selectedPaymentMethod =
                  _paymentMethods.isNotEmpty ? _paymentMethods.first : null;
              if (_selectedPaymentMethod != null) {
                widget.onPaymentMethodSelected(_selectedPaymentMethod!);
              }
            }
          });
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Payment method removed'),
                backgroundColor: Colors.green,
              ),
            );
          }
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Failed to remove payment method: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  Future<void> _setDefaultPaymentMethod(
      PaymentMethodModel paymentMethod) async {
    if (!mounted) return;

    try {
      final success =
          await _paymentService.setDefaultPaymentMethod(paymentMethod.id);

      if (!mounted) return;

      if (success) {
        await _loadPaymentMethods();

        if (!mounted) return;

        final scaffoldMessenger = ScaffoldMessenger.of(context);
        scaffoldMessenger.showSnackBar(
          const SnackBar(
            content: Text('Default payment method updated'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (!mounted) return;

      final scaffoldMessenger = ScaffoldMessenger.of(context);
      scaffoldMessenger.showSnackBar(
        SnackBar(
          content: Text('Failed to update default payment method: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (_paymentMethods.isEmpty) {
      return Column(
        children: [
          const Text(
            'No payment methods saved',
            style: TextStyle(fontSize: 16),
          ),
          const SizedBox(height: 16),
          if (widget.showAddButton)
            ElevatedButton.icon(
              onPressed: _addPaymentMethod,
              icon: const Icon(Icons.add),
              label: const Text('Add Payment Method'),
            ),
        ],
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Select Payment Method',
          style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 16),
        ...List.generate(
          _paymentMethods.length,
          (index) {
            final paymentMethod = _paymentMethods[index];
            return RadioListTile<PaymentMethodModel>(
              title: Row(
                children: [
                  Icon(
                    paymentMethod.type.icon,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                  const SizedBox(width: 8),
                  Text(paymentMethod.name),
                  if (paymentMethod.isDefault)
                    const Padding(
                      padding: EdgeInsets.only(left: 8),
                      child: Chip(
                        label: Text('Default'),
                        backgroundColor: Colors.blue,
                        labelStyle: TextStyle(color: Colors.white),
                        padding: EdgeInsets.symmetric(horizontal: 4),
                      ),
                    ),
                ],
              ),
              subtitle: paymentMethod.type == PaymentMethodType.creditCard
                  ? Text(
                      'Card ending in ${paymentMethod.details['last4'] ?? '****'}')
                  : null,
              value: paymentMethod,
              groupValue: _selectedPaymentMethod,
              onChanged: (value) {
                setState(() {
                  _selectedPaymentMethod = value;
                });
                if (value != null) {
                  widget.onPaymentMethodSelected(value);
                }
              },
              secondary: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  if (widget.showSetDefaultButton && !paymentMethod.isDefault)
                    IconButton(
                      icon: const Icon(Icons.star_outline),
                      tooltip: 'Set as default',
                      onPressed: () => _setDefaultPaymentMethod(paymentMethod),
                    ),
                  if (widget.showRemoveButton)
                    IconButton(
                      icon: const Icon(Icons.delete_outline),
                      tooltip: 'Remove',
                      onPressed: () => _removePaymentMethod(paymentMethod),
                    ),
                ],
              ),
            );
          },
        ),
        if (widget.showAddButton)
          Padding(
            padding: const EdgeInsets.only(top: 16),
            child: ElevatedButton.icon(
              onPressed: _addPaymentMethod,
              icon: const Icon(Icons.add),
              label: const Text('Add Payment Method'),
            ),
          ),
      ],
    );
  }
}

/// Dialog for adding a new payment method
class AddPaymentMethodDialog extends ConsumerStatefulWidget {
  /// Creates a new add payment method dialog
  const AddPaymentMethodDialog({super.key});

  @override
  ConsumerState<AddPaymentMethodDialog> createState() =>
      _AddPaymentMethodDialogState();
}

class _AddPaymentMethodDialogState
    extends ConsumerState<AddPaymentMethodDialog> {
  final PaymentService _paymentService = PaymentService();
  final _formKey = GlobalKey<FormState>();

  PaymentMethodType _type = PaymentMethodType.creditCard;
  String _name = '';
  String _cardNumber = '';
  int _expiryMonth = DateTime.now().month;
  int _expiryYear = DateTime.now().year;
  bool _isDefault = false;
  bool _isLoading = false;

  Future<void> _savePaymentMethod() async {
    if (!mounted) return;

    if (_formKey.currentState?.validate() != true) {
      return;
    }

    _formKey.currentState?.save();

    setState(() {
      _isLoading = true;
    });

    try {
      final paymentMethod = await _paymentService.addPaymentMethod(
        type: _type,
        name: _name,
        last4: _cardNumber.isNotEmpty
            ? _cardNumber.substring(_cardNumber.length - 4)
            : null,
        isDefault: _isDefault,
      );

      if (!mounted) return;

      Navigator.pop(context, paymentMethod);
    } catch (e) {
      if (!mounted) return;

      setState(() {
        _isLoading = false;
      });

      final scaffoldMessenger = ScaffoldMessenger.of(context);
      scaffoldMessenger.showSnackBar(
        SnackBar(
          content: Text('Failed to add payment method: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Add Payment Method'),
      content: Form(
        key: _formKey,
        child: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              DropdownButtonFormField<PaymentMethodType>(
                value: _type,
                decoration: const InputDecoration(
                  labelText: 'Payment Method Type',
                ),
                items: PaymentMethodType.values.map((type) {
                  return DropdownMenuItem<PaymentMethodType>(
                    value: type,
                    child: Text(type.displayName),
                  );
                }).toList(),
                onChanged: (value) {
                  setState(() {
                    _type = value!;
                  });
                },
              ),
              TextFormField(
                decoration: const InputDecoration(
                  labelText: 'Name on Card',
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter a name';
                  }
                  return null;
                },
                onSaved: (value) {
                  _name = value!;
                },
              ),
              if (_type == PaymentMethodType.creditCard) ...[
                TextFormField(
                  decoration: const InputDecoration(
                    labelText: 'Card Number',
                  ),
                  keyboardType: TextInputType.number,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please enter a card number';
                    }
                    if (value.length < 13 || value.length > 19) {
                      return 'Card number must be between 13 and 19 digits';
                    }
                    return null;
                  },
                  onSaved: (value) {
                    _cardNumber = value!;
                  },
                ),
                Row(
                  children: [
                    Expanded(
                      child: DropdownButtonFormField<int>(
                        value: _expiryMonth,
                        decoration: const InputDecoration(
                          labelText: 'Expiry Month',
                        ),
                        items: List.generate(12, (index) {
                          final month = index + 1;
                          return DropdownMenuItem<int>(
                            value: month,
                            child: Text(month.toString().padLeft(2, '0')),
                          );
                        }),
                        onChanged: (value) {
                          setState(() {
                            _expiryMonth = value!;
                          });
                        },
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: DropdownButtonFormField<int>(
                        value: _expiryYear,
                        decoration: const InputDecoration(
                          labelText: 'Expiry Year',
                        ),
                        items: List.generate(10, (index) {
                          final year = DateTime.now().year + index;
                          return DropdownMenuItem<int>(
                            value: year,
                            child: Text(year.toString()),
                          );
                        }),
                        onChanged: (value) {
                          setState(() {
                            _expiryYear = value!;
                          });
                        },
                      ),
                    ),
                  ],
                ),
                TextFormField(
                  decoration: const InputDecoration(
                    labelText: 'CVV',
                  ),
                  keyboardType: TextInputType.number,
                  obscureText: true,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please enter a CVV';
                    }
                    if (value.length < 3 || value.length > 4) {
                      return 'CVV must be 3 or 4 digits';
                    }
                    return null;
                  },
                  onSaved: (value) {
                    // CVV is not stored for security reasons
                  },
                ),
              ],
              const SizedBox(height: 16),
              CheckboxListTile(
                title: const Text('Set as default payment method'),
                value: _isDefault,
                onChanged: (value) {
                  setState(() {
                    _isDefault = value!;
                  });
                },
                controlAffinity: ListTileControlAffinity.leading,
                contentPadding: EdgeInsets.zero,
              ),
            ],
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: _isLoading ? null : () => Navigator.pop(context),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: _isLoading ? null : _savePaymentMethod,
          child: _isLoading
              ? const SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                  ),
                )
              : const Text('Save'),
        ),
      ],
    );
  }
}
