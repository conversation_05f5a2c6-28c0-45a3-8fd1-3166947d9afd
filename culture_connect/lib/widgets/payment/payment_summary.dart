import 'package:flutter/material.dart';
import 'package:culture_connect/models/booking.dart';

/// A widget for displaying a payment summary
class PaymentSummary extends StatelessWidget {
  /// The booking to display the summary for
  final Booking booking;
  
  /// The tax rate to apply (percentage)
  final double taxRate;
  
  /// The discount amount to apply
  final double? discountAmount;
  
  /// The discount code
  final String? discountCode;
  
  /// The currency symbol to use
  final String currencySymbol;

  /// Creates a new payment summary
  const PaymentSummary({
    super.key,
    required this.booking,
    this.taxRate = 0.0,
    this.discountAmount,
    this.discountCode,
    this.currencySymbol = '\$',
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    // Calculate amounts
    final subtotal = booking.totalAmount;
    final taxAmount = subtotal * (taxRate / 100);
    final discount = discountAmount ?? 0.0;
    final total = subtotal + taxAmount - discount;
    
    return Card(
      margin: const EdgeInsets.symmetric(vertical: 16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Payment Summary',
              style: theme.textTheme.titleLarge,
            ),
            const SizedBox(height: 16),
            
            // Experience details
            Row(
              children: [
                const Icon(Icons.event, size: 16),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    'Experience Date',
                    style: theme.textTheme.bodyMedium,
                  ),
                ),
                Text(
                  '${booking.date.day}/${booking.date.month}/${booking.date.year}',
                  style: theme.textTheme.bodyMedium,
                ),
              ],
            ),
            const SizedBox(height: 8),
            
            Row(
              children: [
                const Icon(Icons.access_time, size: 16),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    'Time Slot',
                    style: theme.textTheme.bodyMedium,
                  ),
                ),
                Text(
                  booking.timeSlot.formattedTime,
                  style: theme.textTheme.bodyMedium,
                ),
              ],
            ),
            const SizedBox(height: 8),
            
            Row(
              children: [
                const Icon(Icons.people, size: 16),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    'Participants',
                    style: theme.textTheme.bodyMedium,
                  ),
                ),
                Text(
                  '${booking.participantCount}',
                  style: theme.textTheme.bodyMedium,
                ),
              ],
            ),
            
            const Divider(height: 32),
            
            // Price breakdown
            Row(
              children: [
                Expanded(
                  child: Text(
                    'Subtotal',
                    style: theme.textTheme.bodyMedium,
                  ),
                ),
                Text(
                  '$currencySymbol${subtotal.toStringAsFixed(2)}',
                  style: theme.textTheme.bodyMedium,
                ),
              ],
            ),
            const SizedBox(height: 8),
            
            if (taxRate > 0) ...[
              Row(
                children: [
                  Expanded(
                    child: Text(
                      'Tax ($taxRate%)',
                      style: theme.textTheme.bodyMedium,
                    ),
                  ),
                  Text(
                    '$currencySymbol${taxAmount.toStringAsFixed(2)}',
                    style: theme.textTheme.bodyMedium,
                  ),
                ],
              ),
              const SizedBox(height: 8),
            ],
            
            if (discountAmount != null && discountAmount! > 0) ...[
              Row(
                children: [
                  Expanded(
                    child: Text(
                      'Discount${discountCode != null ? ' ($discountCode)' : ''}',
                      style: theme.textTheme.bodyMedium,
                    ),
                  ),
                  Text(
                    '-$currencySymbol${discountAmount!.toStringAsFixed(2)}',
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: Colors.green,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
            ],
            
            const Divider(height: 16),
            
            Row(
              children: [
                Expanded(
                  child: Text(
                    'Total',
                    style: theme.textTheme.titleMedium,
                  ),
                ),
                Text(
                  '$currencySymbol${total.toStringAsFixed(2)}',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            
            if (booking.specialRequirements.isNotEmpty) ...[
              const SizedBox(height: 16),
              const Divider(height: 16),
              
              Text(
                'Special Requirements',
                style: theme.textTheme.titleSmall,
              ),
              const SizedBox(height: 8),
              Text(
                booking.specialRequirements,
                style: theme.textTheme.bodyMedium,
              ),
            ],
          ],
        ),
      ),
    );
  }
}
