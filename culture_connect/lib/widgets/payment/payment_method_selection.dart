import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:culture_connect/models/payment/payment_method_model.dart';
import 'package:culture_connect/services/payment_service.dart';
import 'package:culture_connect/theme/app_theme.dart';
import 'package:culture_connect/widgets/payment/add_payment_method_sheet.dart';

/// A widget for selecting a payment method
class PaymentMethodSelection extends ConsumerStatefulWidget {
  /// The initially selected payment method ID
  final String? initialSelectedId;

  /// Callback when a payment method is selected
  final Function(PaymentMethodModel) onPaymentMethodSelected;

  /// Whether to show the add payment method button
  final bool showAddButton;

  /// Creates a new payment method selection widget
  const PaymentMethodSelection({
    super.key,
    this.initialSelectedId,
    required this.onPaymentMethodSelected,
    this.showAddButton = true,
  });

  @override
  ConsumerState<PaymentMethodSelection> createState() =>
      _PaymentMethodSelectionState();
}

class _PaymentMethodSelectionState
    extends ConsumerState<PaymentMethodSelection> {
  String? _selectedPaymentMethodId;
  bool _isLoading = true;
  List<PaymentMethodModel> _paymentMethods = [];

  @override
  void initState() {
    super.initState();
    _selectedPaymentMethodId = widget.initialSelectedId;
    _loadPaymentMethods();
  }

  Future<void> _loadPaymentMethods() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final paymentService = PaymentService();
      final methods = await paymentService.getSavedPaymentMethods();

      setState(() {
        _paymentMethods = methods;

        // If no payment method is selected, select the default one
        if (_selectedPaymentMethodId == null && methods.isNotEmpty) {
          final defaultMethod = methods.firstWhere(
            (method) => method.isDefault,
            orElse: () => methods.first,
          );
          _selectedPaymentMethodId = defaultMethod.id;
          widget.onPaymentMethodSelected(defaultMethod);
        }

        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error loading payment methods: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _showAddPaymentMethodSheet() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => AddPaymentMethodSheet(
        onPaymentMethodAdded: (newMethod) {
          setState(() {
            _paymentMethods.add(newMethod);
            _selectedPaymentMethodId = newMethod.id;
          });
          widget.onPaymentMethodSelected(newMethod);
        },
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Payment Method',
              style: TextStyle(
                fontSize: 18.sp,
                fontWeight: FontWeight.bold,
              ),
            ),
            if (widget.showAddButton)
              TextButton.icon(
                onPressed: _showAddPaymentMethodSheet,
                icon: Icon(
                  Icons.add,
                  size: 18.sp,
                  color: AppTheme.primaryColor,
                ),
                label: const Text(
                  'Add New',
                  style: TextStyle(
                    color: AppTheme.primaryColor,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
          ],
        ),
        SizedBox(height: 16.h),
        if (_isLoading)
          const Center(
            child: CircularProgressIndicator(),
          )
        else if (_paymentMethods.isEmpty)
          _buildEmptyState()
        else
          _buildPaymentMethodsList(),
      ],
    );
  }

  Widget _buildEmptyState() {
    return Container(
      padding: EdgeInsets.all(16.r),
      decoration: BoxDecoration(
        color: Colors.grey.shade100,
        borderRadius: BorderRadius.circular(8.r),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.credit_card,
            size: 48.sp,
            color: Colors.grey,
          ),
          SizedBox(height: 16.h),
          Text(
            'No payment methods found',
            style: TextStyle(
              fontSize: 16.sp,
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            'Add a payment method to continue',
            style: TextStyle(
              fontSize: 14.sp,
              color: Colors.grey,
            ),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 16.h),
          ElevatedButton.icon(
            onPressed: _showAddPaymentMethodSheet,
            icon: const Icon(Icons.add),
            label: const Text('Add Payment Method'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.primaryColor,
              foregroundColor: Colors.white,
              padding: EdgeInsets.symmetric(
                horizontal: 24.w,
                vertical: 12.h,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPaymentMethodsList() {
    return Column(
      children: _paymentMethods.map((method) {
        final isSelected = method.id == _selectedPaymentMethodId;

        return GestureDetector(
          onTap: () {
            setState(() {
              _selectedPaymentMethodId = method.id;
            });
            widget.onPaymentMethodSelected(method);
          },
          child: Container(
            margin: EdgeInsets.only(bottom: 12.h),
            padding: EdgeInsets.all(16.r),
            decoration: BoxDecoration(
              color: isSelected
                  ? AppTheme.primaryColor.withAlpha(26)
                  : Colors.white,
              borderRadius: BorderRadius.circular(8.r),
              border: Border.all(
                color:
                    isSelected ? AppTheme.primaryColor : Colors.grey.shade300,
                width: isSelected ? 2 : 1,
              ),
            ),
            child: Row(
              children: [
                // Payment method icon
                Container(
                  width: 48.w,
                  height: 48.w,
                  decoration: BoxDecoration(
                    color: AppTheme.primaryColor.withAlpha(26),
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    method.type.icon,
                    color: AppTheme.primaryColor,
                    size: 24.sp,
                  ),
                ),
                SizedBox(width: 16.w),

                // Payment method details
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        method.name,
                        style: TextStyle(
                          fontSize: 16.sp,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      if (method.details['last4'] != null) ...[
                        SizedBox(height: 4.h),
                        Text(
                          'Ending in ${method.details['last4']}',
                          style: TextStyle(
                            fontSize: 14.sp,
                            color: Colors.grey,
                          ),
                        ),
                      ],
                    ],
                  ),
                ),

                // Selection indicator
                if (isSelected)
                  Icon(
                    Icons.check_circle,
                    color: AppTheme.primaryColor,
                    size: 24.sp,
                  )
                else
                  Icon(
                    Icons.circle_outlined,
                    color: Colors.grey,
                    size: 24.sp,
                  ),
              ],
            ),
          ),
        );
      }).toList(),
    );
  }
}
