import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:culture_connect/theme/app_theme.dart';
import 'package:culture_connect/widgets/payment/credit_card_input_formatters.dart';

/// A form for entering credit card details
class CreditCardForm extends StatefulWidget {
  /// Callback when the form is submitted
  final Function({
    required String cardNumber,
    required String cardHolderName,
    required int expiryMonth,
    required int expiryYear,
    required String cvv,
    required bool setAsDefault,
  }) onSubmit;

  /// Whether the form is in a loading state
  final bool isLoading;

  /// Creates a new credit card form
  const CreditCardForm({
    super.key,
    required this.onSubmit,
    this.isLoading = false,
  });

  @override
  State<CreditCardForm> createState() => _CreditCardFormState();
}

class _CreditCardFormState extends State<CreditCardForm> {
  final _formKey = GlobalKey<FormState>();
  final _cardNumberController = TextEditingController();
  final _cardHolderNameController = TextEditingController();
  final _expiryDateController = TextEditingController();
  final _cvvController = TextEditingController();

  bool _setAsDefault = true;
  String? _cardType;

  @override
  void dispose() {
    _cardNumberController.dispose();
    _cardHolderNameController.dispose();
    _expiryDateController.dispose();
    _cvvController.dispose();
    super.dispose();
  }

  void _updateCardType(String cardNumber) {
    setState(() {
      if (cardNumber.startsWith('4')) {
        _cardType = 'Visa';
      } else if (cardNumber.startsWith(RegExp(r'5[1-5]'))) {
        _cardType = 'Mastercard';
      } else if (cardNumber.startsWith(RegExp(r'3[47]'))) {
        _cardType = 'American Express';
      } else if (cardNumber.startsWith(RegExp(r'6(?:011|5[0-9]{2})'))) {
        _cardType = 'Discover';
      } else if (cardNumber.length >= 4) {
        _cardType = 'Unknown';
      } else {
        _cardType = null;
      }
    });
  }

  void _submitForm() {
    if (_formKey.currentState?.validate() != true) {
      return;
    }

    // Parse expiry date
    final expiryParts = _expiryDateController.text.split('/');
    if (expiryParts.length != 2) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Invalid expiry date format'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    final expiryMonth = int.tryParse(expiryParts[0]);
    final expiryYear = int.tryParse(expiryParts[1]);

    if (expiryMonth == null || expiryYear == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Invalid expiry date'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    // Add 2000 to convert 2-digit year to 4-digit year
    final fullYear = expiryYear + 2000;

    // Call the onSubmit callback
    widget.onSubmit(
      cardNumber: _cardNumberController.text.replaceAll(' ', ''),
      cardHolderName: _cardHolderNameController.text,
      expiryMonth: expiryMonth,
      expiryYear: fullYear,
      cvv: _cvvController.text,
      setAsDefault: _setAsDefault,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Form(
      key: _formKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Card number field
          Text(
            'Card Number',
            style: TextStyle(
              fontSize: 14.sp,
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: 8.h),
          TextFormField(
            controller: _cardNumberController,
            decoration: InputDecoration(
              hintText: '1234 5678 9012 3456',
              prefixIcon: const Icon(Icons.credit_card),
              suffixIcon: _cardType != null
                  ? Padding(
                      padding: EdgeInsets.all(12.r),
                      child: Text(
                        _cardType!,
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: _cardType == 'Unknown'
                              ? Colors.red
                              : Colors.black,
                        ),
                      ),
                    )
                  : null,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8.r),
              ),
            ),
            keyboardType: TextInputType.number,
            inputFormatters: [
              FilteringTextInputFormatter.digitsOnly,
              LengthLimitingTextInputFormatter(16),
              CardNumberInputFormatter(),
            ],
            onChanged: _updateCardType,
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Please enter your card number';
              }

              final cleanNumber = value.replaceAll(' ', '');
              if (cleanNumber.length < 15) {
                return 'Card number is too short';
              }

              if (!_isValidCardNumber(cleanNumber)) {
                return 'Invalid card number';
              }

              return null;
            },
          ),
          SizedBox(height: 16.h),

          // Card holder name field
          Text(
            'Card Holder Name',
            style: TextStyle(
              fontSize: 14.sp,
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: 8.h),
          TextFormField(
            controller: _cardHolderNameController,
            decoration: InputDecoration(
              hintText: 'John Doe',
              prefixIcon: const Icon(Icons.person),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8.r),
              ),
            ),
            textCapitalization: TextCapitalization.words,
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Please enter the card holder name';
              }
              return null;
            },
          ),
          SizedBox(height: 16.h),

          // Expiry date and CVV fields
          Row(
            children: [
              // Expiry date field
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Expiry Date',
                      style: TextStyle(
                        fontSize: 14.sp,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    SizedBox(height: 8.h),
                    TextFormField(
                      controller: _expiryDateController,
                      decoration: InputDecoration(
                        hintText: 'MM/YY',
                        prefixIcon: const Icon(Icons.calendar_today),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8.r),
                        ),
                      ),
                      keyboardType: TextInputType.number,
                      inputFormatters: [
                        FilteringTextInputFormatter.digitsOnly,
                        LengthLimitingTextInputFormatter(4),
                        CardExpiryInputFormatter(),
                      ],
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Required';
                        }

                        if (!value.contains('/')) {
                          return 'Invalid format';
                        }

                        final parts = value.split('/');
                        if (parts.length != 2) {
                          return 'Invalid format';
                        }

                        final month = int.tryParse(parts[0]);
                        final year = int.tryParse(parts[1]);

                        if (month == null || year == null) {
                          return 'Invalid date';
                        }

                        if (month < 1 || month > 12) {
                          return 'Invalid month';
                        }

                        final now = DateTime.now();
                        final currentYear = now.year % 100; // Get last 2 digits
                        final currentMonth = now.month;

                        if (year < currentYear ||
                            (year == currentYear && month < currentMonth)) {
                          return 'Card expired';
                        }

                        return null;
                      },
                    ),
                  ],
                ),
              ),
              SizedBox(width: 16.w),

              // CVV field
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'CVV',
                      style: TextStyle(
                        fontSize: 14.sp,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    SizedBox(height: 8.h),
                    TextFormField(
                      controller: _cvvController,
                      decoration: InputDecoration(
                        hintText: '123',
                        prefixIcon: const Icon(Icons.security),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8.r),
                        ),
                      ),
                      keyboardType: TextInputType.number,
                      inputFormatters: [
                        FilteringTextInputFormatter.digitsOnly,
                        LengthLimitingTextInputFormatter(4),
                      ],
                      obscureText: true,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Required';
                        }

                        if (value.length < 3) {
                          return 'Too short';
                        }

                        return null;
                      },
                    ),
                  ],
                ),
              ),
            ],
          ),
          SizedBox(height: 24.h),

          // Set as default checkbox
          Row(
            children: [
              Checkbox(
                value: _setAsDefault,
                onChanged: (value) {
                  setState(() {
                    _setAsDefault = value ?? false;
                  });
                },
                activeColor: AppTheme.primaryColor,
              ),
              Text(
                'Set as default payment method',
                style: TextStyle(
                  fontSize: 14.sp,
                ),
              ),
            ],
          ),
          SizedBox(height: 24.h),

          // Submit button
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: widget.isLoading ? null : _submitForm,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.primaryColor,
                foregroundColor: Colors.white,
                padding: EdgeInsets.symmetric(vertical: 16.h),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8.r),
                ),
              ),
              child: widget.isLoading
                  ? SizedBox(
                      width: 24.w,
                      height: 24.h,
                      child: CircularProgressIndicator(
                        color: Colors.white,
                        strokeWidth: 2.w,
                      ),
                    )
                  : Text(
                      'Add Card',
                      style: TextStyle(
                        fontSize: 16.sp,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
            ),
          ),
          SizedBox(height: 16.h),

          // Security note
          Center(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.lock,
                  size: 16.sp,
                  color: Colors.grey,
                ),
                SizedBox(width: 8.w),
                Text(
                  'Your payment information is secure',
                  style: TextStyle(
                    fontSize: 12.sp,
                    color: Colors.grey,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  bool _isValidCardNumber(String cardNumber) {
    // Luhn algorithm for card number validation
    int sum = 0;
    bool alternate = false;

    for (int i = cardNumber.length - 1; i >= 0; i--) {
      int digit = int.parse(cardNumber[i]);

      if (alternate) {
        digit *= 2;
        if (digit > 9) {
          digit -= 9;
        }
      }

      sum += digit;
      alternate = !alternate;
    }

    return sum % 10 == 0;
  }
}
