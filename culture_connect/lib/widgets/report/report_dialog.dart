import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:image_picker/image_picker.dart';
import 'package:culture_connect/models/report_model.dart';
import 'package:culture_connect/services/report_service.dart';

/// A dialog for reporting entities
class ReportDialog extends ConsumerStatefulWidget {
  /// The ID of the entity to report
  final String entityId;

  /// The type of report
  final ReportType reportType;

  /// The title of the entity being reported
  final String entityTitle;

  /// Callback when the report is submitted
  final Function(Report)? onReportSubmitted;

  /// Creates a new report dialog
  const ReportDialog({
    super.key,
    required this.entityId,
    required this.reportType,
    required this.entityTitle,
    this.onReportSubmitted,
  });

  /// Shows the report dialog
  static Future<Report?> show({
    required BuildContext context,
    required String entityId,
    required ReportType reportType,
    required String entityTitle,
    Function(Report)? onReportSubmitted,
  }) {
    return showDialog<Report>(
      context: context,
      builder: (context) => ReportDialog(
        entityId: entityId,
        reportType: reportType,
        entityTitle: entityTitle,
        onReportSubmitted: onReportSubmitted,
      ),
    );
  }

  @override
  ConsumerState<ReportDialog> createState() => _ReportDialogState();
}

class _ReportDialogState extends ConsumerState<ReportDialog> {
  final _formKey = GlobalKey<FormState>();
  final _descriptionController = TextEditingController();

  ReportReason? _selectedReason;
  ReportSeverity _selectedSeverity = ReportSeverity.medium;
  final List<File> _evidenceFiles = [];
  bool _isLoading = false;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _loadReportReasons();
  }

  @override
  void dispose() {
    _descriptionController.dispose();
    super.dispose();
  }

  Future<void> _loadReportReasons() async {
    // This will trigger the reportReasonsProvider to load the reasons
  }

  Future<void> _submitReport() async {
    if (_formKey.currentState?.validate() != true) {
      return;
    }

    if (_selectedReason == null) {
      setState(() {
        _errorMessage = 'Please select a reason for reporting';
      });
      return;
    }

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final reportService = ref.read(reportServiceProvider);

      final report = await reportService.submitReport(
        reportedEntityId: widget.entityId,
        type: widget.reportType,
        reason: _selectedReason!.title,
        description: _descriptionController.text,
        severity: _selectedSeverity,
        evidence: _evidenceFiles,
      );

      if (mounted) {
        widget.onReportSubmitted?.call(report);
        Navigator.of(context).pop(report);
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Failed to submit report: $e';
        _isLoading = false;
      });
    }
  }

  Future<void> _pickImage() async {
    try {
      final picker = ImagePicker();
      final pickedFile = await picker.pickImage(source: ImageSource.gallery);

      if (pickedFile != null) {
        setState(() {
          _evidenceFiles.add(File(pickedFile.path));
        });
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Failed to pick image: $e';
      });
    }
  }

  void _removeEvidence(int index) {
    setState(() {
      _evidenceFiles.removeAt(index);
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final reasonsAsync = ref.watch(reportReasonsProvider(widget.reportType));

    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Container(
        padding: const EdgeInsets.all(16),
        constraints: const BoxConstraints(
          maxWidth: 500,
          maxHeight: 700,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Dialog title
            Row(
              children: [
                Icon(
                  Icons.report_problem,
                  color: theme.colorScheme.error,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    'Report ${widget.reportType.typeDisplayName}',
                    style: theme.textTheme.titleLarge,
                  ),
                ),
                IconButton(
                  icon: const Icon(Icons.close),
                  onPressed: () => Navigator.of(context).pop(),
                ),
              ],
            ),

            // Entity being reported
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 8),
              child: Text(
                'You are reporting: ${widget.entityTitle}',
                style: theme.textTheme.bodyMedium,
              ),
            ),

            const Divider(),

            // Report form
            Expanded(
              child: SingleChildScrollView(
                child: Form(
                  key: _formKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Report reason
                      Text(
                        'Reason for reporting',
                        style: theme.textTheme.titleSmall,
                      ),
                      const SizedBox(height: 8),

                      reasonsAsync.when(
                        data: (reasons) {
                          if (reasons.isEmpty) {
                            return const Text('No report reasons available');
                          }

                          return Column(
                            children: reasons.map((reason) {
                              return RadioListTile<ReportReason>(
                                title: Text(reason.title),
                                subtitle: Text(
                                  reason.description,
                                  style: theme.textTheme.bodySmall,
                                ),
                                value: reason,
                                groupValue: _selectedReason,
                                onChanged: (value) {
                                  setState(() {
                                    _selectedReason = value;
                                    _selectedSeverity = reason.defaultSeverity;
                                  });
                                },
                              );
                            }).toList(),
                          );
                        },
                        loading: () => const Center(
                          child: CircularProgressIndicator(),
                        ),
                        error: (error, stackTrace) => Text(
                          'Error loading report reasons: $error',
                          style: TextStyle(color: theme.colorScheme.error),
                        ),
                      ),

                      const SizedBox(height: 16),

                      // Report severity
                      Text(
                        'Severity',
                        style: theme.textTheme.titleSmall,
                      ),
                      const SizedBox(height: 8),

                      DropdownButtonFormField<ReportSeverity>(
                        value: _selectedSeverity,
                        decoration: const InputDecoration(
                          border: OutlineInputBorder(),
                          contentPadding: EdgeInsets.symmetric(
                            horizontal: 16,
                            vertical: 8,
                          ),
                        ),
                        items: ReportSeverity.values.map((severity) {
                          return DropdownMenuItem<ReportSeverity>(
                            value: severity,
                            child: Text(severity.severityDisplayName),
                          );
                        }).toList(),
                        onChanged: (value) {
                          setState(() {
                            _selectedSeverity = value!;
                          });
                        },
                      ),

                      const SizedBox(height: 16),

                      // Report description
                      Text(
                        'Description',
                        style: theme.textTheme.titleSmall,
                      ),
                      const SizedBox(height: 8),

                      TextFormField(
                        controller: _descriptionController,
                        decoration: const InputDecoration(
                          border: OutlineInputBorder(),
                          hintText: 'Provide details about the issue',
                        ),
                        maxLines: 5,
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Please provide a description';
                          }
                          if (value.length < 10) {
                            return 'Description is too short';
                          }
                          return null;
                        },
                      ),

                      const SizedBox(height: 16),

                      // Evidence
                      Text(
                        'Evidence (Optional)',
                        style: theme.textTheme.titleSmall,
                      ),
                      const SizedBox(height: 8),

                      if (_evidenceFiles.isNotEmpty)
                        SizedBox(
                          height: 100,
                          child: ListView.builder(
                            scrollDirection: Axis.horizontal,
                            itemCount: _evidenceFiles.length,
                            itemBuilder: (context, index) {
                              return Stack(
                                children: [
                                  Container(
                                    margin: const EdgeInsets.only(right: 8),
                                    width: 100,
                                    height: 100,
                                    decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(8),
                                      image: DecorationImage(
                                        image: FileImage(_evidenceFiles[index]),
                                        fit: BoxFit.cover,
                                      ),
                                    ),
                                  ),
                                  Positioned(
                                    top: 4,
                                    right: 12,
                                    child: GestureDetector(
                                      onTap: () => _removeEvidence(index),
                                      child: Container(
                                        padding: const EdgeInsets.all(4),
                                        decoration: const BoxDecoration(
                                          color: Colors.red,
                                          shape: BoxShape.circle,
                                        ),
                                        child: const Icon(
                                          Icons.close,
                                          color: Colors.white,
                                          size: 16,
                                        ),
                                      ),
                                    ),
                                  ),
                                ],
                              );
                            },
                          ),
                        ),

                      const SizedBox(height: 8),

                      OutlinedButton.icon(
                        onPressed: _pickImage,
                        icon: const Icon(Icons.add_photo_alternate),
                        label: const Text('Add Evidence'),
                      ),

                      if (_errorMessage != null) ...[
                        const SizedBox(height: 16),
                        Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: theme.colorScheme.error.withAlpha(
                                26), // 0.1 opacity is approximately 26 alpha
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Row(
                            children: [
                              Icon(
                                Icons.error_outline,
                                color: theme.colorScheme.error,
                              ),
                              const SizedBox(width: 8),
                              Expanded(
                                child: Text(
                                  _errorMessage!,
                                  style: TextStyle(
                                    color: theme.colorScheme.error,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
              ),
            ),

            const Divider(),

            // Action buttons
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton(
                  onPressed:
                      _isLoading ? null : () => Navigator.of(context).pop(),
                  child: const Text('Cancel'),
                ),
                const SizedBox(width: 8),
                ElevatedButton(
                  onPressed: _isLoading ? null : _submitReport,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: theme.colorScheme.error,
                    foregroundColor: Colors.white,
                  ),
                  child: _isLoading
                      ? const SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor:
                                AlwaysStoppedAnimation<Color>(Colors.white),
                          ),
                        )
                      : const Text('Submit Report'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
