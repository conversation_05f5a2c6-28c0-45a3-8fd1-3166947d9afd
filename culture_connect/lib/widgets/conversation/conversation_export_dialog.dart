// Flutter imports
import 'package:flutter/material.dart';

// Package imports
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:share_plus/share_plus.dart';

// Project imports
import 'package:culture_connect/models/translation/conversation_model.dart';
import 'package:culture_connect/services/voice_translation/conversation_service.dart';
import 'package:culture_connect/theme/app_theme.dart';

/// A dialog for exporting conversations
class ConversationExportDialog extends ConsumerStatefulWidget {
  /// The conversation to export
  final ConversationModel conversation;

  /// Creates a new conversation export dialog
  const ConversationExportDialog({
    super.key,
    required this.conversation,
  });

  @override
  ConsumerState<ConversationExportDialog> createState() =>
      _ConversationExportDialogState();
}

class _ConversationExportDialogState
    extends ConsumerState<ConversationExportDialog> {
  ConversationExportFormat _selectedFormat = ConversationExportFormat.text;
  bool _isExporting = false;
  String? _exportedFilePath;
  String? _errorMessage;

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Export Conversation'),
      content: SizedBox(
        width: 300,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Export "${widget.conversation.title}" to a file.',
              style: const TextStyle(
                fontSize: 14,
                color: AppTheme.textSecondaryColor,
              ),
            ),

            const SizedBox(height: 16),

            // Format selection
            const Text(
              'Select Format:',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color: AppTheme.textPrimaryColor,
              ),
            ),

            const SizedBox(height: 8),

            // Text format option
            _buildFormatOption(
              ConversationExportFormat.text,
              'Text (.txt)',
              'Plain text format with conversation details.',
              Icons.text_fields,
            ),

            // JSON format option
            _buildFormatOption(
              ConversationExportFormat.json,
              'JSON (.json)',
              'Structured data format for developers.',
              Icons.code,
            ),

            // PDF format option (disabled for now)
            _buildFormatOption(
              ConversationExportFormat.pdf,
              'PDF (.pdf)',
              'Document format with formatting (coming soon).',
              Icons.picture_as_pdf,
              enabled: false,
            ),

            if (_errorMessage != null) ...[
              const SizedBox(height: 16),
              Text(
                _errorMessage!,
                style: const TextStyle(
                  fontSize: 12,
                  color: Colors.red,
                ),
              ),
            ],

            if (_exportedFilePath != null) ...[
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.green.withAlpha(26), // 0.1 * 255 = 26
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: Colors.green.withAlpha(77), // 0.3 * 255 = 77
                    width: 1,
                  ),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Export Successful!',
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                        color: Colors.green,
                      ),
                    ),
                    const SizedBox(height: 4),
                    const Text(
                      'File saved to:',
                      style: TextStyle(
                        fontSize: 12,
                        color: AppTheme.textSecondaryColor,
                      ),
                    ),
                    Text(
                      _exportedFilePath!,
                      style: const TextStyle(
                        fontSize: 12,
                        color: AppTheme.textPrimaryColor,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
      actions: [
        // Cancel button
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text(
            'Cancel',
            style: TextStyle(
              color: AppTheme.textSecondaryColor,
            ),
          ),
        ),

        // Share button (only if export was successful)
        if (_exportedFilePath != null)
          TextButton(
            onPressed: _shareExportedFile,
            child: const Text(
              'Share',
              style: TextStyle(
                color: AppTheme.primaryColor,
              ),
            ),
          ),

        // Export button
        ElevatedButton(
          onPressed: _isExporting ? null : _exportConversation,
          style: ElevatedButton.styleFrom(
            backgroundColor: AppTheme.primaryColor,
            foregroundColor: Colors.white,
          ),
          child: _isExporting
              ? const SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    strokeWidth: 2,
                  ),
                )
              : const Text('Export'),
        ),
      ],
    );
  }

  /// Build a format option
  Widget _buildFormatOption(
    ConversationExportFormat format,
    String title,
    String description,
    IconData icon, {
    bool enabled = true,
  }) {
    return Opacity(
      opacity: enabled ? 1.0 : 0.5,
      child: RadioListTile<ConversationExportFormat>(
        title: Row(
          children: [
            Icon(
              icon,
              size: 20,
              color: AppTheme.primaryColor,
            ),
            const SizedBox(width: 8),
            Text(
              title,
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color: AppTheme.textPrimaryColor,
              ),
            ),
          ],
        ),
        subtitle: Text(
          description,
          style: const TextStyle(
            fontSize: 12,
            color: AppTheme.textSecondaryColor,
          ),
        ),
        value: format,
        groupValue: _selectedFormat,
        onChanged: enabled
            ? (value) {
                if (value != null) {
                  setState(() {
                    _selectedFormat = value;
                  });
                }
              }
            : null,
        activeColor: AppTheme.primaryColor,
        contentPadding: EdgeInsets.zero,
        dense: true,
      ),
    );
  }

  /// Export the conversation
  Future<void> _exportConversation() async {
    setState(() {
      _isExporting = true;
      _errorMessage = null;
      _exportedFilePath = null;
    });

    try {
      final conversationService = ref.read(conversationServiceProvider);
      final filePath = await conversationService.exportConversation(
        widget.conversation.id,
        _selectedFormat,
      );

      if (!mounted) return;

      if (filePath != null) {
        setState(() {
          _exportedFilePath = filePath;
        });
      } else {
        setState(() {
          _errorMessage = 'Failed to export conversation. Please try again.';
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = 'Error: $e';
        });
      }
    } finally {
      if (mounted) {
        setState(() {
          _isExporting = false;
        });
      }
    }
  }

  /// Share the exported file
  Future<void> _shareExportedFile() async {
    if (_exportedFilePath == null) return;

    try {
      // Create XFile from the file path
      final xFile = XFile(_exportedFilePath!);

      // Use shareXFiles instead of deprecated shareFiles
      await Share.shareXFiles(
        [xFile],
        text: 'Conversation: ${widget.conversation.title}',
      );
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = 'Error sharing file: $e';
        });
      }
    }
  }
}
