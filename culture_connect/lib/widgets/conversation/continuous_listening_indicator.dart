// Flutter imports
import 'package:flutter/material.dart';

// Package imports
import 'package:flutter_riverpod/flutter_riverpod.dart';

// Project imports
import 'package:culture_connect/models/translation/conversation_model.dart';
import 'package:culture_connect/theme/app_theme.dart';

/// A widget that indicates continuous listening mode is active
class ContinuousListeningIndicator extends ConsumerStatefulWidget {
  /// Whether continuous listening is active
  final bool isActive;

  /// The current role
  final ConversationRole currentRole;

  /// The size of the indicator
  final double size;

  /// Whether to show the label
  final bool showLabel;

  /// The color of the indicator
  final Color? color;

  /// Creates a new continuous listening indicator
  const ContinuousListeningIndicator({
    super.key,
    required this.isActive,
    required this.currentRole,
    this.size = 24.0,
    this.showLabel = true,
    this.color,
  });

  @override
  ConsumerState<ContinuousListeningIndicator> createState() =>
      _ContinuousListeningIndicatorState();
}

class _ContinuousListeningIndicatorState
    extends ConsumerState<ContinuousListeningIndicator>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1500),
    );

    _animation = Tween<double>(begin: 0.5, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeInOut,
      ),
    );

    if (widget.isActive) {
      _animationController.repeat(reverse: true);
    }
  }

  @override
  void didUpdateWidget(ContinuousListeningIndicator oldWidget) {
    super.didUpdateWidget(oldWidget);

    if (widget.isActive && !oldWidget.isActive) {
      _animationController.repeat(reverse: true);
    } else if (!widget.isActive && oldWidget.isActive) {
      _animationController.stop();
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final color = widget.color ?? _getColorForRole(widget.currentRole);

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Animated microphone icon
        AnimatedBuilder(
          animation: _animation,
          builder: (context, child) {
            return Container(
              width: widget.size,
              height: widget.size,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: color.withAlpha(51), // 0.2 * 255 = 51
                boxShadow: [
                  BoxShadow(
                    color:
                        color.withAlpha((_animation.value * 0.3 * 255).toInt()),
                    blurRadius: widget.size * _animation.value,
                    spreadRadius: widget.size * _animation.value * 0.5,
                  ),
                ],
              ),
              child: Icon(
                Icons.mic,
                size: widget.size * 0.6,
                color: color,
              ),
            );
          },
        ),

        if (widget.showLabel) ...[
          const SizedBox(width: 8),

          // Label
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                widget.isActive ? 'Listening...' : 'Listening paused',
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                  color: color,
                ),
              ),
              Text(
                _getRoleLabel(widget.currentRole),
                style: const TextStyle(
                  fontSize: 10,
                  color: AppTheme.textSecondaryColor,
                ),
              ),
            ],
          ),
        ],
      ],
    );
  }

  /// Get the color for a role
  Color _getColorForRole(ConversationRole role) {
    switch (role) {
      case ConversationRole.user:
        return AppTheme.primaryColor;
      case ConversationRole.other:
        return AppTheme.secondaryColor;
      case ConversationRole.system:
        return Colors.grey;
    }
  }

  /// Get the label for a role
  String _getRoleLabel(ConversationRole role) {
    switch (role) {
      case ConversationRole.user:
        return 'You';
      case ConversationRole.other:
        return 'Other speaker';
      case ConversationRole.system:
        return 'System';
    }
  }
}

/// A widget that shows a pulsating wave animation for voice recording
class VoiceWaveAnimation extends StatefulWidget {
  /// Whether the animation is active
  final bool isActive;

  /// The color of the waves
  final Color color;

  /// The size of the widget
  final double size;

  /// The number of waves
  final int waveCount;

  /// Creates a new voice wave animation
  const VoiceWaveAnimation({
    super.key,
    required this.isActive,
    required this.color,
    this.size = 60.0,
    this.waveCount = 3,
  });

  @override
  State<VoiceWaveAnimation> createState() => _VoiceWaveAnimationState();
}

class _VoiceWaveAnimationState extends State<VoiceWaveAnimation>
    with TickerProviderStateMixin {
  late List<AnimationController> _controllers;
  late List<Animation<double>> _animations;

  @override
  void initState() {
    super.initState();

    _controllers = List.generate(
      widget.waveCount,
      (index) => AnimationController(
        vsync: this,
        duration: Duration(milliseconds: 1500 + (index * 200)),
      ),
    );

    _animations = List.generate(
      widget.waveCount,
      (index) => Tween<double>(begin: 0.0, end: 1.0).animate(
        CurvedAnimation(
          parent: _controllers[index],
          curve: Curves.easeInOut,
        ),
      ),
    );

    if (widget.isActive) {
      for (var controller in _controllers) {
        controller.repeat();
      }
    }
  }

  @override
  void didUpdateWidget(VoiceWaveAnimation oldWidget) {
    super.didUpdateWidget(oldWidget);

    if (widget.isActive && !oldWidget.isActive) {
      for (var controller in _controllers) {
        controller.repeat();
      }
    } else if (!widget.isActive && oldWidget.isActive) {
      for (var controller in _controllers) {
        controller.stop();
      }
    }
  }

  @override
  void dispose() {
    for (var controller in _controllers) {
      controller.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: widget.size,
      height: widget.size,
      child: Stack(
        alignment: Alignment.center,
        children: [
          // Waves
          ...List.generate(widget.waveCount, (index) {
            return AnimatedBuilder(
              animation: _animations[index],
              builder: (context, child) {
                return Opacity(
                  opacity: 1.0 - _animations[index].value,
                  child: Container(
                    width: widget.size * _animations[index].value,
                    height: widget.size * _animations[index].value,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: widget.color.withAlpha(77), // 0.3 * 255 = 77
                    ),
                  ),
                );
              },
            );
          }),

          // Center dot
          Container(
            width: widget.size * 0.2,
            height: widget.size * 0.2,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: widget.color,
            ),
          ),
        ],
      ),
    );
  }
}
