import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:culture_connect/services/ar_voice_command_service.dart';

/// A widget that provides a UI for voice commands in the AR experience.
class ARVoiceCommandUI extends ConsumerStatefulWidget {
  final VoidCallback? onClose;

  const ARVoiceCommandUI({
    super.key,
    this.onClose,
  });

  @override
  ConsumerState<ARVoiceCommandUI> createState() => _ARVoiceCommandUIState();
}

class _ARVoiceCommandUIState extends ConsumerState<ARVoiceCommandUI>
    with SingleTickerProviderStateMixin {
  late ARVoiceCommandService _voiceCommandService;
  late AnimationController _animationController;
  late Animation<double> _pulseAnimation;

  bool _isListening = false;
  String _recognizedText = '';
  String _lastCommand = '';
  bool _showCommandList = false;

  @override
  void initState() {
    super.initState();

    // Get the voice command service
    _voiceCommandService = ref.read(arVoiceCommandServiceProvider);

    // Initialize animations
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1500),
    );

    _pulseAnimation = Tween<double>(begin: 1.0, end: 1.3).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeInOut,
      ),
    );

    _animationController.repeat(reverse: true);

    // Add listeners
    _voiceCommandService.addRecognitionListener(_onRecognitionResult);
    _voiceCommandService.addListeningStateListener(_onListeningStateChanged);
    _voiceCommandService.addCommandExecutedListener(_onCommandExecuted);

    // Initialize the voice command service
    _initializeVoiceCommands();
  }

  Future<void> _initializeVoiceCommands() async {
    final initialized = await _voiceCommandService.initialize();
    if (!initialized) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Failed to initialize voice commands'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _onRecognitionResult(String text) {
    if (mounted) {
      setState(() {
        _recognizedText = text;
      });
    }
  }

  void _onListeningStateChanged(bool isListening) {
    if (mounted) {
      setState(() {
        _isListening = isListening;
      });
    }
  }

  void _onCommandExecuted(String command) {
    if (mounted) {
      setState(() {
        _lastCommand = command;
      });
    }
  }

  void _toggleListening() async {
    if (_isListening) {
      await _voiceCommandService.stopListening();
    } else {
      final success = await _voiceCommandService.startListening();
      if (!success && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Failed to start voice recognition'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _toggleCommandList() {
    setState(() {
      _showCommandList = !_showCommandList;
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    _voiceCommandService.removeRecognitionListener(_onRecognitionResult);
    _voiceCommandService.removeListeningStateListener(_onListeningStateChanged);
    _voiceCommandService.removeCommandExecutedListener(_onCommandExecuted);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.all(16),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      elevation: 4,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 300),
        width: double.infinity,
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: _isListening ? Colors.blue.withAlpha(26) : Colors.white,
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: _isListening ? Colors.blue : Colors.grey.withAlpha(77),
            width: _isListening ? 2 : 1,
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Voice Commands',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
                Row(
                  children: [
                    IconButton(
                      icon: Icon(
                        _showCommandList ? Icons.list : Icons.help_outline,
                        color: Colors.grey[700],
                      ),
                      onPressed: _toggleCommandList,
                      tooltip: _showCommandList
                          ? 'Hide commands'
                          : 'Show available commands',
                    ),
                    if (widget.onClose != null)
                      IconButton(
                        icon: Icon(
                          Icons.close,
                          color: Colors.grey[700],
                        ),
                        onPressed: widget.onClose,
                        tooltip: 'Close',
                      ),
                  ],
                ),
              ],
            ),
            const Divider(),
            const SizedBox(height: 8),

            // Microphone button
            GestureDetector(
              onTap: _toggleListening,
              child: AnimatedBuilder(
                animation: _animationController,
                builder: (context, child) {
                  return Transform.scale(
                    scale: _isListening ? _pulseAnimation.value : 1.0,
                    child: Container(
                      width: 80,
                      height: 80,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: _isListening ? Colors.blue : Colors.grey[300],
                        boxShadow: [
                          BoxShadow(
                            color: _isListening
                                ? Colors.blue.withAlpha(77)
                                : Colors.black.withAlpha(26),
                            blurRadius: 10,
                            spreadRadius: 2,
                          ),
                        ],
                      ),
                      child: const Icon(
                        Icons.mic,
                        color: Colors.white,
                        size: 40,
                      ),
                    ),
                  );
                },
              ),
            ),
            const SizedBox(height: 16),

            // Status text
            Text(
              _isListening ? 'Listening...' : 'Tap to speak',
              style: TextStyle(
                color: _isListening ? Colors.blue : Colors.grey[700],
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),

            // Recognized text
            if (_recognizedText.isNotEmpty)
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  _recognizedText,
                  style: const TextStyle(
                    fontStyle: FontStyle.italic,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),

            // Last command
            if (_lastCommand.isNotEmpty)
              Padding(
                padding: const EdgeInsets.only(top: 8),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Text(
                      'Last command: ',
                      style: TextStyle(
                        color: Colors.grey,
                      ),
                    ),
                    Text(
                      _lastCommand,
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        color: Colors.green,
                      ),
                    ),
                  ],
                ),
              ),

            // Command list
            if (_showCommandList)
              AnimatedContainer(
                duration: const Duration(milliseconds: 300),
                margin: const EdgeInsets.only(top: 16),
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Available Commands:',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Wrap(
                      spacing: 8,
                      runSpacing: 8,
                      children: ARVoiceCommandService.availableCommands
                          .map((command) {
                        return Chip(
                          label: Text(command),
                          backgroundColor: Colors.white,
                          side: BorderSide(color: Colors.grey[300]!),
                          labelStyle: TextStyle(
                            color: Colors.grey[800],
                            fontSize: 12,
                          ),
                        );
                      }).toList(),
                    ),
                  ],
                ),
              ),
          ],
        ),
      ),
    );
  }
}
