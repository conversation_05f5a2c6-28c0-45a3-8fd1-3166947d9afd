import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';
import 'package:culture_connect/models/travel/car_rental_filter.dart';
import 'package:culture_connect/models/travel/travel.dart';
import 'package:culture_connect/providers/travel/car_rental_filter_provider.dart';

/// A dialog for filtering car rentals
class CarRentalFilterDialog extends ConsumerStatefulWidget {
  /// Creates a new car rental filter dialog
  const CarRentalFilterDialog({super.key});

  @override
  ConsumerState<CarRentalFilterDialog> createState() =>
      _CarRentalFilterDialogState();
}

class _CarRentalFilterDialogState extends ConsumerState<CarRentalFilterDialog> {
  late CarRentalFilter _filter;
  final double _maxPrice = 500.0;

  @override
  void initState() {
    super.initState();
    _filter = ref.read(carRentalFilterProvider);
  }

  void _applyFilters() {
    ref.read(carRentalFilterProvider.notifier).state = _filter;
    Navigator.of(context).pop();
  }

  void _resetFilters() {
    setState(() {
      _filter = _filter.reset();
    });
  }

  Future<void> _selectPickupDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate:
          _filter.pickupDate ?? DateTime.now().add(const Duration(days: 1)),
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );

    if (picked != null) {
      setState(() {
        _filter = _filter.copyWith(pickupDate: picked);

        // If dropoff date is before pickup date, update it
        if (_filter.dropoffDate != null &&
            _filter.dropoffDate!.isBefore(picked)) {
          _filter = _filter.copyWith(
              dropoffDate: picked.add(const Duration(days: 1)));
        }
      });
    }
  }

  Future<void> _selectDropoffDate() async {
    if (_filter.pickupDate == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please select a pickup date first'),
        ),
      );
      return;
    }

    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _filter.dropoffDate ??
          _filter.pickupDate!.add(const Duration(days: 1)),
      firstDate: _filter.pickupDate!,
      lastDate: _filter.pickupDate!.add(const Duration(days: 30)),
    );

    if (picked != null) {
      setState(() {
        _filter = _filter.copyWith(dropoffDate: picked);
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16.r),
      ),
      child: Container(
        width: double.maxFinite,
        constraints: BoxConstraints(
          maxHeight: MediaQuery.of(context).size.height * 0.8,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header
            Padding(
              padding: EdgeInsets.all(16.r),
              child: Row(
                children: [
                  Expanded(
                    child: Text(
                      'Filter Car Rentals',
                      style: theme.textTheme.titleLarge,
                    ),
                  ),
                  TextButton(
                    onPressed: _resetFilters,
                    child: const Text('Reset'),
                  ),
                  IconButton(
                    icon: const Icon(Icons.close),
                    onPressed: () {
                      Navigator.of(context).pop();
                    },
                  ),
                ],
              ),
            ),

            const Divider(),

            // Filter options
            Expanded(
              child: ListView(
                padding: EdgeInsets.all(16.r),
                children: [
                  // Car type filter
                  Text(
                    'Car Type',
                    style: theme.textTheme.titleMedium,
                  ),
                  SizedBox(height: 8.h),
                  Wrap(
                    spacing: 8.w,
                    runSpacing: 8.h,
                    children: CarType.values.map((type) {
                      final isSelected =
                          _filter.carTypes?.contains(type) ?? false;
                      return FilterChip(
                        label: Text(type.displayName),
                        selected: isSelected,
                        onSelected: (selected) {
                          setState(() {
                            final carTypes = _filter.carTypes?.toList() ?? [];
                            if (selected) {
                              carTypes.add(type);
                            } else {
                              carTypes.remove(type);
                            }
                            _filter = _filter.copyWith(carTypes: carTypes);
                          });
                        },
                      );
                    }).toList(),
                  ),
                  SizedBox(height: 16.h),

                  // Price range filter
                  Text(
                    'Price Range (per day)',
                    style: theme.textTheme.titleMedium,
                  ),
                  SizedBox(height: 8.h),
                  RangeSlider(
                    values: _filter.priceRange ?? const RangeValues(0, 500),
                    min: 0,
                    max: _maxPrice,
                    divisions: 50,
                    labels: RangeLabels(
                      '\$${(_filter.priceRange?.start ?? 0).round()}',
                      '\$${(_filter.priceRange?.end ?? _maxPrice).round()}',
                    ),
                    onChanged: (values) {
                      setState(() {
                        _filter = _filter.copyWith(priceRange: values);
                      });
                    },
                  ),
                  Padding(
                    padding: EdgeInsets.symmetric(horizontal: 16.w),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        const Text('\$0'),
                        Text('\$${_maxPrice.round()}'),
                      ],
                    ),
                  ),
                  SizedBox(height: 16.h),

                  // Rating filter
                  Text(
                    'Minimum Rating',
                    style: theme.textTheme.titleMedium,
                  ),
                  SizedBox(height: 8.h),
                  Slider(
                    value: _filter.minRating ?? 0,
                    min: 0,
                    max: 5,
                    divisions: 10,
                    label: (_filter.minRating ?? 0).toStringAsFixed(1),
                    onChanged: (value) {
                      setState(() {
                        _filter = _filter.copyWith(minRating: value);
                      });
                    },
                  ),
                  Padding(
                    padding: EdgeInsets.symmetric(horizontal: 16.w),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        const Text('Any'),
                        Row(
                          children: [
                            const Text('5.0'),
                            SizedBox(width: 4.w),
                            const Icon(Icons.star,
                                size: 16, color: Colors.amber),
                          ],
                        ),
                      ],
                    ),
                  ),
                  SizedBox(height: 16.h),

                  // Date filter
                  Text(
                    'Rental Period',
                    style: theme.textTheme.titleMedium,
                  ),
                  SizedBox(height: 8.h),
                  Row(
                    children: [
                      Expanded(
                        child: InkWell(
                          onTap: _selectPickupDate,
                          child: InputDecorator(
                            decoration: InputDecoration(
                              labelText: 'Pickup Date',
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8.r),
                              ),
                              contentPadding: EdgeInsets.all(12.r),
                            ),
                            child: Text(
                              _filter.pickupDate != null
                                  ? DateFormat('MMM dd, yyyy')
                                      .format(_filter.pickupDate!)
                                  : 'Select Date',
                            ),
                          ),
                        ),
                      ),
                      SizedBox(width: 16.w),
                      Expanded(
                        child: InkWell(
                          onTap: _selectDropoffDate,
                          child: InputDecorator(
                            decoration: InputDecoration(
                              labelText: 'Dropoff Date',
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8.r),
                              ),
                              contentPadding: EdgeInsets.all(12.r),
                            ),
                            child: Text(
                              _filter.dropoffDate != null
                                  ? DateFormat('MMM dd, yyyy')
                                      .format(_filter.dropoffDate!)
                                  : 'Select Date',
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 16.h),

                  // Transmission filter
                  Text(
                    'Transmission',
                    style: theme.textTheme.titleMedium,
                  ),
                  SizedBox(height: 8.h),
                  Wrap(
                    spacing: 8.w,
                    runSpacing: 8.h,
                    children: TransmissionType.values.map((type) {
                      final isSelected =
                          _filter.transmissionTypes?.contains(type) ?? false;
                      return FilterChip(
                        label: Text(type.displayName),
                        selected: isSelected,
                        onSelected: (selected) {
                          setState(() {
                            final transmissionTypes =
                                _filter.transmissionTypes?.toList() ?? [];
                            if (selected) {
                              transmissionTypes.add(type);
                            } else {
                              transmissionTypes.remove(type);
                            }
                            _filter = _filter.copyWith(
                                transmissionTypes: transmissionTypes);
                          });
                        },
                      );
                    }).toList(),
                  ),
                  SizedBox(height: 16.h),

                  // Fuel type filter
                  Text(
                    'Fuel Type',
                    style: theme.textTheme.titleMedium,
                  ),
                  SizedBox(height: 8.h),
                  Wrap(
                    spacing: 8.w,
                    runSpacing: 8.h,
                    children: FuelType.values.map((type) {
                      final isSelected =
                          _filter.fuelTypes?.contains(type) ?? false;
                      return FilterChip(
                        label: Text(type.displayName),
                        selected: isSelected,
                        onSelected: (selected) {
                          setState(() {
                            final fuelTypes = _filter.fuelTypes?.toList() ?? [];
                            if (selected) {
                              fuelTypes.add(type);
                            } else {
                              fuelTypes.remove(type);
                            }
                            _filter = _filter.copyWith(fuelTypes: fuelTypes);
                          });
                        },
                      );
                    }).toList(),
                  ),
                  SizedBox(height: 16.h),

                  // Minimum seats filter
                  Text(
                    'Minimum Seats',
                    style: theme.textTheme.titleMedium,
                  ),
                  SizedBox(height: 8.h),
                  Slider(
                    value: (_filter.minSeats ?? 2).toDouble(),
                    min: 2,
                    max: 9,
                    divisions: 7,
                    label: '${_filter.minSeats ?? 2}',
                    onChanged: (value) {
                      setState(() {
                        _filter = _filter.copyWith(minSeats: value.round());
                      });
                    },
                  ),
                  Padding(
                    padding: EdgeInsets.symmetric(horizontal: 16.w),
                    child: const Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text('2 seats'),
                        Text('9 seats'),
                      ],
                    ),
                  ),
                  SizedBox(height: 16.h),

                  // Features filter
                  Text(
                    'Features',
                    style: theme.textTheme.titleMedium,
                  ),
                  SizedBox(height: 8.h),
                  _buildFeatureCheckbox('Air Conditioning', 'airConditioning'),
                  _buildFeatureCheckbox('GPS Navigation', 'gps'),
                  _buildFeatureCheckbox('Bluetooth', 'bluetooth'),
                  _buildFeatureCheckbox('USB Ports', 'usb'),
                  _buildFeatureCheckbox('Sunroof', 'sunroof'),
                ],
              ),
            ),

            const Divider(),

            // Footer
            Padding(
              padding: EdgeInsets.all(16.r),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  OutlinedButton(
                    onPressed: () {
                      Navigator.of(context).pop();
                    },
                    child: const Text('Cancel'),
                  ),
                  SizedBox(width: 16.w),
                  ElevatedButton(
                    onPressed: _applyFilters,
                    child: const Text('Apply Filters'),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFeatureCheckbox(String label, String feature) {
    final features = _filter.features ?? {};
    final isChecked = features[feature] ?? false;

    return CheckboxListTile(
      title: Text(label),
      value: isChecked,
      onChanged: (value) {
        setState(() {
          final newFeatures = Map<String, bool>.from(features);
          newFeatures[feature] = value ?? false;
          _filter = _filter.copyWith(features: newFeatures);
        });
      },
      controlAffinity: ListTileControlAffinity.leading,
      dense: true,
    );
  }
}
