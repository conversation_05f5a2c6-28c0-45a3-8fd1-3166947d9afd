import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:culture_connect/models/travel/price_alert.dart';
import 'package:culture_connect/theme/app_colors.dart';
import 'package:culture_connect/theme/app_text_styles.dart';
import 'package:culture_connect/widgets/common/animated_price_tag.dart';
import 'package:culture_connect/widgets/common/price_history_chart.dart';

/// A widget that displays a price alert card
class PriceAlertCard extends ConsumerWidget {
  /// The price alert to display
  final PriceAlert priceAlert;

  /// Callback when the alert is deleted
  final VoidCallback? onDelete;

  /// Callback when the alert is edited
  final VoidCallback? onEdit;

  /// Callback when the alert is tapped
  final VoidCallback? onTap;

  /// Creates a new price alert card
  const PriceAlertCard({
    super.key,
    required this.priceAlert,
    this.onDelete,
    this.onEdit,
    this.onTap,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final dateFormat = DateFormat('MMM d, yyyy');

    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(
          color: _getStatusColor(theme),
          width: 1.5,
        ),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  _buildStatusBadge(theme),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      priceAlert.travelServiceName,
                      style: AppTextStyles.subtitle1.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  _buildPopupMenu(context),
                ],
              ),
              const SizedBox(height: 16),
              Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Current Price',
                          style: AppTextStyles.caption,
                        ),
                        const SizedBox(height: 4),
                        AnimatedPriceTag(
                          price: priceAlert.currentPrice,
                          currency: priceAlert.currency,
                          style: AppTextStyles.subtitle1.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                  ),
                  Container(
                    width: 1,
                    height: 40,
                    color: theme.dividerColor,
                  ),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Target Price',
                          style: AppTextStyles.caption,
                        ),
                        const SizedBox(height: 4),
                        Row(
                          children: [
                            Text(
                              priceAlert.formattedTargetPrice,
                              style: AppTextStyles.subtitle1.copyWith(
                                fontWeight: FontWeight.bold,
                                color: AppColors.success,
                              ),
                            ),
                            const SizedBox(width: 4),
                            Text(
                              priceAlert.formattedPriceDifferencePercentage,
                              style: AppTextStyles.caption.copyWith(
                                color: AppColors.success,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              if (priceAlert.priceHistory.length > 1) ...[
                const SizedBox(height: 16),
                SizedBox(
                  height: 100,
                  child: PriceHistoryChart(
                    priceHistory: priceAlert.priceHistory,
                    targetPrice: priceAlert.targetPrice,
                    currency: priceAlert.currency,
                  ),
                ),
              ],
              const SizedBox(height: 16),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Alert: ${priceAlert.frequency.displayName}',
                    style: AppTextStyles.caption,
                  ),
                  Text(
                    'Expires: ${dateFormat.format(priceAlert.endDate)}',
                    style: AppTextStyles.caption,
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Build the status badge
  Widget _buildStatusBadge(ThemeData theme) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: _getStatusColor(theme).withAlpha(25),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: _getStatusColor(theme),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            priceAlert.status.icon,
            size: 14,
            color: _getStatusColor(theme),
          ),
          const SizedBox(width: 4),
          Text(
            priceAlert.status.displayName,
            style: AppTextStyles.caption.copyWith(
              color: _getStatusColor(theme),
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  /// Build the popup menu
  Widget _buildPopupMenu(BuildContext context) {
    return PopupMenuButton<String>(
      icon: const Icon(Icons.more_vert),
      onSelected: (value) {
        switch (value) {
          case 'edit':
            onEdit?.call();
            break;
          case 'delete':
            _confirmDelete(context);
            break;
        }
      },
      itemBuilder: (context) => [
        const PopupMenuItem<String>(
          value: 'edit',
          child: Row(
            children: [
              Icon(Icons.edit),
              SizedBox(width: 8),
              Text('Edit Alert'),
            ],
          ),
        ),
        const PopupMenuItem<String>(
          value: 'delete',
          child: Row(
            children: [
              Icon(Icons.delete, color: Colors.red),
              SizedBox(width: 8),
              Text('Delete Alert', style: TextStyle(color: Colors.red)),
            ],
          ),
        ),
      ],
    );
  }

  /// Confirm delete dialog
  Future<void> _confirmDelete(BuildContext context) async {
    final result = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Price Alert'),
        content: Text(
          'Are you sure you want to delete the price alert for ${priceAlert.travelServiceName}?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('CANCEL'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('DELETE'),
          ),
        ],
      ),
    );

    if (result == true) {
      onDelete?.call();
    }
  }

  /// Get the color for the current status
  Color _getStatusColor(ThemeData theme) {
    return priceAlert.status.color;
  }
}
