import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:shimmer/shimmer.dart';

/// A widget for displaying a skeleton loading animation for car rentals
class CarRentalSkeletonLoading extends StatelessWidget {
  /// Whether to display in grid mode
  final bool isGrid;
  
  /// The number of items to display
  final int itemCount;
  
  /// Creates a new car rental skeleton loading
  const CarRentalSkeletonLoading({
    super.key,
    this.isGrid = false,
    this.itemCount = 6,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Shimmer.fromColors(
      baseColor: theme.colorScheme.surfaceContainerHighest,
      highlightColor: theme.colorScheme.surface,
      child: isGrid
          ? _buildGridSkeleton(context)
          : _buildListSkeleton(context),
    );
  }
  
  Widget _buildGridSkeleton(BuildContext context) {
    return GridView.builder(
      padding: EdgeInsets.all(16.r),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        childAspectRatio: 0.75,
        crossAxisSpacing: 16.w,
        mainAxisSpacing: 16.h,
      ),
      itemCount: itemCount,
      itemBuilder: (context, index) {
        return _buildGridItem(context);
      },
    );
  }
  
  Widget _buildListSkeleton(BuildContext context) {
    return ListView.builder(
      padding: EdgeInsets.all(16.r),
      itemCount: itemCount,
      itemBuilder: (context, index) {
        return Padding(
          padding: EdgeInsets.only(bottom: 16.h),
          child: _buildListItem(context),
        );
      },
    );
  }
  
  Widget _buildGridItem(BuildContext context) {
    return Card(
      clipBehavior: Clip.antiAlias,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Image placeholder
          AspectRatio(
            aspectRatio: 16 / 9,
            child: Container(
              color: Colors.white,
            ),
          ),
          
          // Content placeholders
          Padding(
            padding: EdgeInsets.all(12.r),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Title placeholder
                Container(
                  width: double.infinity,
                  height: 16.h,
                  color: Colors.white,
                ),
                SizedBox(height: 8.h),
                
                // Subtitle placeholder
                Container(
                  width: 120.w,
                  height: 12.h,
                  color: Colors.white,
                ),
                SizedBox(height: 12.h),
                
                // Features placeholder
                Row(
                  children: [
                    Expanded(
                      child: Container(
                        height: 10.h,
                        color: Colors.white,
                      ),
                    ),
                    SizedBox(width: 8.w),
                    Expanded(
                      child: Container(
                        height: 10.h,
                        color: Colors.white,
                      ),
                    ),
                    SizedBox(width: 8.w),
                    Expanded(
                      child: Container(
                        height: 10.h,
                        color: Colors.white,
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 12.h),
                
                // Rating and price placeholder
                Row(
                  children: [
                    Container(
                      width: 80.w,
                      height: 12.h,
                      color: Colors.white,
                    ),
                    const Spacer(),
                    Container(
                      width: 60.w,
                      height: 16.h,
                      color: Colors.white,
                    ),
                  ],
                ),
                SizedBox(height: 8.h),
                
                // Per day label placeholder
                Align(
                  alignment: Alignment.centerRight,
                  child: Container(
                    width: 40.w,
                    height: 8.h,
                    color: Colors.white,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
  
  Widget _buildListItem(BuildContext context) {
    return Card(
      clipBehavior: Clip.antiAlias,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: SizedBox(
        height: 120.h,
        child: Row(
          children: [
            // Image placeholder
            AspectRatio(
              aspectRatio: 1,
              child: Container(
                color: Colors.white,
              ),
            ),
            
            // Content placeholders
            Expanded(
              child: Padding(
                padding: EdgeInsets.all(12.r),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Title placeholder
                    Container(
                      width: double.infinity,
                      height: 16.h,
                      color: Colors.white,
                    ),
                    SizedBox(height: 8.h),
                    
                    // Subtitle placeholder
                    Container(
                      width: 120.w,
                      height: 12.h,
                      color: Colors.white,
                    ),
                    SizedBox(height: 12.h),
                    
                    // Features placeholder
                    Row(
                      children: [
                        Expanded(
                          child: Container(
                            height: 10.h,
                            color: Colors.white,
                          ),
                        ),
                        SizedBox(width: 8.w),
                        Expanded(
                          child: Container(
                            height: 10.h,
                            color: Colors.white,
                          ),
                        ),
                      ],
                    ),
                    const Spacer(),
                    
                    // Price placeholder
                    Row(
                      children: [
                        Container(
                          width: 80.w,
                          height: 12.h,
                          color: Colors.white,
                        ),
                        const Spacer(),
                        Container(
                          width: 60.w,
                          height: 16.h,
                          color: Colors.white,
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
