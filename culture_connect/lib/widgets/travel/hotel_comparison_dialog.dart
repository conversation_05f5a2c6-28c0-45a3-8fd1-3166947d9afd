import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:culture_connect/models/travel/travel.dart';
import 'package:culture_connect/providers/travel/travel_services_provider.dart';

/// A dialog for comparing hotels
class HotelComparisonDialog extends ConsumerStatefulWidget {
  /// The hotel to compare
  final Hotel hotel;
  
  /// Callback when a hotel is selected
  final Function(Hotel) onHotelSelected;
  
  /// Creates a new hotel comparison dialog
  const HotelComparisonDialog({
    super.key,
    required this.hotel,
    required this.onHotelSelected,
  });

  @override
  ConsumerState<HotelComparisonDialog> createState() => _HotelComparisonDialogState();
}

class _HotelComparisonDialogState extends ConsumerState<HotelComparisonDialog> with SingleTickerProviderStateMixin {
  late TabController _tabController;
  Hotel? _selectedHotel;
  List<Hotel> _similarHotels = [];
  bool _isLoading = true;
  
  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _loadSimilarHotels();
  }
  
  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }
  
  Future<void> _loadSimilarHotels() async {
    setState(() {
      _isLoading = true;
    });
    
    try {
      // Get all hotels
      final hotelsAsyncValue = await ref.read(hotelsProvider.future);
      
      // Filter similar hotels (same star rating, similar price range)
      final similarHotels = hotelsAsyncValue.where((hotel) {
        return hotel.id != widget.hotel.id &&
            hotel.starRating == widget.hotel.starRating &&
            (hotel.price >= widget.hotel.price * 0.8 &&
                hotel.price <= widget.hotel.price * 1.2);
      }).toList();
      
      // Sort by rating
      similarHotels.sort((a, b) => b.rating.compareTo(a.rating));
      
      // Take top 5
      final topSimilarHotels = similarHotels.take(5).toList();
      
      setState(() {
        _similarHotels = topSimilarHotels;
        _selectedHotel = topSimilarHotels.isNotEmpty ? topSimilarHotels.first : null;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      debugPrint('Error loading similar hotels: $e');
    }
  }
  
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16.r),
      ),
      child: Container(
        width: double.infinity,
        constraints: BoxConstraints(
          maxWidth: 800.w,
          maxHeight: 600.h,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header
            Padding(
              padding: EdgeInsets.all(16.r),
              child: Row(
                children: [
                  Expanded(
                    child: Text(
                      'Compare Hotels',
                      style: theme.textTheme.titleLarge,
                    ),
                  ),
                  IconButton(
                    icon: const Icon(Icons.close),
                    onPressed: () {
                      Navigator.of(context).pop();
                    },
                  ),
                ],
              ),
            ),
            
            // Tab bar
            TabBar(
              controller: _tabController,
              tabs: const [
                Tab(text: 'Side by Side'),
                Tab(text: 'Feature Comparison'),
              ],
              labelColor: theme.colorScheme.primary,
              unselectedLabelColor: theme.colorScheme.onSurface,
              indicatorColor: theme.colorScheme.primary,
            ),
            
            // Tab content
            Expanded(
              child: _isLoading
                  ? const Center(child: CircularProgressIndicator())
                  : _similarHotels.isEmpty
                      ? Center(
                          child: Padding(
                            padding: EdgeInsets.all(16.r),
                            child: Text(
                              'No similar hotels found to compare',
                              style: theme.textTheme.titleMedium,
                            ),
                          ),
                        )
                      : TabBarView(
                          controller: _tabController,
                          children: [
                            _buildSideBySideComparison(),
                            _buildFeatureComparison(),
                          ],
                        ),
            ),
            
            // Footer
            Padding(
              padding: EdgeInsets.all(16.r),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  OutlinedButton(
                    onPressed: () {
                      Navigator.of(context).pop();
                    },
                    child: const Text('Cancel'),
                  ),
                  if (_selectedHotel != null)
                    ElevatedButton(
                      onPressed: () {
                        widget.onHotelSelected(_selectedHotel!);
                        Navigator.of(context).pop();
                      },
                      child: const Text('Select Hotel'),
                    ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
  
  Widget _buildSideBySideComparison() {
    return Padding(
      padding: EdgeInsets.all(16.r),
      child: Column(
        children: [
          // Hotel selection dropdown
          DropdownButtonFormField<String>(
            decoration: InputDecoration(
              labelText: 'Compare with',
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8.r),
              ),
            ),
            value: _selectedHotel?.id,
            items: _similarHotels.map((hotel) {
              return DropdownMenuItem<String>(
                value: hotel.id,
                child: Text('${hotel.name} - ${hotel.formattedPrice}/night'),
              );
            }).toList(),
            onChanged: (value) {
              if (value != null) {
                setState(() {
                  _selectedHotel = _similarHotels.firstWhere((hotel) => hotel.id == value);
                });
              }
            },
          ),
          SizedBox(height: 16.h),
          
          // Hotel comparison
          Expanded(
            child: _selectedHotel == null
                ? Center(
                    child: Text(
                      'Select a hotel to compare',
                      style: Theme.of(context).textTheme.bodyLarge,
                    ),
                  )
                : Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Expanded(
                        child: _buildHotelCard(widget.hotel, isSelected: true),
                      ),
                      SizedBox(width: 16.w),
                      Expanded(
                        child: _buildHotelCard(_selectedHotel!, isSelected: false),
                      ),
                    ],
                  ),
          ),
        ],
      ),
    );
  }
  
  Widget _buildFeatureComparison() {
    final theme = Theme.of(context);
    
    // Define the features to compare
    final features = [
      'Name',
      'Star Rating',
      'Price per Night',
      'Rating',
      'Location',
      'Distance from City Center',
      'Distance from Airport',
      'Check-in Time',
      'Check-out Time',
      'Restaurant',
      'Bar',
      'Pool',
      'Spa',
      'Gym',
      'Free WiFi',
      'Free Parking',
      'Room Service',
      'Business Center',
      'Kids Club',
      'Concierge Service',
      'Laundry Service',
      'Shuttle Service',
      '24-Hour Front Desk',
    ];
    
    return Padding(
      padding: EdgeInsets.all(16.r),
      child: Column(
        children: [
          // Hotel selection dropdown
          DropdownButtonFormField<String>(
            decoration: InputDecoration(
              labelText: 'Compare with',
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8.r),
              ),
            ),
            value: _selectedHotel?.id,
            items: _similarHotels.map((hotel) {
              return DropdownMenuItem<String>(
                value: hotel.id,
                child: Text('${hotel.name} - ${hotel.formattedPrice}/night'),
              );
            }).toList(),
            onChanged: (value) {
              if (value != null) {
                setState(() {
                  _selectedHotel = _similarHotels.firstWhere((hotel) => hotel.id == value);
                });
              }
            },
          ),
          SizedBox(height: 16.h),
          
          // Feature comparison table
          Expanded(
            child: _selectedHotel == null
                ? Center(
                    child: Text(
                      'Select a hotel to compare',
                      style: theme.textTheme.bodyLarge,
                    ),
                  )
                : SingleChildScrollView(
                    child: Table(
                      border: TableBorder.all(
                        color: theme.colorScheme.outlineVariant,
                        width: 1,
                      ),
                      columnWidths: const {
                        0: FlexColumnWidth(2),
                        1: FlexColumnWidth(3),
                        2: FlexColumnWidth(3),
                      },
                      children: [
                        // Header row
                        TableRow(
                          decoration: BoxDecoration(
                            color: theme.colorScheme.surfaceContainerHighest,
                          ),
                          children: [
                            _buildTableCell('Feature', isHeader: true),
                            _buildTableCell(widget.hotel.name, isHeader: true),
                            _buildTableCell(_selectedHotel!.name, isHeader: true),
                          ],
                        ),
                        
                        // Feature rows
                        for (var feature in features)
                          TableRow(
                            children: [
                              _buildTableCell(feature),
                              _buildTableCell(_getHotelFeatureValue(widget.hotel, feature)),
                              _buildTableCell(_getHotelFeatureValue(_selectedHotel!, feature)),
                            ],
                          ),
                      ],
                    ),
                  ),
          ),
        ],
      ),
    );
  }
  
  Widget _buildHotelCard(Hotel hotel, {required bool isSelected}) {
    final theme = Theme.of(context);
    
    return Card(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8.r),
        side: BorderSide(
          color: isSelected ? theme.colorScheme.primary : Colors.transparent,
          width: 2.w,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Hotel image
          ClipRRect(
            borderRadius: BorderRadius.vertical(top: Radius.circular(8.r)),
            child: Image.network(
              hotel.imageUrl,
              height: 150.h,
              width: double.infinity,
              fit: BoxFit.cover,
              errorBuilder: (context, error, stackTrace) {
                return Container(
                  height: 150.h,
                  color: theme.colorScheme.surfaceContainerHighest,
                  child: Center(
                    child: Icon(
                      Icons.image_not_supported,
                      color: theme.colorScheme.onSurfaceVariant,
                    ),
                  ),
                );
              },
            ),
          ),
          
          // Hotel details
          Padding(
            padding: EdgeInsets.all(16.r),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  hotel.name,
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(height: 4.h),
                Row(
                  children: [
                    ...List.generate(
                      _getStarRatingValue(hotel.starRating),
                      (index) => Icon(
                        Icons.star,
                        size: 16.r,
                        color: Colors.amber,
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 8.h),
                Row(
                  children: [
                    Icon(
                      Icons.star,
                      size: 16.r,
                      color: Colors.amber,
                    ),
                    SizedBox(width: 4.w),
                    Text(
                      '${hotel.rating.toStringAsFixed(1)} (${hotel.reviewCount})',
                      style: theme.textTheme.bodyMedium,
                    ),
                  ],
                ),
                SizedBox(height: 8.h),
                Text(
                  '${hotel.formattedPrice} / night',
                  style: theme.textTheme.titleMedium?.copyWith(
                    color: theme.colorScheme.primary,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(height: 16.h),
                _buildFeatureRow(Icons.location_on, 'Location', hotel.location),
                _buildFeatureRow(Icons.access_time, 'Check-in', hotel.formattedCheckInTime),
                _buildFeatureRow(Icons.access_time, 'Check-out', hotel.formattedCheckOutTime),
                SizedBox(height: 16.h),
                if (!isSelected)
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: () {
                        widget.onHotelSelected(hotel);
                        Navigator.of(context).pop();
                      },
                      child: const Text('Select'),
                    ),
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }
  
  Widget _buildFeatureRow(IconData icon, String label, String value) {
    final theme = Theme.of(context);
    
    return Padding(
      padding: EdgeInsets.only(bottom: 8.h),
      child: Row(
        children: [
          Icon(
            icon,
            size: 16.r,
            color: theme.colorScheme.primary,
          ),
          SizedBox(width: 8.w),
          Text(
            '$label: ',
            style: theme.textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: theme.textTheme.bodyMedium,
            ),
          ),
        ],
      ),
    );
  }
  
  Widget _buildTableCell(String text, {bool isHeader = false}) {
    final theme = Theme.of(context);
    
    return TableCell(
      verticalAlignment: TableCellVerticalAlignment.middle,
      child: Padding(
        padding: EdgeInsets.all(8.r),
        child: Text(
          text,
          style: isHeader
              ? theme.textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                )
              : theme.textTheme.bodyMedium,
          textAlign: isHeader ? TextAlign.center : TextAlign.left,
        ),
      ),
    );
  }
  
  String _getHotelFeatureValue(Hotel hotel, String feature) {
    switch (feature) {
      case 'Name':
        return hotel.name;
      case 'Star Rating':
        return _getStarRatingText(hotel.starRating);
      case 'Price per Night':
        return hotel.formattedPrice;
      case 'Rating':
        return '${hotel.rating.toStringAsFixed(1)} (${hotel.reviewCount})';
      case 'Location':
        return hotel.location;
      case 'Distance from City Center':
        return hotel.formattedDistanceFromCityCenter;
      case 'Distance from Airport':
        return hotel.formattedDistanceFromAirport;
      case 'Check-in Time':
        return hotel.formattedCheckInTime;
      case 'Check-out Time':
        return hotel.formattedCheckOutTime;
      case 'Restaurant':
        return hotel.hasRestaurant ? 'Yes' : 'No';
      case 'Bar':
        return hotel.hasBar ? 'Yes' : 'No';
      case 'Pool':
        return hotel.hasPool ? 'Yes' : 'No';
      case 'Spa':
        return hotel.hasSpa ? 'Yes' : 'No';
      case 'Gym':
        return hotel.hasGym ? 'Yes' : 'No';
      case 'Free WiFi':
        return hotel.hasFreeWifi ? 'Yes' : 'No';
      case 'Free Parking':
        return hotel.hasFreeParking ? 'Yes' : 'No';
      case 'Room Service':
        return hotel.hasRoomService ? 'Yes' : 'No';
      case 'Business Center':
        return hotel.hasBusinessCenter ? 'Yes' : 'No';
      case 'Kids Club':
        return hotel.hasKidsClub ? 'Yes' : 'No';
      case 'Concierge Service':
        return hotel.hasConciergeService ? 'Yes' : 'No';
      case 'Laundry Service':
        return hotel.hasLaundryService ? 'Yes' : 'No';
      case 'Shuttle Service':
        return hotel.hasShuttleService ? 'Yes' : 'No';
      case '24-Hour Front Desk':
        return hotel.has24HrFrontDesk ? 'Yes' : 'No';
      default:
        return '-';
    }
  }
  
  String _getStarRatingText(HotelStarRating rating) {
    switch (rating) {
      case HotelStarRating.oneStar:
        return '1 Star';
      case HotelStarRating.twoStar:
        return '2 Stars';
      case HotelStarRating.threeStar:
        return '3 Stars';
      case HotelStarRating.fourStar:
        return '4 Stars';
      case HotelStarRating.fiveStar:
        return '5 Stars';
    }
  }
  
  int _getStarRatingValue(HotelStarRating rating) {
    switch (rating) {
      case HotelStarRating.oneStar:
        return 1;
      case HotelStarRating.twoStar:
        return 2;
      case HotelStarRating.threeStar:
        return 3;
      case HotelStarRating.fourStar:
        return 4;
      case HotelStarRating.fiveStar:
        return 5;
    }
  }
}
