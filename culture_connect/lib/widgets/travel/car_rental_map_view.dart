import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart' as gmaps;
import 'package:culture_connect/models/travel/car_rental.dart';
import 'package:culture_connect/models/location/geo_location.dart';

import 'package:culture_connect/widgets/common/loading_indicator.dart';
import 'package:culture_connect/widgets/common/error_display.dart';

/// A widget for displaying car rental locations on a map
class CarRentalMapView extends ConsumerStatefulWidget {
  /// The car rental to display
  final CarRental carRental;

  /// Whether to show the route between pickup and dropoff locations
  final bool showRoute;

  /// Creates a new car rental map view
  const CarRentalMapView({
    super.key,
    required this.carRental,
    this.showRoute = true,
  });

  @override
  ConsumerState<CarRentalMapView> createState() => _CarRentalMapViewState();
}

class _CarRentalMapViewState extends ConsumerState<CarRentalMapView> {
  gmaps.GoogleMapController? _mapController;
  Set<gmaps.Marker> _markers = {};
  Set<gmaps.Polyline> _polylines = {};
  bool _isLoading = true;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _initializeMap();
  }

  @override
  void dispose() {
    _mapController?.dispose();
    super.dispose();
  }

  /// Converts a GeoLocation to a Google Maps LatLng
  gmaps.LatLng _geoLocationToLatLng(GeoLocation location) {
    return gmaps.LatLng(location.latitude, location.longitude);
  }

  Future<void> _initializeMap() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      // Convert GeoLocation to LatLng
      final pickupLatLng =
          _geoLocationToLatLng(widget.carRental.pickupCoordinates);
      final dropoffLatLng =
          _geoLocationToLatLng(widget.carRental.dropoffCoordinates);

      // Create markers for pickup and dropoff locations
      final pickupMarker = gmaps.Marker(
        markerId: const gmaps.MarkerId('pickup'),
        position: pickupLatLng,
        infoWindow: gmaps.InfoWindow(
          title: 'Pickup Location',
          snippet: widget.carRental.pickupLocation,
        ),
        icon: gmaps.BitmapDescriptor.defaultMarkerWithHue(
            gmaps.BitmapDescriptor.hueGreen),
      );

      final dropoffMarker = gmaps.Marker(
        markerId: const gmaps.MarkerId('dropoff'),
        position: dropoffLatLng,
        infoWindow: gmaps.InfoWindow(
          title: 'Dropoff Location',
          snippet: widget.carRental.dropoffLocation,
        ),
        icon: gmaps.BitmapDescriptor.defaultMarkerWithHue(
            gmaps.BitmapDescriptor.hueRed),
      );

      setState(() {
        _markers = {pickupMarker, dropoffMarker};
      });

      // If showing route, get the route between pickup and dropoff
      if (widget.showRoute) {
        await _getRoute(pickupLatLng, dropoffLatLng);
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Error initializing map: $e';
      });
      debugPrint('Error initializing map: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _getRoute(gmaps.LatLng pickup, gmaps.LatLng dropoff) async {
    try {
      // Create a simple route with 5 points
      final route = <gmaps.LatLng>[];

      // Add origin
      route.add(pickup);

      // Add 3 intermediate points
      for (int i = 1; i <= 3; i++) {
        final fraction = i / 4;
        final lat =
            pickup.latitude + (dropoff.latitude - pickup.latitude) * fraction;
        final lng = pickup.longitude +
            (dropoff.longitude - pickup.longitude) * fraction;
        route.add(gmaps.LatLng(lat, lng));
      }

      // Add destination
      route.add(dropoff);

      final polyline = gmaps.Polyline(
        polylineId: const gmaps.PolylineId('route'),
        points: route,
        color: Colors.blue,
        width: 5,
      );

      setState(() {
        _polylines = {polyline};
      });
    } catch (e) {
      debugPrint('Error getting route: $e');
    }
  }

  void _onMapCreated(gmaps.GoogleMapController controller) {
    _mapController = controller;

    // Fit bounds to include both markers
    _fitBounds();
  }

  void _fitBounds() {
    if (_mapController == null || _markers.isEmpty) return;

    // Calculate bounds
    final bounds = _calculateBounds();

    // Fit bounds with padding
    _mapController!.animateCamera(
      gmaps.CameraUpdate.newLatLngBounds(
        bounds,
        50.0, // padding
      ),
    );
  }

  gmaps.LatLngBounds _calculateBounds() {
    double minLat = 90.0;
    double maxLat = -90.0;
    double minLng = 180.0;
    double maxLng = -180.0;

    for (final marker in _markers) {
      final lat = marker.position.latitude;
      final lng = marker.position.longitude;

      minLat = lat < minLat ? lat : minLat;
      maxLat = lat > maxLat ? lat : maxLat;
      minLng = lng < minLng ? lng : minLng;
      maxLng = lng > maxLng ? lng : maxLng;
    }

    return gmaps.LatLngBounds(
      southwest: gmaps.LatLng(minLat, minLng),
      northeast: gmaps.LatLng(maxLat, maxLng),
    );
  }

  /// Calculate distance between two points in kilometers
  double _calculateDistance(GeoLocation start, GeoLocation end) {
    return start.distanceTo(end);
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return SizedBox(
        height: 200.h,
        child: const Center(
          child: LoadingIndicator(),
        ),
      );
    }

    if (_errorMessage != null) {
      return ErrorDisplay(
        message: 'Could not load map',
        details: _errorMessage,
        showRetry: true,
        onRetry: _initializeMap,
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          height: 200.h,
          child: ClipRRect(
            borderRadius: BorderRadius.circular(8.r),
            child: gmaps.GoogleMap(
              initialCameraPosition: gmaps.CameraPosition(
                target:
                    _geoLocationToLatLng(widget.carRental.pickupCoordinates),
                zoom: 12,
              ),
              markers: _markers,
              polylines: _polylines,
              onMapCreated: _onMapCreated,
              myLocationEnabled: false,
              myLocationButtonEnabled: false,
              zoomControlsEnabled: false,
              mapToolbarEnabled: false,
            ),
          ),
        ),
        SizedBox(height: 8.h),

        // Location details
        Card(
          child: Padding(
            padding: EdgeInsets.all(12.r),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildLocationRow(
                  Icons.location_on,
                  'Pickup',
                  widget.carRental.pickupLocation,
                  Colors.green,
                ),
                SizedBox(height: 12.h),
                _buildLocationRow(
                  Icons.location_on,
                  'Dropoff',
                  widget.carRental.dropoffLocation,
                  Colors.red,
                ),
                if (widget.showRoute) ...[
                  SizedBox(height: 12.h),
                  const Divider(),
                  SizedBox(height: 12.h),
                  _buildDistanceRow(),
                ],
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildLocationRow(
    IconData icon,
    String label,
    String location,
    Color color,
  ) {
    final theme = Theme.of(context);

    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Icon(
          icon,
          color: color,
          size: 24.r,
        ),
        SizedBox(width: 12.w),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: theme.textTheme.bodySmall?.copyWith(
                  color: theme.colorScheme.onSurfaceVariant,
                ),
              ),
              SizedBox(height: 2.h),
              Text(
                location,
                style: theme.textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildDistanceRow() {
    final theme = Theme.of(context);

    // Calculate distance between pickup and dropoff
    final distance = _calculateDistance(
      widget.carRental.pickupCoordinates,
      widget.carRental.dropoffCoordinates,
    );

    // Format distance
    final formattedDistance = distance < 1
        ? '${(distance * 1000).toStringAsFixed(0)} m'
        : '${distance.toStringAsFixed(1)} km';

    // Estimate travel time (assuming average speed of 50 km/h)
    final travelTimeMinutes = (distance / 50 * 60).round();
    final formattedTravelTime = travelTimeMinutes < 60
        ? '$travelTimeMinutes min'
        : '${(travelTimeMinutes / 60).toStringAsFixed(1)} hours';

    return Row(
      children: [
        Icon(
          Icons.route,
          color: theme.colorScheme.primary,
          size: 24.r,
        ),
        SizedBox(width: 12.w),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Distance',
                style: theme.textTheme.bodySmall?.copyWith(
                  color: theme.colorScheme.onSurfaceVariant,
                ),
              ),
              SizedBox(height: 2.h),
              Text(
                formattedDistance,
                style: theme.textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        ),
        Icon(
          Icons.access_time,
          color: theme.colorScheme.primary,
          size: 24.r,
        ),
        SizedBox(width: 12.w),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Est. Travel Time',
                style: theme.textTheme.bodySmall?.copyWith(
                  color: theme.colorScheme.onSurfaceVariant,
                ),
              ),
              SizedBox(height: 2.h),
              Text(
                formattedTravelTime,
                style: theme.textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
