import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:culture_connect/models/travel/travel.dart';
import 'package:culture_connect/widgets/common/rating_display.dart';

/// A widget for displaying a car rental in a grid
class CarRentalGridItem extends StatelessWidget {
  /// The car rental to display
  final CarRental carRental;
  
  /// Callback when the item is tapped
  final VoidCallback onTap;
  
  /// Creates a new car rental grid item
  const CarRentalGridItem({
    super.key,
    required this.carRental,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Card(
      clipBehavior: Clip.antiAlias,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12.r),
      ),
      elevation: 2,
      child: InkWell(
        onTap: onTap,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Car image
            Stack(
              children: [
                AspectRatio(
                  aspectRatio: 16 / 9,
                  child: Hero(
                    tag: 'car_rental_image_${carRental.id}',
                    child: Image.network(
                      carRental.imageUrl,
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) {
                        return Container(
                          color: theme.colorScheme.surfaceContainerHighest,
                          child: Center(
                            child: Icon(
                              Icons.image_not_supported,
                              color: theme.colorScheme.onSurfaceVariant,
                            ),
                          ),
                        );
                      },
                    ),
                  ),
                ),
                if (carRental.isOnSale)
                  Positioned(
                    top: 8.r,
                    left: 8.r,
                    child: Container(
                      padding: EdgeInsets.symmetric(
                        horizontal: 8.w,
                        vertical: 4.h,
                      ),
                      decoration: BoxDecoration(
                        color: theme.colorScheme.error,
                        borderRadius: BorderRadius.circular(4.r),
                      ),
                      child: Text(
                        '${carRental.discountPercentage?.round() ?? 0}% OFF',
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: theme.colorScheme.onError,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
              ],
            ),
            
            // Car details
            Padding(
              padding: EdgeInsets.all(12.r),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Car name
                  Text(
                    carRental.name,
                    style: theme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  SizedBox(height: 4.h),
                  
                  // Car full name
                  Text(
                    carRental.fullName,
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: theme.colorScheme.onSurfaceVariant,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  SizedBox(height: 8.h),
                  
                  // Car features
                  Row(
                    children: [
                      _buildFeatureIcon(
                        Icons.settings,
                        carRental.transmission.displayName,
                      ),
                      SizedBox(width: 8.w),
                      _buildFeatureIcon(
                        Icons.local_gas_station,
                        carRental.fuelType.displayName,
                      ),
                      SizedBox(width: 8.w),
                      _buildFeatureIcon(
                        Icons.airline_seat_recline_normal,
                        '${carRental.seats} seats',
                      ),
                    ],
                  ),
                  SizedBox(height: 8.h),
                  
                  // Rating and price
                  Row(
                    children: [
                      RatingDisplay(
                        rating: carRental.rating,
                        size: 16.r,
                      ),
                      SizedBox(width: 4.w),
                      Text(
                        '(${carRental.reviewCount})',
                        style: theme.textTheme.bodySmall,
                      ),
                      const Spacer(),
                      if (carRental.isOnSale && carRental.originalPrice != null)
                        Text(
                          carRental.formattedOriginalPrice!,
                          style: theme.textTheme.bodySmall?.copyWith(
                            decoration: TextDecoration.lineThrough,
                            color: theme.colorScheme.onSurfaceVariant,
                          ),
                        ),
                      SizedBox(width: 4.w),
                      Text(
                        carRental.formattedPrice,
                        style: theme.textTheme.titleMedium?.copyWith(
                          color: theme.colorScheme.primary,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 4.h),
                  
                  // Per day label
                  Align(
                    alignment: Alignment.centerRight,
                    child: Text(
                      'per day',
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: theme.colorScheme.onSurfaceVariant,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
  
  Widget _buildFeatureIcon(IconData icon, String label) {
    return Expanded(
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: 16.r,
            color: Colors.grey[600],
          ),
          SizedBox(width: 4.w),
          Flexible(
            child: Text(
              label,
              style: TextStyle(
                fontSize: 12.sp,
                color: Colors.grey[600],
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }
}
