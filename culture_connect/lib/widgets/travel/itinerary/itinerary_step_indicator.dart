import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:culture_connect/screens/travel/itinerary/itinerary_builder_screen.dart';
import 'package:culture_connect/theme/app_colors.dart';

/// A widget that displays the current step in the itinerary builder
class ItineraryStepIndicator extends StatelessWidget {
  /// The current step
  final ItineraryBuilderStep currentStep;

  /// Callback when a step is tapped
  final Function(ItineraryBuilderStep)? onStepTapped;

  /// Creates a new itinerary step indicator
  const ItineraryStepIndicator({
    super.key,
    required this.currentStep,
    this.onStepTapped,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 100.h,
      padding: EdgeInsets.symmetric(vertical: 16.h),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(13),
            blurRadius: 5,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: ItineraryBuilderStep.values.map((step) {
          final isActive = step.index <= currentStep.index;
          final isCurrent = step == currentStep;

          return Expanded(
            child: _StepItem(
              step: step,
              isActive: isActive,
              isCurrent: isCurrent,
              onTap: isActive ? () => onStepTapped?.call(step) : null,
            ),
          );
        }).toList(),
      ),
    );
  }
}

/// A single step item in the step indicator
class _StepItem extends StatelessWidget {
  /// The step
  final ItineraryBuilderStep step;

  /// Whether the step is active
  final bool isActive;

  /// Whether the step is the current step
  final bool isCurrent;

  /// Callback when the step is tapped
  final VoidCallback? onTap;

  /// Creates a new step item
  const _StepItem({
    required this.step,
    required this.isActive,
    required this.isCurrent,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Step circle
          Container(
            width: 40.r,
            height: 40.r,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: isActive ? AppColors.primary : Colors.grey.shade300,
              border: Border.all(
                color: isCurrent ? AppColors.secondary : Colors.transparent,
                width: 2.r,
              ),
            ),
            child: Center(
              child: Icon(
                step.icon,
                color: isActive ? Colors.white : Colors.grey,
                size: 20.r,
              ),
            ),
          ),

          SizedBox(height: 4.h),

          // Step name
          Text(
            step.displayName,
            style: TextStyle(
              fontSize: 12.sp,
              fontWeight: isCurrent ? FontWeight.bold : FontWeight.normal,
              color: isActive ? AppColors.primary : Colors.grey,
            ),
          ),
        ],
      ),
    );
  }
}

/// A widget that displays a step progress bar
class StepProgressBar extends StatelessWidget {
  /// The current step
  final int currentStep;

  /// The total number of steps
  final int totalSteps;

  /// The height of the progress bar
  final double height;

  /// The color of the active part of the progress bar
  final Color activeColor;

  /// The color of the inactive part of the progress bar
  final Color inactiveColor;

  /// Creates a new step progress bar
  const StepProgressBar({
    super.key,
    required this.currentStep,
    required this.totalSteps,
    this.height = 4.0,
    this.activeColor = AppColors.primary,
    this.inactiveColor = Colors.grey,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: height,
      decoration: BoxDecoration(
        color: inactiveColor,
        borderRadius: BorderRadius.circular(height / 2),
      ),
      child: Row(
        children: [
          Flexible(
            flex: currentStep,
            child: Container(
              decoration: BoxDecoration(
                color: activeColor,
                borderRadius: BorderRadius.circular(height / 2),
              ),
            ),
          ),
          Flexible(
            flex: totalSteps - currentStep,
            child: Container(),
          ),
        ],
      ),
    );
  }
}
