import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';
import 'package:culture_connect/theme/app_colors.dart';
import 'package:culture_connect/theme/app_text_styles.dart';

/// A widget for selecting dates for an itinerary
class DateSelectionStep extends ConsumerStatefulWidget {
  /// The initial start date
  final DateTime? initialStartDate;

  /// The initial end date
  final DateTime? initialEndDate;

  /// Callback when the dates change
  final Function(DateTime?, DateTime?) onDatesChanged;

  /// The initial budget amount
  final double? initialBudget;

  /// The initial currency
  final String initialCurrency;

  /// Callback when the budget changes
  final Function(double?, String) onBudgetChanged;

  /// Creates a new date selection step
  const DateSelectionStep({
    super.key,
    this.initialStartDate,
    this.initialEndDate,
    required this.onDatesChanged,
    this.initialBudget,
    this.initialCurrency = 'USD',
    required this.onBudgetChanged,
  });

  @override
  ConsumerState<DateSelectionStep> createState() => _DateSelectionStepState();
}

class _DateSelectionStepState extends ConsumerState<DateSelectionStep> {
  late DateTime? _startDate;
  late DateTime? _endDate;
  late TextEditingController _budgetController;
  late String _currency;

  final List<String> _currencies = [
    'USD',
    'EUR',
    'GBP',
    'JPY',
    'NGN',
    'ZAR',
    'AUD',
    'CAD'
  ];

  @override
  void initState() {
    super.initState();
    _startDate = widget.initialStartDate;
    _endDate = widget.initialEndDate;
    _budgetController = TextEditingController(
      text: widget.initialBudget?.toString() ?? '',
    );
    _currency = widget.initialCurrency;
  }

  @override
  void dispose() {
    _budgetController.dispose();
    super.dispose();
  }

  /// Select the start date
  Future<void> _selectStartDate(BuildContext context) async {
    final DateTime now = DateTime.now();
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _startDate ?? now,
      firstDate: now,
      lastDate: now.add(const Duration(days: 365 * 2)),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: const ColorScheme.light(
              primary: AppColors.primary,
              onPrimary: Colors.white,
              onSurface: Colors.black,
            ),
          ),
          child: child!,
        );
      },
    );

    if (picked != null && picked != _startDate) {
      setState(() {
        _startDate = picked;

        // If end date is before start date or not set, update it
        if (_endDate == null || _endDate!.isBefore(_startDate!)) {
          _endDate = _startDate!.add(const Duration(days: 7));
        }

        widget.onDatesChanged(_startDate, _endDate);
      });
    }
  }

  /// Select the end date
  Future<void> _selectEndDate(BuildContext context) async {
    if (_startDate == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please select a start date first')),
      );
      return;
    }

    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _endDate ?? _startDate!.add(const Duration(days: 7)),
      firstDate: _startDate!,
      lastDate: _startDate!.add(const Duration(days: 365)),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: const ColorScheme.light(
              primary: AppColors.primary,
              onPrimary: Colors.white,
              onSurface: Colors.black,
            ),
          ),
          child: child!,
        );
      },
    );

    if (picked != null && picked != _endDate) {
      setState(() {
        _endDate = picked;
        widget.onDatesChanged(_startDate, _endDate);
      });
    }
  }

  /// Update the budget
  void _updateBudget() {
    final budgetText = _budgetController.text;
    final budget = budgetText.isEmpty ? null : double.tryParse(budgetText);
    widget.onBudgetChanged(budget, _currency);
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: EdgeInsets.all(16.r),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title
          Text(
            'When are you traveling?',
            style: AppTextStyles.headline6,
          ),

          SizedBox(height: 8.h),

          // Subtitle
          Text(
            'Select the dates for your trip',
            style: AppTextStyles.subtitle2.copyWith(
              color: Colors.grey[600],
            ),
          ),

          SizedBox(height: 24.h),

          // Date selection cards
          Row(
            children: [
              // Start date card
              Expanded(
                child: _DateCard(
                  label: 'Start Date',
                  date: _startDate,
                  onTap: () => _selectStartDate(context),
                ),
              ),

              SizedBox(width: 16.w),

              // End date card
              Expanded(
                child: _DateCard(
                  label: 'End Date',
                  date: _endDate,
                  onTap: () => _selectEndDate(context),
                ),
              ),
            ],
          ),

          // Trip duration
          if (_startDate != null && _endDate != null) ...[
            SizedBox(height: 16.h),
            Container(
              padding: EdgeInsets.all(12.r),
              decoration: BoxDecoration(
                color: AppColors.primary.withAlpha(25),
                borderRadius: BorderRadius.circular(8.r),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.info_outline,
                    color: AppColors.primary,
                    size: 20.r,
                  ),
                  SizedBox(width: 8.w),
                  Text(
                    'Trip Duration: ${_endDate!.difference(_startDate!).inDays + 1} days',
                    style: TextStyle(
                      fontSize: 14.sp,
                      fontWeight: FontWeight.bold,
                      color: AppColors.primary,
                    ),
                  ),
                ],
              ),
            ),
          ],

          SizedBox(height: 32.h),

          // Budget section
          Text(
            'What\'s your budget?',
            style: AppTextStyles.subtitle1.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),

          SizedBox(height: 8.h),

          Text(
            'This helps us recommend activities within your price range',
            style: AppTextStyles.body2.copyWith(
              color: Colors.grey[600],
            ),
          ),

          SizedBox(height: 16.h),

          // Budget input
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Currency dropdown
              Container(
                width: 100.w,
                height: 56.h,
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey),
                  borderRadius: BorderRadius.circular(8.r),
                ),
                child: DropdownButtonHideUnderline(
                  child: DropdownButton<String>(
                    value: _currency,
                    icon: const Icon(Icons.arrow_drop_down),
                    iconSize: 24.r,
                    elevation: 16,
                    isExpanded: true,
                    padding: EdgeInsets.symmetric(horizontal: 12.w),
                    onChanged: (String? newValue) {
                      if (newValue != null) {
                        setState(() {
                          _currency = newValue;
                          _updateBudget();
                        });
                      }
                    },
                    items: _currencies
                        .map<DropdownMenuItem<String>>((String value) {
                      return DropdownMenuItem<String>(
                        value: value,
                        child: Text(value),
                      );
                    }).toList(),
                  ),
                ),
              ),

              SizedBox(width: 16.w),

              // Amount input
              Expanded(
                child: TextField(
                  controller: _budgetController,
                  decoration: InputDecoration(
                    labelText: 'Budget Amount',
                    hintText: 'e.g. 1000',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8.r),
                    ),
                  ),
                  keyboardType:
                      const TextInputType.numberWithOptions(decimal: true),
                  inputFormatters: [
                    FilteringTextInputFormatter.allow(
                        RegExp(r'^\d+\.?\d{0,2}')),
                  ],
                  onChanged: (value) => _updateBudget(),
                ),
              ),
            ],
          ),

          SizedBox(height: 16.h),

          // Budget tips
          Container(
            padding: EdgeInsets.all(16.r),
            decoration: BoxDecoration(
              color: Colors.amber.withAlpha(25),
              borderRadius: BorderRadius.circular(8.r),
              border: Border.all(color: Colors.amber),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.lightbulb_outline,
                      color: Colors.amber[800],
                      size: 20.r,
                    ),
                    SizedBox(width: 8.w),
                    Text(
                      'Budget Tips',
                      style: TextStyle(
                        fontSize: 16.sp,
                        fontWeight: FontWeight.bold,
                        color: Colors.amber[800],
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 8.h),
                Text(
                  '• Consider accommodation, food, transportation, and activities',
                  style: TextStyle(fontSize: 14.sp),
                ),
                SizedBox(height: 4.h),
                Text(
                  '• Add a buffer of 10-15% for unexpected expenses',
                  style: TextStyle(fontSize: 14.sp),
                ),
                SizedBox(height: 4.h),
                Text(
                  '• Research local costs at your destination',
                  style: TextStyle(fontSize: 14.sp),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

/// A card for selecting a date
class _DateCard extends StatelessWidget {
  /// The label for the card
  final String label;

  /// The selected date
  final DateTime? date;

  /// Callback when the card is tapped
  final VoidCallback onTap;

  /// Creates a new date card
  const _DateCard({
    required this.label,
    required this.date,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final dateFormat = DateFormat('MMM d, yyyy');

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8.r),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8.r),
        child: Padding(
          padding: EdgeInsets.all(16.r),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Label
              Text(
                label,
                style: TextStyle(
                  fontSize: 14.sp,
                  fontWeight: FontWeight.bold,
                  color: Colors.grey[600],
                ),
              ),

              SizedBox(height: 8.h),

              // Date
              Row(
                children: [
                  Icon(
                    Icons.calendar_today,
                    size: 16.r,
                    color: AppColors.primary,
                  ),
                  SizedBox(width: 8.w),
                  Expanded(
                    child: Text(
                      date != null ? dateFormat.format(date!) : 'Select Date',
                      style: TextStyle(
                        fontSize: 16.sp,
                        fontWeight: FontWeight.bold,
                        color: date != null ? Colors.black : Colors.grey,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),

              // Day of week
              if (date != null) ...[
                SizedBox(height: 4.h),
                Text(
                  DateFormat('EEEE').format(date!),
                  style: TextStyle(
                    fontSize: 12.sp,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }
}
