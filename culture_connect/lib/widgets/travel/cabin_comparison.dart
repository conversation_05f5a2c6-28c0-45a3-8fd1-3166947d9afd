import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:culture_connect/models/travel/travel.dart';

/// A widget for comparing cruise cabins
class CabinComparisonDialog extends StatefulWidget {
  /// The list of cabins to compare
  final List<CruiseCabin> cabins;

  /// Callback when a cabin is selected
  final Function(CruiseCabin) onCabinSelected;

  /// Creates a new cabin comparison dialog
  const CabinComparisonDialog({
    super.key,
    required this.cabins,
    required this.onCabinSelected,
  });

  @override
  State<CabinComparisonDialog> createState() => _CabinComparisonDialogState();
}

class _CabinComparisonDialogState extends State<CabinComparisonDialog>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  List<CruiseCabin> _selectedCabins = [];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);

    // Pre-select the first two cabins if available
    if (widget.cabins.length >= 2) {
      _selectedCabins = [widget.cabins[0], widget.cabins[1]];
    } else if (widget.cabins.isNotEmpty) {
      _selectedCabins = [widget.cabins[0]];
    }
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16.r),
      ),
      child: Container(
        width: double.infinity,
        constraints: BoxConstraints(
          maxWidth: 800.w,
          maxHeight: 600.h,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header
            Padding(
              padding: EdgeInsets.all(16.r),
              child: Row(
                children: [
                  Expanded(
                    child: Text(
                      'Compare Cabins',
                      style: theme.textTheme.titleLarge,
                    ),
                  ),
                  IconButton(
                    icon: const Icon(Icons.close),
                    onPressed: () {
                      Navigator.of(context).pop();
                    },
                  ),
                ],
              ),
            ),

            // Tab bar
            TabBar(
              controller: _tabController,
              tabs: const [
                Tab(text: 'Side by Side'),
                Tab(text: 'Feature Comparison'),
              ],
              labelColor: theme.colorScheme.primary,
              unselectedLabelColor: theme.colorScheme.onSurface,
              indicatorColor: theme.colorScheme.primary,
            ),

            // Tab content
            Expanded(
              child: TabBarView(
                controller: _tabController,
                children: [
                  _buildSideBySideComparison(),
                  _buildFeatureComparison(),
                ],
              ),
            ),

            // Footer
            Padding(
              padding: EdgeInsets.all(16.r),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  OutlinedButton(
                    onPressed: () {
                      Navigator.of(context).pop();
                    },
                    child: const Text('Cancel'),
                  ),
                  if (_selectedCabins.isNotEmpty)
                    ElevatedButton(
                      onPressed: () {
                        widget.onCabinSelected(_selectedCabins[0]);
                        Navigator.of(context).pop();
                      },
                      child: const Text('Select Cabin'),
                    ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSideBySideComparison() {
    return Padding(
      padding: EdgeInsets.all(16.r),
      child: Column(
        children: [
          // Cabin selection dropdowns
          Row(
            children: [
              Expanded(
                child: _buildCabinDropdown(0),
              ),
              SizedBox(width: 16.w),
              Expanded(
                child: _buildCabinDropdown(1),
              ),
            ],
          ),
          SizedBox(height: 16.h),

          // Cabin comparison
          Expanded(
            child: _selectedCabins.isEmpty
                ? Center(
                    child: Text(
                      'Select cabins to compare',
                      style: Theme.of(context).textTheme.bodyLarge,
                    ),
                  )
                : Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      if (_selectedCabins.isNotEmpty)
                        Expanded(
                          child: _buildCabinCard(_selectedCabins[0]),
                        ),
                      SizedBox(width: 16.w),
                      if (_selectedCabins.length > 1)
                        Expanded(
                          child: _buildCabinCard(_selectedCabins[1]),
                        )
                      else
                        Expanded(
                          child: Center(
                            child: Text(
                              'Select a second cabin to compare',
                              style: Theme.of(context).textTheme.bodyMedium,
                              textAlign: TextAlign.center,
                            ),
                          ),
                        ),
                    ],
                  ),
          ),
        ],
      ),
    );
  }

  Widget _buildFeatureComparison() {
    final theme = Theme.of(context);

    // Define the features to compare
    final features = [
      'Type',
      'Price per Person',
      'Size',
      'Max Guests',
      'Deck Number',
      'Window',
      'Balcony',
      'Private Bathroom',
      'TV',
      'Minibar',
      'Safe',
      'Room Service',
    ];

    return Padding(
      padding: EdgeInsets.all(16.r),
      child: Column(
        children: [
          // Cabin selection dropdowns
          Row(
            children: [
              Expanded(
                child: _buildCabinDropdown(0),
              ),
              SizedBox(width: 16.w),
              Expanded(
                child: _buildCabinDropdown(1),
              ),
            ],
          ),
          SizedBox(height: 16.h),

          // Feature comparison table
          Expanded(
            child: _selectedCabins.isEmpty
                ? Center(
                    child: Text(
                      'Select cabins to compare',
                      style: theme.textTheme.bodyLarge,
                    ),
                  )
                : SingleChildScrollView(
                    child: Table(
                      border: TableBorder.all(
                        color: theme.colorScheme.outlineVariant,
                        width: 1,
                      ),
                      columnWidths: const {
                        0: FlexColumnWidth(2),
                        1: FlexColumnWidth(3),
                        2: FlexColumnWidth(3),
                      },
                      children: [
                        // Header row
                        TableRow(
                          decoration: BoxDecoration(
                            color: theme.colorScheme.surfaceContainerHighest,
                          ),
                          children: [
                            _buildTableCell('Feature', isHeader: true),
                            _buildTableCell(_selectedCabins[0].type.displayName,
                                isHeader: true),
                            _buildTableCell(
                              _selectedCabins.length > 1
                                  ? _selectedCabins[1].type.displayName
                                  : 'Select Cabin',
                              isHeader: true,
                            ),
                          ],
                        ),

                        // Feature rows
                        for (var feature in features)
                          TableRow(
                            children: [
                              _buildTableCell(feature),
                              _buildTableCell(_getCabinFeatureValue(
                                  _selectedCabins[0], feature)),
                              _buildTableCell(
                                _selectedCabins.length > 1
                                    ? _getCabinFeatureValue(
                                        _selectedCabins[1], feature)
                                    : '-',
                              ),
                            ],
                          ),
                      ],
                    ),
                  ),
          ),
        ],
      ),
    );
  }

  Widget _buildCabinDropdown(int index) {
    return DropdownButtonFormField<String>(
      decoration: InputDecoration(
        labelText: 'Cabin ${index + 1}',
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8.r),
        ),
      ),
      value: _selectedCabins.length > index ? _selectedCabins[index].id : null,
      items: widget.cabins.map((cabin) {
        return DropdownMenuItem<String>(
          value: cabin.id,
          child: Text(
              '${cabin.type.displayName} - ${cabin.formattedPricePerPerson}'),
        );
      }).toList(),
      onChanged: (value) {
        if (value != null) {
          setState(() {
            final cabin = widget.cabins.firstWhere((c) => c.id == value);
            if (_selectedCabins.length > index) {
              _selectedCabins[index] = cabin;
            } else {
              _selectedCabins.add(cabin);
            }
          });
        }
      },
    );
  }

  Widget _buildCabinCard(CruiseCabin cabin) {
    final theme = Theme.of(context);

    return Card(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Cabin image
          ClipRRect(
            borderRadius: BorderRadius.vertical(top: Radius.circular(8.r)),
            child: Image.network(
              cabin.imageUrl,
              height: 150.h,
              width: double.infinity,
              fit: BoxFit.cover,
              errorBuilder: (context, error, stackTrace) {
                return Container(
                  height: 150.h,
                  color: theme.colorScheme.surfaceContainerHighest,
                  child: Center(
                    child: Icon(
                      Icons.image_not_supported,
                      color: theme.colorScheme.onSurfaceVariant,
                    ),
                  ),
                );
              },
            ),
          ),

          // Cabin details
          Padding(
            padding: EdgeInsets.all(16.r),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  cabin.type.displayName,
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(height: 8.h),
                Text(
                  cabin.description,
                  style: theme.textTheme.bodyMedium,
                  maxLines: 3,
                  overflow: TextOverflow.ellipsis,
                ),
                SizedBox(height: 16.h),
                _buildFeatureRow(Icons.straighten, 'Size', cabin.size),
                _buildFeatureRow(
                    Icons.people, 'Max Guests', cabin.maxGuests.toString()),
                _buildFeatureRow(
                    Icons.layers, 'Deck', cabin.deckNumber.toString()),
                _buildFeatureRow(
                    Icons.attach_money, 'Price', cabin.formattedPricePerPerson),
                SizedBox(height: 16.h),
                Wrap(
                  spacing: 8.w,
                  runSpacing: 8.h,
                  children: [
                    if (cabin.hasWindow)
                      _buildFeatureChip(Icons.window, 'Window'),
                    if (cabin.hasBalcony)
                      _buildFeatureChip(Icons.balcony, 'Balcony'),
                    if (cabin.hasPrivateBathroom)
                      _buildFeatureChip(Icons.bathroom, 'Bathroom'),
                    if (cabin.hasTV) _buildFeatureChip(Icons.tv, 'TV'),
                    if (cabin.hasMinibar)
                      _buildFeatureChip(Icons.local_bar, 'Minibar'),
                    if (cabin.hasSafe) _buildFeatureChip(Icons.lock, 'Safe'),
                    if (cabin.hasRoomService)
                      _buildFeatureChip(Icons.room_service, 'Room Service'),
                  ],
                ),
                SizedBox(height: 16.h),
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: () {
                      widget.onCabinSelected(cabin);
                      Navigator.of(context).pop();
                    },
                    child: const Text('Select'),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFeatureRow(IconData icon, String label, String value) {
    final theme = Theme.of(context);

    return Padding(
      padding: EdgeInsets.only(bottom: 8.h),
      child: Row(
        children: [
          Icon(
            icon,
            size: 16.r,
            color: theme.colorScheme.primary,
          ),
          SizedBox(width: 8.w),
          Text(
            '$label: ',
            style: theme.textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: theme.textTheme.bodyMedium,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFeatureChip(IconData icon, String label) {
    final theme = Theme.of(context);

    return Chip(
      materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
      avatar: Icon(
        icon,
        size: 16.r,
        color: theme.colorScheme.primary,
      ),
      label: Text(
        label,
        style: theme.textTheme.bodySmall,
      ),
      backgroundColor: theme.colorScheme.surfaceContainerHighest,
      padding: EdgeInsets.symmetric(horizontal: 4.w),
      visualDensity: VisualDensity.compact,
    );
  }

  Widget _buildTableCell(String text, {bool isHeader = false}) {
    final theme = Theme.of(context);

    return TableCell(
      verticalAlignment: TableCellVerticalAlignment.middle,
      child: Padding(
        padding: EdgeInsets.all(8.r),
        child: Text(
          text,
          style: isHeader
              ? theme.textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                )
              : theme.textTheme.bodyMedium,
          textAlign: isHeader ? TextAlign.center : TextAlign.left,
        ),
      ),
    );
  }

  String _getCabinFeatureValue(CruiseCabin cabin, String feature) {
    switch (feature) {
      case 'Type':
        return cabin.type.displayName;
      case 'Price per Person':
        return cabin.formattedPricePerPerson;
      case 'Size':
        return cabin.size;
      case 'Max Guests':
        return cabin.maxGuests.toString();
      case 'Deck Number':
        return cabin.deckNumber.toString();
      case 'Window':
        return cabin.hasWindow ? 'Yes' : 'No';
      case 'Balcony':
        return cabin.hasBalcony ? 'Yes' : 'No';
      case 'Private Bathroom':
        return cabin.hasPrivateBathroom ? 'Yes' : 'No';
      case 'TV':
        return cabin.hasTV ? 'Yes' : 'No';
      case 'Minibar':
        return cabin.hasMinibar ? 'Yes' : 'No';
      case 'Safe':
        return cabin.hasSafe ? 'Yes' : 'No';
      case 'Room Service':
        return cabin.hasRoomService ? 'Yes' : 'No';
      default:
        return '-';
    }
  }
}
