// Dart imports
import 'package:flutter/material.dart';

// Package imports
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

// Project imports
import 'package:culture_connect/providers/services_providers.dart';

/// A widget for selecting the party size for a restaurant reservation
class PartySizeSelector extends ConsumerWidget {
  /// The current party size
  final int partySize;

  /// Callback when the party size changes
  final Function(int) onPartySizeChanged;

  /// The minimum party size
  final int minSize;

  /// The maximum party size
  final int maxSize;

  /// Creates a new party size selector
  const PartySizeSelector({
    super.key,
    required this.partySize,
    required this.onPartySizeChanged,
    this.minSize = 1,
    this.maxSize = 20,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final logger = ref.read(loggingServiceProvider);

    try {
      logger.debug('PartySizeSelector',
          'Building party size selector with current size: $partySize');

      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Stepper with - and + buttons
          Container(
            decoration: BoxDecoration(
              border: Border.all(color: theme.colorScheme.outline),
              borderRadius: BorderRadius.circular(8.r),
            ),
            child: Row(
              children: [
                // Decrement button
                _buildButton(
                  context: context,
                  icon: Icons.remove,
                  onPressed: partySize > minSize
                      ? () {
                          try {
                            logger.debug('PartySizeSelector',
                                'Decreasing party size from $partySize to ${partySize - 1}');
                            onPartySizeChanged(partySize - 1);
                          } catch (e, stackTrace) {
                            logger.error('PartySizeSelector',
                                'Error decreasing party size', e, stackTrace);
                          }
                        }
                      : null,
                ),

                // Divider
                Container(
                  width: 1,
                  height: 40.h,
                  color: theme.colorScheme.outline,
                ),

                // Current value
                Expanded(
                  child: Center(
                    child: Text(
                      '$partySize ${partySize == 1 ? 'person' : 'people'}',
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),

                // Divider
                Container(
                  width: 1,
                  height: 40.h,
                  color: theme.colorScheme.outline,
                ),

                // Increment button
                _buildButton(
                  context: context,
                  icon: Icons.add,
                  onPressed: partySize < maxSize
                      ? () {
                          try {
                            logger.debug('PartySizeSelector',
                                'Increasing party size from $partySize to ${partySize + 1}');
                            onPartySizeChanged(partySize + 1);
                          } catch (e, stackTrace) {
                            logger.error('PartySizeSelector',
                                'Error increasing party size', e, stackTrace);
                          }
                        }
                      : null,
                ),
              ],
            ),
          ),

          SizedBox(height: 16.h),

          // Quick selection buttons
          Wrap(
            spacing: 8.w,
            runSpacing: 8.h,
            children: [
              for (final size in [2, 4, 6, 8, 10])
                _buildQuickSelectButton(context, size),
            ],
          ),
        ],
      );
    } catch (e, stackTrace) {
      logger.error('PartySizeSelector', 'Error building party size selector', e,
          stackTrace);

      // Fallback UI in case of error
      return Center(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Text(
            'Error loading party size selector: ${e.toString()}',
            style: theme.textTheme.bodyMedium
                ?.copyWith(color: theme.colorScheme.error),
          ),
        ),
      );
    }
  }

  Widget _buildButton({
    required BuildContext context,
    required IconData icon,
    VoidCallback? onPressed,
  }) {
    final theme = Theme.of(context);

    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onPressed,
        child: SizedBox(
          width: 48.w,
          height: 48.h,
          child: Center(
            child: Icon(
              icon,
              color: onPressed != null
                  ? theme.colorScheme.primary
                  : theme.colorScheme.onSurfaceVariant
                      .withAlpha(128), // 128 is equivalent to 0.5 opacity
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildQuickSelectButton(BuildContext context, int size) {
    final theme = Theme.of(context);
    final isSelected = partySize == size;

    try {
      return InkWell(
        onTap: () {
          try {
            debugPrint('PartySizeSelector: Quick selecting party size: $size');
            onPartySizeChanged(size);
          } catch (e) {
            debugPrint(
                'PartySizeSelector: Error setting party size to $size: $e');
          }
        },
        borderRadius: BorderRadius.circular(8.r),
        child: AnimatedContainer(
          duration: const Duration(milliseconds: 200),
          padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
          decoration: BoxDecoration(
            color: isSelected
                ? theme.colorScheme.primary
                : theme.colorScheme.surfaceContainerHighest,
            borderRadius: BorderRadius.circular(8.r),
          ),
          child: Text(
            '$size ${size == 1 ? 'person' : 'people'}',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: isSelected
                  ? theme.colorScheme.onPrimary
                  : theme.colorScheme.onSurface,
              fontWeight: isSelected ? FontWeight.bold : null,
            ),
          ),
        ),
      );
    } catch (e) {
      debugPrint(
          'PartySizeSelector: Error building quick select button for size $size: $e');

      // Fallback UI in case of error
      return Container(
        padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
        decoration: BoxDecoration(
          color: Colors.grey.withAlpha(50),
          borderRadius: BorderRadius.circular(8.r),
        ),
        child: Text('$size', style: theme.textTheme.bodyMedium),
      );
    }
  }
}
