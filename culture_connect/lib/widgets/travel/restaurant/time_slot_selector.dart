// Dart imports
import 'package:flutter/material.dart';

// Package imports
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

// Project imports
import 'package:culture_connect/models/travel/restaurant_reservation.dart';
import 'package:culture_connect/providers/services_providers.dart';

/// A widget for selecting a time slot for a restaurant reservation
class TimeSlotSelector extends ConsumerWidget {
  /// The available time slots
  final List<RestaurantTimeSlot> timeSlots;

  /// The currently selected time slot
  final RestaurantTimeSlot? selectedTimeSlot;

  /// Callback when a time slot is selected
  final Function(RestaurantTimeSlot) onTimeSlotSelected;

  /// Creates a new time slot selector
  const TimeSlotSelector({
    super.key,
    required this.timeSlots,
    this.selectedTimeSlot,
    required this.onTimeSlotSelected,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final logger = ref.read(loggingServiceProvider);

    try {
      logger.debug('TimeSlotSelector',
          'Building time slot selector with ${timeSlots.length} slots');

      if (timeSlots.isEmpty) {
        logger.info('TimeSlotSelector', 'No time slots available');
        return const Center(
          child: Padding(
            padding: EdgeInsets.all(16.0),
            child: Text('No available time slots'),
          ),
        );
      }

      // Sort time slots by start time
      final sortedSlots = List<RestaurantTimeSlot>.from(timeSlots);
      try {
        sortedSlots.sort((a, b) {
          final aMinutes = a.startTime.hour * 60 + a.startTime.minute;
          final bMinutes = b.startTime.hour * 60 + b.startTime.minute;
          return aMinutes.compareTo(bMinutes);
        });
        logger.debug('TimeSlotSelector',
            'Successfully sorted ${sortedSlots.length} time slots');
      } catch (e, stackTrace) {
        logger.error(
            'TimeSlotSelector', 'Error sorting time slots', e, stackTrace);
        return Center(
          child: Text('Error sorting time slots: ${e.toString()}'),
        );
      }

      // Group time slots by hour
      final groupedSlots = <int, List<RestaurantTimeSlot>>{};
      try {
        for (final slot in sortedSlots) {
          final hour = slot.startTime.hour;
          if (!groupedSlots.containsKey(hour)) {
            groupedSlots[hour] = [];
          }
          groupedSlots[hour]!.add(slot);
        }
        logger.debug('TimeSlotSelector',
            'Successfully grouped time slots into ${groupedSlots.length} hours');
      } catch (e, stackTrace) {
        logger.error(
            'TimeSlotSelector', 'Error grouping time slots', e, stackTrace);
        return Center(
          child: Text('Error grouping time slots: ${e.toString()}'),
        );
      }

      // Sort hours
      final sortedHours = groupedSlots.keys.toList()..sort();
      logger.debug('TimeSlotSelector',
          'Time slots available for ${sortedHours.length} hours');

      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          for (final hour in sortedHours) ...[
            Padding(
              padding: EdgeInsets.only(
                  bottom: 8.h, top: hour == sortedHours.first ? 0 : 16.h),
              child: Text(
                _formatHour(hour),
                style: Theme.of(context).textTheme.titleSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
              ),
            ),
            Wrap(
              spacing: 8.w,
              runSpacing: 8.h,
              children: groupedSlots[hour]!
                  .map((slot) => _buildTimeSlotChip(context, slot))
                  .toList(),
            ),
          ],
        ],
      );
    } catch (e, stackTrace) {
      logger.error('TimeSlotSelector',
          'Unexpected error building time slot selector', e, stackTrace);
      return Center(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Text('Error displaying time slots: ${e.toString()}'),
        ),
      );
    }
  }

  Widget _buildTimeSlotChip(BuildContext context, RestaurantTimeSlot slot) {
    final theme = Theme.of(context);
    final isSelected = selectedTimeSlot?.id == slot.id;

    try {
      // Determine color based on availability
      Color backgroundColor;
      Color textColor;

      if (isSelected) {
        backgroundColor = theme.colorScheme.primary;
        textColor = theme.colorScheme.onPrimary;
      } else if (!slot.isAvailable) {
        backgroundColor = theme.colorScheme.surfaceContainerHighest;
        textColor = theme.colorScheme.onSurfaceVariant;
      } else if (slot.availabilityPercentage < 0.3) {
        // Low availability
        backgroundColor =
            Colors.red.withAlpha(26); // 26 is equivalent to 0.1 opacity
        textColor = Colors.red;
      } else if (slot.availabilityPercentage < 0.6) {
        // Medium availability
        backgroundColor =
            Colors.orange.withAlpha(26); // 26 is equivalent to 0.1 opacity
        textColor = Colors.orange.shade800;
      } else {
        // High availability
        backgroundColor =
            Colors.green.withAlpha(26); // 26 is equivalent to 0.1 opacity
        textColor = Colors.green.shade800;
      }

      return AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        child: Material(
          color: Colors.transparent,
          child: InkWell(
            onTap: slot.isAvailable
                ? () {
                    try {
                      onTimeSlotSelected(slot);
                    } catch (e) {
                      debugPrint('Error selecting time slot: $e');
                    }
                  }
                : null,
            borderRadius: BorderRadius.circular(8.r),
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.h),
              decoration: BoxDecoration(
                color: backgroundColor,
                borderRadius: BorderRadius.circular(8.r),
                border: Border.all(
                  color: isSelected
                      ? theme.colorScheme.primary
                      : Colors.transparent,
                  width: 2,
                ),
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    _formatTimeOfDay(slot.startTime),
                    style: theme.textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: textColor,
                    ),
                  ),
                  if (!slot.isAvailable)
                    const Text(
                      'Unavailable',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey,
                      ),
                    )
                  else if (slot.availabilityPercentage < 0.3)
                    Text(
                      'Almost Full',
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: textColor,
                      ),
                    ),
                ],
              ),
            ),
          ),
        ),
      );
    } catch (e) {
      // Fallback in case of error
      debugPrint('Error building time slot chip: $e');
      return Container(
        padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.h),
        decoration: BoxDecoration(
          color: Colors.grey.withAlpha(50),
          borderRadius: BorderRadius.circular(8.r),
        ),
        child: const Text('Error'),
      );
    }
  }

  /// Format an hour (0-23) to a 12-hour format with AM/PM
  String _formatHour(int hour) {
    try {
      if (hour < 0 || hour > 23) {
        debugPrint('Invalid hour value: $hour');
        return 'Invalid hour';
      }

      if (hour == 0) {
        return '12 AM';
      } else if (hour < 12) {
        return '$hour AM';
      } else if (hour == 12) {
        return '12 PM';
      } else {
        return '${hour - 12} PM';
      }
    } catch (e) {
      debugPrint('Error formatting hour: $e');
      return 'Error';
    }
  }

  /// Format a TimeOfDay to a string in 12-hour format with AM/PM
  String _formatTimeOfDay(TimeOfDay timeOfDay) {
    try {
      final hour = timeOfDay.hour == 0
          ? 12
          : (timeOfDay.hour > 12 ? timeOfDay.hour - 12 : timeOfDay.hour);
      final minute = timeOfDay.minute.toString().padLeft(2, '0');
      final period = timeOfDay.hour < 12 ? 'AM' : 'PM';
      return '$hour:$minute $period';
    } catch (e) {
      debugPrint('Error formatting TimeOfDay: $e');
      return 'Error';
    }
  }
}
