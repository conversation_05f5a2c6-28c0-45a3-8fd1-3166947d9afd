// Dart imports
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

// Package imports
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

// Project imports
import 'package:culture_connect/providers/services_providers.dart';

/// A widget for selecting a date from a horizontal calendar
class CalendarDayPicker extends ConsumerStatefulWidget {
  /// The currently selected date
  final DateTime selectedDate;

  /// Callback when a date is selected
  final Function(DateTime) onDateSelected;

  /// The minimum selectable date
  final DateTime minDate;

  /// The maximum selectable date
  final DateTime maxDate;

  /// Creates a new calendar day picker
  const CalendarDayPicker({
    super.key,
    required this.selectedDate,
    required this.onDateSelected,
    required this.minDate,
    required this.maxDate,
  });

  @override
  ConsumerState<CalendarDayPicker> createState() => _CalendarDayPickerState();
}

class _CalendarDayPickerState extends ConsumerState<CalendarDayPicker> {
  late ScrollController _scrollController;
  late List<DateTime> _visibleDates;
  late DateTime _currentMonth;

  // Logger tag for this widget
  static const String _logTag = 'CalendarDayPicker';

  @override
  void initState() {
    super.initState();
    final logger = ref.read(loggingServiceProvider);

    try {
      logger.debug(_logTag, 'Initializing calendar day picker');
      _scrollController = ScrollController();
      _currentMonth =
          DateTime(widget.selectedDate.year, widget.selectedDate.month);
      _generateVisibleDates();

      // Scroll to selected date after build
      WidgetsBinding.instance.addPostFrameCallback((_) {
        try {
          _scrollToSelectedDate();
        } catch (e, stackTrace) {
          logger.error(
              _logTag, 'Error scrolling to selected date', e, stackTrace);
        }
      });

      logger.debug(_logTag,
          'Calendar day picker initialized with ${_visibleDates.length} dates');
    } catch (e, stackTrace) {
      logger.error(
          _logTag, 'Error initializing calendar day picker', e, stackTrace);
      // Initialize with defaults to prevent further errors
      _scrollController = ScrollController();
      _visibleDates = [];
      _currentMonth = DateTime.now();
    }
  }

  @override
  void didUpdateWidget(CalendarDayPicker oldWidget) {
    super.didUpdateWidget(oldWidget);
    final logger = ref.read(loggingServiceProvider);

    try {
      if (oldWidget.selectedDate != widget.selectedDate) {
        logger.debug(_logTag,
            'Selected date changed, scrolling to new date: ${widget.selectedDate}');
        _scrollToSelectedDate();
      }

      // Check if min/max dates changed and regenerate visible dates if needed
      if (oldWidget.minDate != widget.minDate ||
          oldWidget.maxDate != widget.maxDate) {
        logger.debug(
            _logTag, 'Min/max dates changed, regenerating visible dates');
        _generateVisibleDates();
        _scrollToSelectedDate();
      }
    } catch (e, stackTrace) {
      logger.error(_logTag, 'Error in didUpdateWidget', e, stackTrace);
    }
  }

  @override
  void dispose() {
    final logger = ref.read(loggingServiceProvider);

    try {
      logger.debug(_logTag, 'Disposing calendar day picker');
      _scrollController.dispose();
    } catch (e, stackTrace) {
      logger.error(
          _logTag, 'Error disposing calendar day picker', e, stackTrace);
    } finally {
      super.dispose();
    }
  }

  void _generateVisibleDates() {
    final logger = ref.read(loggingServiceProvider);

    try {
      logger.debug(_logTag,
          'Generating visible dates from ${widget.minDate} to ${widget.maxDate}');
      _visibleDates = [];

      // Validate min and max dates
      if (widget.minDate.isAfter(widget.maxDate)) {
        logger.warning(
            _logTag, 'Min date is after max date, using default range');
        final today = DateTime.now();
        final minDate = DateTime(today.year, today.month, today.day);
        final maxDate = minDate.add(const Duration(days: 30));

        // Generate dates from today to 30 days later
        DateTime current = minDate;
        while (current.isBefore(maxDate) || current.isAtSameMomentAs(maxDate)) {
          _visibleDates.add(current);
          current = current.add(const Duration(days: 1));
        }
      } else {
        // Generate dates from min date to max date
        DateTime current = DateTime(
            widget.minDate.year, widget.minDate.month, widget.minDate.day);
        while (current.isBefore(widget.maxDate) ||
            current.isAtSameMomentAs(widget.maxDate)) {
          _visibleDates.add(current);
          current = current.add(const Duration(days: 1));
        }
      }

      logger.debug(_logTag, 'Generated ${_visibleDates.length} visible dates');
    } catch (e, stackTrace) {
      logger.error(_logTag, 'Error generating visible dates', e, stackTrace);

      // Fallback to a default range of dates
      _visibleDates = [];
      final today = DateTime.now();
      for (int i = 0; i < 30; i++) {
        _visibleDates.add(today.add(Duration(days: i)));
      }
    }
  }

  void _scrollToSelectedDate() {
    final logger = ref.read(loggingServiceProvider);

    try {
      logger.debug(
          _logTag, 'Scrolling to selected date: ${widget.selectedDate}');

      if (_visibleDates.isEmpty) {
        logger.warning(_logTag, 'No visible dates available for scrolling');
        return;
      }

      final selectedIndex = _visibleDates.indexWhere((date) =>
          date.year == widget.selectedDate.year &&
          date.month == widget.selectedDate.month &&
          date.day == widget.selectedDate.day);

      logger.debug(_logTag, 'Selected date index: $selectedIndex');

      if (selectedIndex != -1 && _scrollController.hasClients) {
        final itemWidth = 70.w;
        final screenWidth = MediaQuery.of(context).size.width;
        final offset =
            (selectedIndex * itemWidth) - (screenWidth / 2) + (itemWidth / 2);
        final clampedOffset =
            offset.clamp(0.0, _scrollController.position.maxScrollExtent);

        logger.debug(_logTag, 'Scrolling to offset: $clampedOffset');

        _scrollController.animateTo(
          clampedOffset,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
        );
      } else if (selectedIndex == -1) {
        logger.warning(_logTag, 'Selected date not found in visible dates');
      } else if (!_scrollController.hasClients) {
        logger.warning(_logTag, 'Scroll controller has no clients');
      }
    } catch (e, stackTrace) {
      logger.error(_logTag, 'Error scrolling to selected date', e, stackTrace);
    }
  }

  Future<void> _showMonthPicker() async {
    final logger = ref.read(loggingServiceProvider);

    try {
      logger.debug(_logTag, 'Showing month picker');

      final DateTime? picked = await showDatePicker(
        context: context,
        initialDate: widget.selectedDate,
        firstDate: widget.minDate,
        lastDate: widget.maxDate,
        initialEntryMode: DatePickerEntryMode.calendarOnly,
      );

      if (picked != null) {
        logger.debug(_logTag, 'Date selected from picker: $picked');

        if (mounted) {
          try {
            widget.onDateSelected(picked);
          } catch (e, stackTrace) {
            logger.error(
                _logTag, 'Error in onDateSelected callback', e, stackTrace);
          }
        } else {
          logger.warning(
              _logTag, 'Widget not mounted, cannot call onDateSelected');
        }
      } else {
        logger.debug(_logTag, 'No date selected from picker');
      }
    } catch (e, stackTrace) {
      logger.error(_logTag, 'Error showing month picker', e, stackTrace);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error showing calendar: ${e.toString()}'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final logger = ref.read(loggingServiceProvider);

    try {
      logger.debug(_logTag, 'Building calendar day picker');

      return Column(
        children: [
          // Month header with arrows
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                DateFormat('MMMM yyyy').format(widget.selectedDate),
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              TextButton(
                onPressed: _showMonthPicker,
                child: Row(
                  children: [
                    Text(
                      'Calendar',
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: theme.colorScheme.primary,
                      ),
                    ),
                    SizedBox(width: 4.w),
                    Icon(
                      Icons.calendar_month,
                      size: 18.r,
                      color: theme.colorScheme.primary,
                    ),
                  ],
                ),
              ),
            ],
          ),

          SizedBox(height: 8.h),

          // Day selector
          if (_visibleDates.isEmpty)
            Center(
              child: Padding(
                padding: EdgeInsets.symmetric(vertical: 20.h),
                child: Text(
                  'No dates available',
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: theme.colorScheme.error,
                  ),
                ),
              ),
            )
          else
            SizedBox(
              height: 90.h,
              child: ListView.builder(
                controller: _scrollController,
                scrollDirection: Axis.horizontal,
                itemCount: _visibleDates.length,
                itemBuilder: (context, index) {
                  try {
                    final date = _visibleDates[index];
                    return _buildDayItem(context, date);
                  } catch (e, stackTrace) {
                    logger.error(
                        _logTag,
                        'Error building day item at index $index',
                        e,
                        stackTrace);
                    return const SizedBox(width: 70);
                  }
                },
              ),
            ),
        ],
      );
    } catch (e, stackTrace) {
      logger.error(
          _logTag, 'Error building calendar day picker', e, stackTrace);

      // Fallback UI in case of error
      return Center(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Text(
            'Error loading calendar: ${e.toString()}',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.error,
            ),
          ),
        ),
      );
    }
  }

  Widget _buildDayItem(BuildContext context, DateTime date) {
    final theme = Theme.of(context);
    final logger = ref.read(loggingServiceProvider);

    try {
      final isSelected = date.year == widget.selectedDate.year &&
          date.month == widget.selectedDate.month &&
          date.day == widget.selectedDate.day;

      final isToday = date.year == DateTime.now().year &&
          date.month == DateTime.now().month &&
          date.day == DateTime.now().day;

      final isDisabled =
          date.isBefore(widget.minDate) || date.isAfter(widget.maxDate);

      // Check if it's a weekend
      final isWeekend =
          date.weekday == DateTime.saturday || date.weekday == DateTime.sunday;

      return GestureDetector(
        onTap: isDisabled
            ? null
            : () {
                try {
                  logger.debug(_logTag, 'Date selected: $date');
                  widget.onDateSelected(date);
                } catch (e, stackTrace) {
                  logger.error(_logTag, 'Error in onDateSelected callback', e,
                      stackTrace);
                }
              },
        child: AnimatedContainer(
          duration: const Duration(milliseconds: 200),
          width: 70.w,
          margin: EdgeInsets.symmetric(horizontal: 4.w),
          decoration: BoxDecoration(
            color: isSelected
                ? theme.colorScheme.primary
                : (isToday
                    ? theme.colorScheme.primaryContainer
                    : theme.colorScheme.surfaceContainerHighest),
            borderRadius: BorderRadius.circular(12.r),
            border: isToday && !isSelected
                ? Border.all(color: theme.colorScheme.primary, width: 2)
                : null,
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Day of week
              Text(
                DateFormat('E').format(date),
                style: theme.textTheme.bodySmall?.copyWith(
                  color: isSelected
                      ? theme.colorScheme.onPrimary
                      : (isDisabled
                          ? theme.colorScheme.onSurface.withAlpha(
                              102) // 102 is equivalent to 0.4 opacity
                          : (isWeekend
                              ? theme.colorScheme.primary
                              : theme.colorScheme.onSurface)),
                  fontWeight: isWeekend ? FontWeight.bold : null,
                ),
              ),

              SizedBox(height: 4.h),

              // Day number
              Text(
                date.day.toString(),
                style: theme.textTheme.titleLarge?.copyWith(
                  color: isSelected
                      ? theme.colorScheme.onPrimary
                      : (isDisabled
                          ? theme.colorScheme.onSurface.withAlpha(
                              102) // 102 is equivalent to 0.4 opacity
                          : theme.colorScheme.onSurface),
                  fontWeight: isSelected || isToday ? FontWeight.bold : null,
                ),
              ),

              SizedBox(height: 4.h),

              // Month (only show if different from current month)
              if (date.month != _currentMonth.month)
                Text(
                  DateFormat('MMM').format(date),
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: isSelected
                        ? theme.colorScheme.onPrimary
                        : (isDisabled
                            ? theme.colorScheme.onSurface.withAlpha(
                                102) // 102 is equivalent to 0.4 opacity
                            : theme.colorScheme.onSurface),
                  ),
                ),
            ],
          ),
        ),
      );
    } catch (e, stackTrace) {
      logger.error(
          _logTag, 'Error building day item for date: $date', e, stackTrace);

      // Fallback UI in case of error
      return Container(
        width: 70.w,
        margin: EdgeInsets.symmetric(horizontal: 4.w),
        decoration: BoxDecoration(
          color: Colors.grey.withAlpha(50),
          borderRadius: BorderRadius.circular(12.r),
        ),
        child: Center(
          child: Text(
            '?',
            style: theme.textTheme.titleLarge,
          ),
        ),
      );
    }
  }
}
