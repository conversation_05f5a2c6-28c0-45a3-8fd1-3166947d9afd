import 'package:flutter/material.dart';

import 'package:culture_connect/models/travel/transfer/transfer_location.dart';
import 'package:culture_connect/theme/app_theme.dart';

/// A widget for picking transfer locations
class LocationPicker extends StatefulWidget {
  /// The initially selected location
  final TransferLocation? initialLocation;

  /// The type of location to pick
  final TransferLocationType? locationType;

  /// The list of available locations
  final List<TransferLocation> availableLocations;

  /// Callback when a location is selected
  final Function(TransferLocation location) onLocationSelected;

  /// Callback when the add new location button is tapped
  final VoidCallback? onAddNewLocation;

  /// Creates a new location picker
  const LocationPicker({
    super.key,
    this.initialLocation,
    this.locationType,
    required this.availableLocations,
    required this.onLocationSelected,
    this.onAddNewLocation,
  });

  @override
  State<LocationPicker> createState() => _LocationPickerState();
}

class _LocationPickerState extends State<LocationPicker> {
  TransferLocation? _selectedLocation;
  final TextEditingController _searchController = TextEditingController();
  List<TransferLocation> _filteredLocations = [];

  @override
  void initState() {
    super.initState();
    _selectedLocation = widget.initialLocation;
    _filteredLocations = _filterLocationsByType(widget.availableLocations);

    _searchController.addListener(_filterLocations);
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  /// Filter locations by type
  List<TransferLocation> _filterLocationsByType(
      List<TransferLocation> locations) {
    if (widget.locationType == null) {
      return locations;
    }

    return locations
        .where((location) => location.type == widget.locationType)
        .toList();
  }

  /// Filter locations by search query
  void _filterLocations() {
    final query = _searchController.text.toLowerCase();

    setState(() {
      if (query.isEmpty) {
        _filteredLocations = _filterLocationsByType(widget.availableLocations);
      } else {
        _filteredLocations =
            _filterLocationsByType(widget.availableLocations).where((location) {
          return location.name.toLowerCase().contains(query) ||
              location.address.toLowerCase().contains(query) ||
              location.city.toLowerCase().contains(query) ||
              location.country.toLowerCase().contains(query);
        }).toList();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Selected location display
        InkWell(
          onTap: () => _showLocationPicker(context),
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey[300]!),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              children: [
                Icon(
                  _selectedLocation?.type.icon ?? Icons.location_on,
                  color: _selectedLocation?.type.color ?? Colors.grey[600],
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _selectedLocation != null
                      ? Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              _selectedLocation!.name,
                              style: const TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            Text(
                              _selectedLocation!.address,
                              style: TextStyle(
                                fontSize: 14,
                                color: Colors.grey[600],
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ],
                        )
                      : Text(
                          'Select a location',
                          style: TextStyle(
                            fontSize: 16,
                            color: Colors.grey[600],
                          ),
                        ),
                ),
                Icon(
                  Icons.arrow_drop_down,
                  color: Colors.grey[600],
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  /// Show the location picker dialog
  void _showLocationPicker(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (context) {
        return Container(
          height: MediaQuery.of(context).size.height * 0.7,
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Select Location',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),
              // Search field
              TextField(
                controller: _searchController,
                decoration: InputDecoration(
                  hintText: 'Search locations',
                  prefixIcon: const Icon(Icons.search),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  contentPadding: const EdgeInsets.symmetric(vertical: 12),
                ),
              ),
              const SizedBox(height: 16),
              // Location type filter
              if (widget.locationType == null)
                SizedBox(
                  height: 40,
                  child: ListView(
                    scrollDirection: Axis.horizontal,
                    children: [
                      _buildTypeFilterChip(null, 'All'),
                      ...TransferLocationType.values.map((type) =>
                          _buildTypeFilterChip(type, type.displayName)),
                    ],
                  ),
                ),
              const SizedBox(height: 16),
              // Location list
              Expanded(
                child: _filteredLocations.isEmpty
                    ? Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.location_off,
                              size: 48,
                              color: Colors.grey[400],
                            ),
                            const SizedBox(height: 16),
                            Text(
                              'No locations found',
                              style: TextStyle(
                                fontSize: 16,
                                color: Colors.grey[600],
                              ),
                            ),
                          ],
                        ),
                      )
                    : ListView.builder(
                        itemCount: _filteredLocations.length,
                        itemBuilder: (context, index) {
                          final location = _filteredLocations[index];
                          final isSelected =
                              _selectedLocation?.id == location.id;

                          return ListTile(
                            leading: Icon(
                              location.type.icon,
                              color: location.type.color,
                            ),
                            title: Text(location.name),
                            subtitle: Text(
                              location.address,
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                            trailing: isSelected
                                ? const Icon(
                                    Icons.check_circle,
                                    color: AppTheme.primaryColor,
                                  )
                                : null,
                            onTap: () {
                              setState(() {
                                _selectedLocation = location;
                              });
                              widget.onLocationSelected(location);
                              Navigator.pop(context);
                            },
                          );
                        },
                      ),
              ),
              // Add new location button
              if (widget.onAddNewLocation != null)
                Padding(
                  padding: const EdgeInsets.only(top: 16),
                  child: SizedBox(
                    width: double.infinity,
                    child: ElevatedButton.icon(
                      onPressed: () {
                        Navigator.pop(context);
                        widget.onAddNewLocation!();
                      },
                      icon: const Icon(Icons.add_location_alt),
                      label: const Text('Add New Location'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppTheme.primaryColor,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                    ),
                  ),
                ),
            ],
          ),
        );
      },
    );
  }

  /// Build a type filter chip
  Widget _buildTypeFilterChip(TransferLocationType? type, String label) {
    final isSelected = widget.locationType == type;

    return GestureDetector(
      onTap: () {
        // This is just for UI demonstration
        // In a real app, this would update the filter
      },
      child: Container(
        margin: const EdgeInsets.only(right: 8),
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color: isSelected
              ? (type?.color ?? AppTheme.primaryColor).withAlpha(30)
              : Colors.grey[200],
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: isSelected
                ? (type?.color ?? AppTheme.primaryColor)
                : Colors.grey[300]!,
          ),
        ),
        child: Text(
          label,
          style: TextStyle(
            fontSize: 14,
            fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
            color: isSelected
                ? (type?.color ?? AppTheme.primaryColor)
                : Colors.grey[700],
          ),
        ),
      ),
    );
  }
}
