import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:culture_connect/models/travel/transfer/transfers.dart';
import 'package:culture_connect/theme/app_theme.dart';

/// A card widget for displaying a transfer service
class TransferCard extends StatelessWidget {
  /// The transfer service to display
  final TransferService transfer;
  
  /// Whether to show the full details
  final bool showFullDetails;
  
  /// Callback when the card is tapped
  final VoidCallback? onTap;
  
  /// Callback when the book button is tapped
  final VoidCallback? onBook;
  
  /// Creates a new transfer card
  const TransferCard({
    super.key,
    required this.transfer,
    this.showFullDetails = false,
    this.onTap,
    this.onBook,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Card(
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12.r),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Image
            ClipRRect(
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(12.r),
                topRight: Radius.circular(12.r),
              ),
              child: Stack(
                children: [
                  Image.network(
                    transfer.imageUrl,
                    height: 150.h,
                    width: double.infinity,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) {
                      return Container(
                        height: 150.h,
                        width: double.infinity,
                        color: Colors.grey[300],
                        child: Icon(
                          Icons.airport_shuttle,
                          size: 50.r,
                          color: Colors.grey[500],
                        ),
                      );
                    },
                  ),
                  if (transfer.isFeatured)
                    Positioned(
                      top: 8.r,
                      left: 8.r,
                      child: Container(
                        padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
                        decoration: BoxDecoration(
                          color: AppTheme.primaryColor,
                          borderRadius: BorderRadius.circular(4.r),
                        ),
                        child: Text(
                          'Featured',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 12.sp,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ),
                  if (transfer.isOnSale)
                    Positioned(
                      top: 8.r,
                      right: 8.r,
                      child: Container(
                        padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
                        decoration: BoxDecoration(
                          color: Colors.red,
                          borderRadius: BorderRadius.circular(4.r),
                        ),
                        child: Text(
                          '${transfer.discountPercentage?.toStringAsFixed(0)}% OFF',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 12.sp,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ),
                ],
              ),
            ),
            
            // Content
            Padding(
              padding: EdgeInsets.all(16.r),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Title and rating
                  Row(
                    children: [
                      Expanded(
                        child: Text(
                          transfer.name,
                          style: TextStyle(
                            fontSize: 18.sp,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                      Row(
                        children: [
                          Icon(
                            Icons.star,
                            color: Colors.amber,
                            size: 16.r,
                          ),
                          SizedBox(width: 4.w),
                          Text(
                            transfer.rating.toString(),
                            style: TextStyle(
                              fontSize: 14.sp,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          SizedBox(width: 4.w),
                          Text(
                            '(${transfer.reviewCount})',
                            style: TextStyle(
                              fontSize: 12.sp,
                              color: Colors.grey[600],
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                  
                  SizedBox(height: 8.h),
                  
                  // Provider and location
                  Row(
                    children: [
                      Icon(
                        Icons.business,
                        size: 16.r,
                        color: Colors.grey[600],
                      ),
                      SizedBox(width: 4.w),
                      Text(
                        transfer.provider,
                        style: TextStyle(
                          fontSize: 14.sp,
                          color: Colors.grey[600],
                        ),
                      ),
                      SizedBox(width: 8.w),
                      Icon(
                        Icons.location_on,
                        size: 16.r,
                        color: Colors.grey[600],
                      ),
                      SizedBox(width: 4.w),
                      Text(
                        transfer.location,
                        style: TextStyle(
                          fontSize: 14.sp,
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                  
                  SizedBox(height: 12.h),
                  
                  // Vehicle info
                  Row(
                    children: [
                      Icon(
                        transfer.vehicleType.icon,
                        size: 16.r,
                        color: transfer.vehicleType.color,
                      ),
                      SizedBox(width: 4.w),
                      Text(
                        transfer.vehicleType.displayName,
                        style: TextStyle(
                          fontSize: 14.sp,
                          fontWeight: FontWeight.bold,
                          color: transfer.vehicleType.color,
                        ),
                      ),
                      SizedBox(width: 8.w),
                      Icon(
                        Icons.person,
                        size: 16.r,
                        color: Colors.grey[600],
                      ),
                      SizedBox(width: 4.w),
                      Text(
                        '${transfer.passengerCapacity} passengers',
                        style: TextStyle(
                          fontSize: 14.sp,
                          color: Colors.grey[600],
                        ),
                      ),
                      SizedBox(width: 8.w),
                      Icon(
                        Icons.luggage,
                        size: 16.r,
                        color: Colors.grey[600],
                      ),
                      SizedBox(width: 4.w),
                      Text(
                        '${transfer.luggageCapacity} luggage',
                        style: TextStyle(
                          fontSize: 14.sp,
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                  
                  SizedBox(height: 12.h),
                  
                  // Features
                  Wrap(
                    spacing: 8.w,
                    runSpacing: 8.h,
                    children: [
                      if (transfer.isPrivate)
                        _buildFeatureChip('Private', Icons.lock),
                      if (transfer.includesMeetAndGreet)
                        _buildFeatureChip('Meet & Greet', Icons.emoji_people),
                      if (transfer.includesFlightTracking)
                        _buildFeatureChip('Flight Tracking', Icons.flight),
                      if (transfer.includesWaitingTime)
                        _buildFeatureChip('${transfer.freeWaitingTimeMinutes} min wait', Icons.timer),
                      if (transfer.isAvailable24Hours)
                        _buildFeatureChip('24/7 Available', Icons.access_time),
                    ],
                  ),
                  
                  // Additional details
                  if (showFullDetails) ...[
                    SizedBox(height: 16.h),
                    Text(
                      transfer.description,
                      style: TextStyle(
                        fontSize: 14.sp,
                      ),
                    ),
                    SizedBox(height: 16.h),
                    _buildVehicleDetails(),
                    if (transfer.driver != null) ...[
                      SizedBox(height: 16.h),
                      _buildDriverDetails(),
                    ],
                    SizedBox(height: 16.h),
                    _buildAmenitiesSection(),
                    SizedBox(height: 16.h),
                    _buildPolicySection(),
                  ],
                  
                  SizedBox(height: 16.h),
                  
                  // Price and book button
                  Row(
                    children: [
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Price',
                            style: TextStyle(
                              fontSize: 14.sp,
                              color: Colors.grey[600],
                            ),
                          ),
                          Row(
                            children: [
                              Text(
                                '${transfer.currency} ${transfer.price.toStringAsFixed(2)}',
                                style: TextStyle(
                                  fontSize: 18.sp,
                                  fontWeight: FontWeight.bold,
                                  color: AppTheme.primaryColor,
                                ),
                              ),
                              if (transfer.isOnSale && transfer.originalPrice != null) ...[
                                SizedBox(width: 4.w),
                                Text(
                                  '${transfer.currency} ${transfer.originalPrice!.toStringAsFixed(2)}',
                                  style: TextStyle(
                                    fontSize: 14.sp,
                                    decoration: TextDecoration.lineThrough,
                                    color: Colors.grey[600],
                                  ),
                                ),
                              ],
                            ],
                          ),
                        ],
                      ),
                      const Spacer(),
                      if (onBook != null)
                        ElevatedButton(
                          onPressed: onBook,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: AppTheme.primaryColor,
                            foregroundColor: Colors.white,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8.r),
                            ),
                            padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
                          ),
                          child: Text(
                            'Book Now',
                            style: TextStyle(
                              fontSize: 14.sp,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
  
  /// Build a feature chip
  Widget _buildFeatureChip(String label, IconData icon) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
      decoration: BoxDecoration(
        color: Colors.grey[200],
        borderRadius: BorderRadius.circular(16.r),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: 12.r,
            color: Colors.grey[700],
          ),
          SizedBox(width: 4.w),
          Text(
            label,
            style: TextStyle(
              fontSize: 12.sp,
              color: Colors.grey[700],
            ),
          ),
        ],
      ),
    );
  }
  
  /// Build vehicle details section
  Widget _buildVehicleDetails() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Vehicle Details',
          style: TextStyle(
            fontSize: 16.sp,
            fontWeight: FontWeight.bold,
          ),
        ),
        SizedBox(height: 8.h),
        Row(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildDetailRow('Make & Model', '${transfer.vehicle.make} ${transfer.vehicle.model}'),
                  SizedBox(height: 4.h),
                  _buildDetailRow('Year', transfer.vehicle.year.toString()),
                  SizedBox(height: 4.h),
                  _buildDetailRow('Color', transfer.vehicle.color),
                ],
              ),
            ),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildDetailRow('Passenger Capacity', transfer.passengerCapacity.toString()),
                  SizedBox(height: 4.h),
                  _buildDetailRow('Luggage Capacity', transfer.luggageCapacity.toString()),
                  SizedBox(height: 4.h),
                  _buildDetailRow('Type', transfer.vehicleType.displayName),
                ],
              ),
            ),
          ],
        ),
        SizedBox(height: 8.h),
        Wrap(
          spacing: 8.w,
          runSpacing: 8.h,
          children: [
            if (transfer.vehicle.hasAirConditioning)
              _buildFeatureChip('A/C', Icons.ac_unit),
            if (transfer.vehicle.hasWifi)
              _buildFeatureChip('WiFi', Icons.wifi),
            if (transfer.vehicle.hasUsb)
              _buildFeatureChip('USB', Icons.usb),
            if (transfer.vehicle.hasChildSeat)
              _buildFeatureChip('Child Seat', Icons.child_care),
            if (transfer.vehicle.hasWheelchairAccess)
              _buildFeatureChip('Wheelchair Access', Icons.accessible),
          ],
        ),
      ],
    );
  }
  
  /// Build driver details section
  Widget _buildDriverDetails() {
    final driver = transfer.driver!;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Driver',
          style: TextStyle(
            fontSize: 16.sp,
            fontWeight: FontWeight.bold,
          ),
        ),
        SizedBox(height: 8.h),
        Row(
          children: [
            ClipRRect(
              borderRadius: BorderRadius.circular(24.r),
              child: Image.network(
                driver.photoUrl,
                width: 48.r,
                height: 48.r,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) {
                  return Container(
                    width: 48.r,
                    height: 48.r,
                    color: Colors.grey[300],
                    child: Icon(
                      Icons.person,
                      size: 24.r,
                      color: Colors.grey[500],
                    ),
                  );
                },
              ),
            ),
            SizedBox(width: 12.w),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    driver.name,
                    style: TextStyle(
                      fontSize: 16.sp,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Row(
                    children: [
                      Icon(
                        Icons.star,
                        color: Colors.amber,
                        size: 14.r,
                      ),
                      SizedBox(width: 4.w),
                      Text(
                        driver.rating.toString(),
                        style: TextStyle(
                          fontSize: 14.sp,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      SizedBox(width: 4.w),
                      Text(
                        '(${driver.reviewCount})',
                        style: TextStyle(
                          fontSize: 12.sp,
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            if (driver.isVerified)
              Container(
                padding: EdgeInsets.all(4.r),
                decoration: BoxDecoration(
                  color: Colors.green.withAlpha(30),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  Icons.verified,
                  color: Colors.green,
                  size: 16.r,
                ),
              ),
          ],
        ),
        SizedBox(height: 8.h),
        _buildDetailRow('Experience', '${driver.yearsOfExperience} years'),
        SizedBox(height: 4.h),
        _buildDetailRow('Languages', driver.languages.join(', ')),
      ],
    );
  }
  
  /// Build amenities section
  Widget _buildAmenitiesSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Amenities',
          style: TextStyle(
            fontSize: 16.sp,
            fontWeight: FontWeight.bold,
          ),
        ),
        SizedBox(height: 8.h),
        Wrap(
          spacing: 8.w,
          runSpacing: 8.h,
          children: transfer.amenities.map((amenity) {
            return Container(
              padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
              decoration: BoxDecoration(
                color: Colors.grey[200],
                borderRadius: BorderRadius.circular(16.r),
              ),
              child: Text(
                amenity,
                style: TextStyle(
                  fontSize: 14.sp,
                  color: Colors.grey[700],
                ),
              ),
            );
          }).toList(),
        ),
      ],
    );
  }
  
  /// Build policy section
  Widget _buildPolicySection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Policies',
          style: TextStyle(
            fontSize: 16.sp,
            fontWeight: FontWeight.bold,
          ),
        ),
        SizedBox(height: 8.h),
        _buildPolicyItem(
          'Cancellation',
          'Free cancellation up to ${transfer.freeCancellationHours} hours before pickup',
          Icons.event_busy,
        ),
        SizedBox(height: 4.h),
        _buildPolicyItem(
          'Waiting Time',
          '${transfer.freeWaitingTimeMinutes} minutes free waiting time',
          Icons.timer,
        ),
        SizedBox(height: 4.h),
        _buildPolicyItem(
          'Minimum Notice',
          'Book at least ${transfer.minimumNoticeHours} hours in advance',
          Icons.schedule,
        ),
      ],
    );
  }
  
  /// Build a policy item
  Widget _buildPolicyItem(String title, String description, IconData icon) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Icon(
          icon,
          size: 16.r,
          color: Colors.grey[600],
        ),
        SizedBox(width: 8.w),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: TextStyle(
                  fontSize: 14.sp,
                  fontWeight: FontWeight.bold,
                ),
              ),
              Text(
                description,
                style: TextStyle(
                  fontSize: 14.sp,
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
  
  /// Build a detail row with label and value
  Widget _buildDetailRow(String label, String value) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          width: 100.w,
          child: Text(
            '$label:',
            style: TextStyle(
              fontSize: 14.sp,
              color: Colors.grey[600],
            ),
          ),
        ),
        Expanded(
          child: Text(
            value,
            style: TextStyle(
              fontSize: 14.sp,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
      ],
    );
  }
}
