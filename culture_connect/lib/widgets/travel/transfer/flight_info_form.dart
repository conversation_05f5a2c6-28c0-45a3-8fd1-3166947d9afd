import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:culture_connect/models/travel/flight/flight_info.dart';
import 'package:culture_connect/services/travel/transfer/flight_integration_service.dart';
import 'package:culture_connect/theme/app_theme.dart';
import 'package:culture_connect/widgets/common/custom_text_field.dart';
import 'package:culture_connect/widgets/common/custom_date_picker.dart';

/// A form for entering flight information
class FlightInfoForm extends StatefulWidget {
  /// The initial flight number
  final String? initialFlightNumber;

  /// The initial flight date
  final DateTime? initialFlightDate;

  /// Callback when the form is submitted
  final Function(String flightNumber, DateTime flightDate) onSubmit;

  /// Callback when flight information is loaded
  final Function(FlightInfo flightInfo)? onFlightInfoLoaded;

  /// Creates a new flight info form
  const FlightInfoForm({
    super.key,
    this.initialFlightNumber,
    this.initialFlightDate,
    required this.onSubmit,
    this.onFlightInfoLoaded,
  });

  @override
  State<FlightInfoForm> createState() => _FlightInfoFormState();
}

class _FlightInfoFormState extends State<FlightInfoForm> {
  final _formKey = GlobalKey<FormState>();
  final _flightNumberController = TextEditingController();
  late DateTime _flightDate;

  bool _isLoading = false;
  FlightInfo? _flightInfo;
  String? _errorMessage;

  final _flightService = FlightIntegrationService();

  @override
  void initState() {
    super.initState();
    _flightNumberController.text = widget.initialFlightNumber ?? '';
    _flightDate =
        widget.initialFlightDate ?? DateTime.now().add(const Duration(days: 1));

    // Load flight info if initial values are provided
    if (widget.initialFlightNumber != null &&
        widget.initialFlightDate != null) {
      _loadFlightInfo();
    }
  }

  @override
  void dispose() {
    _flightNumberController.dispose();
    super.dispose();
  }

  /// Load flight information
  Future<void> _loadFlightInfo() async {
    if (_flightNumberController.text.isEmpty) {
      return;
    }

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final flightInfo = await _flightService.getFlightInfo(
        _flightNumberController.text,
        _flightDate,
      );

      setState(() {
        _flightInfo = flightInfo;
        _isLoading = false;
      });

      if (flightInfo != null && widget.onFlightInfoLoaded != null) {
        widget.onFlightInfoLoaded!(flightInfo);
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Error loading flight information';
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Form(
      key: _formKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Flight Information',
            style: TextStyle(
              fontSize: 16.sp,
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: 16.h),

          // Flight number
          CustomTextField(
            controller: _flightNumberController,
            label: 'Flight Number',
            hint: 'e.g. BA123',
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Please enter a flight number';
              }
              // Simple validation for flight number format
              if (!RegExp(r'^[A-Z0-9]{2,3}[0-9]{1,4}$').hasMatch(value)) {
                return 'Please enter a valid flight number (e.g. BA123)';
              }
              return null;
            },
          ),

          SizedBox(height: 16.h),

          // Flight date
          CustomDatePicker(
            labelText: 'Flight Date',
            initialDate: _flightDate,
            firstDate: DateTime.now().subtract(const Duration(days: 1)),
            lastDate: DateTime.now().add(const Duration(days: 365)),
            onDateSelected: (date) {
              setState(() {
                _flightDate = date;
              });
            },
          ),

          SizedBox(height: 16.h),

          // Search button
          SizedBox(
            width: double.infinity,
            child: ElevatedButton.icon(
              onPressed: _isLoading ? null : _loadFlightInfo,
              icon: _isLoading
                  ? SizedBox(
                      width: 20.r,
                      height: 20.r,
                      child: const CircularProgressIndicator(
                        color: Colors.white,
                        strokeWidth: 2,
                      ),
                    )
                  : const Icon(Icons.search),
              label: Text(_isLoading ? 'Searching...' : 'Search Flight'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.primaryColor,
                foregroundColor: Colors.white,
                padding: EdgeInsets.symmetric(vertical: 12.h),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8.r),
                ),
              ),
            ),
          ),

          // Error message
          if (_errorMessage != null) ...[
            SizedBox(height: 16.h),
            Container(
              padding: EdgeInsets.all(12.r),
              decoration: BoxDecoration(
                color: Colors.red[50],
                borderRadius: BorderRadius.circular(8.r),
                border: Border.all(color: Colors.red[300]!),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.error,
                    color: Colors.red,
                    size: 20.r,
                  ),
                  SizedBox(width: 8.w),
                  Expanded(
                    child: Text(
                      _errorMessage!,
                      style: TextStyle(
                        fontSize: 14.sp,
                        color: Colors.red,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],

          // Flight info
          if (_flightInfo != null) ...[
            SizedBox(height: 24.h),
            _buildFlightInfoCard(),
          ],

          SizedBox(height: 16.h),

          // Submit button
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: () {
                if (_formKey.currentState!.validate()) {
                  widget.onSubmit(_flightNumberController.text, _flightDate);
                }
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.primaryColor,
                foregroundColor: Colors.white,
                padding: EdgeInsets.symmetric(vertical: 12.h),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8.r),
                ),
              ),
              child: const Text('Confirm Flight Details'),
            ),
          ),
        ],
      ),
    );
  }

  /// Build the flight info card
  Widget _buildFlightInfoCard() {
    final flight = _flightInfo!;

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: Padding(
        padding: EdgeInsets.all(16.r),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Airline and flight number
            Row(
              children: [
                Icon(
                  Icons.flight,
                  size: 24.r,
                  color: Colors.blue,
                ),
                SizedBox(width: 8.w),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      flight.airlineName,
                      style: TextStyle(
                        fontSize: 16.sp,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Text(
                      'Flight ${flight.flightNumber}',
                      style: TextStyle(
                        fontSize: 14.sp,
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),
                const Spacer(),
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
                  decoration: BoxDecoration(
                    color: _getStatusColor(flight.status.displayName)
                        .withAlpha(30),
                    borderRadius: BorderRadius.circular(4.r),
                  ),
                  child: Text(
                    flight.status.displayName,
                    style: TextStyle(
                      fontSize: 12.sp,
                      fontWeight: FontWeight.bold,
                      color: _getStatusColor(flight.status.displayName),
                    ),
                  ),
                ),
              ],
            ),

            SizedBox(height: 16.h),

            // Flight route
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        flight.departureAirportCode,
                        style: TextStyle(
                          fontSize: 20.sp,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        flight.departureCity,
                        style: TextStyle(
                          fontSize: 14.sp,
                          color: Colors.grey[600],
                        ),
                      ),
                      if (flight.departureTerminal != null) ...[
                        SizedBox(height: 4.h),
                        Text(
                          flight.departureTerminal!,
                          style: TextStyle(
                            fontSize: 12.sp,
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
                Column(
                  children: [
                    Icon(
                      Icons.flight_takeoff,
                      size: 24.r,
                      color: Colors.grey[600],
                    ),
                    SizedBox(height: 4.h),
                    Container(
                      width: 60.w,
                      height: 1.h,
                      color: Colors.grey[300],
                    ),
                  ],
                ),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Text(
                        flight.arrivalAirportCode,
                        style: TextStyle(
                          fontSize: 20.sp,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        flight.arrivalCity,
                        style: TextStyle(
                          fontSize: 14.sp,
                          color: Colors.grey[600],
                        ),
                      ),
                      if (flight.arrivalTerminal != null) ...[
                        SizedBox(height: 4.h),
                        Text(
                          flight.arrivalTerminal!,
                          style: TextStyle(
                            fontSize: 12.sp,
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
              ],
            ),

            SizedBox(height: 16.h),

            // Flight times
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Departure',
                        style: TextStyle(
                          fontSize: 12.sp,
                          color: Colors.grey[600],
                        ),
                      ),
                      Text(
                        _formatDateTime(flight.scheduledDepartureTime),
                        style: TextStyle(
                          fontSize: 14.sp,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      if (flight.actualDepartureTime != null) ...[
                        SizedBox(height: 4.h),
                        Text(
                          'Actual: ${_formatDateTime(flight.actualDepartureTime!)}',
                          style: TextStyle(
                            fontSize: 12.sp,
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Text(
                        'Arrival',
                        style: TextStyle(
                          fontSize: 12.sp,
                          color: Colors.grey[600],
                        ),
                      ),
                      Text(
                        _formatDateTime(flight.scheduledArrival),
                        style: TextStyle(
                          fontSize: 14.sp,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      if (flight.actualArrival != null) ...[
                        SizedBox(height: 4.h),
                        Text(
                          'Actual: ${_formatDateTime(flight.actualArrival!)}',
                          style: TextStyle(
                            fontSize: 12.sp,
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// Format a date and time
  String _formatDateTime(DateTime dateTime) {
    final hour = dateTime.hour > 12
        ? dateTime.hour - 12
        : dateTime.hour == 0
            ? 12
            : dateTime.hour;
    final minute = dateTime.minute.toString().padLeft(2, '0');
    final period = dateTime.hour >= 12 ? 'PM' : 'AM';
    return '${dateTime.month}/${dateTime.day} $hour:$minute $period';
  }

  /// Get the color for a flight status
  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'scheduled':
      case 'on time':
        return Colors.green;
      case 'delayed':
        return Colors.orange;
      case 'cancelled':
        return Colors.red;
      case 'boarding':
        return Colors.blue;
      case 'departed':
      case 'in air':
        return Colors.purple;
      case 'arrived':
        return Colors.teal;
      default:
        return Colors.grey;
    }
  }
}
