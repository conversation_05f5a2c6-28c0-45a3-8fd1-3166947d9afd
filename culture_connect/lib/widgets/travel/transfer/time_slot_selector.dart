import 'package:flutter/material.dart';
import 'package:culture_connect/theme/app_colors.dart';
import 'package:culture_connect/theme/app_text_styles.dart';
import 'package:culture_connect/services/logging_service.dart';
import 'package:intl/intl.dart';

/// A widget for selecting a time slot
class TimeSlotSelector extends StatefulWidget {
  /// The initially selected time
  final TimeOfDay? initialTime;
  
  /// The available time slots
  final List<TimeOfDay> availableTimeSlots;
  
  /// Callback when a time slot is selected
  final Function(TimeOfDay timeSlot) onTimeSlotSelected;
  
  /// Creates a new time slot selector
  const TimeSlotSelector({
    super.key,
    this.initialTime,
    required this.availableTimeSlots,
    required this.onTimeSlotSelected,
  });

  @override
  State<TimeSlotSelector> createState() => _TimeSlotSelectorState();
}

class _TimeSlotSelectorState extends State<TimeSlotSelector> {
  TimeOfDay? _selectedTimeSlot;
  final LoggingService _loggingService = LoggingService();
  
  @override
  void initState() {
    super.initState();
    _selectedTimeSlot = widget.initialTime;
  }
  
  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Pickup Time',
          style: AppTextStyles.subtitle1.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        SizedBox(
          height: 60,
          child: ListView(
            scrollDirection: Axis.horizontal,
            children: widget.availableTimeSlots.map((timeSlot) {
              return _buildTimeSlotCard(timeSlot);
            }).toList(),
          ),
        ),
      ],
    );
  }
  
  /// Build a time slot card
  Widget _buildTimeSlotCard(TimeOfDay timeSlot) {
    final isSelected = _selectedTimeSlot?.hour == timeSlot.hour && 
                       _selectedTimeSlot?.minute == timeSlot.minute;
    
    return GestureDetector(
      onTap: () {
        try {
          setState(() {
            _selectedTimeSlot = timeSlot;
          });
          widget.onTimeSlotSelected(timeSlot);
        } catch (e, stackTrace) {
          _loggingService.error(
            'TimeSlotSelector',
            'Error selecting time slot',
            e,
            stackTrace,
          );
        }
      },
      child: Container(
        width: 80,
        margin: const EdgeInsets.only(right: 12),
        decoration: BoxDecoration(
          color: isSelected ? AppColors.primary.withAlpha(25) : AppColors.surface, // withAlpha(25) is equivalent to withOpacity(0.1)
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: isSelected ? AppColors.primary : AppColors.border,
            width: isSelected ? 2 : 1,
          ),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              _formatTimeOfDay(timeSlot),
              style: AppTextStyles.subtitle2.copyWith(
                fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                color: isSelected ? AppColors.primary : AppColors.textPrimary,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 4),
            Text(
              _getPeriod(timeSlot),
              style: AppTextStyles.caption.copyWith(
                color: AppColors.textSecondary,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
  
  /// Format a TimeOfDay to a string
  String _formatTimeOfDay(TimeOfDay timeOfDay) {
    final now = DateTime.now();
    final dateTime = DateTime(
      now.year,
      now.month,
      now.day,
      timeOfDay.hour,
      timeOfDay.minute,
    );
    
    return DateFormat.jm().format(dateTime).split(' ')[0];
  }
  
  /// Get the period (AM/PM) from a TimeOfDay
  String _getPeriod(TimeOfDay timeOfDay) {
    final now = DateTime.now();
    final dateTime = DateTime(
      now.year,
      now.month,
      now.day,
      timeOfDay.hour,
      timeOfDay.minute,
    );
    
    return DateFormat.jm().format(dateTime).split(' ')[1];
  }
}
