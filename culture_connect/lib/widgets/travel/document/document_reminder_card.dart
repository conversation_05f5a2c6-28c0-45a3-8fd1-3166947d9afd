import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:culture_connect/models/travel/document/document_reminder.dart';
import 'package:culture_connect/theme/app_theme.dart';

/// A card widget for displaying a document reminder
class DocumentReminderCard extends StatelessWidget {
  /// The document reminder to display
  final DocumentReminder reminder;
  
  /// Callback when the card is tapped
  final VoidCallback? onTap;
  
  /// Callback when the mark as read button is tapped
  final VoidCallback? onMarkAsRead;
  
  /// Callback when the dismiss button is tapped
  final VoidCallback? onDismiss;
  
  /// Creates a new document reminder card
  const DocumentReminderCard({
    super.key,
    required this.reminder,
    this.onTap,
    this.onMarkAsRead,
    this.onDismiss,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Card(
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12.r),
        ),
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12.r),
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                reminder.type.color.withAlpha(50),
                Colors.white,
              ],
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              Container(
                padding: EdgeInsets.all(16.r),
                decoration: BoxDecoration(
                  color: reminder.type.color.withAlpha(30),
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(12.r),
                    topRight: Radius.circular(12.r),
                  ),
                ),
                child: Row(
                  children: [
                    Icon(
                      reminder.type.icon,
                      color: reminder.type.color,
                      size: 24.r,
                    ),
                    SizedBox(width: 8.w),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            reminder.title,
                            style: TextStyle(
                              fontSize: 16.sp,
                              fontWeight: FontWeight.bold,
                              color: reminder.type.color,
                            ),
                          ),
                          Text(
                            reminder.type.displayName,
                            style: TextStyle(
                              fontSize: 14.sp,
                              color: Colors.grey[700],
                            ),
                          ),
                        ],
                      ),
                    ),
                    _buildStatusBadge(),
                  ],
                ),
              ),
              
              // Content
              Padding(
                padding: EdgeInsets.all(16.r),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Message
                    Text(
                      reminder.message,
                      style: TextStyle(
                        fontSize: 14.sp,
                      ),
                    ),
                    
                    SizedBox(height: 16.h),
                    
                    // Reminder date
                    Row(
                      children: [
                        Icon(
                          Icons.event,
                          size: 16.r,
                          color: Colors.grey[600],
                        ),
                        SizedBox(width: 8.w),
                        Text(
                          'Reminder Date:',
                          style: TextStyle(
                            fontSize: 12.sp,
                            color: Colors.grey[600],
                          ),
                        ),
                        SizedBox(width: 4.w),
                        Text(
                          _formatDate(reminder.reminderDate),
                          style: TextStyle(
                            fontSize: 14.sp,
                            fontWeight: FontWeight.bold,
                            color: reminder.isDue ? Colors.red : null,
                          ),
                        ),
                      ],
                    ),
                    
                    // Recurring info
                    if (reminder.isRecurring) ...[
                      SizedBox(height: 8.h),
                      Row(
                        children: [
                          Icon(
                            Icons.repeat,
                            size: 16.r,
                            color: Colors.grey[600],
                          ),
                          SizedBox(width: 8.w),
                          Text(
                            'Recurring:',
                            style: TextStyle(
                              fontSize: 12.sp,
                              color: Colors.grey[600],
                            ),
                          ),
                          SizedBox(width: 4.w),
                          Text(
                            reminder.recurrenceIntervalDays != null
                                ? 'Every ${reminder.recurrenceIntervalDays} days'
                                : 'Yes',
                            style: TextStyle(
                              fontSize: 14.sp,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ],
                ),
              ),
              
              // Actions
              if (!reminder.isRead || !reminder.isDismissed)
                Padding(
                  padding: EdgeInsets.all(8.r),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      if (!reminder.isRead && onMarkAsRead != null)
                        TextButton.icon(
                          onPressed: onMarkAsRead,
                          icon: const Icon(Icons.check),
                          label: const Text('Mark as Read'),
                          style: TextButton.styleFrom(
                            foregroundColor: AppTheme.primaryColor,
                          ),
                        ),
                      if (!reminder.isDismissed && onDismiss != null)
                        TextButton.icon(
                          onPressed: onDismiss,
                          icon: const Icon(Icons.close),
                          label: const Text('Dismiss'),
                          style: TextButton.styleFrom(
                            foregroundColor: Colors.red,
                          ),
                        ),
                    ],
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }
  
  /// Build the status badge
  Widget _buildStatusBadge() {
    if (reminder.isDismissed) {
      return Container(
        padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
        decoration: BoxDecoration(
          color: Colors.grey.withAlpha(50),
          borderRadius: BorderRadius.circular(12.r),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.close,
              color: Colors.grey,
              size: 12.r,
            ),
            SizedBox(width: 4.w),
            Text(
              'Dismissed',
              style: TextStyle(
                fontSize: 12.sp,
                fontWeight: FontWeight.bold,
                color: Colors.grey,
              ),
            ),
          ],
        ),
      );
    } else if (reminder.isRead) {
      return Container(
        padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
        decoration: BoxDecoration(
          color: Colors.green.withAlpha(50),
          borderRadius: BorderRadius.circular(12.r),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.check,
              color: Colors.green,
              size: 12.r,
            ),
            SizedBox(width: 4.w),
            Text(
              'Read',
              style: TextStyle(
                fontSize: 12.sp,
                fontWeight: FontWeight.bold,
                color: Colors.green,
              ),
            ),
          ],
        ),
      );
    } else if (reminder.isDue) {
      return Container(
        padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
        decoration: BoxDecoration(
          color: Colors.red.withAlpha(50),
          borderRadius: BorderRadius.circular(12.r),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.warning,
              color: Colors.red,
              size: 12.r,
            ),
            SizedBox(width: 4.w),
            Text(
              'Due',
              style: TextStyle(
                fontSize: 12.sp,
                fontWeight: FontWeight.bold,
                color: Colors.red,
              ),
            ),
          ],
        ),
      );
    } else {
      return Container(
        padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
        decoration: BoxDecoration(
          color: Colors.blue.withAlpha(50),
          borderRadius: BorderRadius.circular(12.r),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.schedule,
              color: Colors.blue,
              size: 12.r,
            ),
            SizedBox(width: 4.w),
            Text(
              'Upcoming',
              style: TextStyle(
                fontSize: 12.sp,
                fontWeight: FontWeight.bold,
                color: Colors.blue,
              ),
            ),
          ],
        ),
      );
    }
  }
  
  /// Format a date
  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final reminderDate = DateTime(date.year, date.month, date.day);
    
    final difference = reminderDate.difference(today).inDays;
    
    if (difference == 0) {
      return 'Today, ${_formatTime(date)}';
    } else if (difference == 1) {
      return 'Tomorrow, ${_formatTime(date)}';
    } else if (difference == -1) {
      return 'Yesterday, ${_formatTime(date)}';
    } else if (difference > 0 && difference < 7) {
      return '${_getDayOfWeek(date)}, ${_formatTime(date)}';
    } else {
      return '${date.month}/${date.day}/${date.year}, ${_formatTime(date)}';
    }
  }
  
  /// Format a time
  String _formatTime(DateTime date) {
    final hour = date.hour > 12 ? date.hour - 12 : date.hour == 0 ? 12 : date.hour;
    final minute = date.minute.toString().padLeft(2, '0');
    final period = date.hour >= 12 ? 'PM' : 'AM';
    return '$hour:$minute $period';
  }
  
  /// Get the day of the week
  String _getDayOfWeek(DateTime date) {
    switch (date.weekday) {
      case 1:
        return 'Monday';
      case 2:
        return 'Tuesday';
      case 3:
        return 'Wednesday';
      case 4:
        return 'Thursday';
      case 5:
        return 'Friday';
      case 6:
        return 'Saturday';
      case 7:
        return 'Sunday';
      default:
        return '';
    }
  }
}
