import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart' as gmaps;

import 'package:culture_connect/models/location/geo_location.dart';
import 'package:culture_connect/models/travel/travel.dart';
import 'package:culture_connect/services/logging_service.dart';
import 'package:culture_connect/providers/services_providers.dart';
import 'package:culture_connect/widgets/common/loading_indicator.dart';

/// A widget for displaying a hotel location on a map
class HotelMapView extends ConsumerStatefulWidget {
  /// The hotel to display
  final Hotel hotel;

  /// Whether to show nearby attractions
  final bool showNearbyAttractions;

  /// Creates a new hotel map view
  const HotelMapView({
    super.key,
    required this.hotel,
    this.showNearbyAttractions = true,
  });

  @override
  ConsumerState<HotelMapView> createState() => _HotelMapViewState();
}

class _HotelMapViewState extends ConsumerState<HotelMapView> {
  gmaps.GoogleMapController? _mapController;
  Set<gmaps.Marker> _markers = {};
  bool _isLoading = true;
  bool _hasError = false;
  String _errorMessage = '';
  String? _mapStyle;
  late LoggingService _loggingService;

  @override
  void initState() {
    super.initState();
    _loggingService = LoggingService();
    _loadMapStyle();
    _initializeMap();
  }

  @override
  void dispose() {
    _mapController?.dispose();
    super.dispose();
  }

  /// Convert GeoLocation to Google Maps LatLng
  gmaps.LatLng _geoLocationToLatLng(GeoLocation location) {
    return gmaps.LatLng(location.latitude, location.longitude);
  }

  Future<void> _initializeMap() async {
    setState(() {
      _isLoading = true;
      _hasError = false;
      _errorMessage = '';
    });

    try {
      // Convert GeoLocation to LatLng
      final hotelLatLng = _geoLocationToLatLng(widget.hotel.coordinates);

      // Create marker for hotel location
      final hotelMarker = gmaps.Marker(
        markerId: const gmaps.MarkerId('hotel'),
        position: hotelLatLng,
        infoWindow: gmaps.InfoWindow(
          title: widget.hotel.name,
          snippet: widget.hotel.location,
        ),
        icon: gmaps.BitmapDescriptor.defaultMarkerWithHue(
            gmaps.BitmapDescriptor.hueViolet),
      );

      setState(() {
        _markers = {hotelMarker};
      });

      // If showing nearby attractions, get them
      if (widget.showNearbyAttractions) {
        await _getNearbyAttractions();
      }

      _loggingService.debug(
        'HotelMapView',
        'Map initialized successfully for hotel: ${widget.hotel.id}',
      );
    } catch (e, stackTrace) {
      // Handle error
      _loggingService.error(
        'HotelMapView',
        'Error initializing map',
        e,
        stackTrace,
      );

      setState(() {
        _hasError = true;
        _errorMessage = 'Failed to load map. Please try again later.';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _getNearbyAttractions() async {
    try {
      final locationService = ref.read(locationServiceProvider);

      // Convert GeoLocation to LatLng
      final hotelLatLng = _geoLocationToLatLng(widget.hotel.coordinates);

      // Get nearby attractions using the getNearbyLandmarks method
      final attractions = await locationService.getNearbyLandmarks(
        hotelLatLng.latitude,
        hotelLatLng.longitude,
        1500, // 1.5km radius
      );

      if (attractions.isNotEmpty) {
        final attractionMarkers = attractions.map((attraction) {
          // Extract coordinates from the location map
          final lat = attraction.location['latitude'] as double;
          final lng = attraction.location['longitude'] as double;
          final position = gmaps.LatLng(lat, lng);

          // Get the first tag as the category, or use a default
          final category = attraction.tags.isNotEmpty
              ? attraction.tags.first
              : 'Tourist Attraction';

          return gmaps.Marker(
            markerId: gmaps.MarkerId('attraction_${attraction.id}'),
            position: position,
            infoWindow: gmaps.InfoWindow(
              title: attraction.name,
              snippet: category,
            ),
            icon: gmaps.BitmapDescriptor.defaultMarkerWithHue(
                gmaps.BitmapDescriptor.hueAzure),
          );
        }).toSet();

        setState(() {
          _markers = {..._markers, ...attractionMarkers};
        });

        _loggingService.debug(
          'HotelMapView',
          'Added ${attractionMarkers.length} nearby attractions to map',
        );
      }
    } catch (e, stackTrace) {
      // Handle error
      _loggingService.error(
        'HotelMapView',
        'Error getting nearby attractions',
        e,
        stackTrace,
      );
    }
  }

  void _onMapCreated(gmaps.GoogleMapController controller) {
    _mapController = controller;

    // Fit bounds to include all markers
    if (_markers.length > 1) {
      _fitBounds();
    }

    _loggingService.debug(
      'HotelMapView',
      'Map created successfully',
    );
  }

  Future<void> _loadMapStyle() async {
    try {
      // Load map style from assets
      final mapStyle = await rootBundle.loadString(
        'assets/map_styles/standard.json',
      );

      if (mapStyle.isNotEmpty) {
        setState(() {
          _mapStyle = mapStyle;
        });

        _loggingService.debug(
          'HotelMapView',
          'Map style loaded successfully',
        );
      }
    } catch (e, stackTrace) {
      // Handle error - continue without custom style
      _loggingService.error(
        'HotelMapView',
        'Error loading map style',
        e,
        stackTrace,
      );
    }
  }

  void _fitBounds() {
    if (_mapController == null || _markers.isEmpty) return;

    // Calculate bounds
    final bounds = _calculateBounds();

    // Fit bounds with padding
    _mapController!.animateCamera(
      gmaps.CameraUpdate.newLatLngBounds(
        bounds,
        50.0, // padding
      ),
    );

    _loggingService.debug(
      'HotelMapView',
      'Map bounds adjusted to fit all markers',
    );
  }

  gmaps.LatLngBounds _calculateBounds() {
    double minLat = 90.0;
    double maxLat = -90.0;
    double minLng = 180.0;
    double maxLng = -180.0;

    for (final marker in _markers) {
      final lat = marker.position.latitude;
      final lng = marker.position.longitude;

      minLat = lat < minLat ? lat : minLat;
      maxLat = lat > maxLat ? lat : maxLat;
      minLng = lng < minLng ? lng : minLng;
      maxLng = lng > maxLng ? lng : maxLng;
    }

    return gmaps.LatLngBounds(
      southwest: gmaps.LatLng(minLat, minLng),
      northeast: gmaps.LatLng(maxLat, maxLng),
    );
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const SizedBox(
        height: 200,
        child: Center(
          child: LoadingIndicator(),
        ),
      );
    }

    if (_hasError) {
      return SizedBox(
        height: 200,
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.error_outline,
                color: Theme.of(context).colorScheme.error,
                size: 48,
              ),
              const SizedBox(height: 16),
              Text(
                _errorMessage,
                style: Theme.of(context).textTheme.bodyMedium,
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      );
    }

    // Convert GeoLocation to LatLng for the map
    final hotelLatLng = _geoLocationToLatLng(widget.hotel.coordinates);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          height: 200,
          child: ClipRRect(
            borderRadius: BorderRadius.circular(8),
            child: gmaps.GoogleMap(
              initialCameraPosition: gmaps.CameraPosition(
                target: hotelLatLng,
                zoom: 14,
              ),
              markers: _markers,
              onMapCreated: _onMapCreated,
              myLocationEnabled: false,
              myLocationButtonEnabled: false,
              zoomControlsEnabled: false,
              mapToolbarEnabled: false,
              style: _mapStyle,
            ),
          ),
        ),
        const SizedBox(height: 8),

        // Location details
        Card(
          child: Padding(
            padding: const EdgeInsets.all(12),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildLocationDetail(
                  Icons.location_city,
                  'Distance from City Center',
                  widget.hotel.formattedDistanceFromCityCenter,
                ),
                const SizedBox(height: 12),
                _buildLocationDetail(
                  Icons.flight,
                  'Distance from Airport',
                  '${widget.hotel.formattedDistanceFromAirport} (${widget.hotel.nearestAirport})',
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildLocationDetail(
    IconData icon,
    String label,
    String value,
  ) {
    final theme = Theme.of(context);

    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Icon(
          icon,
          color: theme.colorScheme.primary,
          size: 24,
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: theme.textTheme.bodySmall?.copyWith(
                  color: theme.colorScheme.onSurfaceVariant,
                ),
              ),
              const SizedBox(height: 2),
              Text(
                value,
                style: theme.textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
