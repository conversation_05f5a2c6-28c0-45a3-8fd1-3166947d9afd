import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:culture_connect/models/travel/insurance/insurance.dart';

/// A card widget for displaying an insurance policy
class InsurancePolicyCard extends StatelessWidget {
  /// The insurance policy to display
  final InsurancePolicy policy;

  /// Whether to show the policy details
  final bool showDetails;

  /// Whether to show the policy status
  final bool showStatus;

  /// Whether to show the policy provider
  final bool showProvider;

  /// Whether to show the policy coverage
  final bool showCoverage;

  /// Whether to show the policy dates
  final bool showDates;

  /// Whether to show the policy price
  final bool showPrice;

  /// Whether to show the policy actions
  final bool showActions;

  /// Whether to use a horizontal layout
  final bool isHorizontal;

  /// Callback when the card is tapped
  final VoidCallback? onTap;

  /// Callback when the view details button is tapped
  final VoidCallback? onViewDetails;

  /// Callback when the purchase button is tapped
  final VoidCallback? onPurchase;

  /// Creates a new insurance policy card
  const InsurancePolicyCard({
    super.key,
    required this.policy,
    this.showDetails = true,
    this.showStatus = true,
    this.showProvider = true,
    this.showCoverage = false,
    this.showDates = false,
    this.showPrice = true,
    this.showActions = true,
    this.isHorizontal = false,
    this.onTap,
    this.onViewDetails,
    this.onPurchase,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    if (isHorizontal) {
      return _buildHorizontalCard(theme);
    }

    return _buildVerticalCard(theme);
  }

  Widget _buildVerticalCard(ThemeData theme) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12.r),
        child: Padding(
          padding: EdgeInsets.all(16.r),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header with type icon and name
              Row(
                children: [
                  Container(
                    padding: EdgeInsets.all(8.r),
                    decoration: BoxDecoration(
                      color: policy.type.icon == Icons.flight
                          ? Colors.blue.withAlpha(25)
                          : policy.type.icon == Icons.calendar_month
                              ? Colors.purple.withAlpha(25)
                              : policy.type.icon == Icons.backpack
                                  ? Colors.orange.withAlpha(25)
                                  : Colors.green.withAlpha(25),
                      borderRadius: BorderRadius.circular(8.r),
                    ),
                    child: Icon(
                      policy.type.icon,
                      color: policy.type.icon == Icons.flight
                          ? Colors.blue
                          : policy.type.icon == Icons.calendar_month
                              ? Colors.purple
                              : policy.type.icon == Icons.backpack
                                  ? Colors.orange
                                  : Colors.green,
                      size: 24.r,
                    ),
                  ),
                  SizedBox(width: 12.w),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          policy.name,
                          style: theme.textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                        SizedBox(height: 4.h),
                        Text(
                          policy.type.displayName,
                          style: theme.textTheme.bodySmall?.copyWith(
                            color: theme.colorScheme.onSurfaceVariant,
                          ),
                        ),
                      ],
                    ),
                  ),
                  if (showStatus && policy.policyNumber != null)
                    Container(
                      padding: EdgeInsets.symmetric(
                        horizontal: 8.w,
                        vertical: 4.h,
                      ),
                      decoration: BoxDecoration(
                        color: policy.status.color.withAlpha(25),
                        borderRadius: BorderRadius.circular(4.r),
                      ),
                      child: Text(
                        policy.status.displayName,
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: policy.status.color,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                ],
              ),

              if (showDetails) ...[
                SizedBox(height: 16.h),
                Text(
                  policy.description,
                  style: theme.textTheme.bodyMedium,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ],

              if (showProvider) ...[
                SizedBox(height: 16.h),
                Row(
                  children: [
                    ClipRRect(
                      borderRadius: BorderRadius.circular(4.r),
                      child: Image.network(
                        policy.provider.logoUrl,
                        width: 24.r,
                        height: 24.r,
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) {
                          return Container(
                            width: 24.r,
                            height: 24.r,
                            color: Colors.grey[300],
                            child: Icon(
                              Icons.business,
                              size: 16.r,
                              color: Colors.grey[600],
                            ),
                          );
                        },
                      ),
                    ),
                    SizedBox(width: 8.w),
                    Text(
                      policy.provider.name,
                      style: theme.textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const Spacer(),
                    if (policy.provider.rating > 0) ...[
                      Icon(
                        Icons.star,
                        size: 16.r,
                        color: Colors.amber,
                      ),
                      SizedBox(width: 4.w),
                      Text(
                        policy.provider.rating.toString(),
                        style: theme.textTheme.bodySmall?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ],
                ),
              ],

              if (showCoverage) ...[
                SizedBox(height: 16.h),
                Text(
                  'Key Coverage',
                  style: theme.textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(height: 8.h),
                Wrap(
                  spacing: 8.w,
                  runSpacing: 8.h,
                  children: policy.coverages
                      .where((coverage) => coverage.isIncluded)
                      .take(3)
                      .map((coverage) => Chip(
                            label: Text(
                              coverage.type.displayName,
                              style: theme.textTheme.bodySmall?.copyWith(
                                color: theme.colorScheme.onPrimaryContainer,
                              ),
                            ),
                            backgroundColor: theme.colorScheme.primaryContainer,
                            padding: EdgeInsets.zero,
                            materialTapTargetSize:
                                MaterialTapTargetSize.shrinkWrap,
                          ))
                      .toList(),
                ),
              ],

              if (showDates &&
                  policy.startDate != null &&
                  policy.endDate != null) ...[
                SizedBox(height: 16.h),
                Row(
                  children: [
                    Icon(
                      Icons.date_range,
                      size: 16.r,
                      color: theme.colorScheme.onSurfaceVariant,
                    ),
                    SizedBox(width: 8.w),
                    Text(
                      '${_formatDate(policy.startDate!)} - ${_formatDate(policy.endDate!)}',
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: theme.colorScheme.onSurfaceVariant,
                      ),
                    ),
                    if (policy.durationDays != null) ...[
                      const Spacer(),
                      Text(
                        policy.formattedDuration!,
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: theme.colorScheme.onSurfaceVariant,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ],
                ),
              ],

              if (showPrice) ...[
                SizedBox(height: 16.h),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      policy.formattedPrice,
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: theme.colorScheme.primary,
                      ),
                    ),
                    if (policy.policyNumber == null)
                      Text(
                        'per person',
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: theme.colorScheme.onSurfaceVariant,
                        ),
                      ),
                  ],
                ),
              ],

              if (showActions) ...[
                SizedBox(height: 16.h),
                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    if (onViewDetails != null)
                      TextButton(
                        onPressed: onViewDetails,
                        child: const Text('View Details'),
                      ),
                    if (onPurchase != null) ...[
                      SizedBox(width: 8.w),
                      ElevatedButton(
                        onPressed: onPurchase,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: theme.colorScheme.primary,
                          foregroundColor: theme.colorScheme.onPrimary,
                        ),
                        child: const Text('Purchase'),
                      ),
                    ],
                  ],
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHorizontalCard(ThemeData theme) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12.r),
        child: Padding(
          padding: EdgeInsets.all(16.r),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Type icon
              Container(
                padding: EdgeInsets.all(12.r),
                decoration: BoxDecoration(
                  color: policy.type.icon == Icons.flight
                      ? Colors.blue.withAlpha(25)
                      : policy.type.icon == Icons.calendar_month
                          ? Colors.purple.withAlpha(25)
                          : policy.type.icon == Icons.backpack
                              ? Colors.orange.withAlpha(25)
                              : Colors.green.withAlpha(25),
                  borderRadius: BorderRadius.circular(8.r),
                ),
                child: Icon(
                  policy.type.icon,
                  color: policy.type.icon == Icons.flight
                      ? Colors.blue
                      : policy.type.icon == Icons.calendar_month
                          ? Colors.purple
                          : policy.type.icon == Icons.backpack
                              ? Colors.orange
                              : Colors.green,
                  size: 24.r,
                ),
              ),
              SizedBox(width: 16.w),

              // Content
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Name and type
                    Row(
                      children: [
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                policy.name,
                                style: theme.textTheme.titleMedium?.copyWith(
                                  fontWeight: FontWeight.bold,
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                              SizedBox(height: 4.h),
                              Text(
                                policy.type.displayName,
                                style: theme.textTheme.bodySmall?.copyWith(
                                  color: theme.colorScheme.onSurfaceVariant,
                                ),
                              ),
                            ],
                          ),
                        ),
                        if (showStatus && policy.policyNumber != null)
                          Container(
                            padding: EdgeInsets.symmetric(
                              horizontal: 8.w,
                              vertical: 4.h,
                            ),
                            decoration: BoxDecoration(
                              color: policy.status.color.withAlpha(25),
                              borderRadius: BorderRadius.circular(4.r),
                            ),
                            child: Text(
                              policy.status.displayName,
                              style: theme.textTheme.bodySmall?.copyWith(
                                color: policy.status.color,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                      ],
                    ),

                    if (showProvider) ...[
                      SizedBox(height: 8.h),
                      Row(
                        children: [
                          ClipRRect(
                            borderRadius: BorderRadius.circular(4.r),
                            child: Image.network(
                              policy.provider.logoUrl,
                              width: 16.r,
                              height: 16.r,
                              fit: BoxFit.cover,
                              errorBuilder: (context, error, stackTrace) {
                                return Container(
                                  width: 16.r,
                                  height: 16.r,
                                  color: Colors.grey[300],
                                  child: Icon(
                                    Icons.business,
                                    size: 12.r,
                                    color: Colors.grey[600],
                                  ),
                                );
                              },
                            ),
                          ),
                          SizedBox(width: 4.w),
                          Text(
                            policy.provider.name,
                            style: theme.textTheme.bodySmall?.copyWith(
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                    ],

                    if (showPrice) ...[
                      SizedBox(height: 8.h),
                      Text(
                        policy.formattedPrice,
                        style: theme.textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: theme.colorScheme.primary,
                        ),
                      ),
                    ],

                    if (showActions &&
                        (onViewDetails != null || onPurchase != null)) ...[
                      SizedBox(height: 8.h),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: [
                          if (onViewDetails != null)
                            TextButton(
                              onPressed: onViewDetails,
                              style: TextButton.styleFrom(
                                padding: EdgeInsets.symmetric(
                                  horizontal: 8.w,
                                  vertical: 4.h,
                                ),
                                minimumSize: Size.zero,
                                tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                              ),
                              child: const Text('Details'),
                            ),
                          if (onPurchase != null) ...[
                            SizedBox(width: 8.w),
                            ElevatedButton(
                              onPressed: onPurchase,
                              style: ElevatedButton.styleFrom(
                                backgroundColor: theme.colorScheme.primary,
                                foregroundColor: theme.colorScheme.onPrimary,
                                padding: EdgeInsets.symmetric(
                                  horizontal: 8.w,
                                  vertical: 4.h,
                                ),
                                minimumSize: Size.zero,
                                tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                              ),
                              child: const Text('Purchase'),
                            ),
                          ],
                        ],
                      ),
                    ],
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}
