import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:culture_connect/models/travel/insurance/insurance.dart';

/// A card widget for displaying an insurance provider
class InsuranceProviderCard extends StatelessWidget {
  /// The insurance provider to display
  final InsuranceProvider provider;

  /// Whether to show the provider details
  final bool showDetails;

  /// Whether to show the provider rating
  final bool showRating;

  /// Whether to show the provider contact information
  final bool showContact;

  /// Whether to show the provider countries
  final bool showCountries;

  /// Whether to show the provider actions
  final bool showActions;

  /// Whether to use a compact layout
  final bool isCompact;

  /// Callback when the card is tapped
  final VoidCallback? onTap;

  /// Callback when the view policies button is tapped
  final VoidCallback? onViewPolicies;

  /// Callback when the contact button is tapped
  final VoidCallback? onContact;

  /// Creates a new insurance provider card
  const InsuranceProviderCard({
    super.key,
    required this.provider,
    this.showDetails = true,
    this.showRating = true,
    this.showContact = false,
    this.showCountries = false,
    this.showActions = true,
    this.isCompact = false,
    this.onTap,
    this.onViewPolicies,
    this.onContact,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    if (isCompact) {
      return _buildCompactCard(theme);
    }

    return _buildFullCard(theme);
  }

  Widget _buildFullCard(ThemeData theme) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12.r),
        child: Padding(
          padding: EdgeInsets.all(16.r),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header with logo and name
              Row(
                children: [
                  ClipRRect(
                    borderRadius: BorderRadius.circular(8.r),
                    child: Image.network(
                      provider.logoUrl,
                      width: 48.r,
                      height: 48.r,
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) {
                        return Container(
                          width: 48.r,
                          height: 48.r,
                          color: provider.primaryColor.withAlpha(25),
                          child: Icon(
                            Icons.business,
                            size: 24.r,
                            color: provider.primaryColor,
                          ),
                        );
                      },
                    ),
                  ),
                  SizedBox(width: 16.w),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          provider.name,
                          style: theme.textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                        SizedBox(height: 4.h),
                        Row(
                          children: [
                            if (provider.isPartner)
                              Container(
                                padding: EdgeInsets.symmetric(
                                  horizontal: 6.w,
                                  vertical: 2.h,
                                ),
                                decoration: BoxDecoration(
                                  color:
                                      theme.colorScheme.primary.withAlpha(25),
                                  borderRadius: BorderRadius.circular(4.r),
                                ),
                                child: Text(
                                  'Partner',
                                  style: theme.textTheme.bodySmall?.copyWith(
                                    color: theme.colorScheme.primary,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                            if (provider.isPartner && provider.isFeatured)
                              SizedBox(width: 8.w),
                            if (provider.isFeatured)
                              Container(
                                padding: EdgeInsets.symmetric(
                                  horizontal: 6.w,
                                  vertical: 2.h,
                                ),
                                decoration: BoxDecoration(
                                  color: Colors.amber.withAlpha(25),
                                  borderRadius: BorderRadius.circular(4.r),
                                ),
                                child: Text(
                                  'Featured',
                                  style: theme.textTheme.bodySmall?.copyWith(
                                    color: Colors.amber[800],
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                          ],
                        ),
                      ],
                    ),
                  ),
                  if (showRating && provider.rating > 0) ...[
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        Row(
                          children: [
                            Icon(
                              Icons.star,
                              size: 16.r,
                              color: Colors.amber,
                            ),
                            SizedBox(width: 4.w),
                            Text(
                              provider.rating.toString(),
                              style: theme.textTheme.bodyMedium?.copyWith(
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                        SizedBox(height: 4.h),
                        Text(
                          '${provider.reviewCount} reviews',
                          style: theme.textTheme.bodySmall?.copyWith(
                            color: theme.colorScheme.onSurfaceVariant,
                          ),
                        ),
                      ],
                    ),
                  ],
                ],
              ),

              if (showDetails) ...[
                SizedBox(height: 16.h),
                Text(
                  provider.description,
                  style: theme.textTheme.bodyMedium,
                ),
              ],

              if (showContact) ...[
                SizedBox(height: 16.h),
                Row(
                  children: [
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Icon(
                                Icons.phone,
                                size: 16.r,
                                color: theme.colorScheme.onSurfaceVariant,
                              ),
                              SizedBox(width: 8.w),
                              Expanded(
                                child: Text(
                                  provider.phoneNumber,
                                  style: theme.textTheme.bodySmall?.copyWith(
                                    color: theme.colorScheme.onSurfaceVariant,
                                  ),
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                            ],
                          ),
                          SizedBox(height: 8.h),
                          Row(
                            children: [
                              Icon(
                                Icons.email,
                                size: 16.r,
                                color: theme.colorScheme.onSurfaceVariant,
                              ),
                              SizedBox(width: 8.w),
                              Expanded(
                                child: Text(
                                  provider.email,
                                  style: theme.textTheme.bodySmall?.copyWith(
                                    color: theme.colorScheme.onSurfaceVariant,
                                  ),
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                    SizedBox(width: 16.w),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Icon(
                                Icons.language,
                                size: 16.r,
                                color: theme.colorScheme.onSurfaceVariant,
                              ),
                              SizedBox(width: 8.w),
                              Expanded(
                                child: Text(
                                  provider.websiteUrl,
                                  style: theme.textTheme.bodySmall?.copyWith(
                                    color: theme.colorScheme.onSurfaceVariant,
                                  ),
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ],

              if (showCountries && provider.countries.isNotEmpty) ...[
                SizedBox(height: 16.h),
                Text(
                  'Available in ${provider.countries.length} countries',
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.onSurfaceVariant,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(height: 8.h),
                Wrap(
                  spacing: 8.w,
                  runSpacing: 8.h,
                  children: provider.countries
                      .take(5)
                      .map((country) => Chip(
                            label: Text(
                              country,
                              style: theme.textTheme.bodySmall?.copyWith(
                                color: theme.colorScheme.onSurfaceVariant,
                              ),
                            ),
                            backgroundColor:
                                theme.colorScheme.surfaceContainerLow,
                            padding: EdgeInsets.zero,
                            materialTapTargetSize:
                                MaterialTapTargetSize.shrinkWrap,
                          ))
                      .toList(),
                ),
                if (provider.countries.length > 5) ...[
                  SizedBox(height: 4.h),
                  Text(
                    '+ ${provider.countries.length - 5} more',
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: theme.colorScheme.onSurfaceVariant,
                    ),
                  ),
                ],
              ],

              if (showActions) ...[
                SizedBox(height: 16.h),
                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    if (onContact != null)
                      TextButton.icon(
                        onPressed: onContact,
                        icon: Icon(
                          Icons.phone,
                          size: 16.r,
                        ),
                        label: const Text('Contact'),
                      ),
                    if (onViewPolicies != null) ...[
                      SizedBox(width: 8.w),
                      ElevatedButton.icon(
                        onPressed: onViewPolicies,
                        icon: Icon(
                          Icons.policy,
                          size: 16.r,
                        ),
                        label: const Text('View Policies'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: theme.colorScheme.primary,
                          foregroundColor: theme.colorScheme.onPrimary,
                        ),
                      ),
                    ],
                  ],
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCompactCard(ThemeData theme) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12.r),
        child: Padding(
          padding: EdgeInsets.all(12.r),
          child: Row(
            children: [
              ClipRRect(
                borderRadius: BorderRadius.circular(6.r),
                child: Image.network(
                  provider.logoUrl,
                  width: 40.r,
                  height: 40.r,
                  fit: BoxFit.cover,
                  errorBuilder: (context, error, stackTrace) {
                    return Container(
                      width: 40.r,
                      height: 40.r,
                      color: provider.primaryColor.withAlpha(25),
                      child: Icon(
                        Icons.business,
                        size: 20.r,
                        color: provider.primaryColor,
                      ),
                    );
                  },
                ),
              ),
              SizedBox(width: 12.w),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      provider.name,
                      style: theme.textTheme.titleSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    SizedBox(height: 4.h),
                    Row(
                      children: [
                        if (showRating && provider.rating > 0) ...[
                          Icon(
                            Icons.star,
                            size: 14.r,
                            color: Colors.amber,
                          ),
                          SizedBox(width: 4.w),
                          Text(
                            provider.rating.toString(),
                            style: theme.textTheme.bodySmall?.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          SizedBox(width: 8.w),
                        ],
                        if (provider.isPartner)
                          Container(
                            padding: EdgeInsets.symmetric(
                              horizontal: 4.w,
                              vertical: 1.h,
                            ),
                            decoration: BoxDecoration(
                              color: theme.colorScheme.primary.withAlpha(25),
                              borderRadius: BorderRadius.circular(2.r),
                            ),
                            child: Text(
                              'Partner',
                              style: theme.textTheme.bodySmall?.copyWith(
                                color: theme.colorScheme.primary,
                                fontWeight: FontWeight.bold,
                                fontSize: 10.sp,
                              ),
                            ),
                          ),
                      ],
                    ),
                  ],
                ),
              ),
              if (onViewPolicies != null)
                IconButton(
                  onPressed: onViewPolicies,
                  icon: Icon(
                    Icons.arrow_forward_ios,
                    size: 16.r,
                  ),
                  constraints: BoxConstraints.tightFor(
                    width: 32.r,
                    height: 32.r,
                  ),
                  padding: EdgeInsets.zero,
                ),
            ],
          ),
        ),
      ),
    );
  }
}
