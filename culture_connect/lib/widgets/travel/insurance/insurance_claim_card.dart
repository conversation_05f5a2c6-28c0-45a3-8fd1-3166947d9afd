import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:culture_connect/models/travel/insurance/insurance.dart';

/// A card widget for displaying an insurance claim
class InsuranceClaimCard extends StatelessWidget {
  /// The insurance claim to display
  final InsuranceClaim claim;

  /// Whether to show the claim details
  final bool showDetails;

  /// Whether to show the claim status
  final bool showStatus;

  /// Whether to show the claim policy
  final bool showPolicy;

  /// Whether to show the claim dates
  final bool showDates;

  /// Whether to show the claim amount
  final bool showAmount;

  /// Whether to show the claim actions
  final bool showActions;

  /// Whether to use a horizontal layout
  final bool isHorizontal;

  /// Callback when the card is tapped
  final VoidCallback? onTap;

  /// Callback when the view details button is tapped
  final VoidCallback? onViewDetails;

  /// Callback when the update button is tapped
  final VoidCallback? onUpdate;

  /// Creates a new insurance claim card
  const InsuranceClaimCard({
    super.key,
    required this.claim,
    this.showDetails = true,
    this.showStatus = true,
    this.showPolicy = true,
    this.showDates = true,
    this.showAmount = true,
    this.showActions = true,
    this.isHorizontal = false,
    this.onTap,
    this.onViewDetails,
    this.onUpdate,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    if (isHorizontal) {
      return _buildHorizontalCard(theme);
    }

    return _buildVerticalCard(theme);
  }

  Widget _buildVerticalCard(ThemeData theme) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12.r),
        child: Padding(
          padding: EdgeInsets.all(16.r),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header with coverage type icon and reference number
              Row(
                children: [
                  Container(
                    padding: EdgeInsets.all(8.r),
                    decoration: BoxDecoration(
                      color: claim.coverageType.icon == Icons.medical_services
                          ? Colors.red.withAlpha(25)
                          : claim.coverageType.icon == Icons.luggage
                              ? Colors.blue.withAlpha(25)
                              : claim.coverageType.icon == Icons.flight_land
                                  ? Colors.orange.withAlpha(25)
                                  : Colors.green.withAlpha(25),
                      borderRadius: BorderRadius.circular(8.r),
                    ),
                    child: Icon(
                      claim.coverageType.icon,
                      color: claim.coverageType.icon == Icons.medical_services
                          ? Colors.red
                          : claim.coverageType.icon == Icons.luggage
                              ? Colors.blue
                              : claim.coverageType.icon == Icons.flight_land
                                  ? Colors.orange
                                  : Colors.green,
                      size: 24.r,
                    ),
                  ),
                  SizedBox(width: 12.w),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          claim.coverageType.displayName,
                          style: theme.textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                        SizedBox(height: 4.h),
                        Text(
                          'Ref: ${claim.referenceNumber}',
                          style: theme.textTheme.bodySmall?.copyWith(
                            color: theme.colorScheme.onSurfaceVariant,
                          ),
                        ),
                      ],
                    ),
                  ),
                  if (showStatus)
                    Container(
                      padding: EdgeInsets.symmetric(
                        horizontal: 8.w,
                        vertical: 4.h,
                      ),
                      decoration: BoxDecoration(
                        color: claim.status.color.withAlpha(25),
                        borderRadius: BorderRadius.circular(4.r),
                      ),
                      child: Text(
                        claim.status.displayName,
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: claim.status.color,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                ],
              ),

              if (showDetails) ...[
                SizedBox(height: 16.h),
                Text(
                  claim.incidentDescription,
                  style: theme.textTheme.bodyMedium,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ],

              if (showPolicy) ...[
                SizedBox(height: 16.h),
                Row(
                  children: [
                    Icon(
                      Icons.policy,
                      size: 16.r,
                      color: theme.colorScheme.onSurfaceVariant,
                    ),
                    SizedBox(width: 8.w),
                    Expanded(
                      child: Text(
                        'Policy: ${claim.policy.name} (${claim.policy.policyNumber})',
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: theme.colorScheme.onSurfaceVariant,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ),
              ],

              if (showDates) ...[
                SizedBox(height: 8.h),
                Row(
                  children: [
                    Icon(
                      Icons.event,
                      size: 16.r,
                      color: theme.colorScheme.onSurfaceVariant,
                    ),
                    SizedBox(width: 8.w),
                    Text(
                      'Incident: ${_formatDate(claim.incidentDate)}',
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: theme.colorScheme.onSurfaceVariant,
                      ),
                    ),
                    const Spacer(),
                    Text(
                      'Submitted: ${_formatDate(claim.submittedDate)}',
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: theme.colorScheme.onSurfaceVariant,
                      ),
                    ),
                  ],
                ),
              ],

              if (showAmount) ...[
                SizedBox(height: 16.h),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Claimed Amount',
                          style: theme.textTheme.bodySmall?.copyWith(
                            color: theme.colorScheme.onSurfaceVariant,
                          ),
                        ),
                        SizedBox(height: 4.h),
                        Text(
                          claim.formattedClaimAmount,
                          style: theme.textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: theme.colorScheme.primary,
                          ),
                        ),
                      ],
                    ),
                    if (claim.approvedAmount != null)
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.end,
                        children: [
                          Text(
                            'Approved Amount',
                            style: theme.textTheme.bodySmall?.copyWith(
                              color: theme.colorScheme.onSurfaceVariant,
                            ),
                          ),
                          SizedBox(height: 4.h),
                          Text(
                            claim.formattedApprovedAmount!,
                            style: theme.textTheme.titleMedium?.copyWith(
                              fontWeight: FontWeight.bold,
                              color: Colors.green,
                            ),
                          ),
                        ],
                      ),
                  ],
                ),
              ],

              if (claim.status == InsuranceClaimStatus.infoRequested) ...[
                SizedBox(height: 16.h),
                Container(
                  padding: EdgeInsets.all(12.r),
                  decoration: BoxDecoration(
                    color: Colors.amber.withAlpha(25),
                    borderRadius: BorderRadius.circular(8.r),
                    border: Border.all(
                      color: Colors.amber,
                      width: 1,
                    ),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.info_outline,
                        color: Colors.amber,
                        size: 20.r,
                      ),
                      SizedBox(width: 8.w),
                      Expanded(
                        child: Text(
                          claim.additionalInfoRequested ??
                              'Additional information requested',
                          style: theme.textTheme.bodySmall?.copyWith(
                            color: Colors.amber[800],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],

              if (showActions) ...[
                SizedBox(height: 16.h),
                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    if (onViewDetails != null)
                      TextButton(
                        onPressed: onViewDetails,
                        child: const Text('View Details'),
                      ),
                    if (onUpdate != null &&
                        (claim.status == InsuranceClaimStatus.infoRequested ||
                            claim.status ==
                                InsuranceClaimStatus.submitted)) ...[
                      SizedBox(width: 8.w),
                      ElevatedButton(
                        onPressed: onUpdate,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: theme.colorScheme.primary,
                          foregroundColor: theme.colorScheme.onPrimary,
                        ),
                        child: const Text('Update'),
                      ),
                    ],
                  ],
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHorizontalCard(ThemeData theme) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12.r),
        child: Padding(
          padding: EdgeInsets.all(16.r),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Coverage type icon
              Container(
                padding: EdgeInsets.all(12.r),
                decoration: BoxDecoration(
                  color: claim.coverageType.icon == Icons.medical_services
                      ? Colors.red.withAlpha(25)
                      : claim.coverageType.icon == Icons.luggage
                          ? Colors.blue.withAlpha(25)
                          : claim.coverageType.icon == Icons.flight_land
                              ? Colors.orange.withAlpha(25)
                              : Colors.green.withAlpha(25),
                  borderRadius: BorderRadius.circular(8.r),
                ),
                child: Icon(
                  claim.coverageType.icon,
                  color: claim.coverageType.icon == Icons.medical_services
                      ? Colors.red
                      : claim.coverageType.icon == Icons.luggage
                          ? Colors.blue
                          : claim.coverageType.icon == Icons.flight_land
                              ? Colors.orange
                              : Colors.green,
                  size: 24.r,
                ),
              ),
              SizedBox(width: 16.w),

              // Content
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Coverage type and status
                    Row(
                      children: [
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                claim.coverageType.displayName,
                                style: theme.textTheme.titleMedium?.copyWith(
                                  fontWeight: FontWeight.bold,
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                              SizedBox(height: 4.h),
                              Text(
                                'Ref: ${claim.referenceNumber}',
                                style: theme.textTheme.bodySmall?.copyWith(
                                  color: theme.colorScheme.onSurfaceVariant,
                                ),
                              ),
                            ],
                          ),
                        ),
                        if (showStatus)
                          Container(
                            padding: EdgeInsets.symmetric(
                              horizontal: 8.w,
                              vertical: 4.h,
                            ),
                            decoration: BoxDecoration(
                              color: claim.status.color.withAlpha(25),
                              borderRadius: BorderRadius.circular(4.r),
                            ),
                            child: Text(
                              claim.status.displayName,
                              style: theme.textTheme.bodySmall?.copyWith(
                                color: claim.status.color,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                      ],
                    ),

                    if (showAmount) ...[
                      SizedBox(height: 8.h),
                      Row(
                        children: [
                          Text(
                            claim.formattedClaimAmount,
                            style: theme.textTheme.titleSmall?.copyWith(
                              fontWeight: FontWeight.bold,
                              color: theme.colorScheme.primary,
                            ),
                          ),
                          if (claim.approvedAmount != null) ...[
                            SizedBox(width: 8.w),
                            Text(
                              '→',
                              style: theme.textTheme.bodyMedium,
                            ),
                            SizedBox(width: 8.w),
                            Text(
                              claim.formattedApprovedAmount!,
                              style: theme.textTheme.titleSmall?.copyWith(
                                fontWeight: FontWeight.bold,
                                color: Colors.green,
                              ),
                            ),
                          ],
                        ],
                      ),
                    ],

                    if (showDates) ...[
                      SizedBox(height: 4.h),
                      Text(
                        'Submitted ${claim.formattedDaysSinceSubmission}',
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: theme.colorScheme.onSurfaceVariant,
                        ),
                      ),
                    ],

                    if (showActions &&
                        (onViewDetails != null || onUpdate != null)) ...[
                      SizedBox(height: 8.h),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: [
                          if (onViewDetails != null)
                            TextButton(
                              onPressed: onViewDetails,
                              style: TextButton.styleFrom(
                                padding: EdgeInsets.symmetric(
                                  horizontal: 8.w,
                                  vertical: 4.h,
                                ),
                                minimumSize: Size.zero,
                                tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                              ),
                              child: const Text('Details'),
                            ),
                          if (onUpdate != null &&
                              (claim.status ==
                                      InsuranceClaimStatus.infoRequested ||
                                  claim.status ==
                                      InsuranceClaimStatus.submitted)) ...[
                            SizedBox(width: 8.w),
                            ElevatedButton(
                              onPressed: onUpdate,
                              style: ElevatedButton.styleFrom(
                                backgroundColor: theme.colorScheme.primary,
                                foregroundColor: theme.colorScheme.onPrimary,
                                padding: EdgeInsets.symmetric(
                                  horizontal: 8.w,
                                  vertical: 4.h,
                                ),
                                minimumSize: Size.zero,
                                tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                              ),
                              child: const Text('Update'),
                            ),
                          ],
                        ],
                      ),
                    ],
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}
