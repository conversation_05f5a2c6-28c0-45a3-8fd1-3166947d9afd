import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:culture_connect/models/travel/insurance/insurance.dart';

/// A card widget for displaying insurance coverage
class InsuranceCoverageCard extends StatelessWidget {
  /// The insurance coverage to display
  final InsuranceCoverage coverage;

  /// Whether to show the coverage details
  final bool showDetails;

  /// Whether to show the coverage amount
  final bool showAmount;

  /// Whether to show the coverage deductible
  final bool showDeductible;

  /// Whether to show the coverage maximum benefit
  final bool showMaximumBenefit;

  /// Whether to use a compact layout
  final bool isCompact;

  /// Creates a new insurance coverage card
  const InsuranceCoverageCard({
    super.key,
    required this.coverage,
    this.showDetails = true,
    this.showAmount = true,
    this.showDeductible = true,
    this.showMaximumBenefit = true,
    this.isCompact = false,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    if (isCompact) {
      return _buildCompactCard(theme);
    }

    return _buildFullCard(theme);
  }

  Widget _buildFullCard(ThemeData theme) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: Padding(
        padding: EdgeInsets.all(16.r),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with type icon and name
            Row(
              children: [
                Container(
                  padding: EdgeInsets.all(8.r),
                  decoration: BoxDecoration(
                    color: coverage.isIncluded
                        ? Colors.green.withAlpha(25)
                        : Colors.grey.withAlpha(25),
                    borderRadius: BorderRadius.circular(8.r),
                  ),
                  child: Icon(
                    coverage.type.icon,
                    color: coverage.isIncluded ? Colors.green : Colors.grey,
                    size: 24.r,
                  ),
                ),
                SizedBox(width: 12.w),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        coverage.type.displayName,
                        style: theme.textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      SizedBox(height: 4.h),
                      Text(
                        coverage.isIncluded ? 'Included' : 'Not Included',
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: coverage.isIncluded
                              ? Colors.green
                              : theme.colorScheme.onSurfaceVariant,
                          fontWeight: coverage.isIncluded
                              ? FontWeight.bold
                              : FontWeight.normal,
                        ),
                      ),
                    ],
                  ),
                ),
                if (showAmount && coverage.isIncluded)
                  Text(
                    coverage.formattedAmount,
                    style: theme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: theme.colorScheme.primary,
                    ),
                  ),
              ],
            ),

            if (showDetails) ...[
              SizedBox(height: 16.h),
              Text(
                coverage.type.description,
                style: theme.textTheme.bodyMedium,
              ),
            ],

            if (coverage.isIncluded) ...[
              if ((showDeductible && coverage.deductible != null) ||
                  (showMaximumBenefit && coverage.maximumBenefit != null)) ...[
                SizedBox(height: 16.h),
                Row(
                  children: [
                    if (showDeductible && coverage.deductible != null) ...[
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Deductible',
                              style: theme.textTheme.bodySmall?.copyWith(
                                color: theme.colorScheme.onSurfaceVariant,
                              ),
                            ),
                            SizedBox(height: 4.h),
                            Text(
                              coverage.formattedDeductible!,
                              style: theme.textTheme.titleSmall?.copyWith(
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                    if (showMaximumBenefit &&
                        coverage.maximumBenefit != null) ...[
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Maximum Benefit',
                              style: theme.textTheme.bodySmall?.copyWith(
                                color: theme.colorScheme.onSurfaceVariant,
                              ),
                            ),
                            SizedBox(height: 4.h),
                            Text(
                              coverage.formattedMaximumBenefit!,
                              style: theme.textTheme.titleSmall?.copyWith(
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ],
                ),
              ],
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildCompactCard(ThemeData theme) {
    return Container(
      padding: EdgeInsets.all(12.r),
      decoration: BoxDecoration(
        color: coverage.isIncluded
            ? Colors.green.withAlpha(25)
            : Colors.grey.withAlpha(25),
        borderRadius: BorderRadius.circular(8.r),
        border: Border.all(
          color: coverage.isIncluded
              ? Colors.green.withAlpha(77)
              : Colors.grey.withAlpha(77),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Icon(
            coverage.type.icon,
            color: coverage.isIncluded ? Colors.green : Colors.grey,
            size: 20.r,
          ),
          SizedBox(width: 8.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  coverage.type.displayName,
                  style: theme.textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                if (coverage.isIncluded && showAmount) ...[
                  SizedBox(height: 2.h),
                  Text(
                    coverage.formattedAmount,
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: theme.colorScheme.primary,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ],
            ),
          ),
          if (!coverage.isIncluded)
            Icon(
              Icons.close,
              color: Colors.grey,
              size: 16.r,
            )
          else
            Icon(
              Icons.check_circle,
              color: Colors.green,
              size: 16.r,
            ),
        ],
      ),
    );
  }
}
