import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// A widget for comparing insurance policies
class InsuranceComparisonTable extends StatelessWidget {
  /// The comparison data
  final Map<String, List<dynamic>> comparisonData;

  /// The feature categories
  final Map<String, List<String>> featureCategories;

  /// The selected policy index
  final int? selectedPolicyIndex;

  /// Callback when a policy is selected
  final Function(int)? onPolicySelected;

  /// Creates a new insurance comparison table
  const InsuranceComparisonTable({
    super.key,
    required this.comparisonData,
    this.featureCategories = const {},
    this.selectedPolicyIndex,
    this.onPolicySelected,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    // Get the number of policies being compared
    final policyCount =
        comparisonData.isNotEmpty ? comparisonData.values.first.length : 0;

    if (policyCount == 0) {
      return const Center(
        child: Text('No policies to compare'),
      );
    }

    // Group features by category
    final groupedFeatures = _groupFeaturesByCategory();

    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header row with policy names
            _buildHeaderRow(theme, policyCount),

            // Feature rows
            ...groupedFeatures.entries.map((entry) {
              final category = entry.key;
              final features = entry.value;

              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Category header
                  if (category.isNotEmpty)
                    Container(
                      width: (120 + 150 * policyCount).w,
                      padding: EdgeInsets.symmetric(
                        horizontal: 16.w,
                        vertical: 8.h,
                      ),
                      color: theme.colorScheme.surfaceContainerLow,
                      child: Text(
                        category,
                        style: theme.textTheme.titleSmall?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),

                  // Feature rows
                  ...features.map((feature) =>
                      _buildFeatureRow(theme, feature, policyCount)),
                ],
              );
            }),
          ],
        ),
      ),
    );
  }

  Widget _buildHeaderRow(ThemeData theme, int policyCount) {
    return Container(
      height: 80.h,
      decoration: BoxDecoration(
        color: theme.colorScheme.surfaceContainerHigh,
        border: Border(
          bottom: BorderSide(
            color: theme.colorScheme.outlineVariant,
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          // Empty cell for feature names
          Container(
            width: 120.w,
            padding: EdgeInsets.all(16.w),
            alignment: Alignment.centerLeft,
            child: Text(
              'Features',
              style: theme.textTheme.titleSmall?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
          ),

          // Policy name cells
          ...List.generate(policyCount, (index) {
            final isSelected = selectedPolicyIndex == index;

            return GestureDetector(
              onTap: onPolicySelected != null
                  ? () => onPolicySelected!(index)
                  : null,
              child: Container(
                width: 150.w,
                padding: EdgeInsets.all(16.w),
                decoration: BoxDecoration(
                  color: isSelected ? theme.colorScheme.primaryContainer : null,
                  border: Border(
                    left: BorderSide(
                      color: theme.colorScheme.outlineVariant,
                      width: 1,
                    ),
                  ),
                ),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      comparisonData['name']?[index]?.toString() ?? '',
                      style: theme.textTheme.titleSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: isSelected ? theme.colorScheme.primary : null,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    SizedBox(height: 4.h),
                    Text(
                      comparisonData['provider']?[index]?.toString() ?? '',
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: isSelected
                            ? theme.colorScheme.primary
                            : theme.colorScheme.onSurfaceVariant,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),
            );
          }),
        ],
      ),
    );
  }

  Widget _buildFeatureRow(ThemeData theme, String feature, int policyCount) {
    final values = comparisonData[feature] ?? List.filled(policyCount, '');

    return Container(
      height: 60.h,
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color: theme.colorScheme.outlineVariant,
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          // Feature name cell
          Container(
            width: 120.w,
            padding: EdgeInsets.all(16.w),
            alignment: Alignment.centerLeft,
            child: Text(
              feature,
              style: theme.textTheme.bodyMedium,
            ),
          ),

          // Feature value cells
          ...List.generate(policyCount, (index) {
            final isSelected = selectedPolicyIndex == index;
            final value = values[index]?.toString() ?? '';

            // Determine if this is the best value
            bool isHighlighted = false;
            if (feature == 'price') {
              // For price, lower is better
              final prices = values
                  .map((v) => v.toString())
                  .where((v) => v != 'Not covered' && v.isNotEmpty)
                  .map((v) =>
                      double.tryParse(v.replaceAll(RegExp(r'[^\d.]'), '')) ??
                      double.infinity)
                  .toList();

              if (prices.isNotEmpty) {
                final minPrice = prices.reduce((a, b) => a < b ? a : b);
                final currentPrice =
                    double.tryParse(value.replaceAll(RegExp(r'[^\d.]'), '')) ??
                        double.infinity;
                isHighlighted = currentPrice == minPrice;
              }
            } else if (value != 'Not covered' && value.isNotEmpty) {
              // For coverage amounts, higher is better
              final amounts = values
                  .map((v) => v.toString())
                  .where((v) => v != 'Not covered' && v.isNotEmpty)
                  .map((v) =>
                      double.tryParse(v.replaceAll(RegExp(r'[^\d.]'), '')) ?? 0)
                  .toList();

              if (amounts.isNotEmpty) {
                final maxAmount = amounts.reduce((a, b) => a > b ? a : b);
                final currentAmount =
                    double.tryParse(value.replaceAll(RegExp(r'[^\d.]'), '')) ??
                        0;
                isHighlighted = currentAmount == maxAmount && currentAmount > 0;
              }
            }

            return Container(
              width: 150.w,
              padding: EdgeInsets.all(16.w),
              decoration: BoxDecoration(
                color: isSelected
                    ? theme.colorScheme.primaryContainer.withAlpha(128)
                    : null,
                border: Border(
                  left: BorderSide(
                    color: theme.colorScheme.outlineVariant,
                    width: 1,
                  ),
                ),
              ),
              alignment: Alignment.centerLeft,
              child: Text(
                value,
                style: theme.textTheme.bodyMedium?.copyWith(
                  fontWeight: isHighlighted ? FontWeight.bold : null,
                  color: isHighlighted
                      ? theme.colorScheme.primary
                      : value == 'Not covered'
                          ? theme.colorScheme.error
                          : isSelected
                              ? theme.colorScheme.onPrimaryContainer
                              : null,
                ),
              ),
            );
          }),
        ],
      ),
    );
  }

  Map<String, List<String>> _groupFeaturesByCategory() {
    final result = <String, List<String>>{};

    // Add features from categories
    for (final entry in featureCategories.entries) {
      result[entry.key] = entry.value
          .where((feature) => comparisonData.containsKey(feature))
          .toList();
    }

    // Add remaining features to 'Other' category
    final categorizedFeatures =
        featureCategories.values.expand((e) => e).toSet();
    final otherFeatures = comparisonData.keys
        .where((key) =>
            !categorizedFeatures.contains(key) &&
            key != 'name' &&
            key != 'provider' &&
            key != 'type')
        .toList();

    if (otherFeatures.isNotEmpty) {
      result['Other'] = otherFeatures;
    }

    return result;
  }
}
