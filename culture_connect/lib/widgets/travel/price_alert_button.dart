import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:culture_connect/models/travel/travel_service_base.dart';
import 'package:culture_connect/models/travel/price_alert.dart';
import 'package:culture_connect/providers/services_providers.dart';
import 'package:culture_connect/providers/auth_provider.dart';
import 'package:culture_connect/models/user_model.dart';
import 'package:culture_connect/screens/travel/create_price_alert_screen.dart';
import 'package:culture_connect/theme/app_colors.dart';

/// A button for creating price alerts for travel services
class PriceAlertButton extends ConsumerWidget {
  /// The travel service ID
  final String travelServiceId;

  /// The travel service type
  final TravelServiceType travelServiceType;

  /// The travel service name
  final String travelServiceName;

  /// The current price
  final double currentPrice;

  /// The currency
  final String currency;

  /// Whether to show the button as an icon button
  final bool isIconButton;

  /// The button color
  final Color? color;

  /// The text color
  final Color? textColor;

  /// The icon color
  final Color? iconColor;

  /// The callback when a price alert is created
  final VoidCallback? onAlertCreated;

  /// Creates a new price alert button
  const PriceAlertButton({
    super.key,
    required this.travelServiceId,
    required this.travelServiceType,
    required this.travelServiceName,
    required this.currentPrice,
    required this.currency,
    this.isIconButton = false,
    this.color,
    this.textColor,
    this.iconColor,
    this.onAlertCreated,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final userAsync = ref.watch(currentUserModelProvider);

    return userAsync.when(
      data: (user) {
        // If user is not logged in, don't show the button
        if (user == null) {
          return const SizedBox.shrink();
        }

        return _buildPriceAlertButton(context, ref, user);
      },
      loading: () => const SizedBox.shrink(),
      error: (_, __) => const SizedBox.shrink(),
    );
  }

  Widget _buildPriceAlertButton(
      BuildContext context, WidgetRef ref, UserModel user) {
    final theme = Theme.of(context);

    // Check if there's already an alert for this service
    final hasExistingAlertProvider =
        FutureProvider.autoDispose<bool>((ref) async {
      final priceAlertService = ref.watch(priceAlertServiceProvider(user.id));
      final alerts =
          await priceAlertService.getPriceAlertsForService(travelServiceId);
      return alerts.any((alert) => alert.status == PriceAlertStatus.active);
    });

    return Consumer(
      builder: (context, ref, child) {
        final hasExistingAlertAsync = ref.watch(hasExistingAlertProvider);

        return hasExistingAlertAsync.when(
          data: (hasExistingAlert) {
            if (isIconButton) {
              return IconButton(
                icon: Icon(
                  hasExistingAlert
                      ? Icons.notifications_active
                      : Icons.notifications_none,
                  color: iconColor ??
                      (hasExistingAlert
                          ? AppColors.success
                          : theme.iconTheme.color),
                ),
                tooltip:
                    hasExistingAlert ? 'Price alert active' : 'Set price alert',
                onPressed: hasExistingAlert
                    ? _showExistingAlertDialog(context, ref)
                    : () => _createPriceAlert(context, ref),
              );
            } else {
              return OutlinedButton.icon(
                icon: Icon(
                  hasExistingAlert
                      ? Icons.notifications_active
                      : Icons.notifications_none,
                  size: 18,
                ),
                label:
                    Text(hasExistingAlert ? 'Alert Active' : 'Set Price Alert'),
                style: OutlinedButton.styleFrom(
                  foregroundColor: textColor ??
                      (hasExistingAlert
                          ? AppColors.success
                          : theme.colorScheme.primary),
                  side: BorderSide(
                    color: color ??
                        (hasExistingAlert
                            ? AppColors.success
                            : theme.colorScheme.primary),
                  ),
                ),
                onPressed: hasExistingAlert
                    ? _showExistingAlertDialog(context, ref)
                    : () => _createPriceAlert(context, ref),
              );
            }
          },
          loading: () {
            if (isIconButton) {
              return const IconButton(
                icon: SizedBox(
                  width: 18,
                  height: 18,
                  child: CircularProgressIndicator(strokeWidth: 2),
                ),
                onPressed: null,
              );
            } else {
              return const OutlinedButton(
                onPressed: null,
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    SizedBox(
                      width: 18,
                      height: 18,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    ),
                    SizedBox(width: 8),
                    Text('Checking...'),
                  ],
                ),
              );
            }
          },
          error: (_, __) {
            if (isIconButton) {
              return IconButton(
                icon: Icon(
                  Icons.notifications_none,
                  color: iconColor ?? theme.iconTheme.color,
                ),
                tooltip: 'Set price alert',
                onPressed: () => _createPriceAlert(context, ref),
              );
            } else {
              return OutlinedButton.icon(
                icon: const Icon(
                  Icons.notifications_none,
                  size: 18,
                ),
                label: const Text('Set Price Alert'),
                style: OutlinedButton.styleFrom(
                  foregroundColor: textColor ?? theme.colorScheme.primary,
                  side: BorderSide(
                    color: color ?? theme.colorScheme.primary,
                  ),
                ),
                onPressed: () => _createPriceAlert(context, ref),
              );
            }
          },
        );
      },
    );
  }

  /// Create a price alert
  Future<void> _createPriceAlert(BuildContext context, WidgetRef ref) async {
    final userAsync = ref.read(currentUserModelProvider);
    final user = userAsync.value;
    if (user == null) return;

    if (!context.mounted) return;
    final result = await Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => CreatePriceAlertScreen(
          travelServiceId: travelServiceId,
          travelServiceType: travelServiceType,
          travelServiceName: travelServiceName,
          currentPrice: currentPrice,
          currency: currency,
        ),
      ),
    );

    if (result == true) {
      // Call the callback if provided
      onAlertCreated?.call();

      // Show a snackbar
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Price alert created successfully!'),
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    }
  }

  /// Show a dialog for an existing alert
  VoidCallback _showExistingAlertDialog(BuildContext context, WidgetRef ref) {
    return () {
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('Price Alert Active'),
          content: const Text(
            'You already have an active price alert for this service. '
            'Would you like to view your alerts or create a new one?',
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                _createPriceAlert(context, ref);
              },
              child: const Text('CREATE NEW'),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                Navigator.of(context).pushNamed('/price-alerts');
              },
              child: const Text('VIEW ALERTS'),
            ),
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('CANCEL'),
            ),
          ],
        ),
      );
    };
  }
}
