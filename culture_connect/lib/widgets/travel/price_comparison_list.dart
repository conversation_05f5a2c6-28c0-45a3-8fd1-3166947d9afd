import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'package:culture_connect/providers/price_comparison_provider.dart';
import 'package:culture_connect/theme/app_theme.dart';
import 'package:culture_connect/widgets/travel/price_comparison_card.dart';

/// A widget that displays a list of price comparisons
class PriceComparisonList extends ConsumerStatefulWidget {
  /// The travel service type
  final String serviceType;

  /// The travel service ID
  final String serviceId;

  /// The travel service name
  final String serviceName;

  /// Whether to show the price breakdown
  final bool showPriceBreakdown;

  /// Whether to show the booking button
  final bool showBookingButton;

  /// Whether to show the refresh button
  final bool showRefreshButton;

  /// Creates a new price comparison list
  const PriceComparisonList({
    super.key,
    required this.serviceType,
    required this.serviceId,
    required this.serviceName,
    this.showPriceBreakdown = false,
    this.showBookingButton = true,
    this.showRefreshButton = true,
  });

  @override
  ConsumerState<PriceComparisonList> createState() =>
      _PriceComparisonListState();
}

class _PriceComparisonListState extends ConsumerState<PriceComparisonList> {
  bool _isExpanded = false;

  @override
  Widget build(BuildContext context) {
    final serviceEntry = MapEntry(widget.serviceType, widget.serviceId);
    final priceComparisonAsync =
        ref.watch(priceComparisonProvider(serviceEntry));
    final bestPriceAsync = ref.watch(bestPriceProvider(serviceEntry));
    final priceRangeAsync = ref.watch(priceRangeProvider(serviceEntry));

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Header
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
          child: Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Price Comparison',
                      style: TextStyle(
                        fontSize: 18.sp,
                        fontWeight: FontWeight.bold,
                        color: AppTheme.textPrimaryColor,
                      ),
                    ),
                    SizedBox(height: 4.h),
                    Text(
                      widget.serviceName,
                      style: TextStyle(
                        fontSize: 14.sp,
                        color: AppTheme.textSecondaryColor,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),
              if (widget.showRefreshButton)
                IconButton(
                  icon: const Icon(Icons.refresh),
                  onPressed: () {
                    ref.invalidate(priceComparisonProvider(serviceEntry));
                  },
                  tooltip: 'Refresh prices',
                ),
            ],
          ),
        ),

        // Price range
        priceRangeAsync.when(
          data: (priceRange) {
            if (priceRange == null) return const SizedBox.shrink();

            return Padding(
              padding: EdgeInsets.symmetric(horizontal: 16.w),
              child: Row(
                children: [
                  Text(
                    'Price range: ',
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: AppTheme.textSecondaryColor,
                    ),
                  ),
                  Text(
                    '${widget.serviceType == 'flight' ? 'USD' : '\$'}${priceRange.key.toStringAsFixed(2)} - ${widget.serviceType == 'flight' ? 'USD' : '\$'}${priceRange.value.toStringAsFixed(2)}',
                    style: TextStyle(
                      fontSize: 14.sp,
                      fontWeight: FontWeight.bold,
                      color: AppTheme.textPrimaryColor,
                    ),
                  ),
                ],
              ),
            );
          },
          loading: () => const SizedBox.shrink(),
          error: (_, __) => const SizedBox.shrink(),
        ),

        SizedBox(height: 8.h),

        // Price comparison list
        priceComparisonAsync.when(
          data: (pricePoints) {
            if (pricePoints.isEmpty) {
              return Center(
                child: Padding(
                  padding: EdgeInsets.all(16.r),
                  child: Column(
                    children: [
                      Icon(
                        Icons.search_off,
                        size: 48.r,
                        color: Colors.grey[400],
                      ),
                      SizedBox(height: 16.h),
                      Text(
                        'No price comparisons available',
                        style: TextStyle(
                          fontSize: 16.sp,
                          color: AppTheme.textSecondaryColor,
                        ),
                      ),
                      SizedBox(height: 8.h),
                      if (widget.showRefreshButton)
                        ElevatedButton.icon(
                          onPressed: () {
                            ref.invalidate(
                                priceComparisonProvider(serviceEntry));
                          },
                          icon: const Icon(Icons.refresh),
                          label: const Text('Refresh'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: AppTheme.primaryColor,
                            foregroundColor: Colors.white,
                          ),
                        ),
                    ],
                  ),
                ),
              );
            }

            // Determine how many items to show
            final displayCount = _isExpanded ? pricePoints.length : 3;
            final displayedPricePoints =
                pricePoints.take(displayCount).toList();

            return Column(
              children: [
                // Price cards
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: 16.w),
                  child: Column(
                    children: [
                      ...displayedPricePoints.map((pricePoint) {
                        return bestPriceAsync.when(
                          data: (bestPrice) {
                            final isBestPrice = bestPrice != null &&
                                bestPrice.id == pricePoint.id;

                            return PriceComparisonCard(
                              pricePoint: pricePoint,
                              isBestPrice: isBestPrice,
                              showBookingButton: widget.showBookingButton,
                              showPriceBreakdown: widget.showPriceBreakdown,
                              onTap: () {
                                setState(() {
                                  _isExpanded = true;
                                });
                              },
                            );
                          },
                          loading: () => PriceComparisonCard(
                            pricePoint: pricePoint,
                            showBookingButton: widget.showBookingButton,
                            showPriceBreakdown: widget.showPriceBreakdown,
                          ),
                          error: (_, __) => PriceComparisonCard(
                            pricePoint: pricePoint,
                            showBookingButton: widget.showBookingButton,
                            showPriceBreakdown: widget.showPriceBreakdown,
                          ),
                        );
                      }),
                    ],
                  ),
                ),

                // Show more/less button
                if (pricePoints.length > 3)
                  TextButton(
                    onPressed: () {
                      setState(() {
                        _isExpanded = !_isExpanded;
                      });
                    },
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          _isExpanded
                              ? 'Show less'
                              : 'Show all ${pricePoints.length} options',
                          style: TextStyle(
                            fontSize: 14.sp,
                            color: AppTheme.primaryColor,
                          ),
                        ),
                        Icon(
                          _isExpanded
                              ? Icons.keyboard_arrow_up
                              : Icons.keyboard_arrow_down,
                          size: 16.r,
                          color: AppTheme.primaryColor,
                        ),
                      ],
                    ),
                  ),
              ],
            );
          },
          loading: () => Center(
            child: Padding(
              padding: EdgeInsets.all(32.r),
              child: Column(
                children: [
                  const CircularProgressIndicator(
                    valueColor:
                        AlwaysStoppedAnimation<Color>(AppTheme.primaryColor),
                  ),
                  SizedBox(height: 16.h),
                  Text(
                    'Comparing prices...',
                    style: TextStyle(
                      fontSize: 16.sp,
                      color: AppTheme.textSecondaryColor,
                    ),
                  ),
                ],
              ),
            ),
          ),
          error: (error, stackTrace) => Center(
            child: Padding(
              padding: EdgeInsets.all(16.r),
              child: Column(
                children: [
                  Icon(
                    Icons.error_outline,
                    size: 48.r,
                    color: Colors.red,
                  ),
                  SizedBox(height: 16.h),
                  Text(
                    'Error loading price comparisons',
                    style: TextStyle(
                      fontSize: 16.sp,
                      fontWeight: FontWeight.bold,
                      color: Colors.red,
                    ),
                  ),
                  SizedBox(height: 8.h),
                  Text(
                    error.toString(),
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: AppTheme.textSecondaryColor,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  SizedBox(height: 16.h),
                  if (widget.showRefreshButton)
                    ElevatedButton.icon(
                      onPressed: () {
                        ref.invalidate(priceComparisonProvider(serviceEntry));
                      },
                      icon: const Icon(Icons.refresh),
                      label: const Text('Try Again'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppTheme.primaryColor,
                        foregroundColor: Colors.white,
                      ),
                    ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }
}
