import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:shimmer/shimmer.dart';

/// A skeleton loading widget for hotels
class HotelSkeletonLoading extends StatelessWidget {
  /// Whether to show a grid view
  final bool isGrid;
  
  /// Creates a new hotel skeleton loading widget
  const HotelSkeletonLoading({
    super.key,
    this.isGrid = false,
  });
  
  @override
  Widget build(BuildContext context) {
    return Shimmer.fromColors(
      baseColor: Colors.grey[300]!,
      highlightColor: Colors.grey[100]!,
      child: isGrid ? _buildGridView() : _buildListView(),
    );
  }
  
  Widget _buildGridView() {
    return GridView.builder(
      padding: EdgeInsets.all(16.r),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        childAspectRatio: 0.75,
        crossAxisSpacing: 16.w,
        mainAxisSpacing: 16.h,
      ),
      itemCount: 6,
      itemBuilder: (context, index) {
        return _buildGridItem();
      },
    );
  }
  
  Widget _buildListView() {
    return ListView.builder(
      padding: EdgeInsets.all(16.r),
      itemCount: 5,
      itemBuilder: (context, index) {
        return Padding(
          padding: EdgeInsets.only(bottom: 16.h),
          child: _buildListItem(),
        );
      },
    );
  }
  
  Widget _buildGridItem() {
    return Card(
      clipBehavior: Clip.antiAlias,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Image placeholder
          AspectRatio(
            aspectRatio: 16 / 9,
            child: Container(
              color: Colors.white,
            ),
          ),
          
          // Content placeholder
          Padding(
            padding: EdgeInsets.all(12.r),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Title placeholder
                Container(
                  width: double.infinity,
                  height: 16.h,
                  color: Colors.white,
                ),
                SizedBox(height: 8.h),
                
                // Location placeholder
                Container(
                  width: 120.w,
                  height: 12.h,
                  color: Colors.white,
                ),
                SizedBox(height: 8.h),
                
                // Rating placeholder
                Container(
                  width: 80.w,
                  height: 12.h,
                  color: Colors.white,
                ),
                SizedBox(height: 8.h),
                
                // Price placeholder
                Container(
                  width: 100.w,
                  height: 16.h,
                  color: Colors.white,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
  
  Widget _buildListItem() {
    return Card(
      clipBehavior: Clip.antiAlias,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Image placeholder
          SizedBox(
            width: 120.w,
            height: 120.h,
            child: Container(
              color: Colors.white,
            ),
          ),
          
          // Content placeholder
          Expanded(
            child: Padding(
              padding: EdgeInsets.all(12.r),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Title placeholder
                  Container(
                    width: double.infinity,
                    height: 16.h,
                    color: Colors.white,
                  ),
                  SizedBox(height: 8.h),
                  
                  // Location placeholder
                  Container(
                    width: 120.w,
                    height: 12.h,
                    color: Colors.white,
                  ),
                  SizedBox(height: 8.h),
                  
                  // Rating placeholder
                  Container(
                    width: 80.w,
                    height: 12.h,
                    color: Colors.white,
                  ),
                  SizedBox(height: 8.h),
                  
                  // Price placeholder
                  Container(
                    width: 100.w,
                    height: 16.h,
                    color: Colors.white,
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
