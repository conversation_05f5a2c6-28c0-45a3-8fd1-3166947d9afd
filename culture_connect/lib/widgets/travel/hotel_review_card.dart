import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:timeago/timeago.dart' as timeago;
import 'package:culture_connect/models/travel/hotel_review.dart';
import 'package:culture_connect/widgets/common/rating_display.dart';
import 'package:culture_connect/widgets/photo_gallery.dart';

/// A card displaying a hotel review
class HotelReviewCard extends ConsumerStatefulWidget {
  /// The review to display
  final HotelReview review;

  /// Whether to show the full content
  final bool showFullContent;

  /// Whether to show the helpful button
  final bool showHelpfulButton;

  /// Whether to show the photos
  final bool showPhotos;

  /// Callback when the review is marked as helpful
  final Function(HotelReview)? onHelpfulToggled;

  /// Creates a new hotel review card
  const HotelReviewCard({
    super.key,
    required this.review,
    this.showFullContent = false,
    this.showHelpfulButton = true,
    this.showPhotos = true,
    this.onHelpfulToggled,
  });

  @override
  ConsumerState<HotelReviewCard> createState() => _HotelReviewCardState();
}

class _HotelReviewCardState extends ConsumerState<HotelReviewCard>
    with SingleTickerProviderStateMixin {
  bool _isExpanded = false;
  bool _isHelpful = false;
  late AnimationController _animationController;

  @override
  void initState() {
    super.initState();
    _isExpanded = widget.showFullContent;
    // Check if the current user has marked this review as helpful
    // This would normally use the current user's ID from an auth service
    _isHelpful = widget.review.helpfulUserIds.contains('current-user-id');

    // Initialize animation controller
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );

    if (_isExpanded) {
      _animationController.value = 1.0;
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _toggleExpanded() {
    setState(() {
      _isExpanded = !_isExpanded;
      if (_isExpanded) {
        _animationController.forward();
      } else {
        _animationController.reverse();
      }
    });
  }

  void _toggleHelpful() {
    setState(() {
      _isHelpful = !_isHelpful;
    });

    if (widget.onHelpfulToggled != null) {
      widget.onHelpfulToggled!(widget.review);
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final review = widget.review;

    return Card(
      margin: EdgeInsets.only(bottom: 16.h),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: Padding(
        padding: EdgeInsets.all(16.r),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // User info and overall rating
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // User avatar
                Semantics(
                  label: 'Profile picture of ${review.userName}',
                  child: CircleAvatar(
                    radius: 24.r,
                    backgroundImage: review.userProfileImageUrl != null
                        ? NetworkImage(review.userProfileImageUrl!)
                        : null,
                    child: review.userProfileImageUrl == null
                        ? Icon(
                            Icons.person,
                            size: 24.r,
                            color: Colors.white,
                            semanticLabel: 'Default profile icon',
                          )
                        : null,
                  ),
                ),

                SizedBox(width: 12.w),

                // User info and rating
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Expanded(
                            child: Text(
                              review.userName,
                              style: TextStyle(
                                fontSize: 16.sp,
                                fontWeight: FontWeight.bold,
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                          if (review.isVerified)
                            Tooltip(
                              message: 'Verified Stay',
                              child: Icon(
                                Icons.verified,
                                size: 16.r,
                                color: Colors.green,
                                semanticLabel: 'Verified Stay',
                              ),
                            ),
                        ],
                      ),
                      SizedBox(height: 4.h),
                      Row(
                        children: [
                          RatingDisplay(
                            rating: review.overallRating,
                            size: 16.r,
                          ),
                          SizedBox(width: 8.w),
                          Text(
                            timeago.format(review.datePosted),
                            style: TextStyle(
                              fontSize: 12.sp,
                              color: theme.colorScheme.onSurfaceVariant,
                            ),
                          ),
                        ],
                      ),
                      if (review.tripType != null ||
                          review.roomType != null) ...[
                        SizedBox(height: 4.h),
                        Row(
                          children: [
                            if (review.tripType != null) ...[
                              Icon(
                                Icons.people,
                                size: 14.r,
                                color: theme.colorScheme.onSurfaceVariant,
                              ),
                              SizedBox(width: 4.w),
                              Text(
                                review.tripType!,
                                style: TextStyle(
                                  fontSize: 12.sp,
                                  color: theme.colorScheme.onSurfaceVariant,
                                ),
                              ),
                              SizedBox(width: 8.w),
                            ],
                            if (review.roomType != null) ...[
                              Icon(
                                Icons.hotel,
                                size: 14.r,
                                color: theme.colorScheme.onSurfaceVariant,
                              ),
                              SizedBox(width: 4.w),
                              Text(
                                review.roomType!,
                                style: TextStyle(
                                  fontSize: 12.sp,
                                  color: theme.colorScheme.onSurfaceVariant,
                                ),
                              ),
                            ],
                          ],
                        ),
                      ],
                    ],
                  ),
                ),
              ],
            ),

            SizedBox(height: 12.h),

            // Category ratings
            if (review.categoryRatings.isNotEmpty) ...[
              Wrap(
                spacing: 16.w,
                runSpacing: 8.h,
                children: review.categoryRatings.entries.map((entry) {
                  return _buildCategoryRating(
                    entry.key.displayName,
                    entry.value,
                  );
                }).toList(),
              ),
              SizedBox(height: 12.h),
            ],

            // Tags
            if (review.tags.isNotEmpty) ...[
              Wrap(
                spacing: 8.w,
                runSpacing: 8.h,
                children: review.tags.map((tag) {
                  return Chip(
                    label: Text(
                      tag,
                      style: TextStyle(
                        fontSize: 12.sp,
                      ),
                    ),
                    materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                    visualDensity: VisualDensity.compact,
                    padding: EdgeInsets.zero,
                  );
                }).toList(),
              ),
              SizedBox(height: 12.h),
            ],

            // Review content
            AnimatedSize(
              duration: const Duration(milliseconds: 300),
              curve: Curves.easeInOut,
              child: Container(
                constraints: BoxConstraints(
                  maxHeight: _isExpanded ? double.infinity : 72.h,
                ),
                child: Text(
                  review.content,
                  style: theme.textTheme.bodyMedium,
                  overflow:
                      _isExpanded ? TextOverflow.visible : TextOverflow.fade,
                ),
              ),
            ),

            // Show more/less button
            if (!widget.showFullContent && review.content.length > 150)
              AnimatedSwitcher(
                duration: const Duration(milliseconds: 300),
                child: TextButton.icon(
                  key: ValueKey<bool>(_isExpanded),
                  onPressed: _toggleExpanded,
                  icon: Icon(
                    _isExpanded
                        ? Icons.keyboard_arrow_up
                        : Icons.keyboard_arrow_down,
                    size: 16.r,
                  ),
                  label: Text(_isExpanded ? 'Show less' : 'Show more'),
                ),
              ),

            // Review photos
            if (widget.showPhotos && review.photoUrls.isNotEmpty) ...[
              SizedBox(height: 12.h),
              SizedBox(
                height: 100.h,
                child: ListView.builder(
                  scrollDirection: Axis.horizontal,
                  itemCount: review.photoUrls.length,
                  itemBuilder: (context, index) {
                    return Padding(
                      padding: EdgeInsets.only(right: 8.w),
                      child: GestureDetector(
                        onTap: () {
                          showDialog(
                            context: context,
                            builder: (context) => Dialog(
                              child: PhotoGallery(
                                photos: review.photoUrls,
                                initialIndex: index,
                              ),
                            ),
                          );
                        },
                        child: Hero(
                          tag: 'review_photo_${review.id}_$index',
                          child: ClipRRect(
                            borderRadius: BorderRadius.circular(8.r),
                            child: FadeInImage.assetNetwork(
                              placeholder:
                                  'assets/images/placeholder_image.png',
                              image: review.photoUrls[index],
                              width: 100.w,
                              height: 100.h,
                              fit: BoxFit.cover,
                              fadeInDuration: const Duration(milliseconds: 300),
                              fadeOutDuration:
                                  const Duration(milliseconds: 300),
                              imageErrorBuilder: (context, error, stackTrace) {
                                return Container(
                                  width: 100.w,
                                  height: 100.h,
                                  color:
                                      theme.colorScheme.surfaceContainerHighest,
                                  child: Center(
                                    child: Column(
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        Icon(
                                          Icons.image_not_supported,
                                          color: theme
                                              .colorScheme.onSurfaceVariant,
                                          size: 24.r,
                                          semanticLabel: 'Image failed to load',
                                        ),
                                        SizedBox(height: 4.h),
                                        Text(
                                          'Failed to load',
                                          style: TextStyle(
                                            fontSize: 10.sp,
                                            color: theme
                                                .colorScheme.onSurfaceVariant,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                );
                              },
                            ),
                          ),
                        ),
                      ),
                    );
                  },
                ),
              ),
            ],

            // Hotel response
            if (review.hotelResponse != null) ...[
              SizedBox(height: 16.h),
              Container(
                padding: EdgeInsets.all(12.r),
                decoration: BoxDecoration(
                  color: theme.colorScheme.surfaceContainerLowest,
                  borderRadius: BorderRadius.circular(8.r),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(
                          Icons.business,
                          size: 16.r,
                          color: theme.colorScheme.primary,
                        ),
                        SizedBox(width: 8.w),
                        Expanded(
                          child: Text(
                            'Response from ${review.hotelResponse!.staffName}, ${review.hotelResponse!.staffTitle}',
                            style: TextStyle(
                              fontSize: 14.sp,
                              fontWeight: FontWeight.bold,
                              color: theme.colorScheme.primary,
                            ),
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: 8.h),
                    Text(
                      review.hotelResponse!.content,
                      style: theme.textTheme.bodyMedium,
                    ),
                    SizedBox(height: 4.h),
                    Text(
                      'Responded ${timeago.format(review.hotelResponse!.datePosted)}',
                      style: TextStyle(
                        fontSize: 12.sp,
                        color: theme.colorScheme.onSurfaceVariant,
                      ),
                    ),
                  ],
                ),
              ),
            ],

            // Helpful button
            if (widget.showHelpfulButton) ...[
              SizedBox(height: 16.h),
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  AnimatedContainer(
                    duration: const Duration(milliseconds: 300),
                    curve: Curves.easeInOut,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(20.r),
                      border: Border.all(
                        color: _isHelpful
                            ? theme.colorScheme.primary
                            : theme.colorScheme.outline,
                        width: 1.w,
                      ),
                    ),
                    child: OutlinedButton.icon(
                      onPressed: _toggleHelpful,
                      icon: AnimatedSwitcher(
                        duration: const Duration(milliseconds: 300),
                        transitionBuilder:
                            (Widget child, Animation<double> animation) {
                          return ScaleTransition(
                              scale: animation, child: child);
                        },
                        child: Icon(
                          _isHelpful ? Icons.thumb_up : Icons.thumb_up_outlined,
                          key: ValueKey<bool>(_isHelpful),
                          size: 16.r,
                          semanticLabel: _isHelpful
                              ? 'Marked as helpful'
                              : 'Mark as helpful',
                        ),
                      ),
                      label: Text(
                        _isHelpful
                            ? 'Helpful (${widget.review.helpfulCount + 1})'
                            : 'Helpful (${widget.review.helpfulCount})',
                      ),
                      style: OutlinedButton.styleFrom(
                        foregroundColor: _isHelpful
                            ? theme.colorScheme.primary
                            : theme.colorScheme.onSurface,
                        side: BorderSide.none,
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildCategoryRating(String category, double rating) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          '$category: ',
          style: TextStyle(
            fontSize: 12.sp,
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
        ),
        Text(
          rating.toStringAsFixed(1),
          style: TextStyle(
            fontSize: 12.sp,
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }
}
