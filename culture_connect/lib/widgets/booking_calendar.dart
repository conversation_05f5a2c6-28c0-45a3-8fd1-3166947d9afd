import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:table_calendar/table_calendar.dart';
import 'package:intl/intl.dart';
import 'package:culture_connect/models/experience.dart';

/// A provider for the selected booking date
final selectedBookingDateProvider = StateProvider<DateTime?>((ref) => null);

/// A provider for the selected booking time slot
final selectedTimeSlotProvider = StateProvider<TimeSlot?>((ref) => null);

/// A provider for the selected number of participants
final participantsCountProvider = StateProvider<int>((ref) => 1);

/// A model representing a time slot
class TimeSlot {
  final TimeOfDay startTime;
  final TimeOfDay endTime;
  final bool isAvailable;
  final int maxParticipants;
  final double price;
  final int currentParticipants;

  const TimeSlot({
    required this.startTime,
    required this.endTime,
    this.isAvailable = true,
    this.maxParticipants = 10,
    this.price = 0.0,
    this.currentParticipants = 0,
  });

  /// Format the time slot as a string
  String get formatted {
    return '${_formatTimeOfDay(startTime)} - ${_formatTimeOfDay(endTime)}';
  }

  /// Get the remaining spots
  int get remainingSpots => maxParticipants - currentParticipants;

  /// Check if the slot is fully booked
  bool get isFullyBooked => currentParticipants >= maxParticipants;

  /// Format a TimeOfDay as a string
  String _formatTimeOfDay(TimeOfDay timeOfDay) {
    final hour = timeOfDay.hourOfPeriod == 0 ? 12 : timeOfDay.hourOfPeriod;
    final minute = timeOfDay.minute.toString().padLeft(2, '0');
    final period = timeOfDay.period == DayPeriod.am ? 'AM' : 'PM';
    return '$hour:$minute $period';
  }
}

/// A widget for selecting a booking date and time
class BookingCalendar extends ConsumerStatefulWidget {
  final Experience experience;
  final Function(DateTime, TimeSlot, int)? onBookingSelected;

  const BookingCalendar({
    super.key,
    required this.experience,
    this.onBookingSelected,
  });

  @override
  ConsumerState<BookingCalendar> createState() => _BookingCalendarState();
}

class _BookingCalendarState extends ConsumerState<BookingCalendar>
    with SingleTickerProviderStateMixin {
  late DateTime _focusedDay;
  late DateTime _firstDay;
  late DateTime _lastDay;
  late Map<DateTime, List<TimeSlot>> _availabilityMap;
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _focusedDay = DateTime.now();
    _firstDay = DateTime.now();
    _lastDay = DateTime.now().add(const Duration(days: 90));
    _generateAvailabilityMap();
    _tabController = TabController(length: 2, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  /// Generate a map of available time slots for each day
  void _generateAvailabilityMap() {
    // In a real app, this would come from an API
    _availabilityMap = {};

    // Use the experience's available dates if provided
    final availableDates = widget.experience.availableDates;

    // If no available dates are provided, generate some mock data
    if (availableDates.isEmpty) {
      // Generate some mock availability data
      final now = DateTime.now();
      for (int i = 0; i < 90; i++) {
        final day = now.add(Duration(days: i));

        // Skip some days to simulate unavailability
        if (i % 7 == 0) continue;

        final slots = <TimeSlot>[];

        // Morning slot
        slots.add(TimeSlot(
          startTime: const TimeOfDay(hour: 9, minute: 0),
          endTime: const TimeOfDay(hour: 11, minute: 0),
          isAvailable: i % 5 != 0, // Some slots are unavailable
          maxParticipants: 10,
          price: widget.experience.price,
        ));

        // Afternoon slot
        slots.add(TimeSlot(
          startTime: const TimeOfDay(hour: 14, minute: 0),
          endTime: const TimeOfDay(hour: 16, minute: 0),
          isAvailable: i % 3 != 0, // Some slots are unavailable
          maxParticipants: 8,
          price: widget.experience.price * 1.2, // Premium price for afternoon
        ));

        // Evening slot
        if (i % 2 == 0) {
          slots.add(TimeSlot(
            startTime: const TimeOfDay(hour: 18, minute: 0),
            endTime: const TimeOfDay(hour: 20, minute: 0),
            isAvailable: i % 4 != 0, // Some slots are unavailable
            maxParticipants: 6,
            price: widget.experience.price * 1.5, // Premium price for evening
          ));
        }

        _availabilityMap[DateTime(day.year, day.month, day.day)] = slots;
      }
    } else {
      // Use the experience's available dates
      for (final date in availableDates) {
        final normalizedDate = DateTime(date.year, date.month, date.day);

        // Generate time slots for this date
        final slots = <TimeSlot>[];

        // Morning slot (9-11 AM)
        slots.add(TimeSlot(
          startTime: const TimeOfDay(hour: 9, minute: 0),
          endTime: const TimeOfDay(hour: 11, minute: 0),
          isAvailable: true,
          maxParticipants: widget.experience.maxParticipants,
          price: widget.experience.price,
        ));

        // Afternoon slot (2-4 PM)
        slots.add(TimeSlot(
          startTime: const TimeOfDay(hour: 14, minute: 0),
          endTime: const TimeOfDay(hour: 16, minute: 0),
          isAvailable: true,
          maxParticipants: widget.experience.maxParticipants,
          price: widget.experience.price * 1.2, // Premium price for afternoon
        ));

        // Evening slot (6-8 PM)
        slots.add(TimeSlot(
          startTime: const TimeOfDay(hour: 18, minute: 0),
          endTime: const TimeOfDay(hour: 20, minute: 0),
          isAvailable: true,
          maxParticipants: widget.experience.maxParticipants,
          price: widget.experience.price * 1.5, // Premium price for evening
        ));

        _availabilityMap[normalizedDate] = slots;
      }
    }

    // Check for current participants and update availability
    _updateAvailabilityWithCurrentBookings();
  }

  /// Update availability based on current bookings
  void _updateAvailabilityWithCurrentBookings() {
    // In a real app, this would come from an API
    // For now, we'll simulate some bookings

    final now = DateTime.now();

    // Simulate some bookings for the next 30 days
    for (int i = 0; i < 30; i++) {
      final day = now.add(Duration(days: i));
      final normalizedDay = DateTime(day.year, day.month, day.day);

      // Skip days that don't have availability
      if (!_availabilityMap.containsKey(normalizedDay)) continue;

      final slots = _availabilityMap[normalizedDay]!;

      // Randomly mark some slots as fully booked
      for (int j = 0; j < slots.length; j++) {
        // 20% chance of a slot being fully booked
        if (i % 5 == 0 && j == 0) {
          final updatedSlot = TimeSlot(
            startTime: slots[j].startTime,
            endTime: slots[j].endTime,
            isAvailable: false, // Fully booked
            maxParticipants: slots[j].maxParticipants,
            price: slots[j].price,
          );

          slots[j] = updatedSlot;
        }

        // 30% chance of a slot having some participants
        if (i % 3 == 0 && j == 1) {
          // Simulate some current participants (between 1 and maxParticipants-1)
          final currentParticipants = 1 + (i % (slots[j].maxParticipants - 1));

          // Update the slot with current participants
          final updatedSlot = TimeSlot(
            startTime: slots[j].startTime,
            endTime: slots[j].endTime,
            isAvailable: true,
            maxParticipants: slots[j].maxParticipants,
            price: slots[j].price,
            currentParticipants: currentParticipants,
          );

          slots[j] = updatedSlot;
        }
      }

      _availabilityMap[normalizedDay] = slots;
    }
  }

  /// Check if a day has any available time slots
  bool _isDayAvailable(DateTime day) {
    final normalizedDay = DateTime(day.year, day.month, day.day);
    final slots = _availabilityMap[normalizedDay] ?? [];
    return slots.any((slot) => slot.isAvailable);
  }

  /// Get the available time slots for a day
  List<TimeSlot> _getAvailableTimeSlots(DateTime day) {
    final normalizedDay = DateTime(day.year, day.month, day.day);
    final slots = _availabilityMap[normalizedDay] ?? [];
    return slots.where((slot) => slot.isAvailable).toList();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final selectedDate = ref.watch(selectedBookingDateProvider);
    final selectedTimeSlot = ref.watch(selectedTimeSlotProvider);
    final participantsCount = ref.watch(participantsCountProvider);

    return Column(
      children: [
        TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'Calendar'),
            Tab(text: 'Time Slots'),
          ],
          labelColor: theme.colorScheme.primary,
          unselectedLabelColor: theme.colorScheme.onSurface,
          indicatorColor: theme.colorScheme.primary,
        ),
        const SizedBox(height: 16),
        SizedBox(
          height: 350,
          child: TabBarView(
            controller: _tabController,
            children: [
              _buildCalendarTab(selectedDate),
              _buildTimeSlotsTab(
                  selectedDate, selectedTimeSlot, participantsCount),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildCalendarTab(DateTime? selectedDate) {
    return Column(
      children: [
        TableCalendar(
          firstDay: _firstDay,
          lastDay: _lastDay,
          focusedDay: _focusedDay,
          selectedDayPredicate: (day) {
            return selectedDate != null &&
                day.year == selectedDate.year &&
                day.month == selectedDate.month &&
                day.day == selectedDate.day;
          },
          onDaySelected: (selectedDay, focusedDay) {
            if (!_isDayAvailable(selectedDay)) {
              if (!mounted) return;
              final scaffoldMessenger = ScaffoldMessenger.of(context);
              scaffoldMessenger.showSnackBar(
                const SnackBar(
                  content: Text('No available time slots for this day'),
                  duration: Duration(seconds: 2),
                ),
              );
              return;
            }

            if (!mounted) return;

            setState(() {
              _focusedDay = focusedDay;
            });

            ref.read(selectedBookingDateProvider.notifier).state = selectedDay;
            ref.read(selectedTimeSlotProvider.notifier).state = null;

            // Switch to time slots tab
            _tabController.animateTo(1);
          },
          onPageChanged: (focusedDay) {
            if (!mounted) return;
            setState(() {
              _focusedDay = focusedDay;
            });
          },
          calendarStyle: CalendarStyle(
            outsideDaysVisible: false,
            weekendTextStyle: const TextStyle().copyWith(color: Colors.red),
            holidayTextStyle: const TextStyle().copyWith(color: Colors.red),
          ),
          calendarBuilders: CalendarBuilders(
            defaultBuilder: (context, day, focusedDay) {
              return _buildCalendarDay(day, _isDayAvailable(day));
            },
            selectedBuilder: (context, day, focusedDay) {
              return Container(
                margin: const EdgeInsets.all(4),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.primary,
                  shape: BoxShape.circle,
                ),
                child: Center(
                  child: Text(
                    '${day.day}',
                    style: const TextStyle(color: Colors.white),
                  ),
                ),
              );
            },
            todayBuilder: (context, day, focusedDay) {
              return Container(
                margin: const EdgeInsets.all(4),
                decoration: BoxDecoration(
                  border: Border.all(
                    color: Theme.of(context).colorScheme.primary,
                    width: 2,
                  ),
                  shape: BoxShape.circle,
                ),
                child: Center(
                  child: Text(
                    '${day.day}',
                    style: TextStyle(
                      color: _isDayAvailable(day)
                          ? Theme.of(context).colorScheme.primary
                          : Colors.grey,
                    ),
                  ),
                ),
              );
            },
            disabledBuilder: (context, day, focusedDay) {
              return Container(
                margin: const EdgeInsets.all(4),
                child: Center(
                  child: Text(
                    '${day.day}',
                    style: const TextStyle(color: Colors.grey),
                  ),
                ),
              );
            },
          ),
          headerStyle: const HeaderStyle(
            formatButtonVisible: false,
            titleCentered: true,
          ),
        ),
        const SizedBox(height: 16),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            _buildLegendItem(Colors.green, 'Available'),
            const SizedBox(width: 16),
            _buildLegendItem(Colors.grey, 'Unavailable'),
          ],
        ),
      ],
    );
  }

  Widget _buildTimeSlotsTab(DateTime? selectedDate, TimeSlot? selectedTimeSlot,
      int participantsCount) {
    if (selectedDate == null) {
      return const Center(
        child: Text('Please select a date first'),
      );
    }

    final availableSlots = _getAvailableTimeSlots(selectedDate);

    if (availableSlots.isEmpty) {
      return const Center(
        child: Text('No available time slots for this day'),
      );
    }

    final dateFormat = DateFormat('EEEE, MMMM d, yyyy');

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Selected Date: ${dateFormat.format(selectedDate)}',
          style: Theme.of(context).textTheme.titleMedium,
        ),
        const SizedBox(height: 16),
        Text(
          'Available Time Slots:',
          style: Theme.of(context).textTheme.titleSmall,
        ),
        const SizedBox(height: 8),
        Expanded(
          child: ListView.builder(
            itemCount: availableSlots.length,
            itemBuilder: (context, index) {
              final slot = availableSlots[index];
              final isSelected = selectedTimeSlot == slot;

              return Card(
                elevation: isSelected ? 4 : 1,
                margin: const EdgeInsets.only(bottom: 8),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                  side: isSelected
                      ? BorderSide(
                          color: Theme.of(context).colorScheme.primary,
                          width: 2,
                        )
                      : BorderSide.none,
                ),
                child: InkWell(
                  onTap: () {
                    ref.read(selectedTimeSlotProvider.notifier).state = slot;

                    if (widget.onBookingSelected != null) {
                      widget.onBookingSelected!(
                        selectedDate,
                        slot,
                        participantsCount,
                      );
                    }
                  },
                  borderRadius: BorderRadius.circular(8),
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Row(
                      children: [
                        Icon(
                          Icons.access_time,
                          color: isSelected
                              ? Theme.of(context).colorScheme.primary
                              : Colors.grey,
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                slot.formatted,
                                style: Theme.of(context).textTheme.titleMedium,
                              ),
                              Text(
                                slot.currentParticipants > 0
                                    ? 'Remaining spots: ${slot.remainingSpots} of ${slot.maxParticipants}'
                                    : 'Max participants: ${slot.maxParticipants}',
                                style: Theme.of(context)
                                    .textTheme
                                    .bodySmall
                                    ?.copyWith(
                                      color: slot.remainingSpots < 3
                                          ? Colors.red
                                          : null,
                                      fontWeight: slot.remainingSpots < 3
                                          ? FontWeight.bold
                                          : null,
                                    ),
                              ),
                            ],
                          ),
                        ),
                        Text(
                          '\$${slot.price.toStringAsFixed(2)}',
                          style: Theme.of(context)
                              .textTheme
                              .titleMedium
                              ?.copyWith(
                                color: Theme.of(context).colorScheme.primary,
                              ),
                        ),
                      ],
                    ),
                  ),
                ),
              );
            },
          ),
        ),
        const SizedBox(height: 16),
        Text(
          'Number of Participants:',
          style: Theme.of(context).textTheme.titleSmall,
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            IconButton(
              onPressed: participantsCount > 1
                  ? () {
                      ref.read(participantsCountProvider.notifier).state--;
                    }
                  : null,
              icon: const Icon(Icons.remove_circle_outline),
            ),
            Expanded(
              child: Text(
                '$participantsCount',
                textAlign: TextAlign.center,
                style: Theme.of(context).textTheme.titleLarge,
              ),
            ),
            IconButton(
              onPressed:
                  participantsCount < (selectedTimeSlot?.maxParticipants ?? 10)
                      ? () {
                          ref.read(participantsCountProvider.notifier).state++;
                        }
                      : null,
              icon: const Icon(Icons.add_circle_outline),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildCalendarDay(DateTime day, bool isAvailable) {
    return Container(
      margin: const EdgeInsets.all(4),
      decoration: BoxDecoration(
        color: isAvailable
            ? Colors.green.withAlpha(25) // 0.1 * 255 = 25.5 ≈ 25
            : Colors.grey.withAlpha(25), // 0.1 * 255 = 25.5 ≈ 25
        shape: BoxShape.circle,
      ),
      child: Center(
        child: Text(
          '${day.day}',
          style: TextStyle(
            color: isAvailable ? Colors.black : Colors.grey,
          ),
        ),
      ),
    );
  }

  Widget _buildLegendItem(Color color, String label) {
    return Row(
      children: [
        Container(
          width: 16,
          height: 16,
          decoration: BoxDecoration(
            color: color.withAlpha(51), // 0.2 * 255 = 51
            shape: BoxShape.circle,
          ),
        ),
        const SizedBox(width: 4),
        Text(label),
      ],
    );
  }
}
