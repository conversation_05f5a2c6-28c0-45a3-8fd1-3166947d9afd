import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:culture_connect/models/translation/cultural_context_model.dart';
import 'package:culture_connect/models/translation/translation_cultural_context.dart';
import 'package:culture_connect/theme/app_theme.dart';

/// A widget for displaying a cultural context indicator
class CulturalContextIndicator extends StatelessWidget {
  /// The cultural context information
  final TranslationCulturalContext culturalContext;

  /// Whether to show the label
  final bool showLabel;

  /// Whether to use a compact layout
  final bool compact;

  /// Whether to use light colors (for dark backgrounds)
  final bool useLight;

  /// Callback when the indicator is tapped
  final VoidCallback? onTap;

  /// Creates a new cultural context indicator
  const CulturalContextIndicator({
    super.key,
    required this.culturalContext,
    this.showLabel = true,
    this.compact = false,
    this.useLight = false,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    if (!culturalContext.hasContextInformation) {
      return const SizedBox.shrink();
    }

    return GestureDetector(
      onTap: onTap,
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Context icon
          Icon(
            Icons.info_outline,
            size: compact ? 14.r : 18.r,
            color: useLight ? Colors.white70 : AppTheme.primaryColor,
          ),

          if (showLabel) ...[
            SizedBox(width: 4.w),

            // Context label
            Text(
              compact ? 'Cultural Note' : 'Cultural Context',
              style: TextStyle(
                fontSize: compact ? 10.sp : 12.sp,
                color: useLight ? Colors.white70 : AppTheme.textSecondaryColor,
              ),
            ),
          ],

          // Badge with count
          if (culturalContext.noteCount > 0) ...[
            SizedBox(width: 4.w),
            Container(
              padding: EdgeInsets.symmetric(horizontal: 4.w),
              decoration: BoxDecoration(
                color: useLight
                    ? Colors.white24
                    : AppTheme.primaryColor.withAlpha(51),
                borderRadius: BorderRadius.circular(8.r),
              ),
              child: Text(
                culturalContext.noteCount.toString(),
                style: TextStyle(
                  fontSize: compact ? 8.sp : 10.sp,
                  fontWeight: FontWeight.bold,
                  color: useLight ? Colors.white : AppTheme.primaryColor,
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }
}

/// A widget for displaying a cultural context note
class CulturalContextNoteCard extends StatelessWidget {
  /// The cultural context note
  final CulturalContextNote note;

  /// Creates a new cultural context note card
  const CulturalContextNoteCard({
    super.key,
    required this.note,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      margin: EdgeInsets.only(bottom: 12.h),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12.r),
        side: BorderSide(
          color: note.type.color.withAlpha(77),
          width: 1.w,
        ),
      ),
      child: Padding(
        padding: EdgeInsets.all(12.r),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              children: [
                Icon(
                  note.type.icon,
                  size: 20.r,
                  color: note.type.color,
                ),
                SizedBox(width: 8.w),
                Text(
                  note.type.displayName,
                  style: TextStyle(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.bold,
                    color: AppTheme.textPrimaryColor,
                  ),
                ),
                if (note.isSensitive) ...[
                  const Spacer(),
                  Container(
                    padding: EdgeInsets.symmetric(
                      horizontal: 8.w,
                      vertical: 2.h,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.red.withAlpha(26),
                      borderRadius: BorderRadius.circular(8.r),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          Icons.warning_amber_outlined,
                          size: 14.r,
                          color: Colors.red,
                        ),
                        SizedBox(width: 4.w),
                        Text(
                          'Sensitive',
                          style: TextStyle(
                            fontSize: 12.sp,
                            color: Colors.red,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ],
            ),

            SizedBox(height: 8.h),

            // Text segment
            Container(
              width: double.infinity,
              padding: EdgeInsets.all(8.r),
              decoration: BoxDecoration(
                color: note.type.color.withAlpha(26),
                borderRadius: BorderRadius.circular(8.r),
              ),
              child: Text(
                '"${note.textSegment}"',
                style: TextStyle(
                  fontSize: 14.sp,
                  fontStyle: FontStyle.italic,
                  color: AppTheme.textPrimaryColor,
                ),
              ),
            ),

            SizedBox(height: 8.h),

            // Explanation
            Text(
              note.explanation,
              style: TextStyle(
                fontSize: 14.sp,
                color: AppTheme.textPrimaryColor,
              ),
            ),

            // Alternatives (if available)
            if (note.alternatives != null && note.alternatives!.isNotEmpty) ...[
              SizedBox(height: 12.h),
              Text(
                'Alternative Expressions:',
                style: TextStyle(
                  fontSize: 14.sp,
                  fontWeight: FontWeight.w500,
                  color: AppTheme.textSecondaryColor,
                ),
              ),
              SizedBox(height: 4.h),
              Wrap(
                spacing: 8.w,
                runSpacing: 8.h,
                children: note.alternatives!.map((alternative) {
                  return Container(
                    padding: EdgeInsets.symmetric(
                      horizontal: 8.w,
                      vertical: 4.h,
                    ),
                    decoration: BoxDecoration(
                      color: note.type.color.withAlpha(26),
                      borderRadius: BorderRadius.circular(8.r),
                      border: Border.all(
                        color: note.type.color.withAlpha(77),
                        width: 1.w,
                      ),
                    ),
                    child: Text(
                      alternative,
                      style: TextStyle(
                        fontSize: 12.sp,
                        color: AppTheme.textPrimaryColor,
                      ),
                    ),
                  );
                }).toList(),
              ),
            ],

            // Additional information
            if (note.region != null || note.formalityLevel != null) ...[
              SizedBox(height: 12.h),
              Wrap(
                spacing: 8.w,
                runSpacing: 8.h,
                children: [
                  if (note.region != null)
                    _buildInfoChip(
                      Icons.location_on_outlined,
                      note.region!,
                      Colors.green,
                    ),
                  if (note.formalityLevel != null)
                    _buildInfoChip(
                      Icons.person_outline,
                      note.formalityLevel!,
                      Colors.blue,
                    ),
                ],
              ),
            ],

            // Resources (if available)
            if (note.resources != null && note.resources!.isNotEmpty) ...[
              SizedBox(height: 12.h),
              Text(
                'Learn More:',
                style: TextStyle(
                  fontSize: 14.sp,
                  fontWeight: FontWeight.w500,
                  color: AppTheme.textSecondaryColor,
                ),
              ),
              SizedBox(height: 4.h),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: note.resources!.map((resource) {
                  return Padding(
                    padding: EdgeInsets.only(bottom: 4.h),
                    child: Row(
                      children: [
                        Icon(
                          Icons.link,
                          size: 14.r,
                          color: AppTheme.primaryColor,
                        ),
                        SizedBox(width: 4.w),
                        Expanded(
                          child: Text(
                            resource,
                            style: TextStyle(
                              fontSize: 12.sp,
                              color: AppTheme.primaryColor,
                              decoration: TextDecoration.underline,
                            ),
                          ),
                        ),
                      ],
                    ),
                  );
                }).toList(),
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// Build an info chip
  Widget _buildInfoChip(IconData icon, String label, Color color) {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: 8.w,
        vertical: 4.h,
      ),
      decoration: BoxDecoration(
        color: color.withAlpha(26),
        borderRadius: BorderRadius.circular(8.r),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: 14.r,
            color: color,
          ),
          SizedBox(width: 4.w),
          Text(
            label,
            style: TextStyle(
              fontSize: 12.sp,
              color: color,
            ),
          ),
        ],
      ),
    );
  }
}
