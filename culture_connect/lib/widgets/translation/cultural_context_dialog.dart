import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:culture_connect/models/translation/cultural_context_model.dart';
import 'package:culture_connect/models/translation/translation_cultural_context.dart';
import 'package:culture_connect/providers/cultural_context_provider.dart';
import 'package:culture_connect/theme/app_theme.dart';
import 'package:culture_connect/widgets/translation/cultural_context_indicator.dart';

/// A dialog for displaying cultural context information
class CulturalContextDialog extends ConsumerStatefulWidget {
  /// The cultural context information
  final TranslationCulturalContext culturalContext;

  /// Creates a new cultural context dialog
  const CulturalContextDialog({
    super.key,
    required this.culturalContext,
  });

  @override
  ConsumerState<CulturalContextDialog> createState() =>
      _CulturalContextDialogState();
}

class _CulturalContextDialogState extends ConsumerState<CulturalContextDialog>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  CulturalContextType? _selectedType;
  bool _showSensitiveContent = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _showSensitiveContent = ref.read(showSensitiveContentProvider);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16.r),
      ),
      child: Container(
        width: double.infinity,
        constraints: BoxConstraints(
          maxWidth: 500.w,
          maxHeight: MediaQuery.of(context).size.height * 0.8,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header
            Container(
              padding: EdgeInsets.all(16.r),
              decoration: BoxDecoration(
                color: AppTheme.primaryColor,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(16.r),
                  topRight: Radius.circular(16.r),
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.info_outline,
                    size: 24.r,
                    color: Colors.white,
                  ),
                  SizedBox(width: 8.w),
                  Text(
                    'Cultural Context',
                    style: TextStyle(
                      fontSize: 18.sp,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                  const Spacer(),
                  IconButton(
                    icon: const Icon(Icons.close, color: Colors.white),
                    onPressed: () => Navigator.of(context).pop(),
                  ),
                ],
              ),
            ),

            // Tab bar
            TabBar(
              controller: _tabController,
              labelColor: AppTheme.primaryColor,
              unselectedLabelColor: Colors.grey,
              indicatorColor: AppTheme.primaryColor,
              tabs: const [
                Tab(text: 'Notes'),
                Tab(text: 'About'),
              ],
            ),

            // Tab content
            Expanded(
              child: TabBarView(
                controller: _tabController,
                children: [
                  // Notes tab
                  _buildNotesTab(),

                  // About tab
                  _buildAboutTab(),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Build the notes tab
  Widget _buildNotesTab() {
    // Filter notes based on selected type and sensitivity
    final notes = widget.culturalContext.notes.where((note) {
      if (_selectedType != null && note.type != _selectedType) {
        return false;
      }
      if (note.isSensitive && !_showSensitiveContent) {
        return false;
      }
      return true;
    }).toList();

    return Column(
      children: [
        // Filter bar
        Padding(
          padding: EdgeInsets.all(12.r),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Type filter
              Text(
                'Filter by Type:',
                style: TextStyle(
                  fontSize: 14.sp,
                  fontWeight: FontWeight.w500,
                  color: AppTheme.textSecondaryColor,
                ),
              ),
              SizedBox(height: 8.h),
              SingleChildScrollView(
                scrollDirection: Axis.horizontal,
                child: Row(
                  children: [
                    // All types option
                    _buildTypeFilterChip(null, 'All'),
                    SizedBox(width: 8.w),

                    // Type options
                    ...CulturalContextType.values.map((type) {
                      return Padding(
                        padding: EdgeInsets.only(right: 8.w),
                        child: _buildTypeFilterChip(type, type.displayName),
                      );
                    }),
                  ],
                ),
              ),

              SizedBox(height: 12.h),

              // Sensitive content toggle
              Row(
                children: [
                  Text(
                    'Show Sensitive Content:',
                    style: TextStyle(
                      fontSize: 14.sp,
                      fontWeight: FontWeight.w500,
                      color: AppTheme.textSecondaryColor,
                    ),
                  ),
                  const Spacer(),
                  Switch(
                    value: _showSensitiveContent,
                    onChanged: (value) {
                      setState(() {
                        _showSensitiveContent = value;
                      });
                      ref
                          .read(culturalContextNotifierProvider.notifier)
                          .setShowSensitiveContent(value);
                    },
                    activeColor: AppTheme.primaryColor,
                  ),
                ],
              ),

              // Divider
              Divider(height: 24.h),
            ],
          ),
        ),

        // Notes list
        Expanded(
          child: notes.isEmpty
              ? Center(
                  child: Text(
                    _selectedType != null
                        ? 'No ${_selectedType!.displayName} notes available'
                        : 'No cultural context notes available',
                    style: TextStyle(
                      fontSize: 16.sp,
                      color: AppTheme.textSecondaryColor,
                    ),
                  ),
                )
              : ListView.builder(
                  padding: EdgeInsets.symmetric(horizontal: 16.w),
                  itemCount: notes.length,
                  itemBuilder: (context, index) {
                    return CulturalContextNoteCard(note: notes[index]);
                  },
                ),
        ),
      ],
    );
  }

  /// Build the about tab
  Widget _buildAboutTab() {
    return SingleChildScrollView(
      padding: EdgeInsets.all(16.r),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Language pair information
          Text(
            'Language Information',
            style: TextStyle(
              fontSize: 18.sp,
              fontWeight: FontWeight.bold,
              color: AppTheme.textPrimaryColor,
            ),
          ),
          SizedBox(height: 8.h),
          _buildLanguagePairInfo(),

          SizedBox(height: 16.h),

          // General cultural note
          if (widget.culturalContext.generalCulturalNote != null) ...[
            Text(
              'Cultural Context',
              style: TextStyle(
                fontSize: 18.sp,
                fontWeight: FontWeight.bold,
                color: AppTheme.textPrimaryColor,
              ),
            ),
            SizedBox(height: 8.h),
            Container(
              width: double.infinity,
              padding: EdgeInsets.all(12.r),
              decoration: BoxDecoration(
                color: Colors.blue.withAlpha(26),
                borderRadius: BorderRadius.circular(12.r),
                border: Border.all(
                  color: Colors.blue.withAlpha(77),
                  width: 1.w,
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.info_outline,
                        size: 20.r,
                        color: Colors.blue,
                      ),
                      SizedBox(width: 8.w),
                      Text(
                        'General Cultural Note',
                        style: TextStyle(
                          fontSize: 16.sp,
                          fontWeight: FontWeight.bold,
                          color: AppTheme.textPrimaryColor,
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 8.h),
                  Text(
                    widget.culturalContext.generalCulturalNote!,
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: AppTheme.textPrimaryColor,
                    ),
                  ),
                ],
              ),
            ),
          ],

          SizedBox(height: 16.h),

          // Note type information
          Text(
            'Types of Cultural Notes',
            style: TextStyle(
              fontSize: 18.sp,
              fontWeight: FontWeight.bold,
              color: AppTheme.textPrimaryColor,
            ),
          ),
          SizedBox(height: 8.h),
          ...CulturalContextType.values.map((type) {
            return _buildNoteTypeInfo(type);
          }),
        ],
      ),
    );
  }

  /// Build a type filter chip
  Widget _buildTypeFilterChip(CulturalContextType? type, String label) {
    final isSelected = _selectedType == type;
    final color = type?.color ?? Colors.grey;

    return GestureDetector(
      onTap: () {
        setState(() {
          _selectedType = isSelected ? null : type;
        });
      },
      child: Container(
        padding: EdgeInsets.symmetric(
          horizontal: 12.w,
          vertical: 6.h,
        ),
        decoration: BoxDecoration(
          color: isSelected ? color.withAlpha(51) : Colors.grey[100],
          borderRadius: BorderRadius.circular(16.r),
          border: Border.all(
            color: isSelected ? color : Colors.grey[300]!,
            width: 1.w,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (type != null) ...[
              Icon(
                type.icon,
                size: 16.r,
                color: isSelected ? color : Colors.grey,
              ),
              SizedBox(width: 4.w),
            ],
            Text(
              label,
              style: TextStyle(
                fontSize: 14.sp,
                color: isSelected ? color : Colors.grey[700],
                fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Build language pair information
  Widget _buildLanguagePairInfo() {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(12.r),
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text(
                _getLanguageName(widget.culturalContext.sourceLanguage),
                style: TextStyle(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.bold,
                  color: AppTheme.textPrimaryColor,
                ),
              ),
              SizedBox(width: 8.w),
              Icon(
                Icons.arrow_forward,
                size: 16.r,
                color: Colors.grey,
              ),
              SizedBox(width: 8.w),
              Text(
                _getLanguageName(widget.culturalContext.targetLanguage),
                style: TextStyle(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.bold,
                  color: AppTheme.textPrimaryColor,
                ),
              ),
            ],
          ),
          if (widget.culturalContext.sourceRegion != null ||
              widget.culturalContext.targetRegion != null) ...[
            SizedBox(height: 8.h),
            Row(
              children: [
                if (widget.culturalContext.sourceRegion != null)
                  Text(
                    '${widget.culturalContext.sourceRegion} dialect',
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: AppTheme.textSecondaryColor,
                    ),
                  ),
                if (widget.culturalContext.sourceRegion != null &&
                    widget.culturalContext.targetRegion != null) ...[
                  SizedBox(width: 8.w),
                  Icon(
                    Icons.arrow_forward,
                    size: 14.r,
                    color: Colors.grey,
                  ),
                  SizedBox(width: 8.w),
                ],
                if (widget.culturalContext.targetRegion != null)
                  Text(
                    '${widget.culturalContext.targetRegion} dialect',
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: AppTheme.textSecondaryColor,
                    ),
                  ),
              ],
            ),
          ],
          SizedBox(height: 8.h),
          Text(
            'Cultural context notes: ${widget.culturalContext.noteCount}',
            style: TextStyle(
              fontSize: 14.sp,
              color: AppTheme.textSecondaryColor,
            ),
          ),
        ],
      ),
    );
  }

  /// Build note type information
  Widget _buildNoteTypeInfo(CulturalContextType type) {
    return Padding(
      padding: EdgeInsets.only(bottom: 8.h),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(
            type.icon,
            size: 20.r,
            color: type.color,
          ),
          SizedBox(width: 8.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  type.displayName,
                  style: TextStyle(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.w500,
                    color: AppTheme.textPrimaryColor,
                  ),
                ),
                SizedBox(height: 2.h),
                Text(
                  _getNoteTypeDescription(type),
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: AppTheme.textSecondaryColor,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Get the language name from a language code
  String _getLanguageName(String code) {
    switch (code) {
      case 'en':
        return 'English';
      case 'fr':
        return 'French';
      case 'yo':
        return 'Yoruba';
      case 'ig':
        return 'Igbo';
      case 'ha':
        return 'Hausa';
      case 'sw':
        return 'Swahili';
      default:
        return code;
    }
  }

  /// Get the description for a note type
  String _getNoteTypeDescription(CulturalContextType type) {
    switch (type) {
      case CulturalContextType.general:
        return 'General cultural information relevant to the translation.';
      case CulturalContextType.idiom:
        return 'Expressions that cannot be understood from the individual meanings of their words.';
      case CulturalContextType.reference:
        return 'References to cultural elements, events, or figures that may not be familiar.';
      case CulturalContextType.taboo:
        return 'Content that may be considered sensitive or inappropriate in certain contexts.';
      case CulturalContextType.regional:
        return 'Usage specific to certain regions or dialects.';
      case CulturalContextType.slang:
        return 'Informal language or expressions used by particular groups.';
      case CulturalContextType.formality:
        return 'Information about formal vs. informal usage and social context.';
      case CulturalContextType.historical:
        return 'Historical context that helps understand the meaning or significance.';
      case CulturalContextType.religious:
        return 'References to religious concepts, practices, or beliefs.';
    }
  }
}
