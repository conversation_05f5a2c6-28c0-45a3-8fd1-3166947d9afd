import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';
import 'package:culture_connect/models/loyalty/loyalty_points_transaction.dart';
import 'package:culture_connect/providers/loyalty_provider.dart';
import 'package:culture_connect/theme/app_theme.dart';

/// A widget that displays loyalty points history
class LoyaltyPointsHistory extends ConsumerWidget {
  /// The maximum number of transactions to display
  final int? maxTransactions;

  /// Whether to show the see all button
  final bool showSeeAllButton;

  /// Callback when the see all button is tapped
  final VoidCallback? onSeeAllTapped;

  /// Creates a new loyalty points history widget
  const LoyaltyPointsHistory({
    super.key,
    this.maxTransactions,
    this.showSeeAllButton = false,
    this.onSeeAllTapped,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final transactionsAsync = ref.watch(loyaltyPointsTransactionsProvider);

    // Sort transactions by date (newest first)
    final sortedTransactions =
        List<LoyaltyPointsTransaction>.from(transactionsAsync)
          ..sort((a, b) => b.date.compareTo(a.date));

    // Limit the number of transactions if maxTransactions is provided
    final displayedTransactions = maxTransactions != null
        ? sortedTransactions.take(maxTransactions!).toList()
        : sortedTransactions;

    if (displayedTransactions.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.history,
              size: 48.r,
              color: Colors.grey[400],
            ),
            SizedBox(height: 16.h),
            Text(
              'No points history yet',
              style: TextStyle(
                fontSize: 16.sp,
                color: AppTheme.textSecondaryColor,
              ),
            ),
            SizedBox(height: 8.h),
            Text(
              'Start earning points by booking experiences and travel services',
              style: TextStyle(
                fontSize: 14.sp,
                color: AppTheme.textSecondaryColor,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Header
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 16.w),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Points History',
                style: TextStyle(
                  fontSize: 18.sp,
                  fontWeight: FontWeight.bold,
                  color: AppTheme.textPrimaryColor,
                ),
              ),
              if (showSeeAllButton &&
                  sortedTransactions.length > (maxTransactions ?? 0))
                TextButton(
                  onPressed: onSeeAllTapped,
                  child: Text(
                    'See All',
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: AppTheme.primaryColor,
                    ),
                  ),
                ),
            ],
          ),
        ),

        SizedBox(height: 8.h),

        // Transactions list
        ListView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: displayedTransactions.length,
          itemBuilder: (context, index) {
            final transaction = displayedTransactions[index];
            return _buildTransactionItem(context, transaction);
          },
        ),
      ],
    );
  }

  Widget _buildTransactionItem(
      BuildContext context, LoyaltyPointsTransaction transaction) {
    final isEarning = transaction.isEarning;
    final pointsColor = isEarning ? Colors.green : Colors.red;

    return Card(
      margin: EdgeInsets.symmetric(horizontal: 16.w, vertical: 4.h),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8.r),
      ),
      child: Padding(
        padding: EdgeInsets.all(12.r),
        child: Row(
          children: [
            // Transaction type icon
            Container(
              width: 40.r,
              height: 40.r,
              decoration: BoxDecoration(
                color: transaction.type.isEarning
                    ? Colors.green
                        .withAlpha(26) // 0.1 * 255 = 25.5, rounded to 26
                    : Colors.red
                        .withAlpha(26), // 0.1 * 255 = 25.5, rounded to 26
                borderRadius: BorderRadius.circular(8.r),
              ),
              child: Center(
                child: Icon(
                  transaction.type.icon,
                  size: 20.r,
                  color: transaction.type.isEarning ? Colors.green : Colors.red,
                ),
              ),
            ),

            SizedBox(width: 12.w),

            // Transaction details
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    transaction.type.displayName,
                    style: TextStyle(
                      fontSize: 14.sp,
                      fontWeight: FontWeight.bold,
                      color: AppTheme.textPrimaryColor,
                    ),
                  ),
                  SizedBox(height: 4.h),
                  Text(
                    transaction.description,
                    style: TextStyle(
                      fontSize: 12.sp,
                      color: AppTheme.textSecondaryColor,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  SizedBox(height: 4.h),
                  Text(
                    _formatDate(transaction.date),
                    style: TextStyle(
                      fontSize: 10.sp,
                      color: AppTheme.textSecondaryColor,
                    ),
                  ),
                ],
              ),
            ),

            SizedBox(width: 12.w),

            // Points
            Text(
              transaction.formattedPoints,
              style: TextStyle(
                fontSize: 16.sp,
                fontWeight: FontWeight.bold,
                color: pointsColor,
              ),
            ),
          ],
        ),
      ),
    );
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays < 1) {
      return 'Today, ${DateFormat('h:mm a').format(date)}';
    } else if (difference.inDays < 2) {
      return 'Yesterday, ${DateFormat('h:mm a').format(date)}';
    } else if (difference.inDays < 7) {
      return DateFormat('EEEE, h:mm a').format(date);
    } else {
      return DateFormat('MMM d, yyyy').format(date);
    }
  }
}
