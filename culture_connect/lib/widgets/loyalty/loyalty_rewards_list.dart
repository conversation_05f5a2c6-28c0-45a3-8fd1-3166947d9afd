import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:culture_connect/models/loyalty/loyalty_program_model.dart';
import 'package:culture_connect/models/loyalty/loyalty_reward.dart';
import 'package:culture_connect/providers/loyalty_provider.dart';
import 'package:culture_connect/screens/loyalty/loyalty_reward_details_screen.dart';
import 'package:culture_connect/theme/app_theme.dart';

/// A widget that displays a list of loyalty rewards
class LoyaltyRewardsList extends ConsumerWidget {
  /// The loyalty program
  final LoyaltyProgramModel loyaltyProgram;

  /// The maximum number of rewards to display
  final int? maxRewards;

  /// Whether to show the see all button
  final bool showSeeAllButton;

  /// Callback when the see all button is tapped
  final VoidCallback? onSeeAllTapped;

  /// Whether to show only available rewards
  final bool showOnlyAvailable;

  /// Creates a new loyalty rewards list widget
  const LoyaltyRewardsList({
    super.key,
    required this.loyaltyProgram,
    this.maxRewards,
    this.showSeeAllButton = false,
    this.onSeeAllTapped,
    this.showOnlyAvailable = true,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final rewardsAsync = ref.watch(loyaltyRewardsProvider);

    return rewardsAsync.when(
      data: (rewards) {
        // Filter rewards based on showOnlyAvailable
        final filteredRewards = showOnlyAvailable
            ? rewards
                .where(
                    (reward) => reward.status == LoyaltyRewardStatus.available)
                .toList()
            : rewards;

        // Sort rewards by points required (lowest first)
        filteredRewards
            .sort((a, b) => a.pointsRequired.compareTo(b.pointsRequired));

        // Limit the number of rewards if maxRewards is provided
        final displayedRewards = maxRewards != null
            ? filteredRewards.take(maxRewards!).toList()
            : filteredRewards;

        if (displayedRewards.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.card_giftcard,
                  size: 48.r,
                  color: Colors.grey[400],
                ),
                SizedBox(height: 16.h),
                Text(
                  'No rewards available',
                  style: TextStyle(
                    fontSize: 16.sp,
                    color: AppTheme.textSecondaryColor,
                  ),
                ),
                SizedBox(height: 8.h),
                Text(
                  'Check back later for new rewards',
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: AppTheme.textSecondaryColor,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          );
        }

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 16.w),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Rewards',
                    style: TextStyle(
                      fontSize: 18.sp,
                      fontWeight: FontWeight.bold,
                      color: AppTheme.textPrimaryColor,
                    ),
                  ),
                  if (showSeeAllButton &&
                      filteredRewards.length > (maxRewards ?? 0))
                    TextButton(
                      onPressed: onSeeAllTapped,
                      child: Text(
                        'See All',
                        style: TextStyle(
                          fontSize: 14.sp,
                          color: AppTheme.primaryColor,
                        ),
                      ),
                    ),
                ],
              ),
            ),

            SizedBox(height: 8.h),

            // Rewards list
            ListView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: displayedRewards.length,
              itemBuilder: (context, index) {
                final reward = displayedRewards[index];
                return _buildRewardItem(context, reward, ref);
              },
            ),
          ],
        );
      },
      loading: () => const Center(
        child: CircularProgressIndicator(),
      ),
      error: (error, stackTrace) => Center(
        child: Text(
          'Error loading rewards: $error',
          style: TextStyle(
            fontSize: 16.sp,
            color: Colors.red,
          ),
        ),
      ),
    );
  }

  Widget _buildRewardItem(
      BuildContext context, LoyaltyReward reward, WidgetRef ref) {
    final canRedeem = loyaltyProgram.pointsBalance >= reward.pointsRequired &&
        (reward.minimumTier == null ||
            loyaltyProgram.tier.index >= reward.minimumTier!.index);

    return Card(
      margin: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: InkWell(
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => LoyaltyRewardDetailsScreen(
                reward: reward,
                loyaltyProgram: loyaltyProgram,
              ),
            ),
          );
        },
        borderRadius: BorderRadius.circular(12.r),
        child: Padding(
          padding: EdgeInsets.all(16.r),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Reward type and points
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  // Reward type
                  Container(
                    padding:
                        EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
                    decoration: BoxDecoration(
                      color: AppTheme.primaryColor
                          .withAlpha(26), // 0.1 * 255 = 25.5, rounded to 26
                      borderRadius: BorderRadius.circular(8.r),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          reward.type.icon,
                          size: 16.r,
                          color: AppTheme.primaryColor,
                        ),
                        SizedBox(width: 4.w),
                        Text(
                          reward.type.displayName,
                          style: TextStyle(
                            fontSize: 12.sp,
                            fontWeight: FontWeight.bold,
                            color: AppTheme.primaryColor,
                          ),
                        ),
                      ],
                    ),
                  ),

                  // Points required
                  Row(
                    children: [
                      Icon(
                        Icons.star,
                        size: 16.r,
                        color: AppTheme.primaryColor,
                      ),
                      SizedBox(width: 4.w),
                      Text(
                        '${reward.pointsRequired} points',
                        style: TextStyle(
                          fontSize: 14.sp,
                          fontWeight: FontWeight.bold,
                          color: AppTheme.textPrimaryColor,
                        ),
                      ),
                    ],
                  ),
                ],
              ),

              SizedBox(height: 12.h),

              // Reward name
              Text(
                reward.name,
                style: TextStyle(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.bold,
                  color: AppTheme.textPrimaryColor,
                ),
              ),

              SizedBox(height: 4.h),

              // Reward description
              Text(
                reward.description,
                style: TextStyle(
                  fontSize: 14.sp,
                  color: AppTheme.textSecondaryColor,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),

              SizedBox(height: 12.h),

              // Minimum tier and value
              Row(
                children: [
                  if (reward.minimumTier != null) ...[
                    Icon(
                      reward.minimumTier!.icon,
                      size: 16.r,
                      color: reward.minimumTier!.color,
                    ),
                    SizedBox(width: 4.w),
                    Text(
                      '${reward.minimumTier!.displayName} tier required',
                      style: TextStyle(
                        fontSize: 12.sp,
                        color: AppTheme.textSecondaryColor,
                      ),
                    ),
                    SizedBox(width: 8.w),
                    Container(
                      width: 1.w,
                      height: 16.h,
                      color: Colors.grey[300],
                    ),
                    SizedBox(width: 8.w),
                  ],
                  if (reward.formattedValue != null) ...[
                    Icon(
                      Icons.monetization_on,
                      size: 16.r,
                      color: Colors.amber,
                    ),
                    SizedBox(width: 4.w),
                    Text(
                      'Value: ${reward.formattedValue}',
                      style: TextStyle(
                        fontSize: 12.sp,
                        color: AppTheme.textSecondaryColor,
                      ),
                    ),
                  ],
                ],
              ),

              SizedBox(height: 16.h),

              // Redeem button
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: canRedeem
                      ? () => _redeemReward(context, reward, ref)
                      : null,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppTheme.primaryColor,
                    foregroundColor: Colors.white,
                    padding: EdgeInsets.symmetric(vertical: 12.h),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8.r),
                    ),
                  ),
                  child: Text(
                    canRedeem ? 'Redeem Reward' : 'Not Enough Points',
                    style: TextStyle(
                      fontSize: 14.sp,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _redeemReward(
      BuildContext context, LoyaltyReward reward, WidgetRef ref) async {
    // Show confirmation dialog
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Redeem Reward'),
        content: Text(
          'Are you sure you want to redeem ${reward.name} for ${reward.pointsRequired} points?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.primaryColor,
              foregroundColor: Colors.white,
            ),
            child: const Text('Redeem'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        // Show loading indicator
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Processing redemption...'),
              duration: Duration(seconds: 1),
            ),
          );
        }

        // Redeem the reward
        await ref
            .read(redeemLoyaltyRewardNotifierProvider.notifier)
            .redeemReward(reward.id);

        if (context.mounted) {
          // Show success message
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Successfully redeemed ${reward.name}'),
              backgroundColor: Colors.green,
            ),
          );

          // Navigate to reward details screen
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => LoyaltyRewardDetailsScreen(
                reward: reward.copyWith(
                  status: LoyaltyRewardStatus.redeemed,
                  redemptionDate: DateTime.now(),
                ),
                loyaltyProgram: loyaltyProgram.copyWith(
                  pointsBalance:
                      loyaltyProgram.pointsBalance - reward.pointsRequired,
                ),
              ),
            ),
          );
        }
      } catch (e) {
        if (context.mounted) {
          // Show error message
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Error redeeming reward: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      } finally {
        // Reset the notifier
        ref.read(redeemLoyaltyRewardNotifierProvider.notifier).reset();
      }
    }
  }
}
