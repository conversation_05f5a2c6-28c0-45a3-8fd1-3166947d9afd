import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:culture_connect/providers/navigation_provider.dart';
import 'package:culture_connect/theme/app_theme.dart';

/// A custom bottom navigation bar with Riverpod integration.
///
/// This component combines the best features of both navigation implementations:
/// - Riverpod state management for navigation
/// - Animated transitions between states
/// - Multiple style options (standard, fancy with center button)
/// - Customizable appearance
class CustomBottomNavigation extends ConsumerWidget {
  /// The style of the bottom navigation bar
  final BottomNavStyle style;

  /// Whether to show labels for the navigation items
  final bool showLabels;

  /// The height of the navigation bar (excluding safe area)
  final double height;

  /// The size of the icons
  final double iconSize;

  /// The font size for selected items
  final double selectedFontSize;

  /// The font size for unselected items
  final double unselectedFontSize;

  const CustomBottomNavigation({
    super.key,
    this.style = BottomNavStyle.standard,
    this.showLabels = true,
    this.height = 60,
    this.iconSize = 24,
    this.selectedFontSize = 12,
    this.unselectedFontSize = 10,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final currentIndex = ref.watch(navigationProvider);
    final theme = Theme.of(context);

    // Use the appropriate style
    if (style == BottomNavStyle.fancy) {
      return _buildFancyNavigation(context, ref, currentIndex);
    }

    return Container(
      height: height + MediaQuery.of(context).padding.bottom,
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        boxShadow: [
          BoxShadow(
            color:
                Colors.black.withAlpha(26), // 0.1 * 255 = 25.5, rounded to 26
            blurRadius: 10,
            offset: const Offset(0, -5),
          ),
        ],
      ),
      child: SafeArea(
        top: false,
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: navigationDestinations.map((destination) {
              final isSelected = currentIndex == destination.item;
              return _NavigationItem(
                destination: destination,
                isSelected: isSelected,
                showLabel: showLabels,
                iconSize: iconSize,
                selectedFontSize: selectedFontSize,
                unselectedFontSize: unselectedFontSize,
                onTap: () {
                  ref
                      .read(navigationProvider.notifier)
                      .setNavigationItem(destination.item);
                },
              );
            }).toList(),
          ),
        ),
      ),
    );
  }

  Widget _buildFancyNavigation(
    BuildContext context,
    WidgetRef ref,
    NavigationItem currentIndex,
  ) {
    return Container(
      height: height + MediaQuery.of(context).padding.bottom,
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color:
                Colors.black.withAlpha(13), // 0.05 * 255 = 12.75, rounded to 13
            blurRadius: 10,
            offset: const Offset(0, -5),
          ),
        ],
      ),
      child: SafeArea(
        top: false,
        child: Stack(
          alignment: Alignment.center,
          children: [
            // Bottom nav items
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                _buildFancyNavItem(
                  context: context,
                  ref: ref,
                  destination: navigationDestinations[0],
                  isSelected: currentIndex == NavigationItem.home,
                ),
                _buildFancyNavItem(
                  context: context,
                  ref: ref,
                  destination: navigationDestinations[1],
                  isSelected: currentIndex == NavigationItem.explore,
                ),
                // Empty space for center button
                const SizedBox(width: 60),
                _buildFancyNavItem(
                  context: context,
                  ref: ref,
                  destination: navigationDestinations[3],
                  isSelected: currentIndex == NavigationItem.messages,
                ),
                _buildFancyNavItem(
                  context: context,
                  ref: ref,
                  destination: navigationDestinations[4],
                  isSelected: currentIndex == NavigationItem.profile,
                ),
              ],
            ),

            // Center floating button
            Positioned(
              top: -20,
              child: Container(
                height: 60,
                width: 60,
                decoration: BoxDecoration(
                  gradient: AppTheme.secondaryGradient,
                  shape: BoxShape.circle,
                  boxShadow: [
                    BoxShadow(
                      color: AppTheme.secondaryColor
                          .withAlpha(77), // 0.3 * 255 = 76.5, rounded to 77
                      blurRadius: 10,
                      offset: const Offset(0, 5),
                    ),
                  ],
                ),
                child: Material(
                  color: Colors.transparent,
                  child: InkWell(
                    onTap: () {
                      ref
                          .read(navigationProvider.notifier)
                          .setNavigationItem(NavigationItem.bookings);
                    },
                    customBorder: const CircleBorder(),
                    child: Icon(
                      navigationDestinations[2].selectedIcon,
                      color: Colors.white,
                      size: iconSize,
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFancyNavItem({
    required BuildContext context,
    required WidgetRef ref,
    required CustomNavigationDestination destination,
    required bool isSelected,
  }) {
    final color =
        isSelected ? AppTheme.primaryColor : AppTheme.textSecondaryColor;

    return InkWell(
      onTap: () {
        ref
            .read(navigationProvider.notifier)
            .setNavigationItem(destination.item);
      },
      child: Container(
        height: height,
        padding: const EdgeInsets.symmetric(horizontal: 16),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              isSelected ? destination.selectedIcon : destination.icon,
              size: iconSize,
              color: color,
            ),
            if (showLabels) ...[
              const SizedBox(height: 4),
              Text(
                destination.label,
                style: TextStyle(
                  color: color,
                  fontSize: isSelected ? selectedFontSize : unselectedFontSize,
                  fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}

class _NavigationItem extends StatelessWidget {
  final CustomNavigationDestination destination;
  final bool isSelected;
  final bool showLabel;
  final double iconSize;
  final double selectedFontSize;
  final double unselectedFontSize;
  final VoidCallback onTap;

  const _NavigationItem({
    required this.destination,
    required this.isSelected,
    required this.onTap,
    this.showLabel = true,
    this.iconSize = 24,
    this.selectedFontSize = 12,
    this.unselectedFontSize = 10,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final color = isSelected
        ? theme.colorScheme.primary
        : theme.colorScheme.onSurface.withAlpha(153); // 0.6 * 255 = 153

    return GestureDetector(
      onTap: onTap,
      behavior: HitTestBehavior.opaque,
      child: AnimatedContainer(
        duration: AppTheme.shortAnimation,
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          color: isSelected
              ? theme.colorScheme.primary
                  .withAlpha(26) // 0.1 * 255 = 25.5, rounded to 26
              : Colors.transparent,
          borderRadius: BorderRadius.circular(20),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            AnimatedSwitcher(
              duration: AppTheme.shortAnimation,
              child: Icon(
                isSelected ? destination.selectedIcon : destination.icon,
                key: ValueKey(isSelected),
                color: color,
                size: iconSize,
              ),
            ),
            if (showLabel) ...[
              const SizedBox(height: 4),
              AnimatedDefaultTextStyle(
                duration: AppTheme.shortAnimation,
                style: theme.textTheme.labelSmall!.copyWith(
                  color: color,
                  fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                  fontSize: isSelected ? selectedFontSize : unselectedFontSize,
                ),
                child: Text(destination.label),
              ),
            ],
          ],
        ),
      ),
    );
  }
}

/// The style of the bottom navigation bar
enum BottomNavStyle {
  /// Standard navigation bar with equal-sized items
  standard,

  /// Fancy navigation bar with a prominent center button
  fancy,
}
