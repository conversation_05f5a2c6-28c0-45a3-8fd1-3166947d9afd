/// A widget for displaying voice translation results
library voice_translation_result;

// Package imports
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

// Project imports - Models
import 'package:culture_connect/models/translation/voice_translation_model.dart';

// Project imports - Providers
import 'package:culture_connect/providers/voice_translation_provider.dart';

// Project imports - Theme
import 'package:culture_connect/theme/app_theme.dart';

// Project imports - Widgets
import 'package:culture_connect/widgets/voice_translation/audio_player_controls.dart';
import 'package:culture_connect/widgets/translation/voice_translation_offline_indicator.dart';

/// A widget for displaying voice translation results
class VoiceTranslationResult extends ConsumerWidget {
  /// The translation to display
  final VoiceTranslationModel translation;

  /// Creates a new voice translation result widget
  const VoiceTranslationResult({
    super.key,
    required this.translation,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Status indicator
          _buildStatusIndicator(context, translation),

          const SizedBox(height: 16),

          // Original text
          if (translation.status == VoiceTranslationStatus.completed &&
              translation.originalText != null)
            _buildTextSection(
              context,
              'Original (${translation.getSourceLanguageName()})',
              translation.originalText!,
              translation.originalAudioPath != null,
              () => ref
                  .read(voiceTranslationNotifierProvider.notifier)
                  .playOriginalAudio(),
            ),

          const SizedBox(height: 24),

          // Translated text
          if (translation.status == VoiceTranslationStatus.completed &&
              translation.translatedText != null)
            _buildTextSection(
              context,
              'Translation (${translation.getTargetLanguageName()})',
              translation.translatedText!,
              translation.translatedAudioPath != null,
              () => ref
                  .read(voiceTranslationNotifierProvider.notifier)
                  .playTranslatedAudio(),
            ),

          const SizedBox(height: 24),

          // Audio player controls
          if (translation.status == VoiceTranslationStatus.completed &&
              (translation.originalAudioPath != null ||
                  translation.translatedAudioPath != null))
            const AudioPlayerControls(),

          const SizedBox(height: 24),

          // Action buttons
          if (translation.status == VoiceTranslationStatus.completed)
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                _buildActionButton(
                  context,
                  Icons.favorite,
                  translation.isFavorite ? Colors.red : Colors.grey,
                  translation.isFavorite
                      ? 'Remove from favorites'
                      : 'Add to favorites',
                  () => ref
                      .read(voiceTranslationNotifierProvider.notifier)
                      .toggleFavorite(),
                ),
                _buildActionButton(
                  context,
                  Icons.share,
                  Colors.blue,
                  'Share translation',
                  () => _shareTranslation(context, translation),
                ),
                _buildActionButton(
                  context,
                  Icons.delete,
                  Colors.red,
                  'Delete translation',
                  () => _confirmDelete(context, ref),
                ),
              ],
            ),
        ],
      ),
    );
  }

  /// Build the status indicator
  Widget _buildStatusIndicator(
      BuildContext context, VoiceTranslationModel translation) {
    switch (translation.status) {
      case VoiceTranslationStatus.initial:
        return const Center(
          child: Text(
            'Ready to record',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AppTheme.textSecondaryColor,
            ),
          ),
        );
      case VoiceTranslationStatus.recording:
        return const Center(
          child: Column(
            children: [
              Icon(
                Icons.mic,
                size: 48,
                color: Colors.red,
              ),
              SizedBox(height: 16),
              Text(
                'Recording...',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.red,
                ),
              ),
            ],
          ),
        );
      case VoiceTranslationStatus.processing:
        return const Center(
          child: Column(
            children: [
              SizedBox(
                width: 48,
                height: 48,
                child: CircularProgressIndicator(),
              ),
              SizedBox(height: 16),
              Text(
                'Processing...',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: AppTheme.primaryColor,
                ),
              ),
            ],
          ),
        );
      case VoiceTranslationStatus.completed:
        return Row(
          children: [
            const Icon(
              Icons.check_circle,
              color: Colors.green,
              size: 24,
            ),
            const SizedBox(width: 8),
            const Text(
              'Translation completed',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: Colors.green,
              ),
            ),
            const Spacer(),
            // Offline indicator
            VoiceTranslationOfflineIndicator(
              isOffline: translation.originalAudioPath != null,
              confidence: translation.dialectConfidence ?? 0.8,
              size: 16,
              showConfidence: true,
            ),
          ],
        );
      case VoiceTranslationStatus.error:
        return Center(
          child: Column(
            children: [
              const Icon(
                Icons.error_outline,
                size: 48,
                color: Colors.red,
              ),
              const SizedBox(height: 16),
              const Text(
                'Error',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.red,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                translation.errorMessage ??
                    'An error occurred during translation',
                style: const TextStyle(
                  fontSize: 14,
                  color: AppTheme.textSecondaryColor,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () {
                  // Reset the translation
                  // This would be handled by the provider in a real app
                },
                child: const Text('Try Again'),
              ),
            ],
          ),
        );
    }
  }

  /// Build a text section
  Widget _buildTextSection(
    BuildContext context,
    String title,
    String text,
    bool hasAudio,
    VoidCallback onPlayAudio,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Title
        Row(
          children: [
            Text(
              title,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: AppTheme.textPrimaryColor,
              ),
            ),
            const Spacer(),
            // Play button
            if (hasAudio)
              IconButton(
                icon: const Icon(Icons.play_circle_filled),
                color: AppTheme.primaryColor,
                onPressed: onPlayAudio,
                tooltip: 'Play audio',
              ),
            // Copy button
            IconButton(
              icon: const Icon(Icons.copy),
              color: Colors.grey,
              onPressed: () => _copyToClipboard(context, text),
              tooltip: 'Copy to clipboard',
            ),
          ],
        ),

        const SizedBox(height: 8),

        // Text
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.grey[100],
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: Colors.grey[300]!,
              width: 1,
            ),
          ),
          child: Text(
            text,
            style: const TextStyle(
              fontSize: 16,
              color: AppTheme.textPrimaryColor,
            ),
          ),
        ),
      ],
    );
  }

  /// Build an action button
  Widget _buildActionButton(
    BuildContext context,
    IconData icon,
    Color color,
    String tooltip,
    VoidCallback onPressed,
  ) {
    return Tooltip(
      message: tooltip,
      child: InkWell(
        onTap: onPressed,
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(8),
          child: Column(
            children: [
              Icon(
                icon,
                color: color,
                size: 24,
              ),
              const SizedBox(height: 4),
              Text(
                tooltip.split(' ').first,
                style: TextStyle(
                  fontSize: 12,
                  color: color,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Copy text to clipboard
  void _copyToClipboard(BuildContext context, String text) {
    Clipboard.setData(ClipboardData(text: text));
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Copied to clipboard'),
        duration: Duration(seconds: 2),
      ),
    );
  }

  /// Share translation
  void _shareTranslation(
      BuildContext context, VoiceTranslationModel translation) {
    // In a real app, this would use a share plugin
    final text =
        'Original (${translation.getSourceLanguageName()}): ${translation.originalText}\n\n'
        'Translation (${translation.getTargetLanguageName()}): ${translation.translatedText}';

    Clipboard.setData(ClipboardData(text: text));
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Translation copied to clipboard for sharing'),
        duration: Duration(seconds: 2),
      ),
    );
  }

  /// Confirm deletion
  void _confirmDelete(BuildContext context, WidgetRef ref) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Translation'),
        content:
            const Text('Are you sure you want to delete this translation?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              // Reset the current translation instead of deleting it
              ref.read(currentVoiceTranslationProvider.notifier).state = null;
            },
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }
}
