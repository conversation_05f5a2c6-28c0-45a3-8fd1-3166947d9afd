import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:culture_connect/models/translation/voice_translation_model.dart';
import 'package:culture_connect/providers/voice_translation_provider.dart';
import 'package:culture_connect/theme/app_theme.dart';

/// A button for recording voice input
class VoiceRecordingButton extends ConsumerStatefulWidget {
  /// The size of the button
  final double size;

  /// The color of the button
  final Color? color;

  /// Creates a new voice recording button
  const VoiceRecordingButton({
    super.key,
    this.size = 80,
    this.color,
  });

  @override
  ConsumerState<VoiceRecordingButton> createState() =>
      _VoiceRecordingButtonState();
}

class _VoiceRecordingButtonState extends ConsumerState<VoiceRecordingButton>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _pulseAnimation;

  @override
  void initState() {
    super.initState();

    // Initialize animations
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1500),
    );

    _pulseAnimation = Tween<double>(begin: 1.0, end: 1.3).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeInOut,
      ),
    );

    _animationController.repeat(reverse: true);
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isRecording = ref.watch(isRecordingProvider);
    final amplitude = ref.watch(amplitudeStreamProvider).value ?? 0.0;

    final buttonColor = widget.color ?? AppTheme.primaryColor;

    return GestureDetector(
      onTap: _handleTap,
      onLongPress: _handleLongPress,
      onLongPressUp: _handleLongPressUp,
      child: AnimatedBuilder(
        animation: _animationController,
        builder: (context, child) {
          return Transform.scale(
            scale: isRecording ? _pulseAnimation.value : 1.0,
            child: Container(
              width: widget.size.r,
              height: widget.size.r,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: isRecording ? Colors.red : buttonColor,
                boxShadow: [
                  BoxShadow(
                    color: isRecording
                        ? Colors.red.withAlpha(77)
                        : buttonColor.withAlpha(77),
                    blurRadius: 10,
                    spreadRadius: 2,
                  ),
                ],
              ),
              child: Stack(
                alignment: Alignment.center,
                children: [
                  // Amplitude indicator
                  if (isRecording)
                    Container(
                      width: (widget.size * 0.8 * (0.5 + amplitude * 0.5)).r,
                      height: (widget.size * 0.8 * (0.5 + amplitude * 0.5)).r,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: Colors.white.withAlpha(77),
                      ),
                    ),

                  // Mic icon
                  Icon(
                    isRecording ? Icons.stop : Icons.mic,
                    color: Colors.white,
                    size: (widget.size * 0.5).r,
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  void _handleTap() async {
    final isRecording = ref.read(isRecordingProvider);
    final translation = ref.read(currentVoiceTranslationProvider);

    if (isRecording) {
      // Stop recording
      await ref
          .read(voiceTranslationNotifierProvider.notifier)
          .stopRecordingAndTranslate();
    } else {
      // Start recording
      if (translation == null ||
          translation.status != VoiceTranslationStatus.recording) {
        // Create a new translation if none exists
        if (translation == null ||
            translation.status != VoiceTranslationStatus.initial) {
          await ref
              .read(voiceTranslationNotifierProvider.notifier)
              .startTranslation();
        }

        // Start recording
        await ref
            .read(voiceTranslationNotifierProvider.notifier)
            .startRecording();
      }
    }
  }

  void _handleLongPress() async {
    final isRecording = ref.read(isRecordingProvider);
    final translation = ref.read(currentVoiceTranslationProvider);

    if (!isRecording) {
      // Create a new translation if none exists
      if (translation == null ||
          translation.status != VoiceTranslationStatus.initial) {
        await ref
            .read(voiceTranslationNotifierProvider.notifier)
            .startTranslation();
      }

      // Start recording
      await ref
          .read(voiceTranslationNotifierProvider.notifier)
          .startRecording();
    }
  }

  void _handleLongPressUp() async {
    final isRecording = ref.read(isRecordingProvider);

    if (isRecording) {
      // Stop recording
      await ref
          .read(voiceTranslationNotifierProvider.notifier)
          .stopRecordingAndTranslate();
    }
  }
}
