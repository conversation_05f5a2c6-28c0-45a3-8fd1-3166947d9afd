import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:culture_connect/models/translation/language_model.dart';
import 'package:culture_connect/providers/voice_translation_provider.dart';
import 'package:culture_connect/theme/app_theme.dart';

/// A widget for selecting source and target languages
class LanguageSelector extends ConsumerWidget {
  /// Creates a new language selector
  const LanguageSelector({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final sourceLanguage = ref.watch(sourceLanguageProvider);
    final targetLanguage = ref.watch(targetLanguageProvider);

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(13),
            blurRadius: 10,
            spreadRadius: 1,
          ),
        ],
      ),
      child: Row(
        children: [
          // Source language
          Expanded(
            child: _buildLanguageButton(
              context,
              sourceLanguage,
              'Source',
              () => _showLanguageSelectionDialog(context, ref, true),
            ),
          ),

          // Swap button
          IconButton(
            icon: Icon(
              Icons.swap_horiz,
              color: AppTheme.primaryColor,
              size: 24.r,
            ),
            onPressed: () => _swapLanguages(ref),
            tooltip: 'Swap languages',
          ),

          // Target language
          Expanded(
            child: _buildLanguageButton(
              context,
              targetLanguage,
              'Target',
              () => _showLanguageSelectionDialog(context, ref, false),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLanguageButton(
    BuildContext context,
    LanguageModel language,
    String label,
    VoidCallback onTap,
  ) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8.r),
      child: Padding(
        padding: EdgeInsets.all(8.r),
        child: Column(
          children: [
            Text(
              label,
              style: TextStyle(
                fontSize: 12.sp,
                color: AppTheme.textSecondaryColor,
              ),
            ),
            SizedBox(height: 4.h),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  language.flag,
                  style: TextStyle(
                    fontSize: 16.sp,
                  ),
                ),
                SizedBox(width: 8.w),
                Text(
                  language.name,
                  style: TextStyle(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.bold,
                    color: AppTheme.textPrimaryColor,
                  ),
                ),
                SizedBox(width: 4.w),
                Icon(
                  Icons.arrow_drop_down,
                  size: 16.r,
                  color: AppTheme.textSecondaryColor,
                ),
              ],
            ),
            if (language.isOfflineAvailable)
              Padding(
                padding: EdgeInsets.only(top: 4.h),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.offline_bolt,
                      size: 12.r,
                      color: Colors.green,
                    ),
                    SizedBox(width: 4.w),
                    Text(
                      'Offline',
                      style: TextStyle(
                        fontSize: 10.sp,
                        color: Colors.green,
                      ),
                    ),
                  ],
                ),
              ),
          ],
        ),
      ),
    );
  }

  void _showLanguageSelectionDialog(
      BuildContext context, WidgetRef ref, bool isSource) {
    final currentLanguage = isSource
        ? ref.read(sourceLanguageProvider)
        : ref.read(targetLanguageProvider);

    showModalBottomSheet(
      context: context,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(16.r),
        ),
      ),
      builder: (context) => _LanguageSelectionBottomSheet(
        isSource: isSource,
        currentLanguage: currentLanguage,
      ),
    );
  }

  void _swapLanguages(WidgetRef ref) {
    final sourceLanguage = ref.read(sourceLanguageProvider);
    final targetLanguage = ref.read(targetLanguageProvider);

    ref.read(sourceLanguageProvider.notifier).state = targetLanguage;
    ref.read(targetLanguageProvider.notifier).state = sourceLanguage;
  }
}

class _LanguageSelectionBottomSheet extends ConsumerWidget {
  final bool isSource;
  final LanguageModel currentLanguage;

  const _LanguageSelectionBottomSheet({
    required this.isSource,
    required this.currentLanguage,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Container(
      padding: EdgeInsets.symmetric(vertical: 16.h),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Header
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 16.w),
            child: Row(
              children: [
                Text(
                  isSource
                      ? 'Select Source Language'
                      : 'Select Target Language',
                  style: TextStyle(
                    fontSize: 18.sp,
                    fontWeight: FontWeight.bold,
                    color: AppTheme.textPrimaryColor,
                  ),
                ),
                const Spacer(),
                IconButton(
                  icon: const Icon(Icons.close),
                  onPressed: () => Navigator.pop(context),
                ),
              ],
            ),
          ),

          Divider(height: 16.h),

          // Language list
          Expanded(
            child: ListView.builder(
              itemCount: supportedLanguages.length,
              itemBuilder: (context, index) {
                final language = supportedLanguages[index];
                final isSelected = language.code == currentLanguage.code;

                return ListTile(
                  leading: Text(
                    language.flag,
                    style: TextStyle(
                      fontSize: 24.sp,
                    ),
                  ),
                  title: Text(
                    language.name,
                    style: TextStyle(
                      fontSize: 16.sp,
                      fontWeight:
                          isSelected ? FontWeight.bold : FontWeight.normal,
                      color: AppTheme.textPrimaryColor,
                    ),
                  ),
                  subtitle: language.isOfflineAvailable
                      ? Row(
                          children: [
                            Icon(
                              Icons.offline_bolt,
                              size: 12.r,
                              color: Colors.green,
                            ),
                            SizedBox(width: 4.w),
                            Text(
                              'Available offline',
                              style: TextStyle(
                                fontSize: 12.sp,
                                color: Colors.green,
                              ),
                            ),
                          ],
                        )
                      : null,
                  trailing: isSelected
                      ? Icon(
                          Icons.check_circle,
                          color: AppTheme.primaryColor,
                          size: 24.r,
                        )
                      : null,
                  onTap: () {
                    if (isSource) {
                      ref.read(sourceLanguageProvider.notifier).state =
                          language;
                    } else {
                      ref.read(targetLanguageProvider.notifier).state =
                          language;
                    }
                    Navigator.pop(context);
                  },
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}
