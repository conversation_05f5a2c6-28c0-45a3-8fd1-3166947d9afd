import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:just_audio/just_audio.dart';
import 'package:culture_connect/providers/voice_translation_provider.dart';
import 'package:culture_connect/services/voice_translation/audio_playback_service.dart';
import 'package:culture_connect/theme/app_theme.dart';

/// A widget for controlling audio playback
class AudioPlayerControls extends ConsumerWidget {
  /// Creates a new audio player controls widget
  const AudioPlayerControls({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final isPlaying = ref.watch(isPlayingProvider);
    final positionAsync = ref.watch(playbackPositionStreamProvider);
    final playerStateAsync =
        ref.watch(audioPlaybackServiceProvider).playerStateStream;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          // Playback controls
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Stop button
              IconButton(
                icon: Icon(
                  Icons.stop,
                  size: 32,
                  color: isPlaying ? AppTheme.primaryColor : Colors.grey,
                ),
                onPressed: isPlaying
                    ? () => ref
                        .read(voiceTranslationNotifierProvider.notifier)
                        .stopAudio()
                    : null,
              ),

              const SizedBox(width: 16),

              // Play/pause button
              StreamBuilder<PlayerState>(
                stream: playerStateAsync,
                builder: (context, snapshot) {
                  final playerState = snapshot.data;
                  final processingState = playerState?.processingState;

                  if (processingState == ProcessingState.loading ||
                      processingState == ProcessingState.buffering) {
                    return Container(
                      width: 64,
                      height: 64,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: AppTheme.primaryColor.withAlpha(25),
                      ),
                      child: const Center(
                        child: SizedBox(
                          width: 32,
                          height: 32,
                          child: CircularProgressIndicator(
                            valueColor: AlwaysStoppedAnimation<Color>(
                                AppTheme.primaryColor),
                            strokeWidth: 2,
                          ),
                        ),
                      ),
                    );
                  } else if (isPlaying) {
                    return IconButton(
                      icon: const Icon(
                        Icons.pause_circle_filled,
                        size: 64,
                        color: AppTheme.primaryColor,
                      ),
                      onPressed: () => ref
                          .read(voiceTranslationNotifierProvider.notifier)
                          .stopAudio(),
                    );
                  } else {
                    return IconButton(
                      icon: const Icon(
                        Icons.play_circle_filled,
                        size: 64,
                        color: AppTheme.primaryColor,
                      ),
                      onPressed: () {
                        final translation =
                            ref.read(currentVoiceTranslationProvider);
                        if (translation != null) {
                          if (translation.translatedAudioPath != null) {
                            ref
                                .read(voiceTranslationNotifierProvider.notifier)
                                .playTranslatedAudio();
                          } else if (translation.originalAudioPath != null) {
                            ref
                                .read(voiceTranslationNotifierProvider.notifier)
                                .playOriginalAudio();
                          }
                        }
                      },
                    );
                  }
                },
              ),
            ],
          ),

          const SizedBox(height: 16),

          // Progress bar
          positionAsync.when(
            data: (position) {
              final duration =
                  ref.read(audioPlaybackServiceProvider).duration ??
                      Duration.zero;

              return Column(
                children: [
                  // Slider
                  Slider(
                    value: position.inMilliseconds.toDouble().clamp(
                          0,
                          duration.inMilliseconds.toDouble(),
                        ),
                    max: duration.inMilliseconds.toDouble(),
                    activeColor: AppTheme.primaryColor,
                    inactiveColor: Colors.grey[300],
                    onChanged: (value) {
                      ref.read(audioPlaybackServiceProvider).seekTo(
                            Duration(milliseconds: value.toInt()),
                          );
                    },
                  ),

                  // Time labels
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          _formatDuration(position),
                          style: const TextStyle(
                            fontSize: 12,
                            color: AppTheme.textSecondaryColor,
                          ),
                        ),
                        Text(
                          _formatDuration(duration),
                          style: const TextStyle(
                            fontSize: 12,
                            color: AppTheme.textSecondaryColor,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              );
            },
            loading: () => const LinearProgressIndicator(),
            error: (_, __) => const SizedBox.shrink(),
          ),
        ],
      ),
    );
  }

  String _formatDuration(Duration duration) {
    final minutes = duration.inMinutes.remainder(60).toString().padLeft(2, '0');
    final seconds = duration.inSeconds.remainder(60).toString().padLeft(2, '0');
    return '$minutes:$seconds';
  }
}
