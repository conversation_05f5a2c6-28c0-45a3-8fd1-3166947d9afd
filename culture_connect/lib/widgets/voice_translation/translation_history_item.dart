import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:culture_connect/models/translation/voice_translation_model.dart';
import 'package:culture_connect/providers/voice_translation_provider.dart';
import 'package:culture_connect/theme/app_theme.dart';

/// A widget for displaying a translation history item
class TranslationHistoryItem extends ConsumerWidget {
  /// The translation to display
  final VoiceTranslationModel translation;

  /// Callback when the item is tapped
  final VoidCallback? onTap;

  /// Creates a new translation history item
  const TranslationHistoryItem({
    super.key,
    required this.translation,
    this.onTap,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Card(
      margin: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12.r),
        child: Padding(
          padding: EdgeInsets.all(16.r),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              Row(
                children: [
                  // Status icon
                  Container(
                    width: 40.r,
                    height: 40.r,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: translation.status.color.withAlpha(25),
                    ),
                    child: Center(
                      child: Icon(
                        translation.status.icon,
                        size: 20.r,
                        color: translation.status.color,
                      ),
                    ),
                  ),

                  SizedBox(width: 12.w),

                  // Languages
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          '${translation.getSourceLanguageName()} → ${translation.getTargetLanguageName()}',
                          style: TextStyle(
                            fontSize: 16.sp,
                            fontWeight: FontWeight.bold,
                            color: AppTheme.textPrimaryColor,
                          ),
                        ),
                        SizedBox(height: 4.h),
                        Text(
                          translation.formattedTimestamp,
                          style: TextStyle(
                            fontSize: 12.sp,
                            color: AppTheme.textSecondaryColor,
                          ),
                        ),
                      ],
                    ),
                  ),

                  // Favorite button
                  IconButton(
                    icon: Icon(
                      translation.isFavorite
                          ? Icons.favorite
                          : Icons.favorite_border,
                      color: translation.isFavorite ? Colors.red : Colors.grey,
                      size: 24.r,
                    ),
                    onPressed: () {
                      ref.read(currentVoiceTranslationProvider.notifier).state =
                          translation;
                      ref
                          .read(voiceTranslationNotifierProvider.notifier)
                          .toggleFavorite();
                    },
                  ),
                ],
              ),

              SizedBox(height: 12.h),

              // Content preview
              if (translation.status == VoiceTranslationStatus.completed) ...[
                // Original text preview
                if (translation.originalText != null &&
                    translation.originalText!.isNotEmpty)
                  _buildTextPreview(
                    'Original:',
                    translation.originalText!,
                  ),

                SizedBox(height: 8.h),

                // Translated text preview
                if (translation.translatedText != null &&
                    translation.translatedText!.isNotEmpty)
                  _buildTextPreview(
                    'Translation:',
                    translation.translatedText!,
                  ),
              ] else if (translation.status ==
                  VoiceTranslationStatus.error) ...[
                // Error message
                Container(
                  padding: EdgeInsets.all(8.r),
                  decoration: BoxDecoration(
                    color: Colors.red.withAlpha(25),
                    borderRadius: BorderRadius.circular(4.r),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.error_outline,
                        size: 16.r,
                        color: Colors.red,
                      ),
                      SizedBox(width: 8.w),
                      Expanded(
                        child: Text(
                          translation.errorMessage ?? 'An error occurred',
                          style: TextStyle(
                            fontSize: 12.sp,
                            color: Colors.red,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ] else if (translation.status ==
                  VoiceTranslationStatus.processing) ...[
                // Processing indicator
                Row(
                  children: [
                    SizedBox(
                      width: 16.r,
                      height: 16.r,
                      child: CircularProgressIndicator(
                        strokeWidth: 2.w,
                      ),
                    ),
                    SizedBox(width: 8.w),
                    Text(
                      'Processing translation...',
                      style: TextStyle(
                        fontSize: 12.sp,
                        color: AppTheme.textSecondaryColor,
                      ),
                    ),
                  ],
                ),
              ],

              SizedBox(height: 12.h),

              // Footer
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  // Play button
                  if (translation.status == VoiceTranslationStatus.completed &&
                      (translation.originalAudioPath != null ||
                          translation.translatedAudioPath != null))
                    TextButton.icon(
                      icon: Icon(
                        Icons.play_arrow,
                        size: 16.r,
                      ),
                      label: Text(
                        'Play',
                        style: TextStyle(
                          fontSize: 12.sp,
                        ),
                      ),
                      onPressed: () {
                        ref
                            .read(currentVoiceTranslationProvider.notifier)
                            .state = translation;
                        if (translation.translatedAudioPath != null) {
                          ref
                              .read(voiceTranslationNotifierProvider.notifier)
                              .playTranslatedAudio();
                        } else if (translation.originalAudioPath != null) {
                          ref
                              .read(voiceTranslationNotifierProvider.notifier)
                              .playOriginalAudio();
                        }
                      },
                    ),

                  SizedBox(width: 8.w),

                  // Delete button
                  TextButton.icon(
                    icon: Icon(
                      Icons.delete,
                      size: 16.r,
                      color: Colors.red,
                    ),
                    label: Text(
                      'Delete',
                      style: TextStyle(
                        fontSize: 12.sp,
                        color: Colors.red,
                      ),
                    ),
                    onPressed: () {
                      _confirmDelete(context, ref);
                    },
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTextPreview(String label, String text) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: 12.sp,
            fontWeight: FontWeight.bold,
            color: AppTheme.textSecondaryColor,
          ),
        ),
        SizedBox(height: 4.h),
        Text(
          text,
          style: TextStyle(
            fontSize: 14.sp,
            color: AppTheme.textPrimaryColor,
          ),
          maxLines: 2,
          overflow: TextOverflow.ellipsis,
        ),
      ],
    );
  }

  void _confirmDelete(BuildContext context, WidgetRef ref) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Translation'),
        content:
            const Text('Are you sure you want to delete this translation?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              ref.read(currentVoiceTranslationProvider.notifier).state =
                  translation;
              ref
                  .read(voiceTranslationNotifierProvider.notifier)
                  .deleteTranslation();
            },
            child: const Text('Delete', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }
}
