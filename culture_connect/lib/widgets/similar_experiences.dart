import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'dart:math' as math;
import 'package:culture_connect/models/experience.dart';
import 'package:culture_connect/providers/experience_provider.dart';
import 'package:culture_connect/widgets/experience_card.dart';

/// A class to hold an experience with a similarity score
class _ScoredExperience {
  final Experience experience;
  final double score;

  const _ScoredExperience({
    required this.experience,
    required this.score,
  });
}

/// Calculate a similarity score between two experiences
double _calculateSimilarityScore(Experience a, Experience b) {
  double score = 0;

  // Same category
  if (a.category == b.category) {
    score += 3;
  }

  // Similar price range
  final priceDiff = (a.price - b.price).abs();
  if (priceDiff < 10) {
    score += 2;
  } else if (priceDiff < 30) {
    score += 1;
  }

  // Similar duration
  final durationDiff = (a.durationHours - b.durationHours).abs();
  if (durationDiff < 0.5) {
    score += 2;
  } else if (durationDiff < 1) {
    score += 1;
  }

  // Similar location
  final distance = _calculateDistance(a.coordinates.latitude,
      a.coordinates.longitude, b.coordinates.latitude, b.coordinates.longitude);

  if (distance < 1) {
    score += 3; // Very close (< 1km)
  } else if (distance < 5) {
    score += 2; // Close (< 5km)
  } else if (distance < 10) {
    score += 1; // Somewhat close (< 10km)
  }

  return score;
}

/// Calculate distance between two points using Haversine formula
double _calculateDistance(double lat1, double lon1, double lat2, double lon2) {
  const p = 0.017453292519943295; // Math.PI / 180
  const c = 12742; // 2 * Earth's radius in km

  final a = 0.5 -
      math.cos((lat2 - lat1) * p) / 2 +
      math.cos(lat1 * p) *
          math.cos(lat2 * p) *
          (1 - math.cos((lon2 - lon1) * p)) /
          2;

  return c * math.asin(math.sqrt(a)); // Distance in km
}

/// Provider for similar experiences
final similarExperiencesProvider =
    FutureProvider.family<List<Experience>, String>((ref, experienceId) async {
  // Get all experiences
  final experiencesAsyncValue = ref.watch(experiencesProvider);

  return experiencesAsyncValue.when(
    data: (allExperiences) {
      // Get the current experience
      final currentExperience = allExperiences.firstWhere(
        (exp) => exp.id == experienceId,
        orElse: () => allExperiences.first,
      );

      // Filter out the current experience
      final otherExperiences =
          allExperiences.where((exp) => exp.id != experienceId).toList();

      // Calculate similarity scores
      final scoredExperiences = otherExperiences.map((exp) {
        final score = _calculateSimilarityScore(currentExperience, exp);
        return _ScoredExperience(experience: exp, score: score);
      }).toList();

      // Sort by similarity score (descending)
      scoredExperiences.sort((a, b) => b.score.compareTo(a.score));

      // Return the experiences (without scores)
      return scoredExperiences.map((scored) => scored.experience).toList();
    },
    loading: () => [],
    error: (_, __) => [],
  );
});

/// A widget for displaying similar experiences
class SimilarExperiences extends ConsumerWidget {
  final String experienceId;
  final String? category;
  final List<String>? tags;
  final int limit;
  final Function(Experience)? onExperienceTap;

  const SimilarExperiences({
    super.key,
    required this.experienceId,
    this.category,
    this.tags,
    this.limit = 3,
    this.onExperienceTap,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final similarExperiencesAsync =
        ref.watch(similarExperiencesProvider(experienceId));

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Similar Experiences',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            TextButton(
              onPressed: () {
                // Navigate to see all similar experiences
                // This would typically navigate to a filtered list view
              },
              child: const Text('See All'),
            ),
          ],
        ),
        const SizedBox(height: 8),
        similarExperiencesAsync.when(
          data: (experiences) =>
              _buildSimilarExperiencesList(context, experiences),
          loading: () => const _LoadingSimilarExperiences(),
          error: (error, _) =>
              _ErrorSimilarExperiences(error: error.toString()),
        ),
      ],
    );
  }

  Widget _buildSimilarExperiencesList(
      BuildContext context, List<Experience> experiences) {
    if (experiences.isEmpty) {
      return const Center(
        child: Padding(
          padding: EdgeInsets.symmetric(vertical: 16),
          child: Text('No similar experiences found'),
        ),
      );
    }

    // Limit the number of experiences shown
    final limitedExperiences = experiences.take(limit).toList();

    return SizedBox(
      height: 250,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: limitedExperiences.length,
        itemBuilder: (context, index) {
          final experience = limitedExperiences[index];
          return SizedBox(
            width: 200,
            child: Padding(
              padding: const EdgeInsets.only(right: 16),
              child: ExperienceCard(
                title: experience.title,
                location: experience.location,
                imageUrl: experience.imageUrl,
                rating: experience.rating,
                price: experience.price.toString(),
                category: experience.category,
                reviewCount: experience.reviewCount,
                onTap: () {
                  if (onExperienceTap != null) {
                    onExperienceTap!(experience);
                  }
                },
              ),
            ),
          );
        },
      ),
    );
  }
}

class _LoadingSimilarExperiences extends StatelessWidget {
  const _LoadingSimilarExperiences();

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 250,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: 3,
        itemBuilder: (context, index) {
          return SizedBox(
            width: 200,
            child: Padding(
              padding: const EdgeInsets.only(right: 16),
              child: Card(
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                clipBehavior: Clip.antiAlias,
                child: const Center(
                  child: CircularProgressIndicator(),
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}

class _ErrorSimilarExperiences extends StatelessWidget {
  final String error;

  const _ErrorSimilarExperiences({required this.error});

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 100,
      child: Center(
        child: Text(
          'Failed to load similar experiences',
          style: TextStyle(color: Theme.of(context).colorScheme.error),
        ),
      ),
    );
  }
}
