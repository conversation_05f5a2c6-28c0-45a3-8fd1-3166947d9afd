import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'package:culture_connect/models/ar/ar_content_marker.dart';

/// A widget for displaying AR content information
class ARInfoOverlay extends StatelessWidget {
  /// The title of the AR content
  final String title;

  /// The description of the AR content
  final String? description;

  /// The type of AR content
  final ARContentType contentType;

  /// Callback when the close button is tapped
  final VoidCallback? onClose;

  /// Creates a new AR info overlay
  const ARInfoOverlay({
    super.key,
    required this.title,
    this.description,
    required this.contentType,
    this.onClose,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(16.r),
      margin: EdgeInsets.all(16.r),
      decoration: BoxDecoration(
        color: Colors.black.withAlpha(179),
        borderRadius: BorderRadius.circular(16.r),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Row(
            children: [
              // Content type icon
              Container(
                width: 40.r,
                height: 40.r,
                decoration: BoxDecoration(
                  color: contentType.color.withAlpha(51),
                  shape: BoxShape.circle,
                ),
                child: Center(
                  child: Icon(
                    contentType.icon,
                    color: contentType.color,
                    size: 20.r,
                  ),
                ),
              ),

              SizedBox(width: 12.w),

              // Title
              Expanded(
                child: Text(
                  title,
                  style: TextStyle(
                    fontSize: 18.sp,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
              ),

              // Close button
              IconButton(
                icon: const Icon(Icons.close, color: Colors.white),
                onPressed: onClose,
              ),
            ],
          ),

          SizedBox(height: 16.h),

          // Content type
          Row(
            children: [
              Icon(
                contentType.icon,
                size: 16.sp,
                color: contentType.color,
              ),
              SizedBox(width: 8.w),
              Text(
                contentType.displayName,
                style: TextStyle(
                  fontSize: 14.sp,
                  color: contentType.color,
                ),
              ),
            ],
          ),

          SizedBox(height: 8.h),

          // Description
          if (description != null) ...[
            Text(
              description!,
              style: TextStyle(
                fontSize: 14.sp,
                color: Colors.white,
              ),
            ),
            SizedBox(height: 16.h),
          ],

          // Instructions
          Text(
            'Instructions:',
            style: TextStyle(
              fontSize: 16.sp,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),

          SizedBox(height: 8.h),

          // Pinch to zoom
          Row(
            children: [
              Icon(
                Icons.pinch,
                size: 16.sp,
                color: Colors.white,
              ),
              SizedBox(width: 8.w),
              Text(
                'Pinch to zoom in and out',
                style: TextStyle(
                  fontSize: 14.sp,
                  color: Colors.white,
                ),
              ),
            ],
          ),

          SizedBox(height: 4.h),

          // Rotate
          Row(
            children: [
              Icon(
                Icons.rotate_right,
                size: 16.sp,
                color: Colors.white,
              ),
              SizedBox(width: 8.w),
              Text(
                'Rotate with two fingers',
                style: TextStyle(
                  fontSize: 14.sp,
                  color: Colors.white,
                ),
              ),
            ],
          ),

          SizedBox(height: 4.h),

          // Tap
          Row(
            children: [
              Icon(
                Icons.touch_app,
                size: 16.sp,
                color: Colors.white,
              ),
              SizedBox(width: 8.w),
              Text(
                'Tap to interact with the content',
                style: TextStyle(
                  fontSize: 14.sp,
                  color: Colors.white,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
