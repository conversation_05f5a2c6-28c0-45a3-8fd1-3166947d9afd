import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:culture_connect/services/wishlist_service.dart';
import 'package:culture_connect/services/logging_service.dart';
import 'package:culture_connect/services/error_handling_service.dart';
import 'package:culture_connect/services/analytics_service.dart';

/// A widget for adding/removing an experience to/from the wishlist
class WishlistButton extends ConsumerWidget {
  final String experienceId;
  final String experienceName;
  final Color? color;
  final bool showLabel;
  final bool isOutlined;
  final VoidCallback? onToggleComplete;

  const WishlistButton({
    super.key,
    required this.experienceId,
    required this.experienceName,
    this.color,
    this.showLabel = true,
    this.isOutlined = false,
    this.onToggleComplete,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final isInWishlist = ref.watch(isInWishlistProvider(experienceId));
    final loggingService = ref.watch(loggingServiceProvider);
    final errorHandlingService = ref.watch(errorHandlingServiceProvider);
    final analyticsService = ref.watch(analyticsServiceProvider);

    return isOutlined
        ? OutlinedButton.icon(
            onPressed: () => _toggleWishlist(
              context,
              ref,
              isInWishlist,
              loggingService,
              errorHandlingService,
              analyticsService,
            ),
            icon: Icon(
              isInWishlist ? Icons.favorite : Icons.favorite_border,
              color: isInWishlist
                  ? Colors.red
                  : (color ?? theme.colorScheme.primary),
            ),
            label: showLabel
                ? Text(isInWishlist ? 'Saved' : 'Save')
                : const SizedBox.shrink(),
            style: OutlinedButton.styleFrom(
              foregroundColor: color ?? theme.colorScheme.primary,
            ),
          )
        : ElevatedButton.icon(
            onPressed: () => _toggleWishlist(
              context,
              ref,
              isInWishlist,
              loggingService,
              errorHandlingService,
              analyticsService,
            ),
            icon: Icon(
              isInWishlist ? Icons.favorite : Icons.favorite_border,
              color: isInWishlist ? Colors.red : null,
            ),
            label: showLabel
                ? Text(isInWishlist ? 'Saved' : 'Save')
                : const SizedBox.shrink(),
            style: ElevatedButton.styleFrom(
              backgroundColor: color ?? theme.colorScheme.primary,
              foregroundColor: theme.colorScheme.onPrimary,
            ),
          );
  }

  Future<void> _toggleWishlist(
    BuildContext context,
    WidgetRef ref,
    bool isInWishlist,
    LoggingService? loggingService,
    ErrorHandlingService? errorHandlingService,
    AnalyticsService? analyticsService,
  ) async {
    try {
      // Log the wishlist toggle attempt
      loggingService?.info(
        'WishlistButton',
        'Toggling wishlist for experience: $experienceId, current state: $isInWishlist',
      );

      // Track the wishlist toggle event
      analyticsService?.logEvent(
        name: isInWishlist ? 'remove_from_wishlist' : 'add_to_wishlist',
        category: AnalyticsCategory.userAction,
        parameters: {
          'experience_id': experienceId,
          'experience_name': experienceName,
        },
      );

      // Toggle the wishlist
      final wishlistNotifier = ref.read(wishlistProvider.notifier);

      if (isInWishlist) {
        await wishlistNotifier.removeFromWishlist(experienceId);
      } else {
        await wishlistNotifier.addToWishlist(experienceId);
      }

      // Show a snackbar
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              isInWishlist ? 'Removed from wishlist' : 'Added to wishlist',
            ),
            action: SnackBarAction(
              label: 'Undo',
              onPressed: () {
                // Undo the action
                if (isInWishlist) {
                  wishlistNotifier.addToWishlist(experienceId);
                } else {
                  wishlistNotifier.removeFromWishlist(experienceId);
                }

                // Track the undo event
                analyticsService?.logEvent(
                  name: 'undo_wishlist_action',
                  category: AnalyticsCategory.userAction,
                  parameters: {
                    'experience_id': experienceId,
                    'experience_name': experienceName,
                    'original_action': isInWishlist ? 'remove' : 'add',
                  },
                );
              },
            ),
          ),
        );
      }

      // Call the onToggleComplete callback
      onToggleComplete?.call();
    } catch (e, stackTrace) {
      // Log the error
      loggingService?.error(
        'WishlistButton',
        'Error toggling wishlist',
        e,
        stackTrace,
      );

      // Handle the error
      errorHandlingService?.handleError(
        error: e,
        stackTrace: stackTrace,
        context: 'WishlistButton._toggleWishlist',
        type: ErrorType.unknown,
        severity: ErrorSeverity.medium,
      );

      // Show an error message
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to update wishlist: ${e.toString()}'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    }
  }
}

/// A widget for displaying a floating wishlist button
class FloatingWishlistButton extends ConsumerWidget {
  final String experienceId;
  final String experienceName;

  const FloatingWishlistButton({
    super.key,
    required this.experienceId,
    required this.experienceName,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final isInWishlist = ref.watch(isInWishlistProvider(experienceId));

    return FloatingActionButton(
      heroTag: 'wishlist_fab',
      onPressed: () => _toggleWishlist(context, ref, isInWishlist),
      backgroundColor: Colors.white,
      child: Icon(
        isInWishlist ? Icons.favorite : Icons.favorite_border,
        color: isInWishlist ? Colors.red : Colors.grey,
      ),
    );
  }

  Future<void> _toggleWishlist(
    BuildContext context,
    WidgetRef ref,
    bool isInWishlist,
  ) async {
    final wishlistNotifier = ref.read(wishlistProvider.notifier);

    try {
      if (isInWishlist) {
        await wishlistNotifier.removeFromWishlist(experienceId);

        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Removed from wishlist')),
          );
        }
      } else {
        await wishlistNotifier.addToWishlist(experienceId);

        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Added to wishlist')),
          );
        }
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to update wishlist: ${e.toString()}'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    }
  }
}

/// A widget for displaying a wishlist icon button
class WishlistIconButton extends ConsumerWidget {
  final String experienceId;
  final String experienceName;
  final Color? color;
  final Color? activeColor;
  final double size;
  final VoidCallback? onToggleComplete;

  const WishlistIconButton({
    super.key,
    required this.experienceId,
    required this.experienceName,
    this.color,
    this.activeColor = Colors.red,
    this.size = 24,
    this.onToggleComplete,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final isInWishlist = ref.watch(isInWishlistProvider(experienceId));

    return IconButton(
      icon: Icon(
        isInWishlist ? Icons.favorite : Icons.favorite_border,
        color:
            isInWishlist ? activeColor : (color ?? theme.colorScheme.onSurface),
        size: size,
      ),
      onPressed: () => _toggleWishlist(context, ref, isInWishlist),
    );
  }

  Future<void> _toggleWishlist(
    BuildContext context,
    WidgetRef ref,
    bool isInWishlist,
  ) async {
    final wishlistNotifier = ref.read(wishlistProvider.notifier);
    final analyticsService = ref.read(analyticsServiceProvider);

    try {
      // Track the wishlist toggle event
      analyticsService.logEvent(
        name: isInWishlist ? 'remove_from_wishlist' : 'add_to_wishlist',
        category: AnalyticsCategory.userAction,
        parameters: {
          'experience_id': experienceId,
          'experience_name': experienceName,
          'source': 'icon_button',
        },
      );

      if (isInWishlist) {
        await wishlistNotifier.removeFromWishlist(experienceId);
      } else {
        await wishlistNotifier.addToWishlist(experienceId);
      }

      // Call the onToggleComplete callback
      onToggleComplete?.call();
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to update wishlist: ${e.toString()}'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    }
  }
}
