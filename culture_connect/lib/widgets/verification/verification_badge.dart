import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:culture_connect/models/verification_model.dart';

/// A widget for displaying a verification badge
class VerificationBadgeWidget extends StatelessWidget {
  /// The verification badge to display
  final VerificationBadge badge;

  /// The size of the badge
  final double size;

  /// Whether to show the badge name
  final bool showName;

  /// Whether to show the badge description
  final bool showDescription;

  /// Whether to show the badge status
  final bool showStatus;

  /// Whether to show the badge expiry date
  final bool showExpiry;

  /// Callback when the badge is tapped
  final VoidCallback? onTap;

  /// Creates a new verification badge widget
  const VerificationBadgeWidget({
    super.key,
    required this.badge,
    this.size = 40,
    this.showName = true,
    this.showDescription = false,
    this.showStatus = false,
    this.showExpiry = false,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Row(
          children: [
            // Badge icon
            Container(
              width: size,
              height: size,
              decoration: BoxDecoration(
                color: _getBadgeColor(badge.status),
                shape: BoxShape.circle,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withAlpha(25),
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Center(
                child: badge.iconUrl != null
                    ? Image.network(
                        badge.iconUrl!,
                        width: size * 0.6,
                        height: size * 0.6,
                        errorBuilder: (context, error, stackTrace) {
                          return Icon(
                            _getBadgeIcon(badge.type),
                            color: Colors.white,
                            size: size * 0.6,
                          );
                        },
                      )
                    : Icon(
                        _getBadgeIcon(badge.type),
                        color: Colors.white,
                        size: size * 0.6,
                      ),
              ),
            ),

            if (showName || showDescription || showStatus || showExpiry) ...[
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Badge name
                    if (showName)
                      Text(
                        badge.name,
                        style: theme.textTheme.titleSmall?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),

                    // Badge description
                    if (showDescription && badge.description.isNotEmpty) ...[
                      const SizedBox(height: 4),
                      Text(
                        badge.description,
                        style: theme.textTheme.bodySmall,
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],

                    // Badge status and expiry
                    if (showStatus ||
                        (showExpiry && badge.expiresAt != null)) ...[
                      const SizedBox(height: 4),
                      Row(
                        children: [
                          if (showStatus)
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 6,
                                vertical: 2,
                              ),
                              decoration: BoxDecoration(
                                color: _getStatusColor(badge.status),
                                borderRadius: BorderRadius.circular(4),
                              ),
                              child: Text(
                                badge.statusDisplayName,
                                style: theme.textTheme.bodySmall?.copyWith(
                                  color: Colors.white,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                          if (showStatus &&
                              showExpiry &&
                              badge.expiresAt != null)
                            const SizedBox(width: 8),
                          if (showExpiry && badge.expiresAt != null)
                            Text(
                              'Expires: ${_formatDate(badge.expiresAt!)}',
                              style: theme.textTheme.bodySmall?.copyWith(
                                color: _isExpiringSoon(badge.expiresAt!)
                                    ? Colors.orange
                                    : theme.textTheme.bodySmall?.color,
                              ),
                            ),
                        ],
                      ),
                    ],
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// Returns the icon for the given verification type
  IconData _getBadgeIcon(VerificationType type) {
    switch (type) {
      case VerificationType.identity:
        return Icons.badge;
      case VerificationType.professional:
        return Icons.school;
      case VerificationType.background:
        return Icons.security;
      case VerificationType.address:
        return Icons.home;
      case VerificationType.phone:
        return Icons.phone;
      case VerificationType.email:
        return Icons.email;
      case VerificationType.social:
        return Icons.people;
      case VerificationType.license:
        return Icons.card_membership;
    }
  }

  /// Returns the color for the given verification status
  Color _getBadgeColor(VerificationStatus status) {
    switch (status) {
      case VerificationStatus.pending:
        return Colors.grey;
      case VerificationStatus.approved:
        return Colors.green;
      case VerificationStatus.rejected:
        return Colors.red;
      case VerificationStatus.expired:
        return Colors.orange;
    }
  }

  /// Returns the color for the given verification status (for status badge)
  Color _getStatusColor(VerificationStatus status) {
    switch (status) {
      case VerificationStatus.pending:
        return Colors.grey.shade700;
      case VerificationStatus.approved:
        return Colors.green.shade700;
      case VerificationStatus.rejected:
        return Colors.red.shade700;
      case VerificationStatus.expired:
        return Colors.orange.shade700;
    }
  }

  /// Formats a date as a string
  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  /// Returns whether the given date is expiring soon (within 30 days)
  bool _isExpiringSoon(DateTime date) {
    final now = DateTime.now();
    final difference = date.difference(now).inDays;
    return difference <= 30 && difference >= 0;
  }
}

/// A widget for displaying multiple verification badges
class VerificationBadgesList extends ConsumerWidget {
  /// The verification badges to display
  final List<VerificationBadge> badges;

  /// The size of each badge
  final double badgeSize;

  /// Whether to show the badge name
  final bool showName;

  /// Whether to show the badge description
  final bool showDescription;

  /// Whether to show the badge status
  final bool showStatus;

  /// Whether to show the badge expiry date
  final bool showExpiry;

  /// Callback when a badge is tapped
  final Function(VerificationBadge)? onBadgeTap;

  /// Whether to show only valid badges
  final bool showOnlyValid;

  /// The empty state widget to show when there are no badges
  final Widget? emptyState;

  /// Creates a new verification badges list widget
  const VerificationBadgesList({
    super.key,
    required this.badges,
    this.badgeSize = 40,
    this.showName = true,
    this.showDescription = false,
    this.showStatus = false,
    this.showExpiry = false,
    this.onBadgeTap,
    this.showOnlyValid = false,
    this.emptyState,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final displayBadges = showOnlyValid
        ? badges.where((badge) => badge.isValid).toList()
        : badges;

    if (displayBadges.isEmpty) {
      return emptyState ?? const SizedBox.shrink();
    }

    return ListView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: displayBadges.length,
      itemBuilder: (context, index) {
        final badge = displayBadges[index];
        return VerificationBadgeWidget(
          badge: badge,
          size: badgeSize,
          showName: showName,
          showDescription: showDescription,
          showStatus: showStatus,
          showExpiry: showExpiry,
          onTap: onBadgeTap != null ? () => onBadgeTap!(badge) : null,
        );
      },
    );
  }
}
