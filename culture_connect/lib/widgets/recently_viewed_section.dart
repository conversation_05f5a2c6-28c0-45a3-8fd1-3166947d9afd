import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:culture_connect/models/experience.dart';
import 'package:culture_connect/providers/experience_provider.dart';
import 'package:culture_connect/screens/experience_details_screen.dart';
import 'package:timeago/timeago.dart' as timeago;

/// A widget for displaying recently viewed experiences
class RecentlyViewedSection extends ConsumerWidget {
  final bool showTitle;
  final bool showViewAll;
  final int maxItems;
  final ScrollPhysics? physics;

  const RecentlyViewedSection({
    super.key,
    this.showTitle = true,
    this.showViewAll = true,
    this.maxItems = 10,
    this.physics,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final recentlyViewedExperiences =
        ref.watch(recentlyViewedExperiencesProvider);
    final theme = Theme.of(context);

    if (recentlyViewedExperiences.isEmpty) {
      return const SizedBox.shrink();
    }

    final displayedExperiences =
        recentlyViewedExperiences.take(maxItems).toList();

    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (showTitle) ...[
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Recently Viewed',
                  style: theme.textTheme.titleLarge,
                ),
                if (showViewAll && recentlyViewedExperiences.length > maxItems)
                  TextButton(
                    onPressed: () {
                      // TODO: Navigate to recently viewed screen
                    },
                    child: const Text('View All'),
                  ),
              ],
            ),
            const SizedBox(height: 8),
          ],
          SizedBox(
            height: 120,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              physics: physics ?? const BouncingScrollPhysics(),
              itemCount: displayedExperiences.length,
              itemBuilder: (context, index) {
                final experience = displayedExperiences[index];
                return Padding(
                  padding: const EdgeInsets.only(right: 16),
                  child: _buildRecentlyViewedItem(context, experience, ref),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRecentlyViewedItem(
      BuildContext context, Experience experience, WidgetRef ref) {
    return InkWell(
      onTap: () {
        ref.read(experiencesProvider.notifier).markAsViewed(experience.id);
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => ExperienceDetailsScreen(
              experience: experience,
            ),
          ),
        );
      },
      child: SizedBox(
        width: 200,
        child: Row(
          children: [
            // Image
            ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: Image.network(
                experience.imageUrl,
                width: 80,
                height: 80,
                fit: BoxFit.cover,
              ),
            ),
            const SizedBox(width: 12),
            // Content
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    experience.title,
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 14,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 4),
                  Text(
                    experience.location,
                    style: TextStyle(
                      color: Colors.grey[600],
                      fontSize: 12,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'Viewed ${timeago.format(experience.lastViewed!)}',
                    style: TextStyle(
                      color: Colors.grey[500],
                      fontSize: 10,
                      fontStyle: FontStyle.italic,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
