import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:culture_connect/models/trust_score_model.dart';
import 'package:culture_connect/services/trust_score_service.dart';

/// A widget for displaying a trust score
class TrustScoreWidget extends ConsumerWidget {
  /// The user ID to display the trust score for
  final String userId;

  /// The size of the widget
  final double size;

  /// Whether to show the trust level label
  final bool showLabel;

  /// Whether to show the trust score value
  final bool showScore;

  /// Callback when the widget is tapped
  final VoidCallback? onTap;

  /// Creates a new trust score widget
  const TrustScoreWidget({
    super.key,
    required this.userId,
    this.size = 60,
    this.showLabel = true,
    this.showScore = true,
    this.onTap,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final trustScoreAsync = ref.watch(trustScoreProvider(userId));

    return trustScoreAsync.when(
      data: (trustScore) {
        if (trustScore == null) {
          return _buildEmptyState(theme);
        }

        final trustLevel = trustScore.trustLevel;
        final color = Color(trustLevel.color);

        return InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(size / 2),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Trust score circle
              Container(
                width: size,
                height: size,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: color.withAlpha(25),
                  border: Border.all(
                    color: color,
                    width: 2,
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: color.withAlpha(51),
                      blurRadius: 4,
                      spreadRadius: 1,
                    ),
                  ],
                ),
                child: Center(
                  child: showScore
                      ? Text(
                          '${trustScore.score}',
                          style: TextStyle(
                            color: color,
                            fontSize: size * 0.4,
                            fontWeight: FontWeight.bold,
                          ),
                        )
                      : Icon(
                          _getTrustLevelIcon(trustLevel),
                          color: color,
                          size: size * 0.5,
                        ),
                ),
              ),

              // Trust level label
              if (showLabel) ...[
                const SizedBox(height: 4),
                Text(
                  trustLevel.displayName,
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: color,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ],
          ),
        );
      },
      loading: () => _buildLoadingState(),
      error: (error, stackTrace) => _buildErrorState(theme, error),
    );
  }

  /// Builds the loading state widget
  Widget _buildLoadingState() {
    return SizedBox(
      width: size,
      height: size,
      child: const CircularProgressIndicator(),
    );
  }

  /// Builds the error state widget
  Widget _buildErrorState(ThemeData theme, Object error) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: size,
          height: size,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: theme.colorScheme.error.withAlpha(25),
            border: Border.all(
              color: theme.colorScheme.error,
              width: 2,
            ),
          ),
          child: Center(
            child: Icon(
              Icons.error_outline,
              color: theme.colorScheme.error,
              size: size * 0.5,
            ),
          ),
        ),
        if (showLabel) ...[
          const SizedBox(height: 4),
          Text(
            'Error',
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.error,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ],
    );
  }

  /// Builds the empty state widget
  Widget _buildEmptyState(ThemeData theme) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: size,
          height: size,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: Colors.grey.withAlpha(25),
            border: Border.all(
              color: Colors.grey,
              width: 2,
            ),
          ),
          child: Center(
            child: Icon(
              Icons.help_outline,
              color: Colors.grey,
              size: size * 0.5,
            ),
          ),
        ),
        if (showLabel) ...[
          const SizedBox(height: 4),
          Text(
            'Unknown',
            style: theme.textTheme.bodySmall?.copyWith(
              color: Colors.grey,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ],
    );
  }

  /// Returns the icon for the given trust level
  IconData _getTrustLevelIcon(TrustLevel level) {
    switch (level) {
      case TrustLevel.exceptional:
        return Icons.verified;
      case TrustLevel.excellent:
        return Icons.star;
      case TrustLevel.great:
        return Icons.thumb_up;
      case TrustLevel.good:
        return Icons.check_circle;
      case TrustLevel.average:
        return Icons.sentiment_neutral;
      case TrustLevel.fair:
        return Icons.sentiment_dissatisfied;
      case TrustLevel.poor:
        return Icons.warning;
      case TrustLevel.untrusted:
        return Icons.block;
    }
  }
}

/// A widget for displaying trust factor scores
class TrustFactorsWidget extends ConsumerWidget {
  /// The user ID to display the trust factors for
  final String userId;

  /// Callback when a factor is tapped
  final Function(TrustFactor)? onFactorTap;

  /// Creates a new trust factors widget
  const TrustFactorsWidget({
    super.key,
    required this.userId,
    this.onFactorTap,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final trustScoreAsync = ref.watch(trustScoreProvider(userId));
    final trustFactorsAsync = ref.watch(trustFactorsProvider);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Trust Factors',
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        trustScoreAsync.when(
          data: (trustScore) {
            return trustFactorsAsync.when(
              data: (factors) {
                if (factors.isEmpty) {
                  return const Center(
                    child: Text('No trust factors available'),
                  );
                }

                return ListView.builder(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  itemCount: factors.length,
                  itemBuilder: (context, index) {
                    final factor = factors[index];
                    final factorScore = trustScore?.factorScores[
                            factor.type.toString().split('.').last] ??
                        0;

                    return _buildFactorItem(context, factor, factorScore);
                  },
                );
              },
              loading: () => const Center(
                child: CircularProgressIndicator(),
              ),
              error: (error, stackTrace) => Center(
                child: Text('Error loading trust factors: $error'),
              ),
            );
          },
          loading: () => const Center(
            child: CircularProgressIndicator(),
          ),
          error: (error, stackTrace) => Center(
            child: Text('Error loading trust score: $error'),
          ),
        ),
      ],
    );
  }

  /// Builds a trust factor item
  Widget _buildFactorItem(BuildContext context, TrustFactor factor, int score) {
    final theme = Theme.of(context);

    return InkWell(
      onTap: onFactorTap != null ? () => onFactorTap!(factor) : null,
      borderRadius: BorderRadius.circular(8),
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 8),
        child: Row(
          children: [
            // Factor icon
            Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: theme.colorScheme.primary.withAlpha(25),
                shape: BoxShape.circle,
              ),
              child: Center(
                child: factor.iconUrl != null
                    ? Image.network(
                        factor.iconUrl!,
                        width: 24,
                        height: 24,
                        errorBuilder: (context, error, stackTrace) {
                          return Icon(
                            _getFactorIcon(factor.type),
                            color: theme.colorScheme.primary,
                            size: 24,
                          );
                        },
                      )
                    : Icon(
                        _getFactorIcon(factor.type),
                        color: theme.colorScheme.primary,
                        size: 24,
                      ),
              ),
            ),
            const SizedBox(width: 12),

            // Factor details
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    factor.name,
                    style: theme.textTheme.titleSmall,
                  ),
                  const SizedBox(height: 4),
                  Text(
                    factor.description,
                    style: theme.textTheme.bodySmall,
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ),
            const SizedBox(width: 12),

            // Factor score
            Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: _getScoreColor(score).withAlpha(25),
                shape: BoxShape.circle,
                border: Border.all(
                  color: _getScoreColor(score),
                  width: 2,
                ),
              ),
              child: Center(
                child: Text(
                  '$score',
                  style: TextStyle(
                    color: _getScoreColor(score),
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Returns the icon for the given trust factor type
  IconData _getFactorIcon(TrustFactorType type) {
    switch (type) {
      case TrustFactorType.identityVerification:
        return Icons.badge;
      case TrustFactorType.professionalCertification:
        return Icons.school;
      case TrustFactorType.backgroundCheck:
        return Icons.security;
      case TrustFactorType.positiveReviews:
        return Icons.star;
      case TrustFactorType.completedExperiences:
        return Icons.check_circle;
      case TrustFactorType.responseRate:
        return Icons.chat;
      case TrustFactorType.accountAge:
        return Icons.access_time;
      case TrustFactorType.socialMediaVerification:
        return Icons.people;
      case TrustFactorType.governmentLicense:
        return Icons.card_membership;
      case TrustFactorType.communityStanding:
        return Icons.public;
    }
  }

  /// Returns the color for the given score
  Color _getScoreColor(int score) {
    if (score >= 90) return Colors.green;
    if (score >= 70) return Colors.lightGreen;
    if (score >= 50) return Colors.amber;
    if (score >= 30) return Colors.orange;
    return Colors.red;
  }
}
