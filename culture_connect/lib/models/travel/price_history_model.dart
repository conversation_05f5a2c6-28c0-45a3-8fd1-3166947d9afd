import 'package:culture_connect/models/travel/travel.dart';
import 'package:culture_connect/models/travel/price_comparison_model.dart';

/// A model representing a price history entry
class PriceHistoryEntry {
  /// Unique identifier for the price history entry
  final String id;
  
  /// ID of the travel service
  final String travelServiceId;
  
  /// Type of travel service
  final TravelServiceType travelServiceType;
  
  /// Price source
  final PriceSource source;
  
  /// Price at this point in time
  final double price;
  
  /// Currency
  final String currency;
  
  /// Date of the price
  final DateTime date;
  
  /// Creates a new price history entry
  const PriceHistoryEntry({
    required this.id,
    required this.travelServiceId,
    required this.travelServiceType,
    required this.source,
    required this.price,
    required this.currency,
    required this.date,
  });
  
  /// Creates a copy with some fields replaced
  PriceHistoryEntry copyWith({
    String? id,
    String? travelServiceId,
    TravelServiceType? travelServiceType,
    PriceSource? source,
    double? price,
    String? currency,
    DateTime? date,
  }) {
    return PriceHistoryEntry(
      id: id ?? this.id,
      travelServiceId: travelServiceId ?? this.travelServiceId,
      travelServiceType: travelServiceType ?? this.travelServiceType,
      source: source ?? this.source,
      price: price ?? this.price,
      currency: currency ?? this.currency,
      date: date ?? this.date,
    );
  }
  
  /// Get the formatted price
  String get formattedPrice {
    return '$currency${price.toStringAsFixed(2)}';
  }
  
  /// Converts to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'travelServiceId': travelServiceId,
      'travelServiceType': travelServiceType.index,
      'source': source.toJson(),
      'price': price,
      'currency': currency,
      'date': date.toIso8601String(),
    };
  }
  
  /// Creates from JSON
  factory PriceHistoryEntry.fromJson(Map<String, dynamic> json) {
    return PriceHistoryEntry(
      id: json['id'] as String,
      travelServiceId: json['travelServiceId'] as String,
      travelServiceType: TravelServiceType.values[json['travelServiceType'] as int],
      source: PriceSource.fromJson(json['source'] as Map<String, dynamic>),
      price: json['price'] as double,
      currency: json['currency'] as String,
      date: DateTime.parse(json['date'] as String),
    );
  }
}

/// A model representing a price history for a travel service
class PriceHistory {
  /// Unique identifier for the price history
  final String id;
  
  /// ID of the travel service
  final String travelServiceId;
  
  /// Type of travel service
  final TravelServiceType travelServiceType;
  
  /// Price history entries
  final List<PriceHistoryEntry> entries;
  
  /// When the price history was last updated
  final DateTime lastUpdated;
  
  /// Creates a new price history
  const PriceHistory({
    required this.id,
    required this.travelServiceId,
    required this.travelServiceType,
    required this.entries,
    required this.lastUpdated,
  });
  
  /// Creates a copy with some fields replaced
  PriceHistory copyWith({
    String? id,
    String? travelServiceId,
    TravelServiceType? travelServiceType,
    List<PriceHistoryEntry>? entries,
    DateTime? lastUpdated,
  }) {
    return PriceHistory(
      id: id ?? this.id,
      travelServiceId: travelServiceId ?? this.travelServiceId,
      travelServiceType: travelServiceType ?? this.travelServiceType,
      entries: entries ?? this.entries,
      lastUpdated: lastUpdated ?? this.lastUpdated,
    );
  }
  
  /// Get the minimum price
  double? get minPrice {
    if (entries.isEmpty) return null;
    return entries.map((e) => e.price).reduce((a, b) => a < b ? a : b);
  }
  
  /// Get the maximum price
  double? get maxPrice {
    if (entries.isEmpty) return null;
    return entries.map((e) => e.price).reduce((a, b) => a > b ? a : b);
  }
  
  /// Get the average price
  double? get averagePrice {
    if (entries.isEmpty) return null;
    final sum = entries.map((e) => e.price).reduce((a, b) => a + b);
    return sum / entries.length;
  }
  
  /// Get the current price
  double? get currentPrice {
    if (entries.isEmpty) return null;
    
    // Sort by date (newest first)
    final sortedEntries = List<PriceHistoryEntry>.from(entries)
      ..sort((a, b) => b.date.compareTo(a.date));
    
    return sortedEntries.first.price;
  }
  
  /// Get the price trend (percentage change over the last 30 days)
  double? get priceTrend {
    if (entries.length < 2) return null;
    
    // Sort by date
    final sortedEntries = List<PriceHistoryEntry>.from(entries)
      ..sort((a, b) => a.date.compareTo(b.date));
    
    // Get the oldest and newest prices within the last 30 days
    final now = DateTime.now();
    final thirtyDaysAgo = now.subtract(const Duration(days: 30));
    
    final recentEntries = sortedEntries
        .where((e) => e.date.isAfter(thirtyDaysAgo))
        .toList();
    
    if (recentEntries.length < 2) return null;
    
    final oldestPrice = recentEntries.first.price;
    final newestPrice = recentEntries.last.price;
    
    // Calculate percentage change
    return ((newestPrice - oldestPrice) / oldestPrice) * 100;
  }
  
  /// Get the price forecast (predicted price in 30 days)
  double? get priceForecast {
    if (entries.length < 3) return null;
    
    // This is a simple linear regression forecast
    // In a real app, this would use more sophisticated algorithms
    
    // Sort by date
    final sortedEntries = List<PriceHistoryEntry>.from(entries)
      ..sort((a, b) => a.date.compareTo(b.date));
    
    // Calculate the average rate of change per day
    double sumRateOfChange = 0;
    int countRateOfChange = 0;
    
    for (int i = 1; i < sortedEntries.length; i++) {
      final previous = sortedEntries[i - 1];
      final current = sortedEntries[i];
      
      final daysDifference = current.date.difference(previous.date).inDays;
      if (daysDifference == 0) continue;
      
      final priceChange = current.price - previous.price;
      final rateOfChange = priceChange / daysDifference;
      
      sumRateOfChange += rateOfChange;
      countRateOfChange++;
    }
    
    if (countRateOfChange == 0) return null;
    
    final averageRateOfChange = sumRateOfChange / countRateOfChange;
    final latestPrice = sortedEntries.last.price;
    final latestDate = sortedEntries.last.date;
    
    final now = DateTime.now();
    final thirtyDaysFromNow = now.add(const Duration(days: 30));
    final daysToForecast = thirtyDaysFromNow.difference(latestDate).inDays;
    
    return latestPrice + (averageRateOfChange * daysToForecast);
  }
  
  /// Converts to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'travelServiceId': travelServiceId,
      'travelServiceType': travelServiceType.index,
      'entries': entries.map((e) => e.toJson()).toList(),
      'lastUpdated': lastUpdated.toIso8601String(),
    };
  }
  
  /// Creates from JSON
  factory PriceHistory.fromJson(Map<String, dynamic> json) {
    return PriceHistory(
      id: json['id'] as String,
      travelServiceId: json['travelServiceId'] as String,
      travelServiceType: TravelServiceType.values[json['travelServiceType'] as int],
      entries: (json['entries'] as List)
          .map((e) => PriceHistoryEntry.fromJson(e as Map<String, dynamic>))
          .toList(),
      lastUpdated: DateTime.parse(json['lastUpdated'] as String),
    );
  }
}
