import 'package:flutter/material.dart';
import 'travel_service_base.dart';

/// Enum for restaurant types
enum RestaurantType {
  fineDining,
  casual,
  fastFood,
  cafe,
  buffet,
  streetFood,
  bistro,
  pub,
  bar,
  lounge,
}

/// Extension for restaurant types
extension RestaurantTypeExtension on RestaurantType {
  /// Get the display name for the restaurant type
  String get displayName {
    switch (this) {
      case RestaurantType.fineDining:
        return 'Fine Dining';
      case RestaurantType.casual:
        return 'Casual Dining';
      case RestaurantType.fastFood:
        return 'Fast Food';
      case RestaurantType.cafe:
        return 'Café';
      case RestaurantType.buffet:
        return 'Buffet';
      case RestaurantType.streetFood:
        return 'Street Food';
      case RestaurantType.bistro:
        return 'Bistro';
      case RestaurantType.pub:
        return 'Pub';
      case RestaurantType.bar:
        return 'Bar';
      case RestaurantType.lounge:
        return 'Lounge';
    }
  }

  /// Get the icon for the restaurant type
  IconData get icon {
    switch (this) {
      case RestaurantType.fineDining:
        return Icons.restaurant;
      case RestaurantType.casual:
        return Icons.restaurant;
      case RestaurantType.fastFood:
        return Icons.fastfood;
      case RestaurantType.cafe:
        return Icons.coffee;
      case RestaurantType.buffet:
        return Icons.restaurant_menu;
      case RestaurantType.streetFood:
        return Icons.food_bank;
      case RestaurantType.bistro:
        return Icons.restaurant;
      case RestaurantType.pub:
        return Icons.sports_bar;
      case RestaurantType.bar:
        return Icons.local_bar;
      case RestaurantType.lounge:
        return Icons.nightlife;
    }
  }
}

/// Enum for cuisine types
enum CuisineType {
  african,
  american,
  asian,
  caribbean,
  chinese,
  european,
  french,
  greek,
  indian,
  italian,
  japanese,
  korean,
  mediterranean,
  mexican,
  middleEastern,
  spanish,
  thai,
  turkish,
  vietnamese,
  fusion,
  international,
  local,
  seafood,
  vegetarian,
  vegan,
  glutenFree,
}

/// Extension for cuisine types
extension CuisineTypeExtension on CuisineType {
  /// Get the display name for the cuisine type
  String get displayName {
    switch (this) {
      case CuisineType.african:
        return 'African';
      case CuisineType.american:
        return 'American';
      case CuisineType.asian:
        return 'Asian';
      case CuisineType.caribbean:
        return 'Caribbean';
      case CuisineType.chinese:
        return 'Chinese';
      case CuisineType.european:
        return 'European';
      case CuisineType.french:
        return 'French';
      case CuisineType.greek:
        return 'Greek';
      case CuisineType.indian:
        return 'Indian';
      case CuisineType.italian:
        return 'Italian';
      case CuisineType.japanese:
        return 'Japanese';
      case CuisineType.korean:
        return 'Korean';
      case CuisineType.mediterranean:
        return 'Mediterranean';
      case CuisineType.mexican:
        return 'Mexican';
      case CuisineType.middleEastern:
        return 'Middle Eastern';
      case CuisineType.spanish:
        return 'Spanish';
      case CuisineType.thai:
        return 'Thai';
      case CuisineType.turkish:
        return 'Turkish';
      case CuisineType.vietnamese:
        return 'Vietnamese';
      case CuisineType.fusion:
        return 'Fusion';
      case CuisineType.international:
        return 'International';
      case CuisineType.local:
        return 'Local';
      case CuisineType.seafood:
        return 'Seafood';
      case CuisineType.vegetarian:
        return 'Vegetarian';
      case CuisineType.vegan:
        return 'Vegan';
      case CuisineType.glutenFree:
        return 'Gluten-Free';
    }
  }
}

/// A model representing a menu item
class MenuItem {
  /// Unique identifier for the menu item
  final String id;

  /// Name of the menu item
  final String name;

  /// Description of the menu item
  final String description;

  /// Price of the menu item
  final double price;

  /// Currency of the price
  final String currency;

  /// Category of the menu item (e.g., Appetizers, Main Course, Desserts)
  final String category;

  /// Whether the menu item is vegetarian
  final bool isVegetarian;

  /// Whether the menu item is vegan
  final bool isVegan;

  /// Whether the menu item is gluten-free
  final bool isGlutenFree;

  /// Whether the menu item contains nuts
  final bool containsNuts;

  /// Whether the menu item is spicy
  final bool isSpicy;

  /// Whether the menu item is popular
  final bool isPopular;

  /// URL to the image of the menu item
  final String? imageUrl;

  /// Creates a new menu item
  const MenuItem({
    required this.id,
    required this.name,
    required this.description,
    required this.price,
    required this.currency,
    required this.category,
    required this.isVegetarian,
    required this.isVegan,
    required this.isGlutenFree,
    required this.containsNuts,
    required this.isSpicy,
    this.isPopular = false,
    this.imageUrl,
  });

  /// Get the formatted price
  String get formattedPrice {
    return '$currency${price.toStringAsFixed(2)}';
  }
}

/// A model representing a restaurant
class Restaurant extends TravelService {
  /// Type of restaurant
  final RestaurantType restaurantType;

  /// Cuisine types offered by the restaurant
  final List<CuisineType> cuisineTypes;

  /// Menu items
  final List<MenuItem> menuItems;

  /// Opening hours
  final Map<String, String> openingHours;

  /// Whether the restaurant has outdoor seating
  final bool hasOutdoorSeating;

  /// Whether the restaurant has a bar
  final bool hasBar;

  /// Whether the restaurant has live music
  final bool hasLiveMusic;

  /// Whether the restaurant has a kids menu
  final bool hasKidsMenu;

  /// Whether the restaurant has vegetarian options
  final bool hasVegetarianOptions;

  /// Whether the restaurant has vegan options
  final bool hasVeganOptions;

  /// Whether the restaurant has gluten-free options
  final bool hasGlutenFreeOptions;

  /// Whether the restaurant has halal options
  final bool hasHalalOptions;

  /// Whether the restaurant has kosher options
  final bool hasKosherOptions;

  /// Whether the restaurant has a dress code
  final bool hasDressCode;

  /// Dress code description (if applicable)
  final String? dressCodeDescription;

  /// Whether the restaurant requires reservations
  final bool requiresReservation;

  /// Whether the restaurant has parking
  final bool hasParking;

  /// Whether the restaurant has valet parking
  final bool hasValetParking;

  /// Whether the restaurant is wheelchair accessible
  final bool isWheelchairAccessible;

  /// Whether the restaurant has WiFi
  final bool hasWifi;

  /// Whether the restaurant accepts credit cards
  final bool acceptsCreditCards;

  /// Whether the restaurant has a view
  final bool hasView;

  /// View description (if applicable)
  final String? viewDescription;

  /// Creates a new restaurant
  const Restaurant({
    required super.id,
    required super.name,
    required super.description,
    required super.price,
    required super.currency,
    required super.rating,
    required super.reviewCount,
    required super.imageUrl,
    required super.additionalImages,
    required super.provider,
    required super.location,
    required super.coordinates,
    required super.isAvailable,
    required super.isFeatured,
    required super.isOnSale,
    super.originalPrice,
    super.discountPercentage,
    required super.tags,
    required super.amenities,
    required super.cancellationPolicy,
    required super.createdAt,
    required super.updatedAt,
    required this.restaurantType,
    required this.cuisineTypes,
    required this.menuItems,
    required this.openingHours,
    required this.hasOutdoorSeating,
    required this.hasBar,
    required this.hasLiveMusic,
    required this.hasKidsMenu,
    required this.hasVegetarianOptions,
    required this.hasVeganOptions,
    required this.hasGlutenFreeOptions,
    required this.hasHalalOptions,
    required this.hasKosherOptions,
    required this.hasDressCode,
    this.dressCodeDescription,
    required this.requiresReservation,
    required this.hasParking,
    required this.hasValetParking,
    required this.isWheelchairAccessible,
    required this.hasWifi,
    required this.acceptsCreditCards,
    required this.hasView,
    this.viewDescription,
  });

  @override
  IconData get icon => Icons.restaurant;

  @override
  Color get color => Colors.orange;

  /// Get the formatted cuisine types
  String get formattedCuisineTypes {
    return cuisineTypes.map((type) => type.displayName).join(', ');
  }

  /// Get the opening hours for a specific day
  String getOpeningHoursForDay(String day) {
    return openingHours[day] ?? 'Closed';
  }

  /// Check if the restaurant is open at a specific time
  bool isOpenAt(DateTime dateTime) {
    // Get the day of the week
    final day = _getDayOfWeek(dateTime.weekday);

    // Get the opening hours for that day
    final hours = openingHours[day];

    // If the restaurant is closed on that day, return false
    if (hours == null || hours == 'Closed') {
      return false;
    }

    // Parse the opening hours
    final parts = hours.split(' - ');
    if (parts.length != 2) {
      return false;
    }

    // Parse the opening and closing times
    final openingTime = _parseTime(parts[0]);
    final closingTime = _parseTime(parts[1]);

    // Check if the restaurant is open 24 hours
    if (openingTime == null || closingTime == null) {
      return false;
    }

    // Create DateTime objects for the opening and closing times
    final opening = DateTime(
      dateTime.year,
      dateTime.month,
      dateTime.day,
      openingTime.hour,
      openingTime.minute,
    );

    // If the closing time is earlier than the opening time, it means the restaurant closes after midnight
    DateTime closing;
    if (closingTime.hour < openingTime.hour) {
      closing = DateTime(
        dateTime.year,
        dateTime.month,
        dateTime.day + 1,
        closingTime.hour,
        closingTime.minute,
      );
    } else {
      closing = DateTime(
        dateTime.year,
        dateTime.month,
        dateTime.day,
        closingTime.hour,
        closingTime.minute,
      );
    }

    // Check if the given time is between the opening and closing times
    return dateTime.isAfter(opening) && dateTime.isBefore(closing);
  }

  /// Get the day of the week as a string
  String _getDayOfWeek(int weekday) {
    switch (weekday) {
      case DateTime.monday:
        return 'Monday';
      case DateTime.tuesday:
        return 'Tuesday';
      case DateTime.wednesday:
        return 'Wednesday';
      case DateTime.thursday:
        return 'Thursday';
      case DateTime.friday:
        return 'Friday';
      case DateTime.saturday:
        return 'Saturday';
      case DateTime.sunday:
        return 'Sunday';
      default:
        return '';
    }
  }

  /// Parse a time string (e.g., "9:00 AM") into a TimeOfDay object
  TimeOfDay? _parseTime(String timeString) {
    // Split the time string into time and period
    final parts = timeString.split(' ');
    if (parts.length != 2) {
      return null;
    }

    // Parse the time
    final timeParts = parts[0].split(':');
    if (timeParts.length != 2) {
      return null;
    }

    // Parse the hour and minute
    final hour = int.tryParse(timeParts[0]);
    final minute = int.tryParse(timeParts[1]);
    if (hour == null || minute == null) {
      return null;
    }

    // Parse the period
    final period = parts[1];

    // Convert to 24-hour format
    int hour24 = hour;
    if (period == 'PM' && hour != 12) {
      hour24 += 12;
    } else if (period == 'AM' && hour == 12) {
      hour24 = 0;
    }

    return TimeOfDay(hour: hour24, minute: minute);
  }
}
