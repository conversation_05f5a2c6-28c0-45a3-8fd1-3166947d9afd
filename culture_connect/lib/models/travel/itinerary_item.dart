import 'package:flutter/material.dart';
import 'package:uuid/uuid.dart';
import 'package:culture_connect/models/travel/travel_service_base.dart';
import 'package:culture_connect/models/location/geo_location.dart';

/// Type of itinerary item
enum ItineraryItemType {
  /// Accommodation (hotel, hostel, etc.)
  accommodation,

  /// Transportation (flight, train, bus, etc.)
  transportation,

  /// Activity (tour, museum, etc.)
  activity,

  /// Food (restaurant, cafe, etc.)
  food,

  /// Custom item
  custom,
}

/// Extension for itinerary item types
extension ItineraryItemTypeExtension on ItineraryItemType {
  /// Get the display name for the itinerary item type
  String get displayName {
    switch (this) {
      case ItineraryItemType.accommodation:
        return 'Accommodation';
      case ItineraryItemType.transportation:
        return 'Transportation';
      case ItineraryItemType.activity:
        return 'Activity';
      case ItineraryItemType.food:
        return 'Food';
      case ItineraryItemType.custom:
        return 'Custom';
    }
  }

  /// Get the icon for the itinerary item type
  IconData get icon {
    switch (this) {
      case ItineraryItemType.accommodation:
        return Icons.hotel;
      case ItineraryItemType.transportation:
        return Icons.directions_transit;
      case ItineraryItemType.activity:
        return Icons.attractions;
      case ItineraryItemType.food:
        return Icons.restaurant;
      case ItineraryItemType.custom:
        return Icons.event_note;
    }
  }

  /// Get the color for the itinerary item type
  Color get color {
    switch (this) {
      case ItineraryItemType.accommodation:
        return Colors.indigo;
      case ItineraryItemType.transportation:
        return Colors.blue;
      case ItineraryItemType.activity:
        return Colors.green;
      case ItineraryItemType.food:
        return Colors.orange;
      case ItineraryItemType.custom:
        return Colors.purple;
    }
  }
}

/// Status of an itinerary item
enum ItineraryItemStatus {
  /// Planned item
  planned,

  /// Booked item
  booked,

  /// Completed item
  completed,

  /// Cancelled item
  cancelled,
}

/// Extension for itinerary item status
extension ItineraryItemStatusExtension on ItineraryItemStatus {
  /// Get the display name for the itinerary item status
  String get displayName {
    switch (this) {
      case ItineraryItemStatus.planned:
        return 'Planned';
      case ItineraryItemStatus.booked:
        return 'Booked';
      case ItineraryItemStatus.completed:
        return 'Completed';
      case ItineraryItemStatus.cancelled:
        return 'Cancelled';
    }
  }

  /// Get the icon for the itinerary item status
  IconData get icon {
    switch (this) {
      case ItineraryItemStatus.planned:
        return Icons.event_available;
      case ItineraryItemStatus.booked:
        return Icons.confirmation_number;
      case ItineraryItemStatus.completed:
        return Icons.check_circle;
      case ItineraryItemStatus.cancelled:
        return Icons.cancel;
    }
  }

  /// Get the color for the itinerary item status
  Color get color {
    switch (this) {
      case ItineraryItemStatus.planned:
        return Colors.blue;
      case ItineraryItemStatus.booked:
        return Colors.green;
      case ItineraryItemStatus.completed:
        return Colors.purple;
      case ItineraryItemStatus.cancelled:
        return Colors.red;
    }
  }
}

/// An item in an itinerary
class ItineraryItem {
  /// The unique identifier
  final String id;

  /// The title
  final String title;

  /// The description
  final String? description;

  /// The type
  final ItineraryItemType type;

  /// The status
  final ItineraryItemStatus status;

  /// The start time
  final TimeOfDay? startTime;

  /// The end time
  final TimeOfDay? endTime;

  /// The location
  final String? location;

  /// The coordinates (latitude and longitude)
  final GeoLocation? coordinates;

  /// The price
  final double? price;

  /// The currency
  final String? currency;

  /// The service ID (if linked to a travel service)
  final String? serviceId;

  /// The service type (if linked to a travel service)
  final TravelServiceType? serviceType;

  /// The booking ID (if booked)
  final String? bookingId;

  /// The booking reference (if booked)
  final String? bookingReference;

  /// The recommendation score (if recommended by AI)
  final double? recommendationScore;

  /// The recommendation reason (if recommended by AI)
  final String? recommendationReason;

  /// The notes
  final String? notes;

  /// The created at timestamp
  final DateTime createdAt;

  /// The updated at timestamp
  final DateTime updatedAt;

  /// Creates a new itinerary item
  ItineraryItem({
    String? id,
    required this.title,
    this.description,
    required this.type,
    this.status = ItineraryItemStatus.planned,
    this.startTime,
    this.endTime,
    this.location,
    this.coordinates,
    this.price,
    this.currency,
    this.serviceId,
    this.serviceType,
    this.bookingId,
    this.bookingReference,
    this.recommendationScore,
    this.recommendationReason,
    this.notes,
    DateTime? createdAt,
    DateTime? updatedAt,
  })  : id = id ?? const Uuid().v4(),
        createdAt = createdAt ?? DateTime.now(),
        updatedAt = updatedAt ?? DateTime.now();

  /// Get the formatted price
  String? get formattedPrice {
    if (price == null) return null;
    final curr = currency ?? 'USD';
    // We've already checked that price is not null above
    return '$curr${price!.toStringAsFixed(2)}';
  }

  /// Get the formatted start time
  String? get formattedStartTime {
    if (startTime == null) return null;
    final hour = startTime!.hour.toString().padLeft(2, '0');
    final minute = startTime!.minute.toString().padLeft(2, '0');
    return '$hour:$minute';
  }

  /// Get the formatted end time
  String? get formattedEndTime {
    if (endTime == null) return null;
    final hour = endTime!.hour.toString().padLeft(2, '0');
    final minute = endTime!.minute.toString().padLeft(2, '0');
    return '$hour:$minute';
  }

  /// Get the formatted time range
  String? get formattedTimeRange {
    if (startTime == null) return null;
    if (endTime == null) return formattedStartTime;
    return '$formattedStartTime - $formattedEndTime';
  }

  /// Get the duration in minutes
  int? get durationInMinutes {
    if (startTime == null || endTime == null) return null;
    final startMinutes = startTime!.hour * 60 + startTime!.minute;
    final endMinutes = endTime!.hour * 60 + endTime!.minute;
    return endMinutes - startMinutes;
  }

  /// Get the formatted duration
  String? get formattedDuration {
    final duration = durationInMinutes;
    if (duration == null) return null;

    if (duration < 60) {
      return '$duration min';
    } else {
      final hours = duration ~/ 60;
      final minutes = duration % 60;
      return '$hours h${minutes > 0 ? ' $minutes min' : ''}';
    }
  }

  /// Create a copy of this item with the given fields replaced
  ItineraryItem copyWith({
    String? id,
    String? title,
    String? description,
    ItineraryItemType? type,
    ItineraryItemStatus? status,
    TimeOfDay? startTime,
    TimeOfDay? endTime,
    String? location,
    GeoLocation? coordinates,
    double? price,
    String? currency,
    String? serviceId,
    TravelServiceType? serviceType,
    String? bookingId,
    String? bookingReference,
    double? recommendationScore,
    String? recommendationReason,
    String? notes,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return ItineraryItem(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      type: type ?? this.type,
      status: status ?? this.status,
      startTime: startTime ?? this.startTime,
      endTime: endTime ?? this.endTime,
      location: location ?? this.location,
      coordinates: coordinates ?? this.coordinates,
      price: price ?? this.price,
      currency: currency ?? this.currency,
      serviceId: serviceId ?? this.serviceId,
      serviceType: serviceType ?? this.serviceType,
      bookingId: bookingId ?? this.bookingId,
      bookingReference: bookingReference ?? this.bookingReference,
      recommendationScore: recommendationScore ?? this.recommendationScore,
      recommendationReason: recommendationReason ?? this.recommendationReason,
      notes: notes ?? this.notes,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? DateTime.now(),
    );
  }

  /// Create an itinerary item from JSON
  factory ItineraryItem.fromJson(Map<String, dynamic> json) {
    return ItineraryItem(
      id: json['id'],
      title: json['title'],
      description: json['description'],
      type: ItineraryItemType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => ItineraryItemType.custom,
      ),
      status: ItineraryItemStatus.values.firstWhere(
        (e) => e.name == json['status'],
        orElse: () => ItineraryItemStatus.planned,
      ),
      startTime: json['startTime'] != null
          ? TimeOfDay(
              hour: int.parse(json['startTime'].split(':')[0]),
              minute: int.parse(json['startTime'].split(':')[1]),
            )
          : null,
      endTime: json['endTime'] != null
          ? TimeOfDay(
              hour: int.parse(json['endTime'].split(':')[0]),
              minute: int.parse(json['endTime'].split(':')[1]),
            )
          : null,
      location: json['location'],
      coordinates: json['coordinates'] != null
          ? GeoLocation.fromJson(json['coordinates'] as Map<String, dynamic>)
          : null,
      price: json['price'],
      currency: json['currency'],
      serviceId: json['serviceId'],
      serviceType: json['serviceType'] != null
          ? _findServiceTypeByName(json['serviceType'])
          : null,
      bookingId: json['bookingId'],
      bookingReference: json['bookingReference'],
      recommendationScore: json['recommendationScore'],
      recommendationReason: json['recommendationReason'],
      notes: json['notes'],
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: DateTime.parse(json['updatedAt']),
    );
  }

  /// Helper method to find a service type by name
  static TravelServiceType? _findServiceTypeByName(String name) {
    try {
      return TravelServiceType.values.firstWhere(
        (e) => e.name == name,
      );
    } catch (e) {
      // If no matching service type is found, return null
      return null;
    }
  }

  /// Convert this itinerary item to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'type': type.name,
      'status': status.name,
      'startTime': startTime != null
          ? '${startTime!.hour.toString().padLeft(2, '0')}:${startTime!.minute.toString().padLeft(2, '0')}'
          : null,
      'endTime': endTime != null
          ? '${endTime!.hour.toString().padLeft(2, '0')}:${endTime!.minute.toString().padLeft(2, '0')}'
          : null,
      'location': location,
      'coordinates': coordinates?.toJson(),
      'price': price,
      'currency': currency,
      'serviceId': serviceId,
      'serviceType': serviceType?.name,
      'bookingId': bookingId,
      'bookingReference': bookingReference,
      'recommendationScore': recommendationScore,
      'recommendationReason': recommendationReason,
      'notes': notes,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }
}
