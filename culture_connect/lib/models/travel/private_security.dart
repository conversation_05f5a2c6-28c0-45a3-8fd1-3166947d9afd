import 'package:flutter/material.dart';
import 'package:culture_connect/models/location/geo_location.dart';
import 'travel_service_base.dart';

/// Enum for security service types
enum SecurityServiceType {
  personalBodyguard,
  vehicleEscort,
  eventSecurity,
  residentialSecurity,
  travelSecurity,
  executiveProtection,
}

/// Extension for security service types
extension SecurityServiceTypeExtension on SecurityServiceType {
  /// Get the display name for the security service type
  String get displayName {
    switch (this) {
      case SecurityServiceType.personalBodyguard:
        return 'Personal Bodyguard';
      case SecurityServiceType.vehicleEscort:
        return 'Vehicle Escort';
      case SecurityServiceType.eventSecurity:
        return 'Event Security';
      case SecurityServiceType.residentialSecurity:
        return 'Residential Security';
      case SecurityServiceType.travelSecurity:
        return 'Travel Security';
      case SecurityServiceType.executiveProtection:
        return 'Executive Protection';
    }
  }

  /// Get the icon for the security service type
  IconData get icon {
    switch (this) {
      case SecurityServiceType.personalBodyguard:
        return Icons.person;
      case SecurityServiceType.vehicleEscort:
        return Icons.directions_car;
      case SecurityServiceType.eventSecurity:
        return Icons.event;
      case SecurityServiceType.residentialSecurity:
        return Icons.home;
      case SecurityServiceType.travelSecurity:
        return Icons.flight;
      case SecurityServiceType.executiveProtection:
        return Icons.business;
    }
  }
}

/// Enum for security personnel training levels
enum SecurityTrainingLevel {
  basic,
  advanced,
  expert,
  specialized,
}

/// Extension for security personnel training levels
extension SecurityTrainingLevelExtension on SecurityTrainingLevel {
  /// Get the display name for the security personnel training level
  String get displayName {
    switch (this) {
      case SecurityTrainingLevel.basic:
        return 'Basic';
      case SecurityTrainingLevel.advanced:
        return 'Advanced';
      case SecurityTrainingLevel.expert:
        return 'Expert';
      case SecurityTrainingLevel.specialized:
        return 'Specialized';
    }
  }
}

/// A model representing a private security service
class PrivateSecurity extends TravelService {
  /// Type of security service
  final SecurityServiceType serviceType;

  /// Number of security personnel
  final int personnelCount;

  /// Training level of security personnel
  final SecurityTrainingLevel trainingLevel;

  /// Whether the security personnel are armed
  final bool isArmed;

  /// Whether the security personnel are in uniform
  final bool isUniformed;

  /// Whether the security personnel have a vehicle
  final bool hasVehicle;

  /// Type of vehicle (if applicable)
  final String? vehicleType;

  /// Whether the security personnel have communication equipment
  final bool hasCommunicationEquipment;

  /// Languages spoken by security personnel
  final List<String> languages;

  /// Security company
  final String securityCompany;

  /// Security company logo URL
  final String securityCompanyLogoUrl;

  /// Whether background checks are included
  final bool includesBackgroundChecks;

  /// Whether risk assessment is included
  final bool includesRiskAssessment;

  /// Whether emergency response is included
  final bool includesEmergencyResponse;

  /// Whether the service includes 24/7 support
  final bool includes24HrSupport;

  /// Creates a new private security service
  const PrivateSecurity({
    required super.id,
    required super.name,
    required super.description,
    required super.price,
    required super.currency,
    required super.rating,
    required super.reviewCount,
    required super.imageUrl,
    required super.additionalImages,
    required super.provider,
    required super.location,
    required super.coordinates,
    required super.isAvailable,
    required super.isFeatured,
    required super.isOnSale,
    super.originalPrice,
    super.discountPercentage,
    required super.tags,
    required super.amenities,
    required super.cancellationPolicy,
    required super.createdAt,
    required super.updatedAt,
    required this.serviceType,
    required this.personnelCount,
    required this.trainingLevel,
    required this.isArmed,
    required this.isUniformed,
    required this.hasVehicle,
    this.vehicleType,
    required this.hasCommunicationEquipment,
    required this.languages,
    required this.securityCompany,
    required this.securityCompanyLogoUrl,
    required this.includesBackgroundChecks,
    required this.includesRiskAssessment,
    required this.includesEmergencyResponse,
    required this.includes24HrSupport,
  });

  @override
  IconData get icon => Icons.security;

  @override
  Color get color => Colors.red;

  /// Creates a copy of this private security service with the given fields replaced
  PrivateSecurity copyWith({
    String? id,
    String? name,
    String? description,
    double? price,
    String? currency,
    double? rating,
    int? reviewCount,
    String? imageUrl,
    List<String>? additionalImages,
    String? provider,
    String? location,
    GeoLocation? coordinates,
    bool? isAvailable,
    bool? isFeatured,
    bool? isOnSale,
    double? originalPrice,
    double? discountPercentage,
    List<String>? tags,
    List<String>? amenities,
    String? cancellationPolicy,
    DateTime? createdAt,
    DateTime? updatedAt,
    SecurityServiceType? serviceType,
    int? personnelCount,
    SecurityTrainingLevel? trainingLevel,
    bool? isArmed,
    bool? isUniformed,
    bool? hasVehicle,
    String? vehicleType,
    bool? hasCommunicationEquipment,
    List<String>? languages,
    String? securityCompany,
    String? securityCompanyLogoUrl,
    bool? includesBackgroundChecks,
    bool? includesRiskAssessment,
    bool? includesEmergencyResponse,
    bool? includes24HrSupport,
  }) {
    return PrivateSecurity(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      price: price ?? this.price,
      currency: currency ?? this.currency,
      rating: rating ?? this.rating,
      reviewCount: reviewCount ?? this.reviewCount,
      imageUrl: imageUrl ?? this.imageUrl,
      additionalImages: additionalImages ?? this.additionalImages,
      provider: provider ?? this.provider,
      location: location ?? this.location,
      coordinates: coordinates ?? this.coordinates,
      isAvailable: isAvailable ?? this.isAvailable,
      isFeatured: isFeatured ?? this.isFeatured,
      isOnSale: isOnSale ?? this.isOnSale,
      originalPrice: originalPrice ?? this.originalPrice,
      discountPercentage: discountPercentage ?? this.discountPercentage,
      tags: tags ?? this.tags,
      amenities: amenities ?? this.amenities,
      cancellationPolicy: cancellationPolicy ?? this.cancellationPolicy,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      serviceType: serviceType ?? this.serviceType,
      personnelCount: personnelCount ?? this.personnelCount,
      trainingLevel: trainingLevel ?? this.trainingLevel,
      isArmed: isArmed ?? this.isArmed,
      isUniformed: isUniformed ?? this.isUniformed,
      hasVehicle: hasVehicle ?? this.hasVehicle,
      vehicleType: vehicleType ?? this.vehicleType,
      hasCommunicationEquipment:
          hasCommunicationEquipment ?? this.hasCommunicationEquipment,
      languages: languages ?? this.languages,
      securityCompany: securityCompany ?? this.securityCompany,
      securityCompanyLogoUrl:
          securityCompanyLogoUrl ?? this.securityCompanyLogoUrl,
      includesBackgroundChecks:
          includesBackgroundChecks ?? this.includesBackgroundChecks,
      includesRiskAssessment:
          includesRiskAssessment ?? this.includesRiskAssessment,
      includesEmergencyResponse:
          includesEmergencyResponse ?? this.includesEmergencyResponse,
      includes24HrSupport: includes24HrSupport ?? this.includes24HrSupport,
    );
  }
}
