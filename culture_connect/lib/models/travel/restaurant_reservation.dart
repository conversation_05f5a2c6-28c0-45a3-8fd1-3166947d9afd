import 'package:flutter/material.dart';
import 'package:uuid/uuid.dart';
import 'package:culture_connect/models/travel/restaurant.dart';

/// Status of a restaurant reservation
enum ReservationStatus {
  /// Reservation is pending confirmation
  pending,
  
  /// Reservation is confirmed
  confirmed,
  
  /// Reservation is cancelled
  cancelled,
  
  /// Reservation is completed
  completed,
  
  /// Reservation was no-show
  noShow,
}

/// Extension to get display name for reservation status
extension ReservationStatusExtension on ReservationStatus {
  /// Get the display name for the reservation status
  String get displayName {
    switch (this) {
      case ReservationStatus.pending:
        return 'Pending';
      case ReservationStatus.confirmed:
        return 'Confirmed';
      case ReservationStatus.cancelled:
        return 'Cancelled';
      case ReservationStatus.completed:
        return 'Completed';
      case ReservationStatus.noShow:
        return 'No Show';
    }
  }
  
  /// Get the color for the reservation status
  Color get color {
    switch (this) {
      case ReservationStatus.pending:
        return Colors.orange;
      case ReservationStatus.confirmed:
        return Colors.green;
      case ReservationStatus.cancelled:
        return Colors.red;
      case ReservationStatus.completed:
        return Colors.blue;
      case ReservationStatus.noShow:
        return Colors.grey;
    }
  }
  
  /// Get the icon for the reservation status
  IconData get icon {
    switch (this) {
      case ReservationStatus.pending:
        return Icons.pending;
      case ReservationStatus.confirmed:
        return Icons.check_circle;
      case ReservationStatus.cancelled:
        return Icons.cancel;
      case ReservationStatus.completed:
        return Icons.done_all;
      case ReservationStatus.noShow:
        return Icons.person_off;
    }
  }
}

/// A model representing a restaurant reservation time slot
class RestaurantTimeSlot {
  /// Unique identifier for the time slot
  final String id;
  
  /// Start time of the slot
  final TimeOfDay startTime;
  
  /// End time of the slot (typically 1.5-2 hours after start time)
  final TimeOfDay endTime;
  
  /// Maximum number of reservations available for this time slot
  final int maxReservations;
  
  /// Current number of reservations for this time slot
  final int currentReservations;
  
  /// Whether the time slot is available for booking
  bool get isAvailable => currentReservations < maxReservations;
  
  /// Percentage of availability remaining
  double get availabilityPercentage => 
      maxReservations > 0 ? (maxReservations - currentReservations) / maxReservations : 0;
  
  /// Creates a new restaurant time slot
  RestaurantTimeSlot({
    String? id,
    required this.startTime,
    required this.endTime,
    required this.maxReservations,
    this.currentReservations = 0,
  }) : id = id ?? const Uuid().v4();
  
  /// Format the time slot as a string
  String get formatted {
    return '${_formatTimeOfDay(startTime)} - ${_formatTimeOfDay(endTime)}';
  }
  
  /// Format a TimeOfDay as a string
  static String _formatTimeOfDay(TimeOfDay timeOfDay) {
    final hour = timeOfDay.hour == 0 ? 12 : (timeOfDay.hour > 12 ? timeOfDay.hour - 12 : timeOfDay.hour);
    final minute = timeOfDay.minute.toString().padLeft(2, '0');
    final period = timeOfDay.hour < 12 ? 'AM' : 'PM';
    return '$hour:$minute $period';
  }
  
  /// Create a copy of this time slot with the given fields replaced with the new values
  RestaurantTimeSlot copyWith({
    String? id,
    TimeOfDay? startTime,
    TimeOfDay? endTime,
    int? maxReservations,
    int? currentReservations,
  }) {
    return RestaurantTimeSlot(
      id: id ?? this.id,
      startTime: startTime ?? this.startTime,
      endTime: endTime ?? this.endTime,
      maxReservations: maxReservations ?? this.maxReservations,
      currentReservations: currentReservations ?? this.currentReservations,
    );
  }
}

/// A model representing a restaurant reservation
class RestaurantReservation {
  /// Unique identifier for the reservation
  final String id;
  
  /// ID of the restaurant being reserved
  final String restaurantId;
  
  /// Name of the restaurant (for quick reference)
  final String restaurantName;
  
  /// Date of the reservation
  final DateTime date;
  
  /// Time slot for the reservation
  final RestaurantTimeSlot timeSlot;
  
  /// Number of people in the party
  final int partySize;
  
  /// Special requests or notes for the reservation
  final String specialRequests;
  
  /// Status of the reservation
  final ReservationStatus status;
  
  /// User ID of the person making the reservation
  final String userId;
  
  /// Name of the person making the reservation
  final String userName;
  
  /// Contact phone number
  final String contactPhone;
  
  /// Contact email
  final String contactEmail;
  
  /// Whether the reservation has been confirmed by the restaurant
  final bool isConfirmedByRestaurant;
  
  /// When the reservation was created
  final DateTime createdAt;
  
  /// When the reservation was last updated
  final DateTime updatedAt;
  
  /// Creates a new restaurant reservation
  RestaurantReservation({
    String? id,
    required this.restaurantId,
    required this.restaurantName,
    required this.date,
    required this.timeSlot,
    required this.partySize,
    this.specialRequests = '',
    this.status = ReservationStatus.pending,
    required this.userId,
    required this.userName,
    required this.contactPhone,
    required this.contactEmail,
    this.isConfirmedByRestaurant = false,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) : 
    id = id ?? const Uuid().v4(),
    createdAt = createdAt ?? DateTime.now(),
    updatedAt = updatedAt ?? DateTime.now();
  
  /// Create a copy of this reservation with the given fields replaced with the new values
  RestaurantReservation copyWith({
    String? id,
    String? restaurantId,
    String? restaurantName,
    DateTime? date,
    RestaurantTimeSlot? timeSlot,
    int? partySize,
    String? specialRequests,
    ReservationStatus? status,
    String? userId,
    String? userName,
    String? contactPhone,
    String? contactEmail,
    bool? isConfirmedByRestaurant,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return RestaurantReservation(
      id: id ?? this.id,
      restaurantId: restaurantId ?? this.restaurantId,
      restaurantName: restaurantName ?? this.restaurantName,
      date: date ?? this.date,
      timeSlot: timeSlot ?? this.timeSlot,
      partySize: partySize ?? this.partySize,
      specialRequests: specialRequests ?? this.specialRequests,
      status: status ?? this.status,
      userId: userId ?? this.userId,
      userName: userName ?? this.userName,
      contactPhone: contactPhone ?? this.contactPhone,
      contactEmail: contactEmail ?? this.contactEmail,
      isConfirmedByRestaurant: isConfirmedByRestaurant ?? this.isConfirmedByRestaurant,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}
