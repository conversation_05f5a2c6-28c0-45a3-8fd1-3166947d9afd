import 'package:flutter/material.dart';
import 'package:uuid/uuid.dart';
import 'package:culture_connect/models/travel/travel_service_base.dart';

/// Types of travel services
enum TravelServiceType {
  /// Hotel service
  hotel,

  /// Flight service
  flight,

  /// Car rental service
  carRental,

  /// Transfer service
  transfer,

  /// Restaurant service
  restaurant,

  /// Cruise service
  cruise,

  /// Activity service
  activity,

  /// Other service
  other,
}

/// Status of an itinerary
enum ItineraryStatus {
  /// Draft itinerary
  draft,

  /// Confirmed itinerary
  confirmed,

  /// In progress itinerary
  inProgress,

  /// Completed itinerary
  completed,

  /// Cancelled itinerary
  cancelled,
}

/// Extension for itinerary status
extension ItineraryStatusExtension on ItineraryStatus {
  /// Get the display name for the itinerary status
  String get displayName {
    switch (this) {
      case ItineraryStatus.draft:
        return 'Draft';
      case ItineraryStatus.confirmed:
        return 'Confirmed';
      case ItineraryStatus.inProgress:
        return 'In Progress';
      case ItineraryStatus.completed:
        return 'Completed';
      case ItineraryStatus.cancelled:
        return 'Cancelled';
    }
  }

  /// Get the color for the itinerary status
  Color get color {
    switch (this) {
      case ItineraryStatus.draft:
        return Colors.grey;
      case ItineraryStatus.confirmed:
        return Colors.blue;
      case ItineraryStatus.inProgress:
        return Colors.green;
      case ItineraryStatus.completed:
        return Colors.purple;
      case ItineraryStatus.cancelled:
        return Colors.red;
    }
  }

  /// Get the icon for the itinerary status
  IconData get icon {
    switch (this) {
      case ItineraryStatus.draft:
        return Icons.edit_note;
      case ItineraryStatus.confirmed:
        return Icons.check_circle;
      case ItineraryStatus.inProgress:
        return Icons.directions_run;
      case ItineraryStatus.completed:
        return Icons.done_all;
      case ItineraryStatus.cancelled:
        return Icons.cancel;
    }
  }
}

/// A travel itinerary
class Itinerary {
  /// The unique identifier
  final String id;

  /// The user ID
  final String userId;

  /// The title
  final String title;

  /// The destination
  final String destination;

  /// The start date
  final DateTime startDate;

  /// The end date
  final DateTime endDate;

  /// The budget amount
  final double? budgetAmount;

  /// The budget currency
  final String? budgetCurrency;

  /// The status
  final ItineraryStatus status;

  /// The days in the itinerary
  final List<ItineraryDay> days;

  /// The notes
  final String? notes;

  /// The created at timestamp
  final DateTime createdAt;

  /// The updated at timestamp
  final DateTime updatedAt;

  /// Creates a new itinerary
  Itinerary({
    String? id,
    required this.userId,
    required this.title,
    required this.destination,
    required this.startDate,
    required this.endDate,
    this.budgetAmount,
    this.budgetCurrency,
    this.status = ItineraryStatus.draft,
    List<ItineraryDay>? days,
    this.notes,
    DateTime? createdAt,
    DateTime? updatedAt,
  })  : id = id ?? const Uuid().v4(),
        days = days ?? _generateEmptyDays(startDate, endDate),
        createdAt = createdAt ?? DateTime.now(),
        updatedAt = updatedAt ?? DateTime.now();

  /// Generate empty days for the itinerary
  static List<ItineraryDay> _generateEmptyDays(
      DateTime startDate, DateTime endDate) {
    final days = <ItineraryDay>[];
    final dayCount = endDate.difference(startDate).inDays + 1;

    for (int i = 0; i < dayCount; i++) {
      final date = startDate.add(Duration(days: i));
      days.add(ItineraryDay(
        date: date,
        dayNumber: i + 1,
        items: [],
      ));
    }

    return days;
  }

  /// Get the duration in days
  int get durationInDays => endDate.difference(startDate).inDays + 1;

  /// Get the total price of all items
  double get totalPrice {
    return days.fold(
        0,
        (sum, day) =>
            sum +
            day.items.fold(0, (daySum, item) => daySum + (item.price ?? 0)));
  }

  /// Get the formatted total price
  String get formattedTotalPrice {
    final currency = budgetCurrency ?? 'USD';
    return '$currency${totalPrice.toStringAsFixed(2)}';
  }

  /// Get the budget status
  BudgetStatus get budgetStatus {
    if (budgetAmount == null) return BudgetStatus.noBudget;

    final ratio = totalPrice / budgetAmount!;
    if (ratio <= 0.5) return BudgetStatus.underBudget;
    if (ratio <= 0.9) return BudgetStatus.nearBudget;
    if (ratio <= 1.0) return BudgetStatus.atBudget;
    return BudgetStatus.overBudget;
  }

  /// Create a copy of this itinerary with the given fields replaced
  Itinerary copyWith({
    String? id,
    String? userId,
    String? title,
    String? destination,
    DateTime? startDate,
    DateTime? endDate,
    double? budgetAmount,
    String? budgetCurrency,
    ItineraryStatus? status,
    List<ItineraryDay>? days,
    String? notes,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Itinerary(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      title: title ?? this.title,
      destination: destination ?? this.destination,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      budgetAmount: budgetAmount ?? this.budgetAmount,
      budgetCurrency: budgetCurrency ?? this.budgetCurrency,
      status: status ?? this.status,
      days: days ?? this.days,
      notes: notes ?? this.notes,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? DateTime.now(),
    );
  }

  /// Create an itinerary from JSON
  factory Itinerary.fromJson(Map<String, dynamic> json) {
    return Itinerary(
      id: json['id'],
      userId: json['userId'],
      title: json['title'],
      destination: json['destination'],
      startDate: DateTime.parse(json['startDate']),
      endDate: DateTime.parse(json['endDate']),
      budgetAmount: json['budgetAmount'],
      budgetCurrency: json['budgetCurrency'],
      status: ItineraryStatus.values.firstWhere(
        (e) => e.name == json['status'],
        orElse: () => ItineraryStatus.draft,
      ),
      days: (json['days'] as List)
          .map((day) => ItineraryDay.fromJson(day))
          .toList(),
      notes: json['notes'],
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: DateTime.parse(json['updatedAt']),
    );
  }

  /// Convert this itinerary to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userId': userId,
      'title': title,
      'destination': destination,
      'startDate': startDate.toIso8601String(),
      'endDate': endDate.toIso8601String(),
      'budgetAmount': budgetAmount,
      'budgetCurrency': budgetCurrency,
      'status': status.name,
      'days': days.map((day) => day.toJson()).toList(),
      'notes': notes,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }
}

/// Budget status for an itinerary
enum BudgetStatus {
  /// No budget set
  noBudget,

  /// Under budget
  underBudget,

  /// Near budget
  nearBudget,

  /// At budget
  atBudget,

  /// Over budget
  overBudget,
}

/// Extension for budget status
extension BudgetStatusExtension on BudgetStatus {
  /// Get the display name for the budget status
  String get displayName {
    switch (this) {
      case BudgetStatus.noBudget:
        return 'No Budget';
      case BudgetStatus.underBudget:
        return 'Under Budget';
      case BudgetStatus.nearBudget:
        return 'Near Budget';
      case BudgetStatus.atBudget:
        return 'At Budget';
      case BudgetStatus.overBudget:
        return 'Over Budget';
    }
  }

  /// Get the color for the budget status
  Color get color {
    switch (this) {
      case BudgetStatus.noBudget:
        return Colors.grey;
      case BudgetStatus.underBudget:
        return Colors.green;
      case BudgetStatus.nearBudget:
        return Colors.amber;
      case BudgetStatus.atBudget:
        return Colors.orange;
      case BudgetStatus.overBudget:
        return Colors.red;
    }
  }
}

/// A day in an itinerary
class ItineraryDay {
  /// The unique identifier
  final String id;

  /// The date of the day
  final DateTime date;

  /// The day number in the itinerary
  final int dayNumber;

  /// The items for this day
  final List<ItineraryItem> items;

  /// Creates a new itinerary day
  ItineraryDay({
    String? id,
    required this.date,
    required this.dayNumber,
    required this.items,
  }) : id = id ?? const Uuid().v4();

  /// Get the total price of all items
  double get totalPrice {
    return items.fold(0, (sum, item) => sum + (item.price ?? 0));
  }

  /// Add an item to this day
  ItineraryDay addItem(ItineraryItem item) {
    final updatedItems = List<ItineraryItem>.from(items);
    updatedItems.add(item);
    return copyWith(items: updatedItems);
  }

  /// Remove an item from this day
  ItineraryDay removeItem(String itemId) {
    final updatedItems = items.where((item) => item.id != itemId).toList();
    return copyWith(items: updatedItems);
  }

  /// Update an item in this day
  ItineraryDay updateItem(ItineraryItem updatedItem) {
    final itemIndex = items.indexWhere((item) => item.id == updatedItem.id);
    if (itemIndex == -1) {
      return this;
    }

    final updatedItems = List<ItineraryItem>.from(items);
    updatedItems[itemIndex] = updatedItem;
    return copyWith(items: updatedItems);
  }

  /// Create a copy of this day with the given fields replaced
  ItineraryDay copyWith({
    String? id,
    DateTime? date,
    int? dayNumber,
    List<ItineraryItem>? items,
  }) {
    return ItineraryDay(
      id: id ?? this.id,
      date: date ?? this.date,
      dayNumber: dayNumber ?? this.dayNumber,
      items: items ?? this.items,
    );
  }

  /// Create an itinerary day from JSON
  factory ItineraryDay.fromJson(Map<String, dynamic> json) {
    return ItineraryDay(
      id: json['id'],
      date: DateTime.parse(json['date']),
      dayNumber: json['dayNumber'],
      items: (json['items'] as List?)
              ?.map((item) => ItineraryItem.fromJson(item))
              .toList() ??
          [],
    );
  }

  /// Convert this itinerary day to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'date': date.toIso8601String(),
      'dayNumber': dayNumber,
      'items': items.map((item) => item.toJson()).toList(),
    };
  }
}

/// An item in an itinerary day
class ItineraryItem {
  /// The unique identifier
  final String id;

  /// The title
  final String title;

  /// The description
  final String? description;

  /// The start time
  final TimeOfDay? startTime;

  /// The end time
  final TimeOfDay? endTime;

  /// The location
  final String? location;

  /// The coordinates of the location
  final Map<String, double>? coordinates;

  /// The price
  final double? price;

  /// The currency
  final String? currency;

  /// The type of the item
  final ItineraryItemType type;

  /// The status of the item
  final ItineraryItemStatus status;

  /// The service ID if this item is linked to a service
  final String? serviceId;

  /// The service type if this item is linked to a service
  final TravelServiceType? serviceType;

  /// The booking ID if this item is linked to a booking
  final String? bookingId;

  /// The booking reference if this item is linked to a booking
  final String? bookingReference;

  /// Creates a new itinerary item
  ItineraryItem({
    String? id,
    required this.title,
    this.description,
    this.startTime,
    this.endTime,
    this.location,
    this.coordinates,
    this.price,
    this.currency,
    this.type = ItineraryItemType.activity,
    this.status = ItineraryItemStatus.planned,
    this.serviceId,
    this.serviceType,
    this.bookingId,
    this.bookingReference,
  }) : id = id ?? const Uuid().v4();

  /// Create a copy of this item with the given fields replaced
  ItineraryItem copyWith({
    String? id,
    String? title,
    String? description,
    TimeOfDay? startTime,
    TimeOfDay? endTime,
    String? location,
    Map<String, double>? coordinates,
    double? price,
    String? currency,
    ItineraryItemType? type,
    ItineraryItemStatus? status,
    String? serviceId,
    TravelServiceType? serviceType,
    String? bookingId,
    String? bookingReference,
  }) {
    return ItineraryItem(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      startTime: startTime ?? this.startTime,
      endTime: endTime ?? this.endTime,
      location: location ?? this.location,
      coordinates: coordinates ?? this.coordinates,
      price: price ?? this.price,
      currency: currency ?? this.currency,
      type: type ?? this.type,
      status: status ?? this.status,
      serviceId: serviceId ?? this.serviceId,
      serviceType: serviceType ?? this.serviceType,
      bookingId: bookingId ?? this.bookingId,
      bookingReference: bookingReference ?? this.bookingReference,
    );
  }

  /// Create an itinerary item from JSON
  factory ItineraryItem.fromJson(Map<String, dynamic> json) {
    return ItineraryItem(
      id: json['id'],
      title: json['title'],
      description: json['description'],
      startTime: json['startTime'] != null
          ? TimeOfDay(
              hour: int.parse(json['startTime'].split(':')[0]),
              minute: int.parse(json['startTime'].split(':')[1]),
            )
          : null,
      endTime: json['endTime'] != null
          ? TimeOfDay(
              hour: int.parse(json['endTime'].split(':')[0]),
              minute: int.parse(json['endTime'].split(':')[1]),
            )
          : null,
      location: json['location'],
      coordinates: json['coordinates'] != null
          ? Map<String, double>.from(json['coordinates'])
          : null,
      price: json['price'],
      currency: json['currency'],
      type: ItineraryItemType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => ItineraryItemType.activity,
      ),
      status: ItineraryItemStatus.values.firstWhere(
        (e) => e.name == json['status'],
        orElse: () => ItineraryItemStatus.planned,
      ),
      serviceId: json['serviceId'],
      serviceType: json['serviceType'] != null
          ? TravelServiceType.values.firstWhere(
              (e) => e.name == json['serviceType'],
              orElse: () => TravelServiceType.other,
            )
          : null,
      bookingId: json['bookingId'],
      bookingReference: json['bookingReference'],
    );
  }

  /// Convert this itinerary item to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'startTime': startTime != null
          ? '${startTime!.hour.toString().padLeft(2, '0')}:${startTime!.minute.toString().padLeft(2, '0')}'
          : null,
      'endTime': endTime != null
          ? '${endTime!.hour.toString().padLeft(2, '0')}:${endTime!.minute.toString().padLeft(2, '0')}'
          : null,
      'location': location,
      'coordinates': coordinates,
      'price': price,
      'currency': currency,
      'type': type.name,
      'status': status.name,
      'serviceId': serviceId,
      'serviceType': serviceType?.name,
      'bookingId': bookingId,
      'bookingReference': bookingReference,
    };
  }
}

/// Type of an itinerary item
enum ItineraryItemType {
  /// Activity
  activity,

  /// Transportation
  transportation,

  /// Accommodation
  accommodation,

  /// Food
  food,

  /// Custom
  custom,

  /// Other
  other,
}

/// Status of an itinerary item
enum ItineraryItemStatus {
  /// Planned
  planned,

  /// Confirmed
  confirmed,

  /// Booked
  booked,

  /// In progress
  inProgress,

  /// Completed
  completed,

  /// Cancelled
  cancelled,
}
