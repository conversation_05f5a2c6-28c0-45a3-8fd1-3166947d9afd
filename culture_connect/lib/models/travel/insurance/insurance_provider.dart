import 'package:flutter/material.dart';

/// A model representing an insurance provider
class InsuranceProvider {
  /// Unique identifier for the provider
  final String id;

  /// Name of the provider
  final String name;

  /// Description of the provider
  final String description;

  /// Logo URL of the provider
  final String logoUrl;

  /// Website URL of the provider
  final String websiteUrl;

  /// Contact phone number of the provider
  final String phoneNumber;

  /// Contact email of the provider
  final String email;

  /// Rating of the provider (0-5)
  final double rating;

  /// Number of reviews
  final int reviewCount;

  /// Whether the provider is featured
  final bool isFeatured;

  /// Whether the provider is a partner
  final bool isPartner;

  /// Countries where the provider operates
  final List<String> countries;

  /// Additional details about the provider
  final Map<String, dynamic> details;

  /// Creates a new insurance provider
  const InsuranceProvider({
    required this.id,
    required this.name,
    required this.description,
    required this.logoUrl,
    required this.websiteUrl,
    required this.phoneNumber,
    required this.email,
    required this.rating,
    required this.reviewCount,
    required this.isFeatured,
    required this.isPartner,
    required this.countries,
    this.details = const {},
  });

  /// Get the primary color for the provider
  Color get primaryColor {
    // This would ideally be based on the provider's brand colors
    // For now, we'll use a hash-based approach to generate a color
    final hash = name.hashCode;
    return Color.fromARGB(
      255,
      (hash & 0xFF0000) >> 16,
      (hash & 0x00FF00) >> 8,
      hash & 0x0000FF,
    );
  }

  /// Creates a copy with some fields replaced
  InsuranceProvider copyWith({
    String? id,
    String? name,
    String? description,
    String? logoUrl,
    String? websiteUrl,
    String? phoneNumber,
    String? email,
    double? rating,
    int? reviewCount,
    bool? isFeatured,
    bool? isPartner,
    List<String>? countries,
    Map<String, dynamic>? details,
  }) {
    return InsuranceProvider(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      logoUrl: logoUrl ?? this.logoUrl,
      websiteUrl: websiteUrl ?? this.websiteUrl,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      email: email ?? this.email,
      rating: rating ?? this.rating,
      reviewCount: reviewCount ?? this.reviewCount,
      isFeatured: isFeatured ?? this.isFeatured,
      isPartner: isPartner ?? this.isPartner,
      countries: countries ?? this.countries,
      details: details ?? this.details,
    );
  }

  /// Creates from JSON
  factory InsuranceProvider.fromJson(Map<String, dynamic> json) {
    return InsuranceProvider(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String,
      logoUrl: json['logoUrl'] as String,
      websiteUrl: json['websiteUrl'] as String,
      phoneNumber: json['phoneNumber'] as String,
      email: json['email'] as String,
      rating: json['rating'] as double,
      reviewCount: json['reviewCount'] as int,
      isFeatured: json['isFeatured'] as bool,
      isPartner: json['isPartner'] as bool,
      countries: (json['countries'] as List<dynamic>).cast<String>(),
      details: json['details'] as Map<String, dynamic>? ?? {},
    );
  }

  /// Converts to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'logoUrl': logoUrl,
      'websiteUrl': websiteUrl,
      'phoneNumber': phoneNumber,
      'email': email,
      'rating': rating,
      'reviewCount': reviewCount,
      'isFeatured': isFeatured,
      'isPartner': isPartner,
      'countries': countries,
      'details': details,
    };
  }
}
