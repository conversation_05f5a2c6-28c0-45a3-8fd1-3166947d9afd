import 'package:flutter/material.dart';

/// Represents the status of an insurance claim
enum InsuranceClaimStatus {
  /// Claim has been submitted but not yet reviewed
  submitted(
    displayName: 'Submitted',
    description: 'Claim has been submitted but not yet reviewed',
    color: Colors.blue,
    icon: Icons.send,
  ),

  /// Claim is under review by the insurance provider
  inReview(
    displayName: 'In Review',
    description: '<PERSON>laim is under review by the insurance provider',
    color: Colors.orange,
    icon: Icons.search,
  ),

  /// Additional information has been requested for the claim
  infoRequested(
    displayName: 'Information Requested',
    description: 'Additional information has been requested for the claim',
    color: Colors.amber,
    icon: Icons.info_outline,
  ),

  /// Claim has been approved and payment is being processed
  approved(
    displayName: 'Approved',
    description: 'Claim has been approved and payment is being processed',
    color: Colors.green,
    icon: Icons.check_circle,
  ),

  /// Claim has been paid
  paid(
    displayName: 'Paid',
    description: 'Claim has been paid',
    color: Colors.green,
    icon: Icons.payments,
  ),

  /// <PERSON>laim has been partially paid
  partiallyPaid(
    displayName: 'Partially Paid',
    description: 'Claim has been partially paid',
    color: Colors.lightGreen,
    icon: Icons.payments,
  ),

  /// Claim has been denied
  denied(
    displayName: 'Denied',
    description: 'Claim has been denied',
    color: Colors.red,
    icon: Icons.cancel,
  ),

  /// Claim has been appealed
  appealed(
    displayName: 'Appealed',
    description: 'Claim has been appealed',
    color: Colors.purple,
    icon: Icons.gavel,
  ),

  /// Claim has been closed
  closed(
    displayName: 'Closed',
    description: 'Claim has been closed',
    color: Colors.grey,
    icon: Icons.archive,
  );

  /// Creates a new insurance claim status
  const InsuranceClaimStatus({
    required this.displayName,
    required this.description,
    required this.color,
    required this.icon,
  });

  /// The display name of the status
  final String displayName;

  /// A description of the status
  final String description;

  /// The color associated with the status
  final Color color;

  /// An icon representing the status
  final IconData icon;

  /// Get a status from its string representation
  static InsuranceClaimStatus fromString(String value) {
    return InsuranceClaimStatus.values.firstWhere(
      (status) => status.toString().split('.').last == value,
      orElse: () => InsuranceClaimStatus.submitted,
    );
  }

  /// Convert the status to a string
  String toJson() => toString().split('.').last;

  /// Create a status from JSON
  static InsuranceClaimStatus fromJson(String json) => fromString(json);
}
