import 'package:flutter/material.dart';
import 'package:uuid/uuid.dart';
import 'package:culture_connect/models/travel/travel_service_base.dart';

/// Types of insurance coverage
enum InsuranceCoverageType {
  /// Medical coverage
  medical,

  /// Trip cancellation coverage
  tripCancellation,

  /// Trip interruption coverage
  tripInterruption,

  /// Baggage loss coverage
  baggageLoss,

  /// Flight delay coverage
  flightDelay,

  /// Emergency evacuation coverage
  emergencyEvacuation,

  /// Accidental death coverage
  accidentalDeath,

  /// Rental car damage coverage
  rentalCarDamage,

  /// Adventure activities coverage
  adventureActivities,
}

/// Extension for insurance coverage types
extension InsuranceCoverageTypeExtension on InsuranceCoverageType {
  /// Get the display name for the coverage type
  String get displayName {
    switch (this) {
      case InsuranceCoverageType.medical:
        return 'Medical Coverage';
      case InsuranceCoverageType.tripCancellation:
        return 'Trip Cancellation';
      case InsuranceCoverageType.tripInterruption:
        return 'Trip Interruption';
      case InsuranceCoverageType.baggageLoss:
        return 'Baggage Loss';
      case InsuranceCoverageType.flightDelay:
        return 'Flight Delay';
      case InsuranceCoverageType.emergencyEvacuation:
        return 'Emergency Evacuation';
      case InsuranceCoverageType.accidentalDeath:
        return 'Accidental Death';
      case InsuranceCoverageType.rentalCarDamage:
        return 'Rental Car Damage';
      case InsuranceCoverageType.adventureActivities:
        return 'Adventure Activities';
    }
  }

  /// Get the icon for the coverage type
  IconData get icon {
    switch (this) {
      case InsuranceCoverageType.medical:
        return Icons.medical_services;
      case InsuranceCoverageType.tripCancellation:
        return Icons.cancel;
      case InsuranceCoverageType.tripInterruption:
        return Icons.block;
      case InsuranceCoverageType.baggageLoss:
        return Icons.luggage;
      case InsuranceCoverageType.flightDelay:
        return Icons.flight_land;
      case InsuranceCoverageType.emergencyEvacuation:
        return Icons.emergency;
      case InsuranceCoverageType.accidentalDeath:
        return Icons.dangerous;
      case InsuranceCoverageType.rentalCarDamage:
        return Icons.car_crash;
      case InsuranceCoverageType.adventureActivities:
        return Icons.hiking;
    }
  }

  /// Get the description for the coverage type
  String get description {
    switch (this) {
      case InsuranceCoverageType.medical:
        return 'Covers medical expenses incurred during your trip, including hospital stays, doctor visits, and prescription medications.';
      case InsuranceCoverageType.tripCancellation:
        return 'Reimburses prepaid, non-refundable trip costs if you need to cancel your trip for a covered reason.';
      case InsuranceCoverageType.tripInterruption:
        return 'Covers the unused portion of your trip and additional transportation costs if your trip is interrupted for a covered reason.';
      case InsuranceCoverageType.baggageLoss:
        return 'Reimburses you for lost, stolen, or damaged baggage and personal items during your trip.';
      case InsuranceCoverageType.flightDelay:
        return 'Provides compensation for additional expenses due to a significant flight delay.';
      case InsuranceCoverageType.emergencyEvacuation:
        return 'Covers the cost of emergency medical evacuation to the nearest adequate medical facility.';
      case InsuranceCoverageType.accidentalDeath:
        return 'Provides a benefit to your beneficiaries if you die in an accident during your trip.';
      case InsuranceCoverageType.rentalCarDamage:
        return 'Covers damage to a rental car during your trip, potentially replacing the need for rental car insurance.';
      case InsuranceCoverageType.adventureActivities:
        return 'Extends coverage to include adventure activities that might be excluded from standard policies.';
    }
  }
}

/// Status of an insurance policy
enum InsurancePolicyStatus {
  /// Active policy
  active,

  /// Expired policy
  expired,

  /// Cancelled policy
  cancelled,

  /// Pending policy
  pending,
}

/// Extension for insurance policy status
extension InsurancePolicyStatusExtension on InsurancePolicyStatus {
  /// Get the display name for the policy status
  String get displayName {
    switch (this) {
      case InsurancePolicyStatus.active:
        return 'Active';
      case InsurancePolicyStatus.expired:
        return 'Expired';
      case InsurancePolicyStatus.cancelled:
        return 'Cancelled';
      case InsurancePolicyStatus.pending:
        return 'Pending';
    }
  }

  /// Get the color for the policy status
  Color get color {
    switch (this) {
      case InsurancePolicyStatus.active:
        return Colors.green;
      case InsurancePolicyStatus.expired:
        return Colors.grey;
      case InsurancePolicyStatus.cancelled:
        return Colors.red;
      case InsurancePolicyStatus.pending:
        return Colors.orange;
    }
  }
}

/// Status of an insurance claim
enum InsuranceClaimStatus {
  /// Submitted claim
  submitted,

  /// Under review claim
  underReview,

  /// Additional information requested
  infoRequested,

  /// Approved claim
  approved,

  /// Partially approved claim
  partiallyApproved,

  /// Denied claim
  denied,

  /// Paid claim
  paid,
}

/// Extension for insurance claim status
extension InsuranceClaimStatusExtension on InsuranceClaimStatus {
  /// Get the display name for the claim status
  String get displayName {
    switch (this) {
      case InsuranceClaimStatus.submitted:
        return 'Submitted';
      case InsuranceClaimStatus.underReview:
        return 'Under Review';
      case InsuranceClaimStatus.infoRequested:
        return 'Information Requested';
      case InsuranceClaimStatus.approved:
        return 'Approved';
      case InsuranceClaimStatus.partiallyApproved:
        return 'Partially Approved';
      case InsuranceClaimStatus.denied:
        return 'Denied';
      case InsuranceClaimStatus.paid:
        return 'Paid';
    }
  }

  /// Get the color for the claim status
  Color get color {
    switch (this) {
      case InsuranceClaimStatus.submitted:
        return Colors.blue;
      case InsuranceClaimStatus.underReview:
        return Colors.orange;
      case InsuranceClaimStatus.infoRequested:
        return Colors.amber;
      case InsuranceClaimStatus.approved:
        return Colors.green;
      case InsuranceClaimStatus.partiallyApproved:
        return Colors.lightGreen;
      case InsuranceClaimStatus.denied:
        return Colors.red;
      case InsuranceClaimStatus.paid:
        return Colors.teal;
    }
  }

  /// Get the icon for the claim status
  IconData get icon {
    switch (this) {
      case InsuranceClaimStatus.submitted:
        return Icons.upload_file;
      case InsuranceClaimStatus.underReview:
        return Icons.search;
      case InsuranceClaimStatus.infoRequested:
        return Icons.info;
      case InsuranceClaimStatus.approved:
        return Icons.check_circle;
      case InsuranceClaimStatus.partiallyApproved:
        return Icons.check_circle_outline;
      case InsuranceClaimStatus.denied:
        return Icons.cancel;
      case InsuranceClaimStatus.paid:
        return Icons.payments;
    }
  }
}

/// Type of insurance policy
enum InsurancePolicyType {
  /// Single trip policy
  singleTrip,

  /// Annual multi-trip policy
  annualMultiTrip,

  /// Backpacker policy
  backpacker,

  /// Business travel policy
  business,

  /// Family policy
  family,

  /// Senior policy
  senior,

  /// Adventure policy
  adventure,

  /// Cruise policy
  cruise,
}

/// Extension for insurance policy types
extension InsurancePolicyTypeExtension on InsurancePolicyType {
  /// Get the display name for the policy type
  String get displayName {
    switch (this) {
      case InsurancePolicyType.singleTrip:
        return 'Single Trip';
      case InsurancePolicyType.annualMultiTrip:
        return 'Annual Multi-Trip';
      case InsurancePolicyType.backpacker:
        return 'Backpacker';
      case InsurancePolicyType.business:
        return 'Business Travel';
      case InsurancePolicyType.family:
        return 'Family';
      case InsurancePolicyType.senior:
        return 'Senior';
      case InsurancePolicyType.adventure:
        return 'Adventure';
      case InsurancePolicyType.cruise:
        return 'Cruise';
    }
  }

  /// Get the icon for the policy type
  IconData get icon {
    switch (this) {
      case InsurancePolicyType.singleTrip:
        return Icons.flight;
      case InsurancePolicyType.annualMultiTrip:
        return Icons.calendar_month;
      case InsurancePolicyType.backpacker:
        return Icons.backpack;
      case InsurancePolicyType.business:
        return Icons.business_center;
      case InsurancePolicyType.family:
        return Icons.family_restroom;
      case InsurancePolicyType.senior:
        return Icons.elderly;
      case InsurancePolicyType.adventure:
        return Icons.terrain;
      case InsurancePolicyType.cruise:
        return Icons.directions_boat;
    }
  }

  /// Get the description for the policy type
  String get description {
    switch (this) {
      case InsurancePolicyType.singleTrip:
        return 'Coverage for a single trip from start to finish.';
      case InsurancePolicyType.annualMultiTrip:
        return 'Coverage for multiple trips within a year.';
      case InsurancePolicyType.backpacker:
        return 'Extended coverage for long-term budget travelers.';
      case InsurancePolicyType.business:
        return 'Specialized coverage for business travelers.';
      case InsurancePolicyType.family:
        return 'Coverage for families traveling together.';
      case InsurancePolicyType.senior:
        return 'Specialized coverage for travelers over 65.';
      case InsurancePolicyType.adventure:
        return 'Coverage for adventure activities and sports.';
      case InsurancePolicyType.cruise:
        return 'Specialized coverage for cruise vacations.';
    }
  }
}
