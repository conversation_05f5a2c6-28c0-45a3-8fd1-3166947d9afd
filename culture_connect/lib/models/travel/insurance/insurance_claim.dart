import 'package:flutter/material.dart';
import 'package:culture_connect/models/travel/insurance/insurance_coverage_type.dart';
import 'package:culture_connect/models/travel/insurance/insurance_claim_status.dart';
import 'package:culture_connect/models/travel/insurance/insurance_policy.dart';

/// A model representing an insurance claim
class InsuranceClaim {
  /// Unique identifier for the claim
  final String id;

  /// Policy associated with the claim
  final InsurancePolicy policy;

  /// Type of coverage being claimed
  final InsuranceCoverageType coverageType;

  /// Status of the claim
  final InsuranceClaimStatus status;

  /// Date of the incident
  final DateTime incidentDate;

  /// Description of the incident
  final String incidentDescription;

  /// Location of the incident
  final String incidentLocation;

  /// Amount being claimed
  final double claimAmount;

  /// Currency of the claim amount
  final String currency;

  /// Amount approved for payment
  final double? approvedAmount;

  /// Date the claim was submitted
  final DateTime submittedDate;

  /// Date the claim was last updated
  final DateTime lastUpdatedDate;

  /// Date the claim was resolved
  final DateTime? resolvedDate;

  /// List of document URLs supporting the claim
  final List<String> documentUrls;

  /// Additional information requested
  final String? additionalInfoRequested;

  /// Reason for denial if claim was denied
  final String? denialReason;

  /// Reference number for the claim
  final String referenceNumber;

  /// Additional details about the claim
  final Map<String, dynamic> details;

  /// Creates a new insurance claim
  const InsuranceClaim({
    required this.id,
    required this.policy,
    required this.coverageType,
    required this.status,
    required this.incidentDate,
    required this.incidentDescription,
    required this.incidentLocation,
    required this.claimAmount,
    required this.currency,
    this.approvedAmount,
    required this.submittedDate,
    required this.lastUpdatedDate,
    this.resolvedDate,
    required this.documentUrls,
    this.additionalInfoRequested,
    this.denialReason,
    required this.referenceNumber,
    this.details = const {},
  });

  /// Get the formatted claim amount
  String get formattedClaimAmount {
    return '$currency${claimAmount.toStringAsFixed(2)}';
  }

  /// Get the formatted approved amount
  String? get formattedApprovedAmount {
    if (approvedAmount == null) return null;
    return '$currency${approvedAmount!.toStringAsFixed(2)}';
  }

  /// Get the days since submission
  int get daysSinceSubmission {
    return DateTime.now().difference(submittedDate).inDays;
  }

  /// Get the formatted days since submission
  String get formattedDaysSinceSubmission {
    final days = daysSinceSubmission;
    if (days == 0) return 'Today';
    if (days == 1) return 'Yesterday';
    return '$days days ago';
  }

  /// Get the processing time in days
  int? get processingTimeDays {
    if (resolvedDate == null) return null;
    return resolvedDate!.difference(submittedDate).inDays;
  }

  /// Get the formatted processing time
  String? get formattedProcessingTime {
    final days = processingTimeDays;
    if (days == null) return null;
    return '$days ${days == 1 ? 'day' : 'days'}';
  }

  /// Check if the claim is resolved
  bool get isResolved {
    return status == InsuranceClaimStatus.approved ||
        status == InsuranceClaimStatus.partiallyPaid ||
        status == InsuranceClaimStatus.denied ||
        status == InsuranceClaimStatus.paid;
  }

  /// Check if the claim is successful
  bool get isSuccessful {
    return status == InsuranceClaimStatus.approved ||
        status == InsuranceClaimStatus.partiallyPaid ||
        status == InsuranceClaimStatus.paid;
  }

  /// Get the icon for the claim
  IconData get icon {
    return status.icon;
  }

  /// Get the color for the claim
  Color get color {
    return status.color;
  }

  /// Creates a copy with some fields replaced
  InsuranceClaim copyWith({
    String? id,
    InsurancePolicy? policy,
    InsuranceCoverageType? coverageType,
    InsuranceClaimStatus? status,
    DateTime? incidentDate,
    String? incidentDescription,
    String? incidentLocation,
    double? claimAmount,
    String? currency,
    double? approvedAmount,
    DateTime? submittedDate,
    DateTime? lastUpdatedDate,
    DateTime? resolvedDate,
    List<String>? documentUrls,
    String? additionalInfoRequested,
    String? denialReason,
    String? referenceNumber,
    Map<String, dynamic>? details,
  }) {
    return InsuranceClaim(
      id: id ?? this.id,
      policy: policy ?? this.policy,
      coverageType: coverageType ?? this.coverageType,
      status: status ?? this.status,
      incidentDate: incidentDate ?? this.incidentDate,
      incidentDescription: incidentDescription ?? this.incidentDescription,
      incidentLocation: incidentLocation ?? this.incidentLocation,
      claimAmount: claimAmount ?? this.claimAmount,
      currency: currency ?? this.currency,
      approvedAmount: approvedAmount ?? this.approvedAmount,
      submittedDate: submittedDate ?? this.submittedDate,
      lastUpdatedDate: lastUpdatedDate ?? this.lastUpdatedDate,
      resolvedDate: resolvedDate ?? this.resolvedDate,
      documentUrls: documentUrls ?? this.documentUrls,
      additionalInfoRequested:
          additionalInfoRequested ?? this.additionalInfoRequested,
      denialReason: denialReason ?? this.denialReason,
      referenceNumber: referenceNumber ?? this.referenceNumber,
      details: details ?? this.details,
    );
  }

  /// Creates from JSON
  factory InsuranceClaim.fromJson(Map<String, dynamic> json) {
    return InsuranceClaim(
      id: json['id'] as String,
      policy: InsurancePolicy.fromJson(json['policy'] as Map<String, dynamic>),
      coverageType:
          InsuranceCoverageType.fromString(json['coverageType'] as String),
      status: InsuranceClaimStatus.fromString(json['status'] as String),
      incidentDate: DateTime.parse(json['incidentDate'] as String),
      incidentDescription: json['incidentDescription'] as String,
      incidentLocation: json['incidentLocation'] as String,
      claimAmount: json['claimAmount'] as double,
      currency: json['currency'] as String,
      approvedAmount: json['approvedAmount'] as double?,
      submittedDate: DateTime.parse(json['submittedDate'] as String),
      lastUpdatedDate: DateTime.parse(json['lastUpdatedDate'] as String),
      resolvedDate: json['resolvedDate'] != null
          ? DateTime.parse(json['resolvedDate'] as String)
          : null,
      documentUrls: (json['documentUrls'] as List<dynamic>).cast<String>(),
      additionalInfoRequested: json['additionalInfoRequested'] as String?,
      denialReason: json['denialReason'] as String?,
      referenceNumber: json['referenceNumber'] as String,
      details: json['details'] as Map<String, dynamic>? ?? {},
    );
  }

  /// Converts to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'policy': policy.toJson(),
      'coverageType': coverageType.toJson(),
      'status': status.toJson(),
      'incidentDate': incidentDate.toIso8601String(),
      'incidentDescription': incidentDescription,
      'incidentLocation': incidentLocation,
      'claimAmount': claimAmount,
      'currency': currency,
      'approvedAmount': approvedAmount,
      'submittedDate': submittedDate.toIso8601String(),
      'lastUpdatedDate': lastUpdatedDate.toIso8601String(),
      'resolvedDate': resolvedDate?.toIso8601String(),
      'documentUrls': documentUrls,
      'additionalInfoRequested': additionalInfoRequested,
      'denialReason': denialReason,
      'referenceNumber': referenceNumber,
      'details': details,
    };
  }
}
