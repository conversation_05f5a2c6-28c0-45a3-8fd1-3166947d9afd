import 'package:flutter/material.dart';
import 'package:uuid/uuid.dart';
import 'package:culture_connect/models/travel/itinerary.dart';
import 'package:culture_connect/models/travel/timeline_event.dart';

/// Visibility of a timeline
enum TimelineVisibility {
  /// Private timeline (only visible to the owner)
  private,

  /// Shared timeline (visible to specific users)
  shared,

  /// Public timeline (visible to anyone)
  public,
}

/// Extension for timeline visibility
extension TimelineVisibilityExtension on TimelineVisibility {
  /// Get the display name for the timeline visibility
  String get displayName {
    switch (this) {
      case TimelineVisibility.private:
        return 'Private';
      case TimelineVisibility.shared:
        return 'Shared';
      case TimelineVisibility.public:
        return 'Public';
    }
  }

  /// Get the icon for the timeline visibility
  IconData get icon {
    switch (this) {
      case TimelineVisibility.private:
        return Icons.lock;
      case TimelineVisibility.shared:
        return Icons.group;
      case TimelineVisibility.public:
        return Icons.public;
    }
  }
}

/// Theme for a timeline
enum TimelineTheme {
  /// Default theme
  standard,

  /// Travel theme
  travel,

  /// Adventure theme
  adventure,

  /// Luxury theme
  luxury,

  /// Business theme
  business,

  /// Family theme
  family,
}

/// Extension for timeline theme
extension TimelineThemeExtension on TimelineTheme {
  /// Get the display name for the timeline theme
  String get displayName {
    switch (this) {
      case TimelineTheme.standard:
        return 'Standard';
      case TimelineTheme.travel:
        return 'Travel';
      case TimelineTheme.adventure:
        return 'Adventure';
      case TimelineTheme.luxury:
        return 'Luxury';
      case TimelineTheme.business:
        return 'Business';
      case TimelineTheme.family:
        return 'Family';
    }
  }

  /// Get the primary color for the timeline theme
  Color get primaryColor {
    switch (this) {
      case TimelineTheme.standard:
        return Colors.blue;
      case TimelineTheme.travel:
        return Colors.teal;
      case TimelineTheme.adventure:
        return Colors.orange;
      case TimelineTheme.luxury:
        return Colors.purple;
      case TimelineTheme.business:
        return Colors.indigo;
      case TimelineTheme.family:
        return Colors.green;
    }
  }

  /// Get the secondary color for the timeline theme
  Color get secondaryColor {
    switch (this) {
      case TimelineTheme.standard:
        return Colors.lightBlue;
      case TimelineTheme.travel:
        return Colors.tealAccent;
      case TimelineTheme.adventure:
        return Colors.amber;
      case TimelineTheme.luxury:
        return Colors.deepPurple;
      case TimelineTheme.business:
        return Colors.blue;
      case TimelineTheme.family:
        return Colors.lightGreen;
    }
  }

  /// Get the background color for the timeline theme
  Color get backgroundColor {
    switch (this) {
      case TimelineTheme.standard:
        return Colors.grey.shade100;
      case TimelineTheme.travel:
        return Colors.teal.shade50;
      case TimelineTheme.adventure:
        return Colors.orange.shade50;
      case TimelineTheme.luxury:
        return Colors.purple.shade50;
      case TimelineTheme.business:
        return Colors.indigo.shade50;
      case TimelineTheme.family:
        return Colors.green.shade50;
    }
  }
}

/// A visual representation of an itinerary
class Timeline {
  /// The unique identifier
  final String id;

  /// The user ID
  final String userId;

  /// The title
  final String title;

  /// The description
  final String? description;

  /// The start date
  final DateTime startDate;

  /// The end date
  final DateTime endDate;

  /// The itinerary ID (if linked to an itinerary)
  final String? itineraryId;

  /// The events in the timeline
  final List<TimelineEvent> events;

  /// The theme
  final TimelineTheme theme;

  /// The visibility
  final TimelineVisibility visibility;

  /// The share URL (if shared)
  final String? shareUrl;

  /// The created at timestamp
  final DateTime createdAt;

  /// The updated at timestamp
  final DateTime updatedAt;

  /// Creates a new timeline
  Timeline({
    String? id,
    required this.userId,
    required this.title,
    this.description,
    required this.startDate,
    required this.endDate,
    this.itineraryId,
    List<TimelineEvent>? events,
    this.theme = TimelineTheme.standard,
    this.visibility = TimelineVisibility.private,
    this.shareUrl,
    DateTime? createdAt,
    DateTime? updatedAt,
  })  : id = id ?? const Uuid().v4(),
        events = events ?? [],
        createdAt = createdAt ?? DateTime.now(),
        updatedAt = updatedAt ?? DateTime.now();

  /// Get the duration in days
  int get durationInDays => endDate.difference(startDate).inDays + 1;

  /// Get events for a specific date
  List<TimelineEvent> getEventsForDate(DateTime date) {
    final dateString =
        '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
    return events.where((event) {
      final eventDateString =
          '${event.eventDate.year}-${event.eventDate.month.toString().padLeft(2, '0')}-${event.eventDate.day.toString().padLeft(2, '0')}';
      return eventDateString == dateString;
    }).toList();
  }

  /// Get events with AR content
  List<TimelineEvent> get eventsWithARContent {
    return events.where((event) => event.hasARContent).toList();
  }

  /// Create a copy of this timeline with the given fields replaced
  Timeline copyWith({
    String? id,
    String? userId,
    String? title,
    String? description,
    DateTime? startDate,
    DateTime? endDate,
    String? itineraryId,
    List<TimelineEvent>? events,
    TimelineTheme? theme,
    TimelineVisibility? visibility,
    String? shareUrl,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Timeline(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      title: title ?? this.title,
      description: description ?? this.description,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      itineraryId: itineraryId ?? this.itineraryId,
      events: events ?? this.events,
      theme: theme ?? this.theme,
      visibility: visibility ?? this.visibility,
      shareUrl: shareUrl ?? this.shareUrl,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? DateTime.now(),
    );
  }

  /// Add an event to this timeline
  Timeline addEvent(TimelineEvent event) {
    final newEvents = List<TimelineEvent>.from(events)..add(event);
    return copyWith(events: newEvents);
  }

  /// Update an event in this timeline
  Timeline updateEvent(TimelineEvent event) {
    final index = events.indexWhere((e) => e.id == event.id);
    if (index == -1) {
      return this;
    }

    final newEvents = List<TimelineEvent>.from(events);
    newEvents[index] = event;
    return copyWith(events: newEvents);
  }

  /// Remove an event from this timeline
  Timeline removeEvent(String eventId) {
    final newEvents = events.where((e) => e.id != eventId).toList();
    return copyWith(events: newEvents);
  }

  /// Create a timeline from an itinerary
  factory Timeline.fromItinerary(Itinerary itinerary) {
    final events = <TimelineEvent>[];

    // Add events for each day
    for (final day in itinerary.days) {
      // Add events for each item
      for (final item in day.items) {
        events.add(TimelineEvent(
          title: item.title,
          description: item.description,
          eventDate: day.date,
          eventTime: item.startTime,
          endTime: item.endTime,
          location: item.location,
          coordinates: item.coordinates != null
              ? {
                  'lat': item.coordinates!['lat']!,
                  'lng': item.coordinates!['lng']!
                }
              : null,
          eventType: _mapItemTypeToEventType(item.type),
          serviceId: item.serviceId,
          serviceType: item.serviceType?.name,
          bookingId: item.bookingId,
          bookingReference: item.bookingReference,
          status: _mapItemStatusToEventStatus(item.status),
          hasARContent: false, // Default to false, will be updated later
        ));
      }
    }

    return Timeline(
      userId: itinerary.userId,
      title: itinerary.title,
      description: 'Timeline for ${itinerary.title}',
      startDate: itinerary.startDate,
      endDate: itinerary.endDate,
      itineraryId: itinerary.id,
      events: events,
    );
  }

  /// Map itinerary item type to timeline event type
  static String _mapItemTypeToEventType(ItineraryItemType type) {
    switch (type) {
      case ItineraryItemType.accommodation:
        return 'accommodation';
      case ItineraryItemType.transportation:
        return 'transportation';
      case ItineraryItemType.activity:
        return 'activity';
      case ItineraryItemType.food:
        return 'food';
      case ItineraryItemType.custom:
        return 'custom';
      case ItineraryItemType.other:
        return 'other';
    }
  }

  /// Map itinerary item status to timeline event status
  static String _mapItemStatusToEventStatus(ItineraryItemStatus status) {
    switch (status) {
      case ItineraryItemStatus.planned:
        return 'upcoming';
      case ItineraryItemStatus.confirmed:
        return 'confirmed';
      case ItineraryItemStatus.booked:
        return 'confirmed';
      case ItineraryItemStatus.inProgress:
        return 'in_progress';
      case ItineraryItemStatus.completed:
        return 'completed';
      case ItineraryItemStatus.cancelled:
        return 'cancelled';
    }
  }

  /// Create a timeline from JSON
  factory Timeline.fromJson(Map<String, dynamic> json) {
    return Timeline(
      id: json['id'],
      userId: json['userId'],
      title: json['title'],
      description: json['description'],
      startDate: DateTime.parse(json['startDate']),
      endDate: DateTime.parse(json['endDate']),
      itineraryId: json['itineraryId'],
      events: (json['events'] as List)
          .map((event) => TimelineEvent.fromJson(event))
          .toList(),
      theme: _parseTimelineTheme(json['theme']),
      visibility: _parseTimelineVisibility(json['visibility']),
      shareUrl: json['shareUrl'],
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: DateTime.parse(json['updatedAt']),
    );
  }

  /// Parse timeline theme from string
  static TimelineTheme _parseTimelineTheme(String? theme) {
    if (theme == null) return TimelineTheme.standard;

    switch (theme) {
      case 'standard':
        return TimelineTheme.standard;
      case 'travel':
        return TimelineTheme.travel;
      case 'adventure':
        return TimelineTheme.adventure;
      case 'luxury':
        return TimelineTheme.luxury;
      case 'business':
        return TimelineTheme.business;
      case 'family':
        return TimelineTheme.family;
      default:
        return TimelineTheme.standard;
    }
  }

  /// Parse timeline visibility from string
  static TimelineVisibility _parseTimelineVisibility(String? visibility) {
    if (visibility == null) return TimelineVisibility.private;

    switch (visibility) {
      case 'private':
        return TimelineVisibility.private;
      case 'shared':
        return TimelineVisibility.shared;
      case 'public':
        return TimelineVisibility.public;
      default:
        return TimelineVisibility.private;
    }
  }

  /// Convert this timeline to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userId': userId,
      'title': title,
      'description': description,
      'startDate': startDate.toIso8601String(),
      'endDate': endDate.toIso8601String(),
      'itineraryId': itineraryId,
      'events': events.map((event) => event.toJson()).toList(),
      'theme': theme.name,
      'visibility': visibility.name,
      'shareUrl': shareUrl,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }
}
