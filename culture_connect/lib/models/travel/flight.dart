import 'package:flutter/material.dart';
import 'travel_service_base.dart';

/// Enum for flight classes
enum FlightClass {
  economy,
  premiumEconomy,
  business,
  first,
}

/// Extension for flight classes
extension FlightClassExtension on FlightClass {
  /// Get the display name for the flight class
  String get displayName {
    switch (this) {
      case FlightClass.economy:
        return 'Economy';
      case FlightClass.premiumEconomy:
        return 'Premium Economy';
      case FlightClass.business:
        return 'Business';
      case FlightClass.first:
        return 'First Class';
    }
  }
}

/// Enum for flight types
enum FlightType {
  direct,
  oneStop,
  multiStop,
}

/// Extension for flight types
extension FlightTypeExtension on FlightType {
  /// Get the display name for the flight type
  String get displayName {
    switch (this) {
      case FlightType.direct:
        return 'Direct';
      case FlightType.oneStop:
        return 'One Stop';
      case FlightType.multiStop:
        return 'Multiple Stops';
    }
  }
}

/// A model representing a flight segment
class FlightSegment {
  /// Unique identifier for the flight segment
  final String id;

  /// Flight number
  final String flightNumber;

  /// Airline code
  final String airlineCode;

  /// Airline name
  final String airlineName;

  /// Airline logo URL
  final String airlineLogoUrl;

  /// Departure airport code
  final String departureAirportCode;

  /// Departure airport name
  final String departureAirportName;

  /// Departure city
  final String departureCity;

  /// Departure terminal
  final String? departureTerminal;

  /// Departure gate
  final String? departureGate;

  /// Departure time
  final DateTime departureTime;

  /// Arrival airport code
  final String arrivalAirportCode;

  /// Arrival airport name
  final String arrivalAirportName;

  /// Arrival city
  final String arrivalCity;

  /// Arrival terminal
  final String? arrivalTerminal;

  /// Arrival gate
  final String? arrivalGate;

  /// Arrival time
  final DateTime arrivalTime;

  /// Duration of the flight in minutes
  final int durationMinutes;

  /// Aircraft type
  final String aircraftType;

  /// Distance in kilometers
  final double distance;

  /// Whether the flight is on time
  final bool isOnTime;

  /// Delay in minutes (if applicable)
  final int? delayMinutes;

  /// Creates a new flight segment
  const FlightSegment({
    required this.id,
    required this.flightNumber,
    required this.airlineCode,
    required this.airlineName,
    required this.airlineLogoUrl,
    required this.departureAirportCode,
    required this.departureAirportName,
    required this.departureCity,
    this.departureTerminal,
    this.departureGate,
    required this.departureTime,
    required this.arrivalAirportCode,
    required this.arrivalAirportName,
    required this.arrivalCity,
    this.arrivalTerminal,
    this.arrivalGate,
    required this.arrivalTime,
    required this.durationMinutes,
    required this.aircraftType,
    required this.distance,
    required this.isOnTime,
    this.delayMinutes,
  });

  /// Get the formatted duration
  String get formattedDuration {
    final hours = durationMinutes ~/ 60;
    final minutes = durationMinutes % 60;
    return '${hours}h ${minutes}m';
  }

  /// Get the formatted departure time
  String get formattedDepartureTime {
    return '${departureTime.hour.toString().padLeft(2, '0')}:${departureTime.minute.toString().padLeft(2, '0')}';
  }

  /// Get the formatted arrival time
  String get formattedArrivalTime {
    return '${arrivalTime.hour.toString().padLeft(2, '0')}:${arrivalTime.minute.toString().padLeft(2, '0')}';
  }

  /// Get the formatted departure date
  String get formattedDepartureDate {
    return '${departureTime.day}/${departureTime.month}/${departureTime.year}';
  }

  /// Get the formatted arrival date
  String get formattedArrivalDate {
    return '${arrivalTime.day}/${arrivalTime.month}/${arrivalTime.year}';
  }

  /// Get the formatted distance
  String get formattedDistance {
    return '${distance.toStringAsFixed(0)} km';
  }

  /// Get the formatted status
  String get formattedStatus {
    if (isOnTime) {
      return 'On Time';
    } else if (delayMinutes != null) {
      return 'Delayed by ${delayMinutes}m';
    } else {
      return 'Status Unknown';
    }
  }
}

/// A model representing a layover
class Layover {
  /// Airport code
  final String airportCode;

  /// Airport name
  final String airportName;

  /// City
  final String city;

  /// Duration in minutes
  final int durationMinutes;

  /// Creates a new layover
  const Layover({
    required this.airportCode,
    required this.airportName,
    required this.city,
    required this.durationMinutes,
  });

  /// Get the formatted duration
  String get formattedDuration {
    final hours = durationMinutes ~/ 60;
    final minutes = durationMinutes % 60;
    return '${hours}h ${minutes}m';
  }
}

/// A model representing a flight
class Flight extends TravelService {
  /// Type of flight
  final FlightType flightType;

  /// Class of flight
  final FlightClass flightClass;

  /// Flight segments
  final List<FlightSegment> segments;

  /// Layovers
  final List<Layover> layovers;

  /// Total duration in minutes
  final int totalDurationMinutes;

  /// Departure airport code
  final String departureAirportCode;

  /// Departure city
  final String departureCity;

  /// Departure time
  final DateTime departureTime;

  /// Arrival airport code
  final String arrivalAirportCode;

  /// Arrival city
  final String arrivalCity;

  /// Arrival time
  final DateTime arrivalTime;

  /// Number of passengers
  final int passengerCount;

  /// Baggage allowance in kilograms
  final double baggageAllowance;

  /// Whether the flight is refundable
  final bool isRefundable;

  /// Whether the flight is changeable
  final bool isChangeable;

  /// Change fee (if applicable)
  final double? changeFee;

  /// Whether the flight has in-flight entertainment
  final bool hasInFlightEntertainment;

  /// Whether the flight has WiFi
  final bool hasWifi;

  /// Whether the flight has power outlets
  final bool hasPowerOutlets;

  /// Whether the flight has a meal
  final bool hasMeal;

  /// Whether the flight has a lounge access
  final bool hasLoungeAccess;

  /// Whether the flight has priority boarding
  final bool hasPriorityBoarding;

  /// Whether the flight has extra legroom
  final bool hasExtraLegroom;

  /// Creates a new flight
  const Flight({
    required super.id,
    required super.name,
    required super.description,
    required super.price,
    required super.currency,
    required super.rating,
    required super.reviewCount,
    required super.imageUrl,
    required super.additionalImages,
    required super.provider,
    required super.location,
    required super.coordinates,
    required super.isAvailable,
    required super.isFeatured,
    required super.isOnSale,
    super.originalPrice,
    super.discountPercentage,
    required super.tags,
    required super.amenities,
    required super.cancellationPolicy,
    required super.createdAt,
    required super.updatedAt,
    required this.flightType,
    required this.flightClass,
    required this.segments,
    required this.layovers,
    required this.totalDurationMinutes,
    required this.departureAirportCode,
    required this.departureCity,
    required this.departureTime,
    required this.arrivalAirportCode,
    required this.arrivalCity,
    required this.arrivalTime,
    required this.passengerCount,
    required this.baggageAllowance,
    required this.isRefundable,
    required this.isChangeable,
    this.changeFee,
    required this.hasInFlightEntertainment,
    required this.hasWifi,
    required this.hasPowerOutlets,
    required this.hasMeal,
    required this.hasLoungeAccess,
    required this.hasPriorityBoarding,
    required this.hasExtraLegroom,
  });

  @override
  IconData get icon => Icons.flight;

  @override
  Color get color => Colors.lightBlue;

  /// Get the formatted total duration
  String get formattedTotalDuration {
    final hours = totalDurationMinutes ~/ 60;
    final minutes = totalDurationMinutes % 60;
    return '${hours}h ${minutes}m';
  }

  /// Get the formatted departure time
  String get formattedDepartureTime {
    return '${departureTime.hour.toString().padLeft(2, '0')}:${departureTime.minute.toString().padLeft(2, '0')}';
  }

  /// Get the formatted arrival time
  String get formattedArrivalTime {
    return '${arrivalTime.hour.toString().padLeft(2, '0')}:${arrivalTime.minute.toString().padLeft(2, '0')}';
  }

  /// Get the formatted departure date
  String get formattedDepartureDate {
    return '${departureTime.day}/${departureTime.month}/${departureTime.year}';
  }

  /// Get the formatted arrival date
  String get formattedArrivalDate {
    return '${arrivalTime.day}/${arrivalTime.month}/${arrivalTime.year}';
  }

  /// Get the formatted route
  String get formattedRoute {
    return '$departureAirportCode - $arrivalAirportCode';
  }

  /// Get the formatted baggage allowance
  String get formattedBaggageAllowance {
    return '${baggageAllowance.toStringAsFixed(0)} kg';
  }

  /// Get the formatted change fee
  String? get formattedChangeFee {
    if (changeFee == null) return null;
    return '$currency${changeFee!.toStringAsFixed(2)}';
  }

  /// Get the formatted number of stops
  String get formattedStops {
    switch (flightType) {
      case FlightType.direct:
        return 'Direct';
      case FlightType.oneStop:
        return '1 Stop';
      case FlightType.multiStop:
        return '${layovers.length} Stops';
    }
  }
}
