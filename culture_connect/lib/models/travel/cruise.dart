import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'travel_service_base.dart';

/// Enum for cruise types
enum CruiseType {
  ocean,
  river,
  expedition,
  luxury,
  family,
  adventure,
  themed,
}

/// Extension for cruise types
extension CruiseTypeExtension on CruiseType {
  /// Get the display name for the cruise type
  String get displayName {
    switch (this) {
      case CruiseType.ocean:
        return 'Ocean Cruise';
      case CruiseType.river:
        return 'River Cruise';
      case CruiseType.expedition:
        return 'Expedition Cruise';
      case CruiseType.luxury:
        return 'Luxury Cruise';
      case CruiseType.family:
        return 'Family Cruise';
      case CruiseType.adventure:
        return 'Adventure Cruise';
      case CruiseType.themed:
        return 'Themed Cruise';
    }
  }
}

/// Enum for cabin types
enum CabinType {
  interior,
  oceanView,
  balcony,
  suite,
  deluxeSuite,
  penthouse,
}

/// Extension for cabin types
extension CabinTypeExtension on CabinType {
  /// Get the display name for the cabin type
  String get displayName {
    switch (this) {
      case CabinType.interior:
        return 'Interior Cabin';
      case CabinType.oceanView:
        return 'Ocean View Cabin';
      case CabinType.balcony:
        return 'Balcony Cabin';
      case CabinType.suite:
        return 'Suite';
      case CabinType.deluxeSuite:
        return 'Deluxe Suite';
      case CabinType.penthouse:
        return 'Penthouse Suite';
    }
  }
}

/// A model representing a cruise cabin
class CruiseCabin {
  /// Unique identifier for the cabin
  final String id;
  
  /// Type of cabin
  final CabinType type;
  
  /// Description of the cabin
  final String description;
  
  /// Price per person
  final double pricePerPerson;
  
  /// Currency of the price
  final String currency;
  
  /// Size of the cabin in square meters/feet
  final String size;
  
  /// Maximum number of guests
  final int maxGuests;
  
  /// Deck number
  final int deckNumber;
  
  /// Cabin number
  final String cabinNumber;
  
  /// Whether the cabin has a window
  final bool hasWindow;
  
  /// Whether the cabin has a balcony
  final bool hasBalcony;
  
  /// Whether the cabin has a private bathroom
  final bool hasPrivateBathroom;
  
  /// Whether the cabin has a TV
  final bool hasTV;
  
  /// Whether the cabin has a minibar
  final bool hasMinibar;
  
  /// Whether the cabin has a safe
  final bool hasSafe;
  
  /// Whether the cabin has room service
  final bool hasRoomService;
  
  /// URL to the image of the cabin
  final String imageUrl;
  
  /// List of additional images
  final List<String> additionalImages;
  
  /// Creates a new cruise cabin
  const CruiseCabin({
    required this.id,
    required this.type,
    required this.description,
    required this.pricePerPerson,
    required this.currency,
    required this.size,
    required this.maxGuests,
    required this.deckNumber,
    required this.cabinNumber,
    required this.hasWindow,
    required this.hasBalcony,
    required this.hasPrivateBathroom,
    required this.hasTV,
    required this.hasMinibar,
    required this.hasSafe,
    required this.hasRoomService,
    required this.imageUrl,
    required this.additionalImages,
  });
  
  /// Get the formatted price per person
  String get formattedPricePerPerson {
    return '$currency${pricePerPerson.toStringAsFixed(2)}';
  }
}

/// A model representing a port of call
class PortOfCall {
  /// Unique identifier for the port of call
  final String id;
  
  /// Name of the port
  final String portName;
  
  /// City
  final String city;
  
  /// Country
  final String country;
  
  /// Description of the port
  final String description;
  
  /// Arrival date and time
  final DateTime arrivalDateTime;
  
  /// Departure date and time
  final DateTime departureDateTime;
  
  /// Duration of stay in minutes
  final int durationMinutes;
  
  /// Coordinates of the port
  final LatLng coordinates;
  
  /// Available excursions
  final List<String> availableExcursions;
  
  /// URL to the image of the port
  final String imageUrl;
  
  /// Creates a new port of call
  const PortOfCall({
    required this.id,
    required this.portName,
    required this.city,
    required this.country,
    required this.description,
    required this.arrivalDateTime,
    required this.departureDateTime,
    required this.durationMinutes,
    required this.coordinates,
    required this.availableExcursions,
    required this.imageUrl,
  });
  
  /// Get the formatted arrival date and time
  String get formattedArrivalDateTime {
    return '${arrivalDateTime.day}/${arrivalDateTime.month}/${arrivalDateTime.year} ${arrivalDateTime.hour.toString().padLeft(2, '0')}:${arrivalDateTime.minute.toString().padLeft(2, '0')}';
  }
  
  /// Get the formatted departure date and time
  String get formattedDepartureDateTime {
    return '${departureDateTime.day}/${departureDateTime.month}/${departureDateTime.year} ${departureDateTime.hour.toString().padLeft(2, '0')}:${departureDateTime.minute.toString().padLeft(2, '0')}';
  }
  
  /// Get the formatted duration
  String get formattedDuration {
    final hours = durationMinutes ~/ 60;
    final minutes = durationMinutes % 60;
    return '${hours}h ${minutes}m';
  }
  
  /// Get the formatted location
  String get formattedLocation {
    return '$city, $country';
  }
}

/// A model representing a cruise
class Cruise extends TravelService {
  /// Type of cruise
  final CruiseType cruiseType;
  
  /// Cruise line
  final String cruiseLine;
  
  /// Cruise line logo URL
  final String cruiseLineLogoUrl;
  
  /// Ship name
  final String shipName;
  
  /// Ship description
  final String shipDescription;
  
  /// Year built
  final int yearBuilt;
  
  /// Year refurbished (if applicable)
  final int? yearRefurbished;
  
  /// Number of passengers
  final int passengerCount;
  
  /// Number of crew
  final int crewCount;
  
  /// Number of decks
  final int deckCount;
  
  /// Length of the ship in meters
  final double shipLength;
  
  /// Width of the ship in meters
  final double shipWidth;
  
  /// Tonnage of the ship
  final double shipTonnage;
  
  /// Speed of the ship in knots
  final double shipSpeed;
  
  /// Available cabins
  final List<CruiseCabin> cabins;
  
  /// Departure port
  final String departurePort;
  
  /// Departure city
  final String departureCity;
  
  /// Departure country
  final String departureCountry;
  
  /// Departure date and time
  final DateTime departureDateTime;
  
  /// Arrival port
  final String arrivalPort;
  
  /// Arrival city
  final String arrivalCity;
  
  /// Arrival country
  final String arrivalCountry;
  
  /// Arrival date and time
  final DateTime arrivalDateTime;
  
  /// Duration in days
  final int durationDays;
  
  /// Ports of call
  final List<PortOfCall> portsOfCall;
  
  /// Whether the cruise is all-inclusive
  final bool isAllInclusive;
  
  /// Whether the cruise has a casino
  final bool hasCasino;
  
  /// Whether the cruise has a spa
  final bool hasSpa;
  
  /// Whether the cruise has a gym
  final bool hasGym;
  
  /// Whether the cruise has a pool
  final bool hasPool;
  
  /// Whether the cruise has a kids club
  final bool hasKidsClub;
  
  /// Whether the cruise has entertainment
  final bool hasEntertainment;
  
  /// Whether the cruise has WiFi
  final bool hasWifi;
  
  /// Whether the cruise has restaurants
  final bool hasRestaurants;
  
  /// Whether the cruise has bars
  final bool hasBars;
  
  /// Whether the cruise has room service
  final bool hasRoomService;
  
  /// Whether the cruise has a medical center
  final bool hasMedicalCenter;
  
  /// Creates a new cruise
  const Cruise({
    required super.id,
    required super.name,
    required super.description,
    required super.price,
    required super.currency,
    required super.rating,
    required super.reviewCount,
    required super.imageUrl,
    required super.additionalImages,
    required super.provider,
    required super.location,
    required super.coordinates,
    required super.isAvailable,
    required super.isFeatured,
    required super.isOnSale,
    super.originalPrice,
    super.discountPercentage,
    required super.tags,
    required super.amenities,
    required super.cancellationPolicy,
    required super.createdAt,
    required super.updatedAt,
    required this.cruiseType,
    required this.cruiseLine,
    required this.cruiseLineLogoUrl,
    required this.shipName,
    required this.shipDescription,
    required this.yearBuilt,
    this.yearRefurbished,
    required this.passengerCount,
    required this.crewCount,
    required this.deckCount,
    required this.shipLength,
    required this.shipWidth,
    required this.shipTonnage,
    required this.shipSpeed,
    required this.cabins,
    required this.departurePort,
    required this.departureCity,
    required this.departureCountry,
    required this.departureDateTime,
    required this.arrivalPort,
    required this.arrivalCity,
    required this.arrivalCountry,
    required this.arrivalDateTime,
    required this.durationDays,
    required this.portsOfCall,
    required this.isAllInclusive,
    required this.hasCasino,
    required this.hasSpa,
    required this.hasGym,
    required this.hasPool,
    required this.hasKidsClub,
    required this.hasEntertainment,
    required this.hasWifi,
    required this.hasRestaurants,
    required this.hasBars,
    required this.hasRoomService,
    required this.hasMedicalCenter,
  });
  
  @override
  IconData get icon => Icons.directions_boat;
  
  @override
  Color get color => Colors.teal;
  
  /// Get the formatted departure date and time
  String get formattedDepartureDateTime {
    return '${departureDateTime.day}/${departureDateTime.month}/${departureDateTime.year} ${departureDateTime.hour.toString().padLeft(2, '0')}:${departureDateTime.minute.toString().padLeft(2, '0')}';
  }
  
  /// Get the formatted arrival date and time
  String get formattedArrivalDateTime {
    return '${arrivalDateTime.day}/${arrivalDateTime.month}/${arrivalDateTime.year} ${arrivalDateTime.hour.toString().padLeft(2, '0')}:${arrivalDateTime.minute.toString().padLeft(2, '0')}';
  }
  
  /// Get the formatted duration
  String get formattedDuration {
    return '$durationDays days';
  }
  
  /// Get the formatted departure location
  String get formattedDepartureLocation {
    return '$departurePort, $departureCity, $departureCountry';
  }
  
  /// Get the formatted arrival location
  String get formattedArrivalLocation {
    return '$arrivalPort, $arrivalCity, $arrivalCountry';
  }
  
  /// Get the formatted ship details
  String get formattedShipDetails {
    return '$shipName (Built: $yearBuilt${yearRefurbished != null ? ', Refurbished: $yearRefurbished' : ''})';
  }
  
  /// Get the formatted ship size
  String get formattedShipSize {
    return '${shipLength.toStringAsFixed(0)}m x ${shipWidth.toStringAsFixed(0)}m, ${shipTonnage.toStringAsFixed(0)} tons';
  }
  
  /// Get the formatted passenger and crew count
  String get formattedPassengerAndCrewCount {
    return '$passengerCount passengers, $crewCount crew';
  }
  
  /// Get the formatted itinerary
  String get formattedItinerary {
    final ports = portsOfCall.map((port) => port.city).join(' - ');
    return '$departureCity - $ports - $arrivalCity';
  }
}
