import 'package:cloud_firestore/cloud_firestore.dart';

/// Categories for hotel reviews
enum HotelReviewCategory {
  /// Cleanliness rating
  cleanliness,

  /// Service rating
  service,

  /// Location rating
  location,

  /// Value for money rating
  value,

  /// Comfort rating
  comfort,

  /// Facilities rating
  facilities,
}

/// Extension for hotel review categories
extension HotelReviewCategoryExtension on HotelReviewCategory {
  /// Get the display name for the category
  String get displayName {
    switch (this) {
      case HotelReviewCategory.cleanliness:
        return 'Cleanliness';
      case HotelReviewCategory.service:
        return 'Service';
      case HotelReviewCategory.location:
        return 'Location';
      case HotelReviewCategory.value:
        return 'Value for Money';
      case HotelReviewCategory.comfort:
        return 'Comfort';
      case HotelReviewCategory.facilities:
        return 'Facilities';
    }
  }
}

/// A model representing a hotel review
class HotelReview {
  /// Unique identifier for the review
  final String id;

  /// ID of the hotel being reviewed
  final String hotelId;

  /// ID of the booking associated with this review (optional)
  final String? bookingId;

  /// ID of the user who wrote the review
  final String userId;

  /// User's name who wrote the review
  final String userName;

  /// User's profile image URL
  final String? userProfileImageUrl;

  /// Overall rating given by the user (1-5 stars)
  final double overallRating;

  /// Category ratings (cleanliness, service, etc.)
  final Map<HotelReviewCategory, double> categoryRatings;

  /// Text content of the review
  final String content;

  /// Date when the review was posted
  final DateTime datePosted;

  /// Date when the review was last updated
  final DateTime? dateUpdated;

  /// Number of users who found this review helpful
  final int helpfulCount;

  /// IDs of users who marked this review as helpful
  final List<String> helpfulUserIds;

  /// URLs of photos attached to the review
  final List<String> photoUrls;

  /// Response from the hotel to this review
  final HotelResponse? hotelResponse;

  /// Whether the review is verified (user actually stayed at the hotel)
  final bool isVerified;

  /// Whether the review is published and visible to others
  final bool isPublished;

  /// Tags associated with the review (e.g., "Clean", "Friendly Staff", "Great Location")
  final List<String> tags;

  /// Trip type (e.g., "Business", "Couple", "Family", "Friends", "Solo")
  final String? tripType;

  /// Room type the user stayed in
  final String? roomType;

  /// Length of stay in nights
  final int? stayDuration;

  /// Creates a new hotel review
  const HotelReview({
    required this.id,
    required this.hotelId,
    this.bookingId,
    required this.userId,
    required this.userName,
    this.userProfileImageUrl,
    required this.overallRating,
    required this.categoryRatings,
    required this.content,
    required this.datePosted,
    this.dateUpdated,
    this.helpfulCount = 0,
    this.helpfulUserIds = const [],
    this.photoUrls = const [],
    this.hotelResponse,
    this.isVerified = false,
    this.isPublished = true,
    this.tags = const [],
    this.tripType,
    this.roomType,
    this.stayDuration,
  });

  /// Creates a hotel review from a JSON map
  factory HotelReview.fromJson(Map<String, dynamic> json) {
    // Convert category ratings from JSON
    final categoryRatingsJson =
        json['categoryRatings'] as Map<String, dynamic>? ?? {};
    final categoryRatings = <HotelReviewCategory, double>{};

    for (final entry in categoryRatingsJson.entries) {
      final category = _categoryFromString(entry.key);
      if (category != null) {
        categoryRatings[category] = (entry.value as num).toDouble();
      }
    }

    return HotelReview(
      id: json['id'] as String,
      hotelId: json['hotelId'] as String,
      bookingId: json['bookingId'] as String?,
      userId: json['userId'] as String,
      userName: json['userName'] as String,
      userProfileImageUrl: json['userProfileImageUrl'] as String?,
      overallRating: (json['overallRating'] as num).toDouble(),
      categoryRatings: categoryRatings,
      content: json['content'] as String,
      datePosted: (json['datePosted'] is Timestamp)
          ? (json['datePosted'] as Timestamp).toDate()
          : DateTime.parse(json['datePosted'] as String),
      dateUpdated: json['dateUpdated'] != null
          ? (json['dateUpdated'] is Timestamp)
              ? (json['dateUpdated'] as Timestamp).toDate()
              : DateTime.parse(json['dateUpdated'] as String)
          : null,
      helpfulCount: json['helpfulCount'] as int? ?? 0,
      helpfulUserIds: (json['helpfulUserIds'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      photoUrls: (json['photoUrls'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      hotelResponse: json['hotelResponse'] != null
          ? HotelResponse.fromJson(
              json['hotelResponse'] as Map<String, dynamic>)
          : null,
      isVerified: json['isVerified'] as bool? ?? false,
      isPublished: json['isPublished'] as bool? ?? true,
      tags:
          (json['tags'] as List<dynamic>?)?.map((e) => e as String).toList() ??
              const [],
      tripType: json['tripType'] as String?,
      roomType: json['roomType'] as String?,
      stayDuration: json['stayDuration'] as int?,
    );
  }

  /// Converts this hotel review to a JSON map
  Map<String, dynamic> toJson() {
    // Convert category ratings to JSON
    final categoryRatingsJson = <String, dynamic>{};
    for (final entry in categoryRatings.entries) {
      categoryRatingsJson[_categoryToString(entry.key)] = entry.value;
    }

    return {
      'id': id,
      'hotelId': hotelId,
      'bookingId': bookingId,
      'userId': userId,
      'userName': userName,
      'userProfileImageUrl': userProfileImageUrl,
      'overallRating': overallRating,
      'categoryRatings': categoryRatingsJson,
      'content': content,
      'datePosted': datePosted.toIso8601String(),
      'dateUpdated': dateUpdated?.toIso8601String(),
      'helpfulCount': helpfulCount,
      'helpfulUserIds': helpfulUserIds,
      'photoUrls': photoUrls,
      'hotelResponse': hotelResponse?.toJson(),
      'isVerified': isVerified,
      'isPublished': isPublished,
      'tags': tags,
      'tripType': tripType,
      'roomType': roomType,
      'stayDuration': stayDuration,
    };
  }

  /// Creates a copy of this hotel review with the given fields replaced with new values
  HotelReview copyWith({
    String? id,
    String? hotelId,
    String? bookingId,
    String? userId,
    String? userName,
    String? userProfileImageUrl,
    double? overallRating,
    Map<HotelReviewCategory, double>? categoryRatings,
    String? content,
    DateTime? datePosted,
    DateTime? dateUpdated,
    int? helpfulCount,
    List<String>? helpfulUserIds,
    List<String>? photoUrls,
    HotelResponse? hotelResponse,
    bool? isVerified,
    bool? isPublished,
    List<String>? tags,
    String? tripType,
    String? roomType,
    int? stayDuration,
  }) {
    return HotelReview(
      id: id ?? this.id,
      hotelId: hotelId ?? this.hotelId,
      bookingId: bookingId ?? this.bookingId,
      userId: userId ?? this.userId,
      userName: userName ?? this.userName,
      userProfileImageUrl: userProfileImageUrl ?? this.userProfileImageUrl,
      overallRating: overallRating ?? this.overallRating,
      categoryRatings: categoryRatings ?? this.categoryRatings,
      content: content ?? this.content,
      datePosted: datePosted ?? this.datePosted,
      dateUpdated: dateUpdated ?? this.dateUpdated,
      helpfulCount: helpfulCount ?? this.helpfulCount,
      helpfulUserIds: helpfulUserIds ?? this.helpfulUserIds,
      photoUrls: photoUrls ?? this.photoUrls,
      hotelResponse: hotelResponse ?? this.hotelResponse,
      isVerified: isVerified ?? this.isVerified,
      isPublished: isPublished ?? this.isPublished,
      tags: tags ?? this.tags,
      tripType: tripType ?? this.tripType,
      roomType: roomType ?? this.roomType,
      stayDuration: stayDuration ?? this.stayDuration,
    );
  }

  /// Get the average category rating
  double get averageCategoryRating {
    if (categoryRatings.isEmpty) return overallRating;

    final sum =
        categoryRatings.values.fold<double>(0, (sum, rating) => sum + rating);
    return sum / categoryRatings.length;
  }

  /// Convert a string to a HotelReviewCategory
  static HotelReviewCategory? _categoryFromString(String value) {
    switch (value) {
      case 'cleanliness':
        return HotelReviewCategory.cleanliness;
      case 'service':
        return HotelReviewCategory.service;
      case 'location':
        return HotelReviewCategory.location;
      case 'value':
        return HotelReviewCategory.value;
      case 'comfort':
        return HotelReviewCategory.comfort;
      case 'facilities':
        return HotelReviewCategory.facilities;
      default:
        return null;
    }
  }

  /// Public method to convert a string to a HotelReviewCategory
  static HotelReviewCategory? categoryFromString(String value) {
    return _categoryFromString(value);
  }

  /// Convert a HotelReviewCategory to a string
  static String _categoryToString(HotelReviewCategory category) {
    switch (category) {
      case HotelReviewCategory.cleanliness:
        return 'cleanliness';
      case HotelReviewCategory.service:
        return 'service';
      case HotelReviewCategory.location:
        return 'location';
      case HotelReviewCategory.value:
        return 'value';
      case HotelReviewCategory.comfort:
        return 'comfort';
      case HotelReviewCategory.facilities:
        return 'facilities';
    }
  }

  /// Public method to convert a HotelReviewCategory to a string
  static String categoryToString(HotelReviewCategory category) {
    return _categoryToString(category);
  }
}

/// A model representing a hotel's response to a review
class HotelResponse {
  /// ID of the staff member who responded
  final String staffId;

  /// Name of the staff member who responded
  final String staffName;

  /// Staff member's title/position
  final String staffTitle;

  /// Response content
  final String content;

  /// Date when the response was posted
  final DateTime datePosted;

  /// Creates a new hotel response
  const HotelResponse({
    required this.staffId,
    required this.staffName,
    required this.staffTitle,
    required this.content,
    required this.datePosted,
  });

  /// Creates a hotel response from a JSON map
  factory HotelResponse.fromJson(Map<String, dynamic> json) {
    return HotelResponse(
      staffId: json['staffId'] as String,
      staffName: json['staffName'] as String,
      staffTitle: json['staffTitle'] as String,
      content: json['content'] as String,
      datePosted: (json['datePosted'] is Timestamp)
          ? (json['datePosted'] as Timestamp).toDate()
          : DateTime.parse(json['datePosted'] as String),
    );
  }

  /// Converts this hotel response to a JSON map
  Map<String, dynamic> toJson() {
    return {
      'staffId': staffId,
      'staffName': staffName,
      'staffTitle': staffTitle,
      'content': content,
      'datePosted': datePosted.toIso8601String(),
    };
  }
}
