import 'package:flutter/material.dart';
import 'package:culture_connect/models/travel/travel.dart';

/// Trip type for flight search
enum TripType {
  /// One-way trip
  oneWay,
  
  /// Round-trip
  roundTrip,
  
  /// Multi-city trip
  multiCity,
}

/// Extension for trip types
extension TripTypeExtension on TripType {
  /// Get the display name for the trip type
  String get displayName {
    switch (this) {
      case TripType.oneWay:
        return 'One-way';
      case TripType.roundTrip:
        return 'Round-trip';
      case TripType.multiCity:
        return 'Multi-city';
    }
  }
  
  /// Get the icon for the trip type
  IconData get icon {
    switch (this) {
      case TripType.oneWay:
        return Icons.arrow_forward;
      case TripType.roundTrip:
        return Icons.sync_alt;
      case TripType.multiCity:
        return Icons.alt_route;
    }
  }
}

/// A model representing a passenger
class Passenger {
  /// Type of passenger
  final PassengerType type;
  
  /// Number of passengers of this type
  final int count;
  
  /// Creates a new passenger
  const Passenger({
    required this.type,
    required this.count,
  });
  
  /// Creates a copy with some fields replaced
  Passenger copyWith({
    PassengerType? type,
    int? count,
  }) {
    return Passenger(
      type: type ?? this.type,
      count: count ?? this.count,
    );
  }
}

/// Type of passenger
enum PassengerType {
  /// Adult passenger (12+ years)
  adult,
  
  /// Child passenger (2-11 years)
  child,
  
  /// Infant passenger (under 2 years)
  infant,
}

/// Extension for passenger types
extension PassengerTypeExtension on PassengerType {
  /// Get the display name for the passenger type
  String get displayName {
    switch (this) {
      case PassengerType.adult:
        return 'Adult (12+ years)';
      case PassengerType.child:
        return 'Child (2-11 years)';
      case PassengerType.infant:
        return 'Infant (under 2 years)';
    }
  }
  
  /// Get the short display name for the passenger type
  String get shortDisplayName {
    switch (this) {
      case PassengerType.adult:
        return 'Adult';
      case PassengerType.child:
        return 'Child';
      case PassengerType.infant:
        return 'Infant';
    }
  }
}

/// A model representing a flight route
class FlightRoute {
  /// Origin airport code
  final String originCode;
  
  /// Origin airport name
  final String originName;
  
  /// Origin city
  final String originCity;
  
  /// Destination airport code
  final String destinationCode;
  
  /// Destination airport name
  final String destinationName;
  
  /// Destination city
  final String destinationCity;
  
  /// Departure date
  final DateTime departureDate;
  
  /// Creates a new flight route
  const FlightRoute({
    required this.originCode,
    required this.originName,
    required this.originCity,
    required this.destinationCode,
    required this.destinationName,
    required this.destinationCity,
    required this.departureDate,
  });
  
  /// Creates a copy with some fields replaced
  FlightRoute copyWith({
    String? originCode,
    String? originName,
    String? originCity,
    String? destinationCode,
    String? destinationName,
    String? destinationCity,
    DateTime? departureDate,
  }) {
    return FlightRoute(
      originCode: originCode ?? this.originCode,
      originName: originName ?? this.originName,
      originCity: originCity ?? this.originCity,
      destinationCode: destinationCode ?? this.destinationCode,
      destinationName: destinationName ?? this.destinationName,
      destinationCity: destinationCity ?? this.destinationCity,
      departureDate: departureDate ?? this.departureDate,
    );
  }
  
  /// Get the formatted departure date
  String get formattedDepartureDate {
    return '${departureDate.day}/${departureDate.month}/${departureDate.year}';
  }
  
  /// Get the formatted route
  String get formattedRoute {
    return '$originCode - $destinationCode';
  }
}

/// A model representing flight search parameters
class FlightSearchParams {
  /// Trip type
  final TripType tripType;
  
  /// Flight routes
  final List<FlightRoute> routes;
  
  /// Passengers
  final List<Passenger> passengers;
  
  /// Flight class
  final FlightClass flightClass;
  
  /// Whether to search for direct flights only
  final bool directFlightsOnly;
  
  /// Whether to search for flexible dates
  final bool flexibleDates;
  
  /// Preferred airlines
  final List<String>? preferredAirlines;
  
  /// Creates new flight search parameters
  const FlightSearchParams({
    this.tripType = TripType.roundTrip,
    required this.routes,
    required this.passengers,
    this.flightClass = FlightClass.economy,
    this.directFlightsOnly = false,
    this.flexibleDates = false,
    this.preferredAirlines,
  });
  
  /// Creates a copy with some fields replaced
  FlightSearchParams copyWith({
    TripType? tripType,
    List<FlightRoute>? routes,
    List<Passenger>? passengers,
    FlightClass? flightClass,
    bool? directFlightsOnly,
    bool? flexibleDates,
    List<String>? preferredAirlines,
  }) {
    return FlightSearchParams(
      tripType: tripType ?? this.tripType,
      routes: routes ?? this.routes,
      passengers: passengers ?? this.passengers,
      flightClass: flightClass ?? this.flightClass,
      directFlightsOnly: directFlightsOnly ?? this.directFlightsOnly,
      flexibleDates: flexibleDates ?? this.flexibleDates,
      preferredAirlines: preferredAirlines ?? this.preferredAirlines,
    );
  }
  
  /// Get the total number of passengers
  int get totalPassengers {
    return passengers.fold(0, (sum, passenger) => sum + passenger.count);
  }
  
  /// Get the number of adults
  int get adultCount {
    return passengers
        .where((p) => p.type == PassengerType.adult)
        .fold(0, (sum, p) => sum + p.count);
  }
  
  /// Get the number of children
  int get childCount {
    return passengers
        .where((p) => p.type == PassengerType.child)
        .fold(0, (sum, p) => sum + p.count);
  }
  
  /// Get the number of infants
  int get infantCount {
    return passengers
        .where((p) => p.type == PassengerType.infant)
        .fold(0, (sum, p) => sum + p.count);
  }
  
  /// Get the formatted passenger count
  String get formattedPassengerCount {
    if (totalPassengers == 1) {
      return '1 Passenger';
    } else {
      return '$totalPassengers Passengers';
    }
  }
  
  /// Get the formatted passenger breakdown
  String get formattedPassengerBreakdown {
    final parts = <String>[];
    
    if (adultCount > 0) {
      parts.add('$adultCount ${adultCount == 1 ? 'Adult' : 'Adults'}');
    }
    
    if (childCount > 0) {
      parts.add('$childCount ${childCount == 1 ? 'Child' : 'Children'}');
    }
    
    if (infantCount > 0) {
      parts.add('$infantCount ${infantCount == 1 ? 'Infant' : 'Infants'}');
    }
    
    return parts.join(', ');
  }
  
  /// Create default search parameters
  factory FlightSearchParams.defaultParams() {
    final now = DateTime.now();
    final tomorrow = now.add(const Duration(days: 1));
    final nextWeek = now.add(const Duration(days: 8));
    
    return FlightSearchParams(
      tripType: TripType.roundTrip,
      routes: [
        FlightRoute(
          originCode: 'JFK',
          originName: 'John F. Kennedy International Airport',
          originCity: 'New York',
          destinationCode: 'LAX',
          destinationName: 'Los Angeles International Airport',
          destinationCity: 'Los Angeles',
          departureDate: tomorrow,
        ),
        FlightRoute(
          originCode: 'LAX',
          originName: 'Los Angeles International Airport',
          originCity: 'Los Angeles',
          destinationCode: 'JFK',
          destinationName: 'John F. Kennedy International Airport',
          destinationCity: 'New York',
          departureDate: nextWeek,
        ),
      ],
      passengers: [
        const Passenger(type: PassengerType.adult, count: 1),
      ],
      flightClass: FlightClass.economy,
      directFlightsOnly: false,
      flexibleDates: false,
    );
  }
}
