import 'package:flutter/material.dart';
import 'package:uuid/uuid.dart';

/// A class representing an event in a timeline
class TimelineEvent {
  /// The unique identifier
  final String id;
  
  /// The title
  final String title;
  
  /// The description
  final String? description;
  
  /// The date of the event
  final DateTime eventDate;
  
  /// The time of the event
  final TimeOfDay? eventTime;
  
  /// The end time of the event
  final TimeOfDay? endTime;
  
  /// The location name
  final String? location;
  
  /// The coordinates (latitude and longitude)
  final Map<String, double>? coordinates;
  
  /// The type of event
  final String eventType;
  
  /// The service ID (if linked to a travel service)
  final String? serviceId;
  
  /// The service type (if linked to a travel service)
  final String? serviceType;
  
  /// The booking ID (if linked to a booking)
  final String? bookingId;
  
  /// The booking reference (if linked to a booking)
  final String? bookingReference;
  
  /// The AR content ID (if has AR content)
  final String? arContentId;
  
  /// Whether the event has AR content
  final bool hasARContent;
  
  /// The status of the event
  final String status;
  
  /// The created at timestamp
  final DateTime createdAt;
  
  /// The updated at timestamp
  final DateTime updatedAt;
  
  /// Creates a new timeline event
  TimelineEvent({
    String? id,
    required this.title,
    this.description,
    required this.eventDate,
    this.eventTime,
    this.endTime,
    this.location,
    this.coordinates,
    required this.eventType,
    this.serviceId,
    this.serviceType,
    this.bookingId,
    this.bookingReference,
    this.arContentId,
    this.hasARContent = false,
    this.status = 'upcoming',
    DateTime? createdAt,
    DateTime? updatedAt,
  }) : 
    id = id ?? const Uuid().v4(),
    createdAt = createdAt ?? DateTime.now(),
    updatedAt = updatedAt ?? DateTime.now();
  
  /// Get the formatted time
  String? get formattedTime {
    if (eventTime == null) return null;
    
    final hour = eventTime!.hour.toString().padLeft(2, '0');
    final minute = eventTime!.minute.toString().padLeft(2, '0');
    return '$hour:$minute';
  }
  
  /// Get the formatted end time
  String? get formattedEndTime {
    if (endTime == null) return null;
    
    final hour = endTime!.hour.toString().padLeft(2, '0');
    final minute = endTime!.minute.toString().padLeft(2, '0');
    return '$hour:$minute';
  }
  
  /// Get the formatted time range
  String? get formattedTimeRange {
    if (eventTime == null) return null;
    
    if (endTime == null) {
      return formattedTime;
    }
    
    return '$formattedTime - $formattedEndTime';
  }
  
  /// Get the duration in minutes
  int? get durationMinutes {
    if (eventTime == null || endTime == null) return null;
    
    final startMinutes = eventTime!.hour * 60 + eventTime!.minute;
    final endMinutes = endTime!.hour * 60 + endTime!.minute;
    
    // Handle events that span midnight
    if (endMinutes < startMinutes) {
      return (24 * 60 - startMinutes) + endMinutes;
    }
    
    return endMinutes - startMinutes;
  }
  
  /// Get the formatted duration
  String? get formattedDuration {
    final minutes = durationMinutes;
    if (minutes == null) return null;
    
    final hours = minutes ~/ 60;
    final remainingMinutes = minutes % 60;
    
    if (hours > 0) {
      return '$hours h ${remainingMinutes > 0 ? '$remainingMinutes min' : ''}';
    }
    
    return '$minutes min';
  }
  
  /// Get the icon for the event type
  IconData get typeIcon {
    switch (eventType) {
      case 'accommodation':
        return Icons.hotel;
      case 'transportation':
        return Icons.directions_car;
      case 'activity':
        return Icons.local_activity;
      case 'food':
        return Icons.restaurant;
      case 'custom':
        return Icons.event;
      default:
        return Icons.event;
    }
  }
  
  /// Get the color for the event type
  Color get typeColor {
    switch (eventType) {
      case 'accommodation':
        return Colors.purple;
      case 'transportation':
        return Colors.blue;
      case 'activity':
        return Colors.orange;
      case 'food':
        return Colors.green;
      case 'custom':
        return Colors.grey;
      default:
        return Colors.grey;
    }
  }
  
  /// Get the icon for the event status
  IconData get statusIcon {
    switch (status) {
      case 'upcoming':
        return Icons.event_available;
      case 'confirmed':
        return Icons.confirmation_number;
      case 'completed':
        return Icons.check_circle;
      case 'cancelled':
        return Icons.cancel;
      default:
        return Icons.event;
    }
  }
  
  /// Get the color for the event status
  Color get statusColor {
    switch (status) {
      case 'upcoming':
        return Colors.blue;
      case 'confirmed':
        return Colors.green;
      case 'completed':
        return Colors.purple;
      case 'cancelled':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }
  
  /// Create a copy of this event with the given fields replaced
  TimelineEvent copyWith({
    String? id,
    String? title,
    String? description,
    DateTime? eventDate,
    TimeOfDay? eventTime,
    TimeOfDay? endTime,
    String? location,
    Map<String, double>? coordinates,
    String? eventType,
    String? serviceId,
    String? serviceType,
    String? bookingId,
    String? bookingReference,
    String? arContentId,
    bool? hasARContent,
    String? status,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return TimelineEvent(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      eventDate: eventDate ?? this.eventDate,
      eventTime: eventTime ?? this.eventTime,
      endTime: endTime ?? this.endTime,
      location: location ?? this.location,
      coordinates: coordinates ?? this.coordinates,
      eventType: eventType ?? this.eventType,
      serviceId: serviceId ?? this.serviceId,
      serviceType: serviceType ?? this.serviceType,
      bookingId: bookingId ?? this.bookingId,
      bookingReference: bookingReference ?? this.bookingReference,
      arContentId: arContentId ?? this.arContentId,
      hasARContent: hasARContent ?? this.hasARContent,
      status: status ?? this.status,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? DateTime.now(),
    );
  }
  
  /// Create a timeline event from JSON
  factory TimelineEvent.fromJson(Map<String, dynamic> json) {
    return TimelineEvent(
      id: json['id'],
      title: json['title'],
      description: json['description'],
      eventDate: DateTime.parse(json['eventDate']),
      eventTime: json['eventTime'] != null
          ? _parseTimeOfDay(json['eventTime'])
          : null,
      endTime: json['endTime'] != null
          ? _parseTimeOfDay(json['endTime'])
          : null,
      location: json['location'],
      coordinates: json['coordinates'] != null
          ? Map<String, double>.from(json['coordinates'])
          : null,
      eventType: json['eventType'],
      serviceId: json['serviceId'],
      serviceType: json['serviceType'],
      bookingId: json['bookingId'],
      bookingReference: json['bookingReference'],
      arContentId: json['arContentId'],
      hasARContent: json['hasARContent'] ?? false,
      status: json['status'] ?? 'upcoming',
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: DateTime.parse(json['updatedAt']),
    );
  }
  
  /// Parse TimeOfDay from string
  static TimeOfDay _parseTimeOfDay(String timeString) {
    final parts = timeString.split(':');
    return TimeOfDay(
      hour: int.parse(parts[0]),
      minute: int.parse(parts[1]),
    );
  }
  
  /// Convert this timeline event to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'eventDate': eventDate.toIso8601String(),
      'eventTime': eventTime != null
          ? '${eventTime!.hour.toString().padLeft(2, '0')}:${eventTime!.minute.toString().padLeft(2, '0')}'
          : null,
      'endTime': endTime != null
          ? '${endTime!.hour.toString().padLeft(2, '0')}:${endTime!.minute.toString().padLeft(2, '0')}'
          : null,
      'location': location,
      'coordinates': coordinates,
      'eventType': eventType,
      'serviceId': serviceId,
      'serviceType': serviceType,
      'bookingId': bookingId,
      'bookingReference': bookingReference,
      'arContentId': arContentId,
      'hasARContent': hasARContent,
      'status': status,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }
}
