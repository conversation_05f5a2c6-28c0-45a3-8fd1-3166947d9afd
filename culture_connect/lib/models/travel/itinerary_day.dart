import 'package:uuid/uuid.dart';
import 'package:culture_connect/models/travel/itinerary_item.dart';

/// A day in an itinerary
class ItineraryDay {
  /// The unique identifier
  final String id;

  /// The date
  final DateTime date;

  /// The day number in the itinerary
  final int dayNumber;

  /// The items for this day
  final List<ItineraryItem> items;

  /// The notes for this day
  final String? notes;

  /// The created at timestamp
  final DateTime createdAt;

  /// The updated at timestamp
  final DateTime updatedAt;

  /// Creates a new itinerary day
  ItineraryDay({
    String? id,
    required this.date,
    required this.dayNumber,
    required this.items,
    this.notes,
    DateTime? createdAt,
    DateTime? updatedAt,
  })  : id = id ?? const Uuid().v4(),
        createdAt = createdAt ?? DateTime.now(),
        updatedAt = updatedAt ?? DateTime.now();

  /// Get the formatted date
  String get formattedDate {
    return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
  }

  /// Get the day of week
  String get dayOfWeek {
    const days = [
      'Monday',
      'Tuesday',
      'Wednesday',
      'Thursday',
      'Friday',
      'Saturday',
      'Sunday'
    ];
    return days[date.weekday - 1];
  }

  /// Get the short day of week
  String get shortDayOfWeek {
    const days = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
    return days[date.weekday - 1];
  }

  /// Get the total price of all items
  double get totalPrice {
    return items.fold(0, (sum, item) => sum + (item.price ?? 0));
  }

  /// Get the morning items
  List<ItineraryItem> get morningItems {
    return items.where((item) {
      final startTime = item.startTime;
      if (startTime == null) return false;
      return startTime.hour < 12;
    }).toList();
  }

  /// Get the afternoon items
  List<ItineraryItem> get afternoonItems {
    return items.where((item) {
      final startTime = item.startTime;
      if (startTime == null) return false;
      return startTime.hour >= 12 && startTime.hour < 18;
    }).toList();
  }

  /// Get the evening items
  List<ItineraryItem> get eveningItems {
    return items.where((item) {
      final startTime = item.startTime;
      if (startTime == null) return false;
      return startTime.hour >= 18;
    }).toList();
  }

  /// Get the all-day items
  List<ItineraryItem> get allDayItems {
    return items.where((item) => item.startTime == null).toList();
  }

  /// Get the sorted items by start time
  List<ItineraryItem> get sortedItems {
    final sorted = List<ItineraryItem>.from(items);
    sorted.sort((a, b) {
      if (a.startTime == null && b.startTime == null) return 0;
      if (a.startTime == null) return -1;
      if (b.startTime == null) return 1;

      final aMinutes = a.startTime!.hour * 60 + a.startTime!.minute;
      final bMinutes = b.startTime!.hour * 60 + b.startTime!.minute;
      return aMinutes.compareTo(bMinutes);
    });
    return sorted;
  }

  /// Create a copy of this day with the given fields replaced
  ItineraryDay copyWith({
    String? id,
    DateTime? date,
    int? dayNumber,
    List<ItineraryItem>? items,
    String? notes,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return ItineraryDay(
      id: id ?? this.id,
      date: date ?? this.date,
      dayNumber: dayNumber ?? this.dayNumber,
      items: items ?? this.items,
      notes: notes ?? this.notes,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? DateTime.now(),
    );
  }

  /// Add an item to this day
  ItineraryDay addItem(ItineraryItem item) {
    final newItems = List<ItineraryItem>.from(items)..add(item);
    return copyWith(items: newItems);
  }

  /// Remove an item from this day
  ItineraryDay removeItem(String itemId) {
    final newItems = items.where((item) => item.id != itemId).toList();
    return copyWith(items: newItems);
  }

  /// Update an item in this day
  ItineraryDay updateItem(ItineraryItem updatedItem) {
    final newItems = items.map((item) {
      if (item.id == updatedItem.id) {
        return updatedItem;
      }
      return item;
    }).toList();
    return copyWith(items: newItems);
  }

  /// Create an itinerary day from JSON
  factory ItineraryDay.fromJson(Map<String, dynamic> json) {
    return ItineraryDay(
      id: json['id'],
      date: DateTime.parse(json['date']),
      dayNumber: json['dayNumber'],
      items: (json['items'] as List)
          .map((item) => ItineraryItem.fromJson(item))
          .toList(),
      notes: json['notes'],
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: DateTime.parse(json['updatedAt']),
    );
  }

  /// Convert this itinerary day to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'date': date.toIso8601String(),
      'dayNumber': dayNumber,
      'items': items.map((item) => item.toJson()).toList(),
      'notes': notes,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }
}
