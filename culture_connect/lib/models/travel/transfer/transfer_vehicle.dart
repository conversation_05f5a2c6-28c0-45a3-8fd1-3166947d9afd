import 'package:flutter/material.dart';

/// Enum representing the different types of transfer vehicles
enum TransferVehicleType {
  /// Standard sedan
  sedan,
  
  /// Luxury sedan
  luxurySedan,
  
  /// SUV
  suv,
  
  /// Luxury SUV
  luxurySuv,
  
  /// Minivan
  minivan,
  
  /// Van
  van,
  
  /// Shuttle bus
  shuttleBus,
  
  /// Limousine
  limousine,
  
  /// Electric vehicle
  electric,
}

/// Extension for transfer vehicle types
extension TransferVehicleTypeExtension on TransferVehicleType {
  /// Get the display name for the transfer vehicle type
  String get displayName {
    switch (this) {
      case TransferVehicleType.sedan:
        return 'Sedan';
      case TransferVehicleType.luxurySedan:
        return 'Luxury Sedan';
      case TransferVehicleType.suv:
        return 'SUV';
      case TransferVehicleType.luxurySuv:
        return 'Luxury SUV';
      case TransferVehicleType.minivan:
        return 'Minivan';
      case TransferVehicleType.van:
        return 'Van';
      case TransferVehicleType.shuttleBus:
        return 'Shuttle Bus';
      case TransferVehicleType.limousine:
        return 'Limousine';
      case TransferVehicleType.electric:
        return 'Electric Vehicle';
    }
  }
  
  /// Get the icon for the transfer vehicle type
  IconData get icon {
    switch (this) {
      case TransferVehicleType.sedan:
        return Icons.directions_car;
      case TransferVehicleType.luxurySedan:
        return Icons.directions_car;
      case TransferVehicleType.suv:
        return Icons.directions_car;
      case TransferVehicleType.luxurySuv:
        return Icons.directions_car;
      case TransferVehicleType.minivan:
        return Icons.airport_shuttle;
      case TransferVehicleType.van:
        return Icons.airport_shuttle;
      case TransferVehicleType.shuttleBus:
        return Icons.directions_bus;
      case TransferVehicleType.limousine:
        return Icons.directions_car;
      case TransferVehicleType.electric:
        return Icons.electric_car;
    }
  }
  
  /// Get the color for the transfer vehicle type
  Color get color {
    switch (this) {
      case TransferVehicleType.sedan:
        return Colors.blue;
      case TransferVehicleType.luxurySedan:
        return Colors.indigo;
      case TransferVehicleType.suv:
        return Colors.teal;
      case TransferVehicleType.luxurySuv:
        return Colors.purple;
      case TransferVehicleType.minivan:
        return Colors.orange;
      case TransferVehicleType.van:
        return Colors.amber;
      case TransferVehicleType.shuttleBus:
        return Colors.green;
      case TransferVehicleType.limousine:
        return Colors.black;
      case TransferVehicleType.electric:
        return Colors.lightBlue;
    }
  }
  
  /// Get the typical passenger capacity for the transfer vehicle type
  int get typicalPassengerCapacity {
    switch (this) {
      case TransferVehicleType.sedan:
        return 3;
      case TransferVehicleType.luxurySedan:
        return 3;
      case TransferVehicleType.suv:
        return 5;
      case TransferVehicleType.luxurySuv:
        return 5;
      case TransferVehicleType.minivan:
        return 7;
      case TransferVehicleType.van:
        return 10;
      case TransferVehicleType.shuttleBus:
        return 15;
      case TransferVehicleType.limousine:
        return 6;
      case TransferVehicleType.electric:
        return 4;
    }
  }
  
  /// Get the typical luggage capacity for the transfer vehicle type
  int get typicalLuggageCapacity {
    switch (this) {
      case TransferVehicleType.sedan:
        return 2;
      case TransferVehicleType.luxurySedan:
        return 2;
      case TransferVehicleType.suv:
        return 4;
      case TransferVehicleType.luxurySuv:
        return 4;
      case TransferVehicleType.minivan:
        return 6;
      case TransferVehicleType.van:
        return 10;
      case TransferVehicleType.shuttleBus:
        return 15;
      case TransferVehicleType.limousine:
        return 3;
      case TransferVehicleType.electric:
        return 2;
    }
  }
}

/// A model representing a transfer vehicle
class TransferVehicle {
  /// Type of vehicle
  final TransferVehicleType type;
  
  /// Make of the vehicle
  final String make;
  
  /// Model of the vehicle
  final String model;
  
  /// Year of the vehicle
  final int year;
  
  /// Color of the vehicle
  final String color;
  
  /// License plate number
  final String? licensePlate;
  
  /// Maximum passenger capacity
  final int passengerCapacity;
  
  /// Maximum luggage capacity
  final int luggageCapacity;
  
  /// Features of the vehicle
  final List<String> features;
  
  /// URL to the image of the vehicle
  final String imageUrl;
  
  /// List of additional images
  final List<String> additionalImages;
  
  /// Whether the vehicle has air conditioning
  final bool hasAirConditioning;
  
  /// Whether the vehicle has WiFi
  final bool hasWifi;
  
  /// Whether the vehicle has USB ports
  final bool hasUsb;
  
  /// Whether the vehicle has a child seat
  final bool hasChildSeat;
  
  /// Whether the vehicle has a wheelchair access
  final bool hasWheelchairAccess;
  
  /// Creates a new transfer vehicle
  const TransferVehicle({
    required this.type,
    required this.make,
    required this.model,
    required this.year,
    required this.color,
    this.licensePlate,
    required this.passengerCapacity,
    required this.luggageCapacity,
    required this.features,
    required this.imageUrl,
    required this.additionalImages,
    required this.hasAirConditioning,
    required this.hasWifi,
    required this.hasUsb,
    required this.hasChildSeat,
    required this.hasWheelchairAccess,
  });
  
  /// Create a transfer vehicle from a JSON map
  factory TransferVehicle.fromJson(Map<String, dynamic> json) {
    return TransferVehicle(
      type: TransferVehicleType.values.firstWhere(
        (e) => e.toString() == 'TransferVehicleType.${json['type']}',
        orElse: () => TransferVehicleType.sedan,
      ),
      make: json['make'] as String,
      model: json['model'] as String,
      year: json['year'] as int,
      color: json['color'] as String,
      licensePlate: json['licensePlate'] as String?,
      passengerCapacity: json['passengerCapacity'] as int,
      luggageCapacity: json['luggageCapacity'] as int,
      features: (json['features'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      imageUrl: json['imageUrl'] as String,
      additionalImages: (json['additionalImages'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      hasAirConditioning: json['hasAirConditioning'] as bool,
      hasWifi: json['hasWifi'] as bool,
      hasUsb: json['hasUsb'] as bool,
      hasChildSeat: json['hasChildSeat'] as bool,
      hasWheelchairAccess: json['hasWheelchairAccess'] as bool,
    );
  }
  
  /// Convert the transfer vehicle to a JSON map
  Map<String, dynamic> toJson() {
    return {
      'type': type.toString().split('.').last,
      'make': make,
      'model': model,
      'year': year,
      'color': color,
      'licensePlate': licensePlate,
      'passengerCapacity': passengerCapacity,
      'luggageCapacity': luggageCapacity,
      'features': features,
      'imageUrl': imageUrl,
      'additionalImages': additionalImages,
      'hasAirConditioning': hasAirConditioning,
      'hasWifi': hasWifi,
      'hasUsb': hasUsb,
      'hasChildSeat': hasChildSeat,
      'hasWheelchairAccess': hasWheelchairAccess,
    };
  }
  
  /// Create a copy of this transfer vehicle with the given fields replaced with new values
  TransferVehicle copyWith({
    TransferVehicleType? type,
    String? make,
    String? model,
    int? year,
    String? color,
    String? licensePlate,
    int? passengerCapacity,
    int? luggageCapacity,
    List<String>? features,
    String? imageUrl,
    List<String>? additionalImages,
    bool? hasAirConditioning,
    bool? hasWifi,
    bool? hasUsb,
    bool? hasChildSeat,
    bool? hasWheelchairAccess,
  }) {
    return TransferVehicle(
      type: type ?? this.type,
      make: make ?? this.make,
      model: model ?? this.model,
      year: year ?? this.year,
      color: color ?? this.color,
      licensePlate: licensePlate ?? this.licensePlate,
      passengerCapacity: passengerCapacity ?? this.passengerCapacity,
      luggageCapacity: luggageCapacity ?? this.luggageCapacity,
      features: features ?? this.features,
      imageUrl: imageUrl ?? this.imageUrl,
      additionalImages: additionalImages ?? this.additionalImages,
      hasAirConditioning: hasAirConditioning ?? this.hasAirConditioning,
      hasWifi: hasWifi ?? this.hasWifi,
      hasUsb: hasUsb ?? this.hasUsb,
      hasChildSeat: hasChildSeat ?? this.hasChildSeat,
      hasWheelchairAccess: hasWheelchairAccess ?? this.hasWheelchairAccess,
    );
  }
  
  /// Get the full name of the vehicle
  String get fullName => '$year $make $model';
}
