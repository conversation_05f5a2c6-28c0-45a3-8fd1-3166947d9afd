import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:culture_connect/models/travel/transfer/transfer_service.dart';
import 'package:culture_connect/models/travel/transfer/transfer_location.dart';

/// Enum representing the different statuses of a transfer booking
enum TransferBookingStatus {
  /// Booking is pending
  pending,
  
  /// Booking is confirmed
  confirmed,
  
  /// Booking is in progress
  inProgress,
  
  /// Booking is completed
  completed,
  
  /// Booking is cancelled
  cancelled,
  
  /// Booking is refunded
  refunded,
  
  /// Booking is no-show
  noShow,
}

/// Extension for transfer booking status
extension TransferBookingStatusExtension on TransferBookingStatus {
  /// Get the display name for the transfer booking status
  String get displayName {
    switch (this) {
      case TransferBookingStatus.pending:
        return 'Pending';
      case TransferBookingStatus.confirmed:
        return 'Confirmed';
      case TransferBookingStatus.inProgress:
        return 'In Progress';
      case TransferBookingStatus.completed:
        return 'Completed';
      case TransferBookingStatus.cancelled:
        return 'Cancelled';
      case TransferBookingStatus.refunded:
        return 'Refunded';
      case TransferBookingStatus.noShow:
        return 'No Show';
    }
  }
  
  /// Get the icon for the transfer booking status
  IconData get icon {
    switch (this) {
      case TransferBookingStatus.pending:
        return Icons.hourglass_empty;
      case TransferBookingStatus.confirmed:
        return Icons.check_circle;
      case TransferBookingStatus.inProgress:
        return Icons.directions_car;
      case TransferBookingStatus.completed:
        return Icons.done_all;
      case TransferBookingStatus.cancelled:
        return Icons.cancel;
      case TransferBookingStatus.refunded:
        return Icons.money_off;
      case TransferBookingStatus.noShow:
        return Icons.person_off;
    }
  }
  
  /// Get the color for the transfer booking status
  Color get color {
    switch (this) {
      case TransferBookingStatus.pending:
        return Colors.orange;
      case TransferBookingStatus.confirmed:
        return Colors.green;
      case TransferBookingStatus.inProgress:
        return Colors.blue;
      case TransferBookingStatus.completed:
        return Colors.green;
      case TransferBookingStatus.cancelled:
        return Colors.red;
      case TransferBookingStatus.refunded:
        return Colors.purple;
      case TransferBookingStatus.noShow:
        return Colors.red;
    }
  }
}

/// A model representing a transfer booking
class TransferBooking {
  /// Unique identifier for the booking
  final String id;
  
  /// ID of the user who made the booking
  final String userId;
  
  /// ID of the transfer service
  final String transferId;
  
  /// Transfer service details
  final TransferService? transferService;
  
  /// Pickup location
  final TransferLocation pickupLocation;
  
  /// Dropoff location
  final TransferLocation dropoffLocation;
  
  /// Pickup date and time
  final DateTime pickupDateTime;
  
  /// Number of passengers
  final int passengerCount;
  
  /// Number of luggage items
  final int luggageCount;
  
  /// Special requests
  final String? specialRequests;
  
  /// Contact name
  final String contactName;
  
  /// Contact phone
  final String contactPhone;
  
  /// Contact email
  final String contactEmail;
  
  /// Flight information
  final String? flightInfo;
  
  /// Status of the booking
  final TransferBookingStatus status;
  
  /// Confirmation code
  final String? confirmationCode;
  
  /// Total price
  final double totalPrice;
  
  /// Currency
  final String currency;
  
  /// Payment method
  final String? paymentMethod;
  
  /// Payment transaction ID
  final String? paymentTransactionId;
  
  /// Driver assigned to the booking
  final String? driverId;
  
  /// Driver name
  final String? driverName;
  
  /// Driver phone
  final String? driverPhone;
  
  /// Vehicle details
  final String? vehicleDetails;
  
  /// Vehicle license plate
  final String? vehicleLicensePlate;
  
  /// Tracking URL
  final String? trackingUrl;
  
  /// Cancellation reason
  final String? cancellationReason;
  
  /// Cancellation date
  final DateTime? cancellationDate;
  
  /// Refund amount
  final double? refundAmount;
  
  /// Refund date
  final DateTime? refundDate;
  
  /// When the booking was created
  final DateTime createdAt;
  
  /// When the booking was last updated
  final DateTime updatedAt;
  
  /// Creates a new transfer booking
  const TransferBooking({
    required this.id,
    required this.userId,
    required this.transferId,
    this.transferService,
    required this.pickupLocation,
    required this.dropoffLocation,
    required this.pickupDateTime,
    required this.passengerCount,
    required this.luggageCount,
    this.specialRequests,
    required this.contactName,
    required this.contactPhone,
    required this.contactEmail,
    this.flightInfo,
    required this.status,
    this.confirmationCode,
    required this.totalPrice,
    required this.currency,
    this.paymentMethod,
    this.paymentTransactionId,
    this.driverId,
    this.driverName,
    this.driverPhone,
    this.vehicleDetails,
    this.vehicleLicensePlate,
    this.trackingUrl,
    this.cancellationReason,
    this.cancellationDate,
    this.refundAmount,
    this.refundDate,
    required this.createdAt,
    required this.updatedAt,
  });
  
  /// Create a transfer booking from a JSON map
  factory TransferBooking.fromJson(Map<String, dynamic> json) {
    return TransferBooking(
      id: json['id'] as String,
      userId: json['userId'] as String,
      transferId: json['transferId'] as String,
      transferService: json['transferService'] != null
          ? TransferService.fromJson(json['transferService'] as Map<String, dynamic>)
          : null,
      pickupLocation: TransferLocation.fromJson(json['pickupLocation'] as Map<String, dynamic>),
      dropoffLocation: TransferLocation.fromJson(json['dropoffLocation'] as Map<String, dynamic>),
      pickupDateTime: DateTime.parse(json['pickupDateTime'] as String),
      passengerCount: json['passengerCount'] as int,
      luggageCount: json['luggageCount'] as int,
      specialRequests: json['specialRequests'] as String?,
      contactName: json['contactName'] as String,
      contactPhone: json['contactPhone'] as String,
      contactEmail: json['contactEmail'] as String,
      flightInfo: json['flightInfo'] as String?,
      status: TransferBookingStatus.values.firstWhere(
        (e) => e.toString() == 'TransferBookingStatus.${json['status']}',
        orElse: () => TransferBookingStatus.pending,
      ),
      confirmationCode: json['confirmationCode'] as String?,
      totalPrice: json['totalPrice'] as double,
      currency: json['currency'] as String,
      paymentMethod: json['paymentMethod'] as String?,
      paymentTransactionId: json['paymentTransactionId'] as String?,
      driverId: json['driverId'] as String?,
      driverName: json['driverName'] as String?,
      driverPhone: json['driverPhone'] as String?,
      vehicleDetails: json['vehicleDetails'] as String?,
      vehicleLicensePlate: json['vehicleLicensePlate'] as String?,
      trackingUrl: json['trackingUrl'] as String?,
      cancellationReason: json['cancellationReason'] as String?,
      cancellationDate: json['cancellationDate'] != null
          ? DateTime.parse(json['cancellationDate'] as String)
          : null,
      refundAmount: json['refundAmount'] as double?,
      refundDate: json['refundDate'] != null
          ? DateTime.parse(json['refundDate'] as String)
          : null,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
    );
  }
  
  /// Convert the transfer booking to a JSON map
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userId': userId,
      'transferId': transferId,
      'transferService': transferService?.toJson(),
      'pickupLocation': pickupLocation.toJson(),
      'dropoffLocation': dropoffLocation.toJson(),
      'pickupDateTime': pickupDateTime.toIso8601String(),
      'passengerCount': passengerCount,
      'luggageCount': luggageCount,
      'specialRequests': specialRequests,
      'contactName': contactName,
      'contactPhone': contactPhone,
      'contactEmail': contactEmail,
      'flightInfo': flightInfo,
      'status': status.toString().split('.').last,
      'confirmationCode': confirmationCode,
      'totalPrice': totalPrice,
      'currency': currency,
      'paymentMethod': paymentMethod,
      'paymentTransactionId': paymentTransactionId,
      'driverId': driverId,
      'driverName': driverName,
      'driverPhone': driverPhone,
      'vehicleDetails': vehicleDetails,
      'vehicleLicensePlate': vehicleLicensePlate,
      'trackingUrl': trackingUrl,
      'cancellationReason': cancellationReason,
      'cancellationDate': cancellationDate?.toIso8601String(),
      'refundAmount': refundAmount,
      'refundDate': refundDate?.toIso8601String(),
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }
  
  /// Get the formatted pickup date and time
  String get formattedPickupDateTime {
    return DateFormat('MMM dd, yyyy - h:mm a').format(pickupDateTime);
  }
  
  /// Get the formatted total price
  String get formattedTotalPrice {
    return '$currency${totalPrice.toStringAsFixed(2)}';
  }
  
  /// Get the formatted refund amount
  String? get formattedRefundAmount {
    if (refundAmount == null) return null;
    return '$currency${refundAmount!.toStringAsFixed(2)}';
  }
  
  /// Get the formatted cancellation date
  String? get formattedCancellationDate {
    if (cancellationDate == null) return null;
    return DateFormat('MMM dd, yyyy - h:mm a').format(cancellationDate!);
  }
  
  /// Get the formatted refund date
  String? get formattedRefundDate {
    if (refundDate == null) return null;
    return DateFormat('MMM dd, yyyy - h:mm a').format(refundDate!);
  }
  
  /// Create a copy of this transfer booking with the given fields replaced with new values
  TransferBooking copyWith({
    String? id,
    String? userId,
    String? transferId,
    TransferService? transferService,
    TransferLocation? pickupLocation,
    TransferLocation? dropoffLocation,
    DateTime? pickupDateTime,
    int? passengerCount,
    int? luggageCount,
    String? specialRequests,
    String? contactName,
    String? contactPhone,
    String? contactEmail,
    String? flightInfo,
    TransferBookingStatus? status,
    String? confirmationCode,
    double? totalPrice,
    String? currency,
    String? paymentMethod,
    String? paymentTransactionId,
    String? driverId,
    String? driverName,
    String? driverPhone,
    String? vehicleDetails,
    String? vehicleLicensePlate,
    String? trackingUrl,
    String? cancellationReason,
    DateTime? cancellationDate,
    double? refundAmount,
    DateTime? refundDate,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return TransferBooking(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      transferId: transferId ?? this.transferId,
      transferService: transferService ?? this.transferService,
      pickupLocation: pickupLocation ?? this.pickupLocation,
      dropoffLocation: dropoffLocation ?? this.dropoffLocation,
      pickupDateTime: pickupDateTime ?? this.pickupDateTime,
      passengerCount: passengerCount ?? this.passengerCount,
      luggageCount: luggageCount ?? this.luggageCount,
      specialRequests: specialRequests ?? this.specialRequests,
      contactName: contactName ?? this.contactName,
      contactPhone: contactPhone ?? this.contactPhone,
      contactEmail: contactEmail ?? this.contactEmail,
      flightInfo: flightInfo ?? this.flightInfo,
      status: status ?? this.status,
      confirmationCode: confirmationCode ?? this.confirmationCode,
      totalPrice: totalPrice ?? this.totalPrice,
      currency: currency ?? this.currency,
      paymentMethod: paymentMethod ?? this.paymentMethod,
      paymentTransactionId: paymentTransactionId ?? this.paymentTransactionId,
      driverId: driverId ?? this.driverId,
      driverName: driverName ?? this.driverName,
      driverPhone: driverPhone ?? this.driverPhone,
      vehicleDetails: vehicleDetails ?? this.vehicleDetails,
      vehicleLicensePlate: vehicleLicensePlate ?? this.vehicleLicensePlate,
      trackingUrl: trackingUrl ?? this.trackingUrl,
      cancellationReason: cancellationReason ?? this.cancellationReason,
      cancellationDate: cancellationDate ?? this.cancellationDate,
      refundAmount: refundAmount ?? this.refundAmount,
      refundDate: refundDate ?? this.refundDate,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}
