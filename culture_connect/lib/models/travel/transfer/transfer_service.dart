import 'package:flutter/material.dart';
import 'package:culture_connect/models/travel/travel_service_base.dart';
import 'package:culture_connect/models/travel/transfer/transfer_vehicle.dart';
import 'package:culture_connect/models/travel/transfer/transfer_driver.dart';
import 'package:culture_connect/models/location/geo_location.dart';

/// A model representing an airport transfer service
class TransferService extends TravelService {
  /// Type of vehicle
  final TransferVehicleType vehicleType;

  /// Vehicle details
  final TransferVehicle vehicle;

  /// Driver details
  final TransferDriver? driver;

  /// Maximum passenger capacity
  final int passengerCapacity;

  /// Maximum luggage capacity
  final int luggageCapacity;

  /// Whether the service is private (not shared)
  final bool isPrivate;

  /// Whether the service includes meet and greet
  final bool includesMeetAndGreet;

  /// Whether the service includes flight tracking
  final bool includesFlightTracking;

  /// Whether the service includes waiting time
  final bool includesWaitingTime;

  /// Free waiting time in minutes
  final int freeWaitingTimeMinutes;

  /// Additional waiting time cost per hour
  final double? additionalWaitingTimeCostPerHour;

  /// Whether the service is available 24 hours
  final bool isAvailable24Hours;

  /// Minimum notice hours required for booking
  final int minimumNoticeHours;

  /// Free cancellation hours before pickup
  final int freeCancellationHours;

  /// Creates a new transfer service
  const TransferService({
    required super.id,
    required super.name,
    required super.description,
    required super.price,
    required super.currency,
    required super.rating,
    required super.reviewCount,
    required super.imageUrl,
    required super.additionalImages,
    required super.provider,
    required super.location,
    required super.coordinates,
    required super.isAvailable,
    required super.isFeatured,
    required super.isOnSale,
    super.originalPrice,
    super.discountPercentage,
    required super.tags,
    required super.amenities,
    required super.cancellationPolicy,
    required super.createdAt,
    required super.updatedAt,
    required this.vehicleType,
    required this.vehicle,
    this.driver,
    required this.passengerCapacity,
    required this.luggageCapacity,
    required this.isPrivate,
    required this.includesMeetAndGreet,
    required this.includesFlightTracking,
    required this.includesWaitingTime,
    required this.freeWaitingTimeMinutes,
    this.additionalWaitingTimeCostPerHour,
    required this.isAvailable24Hours,
    required this.minimumNoticeHours,
    required this.freeCancellationHours,
  });

  @override
  IconData get icon => Icons.airport_shuttle;

  @override
  Color get color => Colors.indigo;

  /// Get the formatted free waiting time
  String get formattedFreeWaitingTime {
    if (freeWaitingTimeMinutes < 60) {
      return '$freeWaitingTimeMinutes minutes';
    } else {
      final hours = (freeWaitingTimeMinutes / 60).floor();
      final minutes = freeWaitingTimeMinutes % 60;
      if (minutes == 0) {
        return '$hours ${hours == 1 ? 'hour' : 'hours'}';
      } else {
        return '$hours ${hours == 1 ? 'hour' : 'hours'} and $minutes minutes';
      }
    }
  }

  /// Get the formatted additional waiting time cost
  String? get formattedAdditionalWaitingTimeCost {
    if (additionalWaitingTimeCostPerHour == null) return null;
    return '$currency${additionalWaitingTimeCostPerHour!.toStringAsFixed(2)}/hour';
  }

  /// Get the formatted minimum notice
  String get formattedMinimumNotice {
    if (minimumNoticeHours < 24) {
      return '$minimumNoticeHours ${minimumNoticeHours == 1 ? 'hour' : 'hours'}';
    } else {
      final days = (minimumNoticeHours / 24).floor();
      final hours = minimumNoticeHours % 24;
      if (hours == 0) {
        return '$days ${days == 1 ? 'day' : 'days'}';
      } else {
        return '$days ${days == 1 ? 'day' : 'days'} and $hours ${hours == 1 ? 'hour' : 'hours'}';
      }
    }
  }

  /// Get the formatted free cancellation
  String get formattedFreeCancellation {
    if (freeCancellationHours < 24) {
      return '$freeCancellationHours ${freeCancellationHours == 1 ? 'hour' : 'hours'} before pickup';
    } else {
      final days = (freeCancellationHours / 24).floor();
      final hours = freeCancellationHours % 24;
      if (hours == 0) {
        return '$days ${days == 1 ? 'day' : 'days'} before pickup';
      } else {
        return '$days ${days == 1 ? 'day' : 'days'} and $hours ${hours == 1 ? 'hour' : 'hours'} before pickup';
      }
    }
  }

  /// Create a transfer service from a JSON map
  factory TransferService.fromJson(Map<String, dynamic> json) {
    return TransferService(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String,
      price: json['price'] as double,
      currency: json['currency'] as String,
      rating: json['rating'] as double,
      reviewCount: json['reviewCount'] as int,
      imageUrl: json['imageUrl'] as String,
      additionalImages: (json['additionalImages'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      provider: json['provider'] as String,
      location: json['location'] as String,
      coordinates:
          GeoLocation.fromJson(json['coordinates'] as Map<String, dynamic>),
      isAvailable: json['isAvailable'] as bool,
      isFeatured: json['isFeatured'] as bool,
      isOnSale: json['isOnSale'] as bool,
      originalPrice: json['originalPrice'] as double?,
      discountPercentage: json['discountPercentage'] as double?,
      tags: (json['tags'] as List<dynamic>).map((e) => e as String).toList(),
      amenities:
          (json['amenities'] as List<dynamic>).map((e) => e as String).toList(),
      cancellationPolicy: json['cancellationPolicy'] as String,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
      vehicleType: TransferVehicleType.values.firstWhere(
        (e) => e.toString() == 'TransferVehicleType.${json['vehicleType']}',
        orElse: () => TransferVehicleType.sedan,
      ),
      vehicle:
          TransferVehicle.fromJson(json['vehicle'] as Map<String, dynamic>),
      driver: json['driver'] != null
          ? TransferDriver.fromJson(json['driver'] as Map<String, dynamic>)
          : null,
      passengerCapacity: json['passengerCapacity'] as int,
      luggageCapacity: json['luggageCapacity'] as int,
      isPrivate: json['isPrivate'] as bool,
      includesMeetAndGreet: json['includesMeetAndGreet'] as bool,
      includesFlightTracking: json['includesFlightTracking'] as bool,
      includesWaitingTime: json['includesWaitingTime'] as bool,
      freeWaitingTimeMinutes: json['freeWaitingTimeMinutes'] as int,
      additionalWaitingTimeCostPerHour:
          json['additionalWaitingTimeCostPerHour'] as double?,
      isAvailable24Hours: json['isAvailable24Hours'] as bool,
      minimumNoticeHours: json['minimumNoticeHours'] as int,
      freeCancellationHours: json['freeCancellationHours'] as int,
    );
  }

  /// Convert the transfer service to a JSON map
  @override
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'price': price,
      'currency': currency,
      'rating': rating,
      'reviewCount': reviewCount,
      'imageUrl': imageUrl,
      'additionalImages': additionalImages,
      'provider': provider,
      'location': location,
      'coordinates': coordinates.toJson(),
      'isAvailable': isAvailable,
      'isFeatured': isFeatured,
      'isOnSale': isOnSale,
      'originalPrice': originalPrice,
      'discountPercentage': discountPercentage,
      'tags': tags,
      'amenities': amenities,
      'cancellationPolicy': cancellationPolicy,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'vehicleType': vehicleType.toString().split('.').last,
      'vehicle': vehicle.toJson(),
      'driver': driver?.toJson(),
      'passengerCapacity': passengerCapacity,
      'luggageCapacity': luggageCapacity,
      'isPrivate': isPrivate,
      'includesMeetAndGreet': includesMeetAndGreet,
      'includesFlightTracking': includesFlightTracking,
      'includesWaitingTime': includesWaitingTime,
      'freeWaitingTimeMinutes': freeWaitingTimeMinutes,
      'additionalWaitingTimeCostPerHour': additionalWaitingTimeCostPerHour,
      'isAvailable24Hours': isAvailable24Hours,
      'minimumNoticeHours': minimumNoticeHours,
      'freeCancellationHours': freeCancellationHours,
    };
  }

  /// Create a copy of this transfer service with the given fields replaced with new values
  TransferService copyWith({
    String? id,
    String? name,
    String? description,
    double? price,
    String? currency,
    double? rating,
    int? reviewCount,
    String? imageUrl,
    List<String>? additionalImages,
    String? provider,
    String? location,
    GeoLocation? coordinates,
    bool? isAvailable,
    bool? isFeatured,
    bool? isOnSale,
    double? originalPrice,
    double? discountPercentage,
    List<String>? tags,
    List<String>? amenities,
    String? cancellationPolicy,
    DateTime? createdAt,
    DateTime? updatedAt,
    TransferVehicleType? vehicleType,
    TransferVehicle? vehicle,
    TransferDriver? driver,
    int? passengerCapacity,
    int? luggageCapacity,
    bool? isPrivate,
    bool? includesMeetAndGreet,
    bool? includesFlightTracking,
    bool? includesWaitingTime,
    int? freeWaitingTimeMinutes,
    double? additionalWaitingTimeCostPerHour,
    bool? isAvailable24Hours,
    int? minimumNoticeHours,
    int? freeCancellationHours,
  }) {
    return TransferService(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      price: price ?? this.price,
      currency: currency ?? this.currency,
      rating: rating ?? this.rating,
      reviewCount: reviewCount ?? this.reviewCount,
      imageUrl: imageUrl ?? this.imageUrl,
      additionalImages: additionalImages ?? this.additionalImages,
      provider: provider ?? this.provider,
      location: location ?? this.location,
      coordinates: coordinates ?? this.coordinates,
      isAvailable: isAvailable ?? this.isAvailable,
      isFeatured: isFeatured ?? this.isFeatured,
      isOnSale: isOnSale ?? this.isOnSale,
      originalPrice: originalPrice ?? this.originalPrice,
      discountPercentage: discountPercentage ?? this.discountPercentage,
      tags: tags ?? this.tags,
      amenities: amenities ?? this.amenities,
      cancellationPolicy: cancellationPolicy ?? this.cancellationPolicy,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      vehicleType: vehicleType ?? this.vehicleType,
      vehicle: vehicle ?? this.vehicle,
      driver: driver ?? this.driver,
      passengerCapacity: passengerCapacity ?? this.passengerCapacity,
      luggageCapacity: luggageCapacity ?? this.luggageCapacity,
      isPrivate: isPrivate ?? this.isPrivate,
      includesMeetAndGreet: includesMeetAndGreet ?? this.includesMeetAndGreet,
      includesFlightTracking:
          includesFlightTracking ?? this.includesFlightTracking,
      includesWaitingTime: includesWaitingTime ?? this.includesWaitingTime,
      freeWaitingTimeMinutes:
          freeWaitingTimeMinutes ?? this.freeWaitingTimeMinutes,
      additionalWaitingTimeCostPerHour: additionalWaitingTimeCostPerHour ??
          this.additionalWaitingTimeCostPerHour,
      isAvailable24Hours: isAvailable24Hours ?? this.isAvailable24Hours,
      minimumNoticeHours: minimumNoticeHours ?? this.minimumNoticeHours,
      freeCancellationHours:
          freeCancellationHours ?? this.freeCancellationHours,
    );
  }
}
