import 'package:culture_connect/models/travel/transfer/transfers.dart';
import 'package:intl/intl.dart';

/// Extension methods for TransferBooking
extension TransferBookingExtensions on TransferBooking {
  /// Format the pickup date
  String get formattedPickupDate {
    final dateFormat = DateFormat('EEE, MMM d, yyyy');
    return dateFormat.format(pickupDateTime);
  }
  
  /// Format the pickup time
  String get formattedPickupTime {
    final timeFormat = DateFormat('h:mm a');
    return timeFormat.format(pickupDateTime);
  }
  
  /// Format the pickup date and time
  String get formattedPickupDateTime {
    final dateTimeFormat = DateFormat('EEE, MMM d, yyyy h:mm a');
    return dateTimeFormat.format(pickupDateTime);
  }
  
  /// Format the total price
  String get formattedTotalPrice {
    return '$currency${totalPrice.toStringAsFixed(2)}';
  }
}
