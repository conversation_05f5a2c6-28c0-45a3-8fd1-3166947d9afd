import 'package:flutter/material.dart';
import 'package:culture_connect/models/travel/travel.dart';

/// Sort options for car rentals
enum CarRentalSortOption {
  /// Sort by price (low to high)
  priceLowToHigh,
  
  /// Sort by price (high to low)
  priceHighToLow,
  
  /// Sort by rating (high to low)
  rating,
  
  /// Sort by popularity
  popularity,
  
  /// Sort by newest
  newest,
}

/// Extension for car rental sort options
extension CarRentalSortOptionExtension on CarRentalSortOption {
  /// Get the display name for the sort option
  String get displayName {
    switch (this) {
      case CarRentalSortOption.priceLowToHigh:
        return 'Price: Low to High';
      case CarRentalSortOption.priceHighToLow:
        return 'Price: High to Low';
      case CarRentalSortOption.rating:
        return 'Rating';
      case CarRentalSortOption.popularity:
        return 'Popularity';
      case CarRentalSortOption.newest:
        return 'Newest';
    }
  }
  
  /// Get the icon for the sort option
  IconData get icon {
    switch (this) {
      case CarRentalSortOption.priceLowToHigh:
        return Icons.arrow_upward;
      case CarRentalSortOption.priceHighToLow:
        return Icons.arrow_downward;
      case CarRentalSortOption.rating:
        return Icons.star;
      case CarRentalSortOption.popularity:
        return Icons.trending_up;
      case CarRentalSortOption.newest:
        return Icons.new_releases;
    }
  }
}

/// Filter options for car rentals
class CarRentalFilter {
  /// Selected car types
  final List<CarType>? carTypes;
  
  /// Price range (min, max)
  final RangeValues? priceRange;
  
  /// Rating filter (minimum rating)
  final double? minRating;
  
  /// Pickup date
  final DateTime? pickupDate;
  
  /// Dropoff date
  final DateTime? dropoffDate;
  
  /// Pickup location
  final String? pickupLocation;
  
  /// Transmission types
  final List<TransmissionType>? transmissionTypes;
  
  /// Fuel types
  final List<FuelType>? fuelTypes;
  
  /// Minimum number of seats
  final int? minSeats;
  
  /// Features
  final Map<String, bool>? features;
  
  /// Sort option
  final CarRentalSortOption sortOption;
  
  /// Creates a new car rental filter
  const CarRentalFilter({
    this.carTypes,
    this.priceRange,
    this.minRating,
    this.pickupDate,
    this.dropoffDate,
    this.pickupLocation,
    this.transmissionTypes,
    this.fuelTypes,
    this.minSeats,
    this.features,
    this.sortOption = CarRentalSortOption.priceLowToHigh,
  });
  
  /// Create a copy with some fields replaced
  CarRentalFilter copyWith({
    List<CarType>? carTypes,
    RangeValues? priceRange,
    double? minRating,
    DateTime? pickupDate,
    DateTime? dropoffDate,
    String? pickupLocation,
    List<TransmissionType>? transmissionTypes,
    List<FuelType>? fuelTypes,
    int? minSeats,
    Map<String, bool>? features,
    CarRentalSortOption? sortOption,
  }) {
    return CarRentalFilter(
      carTypes: carTypes ?? this.carTypes,
      priceRange: priceRange ?? this.priceRange,
      minRating: minRating ?? this.minRating,
      pickupDate: pickupDate ?? this.pickupDate,
      dropoffDate: dropoffDate ?? this.dropoffDate,
      pickupLocation: pickupLocation ?? this.pickupLocation,
      transmissionTypes: transmissionTypes ?? this.transmissionTypes,
      fuelTypes: fuelTypes ?? this.fuelTypes,
      minSeats: minSeats ?? this.minSeats,
      features: features ?? this.features,
      sortOption: sortOption ?? this.sortOption,
    );
  }
  
  /// Reset all filters
  CarRentalFilter reset() {
    return const CarRentalFilter(
      sortOption: CarRentalSortOption.priceLowToHigh,
    );
  }
  
  /// Apply the filter to a list of car rentals
  List<CarRental> apply(List<CarRental> carRentals) {
    var filtered = List<CarRental>.from(carRentals);
    
    // Filter by car types
    if (carTypes != null && carTypes!.isNotEmpty) {
      filtered = filtered.where((car) => carTypes!.contains(car.carType)).toList();
    }
    
    // Filter by price range
    if (priceRange != null) {
      filtered = filtered.where((car) => 
        car.price >= priceRange!.start && car.price <= priceRange!.end
      ).toList();
    }
    
    // Filter by rating
    if (minRating != null) {
      filtered = filtered.where((car) => car.rating >= minRating!).toList();
    }
    
    // Filter by pickup location
    if (pickupLocation != null && pickupLocation!.isNotEmpty) {
      filtered = filtered.where((car) => 
        car.pickupLocation.toLowerCase().contains(pickupLocation!.toLowerCase())
      ).toList();
    }
    
    // Filter by transmission types
    if (transmissionTypes != null && transmissionTypes!.isNotEmpty) {
      filtered = filtered.where((car) => 
        transmissionTypes!.contains(car.transmission)
      ).toList();
    }
    
    // Filter by fuel types
    if (fuelTypes != null && fuelTypes!.isNotEmpty) {
      filtered = filtered.where((car) => 
        fuelTypes!.contains(car.fuelType)
      ).toList();
    }
    
    // Filter by minimum seats
    if (minSeats != null) {
      filtered = filtered.where((car) => car.seats >= minSeats!).toList();
    }
    
    // Filter by features
    if (features != null && features!.isNotEmpty) {
      filtered = filtered.where((car) {
        if (features!['airConditioning'] == true && !car.hasAirConditioning) return false;
        if (features!['gps'] == true && !car.hasGPS) return false;
        if (features!['bluetooth'] == true && !car.hasBluetooth) return false;
        if (features!['usb'] == true && !car.hasUSB) return false;
        if (features!['sunroof'] == true && !car.hasSunroof) return false;
        return true;
      }).toList();
    }
    
    // Sort the results
    switch (sortOption) {
      case CarRentalSortOption.priceLowToHigh:
        filtered.sort((a, b) => a.price.compareTo(b.price));
        break;
      case CarRentalSortOption.priceHighToLow:
        filtered.sort((a, b) => b.price.compareTo(a.price));
        break;
      case CarRentalSortOption.rating:
        filtered.sort((a, b) => b.rating.compareTo(a.rating));
        break;
      case CarRentalSortOption.popularity:
        filtered.sort((a, b) => b.reviewCount.compareTo(a.reviewCount));
        break;
      case CarRentalSortOption.newest:
        filtered.sort((a, b) => b.year.compareTo(a.year));
        break;
    }
    
    return filtered;
  }
}
