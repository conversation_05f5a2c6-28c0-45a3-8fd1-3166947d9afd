import 'package:flutter/material.dart';

/// Enum for loyalty program types
enum LoyaltyProgramType {
  airline,
  hotel,
  carRental,
  cruise,
  restaurant,
  travel,
}

/// Extension for loyalty program types
extension LoyaltyProgramTypeExtension on LoyaltyProgramType {
  /// Get the display name for the loyalty program type
  String get displayName {
    switch (this) {
      case LoyaltyProgramType.airline:
        return 'Airline';
      case LoyaltyProgramType.hotel:
        return 'Hotel';
      case LoyaltyProgramType.carRental:
        return 'Car Rental';
      case LoyaltyProgramType.cruise:
        return 'Cruise';
      case LoyaltyProgramType.restaurant:
        return 'Restaurant';
      case LoyaltyProgramType.travel:
        return 'Travel';
    }
  }

  /// Get the icon for the loyalty program type
  IconData get icon {
    switch (this) {
      case LoyaltyProgramType.airline:
        return Icons.flight;
      case LoyaltyProgramType.hotel:
        return Icons.hotel;
      case LoyaltyProgramType.carRental:
        return Icons.directions_car;
      case LoyaltyProgramType.cruise:
        return Icons.directions_boat;
      case LoyaltyProgramType.restaurant:
        return Icons.restaurant;
      case LoyaltyProgramType.travel:
        return Icons.card_travel;
    }
  }
}

/// Enum for loyalty program tiers
enum LoyaltyProgramTier {
  basic,
  silver,
  gold,
  platinum,
  diamond,
}

/// Extension for loyalty program tiers
extension LoyaltyProgramTierExtension on LoyaltyProgramTier {
  /// Get the display name for the loyalty program tier
  String get displayName {
    switch (this) {
      case LoyaltyProgramTier.basic:
        return 'Basic';
      case LoyaltyProgramTier.silver:
        return 'Silver';
      case LoyaltyProgramTier.gold:
        return 'Gold';
      case LoyaltyProgramTier.platinum:
        return 'Platinum';
      case LoyaltyProgramTier.diamond:
        return 'Diamond';
    }
  }

  /// Get the color for the loyalty program tier
  Color get color {
    switch (this) {
      case LoyaltyProgramTier.basic:
        return Colors.grey;
      case LoyaltyProgramTier.silver:
        return Colors.grey.shade400;
      case LoyaltyProgramTier.gold:
        return Colors.amber;
      case LoyaltyProgramTier.platinum:
        return Colors.blueGrey;
      case LoyaltyProgramTier.diamond:
        return Colors.lightBlue;
    }
  }
}

/// A model representing a loyalty program benefit
class LoyaltyProgramBenefit {
  /// Unique identifier for the benefit
  final String id;

  /// Name of the benefit
  final String name;

  /// Description of the benefit
  final String description;

  /// Icon for the benefit
  final IconData icon;

  /// Tier required for the benefit
  final LoyaltyProgramTier tier;

  /// Creates a new loyalty program benefit
  const LoyaltyProgramBenefit({
    required this.id,
    required this.name,
    required this.description,
    required this.icon,
    required this.tier,
  });
}

/// A model representing a loyalty program reward
class LoyaltyProgramReward {
  /// Unique identifier for the reward
  final String id;

  /// Name of the reward
  final String name;

  /// Description of the reward
  final String description;

  /// Points required for the reward
  final int pointsRequired;

  /// URL to the image of the reward
  final String imageUrl;

  /// Whether the reward is available
  final bool isAvailable;

  /// Expiry date of the reward (if applicable)
  final DateTime? expiryDate;

  /// Creates a new loyalty program reward
  const LoyaltyProgramReward({
    required this.id,
    required this.name,
    required this.description,
    required this.pointsRequired,
    required this.imageUrl,
    required this.isAvailable,
    this.expiryDate,
  });

  /// Get the formatted expiry date
  String? get formattedExpiryDate {
    if (expiryDate == null) return null;
    return '${expiryDate!.day}/${expiryDate!.month}/${expiryDate!.year}';
  }
}

/// A model representing a loyalty program
class LoyaltyProgram {
  /// Unique identifier for the loyalty program
  final String id;

  /// Name of the loyalty program
  final String name;

  /// Description of the loyalty program
  final String description;

  /// Type of loyalty program
  final LoyaltyProgramType type;

  /// Company name
  final String companyName;

  /// Company logo URL
  final String companyLogoUrl;

  /// Current tier
  final LoyaltyProgramTier currentTier;

  /// Current points
  final int currentPoints;

  /// Points needed for next tier
  final int pointsForNextTier;

  /// Next tier
  final LoyaltyProgramTier? nextTier;

  /// Points expiry date
  final DateTime? pointsExpiryDate;

  /// Points expiry amount
  final int? pointsExpiryAmount;

  /// Membership number
  final String membershipNumber;

  /// Membership start date
  final DateTime membershipStartDate;

  /// Membership expiry date
  final DateTime? membershipExpiryDate;

  /// Benefits
  final List<LoyaltyProgramBenefit> benefits;

  /// Available rewards
  final List<LoyaltyProgramReward> availableRewards;

  /// Recent activities
  final List<LoyaltyProgramActivity> recentActivities;

  /// Creates a new loyalty program
  const LoyaltyProgram({
    required this.id,
    required this.name,
    required this.description,
    required this.type,
    required this.companyName,
    required this.companyLogoUrl,
    required this.currentTier,
    required this.currentPoints,
    required this.pointsForNextTier,
    this.nextTier,
    this.pointsExpiryDate,
    this.pointsExpiryAmount,
    required this.membershipNumber,
    required this.membershipStartDate,
    this.membershipExpiryDate,
    required this.benefits,
    required this.availableRewards,
    required this.recentActivities,
  });

  /// Get the formatted membership start date
  String get formattedMembershipStartDate {
    return '${membershipStartDate.day}/${membershipStartDate.month}/${membershipStartDate.year}';
  }

  /// Get the formatted membership expiry date
  String? get formattedMembershipExpiryDate {
    if (membershipExpiryDate == null) return null;
    return '${membershipExpiryDate!.day}/${membershipExpiryDate!.month}/${membershipExpiryDate!.year}';
  }

  /// Get the formatted points expiry date
  String? get formattedPointsExpiryDate {
    if (pointsExpiryDate == null) return null;
    return '${pointsExpiryDate!.day}/${pointsExpiryDate!.month}/${pointsExpiryDate!.year}';
  }

  /// Get the formatted points expiry
  String? get formattedPointsExpiry {
    if (pointsExpiryDate == null || pointsExpiryAmount == null) return null;
    return '$pointsExpiryAmount points expiring on ${formattedPointsExpiryDate!}';
  }

  /// Get the progress to next tier as a percentage
  double get progressToNextTier {
    if (nextTier == null || pointsForNextTier == 0) return 1.0;
    return currentPoints / pointsForNextTier;
  }

  /// Get the formatted progress to next tier
  String get formattedProgressToNextTier {
    if (nextTier == null) return 'Highest tier achieved';
    return '$currentPoints / $pointsForNextTier points to ${nextTier!.displayName}';
  }
}

/// Enum for loyalty program activity types
enum LoyaltyProgramActivityType {
  earn,
  redeem,
  tierChange,
  expiry,
  bonus,
  adjustment,
}

/// Extension for loyalty program activity types
extension LoyaltyProgramActivityTypeExtension on LoyaltyProgramActivityType {
  /// Get the display name for the loyalty program activity type
  String get displayName {
    switch (this) {
      case LoyaltyProgramActivityType.earn:
        return 'Earn';
      case LoyaltyProgramActivityType.redeem:
        return 'Redeem';
      case LoyaltyProgramActivityType.tierChange:
        return 'Tier Change';
      case LoyaltyProgramActivityType.expiry:
        return 'Expiry';
      case LoyaltyProgramActivityType.bonus:
        return 'Bonus';
      case LoyaltyProgramActivityType.adjustment:
        return 'Adjustment';
    }
  }

  /// Get the icon for the loyalty program activity type
  IconData get icon {
    switch (this) {
      case LoyaltyProgramActivityType.earn:
        return Icons.add_circle;
      case LoyaltyProgramActivityType.redeem:
        return Icons.remove_circle;
      case LoyaltyProgramActivityType.tierChange:
        return Icons.upgrade;
      case LoyaltyProgramActivityType.expiry:
        return Icons.timer_off;
      case LoyaltyProgramActivityType.bonus:
        return Icons.star;
      case LoyaltyProgramActivityType.adjustment:
        return Icons.tune;
    }
  }

  /// Get the color for the loyalty program activity type
  Color get color {
    switch (this) {
      case LoyaltyProgramActivityType.earn:
        return Colors.green;
      case LoyaltyProgramActivityType.redeem:
        return Colors.red;
      case LoyaltyProgramActivityType.tierChange:
        return Colors.blue;
      case LoyaltyProgramActivityType.expiry:
        return Colors.orange;
      case LoyaltyProgramActivityType.bonus:
        return Colors.purple;
      case LoyaltyProgramActivityType.adjustment:
        return Colors.grey;
    }
  }
}

/// A model representing a loyalty program activity
class LoyaltyProgramActivity {
  /// Unique identifier for the activity
  final String id;

  /// Type of activity
  final LoyaltyProgramActivityType type;

  /// Description of the activity
  final String description;

  /// Points earned or redeemed
  final int points;

  /// Date of the activity
  final DateTime date;

  /// Reference number
  final String? referenceNumber;

  /// Creates a new loyalty program activity
  const LoyaltyProgramActivity({
    required this.id,
    required this.type,
    required this.description,
    required this.points,
    required this.date,
    this.referenceNumber,
  });

  /// Get the formatted date
  String get formattedDate {
    return '${date.day}/${date.month}/${date.year}';
  }

  /// Get the formatted points
  String get formattedPoints {
    if (type == LoyaltyProgramActivityType.earn ||
        type == LoyaltyProgramActivityType.bonus) {
      return '+$points';
    } else if (type == LoyaltyProgramActivityType.redeem ||
        type == LoyaltyProgramActivityType.expiry) {
      return '-$points';
    } else {
      return '$points';
    }
  }
}
