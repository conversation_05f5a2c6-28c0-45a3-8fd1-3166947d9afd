import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

/// Enum representing the different types of travel documents
enum TravelDocumentType {
  /// Passport
  passport,
  
  /// Visa
  visa,
  
  /// ID card
  idCard,
  
  /// Driver's license
  driversLicense,
  
  /// Vaccination certificate
  vaccinationCertificate,
  
  /// Travel insurance
  travelInsurance,
  
  /// Other document
  other,
}

/// Extension for travel document types
extension TravelDocumentTypeExtension on TravelDocumentType {
  /// Get the display name for the travel document type
  String get displayName {
    switch (this) {
      case TravelDocumentType.passport:
        return 'Passport';
      case TravelDocumentType.visa:
        return 'Visa';
      case TravelDocumentType.idCard:
        return 'ID Card';
      case TravelDocumentType.driversLicense:
        return 'Driver\'s License';
      case TravelDocumentType.vaccinationCertificate:
        return 'Vaccination Certificate';
      case TravelDocumentType.travelInsurance:
        return 'Travel Insurance';
      case TravelDocumentType.other:
        return 'Other Document';
    }
  }
  
  /// Get the icon for the travel document type
  IconData get icon {
    switch (this) {
      case TravelDocumentType.passport:
        return Icons.book;
      case TravelDocumentType.visa:
        return Icons.verified;
      case TravelDocumentType.idCard:
        return Icons.perm_identity;
      case TravelDocumentType.driversLicense:
        return Icons.drive_eta;
      case TravelDocumentType.vaccinationCertificate:
        return Icons.health_and_safety;
      case TravelDocumentType.travelInsurance:
        return Icons.health_and_safety;
      case TravelDocumentType.other:
        return Icons.description;
    }
  }
  
  /// Get the color for the travel document type
  Color get color {
    switch (this) {
      case TravelDocumentType.passport:
        return Colors.blue;
      case TravelDocumentType.visa:
        return Colors.green;
      case TravelDocumentType.idCard:
        return Colors.orange;
      case TravelDocumentType.driversLicense:
        return Colors.purple;
      case TravelDocumentType.vaccinationCertificate:
        return Colors.teal;
      case TravelDocumentType.travelInsurance:
        return Colors.indigo;
      case TravelDocumentType.other:
        return Colors.grey;
    }
  }
}

/// Enum representing the status of a travel document
enum TravelDocumentStatus {
  /// Document is valid
  valid,
  
  /// Document is expiring soon
  expiringSoon,
  
  /// Document is expired
  expired,
  
  /// Document is pending verification
  pendingVerification,
  
  /// Document is rejected
  rejected,
}

/// Extension for travel document status
extension TravelDocumentStatusExtension on TravelDocumentStatus {
  /// Get the display name for the travel document status
  String get displayName {
    switch (this) {
      case TravelDocumentStatus.valid:
        return 'Valid';
      case TravelDocumentStatus.expiringSoon:
        return 'Expiring Soon';
      case TravelDocumentStatus.expired:
        return 'Expired';
      case TravelDocumentStatus.pendingVerification:
        return 'Pending Verification';
      case TravelDocumentStatus.rejected:
        return 'Rejected';
    }
  }
  
  /// Get the color for the travel document status
  Color get color {
    switch (this) {
      case TravelDocumentStatus.valid:
        return Colors.green;
      case TravelDocumentStatus.expiringSoon:
        return Colors.orange;
      case TravelDocumentStatus.expired:
        return Colors.red;
      case TravelDocumentStatus.pendingVerification:
        return Colors.blue;
      case TravelDocumentStatus.rejected:
        return Colors.red;
    }
  }
  
  /// Get the icon for the travel document status
  IconData get icon {
    switch (this) {
      case TravelDocumentStatus.valid:
        return Icons.check_circle;
      case TravelDocumentStatus.expiringSoon:
        return Icons.warning;
      case TravelDocumentStatus.expired:
        return Icons.error;
      case TravelDocumentStatus.pendingVerification:
        return Icons.hourglass_empty;
      case TravelDocumentStatus.rejected:
        return Icons.cancel;
    }
  }
}

/// Base class for all travel documents
abstract class TravelDocument {
  /// Unique identifier for the travel document
  final String id;
  
  /// ID of the user who owns the document
  final String userId;
  
  /// Type of travel document
  final TravelDocumentType type;
  
  /// Name of the document
  final String name;
  
  /// Document number
  final String documentNumber;
  
  /// Issuing authority
  final String issuedBy;
  
  /// Date of issue
  final DateTime issuedDate;
  
  /// Expiry date
  final DateTime expiryDate;
  
  /// Status of the document
  final TravelDocumentStatus status;
  
  /// Notes about the document
  final String? notes;
  
  /// URLs of document images
  final List<String> documentImageUrls;
  
  /// When the document was created
  final DateTime createdAt;
  
  /// When the document was last updated
  final DateTime updatedAt;
  
  /// Creates a new travel document
  const TravelDocument({
    required this.id,
    required this.userId,
    required this.type,
    required this.name,
    required this.documentNumber,
    required this.issuedBy,
    required this.issuedDate,
    required this.expiryDate,
    required this.status,
    this.notes,
    required this.documentImageUrls,
    required this.createdAt,
    required this.updatedAt,
  });
  
  /// Get the formatted issue date
  String get formattedIssuedDate {
    return DateFormat('MMM dd, yyyy').format(issuedDate);
  }
  
  /// Get the formatted expiry date
  String get formattedExpiryDate {
    return DateFormat('MMM dd, yyyy').format(expiryDate);
  }
  
  /// Get the days until expiry
  int get daysUntilExpiry {
    final now = DateTime.now();
    return expiryDate.difference(now).inDays;
  }
  
  /// Check if the document is valid
  bool get isValid {
    return expiryDate.isAfter(DateTime.now());
  }
  
  /// Check if the document is expiring soon (within 90 days)
  bool get isExpiringSoon {
    final now = DateTime.now();
    final daysUntilExpiry = expiryDate.difference(now).inDays;
    return daysUntilExpiry > 0 && daysUntilExpiry <= 90;
  }
  
  /// Check if the document is expired
  bool get isExpired {
    return expiryDate.isBefore(DateTime.now());
  }
  
  /// Get the calculated status of the document
  TravelDocumentStatus get calculatedStatus {
    if (isExpired) {
      return TravelDocumentStatus.expired;
    } else if (isExpiringSoon) {
      return TravelDocumentStatus.expiringSoon;
    } else {
      return TravelDocumentStatus.valid;
    }
  }
  
  /// Convert the travel document to a JSON map
  Map<String, dynamic> toJson();
  
  /// Create a copy of this travel document with the given fields replaced with new values
  TravelDocument copyWith({
    String? id,
    String? userId,
    TravelDocumentType? type,
    String? name,
    String? documentNumber,
    String? issuedBy,
    DateTime? issuedDate,
    DateTime? expiryDate,
    TravelDocumentStatus? status,
    String? notes,
    List<String>? documentImageUrls,
    DateTime? createdAt,
    DateTime? updatedAt,
  });
}
