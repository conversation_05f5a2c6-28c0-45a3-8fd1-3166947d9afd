/// Enum for additional service types
enum AdditionalServiceType {
  /// Extra baggage
  extraBaggage,

  /// Priority boarding
  priorityBoarding,

  /// Airport lounge access
  loungeAccess,

  /// In-flight meal
  meal,

  /// Travel insurance
  insurance,

  /// Airport transfer
  airportTransfer,

  /// Fast track security
  fastTrackSecurity,

  /// Seat selection
  seatSelection,

  /// WiFi access
  wifi,

  /// Extra legroom
  extraLegroom,

  /// Unaccompanied minor service
  unaccompaniedMinor,

  /// Pet transport
  petTransport,

  /// Special assistance
  specialAssistance,

  /// Other service
  other,
}

/// Extension for additional service types
extension AdditionalServiceTypeExtension on AdditionalServiceType {
  /// Get the display name for the additional service type
  String get displayName {
    switch (this) {
      case AdditionalServiceType.extraBaggage:
        return 'Extra Baggage';
      case AdditionalServiceType.priorityBoarding:
        return 'Priority Boarding';
      case AdditionalServiceType.loungeAccess:
        return 'Airport Lounge Access';
      case AdditionalServiceType.meal:
        return 'In-flight Meal';
      case AdditionalServiceType.insurance:
        return 'Travel Insurance';
      case AdditionalServiceType.airportTransfer:
        return 'Airport Transfer';
      case AdditionalServiceType.fastTrackSecurity:
        return 'Fast Track Security';
      case AdditionalServiceType.seatSelection:
        return 'Seat Selection';
      case AdditionalServiceType.wifi:
        return 'WiFi Access';
      case AdditionalServiceType.extraLegroom:
        return 'Extra Legroom';
      case AdditionalServiceType.unaccompaniedMinor:
        return 'Unaccompanied Minor Service';
      case AdditionalServiceType.petTransport:
        return 'Pet Transport';
      case AdditionalServiceType.specialAssistance:
        return 'Special Assistance';
      case AdditionalServiceType.other:
        return 'Other Service';
    }
  }

  /// Get the icon for the additional service type
  String get icon {
    switch (this) {
      case AdditionalServiceType.extraBaggage:
        return 'assets/icons/baggage.png';
      case AdditionalServiceType.priorityBoarding:
        return 'assets/icons/priority.png';
      case AdditionalServiceType.loungeAccess:
        return 'assets/icons/lounge.png';
      case AdditionalServiceType.meal:
        return 'assets/icons/meal.png';
      case AdditionalServiceType.insurance:
        return 'assets/icons/insurance.png';
      case AdditionalServiceType.airportTransfer:
        return 'assets/icons/transfer.png';
      case AdditionalServiceType.fastTrackSecurity:
        return 'assets/icons/security.png';
      case AdditionalServiceType.seatSelection:
        return 'assets/icons/seat.png';
      case AdditionalServiceType.wifi:
        return 'assets/icons/wifi.png';
      case AdditionalServiceType.extraLegroom:
        return 'assets/icons/legroom.png';
      case AdditionalServiceType.unaccompaniedMinor:
        return 'assets/icons/minor.png';
      case AdditionalServiceType.petTransport:
        return 'assets/icons/pet.png';
      case AdditionalServiceType.specialAssistance:
        return 'assets/icons/assistance.png';
      case AdditionalServiceType.other:
        return 'assets/icons/other.png';
    }
  }
}

/// A model representing an additional service for a flight
class AdditionalService {
  /// The service ID
  final String id;

  /// The service type
  final AdditionalServiceType type;

  /// The service name
  final String name;

  /// The service description
  final String description;

  /// The service price
  final double price;

  /// The service currency
  final String currency;

  /// Whether the service is per passenger
  final bool perPassenger;

  /// Creates a new additional service
  const AdditionalService({
    required this.id,
    required this.type,
    required this.name,
    required this.description,
    required this.price,
    required this.currency,
    required this.perPassenger,
  });

  /// Creates an additional service from JSON
  factory AdditionalService.fromJson(Map<String, dynamic> json) {
    return AdditionalService(
      id: json['id'] as String,
      type: AdditionalServiceType.values.firstWhere(
        (e) => e.toString().split('.').last == json['type'],
        orElse: () => AdditionalServiceType.other,
      ),
      name: json['name'] as String,
      description: json['description'] as String,
      price: (json['price'] as num).toDouble(),
      currency: json['currency'] as String,
      perPassenger: json['perPassenger'] as bool,
    );
  }

  /// Converts this additional service to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type.toString().split('.').last,
      'name': name,
      'description': description,
      'price': price,
      'currency': currency,
      'perPassenger': perPassenger,
    };
  }

  /// Creates a copy of this additional service with the given fields replaced
  AdditionalService copyWith({
    String? id,
    AdditionalServiceType? type,
    String? name,
    String? description,
    double? price,
    String? currency,
    bool? perPassenger,
  }) {
    return AdditionalService(
      id: id ?? this.id,
      type: type ?? this.type,
      name: name ?? this.name,
      description: description ?? this.description,
      price: price ?? this.price,
      currency: currency ?? this.currency,
      perPassenger: perPassenger ?? this.perPassenger,
    );
  }

  /// Get the formatted price
  String get formattedPrice {
    return '$currency ${price.toStringAsFixed(2)}';
  }

  /// Get the price description
  String get priceDescription {
    return perPassenger ? 'per passenger' : 'per booking';
  }
}
