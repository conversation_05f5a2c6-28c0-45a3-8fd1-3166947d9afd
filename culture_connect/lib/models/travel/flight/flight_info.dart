import 'package:flutter/material.dart';

/// Enum representing the status of a flight
enum FlightStatus {
  /// Flight is scheduled
  scheduled,

  /// Flight is boarding
  boarding,

  /// Flight is in air
  inAir,

  /// Flight has landed
  landed,

  /// Flight is delayed
  delayed,

  /// Flight is cancelled
  cancelled,

  /// Flight is diverted
  diverted,
}

/// Extension for flight status
extension FlightStatusExtension on FlightStatus {
  /// Get the display name for the flight status
  String get displayName {
    switch (this) {
      case FlightStatus.scheduled:
        return 'Scheduled';
      case FlightStatus.boarding:
        return 'Boarding';
      case FlightStatus.inAir:
        return 'In Air';
      case FlightStatus.landed:
        return 'Landed';
      case FlightStatus.delayed:
        return 'Delayed';
      case FlightStatus.cancelled:
        return 'Cancelled';
      case FlightStatus.diverted:
        return 'Diverted';
    }
  }

  /// Get the color for the flight status
  Color get color {
    switch (this) {
      case FlightStatus.scheduled:
        return Colors.blue;
      case FlightStatus.boarding:
        return Colors.orange;
      case FlightStatus.inAir:
        return Colors.green;
      case FlightStatus.landed:
        return Colors.green;
      case FlightStatus.delayed:
        return Colors.amber;
      case FlightStatus.cancelled:
        return Colors.red;
      case FlightStatus.diverted:
        return Colors.purple;
    }
  }

  /// Get the icon for the flight status
  IconData get icon {
    switch (this) {
      case FlightStatus.scheduled:
        return Icons.schedule;
      case FlightStatus.boarding:
        return Icons.airline_seat_recline_normal;
      case FlightStatus.inAir:
        return Icons.flight_takeoff;
      case FlightStatus.landed:
        return Icons.flight_land;
      case FlightStatus.delayed:
        return Icons.hourglass_empty;
      case FlightStatus.cancelled:
        return Icons.cancel;
      case FlightStatus.diverted:
        return Icons.change_circle;
    }
  }
}

/// A model representing flight information
class FlightInfo {
  /// Flight number
  final String flightNumber;

  /// Airline code
  final String airlineCode;

  /// Airline name
  final String airlineName;

  /// Departure airport code
  final String departureAirportCode;

  /// Departure airport name
  final String departureAirportName;

  /// Departure city
  final String departureCity;

  /// Departure terminal
  final String? departureTerminal;

  /// Departure gate
  final String? departureGate;

  /// Scheduled departure time
  final DateTime scheduledDepartureTime;

  /// Actual departure time
  final DateTime? actualDepartureTime;

  /// Arrival airport code
  final String arrivalAirportCode;

  /// Arrival airport name
  final String arrivalAirportName;

  /// Arrival city
  final String arrivalCity;

  /// Arrival terminal
  final String? arrivalTerminal;

  /// Arrival gate
  final String? arrivalGate;

  /// Scheduled arrival time
  final DateTime scheduledArrival;

  /// Actual arrival time
  final DateTime? actualArrival;

  /// Flight status
  final FlightStatus status;

  /// Delay in minutes
  final int? delayMinutes;

  /// Creates a new flight info
  const FlightInfo({
    required this.flightNumber,
    required this.airlineCode,
    required this.airlineName,
    required this.departureAirportCode,
    required this.departureAirportName,
    required this.departureCity,
    this.departureTerminal,
    this.departureGate,
    required this.scheduledDepartureTime,
    this.actualDepartureTime,
    required this.arrivalAirportCode,
    required this.arrivalAirportName,
    required this.arrivalCity,
    this.arrivalTerminal,
    this.arrivalGate,
    required this.scheduledArrival,
    this.actualArrival,
    required this.status,
    this.delayMinutes,
  });

  /// Create a flight info from a JSON map
  factory FlightInfo.fromJson(Map<String, dynamic> json) {
    return FlightInfo(
      flightNumber: json['flightNumber'] as String,
      airlineCode: json['airlineCode'] as String,
      airlineName: json['airlineName'] as String,
      departureAirportCode: json['departureAirportCode'] as String,
      departureAirportName: json['departureAirportName'] as String,
      departureCity: json['departureCity'] as String,
      departureTerminal: json['departureTerminal'] as String?,
      departureGate: json['departureGate'] as String?,
      scheduledDepartureTime: DateTime.parse(json['scheduledDepartureTime'] as String),
      actualDepartureTime: json['actualDepartureTime'] != null
          ? DateTime.parse(json['actualDepartureTime'] as String)
          : null,
      arrivalAirportCode: json['arrivalAirportCode'] as String,
      arrivalAirportName: json['arrivalAirportName'] as String,
      arrivalCity: json['arrivalCity'] as String,
      arrivalTerminal: json['arrivalTerminal'] as String?,
      arrivalGate: json['arrivalGate'] as String?,
      scheduledArrival: DateTime.parse(json['scheduledArrival'] as String),
      actualArrival: json['actualArrival'] != null
          ? DateTime.parse(json['actualArrival'] as String)
          : null,
      status: FlightStatus.values.firstWhere(
        (e) => e.toString() == 'FlightStatus.${json['status']}',
        orElse: () => FlightStatus.scheduled,
      ),
      delayMinutes: json['delayMinutes'] as int?,
    );
  }

  /// Convert the flight info to a JSON map
  Map<String, dynamic> toJson() {
    return {
      'flightNumber': flightNumber,
      'airlineCode': airlineCode,
      'airlineName': airlineName,
      'departureAirportCode': departureAirportCode,
      'departureAirportName': departureAirportName,
      'departureCity': departureCity,
      'departureTerminal': departureTerminal,
      'departureGate': departureGate,
      'scheduledDepartureTime': scheduledDepartureTime.toIso8601String(),
      'actualDepartureTime': actualDepartureTime?.toIso8601String(),
      'arrivalAirportCode': arrivalAirportCode,
      'arrivalAirportName': arrivalAirportName,
      'arrivalCity': arrivalCity,
      'arrivalTerminal': arrivalTerminal,
      'arrivalGate': arrivalGate,
      'scheduledArrival': scheduledArrival.toIso8601String(),
      'actualArrival': actualArrival?.toIso8601String(),
      'status': status.toString().split('.').last,
      'delayMinutes': delayMinutes,
    };
  }
}
