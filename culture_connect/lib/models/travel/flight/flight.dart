import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

/// A model representing a flight
class Flight {
  /// The flight ID
  final String id;

  /// The airline name
  final String airline;

  /// The airline code
  final String airlineCode;

  /// The flight number
  final String flightNumber;

  /// The departure airport code
  final String departureAirport;

  /// The departure city
  final String departureCity;

  /// The departure terminal
  final String? departureTerminal;

  /// The departure date and time
  final DateTime departureDateTime;

  /// The arrival airport code
  final String arrivalAirport;

  /// The arrival city
  final String arrivalCity;

  /// The arrival terminal
  final String? arrivalTerminal;

  /// The arrival date and time
  final DateTime arrivalDateTime;

  /// The duration of the flight in minutes
  final int durationMinutes;

  /// The aircraft type
  final String? aircraft;

  /// The cabin class
  final String cabinClass;

  /// The price of the flight
  final double price;

  /// The currency of the price
  final String currency;

  /// The number of stops
  final int stops;

  /// The layover airports
  final List<String>? layoverAirports;

  /// The layover durations in minutes
  final List<int>? layoverDurations;

  /// The baggage allowance
  final String? baggageAllowance;

  /// The meal included
  final bool mealIncluded;

  /// The in-flight entertainment
  final bool inFlightEntertainment;

  /// The power outlets
  final bool powerOutlets;

  /// The WiFi available
  final bool wifiAvailable;

  /// The seat map
  final Map<String, bool>? seatMap;

  /// Creates a new flight
  const Flight({
    required this.id,
    required this.airline,
    required this.airlineCode,
    required this.flightNumber,
    required this.departureAirport,
    required this.departureCity,
    this.departureTerminal,
    required this.departureDateTime,
    required this.arrivalAirport,
    required this.arrivalCity,
    this.arrivalTerminal,
    required this.arrivalDateTime,
    required this.durationMinutes,
    this.aircraft,
    required this.cabinClass,
    required this.price,
    required this.currency,
    required this.stops,
    this.layoverAirports,
    this.layoverDurations,
    this.baggageAllowance,
    required this.mealIncluded,
    required this.inFlightEntertainment,
    required this.powerOutlets,
    required this.wifiAvailable,
    this.seatMap,
  });

  /// Creates a flight from JSON
  factory Flight.fromJson(Map<String, dynamic> json) {
    return Flight(
      id: json['id'] as String,
      airline: json['airline'] as String,
      airlineCode: json['airlineCode'] as String,
      flightNumber: json['flightNumber'] as String,
      departureAirport: json['departureAirport'] as String,
      departureCity: json['departureCity'] as String,
      departureTerminal: json['departureTerminal'] as String?,
      departureDateTime: DateTime.parse(json['departureDateTime'] as String),
      arrivalAirport: json['arrivalAirport'] as String,
      arrivalCity: json['arrivalCity'] as String,
      arrivalTerminal: json['arrivalTerminal'] as String?,
      arrivalDateTime: DateTime.parse(json['arrivalDateTime'] as String),
      durationMinutes: json['durationMinutes'] as int,
      aircraft: json['aircraft'] as String?,
      cabinClass: json['cabinClass'] as String,
      price: (json['price'] as num).toDouble(),
      currency: json['currency'] as String,
      stops: json['stops'] as int,
      layoverAirports: (json['layoverAirports'] as List<dynamic>?)?.cast<String>(),
      layoverDurations: (json['layoverDurations'] as List<dynamic>?)?.cast<int>(),
      baggageAllowance: json['baggageAllowance'] as String?,
      mealIncluded: json['mealIncluded'] as bool,
      inFlightEntertainment: json['inFlightEntertainment'] as bool,
      powerOutlets: json['powerOutlets'] as bool,
      wifiAvailable: json['wifiAvailable'] as bool,
      seatMap: (json['seatMap'] as Map<String, dynamic>?)?.map(
        (k, v) => MapEntry(k, v as bool),
      ),
    );
  }

  /// Converts this flight to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'airline': airline,
      'airlineCode': airlineCode,
      'flightNumber': flightNumber,
      'departureAirport': departureAirport,
      'departureCity': departureCity,
      'departureTerminal': departureTerminal,
      'departureDateTime': departureDateTime.toIso8601String(),
      'arrivalAirport': arrivalAirport,
      'arrivalCity': arrivalCity,
      'arrivalTerminal': arrivalTerminal,
      'arrivalDateTime': arrivalDateTime.toIso8601String(),
      'durationMinutes': durationMinutes,
      'aircraft': aircraft,
      'cabinClass': cabinClass,
      'price': price,
      'currency': currency,
      'stops': stops,
      'layoverAirports': layoverAirports,
      'layoverDurations': layoverDurations,
      'baggageAllowance': baggageAllowance,
      'mealIncluded': mealIncluded,
      'inFlightEntertainment': inFlightEntertainment,
      'powerOutlets': powerOutlets,
      'wifiAvailable': wifiAvailable,
      'seatMap': seatMap,
    };
  }

  /// Creates a copy of this flight with the given fields replaced
  Flight copyWith({
    String? id,
    String? airline,
    String? airlineCode,
    String? flightNumber,
    String? departureAirport,
    String? departureCity,
    String? departureTerminal,
    DateTime? departureDateTime,
    String? arrivalAirport,
    String? arrivalCity,
    String? arrivalTerminal,
    DateTime? arrivalDateTime,
    int? durationMinutes,
    String? aircraft,
    String? cabinClass,
    double? price,
    String? currency,
    int? stops,
    List<String>? layoverAirports,
    List<int>? layoverDurations,
    String? baggageAllowance,
    bool? mealIncluded,
    bool? inFlightEntertainment,
    bool? powerOutlets,
    bool? wifiAvailable,
    Map<String, bool>? seatMap,
  }) {
    return Flight(
      id: id ?? this.id,
      airline: airline ?? this.airline,
      airlineCode: airlineCode ?? this.airlineCode,
      flightNumber: flightNumber ?? this.flightNumber,
      departureAirport: departureAirport ?? this.departureAirport,
      departureCity: departureCity ?? this.departureCity,
      departureTerminal: departureTerminal ?? this.departureTerminal,
      departureDateTime: departureDateTime ?? this.departureDateTime,
      arrivalAirport: arrivalAirport ?? this.arrivalAirport,
      arrivalCity: arrivalCity ?? this.arrivalCity,
      arrivalTerminal: arrivalTerminal ?? this.arrivalTerminal,
      arrivalDateTime: arrivalDateTime ?? this.arrivalDateTime,
      durationMinutes: durationMinutes ?? this.durationMinutes,
      aircraft: aircraft ?? this.aircraft,
      cabinClass: cabinClass ?? this.cabinClass,
      price: price ?? this.price,
      currency: currency ?? this.currency,
      stops: stops ?? this.stops,
      layoverAirports: layoverAirports ?? this.layoverAirports,
      layoverDurations: layoverDurations ?? this.layoverDurations,
      baggageAllowance: baggageAllowance ?? this.baggageAllowance,
      mealIncluded: mealIncluded ?? this.mealIncluded,
      inFlightEntertainment: inFlightEntertainment ?? this.inFlightEntertainment,
      powerOutlets: powerOutlets ?? this.powerOutlets,
      wifiAvailable: wifiAvailable ?? this.wifiAvailable,
      seatMap: seatMap ?? this.seatMap,
    );
  }

  /// Get the formatted departure date
  String get formattedDepartureDate {
    return DateFormat('EEE, MMM d, yyyy').format(departureDateTime);
  }

  /// Get the formatted departure time
  String get formattedDepartureTime {
    return DateFormat('h:mm a').format(departureDateTime);
  }

  /// Get the formatted arrival date
  String get formattedArrivalDate {
    return DateFormat('EEE, MMM d, yyyy').format(arrivalDateTime);
  }

  /// Get the formatted arrival time
  String get formattedArrivalTime {
    return DateFormat('h:mm a').format(arrivalDateTime);
  }

  /// Get the formatted duration
  String get formattedDuration {
    final hours = durationMinutes ~/ 60;
    final minutes = durationMinutes % 60;
    return '${hours}h ${minutes}m';
  }

  /// Get the formatted price
  String get formattedPrice {
    return '$currency ${price.toStringAsFixed(2)}';
  }

  /// Get the flight code
  String get flightCode {
    return '$airlineCode$flightNumber';
  }
}
