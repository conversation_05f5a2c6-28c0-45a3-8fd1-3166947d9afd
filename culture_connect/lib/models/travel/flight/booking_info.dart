import 'package:culture_connect/models/travel/flight/passenger_info.dart';
import 'package:culture_connect/models/travel/flight/flight.dart';
import 'package:culture_connect/models/travel/flight/additional_service.dart';

/// A model representing booking information for a flight
class BookingInfo {
  /// The flight being booked
  final Flight flight;

  /// The list of passengers
  final List<PassengerInfo> passengers;

  /// The selected seats (passenger ID to seat number)
  final Map<String, String> selectedSeats;

  /// The selected additional services
  final List<AdditionalService> additionalServices;

  /// The contact name
  final String contactName;

  /// The contact email
  final String contactEmail;

  /// The contact phone
  final String contactPhone;

  /// The total price
  final double totalPrice;

  /// The currency
  final String currency;

  /// The booking reference
  final String? bookingReference;

  /// The payment method
  final String? paymentMethod;

  /// The payment transaction ID
  final String? paymentTransactionId;

  /// Creates a new booking information
  const BookingInfo({
    required this.flight,
    required this.passengers,
    required this.selectedSeats,
    required this.additionalServices,
    required this.contactName,
    required this.contactEmail,
    required this.contactPhone,
    required this.totalPrice,
    required this.currency,
    this.bookingReference,
    this.paymentMethod,
    this.paymentTransactionId,
  });

  /// Creates a copy of this booking information with the given fields replaced
  BookingInfo copyWith({
    Flight? flight,
    List<PassengerInfo>? passengers,
    Map<String, String>? selectedSeats,
    List<AdditionalService>? additionalServices,
    String? contactName,
    String? contactEmail,
    String? contactPhone,
    double? totalPrice,
    String? currency,
    String? bookingReference,
    String? paymentMethod,
    String? paymentTransactionId,
  }) {
    return BookingInfo(
      flight: flight ?? this.flight,
      passengers: passengers ?? this.passengers,
      selectedSeats: selectedSeats ?? this.selectedSeats,
      additionalServices: additionalServices ?? this.additionalServices,
      contactName: contactName ?? this.contactName,
      contactEmail: contactEmail ?? this.contactEmail,
      contactPhone: contactPhone ?? this.contactPhone,
      totalPrice: totalPrice ?? this.totalPrice,
      currency: currency ?? this.currency,
      bookingReference: bookingReference ?? this.bookingReference,
      paymentMethod: paymentMethod ?? this.paymentMethod,
      paymentTransactionId: paymentTransactionId ?? this.paymentTransactionId,
    );
  }

  /// Creates a booking information from JSON
  factory BookingInfo.fromJson(Map<String, dynamic> json) {
    return BookingInfo(
      flight: Flight.fromJson(json['flight'] as Map<String, dynamic>),
      passengers: (json['passengers'] as List<dynamic>)
          .map((e) => PassengerInfo.fromJson(e as Map<String, dynamic>))
          .toList(),
      selectedSeats: Map<String, String>.from(json['selectedSeats'] as Map),
      additionalServices: (json['additionalServices'] as List<dynamic>)
          .map((e) => AdditionalService.fromJson(e as Map<String, dynamic>))
          .toList(),
      contactName: json['contactName'] as String,
      contactEmail: json['contactEmail'] as String,
      contactPhone: json['contactPhone'] as String,
      totalPrice: (json['totalPrice'] as num).toDouble(),
      currency: json['currency'] as String,
      bookingReference: json['bookingReference'] as String?,
      paymentMethod: json['paymentMethod'] as String?,
      paymentTransactionId: json['paymentTransactionId'] as String?,
    );
  }

  /// Converts this booking information to JSON
  Map<String, dynamic> toJson() {
    return {
      'flight': flight.toJson(),
      'passengers': passengers.map((e) => e.toJson()).toList(),
      'selectedSeats': selectedSeats,
      'additionalServices': additionalServices.map((e) => e.toJson()).toList(),
      'contactName': contactName,
      'contactEmail': contactEmail,
      'contactPhone': contactPhone,
      'totalPrice': totalPrice,
      'currency': currency,
      if (bookingReference != null) 'bookingReference': bookingReference,
      if (paymentMethod != null) 'paymentMethod': paymentMethod,
      if (paymentTransactionId != null)
        'paymentTransactionId': paymentTransactionId,
    };
  }

  /// Get the formatted total price
  String get formattedTotalPrice {
    return '$currency ${totalPrice.toStringAsFixed(2)}';
  }

  /// Get the number of passengers
  int get passengerCount => passengers.length;

  /// Check if all passengers have seats assigned
  bool get allPassengersHaveSeats {
    return passengers.every(
        (passenger) => selectedSeats.containsKey(passenger.passportNumber));
  }

  /// Get the total price of additional services
  double get additionalServicesTotal {
    return additionalServices.fold(0, (sum, service) => sum + service.price);
  }

  /// Get the base price (without additional services)
  double get basePrice {
    return totalPrice - additionalServicesTotal;
  }

  /// Check if the booking information is complete
  bool get isComplete {
    return passengers.isNotEmpty &&
        passengers.every((passenger) => passenger.isComplete) &&
        allPassengersHaveSeats &&
        contactName.isNotEmpty &&
        contactEmail.isNotEmpty &&
        contactPhone.isNotEmpty;
  }
}
