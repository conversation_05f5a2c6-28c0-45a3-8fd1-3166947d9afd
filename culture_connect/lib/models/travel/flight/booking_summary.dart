import 'package:culture_connect/models/travel/flight/flight.dart';
import 'package:culture_connect/models/travel/flight/passenger_info.dart';
import 'package:culture_connect/models/travel/flight/additional_service.dart';
import 'package:culture_connect/models/travel/flight/fare_breakdown.dart';

/// A model representing a booking summary for a flight
class BookingSummary {
  /// The flight being booked
  final Flight flight;

  /// The list of passengers
  final List<PassengerInfo> passengers;

  /// The selected seats (passenger ID to seat number)
  final Map<String, String> selectedSeats;

  /// The selected additional services
  final List<AdditionalService> selectedServices;

  /// The fare breakdown
  final FareBreakdown fareBreakdown;

  /// The total price
  final double totalPrice;

  /// Creates a new booking summary
  const BookingSummary({
    required this.flight,
    required this.passengers,
    required this.selectedSeats,
    required this.selectedServices,
    required this.fareBreakdown,
    required this.totalPrice,
  });

  /// Creates a copy of this booking summary with the given fields replaced
  BookingSummary copyWith({
    Flight? flight,
    List<PassengerInfo>? passengers,
    Map<String, String>? selectedSeats,
    List<AdditionalService>? selectedServices,
    FareBreakdown? fareBreakdown,
    double? totalPrice,
  }) {
    return BookingSummary(
      flight: flight ?? this.flight,
      passengers: passengers ?? this.passengers,
      selectedSeats: selectedSeats ?? this.selectedSeats,
      selectedServices: selectedServices ?? this.selectedServices,
      fareBreakdown: fareBreakdown ?? this.fareBreakdown,
      totalPrice: totalPrice ?? this.totalPrice,
    );
  }

  /// Get the formatted total price
  String get formattedTotalPrice {
    return '${flight.currency} ${totalPrice.toStringAsFixed(2)}';
  }

  /// Get the number of passengers
  int get passengerCount => passengers.length;

  /// Get the total price of additional services
  double get additionalServicesTotal {
    return selectedServices.fold(0, (sum, service) => sum + service.price);
  }

  /// Get the base price (without additional services)
  double get basePrice {
    return fareBreakdown.totalPrice;
  }

  /// Get the price per passenger
  double get pricePerPassenger {
    return totalPrice / passengerCount;
  }

  /// Get the formatted price per passenger
  String get formattedPricePerPassenger {
    return '${flight.currency} ${pricePerPassenger.toStringAsFixed(2)}';
  }

  /// Get the total taxes and fees
  double get taxesAndFees {
    return fareBreakdown.taxes + fareBreakdown.fees;
  }

  /// Get the formatted taxes and fees
  String get formattedTaxesAndFees {
    return '${flight.currency} ${taxesAndFees.toStringAsFixed(2)}';
  }

  /// Get the total discount
  double get totalDiscount {
    return fareBreakdown.discount;
  }

  /// Get the formatted total discount
  String get formattedTotalDiscount {
    return '${flight.currency} ${totalDiscount.toStringAsFixed(2)}';
  }

  /// Check if all passengers have seats assigned
  bool get allPassengersHaveSeats {
    return passengers.every(
        (passenger) => selectedSeats.containsKey(passenger.passportNumber));
  }

  /// Get the list of passengers without seats
  List<PassengerInfo> get passengersWithoutSeats {
    return passengers
        .where(
            (passenger) => !selectedSeats.containsKey(passenger.passportNumber))
        .toList();
  }

  /// Get the list of passengers with seats
  List<PassengerInfo> get passengersWithSeats {
    return passengers
        .where(
            (passenger) => selectedSeats.containsKey(passenger.passportNumber))
        .toList();
  }

  /// Get the seat for a passenger
  String? getSeatForPassenger(PassengerInfo passenger) {
    return selectedSeats[passenger.passportNumber];
  }

  /// Get the passenger for a seat
  PassengerInfo? getPassengerForSeat(String seat) {
    final passportNumber = selectedSeats.entries
        .firstWhere(
          (entry) => entry.value == seat,
          orElse: () => const MapEntry('', ''),
        )
        .key;

    if (passportNumber.isEmpty) return null;

    try {
      return passengers.firstWhere(
        (passenger) => passenger.passportNumber == passportNumber,
      );
    } catch (e) {
      // No matching passenger found
      return null;
    }
  }
}
