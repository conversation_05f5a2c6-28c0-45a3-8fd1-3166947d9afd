/// A model representing a fare breakdown for a flight
class FareBreakdown {
  /// The base fare
  final double baseFare;

  /// The taxes
  final double taxes;

  /// The fees
  final double fees;

  /// The discount
  final double discount;

  /// The total price
  final double totalPrice;

  /// The currency
  final String currency;

  /// Creates a new fare breakdown
  const FareBreakdown({
    required this.baseFare,
    required this.taxes,
    required this.fees,
    required this.discount,
    required this.totalPrice,
    required this.currency,
  });

  /// Creates a fare breakdown from JSON
  factory FareBreakdown.fromJson(Map<String, dynamic> json) {
    return FareBreakdown(
      baseFare: (json['baseFare'] as num).toDouble(),
      taxes: (json['taxes'] as num).toDouble(),
      fees: (json['fees'] as num).toDouble(),
      discount: (json['discount'] as num).toDouble(),
      totalPrice: (json['totalPrice'] as num).toDouble(),
      currency: json['currency'] as String,
    );
  }

  /// Converts this fare breakdown to JSON
  Map<String, dynamic> toJson() {
    return {
      'baseFare': baseFare,
      'taxes': taxes,
      'fees': fees,
      'discount': discount,
      'totalPrice': totalPrice,
      'currency': currency,
    };
  }

  /// Creates a copy of this fare breakdown with the given fields replaced
  FareBreakdown copyWith({
    double? baseFare,
    double? taxes,
    double? fees,
    double? discount,
    double? totalPrice,
    String? currency,
  }) {
    return FareBreakdown(
      baseFare: baseFare ?? this.baseFare,
      taxes: taxes ?? this.taxes,
      fees: fees ?? this.fees,
      discount: discount ?? this.discount,
      totalPrice: totalPrice ?? this.totalPrice,
      currency: currency ?? this.currency,
    );
  }

  /// Get the formatted base fare
  String get formattedBaseFare {
    return '$currency ${baseFare.toStringAsFixed(2)}';
  }

  /// Get the formatted taxes
  String get formattedTaxes {
    return '$currency ${taxes.toStringAsFixed(2)}';
  }

  /// Get the formatted fees
  String get formattedFees {
    return '$currency ${fees.toStringAsFixed(2)}';
  }

  /// Get the formatted discount
  String get formattedDiscount {
    return '$currency ${discount.toStringAsFixed(2)}';
  }

  /// Get the formatted total price
  String get formattedTotalPrice {
    return '$currency ${totalPrice.toStringAsFixed(2)}';
  }

  /// Get the taxes and fees
  double get taxesAndFees {
    return taxes + fees;
  }

  /// Get the formatted taxes and fees
  String get formattedTaxesAndFees {
    return '$currency ${taxesAndFees.toStringAsFixed(2)}';
  }

  /// Get the subtotal (base fare + taxes + fees)
  double get subtotal {
    return baseFare + taxes + fees;
  }

  /// Get the formatted subtotal
  String get formattedSubtotal {
    return '$currency ${subtotal.toStringAsFixed(2)}';
  }

  /// Check if there is a discount
  bool get hasDiscount {
    return discount > 0;
  }

  /// Get the discount percentage
  double get discountPercentage {
    if (subtotal == 0) return 0;
    return (discount / subtotal) * 100;
  }

  /// Get the formatted discount percentage
  String get formattedDiscountPercentage {
    return '${discountPercentage.toStringAsFixed(0)}%';
  }
}
