import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';

/// Enum representing the different types of reports
enum ReportType {
  /// Report about a guide
  guide,

  /// Report about an experience
  experience,

  /// Report about a message
  message,

  /// Report about a review
  review,

  /// Report about a user
  user,

  /// Other type of report
  other,
}

/// Extension for ReportType enum
extension ReportTypeExtension on ReportType {
  /// Get the display name for the report type
  String get typeDisplayName {
    switch (this) {
      case ReportType.guide:
        return 'Guide';
      case ReportType.experience:
        return 'Experience';
      case ReportType.message:
        return 'Message';
      case ReportType.review:
        return 'Review';
      case ReportType.user:
        return 'User';
      case ReportType.other:
        return 'Other';
    }
  }

  /// Get the icon for the report type
  IconData get icon {
    switch (this) {
      case ReportType.guide:
        return Icons.person;
      case ReportType.experience:
        return Icons.explore;
      case ReportType.message:
        return Icons.message;
      case ReportType.review:
        return Icons.rate_review;
      case ReportType.user:
        return Icons.account_circle;
      case ReportType.other:
        return Icons.report;
    }
  }
}

/// Enum representing the status of a report
enum ReportStatus {
  /// Report is pending review
  pending,

  /// Report is under investigation
  investigating,

  /// Report is resolved
  resolved,

  /// Report is dismissed
  dismissed,
}

/// Enum representing the severity of a report
enum ReportSeverity {
  /// Low severity report
  low,

  /// Medium severity report
  medium,

  /// High severity report
  high,

  /// Critical severity report
  critical,
}

/// Extension for ReportSeverity enum
extension ReportSeverityExtension on ReportSeverity {
  /// Get the display name for the severity
  String get severityDisplayName {
    switch (this) {
      case ReportSeverity.low:
        return 'Low';
      case ReportSeverity.medium:
        return 'Medium';
      case ReportSeverity.high:
        return 'High';
      case ReportSeverity.critical:
        return 'Critical';
    }
  }

  /// Get the color for the severity
  Color get color {
    switch (this) {
      case ReportSeverity.low:
        return Colors.green;
      case ReportSeverity.medium:
        return Colors.orange;
      case ReportSeverity.high:
        return Colors.red;
      case ReportSeverity.critical:
        return Colors.purple;
    }
  }

  /// Get the icon for the severity
  IconData get icon {
    switch (this) {
      case ReportSeverity.low:
        return Icons.info;
      case ReportSeverity.medium:
        return Icons.warning;
      case ReportSeverity.high:
        return Icons.error;
      case ReportSeverity.critical:
        return Icons.dangerous;
    }
  }
}

/// Model representing a report
class Report {
  /// Unique identifier for the report
  final String id;

  /// ID of the user who submitted the report
  final String reporterId;

  /// ID of the entity being reported (user, guide, experience, etc.)
  final String reportedEntityId;

  /// Type of report
  final ReportType type;

  /// Status of the report
  final ReportStatus status;

  /// Severity of the report
  final ReportSeverity severity;

  /// Reason for the report
  final String reason;

  /// Detailed description of the report
  final String description;

  /// Evidence URLs (images, videos, etc.)
  final List<String>? evidenceUrls;

  /// Date when the report was submitted
  final DateTime submittedAt;

  /// Date when the report was last updated
  final DateTime updatedAt;

  /// ID of the admin who processed the report
  final String? processedBy;

  /// Date when the report was processed
  final DateTime? processedAt;

  /// Resolution notes
  final String? resolutionNotes;

  /// Additional metadata for the report
  final Map<String, dynamic>? metadata;

  /// Creates a new report
  const Report({
    required this.id,
    required this.reporterId,
    required this.reportedEntityId,
    required this.type,
    required this.status,
    required this.severity,
    required this.reason,
    required this.description,
    this.evidenceUrls,
    required this.submittedAt,
    required this.updatedAt,
    this.processedBy,
    this.processedAt,
    this.resolutionNotes,
    this.metadata,
  });

  /// Creates a report from a JSON map
  factory Report.fromJson(Map<String, dynamic> json) {
    return Report(
      id: json['id'] as String,
      reporterId: json['reporterId'] as String,
      reportedEntityId: json['reportedEntityId'] as String,
      type: ReportType.values.firstWhere(
        (e) => e.toString().split('.').last == json['type'],
        orElse: () => ReportType.other,
      ),
      status: ReportStatus.values.firstWhere(
        (e) => e.toString().split('.').last == json['status'],
        orElse: () => ReportStatus.pending,
      ),
      severity: ReportSeverity.values.firstWhere(
        (e) => e.toString().split('.').last == json['severity'],
        orElse: () => ReportSeverity.medium,
      ),
      reason: json['reason'] as String,
      description: json['description'] as String,
      evidenceUrls: json['evidenceUrls'] != null
          ? List<String>.from(json['evidenceUrls'] as List)
          : null,
      submittedAt: (json['submittedAt'] as Timestamp).toDate(),
      updatedAt: (json['updatedAt'] as Timestamp).toDate(),
      processedBy: json['processedBy'] as String?,
      processedAt: json['processedAt'] != null
          ? (json['processedAt'] as Timestamp).toDate()
          : null,
      resolutionNotes: json['resolutionNotes'] as String?,
      metadata: json['metadata'] as Map<String, dynamic>?,
    );
  }

  /// Converts this report to a JSON map
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'reporterId': reporterId,
      'reportedEntityId': reportedEntityId,
      'type': type.toString().split('.').last,
      'status': status.toString().split('.').last,
      'severity': severity.toString().split('.').last,
      'reason': reason,
      'description': description,
      'evidenceUrls': evidenceUrls,
      'submittedAt': Timestamp.fromDate(submittedAt),
      'updatedAt': Timestamp.fromDate(updatedAt),
      'processedBy': processedBy,
      if (processedAt != null) 'processedAt': Timestamp.fromDate(processedAt!),
      'resolutionNotes': resolutionNotes,
      'metadata': metadata,
    };
  }

  /// Creates a copy of this report with the given fields replaced with the new values
  Report copyWith({
    String? id,
    String? reporterId,
    String? reportedEntityId,
    ReportType? type,
    ReportStatus? status,
    ReportSeverity? severity,
    String? reason,
    String? description,
    List<String>? evidenceUrls,
    DateTime? submittedAt,
    DateTime? updatedAt,
    String? processedBy,
    DateTime? processedAt,
    String? resolutionNotes,
    Map<String, dynamic>? metadata,
  }) {
    return Report(
      id: id ?? this.id,
      reporterId: reporterId ?? this.reporterId,
      reportedEntityId: reportedEntityId ?? this.reportedEntityId,
      type: type ?? this.type,
      status: status ?? this.status,
      severity: severity ?? this.severity,
      reason: reason ?? this.reason,
      description: description ?? this.description,
      evidenceUrls: evidenceUrls ?? this.evidenceUrls,
      submittedAt: submittedAt ?? this.submittedAt,
      updatedAt: updatedAt ?? this.updatedAt,
      processedBy: processedBy ?? this.processedBy,
      processedAt: processedAt ?? this.processedAt,
      resolutionNotes: resolutionNotes ?? this.resolutionNotes,
      metadata: metadata ?? this.metadata,
    );
  }

  /// Returns the display name for this report type
  String get typeDisplayName {
    switch (type) {
      case ReportType.guide:
        return 'Guide';
      case ReportType.experience:
        return 'Experience';
      case ReportType.message:
        return 'Message';
      case ReportType.review:
        return 'Review';
      case ReportType.user:
        return 'User';
      case ReportType.other:
        return 'Other';
    }
  }

  /// Returns the display name for this report status
  String get statusDisplayName {
    switch (status) {
      case ReportStatus.pending:
        return 'Pending';
      case ReportStatus.investigating:
        return 'Under Investigation';
      case ReportStatus.resolved:
        return 'Resolved';
      case ReportStatus.dismissed:
        return 'Dismissed';
    }
  }

  /// Returns the display name for this report severity
  String get severityDisplayName {
    switch (severity) {
      case ReportSeverity.low:
        return 'Low';
      case ReportSeverity.medium:
        return 'Medium';
      case ReportSeverity.high:
        return 'High';
      case ReportSeverity.critical:
        return 'Critical';
    }
  }
}

/// Model representing a report reason
class ReportReason {
  /// Unique identifier for the report reason
  final String id;

  /// Type of report this reason applies to
  final ReportType type;

  /// Title of the report reason
  final String title;

  /// Description of the report reason
  final String description;

  /// Whether this reason requires additional details
  final bool requiresDetails;

  /// Whether this reason allows evidence uploads
  final bool allowsEvidence;

  /// Default severity for this reason
  final ReportSeverity defaultSeverity;

  /// Creates a new report reason
  const ReportReason({
    required this.id,
    required this.type,
    required this.title,
    required this.description,
    required this.requiresDetails,
    required this.allowsEvidence,
    required this.defaultSeverity,
  });

  /// Creates a report reason from a JSON map
  factory ReportReason.fromJson(Map<String, dynamic> json) {
    return ReportReason(
      id: json['id'] as String,
      type: ReportType.values.firstWhere(
        (e) => e.toString().split('.').last == json['type'],
        orElse: () => ReportType.other,
      ),
      title: json['title'] as String,
      description: json['description'] as String,
      requiresDetails: json['requiresDetails'] as bool,
      allowsEvidence: json['allowsEvidence'] as bool,
      defaultSeverity: ReportSeverity.values.firstWhere(
        (e) => e.toString().split('.').last == json['defaultSeverity'],
        orElse: () => ReportSeverity.medium,
      ),
    );
  }

  /// Converts this report reason to a JSON map
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type.toString().split('.').last,
      'title': title,
      'description': description,
      'requiresDetails': requiresDetails,
      'allowsEvidence': allowsEvidence,
      'defaultSeverity': defaultSeverity.toString().split('.').last,
    };
  }
}
