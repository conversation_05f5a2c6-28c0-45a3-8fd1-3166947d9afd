import 'package:cloud_firestore/cloud_firestore.dart';

/// Model representing a chat between users
class ChatModel {
  /// Unique identifier for the chat
  final String id;
  
  /// List of participant user IDs
  final List<String> participants;
  
  /// Timestamp of the last message
  final DateTime lastMessageTime;
  
  /// Text of the last message
  final String lastMessageText;
  
  /// ID of the user who sent the last message
  final String lastMessageSenderId;
  
  /// Whether the chat is muted for the current user
  final bool isMuted;
  
  /// Whether the chat is pinned for the current user
  final bool isPinned;
  
  /// Custom name for the chat (if any)
  final String? customName;
  
  /// Custom avatar URL for the chat (if any)
  final String? customAvatar;
  
  /// Creates a new chat model
  const ChatModel({
    required this.id,
    required this.participants,
    required this.lastMessageTime,
    required this.lastMessageText,
    required this.lastMessageSenderId,
    this.isMuted = false,
    this.isPinned = false,
    this.customName,
    this.customAvatar,
  });
  
  /// Creates a chat model from a JSON map
  factory ChatModel.fromJson(Map<String, dynamic> json) {
    return ChatModel(
      id: json['id'] as String,
      participants: List<String>.from(json['participants'] as List),
      lastMessageTime: (json['lastMessageTime'] as Timestamp).toDate(),
      lastMessageText: json['lastMessageText'] as String? ?? '',
      lastMessageSenderId: json['lastMessageSenderId'] as String? ?? '',
      isMuted: json['isMuted'] as bool? ?? false,
      isPinned: json['isPinned'] as bool? ?? false,
      customName: json['customName'] as String?,
      customAvatar: json['customAvatar'] as String?,
    );
  }
  
  /// Converts this chat model to a JSON map
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'participants': participants,
      'lastMessageTime': Timestamp.fromDate(lastMessageTime),
      'lastMessageText': lastMessageText,
      'lastMessageSenderId': lastMessageSenderId,
      'isMuted': isMuted,
      'isPinned': isPinned,
      'customName': customName,
      'customAvatar': customAvatar,
    };
  }
  
  /// Creates a copy of this chat model with the given fields replaced with the new values
  ChatModel copyWith({
    String? id,
    List<String>? participants,
    DateTime? lastMessageTime,
    String? lastMessageText,
    String? lastMessageSenderId,
    bool? isMuted,
    bool? isPinned,
    String? customName,
    String? customAvatar,
  }) {
    return ChatModel(
      id: id ?? this.id,
      participants: participants ?? this.participants,
      lastMessageTime: lastMessageTime ?? this.lastMessageTime,
      lastMessageText: lastMessageText ?? this.lastMessageText,
      lastMessageSenderId: lastMessageSenderId ?? this.lastMessageSenderId,
      isMuted: isMuted ?? this.isMuted,
      isPinned: isPinned ?? this.isPinned,
      customName: customName ?? this.customName,
      customAvatar: customAvatar ?? this.customAvatar,
    );
  }
}
