import 'package:cloud_firestore/cloud_firestore.dart';

/// A model representing a chat between users
class ChatModel {
  /// The unique identifier of the chat
  final String id;

  /// The list of participant user IDs
  final List<String> participants;

  /// When the chat was created
  final DateTime createdAt;

  /// When the last message was sent
  final DateTime lastMessageAt;

  /// The text of the last message
  final String lastMessageText;

  /// The ID of the user who sent the last message
  final String lastMessageSenderId;

  /// The number of unread messages for the current user
  final int unreadCount;

  /// Whether the chat is active
  final bool isActive;

  /// Creates a new chat model
  const ChatModel({
    required this.id,
    required this.participants,
    required this.createdAt,
    required this.lastMessageAt,
    required this.lastMessageText,
    required this.lastMessageSenderId,
    required this.unreadCount,
    required this.isActive,
  });

  /// Creates a chat model from JSON
  factory ChatModel.fromJson(Map<String, dynamic> json) {
    return ChatModel(
      id: json['id'] as String,
      participants: List<String>.from(json['participants'] as List),
      createdAt: json['createdAt'] is Timestamp
          ? (json['createdAt'] as Timestamp).toDate()
          : DateTime.parse(json['createdAt'].toString()),
      lastMessageAt: json['lastMessageAt'] is Timestamp
          ? (json['lastMessageAt'] as Timestamp).toDate()
          : DateTime.parse(json['lastMessageAt'].toString()),
      lastMessageText: json['lastMessageText'] as String? ?? '',
      lastMessageSenderId: json['lastMessageSenderId'] as String? ?? '',
      unreadCount: json['unreadCount'] as int? ?? 0,
      isActive: json['isActive'] as bool? ?? true,
    );
  }

  /// Converts this chat model to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'participants': participants,
      'createdAt': createdAt,
      'lastMessageAt': lastMessageAt,
      'lastMessageText': lastMessageText,
      'lastMessageSenderId': lastMessageSenderId,
      'unreadCount': unreadCount,
      'isActive': isActive,
    };
  }

  /// Creates a copy of this chat model with the given fields replaced
  ChatModel copyWith({
    String? id,
    List<String>? participants,
    DateTime? createdAt,
    DateTime? lastMessageAt,
    String? lastMessageText,
    String? lastMessageSenderId,
    int? unreadCount,
    bool? isActive,
  }) {
    return ChatModel(
      id: id ?? this.id,
      participants: participants ?? this.participants,
      createdAt: createdAt ?? this.createdAt,
      lastMessageAt: lastMessageAt ?? this.lastMessageAt,
      lastMessageText: lastMessageText ?? this.lastMessageText,
      lastMessageSenderId: lastMessageSenderId ?? this.lastMessageSenderId,
      unreadCount: unreadCount ?? this.unreadCount,
      isActive: isActive ?? this.isActive,
    );
  }

  /// Checks if a user is a participant in this chat
  bool isParticipant(String userId) {
    return participants.contains(userId);
  }

  /// Gets the other participant's ID in a one-on-one chat
  String? getOtherParticipantId(String currentUserId) {
    if (participants.length != 2) {
      return null;
    }
    return participants.firstWhere((id) => id != currentUserId);
  }

  /// Checks if this is a one-on-one chat
  bool get isOneOnOne => participants.length == 2;

  /// Checks if this chat has unread messages
  bool get hasUnreadMessages => unreadCount > 0;

  /// Checks if this chat has a last message
  bool get hasLastMessage => lastMessageText.isNotEmpty;

  /// Gets the time elapsed since the last message
  Duration get timeSinceLastMessage {
    return DateTime.now().difference(lastMessageAt);
  }

  /// Gets a formatted string of the time elapsed since the last message
  String get formattedTimeSinceLastMessage {
    final duration = timeSinceLastMessage;
    
    if (duration.inDays > 365) {
      final years = (duration.inDays / 365).floor();
      return '$years ${years == 1 ? 'year' : 'years'} ago';
    } else if (duration.inDays > 30) {
      final months = (duration.inDays / 30).floor();
      return '$months ${months == 1 ? 'month' : 'months'} ago';
    } else if (duration.inDays > 0) {
      return '${duration.inDays} ${duration.inDays == 1 ? 'day' : 'days'} ago';
    } else if (duration.inHours > 0) {
      return '${duration.inHours} ${duration.inHours == 1 ? 'hour' : 'hours'} ago';
    } else if (duration.inMinutes > 0) {
      return '${duration.inMinutes} ${duration.inMinutes == 1 ? 'minute' : 'minutes'} ago';
    } else {
      return 'Just now';
    }
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ChatModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
