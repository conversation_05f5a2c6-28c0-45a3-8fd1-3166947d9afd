import 'package:flutter/foundation.dart';

/// A class representing a 3D model for AR visualization.
class ARModel {
  final String id;
  final String name;
  final String modelUrl;
  final List<String> textureUrls;
  final Map<String, dynamic> metadata;
  final ARModelType type;
  final double scale;
  final ARModelComplexity complexity;

  const ARModel({
    required this.id,
    required this.name,
    required this.modelUrl,
    this.textureUrls = const [],
    this.metadata = const {},
    this.type = ARModelType.glb,
    this.scale = 1.0,
    this.complexity = ARModelComplexity.medium,
  });

  /// Create a copy of this ARModel with the given fields replaced with the new values.
  ARModel copyWith({
    String? id,
    String? name,
    String? modelUrl,
    List<String>? textureUrls,
    Map<String, dynamic>? metadata,
    ARModelType? type,
    double? scale,
    ARModelComplexity? complexity,
  }) {
    return ARModel(
      id: id ?? this.id,
      name: name ?? this.name,
      modelUrl: modelUrl ?? this.modelUrl,
      textureUrls: textureUrls ?? this.textureUrls,
      metadata: metadata ?? this.metadata,
      type: type ?? this.type,
      scale: scale ?? this.scale,
      complexity: complexity ?? this.complexity,
    );
  }

  /// Convert this ARModel to a Map.
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'modelUrl': modelUrl,
      'textureUrls': textureUrls,
      'metadata': metadata,
      'type': type.toString().split('.').last,
      'scale': scale,
      'complexity': complexity.toString().split('.').last,
    };
  }

  /// Create an ARModel from a Map.
  factory ARModel.fromMap(Map<String, dynamic> map) {
    return ARModel(
      id: map['id'] as String,
      name: map['name'] as String,
      modelUrl: map['modelUrl'] as String,
      textureUrls: List<String>.from(map['textureUrls'] ?? []),
      metadata: Map<String, dynamic>.from(map['metadata'] ?? {}),
      type: _parseModelType(map['type']),
      scale: (map['scale'] as num?)?.toDouble() ?? 1.0,
      complexity: _parseModelComplexity(map['complexity']),
    );
  }

  /// Parse the model type from a string.
  static ARModelType _parseModelType(String? type) {
    if (type == null) return ARModelType.glb;
    
    switch (type.toLowerCase()) {
      case 'glb':
        return ARModelType.glb;
      case 'gltf':
        return ARModelType.gltf;
      case 'obj':
        return ARModelType.obj;
      case 'fbx':
        return ARModelType.fbx;
      default:
        return ARModelType.glb;
    }
  }

  /// Parse the model complexity from a string.
  static ARModelComplexity _parseModelComplexity(String? complexity) {
    if (complexity == null) return ARModelComplexity.medium;
    
    switch (complexity.toLowerCase()) {
      case 'low':
        return ARModelComplexity.low;
      case 'medium':
        return ARModelComplexity.medium;
      case 'high':
        return ARModelComplexity.high;
      default:
        return ARModelComplexity.medium;
    }
  }

  @override
  String toString() {
    return 'ARModel(id: $id, name: $name, type: $type, scale: $scale, complexity: $complexity)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
  
    return other is ARModel &&
      other.id == id &&
      other.name == name &&
      other.modelUrl == modelUrl &&
      listEquals(other.textureUrls, textureUrls) &&
      mapEquals(other.metadata, metadata) &&
      other.type == type &&
      other.scale == scale &&
      other.complexity == complexity;
  }

  @override
  int get hashCode {
    return id.hashCode ^
      name.hashCode ^
      modelUrl.hashCode ^
      textureUrls.hashCode ^
      metadata.hashCode ^
      type.hashCode ^
      scale.hashCode ^
      complexity.hashCode;
  }
}

/// The type of 3D model file.
enum ARModelType {
  glb,
  gltf,
  obj,
  fbx,
}

/// The complexity level of the 3D model.
enum ARModelComplexity {
  low,
  medium,
  high,
}
