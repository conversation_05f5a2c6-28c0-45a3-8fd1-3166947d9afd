class UserModel {
  final String id;
  final String firstName;
  final String lastName;
  final String email;
  final String phoneNumber;
  final String? dateOfBirth;
  final String userType;
  final bool isVerified;
  final int verificationLevel;
  final String status;
  final String? profilePicture;
  final String? bio;
  final List<String>? languagePreferences;
  final Map<String, dynamic>? location;
  final List<String>? culturalInterests;
  final Map<String, dynamic>? verificationDocuments;
  final double? rating;
  final String createdAt;
  final String updatedAt;
  final String lastLogin;
  final bool emailVerified;
  final bool twoFactorEnabled;

  UserModel({
    required this.id,
    required this.firstName,
    required this.lastName,
    required this.email,
    required this.phoneNumber,
    this.dateOfBirth,
    required this.userType,
    required this.isVerified,
    required this.verificationLevel,
    required this.status,
    this.profilePicture,
    this.bio,
    this.languagePreferences,
    this.location,
    this.culturalInterests,
    this.verificationDocuments,
    this.rating,
    required this.createdAt,
    required this.updatedAt,
    required this.lastLogin,
    required this.emailVerified,
    this.twoFactorEnabled = false,
  });

  String get fullName => '$firstName $lastName';

  bool get isTourist => userType == 'tourist';
  bool get isAdmin => userType == 'admin';

  factory UserModel.fromJson(Map<String, dynamic> json) {
    return UserModel(
      id: json['id'] as String,
      firstName: json['firstName'] as String,
      lastName: json['lastName'] as String,
      email: json['email'] as String,
      phoneNumber: json['phoneNumber'] as String,
      dateOfBirth: json['dateOfBirth'] as String?,
      userType: json['userType'] as String? ?? 'tourist',
      isVerified: json['isVerified'] as bool? ?? false,
      verificationLevel: json['verificationLevel'] as int? ?? 1,
      status: json['status'] as String? ?? 'active',
      profilePicture: json['profilePicture'] as String?,
      bio: json['bio'] as String?,
      languagePreferences: (json['languagePreferences'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      location: json['location'] as Map<String, dynamic>?,
      culturalInterests: (json['culturalInterests'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      verificationDocuments:
          json['verificationDocuments'] as Map<String, dynamic>?,
      rating: json['rating'] as double?,
      createdAt:
          json['createdAt'] as String? ?? DateTime.now().toIso8601String(),
      updatedAt:
          json['updatedAt'] as String? ?? DateTime.now().toIso8601String(),
      lastLogin:
          json['lastLogin'] as String? ?? DateTime.now().toIso8601String(),
      emailVerified: json['emailVerified'] as bool? ?? false,
      twoFactorEnabled: json['twoFactorEnabled'] as bool? ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'firstName': firstName,
      'lastName': lastName,
      'email': email,
      'phoneNumber': phoneNumber,
      'dateOfBirth': dateOfBirth,
      'userType': userType,
      'isVerified': isVerified,
      'verificationLevel': verificationLevel,
      'status': status,
      'profilePicture': profilePicture,
      'bio': bio,
      'languagePreferences': languagePreferences,
      'location': location,
      'culturalInterests': culturalInterests,
      'verificationDocuments': verificationDocuments,
      'rating': rating,
      'createdAt': createdAt,
      'updatedAt': updatedAt,
      'lastLogin': lastLogin,
      'emailVerified': emailVerified,
      'twoFactorEnabled': twoFactorEnabled,
    };
  }

  UserModel copyWith({
    String? id,
    String? firstName,
    String? lastName,
    String? email,
    String? phoneNumber,
    String? dateOfBirth,
    String? userType,
    bool? isVerified,
    int? verificationLevel,
    String? status,
    String? profilePicture,
    String? bio,
    List<String>? languagePreferences,
    Map<String, dynamic>? location,
    List<String>? culturalInterests,
    Map<String, dynamic>? verificationDocuments,
    double? rating,
    String? createdAt,
    String? updatedAt,
    String? lastLogin,
    bool? emailVerified,
    bool? twoFactorEnabled,
  }) {
    return UserModel(
      id: id ?? this.id,
      firstName: firstName ?? this.firstName,
      lastName: lastName ?? this.lastName,
      email: email ?? this.email,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      dateOfBirth: dateOfBirth ?? this.dateOfBirth,
      userType: userType ?? this.userType,
      isVerified: isVerified ?? this.isVerified,
      verificationLevel: verificationLevel ?? this.verificationLevel,
      status: status ?? this.status,
      profilePicture: profilePicture ?? this.profilePicture,
      bio: bio ?? this.bio,
      languagePreferences: languagePreferences ?? this.languagePreferences,
      location: location ?? this.location,
      culturalInterests: culturalInterests ?? this.culturalInterests,
      verificationDocuments:
          verificationDocuments ?? this.verificationDocuments,
      rating: rating ?? this.rating,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      lastLogin: lastLogin ?? this.lastLogin,
      emailVerified: emailVerified ?? this.emailVerified,
      twoFactorEnabled: twoFactorEnabled ?? this.twoFactorEnabled,
    );
  }
}
