enum AnimationType {
  pulse,
  rotate,
  float,
  highlight,
  bounce,
}

class LandmarkLocation {
  final double latitude;
  final double longitude;

  const LandmarkLocation({
    required this.latitude,
    required this.longitude,
  });
}

class Landmark {
  final String id;
  final String name;
  final String description;
  final String historicalSignificance;
  final String imageUrl;
  final double rating;
  final int reviewCount;
  final List<String> tags;
  final Map<String, String> translations;
  final Map<String, dynamic> location;
  final Map<String, dynamic> arContent;

  Landmark({
    required this.id,
    required this.name,
    required this.description,
    required this.historicalSignificance,
    required this.imageUrl,
    required this.rating,
    required this.reviewCount,
    required this.tags,
    required this.translations,
    required this.location,
    required this.arContent,
  });

  /// Create a Landmark from JSON.
  factory Landmark.fromJson(Map<String, dynamic> json) {
    return Landmark(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String,
      historicalSignificance: json['historicalSignificance'] as String,
      imageUrl: json['imageUrl'] as String,
      rating: (json['rating'] as num).toDouble(),
      reviewCount: json['reviewCount'] as int,
      tags: List<String>.from(json['tags'] as List),
      translations: Map<String, String>.from(json['translations'] ?? {}),
      location: json['location'] as Map<String, dynamic>,
      arContent: json['arContent'] as Map<String, dynamic>,
    );
  }

  /// Convert this Landmark to JSON.
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'historicalSignificance': historicalSignificance,
      'imageUrl': imageUrl,
      'rating': rating,
      'reviewCount': reviewCount,
      'tags': tags,
      'translations': translations,
      'location': location,
      'arContent': arContent,
    };
  }
}
