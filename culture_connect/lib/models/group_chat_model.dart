import 'package:cloud_firestore/cloud_firestore.dart';

/// Represents the role of a user in a group chat
enum GroupMemberRole {
  /// The creator of the group with full permissions
  admin,
  
  /// A moderator with some administrative permissions
  moderator,
  
  /// A regular member with basic permissions
  member,
}

/// Extension to get display name for group member role
extension GroupMemberRoleExtension on GroupMemberRole {
  String get displayName {
    switch (this) {
      case GroupMemberRole.admin:
        return 'Admin';
      case GroupMemberRole.moderator:
        return 'Moderator';
      case GroupMemberRole.member:
        return 'Member';
    }
  }
}

/// Represents a member of a group chat
class GroupMember {
  /// The user ID of the member
  final String userId;
  
  /// The role of the member in the group
  final GroupMemberRole role;
  
  /// When the member joined the group
  final DateTime joinedAt;
  
  /// Whether the member has muted the group
  final bool isMuted;
  
  /// When the member last read messages in the group
  final DateTime? lastReadAt;

  /// Creates a new group member
  const GroupMember({
    required this.userId,
    required this.role,
    required this.joinedAt,
    this.isMuted = false,
    this.lastReadAt,
  });

  /// Creates a group member from a JSON map
  factory GroupMember.fromJson(Map<String, dynamic> json) {
    return GroupMember(
      userId: json['userId'] as String,
      role: GroupMemberRole.values.firstWhere(
        (e) => e.toString() == 'GroupMemberRole.${json['role']}',
        orElse: () => GroupMemberRole.member,
      ),
      joinedAt: (json['joinedAt'] as Timestamp).toDate(),
      isMuted: json['isMuted'] as bool? ?? false,
      lastReadAt: json['lastReadAt'] != null
          ? (json['lastReadAt'] as Timestamp).toDate()
          : null,
    );
  }

  /// Converts this group member to a JSON map
  Map<String, dynamic> toJson() {
    return {
      'userId': userId,
      'role': role.toString().split('.').last,
      'joinedAt': joinedAt,
      'isMuted': isMuted,
      'lastReadAt': lastReadAt,
    };
  }

  /// Creates a copy of this group member with the given fields replaced
  GroupMember copyWith({
    String? userId,
    GroupMemberRole? role,
    DateTime? joinedAt,
    bool? isMuted,
    DateTime? lastReadAt,
  }) {
    return GroupMember(
      userId: userId ?? this.userId,
      role: role ?? this.role,
      joinedAt: joinedAt ?? this.joinedAt,
      isMuted: isMuted ?? this.isMuted,
      lastReadAt: lastReadAt ?? this.lastReadAt,
    );
  }
}

/// Represents a group chat
class GroupChatModel {
  /// The unique identifier of the group chat
  final String id;
  
  /// The name of the group chat
  final String name;
  
  /// The description of the group chat
  final String description;
  
  /// The URL of the group chat's image
  final String? imageUrl;
  
  /// The members of the group chat
  final Map<String, GroupMember> members;
  
  /// When the group chat was created
  final DateTime createdAt;
  
  /// When the last message was sent in the group chat
  final DateTime lastMessageAt;
  
  /// The text of the last message sent in the group chat
  final String lastMessageText;
  
  /// The ID of the user who sent the last message
  final String lastMessageSenderId;
  
  /// Whether the group chat is active
  final bool isActive;
  
  /// Whether the group chat is public (can be found in search)
  final bool isPublic;
  
  /// Tags associated with the group chat for discovery
  final List<String> tags;

  /// Creates a new group chat model
  const GroupChatModel({
    required this.id,
    required this.name,
    required this.description,
    this.imageUrl,
    required this.members,
    required this.createdAt,
    required this.lastMessageAt,
    required this.lastMessageText,
    required this.lastMessageSenderId,
    required this.isActive,
    this.isPublic = false,
    this.tags = const [],
  });

  /// Creates a group chat model from a JSON map
  factory GroupChatModel.fromJson(Map<String, dynamic> json) {
    final membersMap = (json['members'] as Map<String, dynamic>).map(
      (key, value) => MapEntry(
        key,
        GroupMember.fromJson(value as Map<String, dynamic>),
      ),
    );

    return GroupChatModel(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String,
      imageUrl: json['imageUrl'] as String?,
      members: membersMap,
      createdAt: (json['createdAt'] as Timestamp).toDate(),
      lastMessageAt: (json['lastMessageAt'] as Timestamp).toDate(),
      lastMessageText: json['lastMessageText'] as String? ?? '',
      lastMessageSenderId: json['lastMessageSenderId'] as String? ?? '',
      isActive: json['isActive'] as bool? ?? true,
      isPublic: json['isPublic'] as bool? ?? false,
      tags: json['tags'] != null
          ? List<String>.from(json['tags'] as List)
          : [],
    );
  }

  /// Converts this group chat model to a JSON map
  Map<String, dynamic> toJson() {
    final membersJson = <String, dynamic>{};
    members.forEach((key, value) {
      membersJson[key] = value.toJson();
    });

    return {
      'id': id,
      'name': name,
      'description': description,
      'imageUrl': imageUrl,
      'members': membersJson,
      'createdAt': createdAt,
      'lastMessageAt': lastMessageAt,
      'lastMessageText': lastMessageText,
      'lastMessageSenderId': lastMessageSenderId,
      'isActive': isActive,
      'isPublic': isPublic,
      'tags': tags,
    };
  }

  /// Creates a copy of this group chat model with the given fields replaced
  GroupChatModel copyWith({
    String? id,
    String? name,
    String? description,
    String? imageUrl,
    Map<String, GroupMember>? members,
    DateTime? createdAt,
    DateTime? lastMessageAt,
    String? lastMessageText,
    String? lastMessageSenderId,
    bool? isActive,
    bool? isPublic,
    List<String>? tags,
  }) {
    return GroupChatModel(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      imageUrl: imageUrl ?? this.imageUrl,
      members: members ?? this.members,
      createdAt: createdAt ?? this.createdAt,
      lastMessageAt: lastMessageAt ?? this.lastMessageAt,
      lastMessageText: lastMessageText ?? this.lastMessageText,
      lastMessageSenderId: lastMessageSenderId ?? this.lastMessageSenderId,
      isActive: isActive ?? this.isActive,
      isPublic: isPublic ?? this.isPublic,
      tags: tags ?? this.tags,
    );
  }

  /// Gets the number of unread messages for a user
  int getUnreadCount(String userId) {
    final member = members[userId];
    if (member == null || member.lastReadAt == null) {
      return 0;
    }
    
    // If the last message is after the last read time, there are unread messages
    if (lastMessageAt.isAfter(member.lastReadAt!)) {
      return 1; // This is a simplification; in a real app, we'd count actual unread messages
    }
    
    return 0;
  }

  /// Gets the role of a user in the group
  GroupMemberRole? getUserRole(String userId) {
    return members[userId]?.role;
  }

  /// Checks if a user is an admin of the group
  bool isUserAdmin(String userId) {
    return members[userId]?.role == GroupMemberRole.admin;
  }

  /// Checks if a user is a moderator of the group
  bool isUserModerator(String userId) {
    final role = members[userId]?.role;
    return role == GroupMemberRole.admin || role == GroupMemberRole.moderator;
  }

  /// Checks if a user is a member of the group
  bool isUserMember(String userId) {
    return members.containsKey(userId);
  }
}
