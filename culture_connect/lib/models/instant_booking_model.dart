import 'package:flutter/material.dart';
import 'package:culture_connect/models/booking_model.dart';
import 'package:culture_connect/models/payment/payment_method_model.dart';
import 'package:culture_connect/models/payment/transaction_model.dart';

/// Enum representing different instant booking statuses
enum InstantBookingStatus {
  /// Booking is being processed
  processing,
  
  /// Booking was successful
  success,
  
  /// Booking failed
  failed,
  
  /// Booking was cancelled
  cancelled,
}

/// Extension on InstantBookingStatus to provide additional functionality
extension InstantBookingStatusExtension on InstantBookingStatus {
  /// Get the display name of the instant booking status
  String get displayName {
    switch (this) {
      case InstantBookingStatus.processing:
        return 'Processing';
      case InstantBookingStatus.success:
        return 'Success';
      case InstantBookingStatus.failed:
        return 'Failed';
      case InstantBookingStatus.cancelled:
        return 'Cancelled';
    }
  }
  
  /// Get the color of the instant booking status
  Color get color {
    switch (this) {
      case InstantBookingStatus.processing:
        return Colors.blue;
      case InstantBookingStatus.success:
        return Colors.green;
      case InstantBookingStatus.failed:
        return Colors.red;
      case InstantBookingStatus.cancelled:
        return Colors.orange;
    }
  }
  
  /// Get the icon of the instant booking status
  IconData get icon {
    switch (this) {
      case InstantBookingStatus.processing:
        return Icons.hourglass_top;
      case InstantBookingStatus.success:
        return Icons.check_circle;
      case InstantBookingStatus.failed:
        return Icons.error;
      case InstantBookingStatus.cancelled:
        return Icons.cancel;
    }
  }
}

/// Enum representing different instant booking types
enum InstantBookingType {
  /// Experience booking
  experience,
  
  /// Hotel booking
  hotel,
  
  /// Flight booking
  flight,
  
  /// Car rental booking
  carRental,
  
  /// Restaurant booking
  restaurant,
  
  /// Private security booking
  privateSecurity,
  
  /// Cruise booking
  cruise,
}

/// Extension on InstantBookingType to provide additional functionality
extension InstantBookingTypeExtension on InstantBookingType {
  /// Get the display name of the instant booking type
  String get displayName {
    switch (this) {
      case InstantBookingType.experience:
        return 'Experience';
      case InstantBookingType.hotel:
        return 'Hotel';
      case InstantBookingType.flight:
        return 'Flight';
      case InstantBookingType.carRental:
        return 'Car Rental';
      case InstantBookingType.restaurant:
        return 'Restaurant';
      case InstantBookingType.privateSecurity:
        return 'Private Security';
      case InstantBookingType.cruise:
        return 'Cruise';
    }
  }
  
  /// Get the icon of the instant booking type
  IconData get icon {
    switch (this) {
      case InstantBookingType.experience:
        return Icons.explore;
      case InstantBookingType.hotel:
        return Icons.hotel;
      case InstantBookingType.flight:
        return Icons.flight;
      case InstantBookingType.carRental:
        return Icons.directions_car;
      case InstantBookingType.restaurant:
        return Icons.restaurant;
      case InstantBookingType.privateSecurity:
        return Icons.security;
      case InstantBookingType.cruise:
        return Icons.directions_boat;
    }
  }
}

/// A model representing an instant booking
class InstantBookingModel {
  /// Unique identifier for the instant booking
  final String id;
  
  /// Type of instant booking
  final InstantBookingType type;
  
  /// Status of the instant booking
  final InstantBookingStatus status;
  
  /// Service ID being booked
  final String serviceId;
  
  /// Service name being booked
  final String serviceName;
  
  /// Service image URL
  final String serviceImageUrl;
  
  /// Booking date
  final DateTime bookingDate;
  
  /// Service date
  final DateTime serviceDate;
  
  /// Number of participants/guests
  final int participantCount;
  
  /// Total amount
  final double totalAmount;
  
  /// Currency
  final String currency;
  
  /// Payment method used
  final PaymentMethodModel paymentMethod;
  
  /// Transaction details
  final TransactionModel? transaction;
  
  /// Booking details
  final BookingModel? booking;
  
  /// Error message if booking failed
  final String? errorMessage;
  
  /// Timestamp of the instant booking
  final DateTime timestamp;
  
  /// Additional details
  final Map<String, dynamic> additionalDetails;
  
  /// Creates a new instant booking model
  const InstantBookingModel({
    required this.id,
    required this.type,
    required this.status,
    required this.serviceId,
    required this.serviceName,
    required this.serviceImageUrl,
    required this.bookingDate,
    required this.serviceDate,
    required this.participantCount,
    required this.totalAmount,
    required this.currency,
    required this.paymentMethod,
    this.transaction,
    this.booking,
    this.errorMessage,
    required this.timestamp,
    this.additionalDetails = const {},
  });
  
  /// Creates a copy with some fields replaced
  InstantBookingModel copyWith({
    String? id,
    InstantBookingType? type,
    InstantBookingStatus? status,
    String? serviceId,
    String? serviceName,
    String? serviceImageUrl,
    DateTime? bookingDate,
    DateTime? serviceDate,
    int? participantCount,
    double? totalAmount,
    String? currency,
    PaymentMethodModel? paymentMethod,
    TransactionModel? transaction,
    BookingModel? booking,
    String? errorMessage,
    DateTime? timestamp,
    Map<String, dynamic>? additionalDetails,
  }) {
    return InstantBookingModel(
      id: id ?? this.id,
      type: type ?? this.type,
      status: status ?? this.status,
      serviceId: serviceId ?? this.serviceId,
      serviceName: serviceName ?? this.serviceName,
      serviceImageUrl: serviceImageUrl ?? this.serviceImageUrl,
      bookingDate: bookingDate ?? this.bookingDate,
      serviceDate: serviceDate ?? this.serviceDate,
      participantCount: participantCount ?? this.participantCount,
      totalAmount: totalAmount ?? this.totalAmount,
      currency: currency ?? this.currency,
      paymentMethod: paymentMethod ?? this.paymentMethod,
      transaction: transaction ?? this.transaction,
      booking: booking ?? this.booking,
      errorMessage: errorMessage ?? this.errorMessage,
      timestamp: timestamp ?? this.timestamp,
      additionalDetails: additionalDetails ?? this.additionalDetails,
    );
  }
  
  /// Get the formatted total amount
  String get formattedTotalAmount {
    return '$currency${totalAmount.toStringAsFixed(2)}';
  }
  
  /// Get the formatted booking date
  String get formattedBookingDate {
    return '${bookingDate.year}-${bookingDate.month.toString().padLeft(2, '0')}-${bookingDate.day.toString().padLeft(2, '0')}';
  }
  
  /// Get the formatted service date
  String get formattedServiceDate {
    return '${serviceDate.year}-${serviceDate.month.toString().padLeft(2, '0')}-${serviceDate.day.toString().padLeft(2, '0')}';
  }
  
  /// Converts to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type.index,
      'status': status.index,
      'serviceId': serviceId,
      'serviceName': serviceName,
      'serviceImageUrl': serviceImageUrl,
      'bookingDate': bookingDate.toIso8601String(),
      'serviceDate': serviceDate.toIso8601String(),
      'participantCount': participantCount,
      'totalAmount': totalAmount,
      'currency': currency,
      'paymentMethod': paymentMethod.toJson(),
      'transaction': transaction?.toJson(),
      'booking': booking?.toJson(),
      'errorMessage': errorMessage,
      'timestamp': timestamp.toIso8601String(),
      'additionalDetails': additionalDetails,
    };
  }
  
  /// Creates from JSON
  factory InstantBookingModel.fromJson(Map<String, dynamic> json) {
    return InstantBookingModel(
      id: json['id'] as String,
      type: InstantBookingType.values[json['type'] as int],
      status: InstantBookingStatus.values[json['status'] as int],
      serviceId: json['serviceId'] as String,
      serviceName: json['serviceName'] as String,
      serviceImageUrl: json['serviceImageUrl'] as String,
      bookingDate: DateTime.parse(json['bookingDate'] as String),
      serviceDate: DateTime.parse(json['serviceDate'] as String),
      participantCount: json['participantCount'] as int,
      totalAmount: json['totalAmount'] as double,
      currency: json['currency'] as String,
      paymentMethod: PaymentMethodModel.fromJson(json['paymentMethod'] as Map<String, dynamic>),
      transaction: json['transaction'] != null
          ? TransactionModel.fromJson(json['transaction'] as Map<String, dynamic>)
          : null,
      booking: json['booking'] != null
          ? BookingModel.fromJson(json['booking'] as Map<String, dynamic>)
          : null,
      errorMessage: json['errorMessage'] as String?,
      timestamp: DateTime.parse(json['timestamp'] as String),
      additionalDetails: json['additionalDetails'] as Map<String, dynamic>? ?? {},
    );
  }
}
