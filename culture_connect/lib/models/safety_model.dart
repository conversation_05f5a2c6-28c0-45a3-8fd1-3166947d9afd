import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

enum EmergencyContactType {
  personal,
  local,
  embassy,
  police,
  medical,
  other,
}

enum SafetyAlertType {
  sos,
  checkIn,
  locationShare,
  incidentReport,
  safetyTip,
}

enum SafetyAlertStatus {
  active,
  resolved,
  cancelled,
}

class EmergencyContact {
  final String id;
  final String userId;
  final String name;
  final String phoneNumber;
  final String? email;
  final String? relationship;
  final EmergencyContactType type;
  final String? notes;
  final bool isPrimary;
  final DateTime createdAt;
  final DateTime updatedAt;

  EmergencyContact({
    required this.id,
    required this.userId,
    required this.name,
    required this.phoneNumber,
    this.email,
    this.relationship,
    required this.type,
    this.notes,
    required this.isPrimary,
    required this.createdAt,
    required this.updatedAt,
  });

  factory EmergencyContact.fromJson(Map<String, dynamic> json) {
    return EmergencyContact(
      id: json['id'] as String,
      userId: json['userId'] as String,
      name: json['name'] as String,
      phoneNumber: json['phoneNumber'] as String,
      email: json['email'] as String?,
      relationship: json['relationship'] as String?,
      type: EmergencyContactType.values.firstWhere(
        (e) => e.toString() == 'EmergencyContactType.${json['type']}',
        orElse: () => EmergencyContactType.personal,
      ),
      notes: json['notes'] as String?,
      isPrimary: json['isPrimary'] as bool? ?? false,
      createdAt: (json['createdAt'] as Timestamp).toDate(),
      updatedAt: (json['updatedAt'] as Timestamp).toDate(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userId': userId,
      'name': name,
      'phoneNumber': phoneNumber,
      'email': email,
      'relationship': relationship,
      'type': type.toString().split('.').last,
      'notes': notes,
      'isPrimary': isPrimary,
      'createdAt': createdAt,
      'updatedAt': updatedAt,
    };
  }

  EmergencyContact copyWith({
    String? id,
    String? userId,
    String? name,
    String? phoneNumber,
    String? email,
    String? relationship,
    EmergencyContactType? type,
    String? notes,
    bool? isPrimary,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return EmergencyContact(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      name: name ?? this.name,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      email: email ?? this.email,
      relationship: relationship ?? this.relationship,
      type: type ?? this.type,
      notes: notes ?? this.notes,
      isPrimary: isPrimary ?? this.isPrimary,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}

class SafetyAlert {
  final String id;
  final String userId;
  final SafetyAlertType type;
  final SafetyAlertStatus status;
  final DateTime createdAt;
  final DateTime? resolvedAt;
  final LatLng location;
  final String? message;
  final List<String>? recipientIds;
  final Map<String, dynamic>? metadata;

  SafetyAlert({
    required this.id,
    required this.userId,
    required this.type,
    required this.status,
    required this.createdAt,
    this.resolvedAt,
    required this.location,
    this.message,
    this.recipientIds,
    this.metadata,
  });

  factory SafetyAlert.fromJson(Map<String, dynamic> json) {
    final GeoPoint geoPoint = json['location'] as GeoPoint;
    return SafetyAlert(
      id: json['id'] as String,
      userId: json['userId'] as String,
      type: SafetyAlertType.values.firstWhere(
        (e) => e.toString() == 'SafetyAlertType.${json['type']}',
        orElse: () => SafetyAlertType.sos,
      ),
      status: SafetyAlertStatus.values.firstWhere(
        (e) => e.toString() == 'SafetyAlertStatus.${json['status']}',
        orElse: () => SafetyAlertStatus.active,
      ),
      createdAt: (json['createdAt'] as Timestamp).toDate(),
      resolvedAt: json['resolvedAt'] != null
          ? (json['resolvedAt'] as Timestamp).toDate()
          : null,
      location: LatLng(geoPoint.latitude, geoPoint.longitude),
      message: json['message'] as String?,
      recipientIds: json['recipientIds'] != null
          ? List<String>.from(json['recipientIds'] as List)
          : null,
      metadata: json['metadata'] as Map<String, dynamic>?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userId': userId,
      'type': type.toString().split('.').last,
      'status': status.toString().split('.').last,
      'createdAt': createdAt,
      'resolvedAt': resolvedAt,
      'location': GeoPoint(location.latitude, location.longitude),
      'message': message,
      'recipientIds': recipientIds,
      'metadata': metadata,
    };
  }

  SafetyAlert copyWith({
    String? id,
    String? userId,
    SafetyAlertType? type,
    SafetyAlertStatus? status,
    DateTime? createdAt,
    DateTime? resolvedAt,
    LatLng? location,
    String? message,
    List<String>? recipientIds,
    Map<String, dynamic>? metadata,
  }) {
    return SafetyAlert(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      type: type ?? this.type,
      status: status ?? this.status,
      createdAt: createdAt ?? this.createdAt,
      resolvedAt: resolvedAt ?? this.resolvedAt,
      location: location ?? this.location,
      message: message ?? this.message,
      recipientIds: recipientIds ?? this.recipientIds,
      metadata: metadata ?? this.metadata,
    );
  }
}

class SafeZone {
  final String id;
  final String name;
  final String description;
  final LatLng location;
  final double radius; // in meters
  final String? address;
  final String? phoneNumber;
  final String? website;
  final String? imageUrl;
  final Map<String, dynamic>? metadata;
  final DateTime createdAt;
  final DateTime updatedAt;

  SafeZone({
    required this.id,
    required this.name,
    required this.description,
    required this.location,
    required this.radius,
    this.address,
    this.phoneNumber,
    this.website,
    this.imageUrl,
    this.metadata,
    required this.createdAt,
    required this.updatedAt,
  });

  factory SafeZone.fromJson(Map<String, dynamic> json) {
    final GeoPoint geoPoint = json['location'] as GeoPoint;
    return SafeZone(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String,
      location: LatLng(geoPoint.latitude, geoPoint.longitude),
      radius: json['radius'] as double,
      address: json['address'] as String?,
      phoneNumber: json['phoneNumber'] as String?,
      website: json['website'] as String?,
      imageUrl: json['imageUrl'] as String?,
      metadata: json['metadata'] as Map<String, dynamic>?,
      createdAt: (json['createdAt'] as Timestamp).toDate(),
      updatedAt: (json['updatedAt'] as Timestamp).toDate(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'location': GeoPoint(location.latitude, location.longitude),
      'radius': radius,
      'address': address,
      'phoneNumber': phoneNumber,
      'website': website,
      'imageUrl': imageUrl,
      'metadata': metadata,
      'createdAt': createdAt,
      'updatedAt': updatedAt,
    };
  }
}

class SafetyTip {
  final String id;
  final String title;
  final String content;
  final String? imageUrl;
  final String category;
  final String? countryCode;
  final String? regionCode;
  final DateTime createdAt;
  final DateTime updatedAt;

  SafetyTip({
    required this.id,
    required this.title,
    required this.content,
    this.imageUrl,
    required this.category,
    this.countryCode,
    this.regionCode,
    required this.createdAt,
    required this.updatedAt,
  });

  factory SafetyTip.fromJson(Map<String, dynamic> json) {
    return SafetyTip(
      id: json['id'] as String,
      title: json['title'] as String,
      content: json['content'] as String,
      imageUrl: json['imageUrl'] as String?,
      category: json['category'] as String,
      countryCode: json['countryCode'] as String?,
      regionCode: json['regionCode'] as String?,
      createdAt: (json['createdAt'] as Timestamp).toDate(),
      updatedAt: (json['updatedAt'] as Timestamp).toDate(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'content': content,
      'imageUrl': imageUrl,
      'category': category,
      'countryCode': countryCode,
      'regionCode': regionCode,
      'createdAt': createdAt,
      'updatedAt': updatedAt,
    };
  }
}
