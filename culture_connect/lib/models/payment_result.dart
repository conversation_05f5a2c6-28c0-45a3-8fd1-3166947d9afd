/// Represents the result of a payment transaction
class PaymentResult {
  /// Whether the payment was successful
  final bool success;
  
  /// The transaction ID from the payment provider
  final String? transactionId;
  
  /// Error message if payment failed
  final String? errorMessage;
  
  /// Additional data from the payment provider
  final Map<String, dynamic>? additionalData;

  /// Creates a new payment result
  const PaymentResult({
    required this.success,
    this.transactionId,
    this.errorMessage,
    this.additionalData,
  });

  /// Creates a successful payment result
  factory PaymentResult.success({
    required String transactionId,
    Map<String, dynamic>? additionalData,
  }) {
    return PaymentResult(
      success: true,
      transactionId: transactionId,
      additionalData: additionalData,
    );
  }

  /// Creates a failed payment result
  factory PaymentResult.failure({
    required String errorMessage,
    Map<String, dynamic>? additionalData,
  }) {
    return PaymentResult(
      success: false,
      errorMessage: errorMessage,
      additionalData: additionalData,
    );
  }
}
