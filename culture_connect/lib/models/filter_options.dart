import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart' show RangeValues;

/// Filter options for experiences
class FilterOptions {
  /// Selected category
  final String? category;

  /// Price range (min, max)
  final RangeValues? priceRange;

  /// Rating filter (minimum rating)
  final double? minRating;

  /// Duration range in hours (min, max)
  final RangeValues? durationRange;

  /// Selected languages
  final List<String>? languages;

  /// Selected locations
  final List<String>? locations;

  /// Date range for availability
  final DateTimeRange? dateRange;

  /// Number of participants
  final int? participants;

  /// Whether to show only available experiences
  final bool? onlyAvailable;

  /// Whether to show only accessible experiences
  final bool? onlyAccessible;

  /// Sort option
  final SortOption sortOption;

  /// Constructor
  const FilterOptions({
    this.category,
    this.priceRange,
    this.minRating,
    this.durationRange,
    this.languages,
    this.locations,
    this.dateRange,
    this.participants,
    this.onlyAvailable,
    this.onlyAccessible,
    this.sortOption = SortOption.recommended,
  });

  /// Create a copy with some fields replaced
  FilterOptions copyWith({
    String? category,
    RangeValues? priceRange,
    double? minRating,
    RangeValues? durationRange,
    List<String>? languages,
    List<String>? locations,
    DateTimeRange? dateRange,
    int? participants,
    bool? onlyAvailable,
    bool? onlyAccessible,
    SortOption? sortOption,
  }) {
    return FilterOptions(
      category: category ?? this.category,
      priceRange: priceRange ?? this.priceRange,
      minRating: minRating ?? this.minRating,
      durationRange: durationRange ?? this.durationRange,
      languages: languages ?? this.languages,
      locations: locations ?? this.locations,
      dateRange: dateRange ?? this.dateRange,
      participants: participants ?? this.participants,
      onlyAvailable: onlyAvailable ?? this.onlyAvailable,
      onlyAccessible: onlyAccessible ?? this.onlyAccessible,
      sortOption: sortOption ?? this.sortOption,
    );
  }

  /// Reset all filters
  FilterOptions reset() {
    return const FilterOptions();
  }

  /// Check if any filter is applied
  bool get isAnyFilterApplied {
    return category != null ||
        priceRange != null ||
        minRating != null ||
        durationRange != null ||
        (languages != null && languages!.isNotEmpty) ||
        (locations != null && locations!.isNotEmpty) ||
        dateRange != null ||
        participants != null ||
        onlyAvailable == true ||
        onlyAccessible == true ||
        sortOption != SortOption.recommended;
  }

  @override
  String toString() {
    return 'FilterOptions(category: $category, priceRange: $priceRange, minRating: $minRating, '
        'durationRange: $durationRange, languages: $languages, locations: $locations, '
        'dateRange: $dateRange, participants: $participants, onlyAvailable: $onlyAvailable, '
        'onlyAccessible: $onlyAccessible, sortOption: $sortOption)';
  }
}

/// Date range for filtering
class DateTimeRange {
  final DateTime start;
  final DateTime end;

  const DateTimeRange({
    required this.start,
    required this.end,
  });

  @override
  String toString() {
    return 'DateTimeRange(start: $start, end: $end)';
  }
}

/// Sort options for experiences
enum SortOption {
  recommended('Recommended'),
  priceHighToLow('Price: High to Low'),
  priceLowToHigh('Price: Low to High'),
  rating('Highest Rated'),
  newest('Newest'),
  popular('Most Popular'),
  distance('Nearest');

  final String label;
  const SortOption(this.label);
}
