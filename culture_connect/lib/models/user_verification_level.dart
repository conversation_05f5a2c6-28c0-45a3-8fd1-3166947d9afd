import 'package:flutter/material.dart';
import 'package:culture_connect/models/verification_model.dart';

/// Enum representing the different user verification levels
enum UserVerificationLevel {
  /// Unverified - No verifications completed
  unverified,
  
  /// Basic - Email and phone verified
  basic,
  
  /// Standard - Basic + Identity verified
  standard,
  
  /// Enhanced - Standard + Address verified
  enhanced,
  
  /// Premium - Enhanced + Background check completed
  premium,
  
  /// Certified - Premium + Professional certification
  certified,
}

/// Extension methods for UserVerificationLevel
extension UserVerificationLevelExtension on UserVerificationLevel {
  /// Get the display name for this verification level
  String get displayName {
    switch (this) {
      case UserVerificationLevel.unverified:
        return 'Unverified';
      case UserVerificationLevel.basic:
        return 'Basic Verification';
      case UserVerificationLevel.standard:
        return 'Standard Verification';
      case UserVerificationLevel.enhanced:
        return 'Enhanced Verification';
      case UserVerificationLevel.premium:
        return 'Premium Verification';
      case UserVerificationLevel.certified:
        return 'Certified Verification';
    }
  }
  
  /// Get the description for this verification level
  String get description {
    switch (this) {
      case UserVerificationLevel.unverified:
        return 'Your account has not been verified yet. Complete basic verification to unlock more features.';
      case UserVerificationLevel.basic:
        return 'Your email and phone number have been verified. Complete identity verification to unlock more features.';
      case UserVerificationLevel.standard:
        return 'Your identity has been verified. Complete address verification to unlock more features.';
      case UserVerificationLevel.enhanced:
        return 'Your address has been verified. Complete a background check to unlock more features.';
      case UserVerificationLevel.premium:
        return 'Your background check has been completed. Complete professional certification to unlock all features.';
      case UserVerificationLevel.certified:
        return 'Your professional certification has been verified. You have access to all features.';
    }
  }
  
  /// Get the icon for this verification level
  IconData get icon {
    switch (this) {
      case UserVerificationLevel.unverified:
        return Icons.person_outline;
      case UserVerificationLevel.basic:
        return Icons.check_circle_outline;
      case UserVerificationLevel.standard:
        return Icons.verified_user_outlined;
      case UserVerificationLevel.enhanced:
        return Icons.verified;
      case UserVerificationLevel.premium:
        return Icons.security;
      case UserVerificationLevel.certified:
        return Icons.workspace_premium;
    }
  }
  
  /// Get the color for this verification level
  Color get color {
    switch (this) {
      case UserVerificationLevel.unverified:
        return Colors.grey;
      case UserVerificationLevel.basic:
        return Colors.blue;
      case UserVerificationLevel.standard:
        return Colors.green;
      case UserVerificationLevel.enhanced:
        return Colors.orange;
      case UserVerificationLevel.premium:
        return Colors.purple;
      case UserVerificationLevel.certified:
        return Colors.amber;
    }
  }
  
  /// Get the required verification types for this level
  List<VerificationType> get requiredVerifications {
    switch (this) {
      case UserVerificationLevel.unverified:
        return [];
      case UserVerificationLevel.basic:
        return [VerificationType.email, VerificationType.phone];
      case UserVerificationLevel.standard:
        return [...UserVerificationLevel.basic.requiredVerifications, VerificationType.identity];
      case UserVerificationLevel.enhanced:
        return [...UserVerificationLevel.standard.requiredVerifications, VerificationType.address];
      case UserVerificationLevel.premium:
        return [...UserVerificationLevel.enhanced.requiredVerifications, VerificationType.background];
      case UserVerificationLevel.certified:
        return [...UserVerificationLevel.premium.requiredVerifications, VerificationType.professional];
    }
  }
  
  /// Get the next verification level
  UserVerificationLevel get nextLevel {
    switch (this) {
      case UserVerificationLevel.unverified:
        return UserVerificationLevel.basic;
      case UserVerificationLevel.basic:
        return UserVerificationLevel.standard;
      case UserVerificationLevel.standard:
        return UserVerificationLevel.enhanced;
      case UserVerificationLevel.enhanced:
        return UserVerificationLevel.premium;
      case UserVerificationLevel.premium:
        return UserVerificationLevel.certified;
      case UserVerificationLevel.certified:
        return UserVerificationLevel.certified; // Already at highest level
    }
  }
  
  /// Get the next verification type needed to reach the next level
  VerificationType? get nextVerificationType {
    if (this == UserVerificationLevel.certified) return null;
    
    final nextLevelVerifications = nextLevel.requiredVerifications;
    final currentLevelVerifications = requiredVerifications;
    
    for (final verificationType in nextLevelVerifications) {
      if (!currentLevelVerifications.contains(verificationType)) {
        return verificationType;
      }
    }
    
    return null;
  }
}

/// Model representing a user's verification status
class UserVerificationStatus {
  /// The user's current verification level
  final UserVerificationLevel level;
  
  /// List of completed verification badges
  final List<VerificationBadge> badges;
  
  /// List of pending verification requests
  final List<VerificationRequest> pendingRequests;
  
  /// Creates a new user verification status
  const UserVerificationStatus({
    required this.level,
    required this.badges,
    required this.pendingRequests,
  });
  
  /// Creates a user verification status from a list of badges and requests
  factory UserVerificationStatus.fromBadgesAndRequests(
    List<VerificationBadge> badges,
    List<VerificationRequest> requests,
  ) {
    // Filter to only valid badges
    final validBadges = badges.where((badge) => badge.isValid).toList();
    
    // Get all verification types that have been completed
    final completedTypes = validBadges.map((badge) => badge.type).toSet();
    
    // Determine the user's verification level based on completed verifications
    UserVerificationLevel level = UserVerificationLevel.unverified;
    
    // Check each level from highest to lowest
    for (final checkLevel in UserVerificationLevel.values.reversed) {
      final requiredTypes = checkLevel.requiredVerifications.toSet();
      if (requiredTypes.every((type) => completedTypes.contains(type))) {
        level = checkLevel;
        break;
      }
    }
    
    return UserVerificationStatus(
      level: level,
      badges: validBadges,
      pendingRequests: requests.where((req) => req.status == VerificationStatus.pending).toList(),
    );
  }
  
  /// Check if the user has a specific verification type
  bool hasVerification(VerificationType type) {
    return badges.any((badge) => badge.type == type && badge.isValid);
  }
  
  /// Check if the user has a pending request for a specific verification type
  bool hasPendingRequest(VerificationType type) {
    return pendingRequests.any((req) => req.type == type);
  }
  
  /// Get the verification badge for a specific type
  VerificationBadge? getBadge(VerificationType type) {
    try {
      return badges.firstWhere((badge) => badge.type == type && badge.isValid);
    } catch (e) {
      return null;
    }
  }
  
  /// Get the pending request for a specific type
  VerificationRequest? getPendingRequest(VerificationType type) {
    try {
      return pendingRequests.firstWhere((req) => req.type == type);
    } catch (e) {
      return null;
    }
  }
  
  /// Check if the user can upgrade to the next level
  bool get canUpgrade {
    if (level == UserVerificationLevel.certified) return false;
    
    final nextLevelVerifications = level.nextLevel.requiredVerifications;
    final completedTypes = badges.where((badge) => badge.isValid).map((badge) => badge.type).toSet();
    
    return nextLevelVerifications.every((type) => completedTypes.contains(type));
  }
  
  /// Get the progress percentage towards the next level
  double get nextLevelProgress {
    if (level == UserVerificationLevel.certified) return 1.0;
    
    final nextLevelVerifications = level.nextLevel.requiredVerifications;
    final completedTypes = badges.where((badge) => badge.isValid).map((badge) => badge.type).toSet();
    
    int completed = 0;
    for (final type in nextLevelVerifications) {
      if (completedTypes.contains(type)) {
        completed++;
      }
    }
    
    return completed / nextLevelVerifications.length;
  }
}
