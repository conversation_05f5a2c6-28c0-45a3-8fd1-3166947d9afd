import 'dart:convert';
import 'package:culture_connect/models/translation/voice_translation_model.dart';
import 'package:flutter/foundation.dart';

/// Role in a conversation
enum ConversationRole {
  /// The user (you)
  user,

  /// The other participant
  other,

  /// System message
  system,
}

/// Status of a conversation
enum ConversationStatus {
  /// Active conversation
  active,

  /// Paused conversation
  paused,

  /// Ended conversation
  ended,

  /// Error in conversation
  error,
}

/// Mode for conversation listening
enum ConversationListeningMode {
  /// Manual mode (press to talk)
  manual,

  /// Continuous mode (automatically listens)
  continuous,

  /// Turn-based mode (automatically switches between speakers)
  turnBased,
}

/// Export format for conversations
enum ConversationExportFormat {
  /// Plain text format
  text,

  /// PDF format
  pdf,

  /// JSON format
  json,
}

/// A model representing a conversation turn
class ConversationTurn {
  /// The unique identifier for the turn
  final String id;

  /// The role of the speaker
  final ConversationRole role;

  /// The translation model for this turn
  final VoiceTranslationModel translation;

  /// When the turn was created
  final DateTime timestamp;

  /// Creates a new conversation turn
  const ConversationTurn({
    required this.id,
    required this.role,
    required this.translation,
    required this.timestamp,
  });

  /// Creates a copy with some fields replaced
  ConversationTurn copyWith({
    String? id,
    ConversationRole? role,
    VoiceTranslationModel? translation,
    DateTime? timestamp,
  }) {
    return ConversationTurn(
      id: id ?? this.id,
      role: role ?? this.role,
      translation: translation ?? this.translation,
      timestamp: timestamp ?? this.timestamp,
    );
  }

  /// Converts to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'role': role.index,
      'translation': translation.toJson(),
      'timestamp': timestamp.toIso8601String(),
    };
  }

  /// Creates from JSON
  factory ConversationTurn.fromJson(Map<String, dynamic> json) {
    return ConversationTurn(
      id: json['id'] as String,
      role: ConversationRole.values[json['role'] as int],
      translation: VoiceTranslationModel.fromJson(
        json['translation'] as Map<String, dynamic>,
      ),
      timestamp: DateTime.parse(json['timestamp'] as String),
    );
  }
}

/// A model representing a conversation
class ConversationModel {
  /// The unique identifier for the conversation
  final String id;

  /// The title of the conversation
  final String title;

  /// The user's language code
  final String userLanguageCode;

  /// The other participant's language code
  final String otherLanguageCode;

  /// The turns in the conversation
  final List<ConversationTurn> turns;

  /// The status of the conversation
  final ConversationStatus status;

  /// When the conversation was created
  final DateTime createdAt;

  /// When the conversation was last updated
  final DateTime updatedAt;

  /// Whether the conversation is favorited
  final bool isFavorite;

  /// Whether automatic language detection is enabled
  final bool autoDetectLanguage;

  /// Whether speaker identification is enabled
  final bool speakerIdentification;

  /// The listening mode for the conversation
  final ConversationListeningMode listeningMode;

  /// The last active speaker role
  final ConversationRole lastActiveRole;

  /// The settings for the conversation
  final ConversationSettings settings;

  /// Creates a new conversation model
  const ConversationModel({
    required this.id,
    required this.title,
    required this.userLanguageCode,
    required this.otherLanguageCode,
    required this.turns,
    required this.status,
    required this.createdAt,
    required this.updatedAt,
    this.isFavorite = false,
    this.autoDetectLanguage = false,
    this.speakerIdentification = false,
    this.listeningMode = ConversationListeningMode.manual,
    this.lastActiveRole = ConversationRole.user,
    this.settings = const ConversationSettings(),
  });

  /// Creates a copy with some fields replaced
  ConversationModel copyWith({
    String? id,
    String? title,
    String? userLanguageCode,
    String? otherLanguageCode,
    List<ConversationTurn>? turns,
    ConversationStatus? status,
    DateTime? createdAt,
    DateTime? updatedAt,
    bool? isFavorite,
    bool? autoDetectLanguage,
    bool? speakerIdentification,
    ConversationListeningMode? listeningMode,
    ConversationRole? lastActiveRole,
    ConversationSettings? settings,
  }) {
    return ConversationModel(
      id: id ?? this.id,
      title: title ?? this.title,
      userLanguageCode: userLanguageCode ?? this.userLanguageCode,
      otherLanguageCode: otherLanguageCode ?? this.otherLanguageCode,
      turns: turns ?? this.turns,
      status: status ?? this.status,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      isFavorite: isFavorite ?? this.isFavorite,
      autoDetectLanguage: autoDetectLanguage ?? this.autoDetectLanguage,
      speakerIdentification:
          speakerIdentification ?? this.speakerIdentification,
      listeningMode: listeningMode ?? this.listeningMode,
      lastActiveRole: lastActiveRole ?? this.lastActiveRole,
      settings: settings ?? this.settings,
    );
  }

  /// Add a turn to the conversation
  ConversationModel addTurn(ConversationTurn turn) {
    final newTurns = List<ConversationTurn>.from(turns)..add(turn);
    return copyWith(
      turns: newTurns,
      updatedAt: DateTime.now(),
    );
  }

  /// Get the last turn in the conversation
  ConversationTurn? get lastTurn {
    if (turns.isEmpty) return null;
    return turns.last;
  }

  /// Get the duration of the conversation
  Duration get duration {
    if (turns.isEmpty) return Duration.zero;
    final firstTurn = turns.first.timestamp;
    final lastTurn = turns.last.timestamp;
    return lastTurn.difference(firstTurn);
  }

  /// Get the formatted duration
  String get formattedDuration {
    final duration = this.duration;
    final minutes = duration.inMinutes;
    final seconds = duration.inSeconds % 60;
    return '$minutes:${seconds.toString().padLeft(2, '0')}';
  }

  /// Get the word count of the conversation
  int get wordCount {
    int count = 0;
    for (final turn in turns) {
      if (turn.translation.originalText != null) {
        count += turn.translation.originalText!.split(' ').length;
      }
      if (turn.translation.translatedText != null) {
        count += turn.translation.translatedText!.split(' ').length;
      }
    }
    return count;
  }

  /// Get the turn count by role
  Map<ConversationRole, int> get turnCountByRole {
    final counts = <ConversationRole, int>{};
    for (final role in ConversationRole.values) {
      counts[role] = 0;
    }

    for (final turn in turns) {
      counts[turn.role] = (counts[turn.role] ?? 0) + 1;
    }

    return counts;
  }

  /// Get the next role in the conversation (for turn-based mode)
  ConversationRole getNextRole(ConversationRole currentRole) {
    if (currentRole == ConversationRole.user) {
      return ConversationRole.other;
    } else if (currentRole == ConversationRole.other) {
      return ConversationRole.user;
    } else {
      return ConversationRole.user; // Default to user for system messages
    }
  }

  /// Check if the conversation can be exported
  bool get canExport => turns.isNotEmpty;

  /// Export the conversation to plain text
  String exportToText() {
    if (turns.isEmpty) return '';

    final buffer = StringBuffer();
    buffer.writeln('Conversation: $title');
    buffer.writeln('Date: ${createdAt.toLocal().toString().split('.')[0]}');
    buffer.writeln('Duration: $formattedDuration');
    buffer.writeln('Languages: $userLanguageCode <-> $otherLanguageCode');
    buffer.writeln('');

    for (final turn in turns) {
      final speaker = turn.role == ConversationRole.user ? 'You' : 'Other';
      final time =
          turn.timestamp.toLocal().toString().split(' ')[1].split('.')[0];

      buffer.writeln('[$time] $speaker:');
      if (turn.translation.originalText != null) {
        buffer.writeln('  ${turn.translation.originalText}');
      }
      if (turn.translation.translatedText != null) {
        buffer.writeln('  (Translated: ${turn.translation.translatedText})');
      }
      buffer.writeln('');
    }

    return buffer.toString();
  }

  /// Export the conversation to JSON
  String exportToJson() {
    return jsonEncode(toJson());
  }

  /// Converts to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'userLanguageCode': userLanguageCode,
      'otherLanguageCode': otherLanguageCode,
      'turns': turns.map((turn) => turn.toJson()).toList(),
      'status': status.index,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'isFavorite': isFavorite,
      'autoDetectLanguage': autoDetectLanguage,
      'speakerIdentification': speakerIdentification,
      'listeningMode': listeningMode.index,
      'lastActiveRole': lastActiveRole.index,
      'settings': settings.toJson(),
    };
  }

  /// Creates from JSON
  factory ConversationModel.fromJson(Map<String, dynamic> json) {
    return ConversationModel(
      id: json['id'] as String,
      title: json['title'] as String,
      userLanguageCode: json['userLanguageCode'] as String,
      otherLanguageCode: json['otherLanguageCode'] as String,
      turns: (json['turns'] as List)
          .map(
              (turn) => ConversationTurn.fromJson(turn as Map<String, dynamic>))
          .toList(),
      status: ConversationStatus.values[json['status'] as int],
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
      isFavorite: json['isFavorite'] as bool? ?? false,
      autoDetectLanguage: json['autoDetectLanguage'] as bool? ?? false,
      speakerIdentification: json['speakerIdentification'] as bool? ?? false,
      listeningMode: json['listeningMode'] != null
          ? ConversationListeningMode.values[json['listeningMode'] as int]
          : ConversationListeningMode.manual,
      lastActiveRole: json['lastActiveRole'] != null
          ? ConversationRole.values[json['lastActiveRole'] as int]
          : ConversationRole.user,
      settings: json['settings'] != null
          ? ConversationSettings.fromJson(
              json['settings'] as Map<String, dynamic>)
          : const ConversationSettings(),
    );
  }
}

/// Settings for a conversation
class ConversationSettings {
  /// Whether to use continuous listening mode
  final bool useContinuousListening;

  /// Whether to automatically switch between speakers
  final bool autoSwitchSpeakers;

  /// Whether to automatically detect languages
  final bool autoDetectLanguages;

  /// Whether to show cultural context information
  final bool showCulturalContext;

  /// Whether to show slang and idiom information
  final bool showSlangIdiom;

  /// Whether to show pronunciation guidance
  final bool showPronunciation;

  /// Whether to use offline translation when available
  final bool useOfflineTranslation;

  /// Whether to save conversation history
  final bool saveConversationHistory;

  /// The silence duration in milliseconds to detect end of speech
  final int silenceDurationMs;

  /// The maximum duration in milliseconds for continuous recording
  final int maxRecordingDurationMs;

  /// Creates a new conversation settings
  const ConversationSettings({
    this.useContinuousListening = false,
    this.autoSwitchSpeakers = false,
    this.autoDetectLanguages = false,
    this.showCulturalContext = true,
    this.showSlangIdiom = true,
    this.showPronunciation = true,
    this.useOfflineTranslation = true,
    this.saveConversationHistory = true,
    this.silenceDurationMs = 1500,
    this.maxRecordingDurationMs = 30000,
  });

  /// Creates a copy with some fields replaced
  ConversationSettings copyWith({
    bool? useContinuousListening,
    bool? autoSwitchSpeakers,
    bool? autoDetectLanguages,
    bool? showCulturalContext,
    bool? showSlangIdiom,
    bool? showPronunciation,
    bool? useOfflineTranslation,
    bool? saveConversationHistory,
    int? silenceDurationMs,
    int? maxRecordingDurationMs,
  }) {
    return ConversationSettings(
      useContinuousListening:
          useContinuousListening ?? this.useContinuousListening,
      autoSwitchSpeakers: autoSwitchSpeakers ?? this.autoSwitchSpeakers,
      autoDetectLanguages: autoDetectLanguages ?? this.autoDetectLanguages,
      showCulturalContext: showCulturalContext ?? this.showCulturalContext,
      showSlangIdiom: showSlangIdiom ?? this.showSlangIdiom,
      showPronunciation: showPronunciation ?? this.showPronunciation,
      useOfflineTranslation:
          useOfflineTranslation ?? this.useOfflineTranslation,
      saveConversationHistory:
          saveConversationHistory ?? this.saveConversationHistory,
      silenceDurationMs: silenceDurationMs ?? this.silenceDurationMs,
      maxRecordingDurationMs:
          maxRecordingDurationMs ?? this.maxRecordingDurationMs,
    );
  }

  /// Converts to JSON
  Map<String, dynamic> toJson() {
    return {
      'useContinuousListening': useContinuousListening,
      'autoSwitchSpeakers': autoSwitchSpeakers,
      'autoDetectLanguages': autoDetectLanguages,
      'showCulturalContext': showCulturalContext,
      'showSlangIdiom': showSlangIdiom,
      'showPronunciation': showPronunciation,
      'useOfflineTranslation': useOfflineTranslation,
      'saveConversationHistory': saveConversationHistory,
      'silenceDurationMs': silenceDurationMs,
      'maxRecordingDurationMs': maxRecordingDurationMs,
    };
  }

  /// Creates from JSON
  factory ConversationSettings.fromJson(Map<String, dynamic> json) {
    return ConversationSettings(
      useContinuousListening: json['useContinuousListening'] as bool? ?? false,
      autoSwitchSpeakers: json['autoSwitchSpeakers'] as bool? ?? false,
      autoDetectLanguages: json['autoDetectLanguages'] as bool? ?? false,
      showCulturalContext: json['showCulturalContext'] as bool? ?? true,
      showSlangIdiom: json['showSlangIdiom'] as bool? ?? true,
      showPronunciation: json['showPronunciation'] as bool? ?? true,
      useOfflineTranslation: json['useOfflineTranslation'] as bool? ?? true,
      saveConversationHistory: json['saveConversationHistory'] as bool? ?? true,
      silenceDurationMs: json['silenceDurationMs'] as int? ?? 1500,
      maxRecordingDurationMs: json['maxRecordingDurationMs'] as int? ?? 30000,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is ConversationSettings &&
        other.useContinuousListening == useContinuousListening &&
        other.autoSwitchSpeakers == autoSwitchSpeakers &&
        other.autoDetectLanguages == autoDetectLanguages &&
        other.showCulturalContext == showCulturalContext &&
        other.showSlangIdiom == showSlangIdiom &&
        other.showPronunciation == showPronunciation &&
        other.useOfflineTranslation == useOfflineTranslation &&
        other.saveConversationHistory == saveConversationHistory &&
        other.silenceDurationMs == silenceDurationMs &&
        other.maxRecordingDurationMs == maxRecordingDurationMs;
  }

  @override
  int get hashCode {
    return useContinuousListening.hashCode ^
        autoSwitchSpeakers.hashCode ^
        autoDetectLanguages.hashCode ^
        showCulturalContext.hashCode ^
        showSlangIdiom.hashCode ^
        showPronunciation.hashCode ^
        useOfflineTranslation.hashCode ^
        saveConversationHistory.hashCode ^
        silenceDurationMs.hashCode ^
        maxRecordingDurationMs.hashCode;
  }
}
