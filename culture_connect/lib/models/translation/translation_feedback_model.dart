import 'package:flutter/material.dart';
import 'package:culture_connect/models/translation/message_translation_metadata.dart';

/// Types of translation feedback
enum TranslationFeedbackType {
  /// General feedback about the translation quality
  general,
  
  /// Feedback about specific words or phrases
  specific,
  
  /// Feedback about grammar or sentence structure
  grammar,
  
  /// Feedback about cultural context or idioms
  cultural,
  
  /// Feedback about technical or specialized terms
  technical,
}

/// Extension on TranslationFeedbackType to provide additional functionality
extension TranslationFeedbackTypeExtension on TranslationFeedbackType {
  /// Get the display name of the feedback type
  String get displayName {
    switch (this) {
      case TranslationFeedbackType.general:
        return 'General';
      case TranslationFeedbackType.specific:
        return 'Specific Words/Phrases';
      case TranslationFeedbackType.grammar:
        return 'Grammar/Structure';
      case TranslationFeedbackType.cultural:
        return 'Cultural Context';
      case TranslationFeedbackType.technical:
        return 'Technical Terms';
    }
  }
  
  /// Get the icon associated with the feedback type
  IconData get icon {
    switch (this) {
      case TranslationFeedbackType.general:
        return Icons.feedback_outlined;
      case TranslationFeedbackType.specific:
        return Icons.text_fields;
      case TranslationFeedbackType.grammar:
        return Icons.spellcheck;
      case TranslationFeedbackType.cultural:
        return Icons.public;
      case TranslationFeedbackType.technical:
        return Icons.science_outlined;
    }
  }
}

/// A model representing feedback for a translation
class TranslationFeedbackModel {
  /// Unique identifier for the feedback
  final String id;
  
  /// ID of the message that was translated
  final String messageId;
  
  /// ID of the user providing the feedback
  final String userId;
  
  /// Source language code
  final String sourceLanguage;
  
  /// Target language code
  final String targetLanguage;
  
  /// Original text before translation
  final String originalText;
  
  /// Translated text that received feedback
  final String translatedText;
  
  /// Suggested correction for the translation
  final String? suggestedCorrection;
  
  /// Quality rating of the translation
  final TranslationQuality quality;
  
  /// Type of feedback
  final TranslationFeedbackType feedbackType;
  
  /// Specific comments about the translation
  final String? comments;
  
  /// Whether the feedback has been reviewed
  final bool isReviewed;
  
  /// Whether the feedback has been incorporated
  final bool isIncorporated;
  
  /// Timestamp when the feedback was created
  final DateTime createdAt;
  
  /// Timestamp when the feedback was last updated
  final DateTime updatedAt;
  
  /// Confidence level of the original translation (0.0 to 1.0)
  final double? originalConfidence;
  
  /// Specific text segments that were problematic (start and end indices)
  final List<TextSegment>? problematicSegments;
  
  /// Creates a new translation feedback model
  const TranslationFeedbackModel({
    required this.id,
    required this.messageId,
    required this.userId,
    required this.sourceLanguage,
    required this.targetLanguage,
    required this.originalText,
    required this.translatedText,
    this.suggestedCorrection,
    required this.quality,
    required this.feedbackType,
    this.comments,
    this.isReviewed = false,
    this.isIncorporated = false,
    required this.createdAt,
    required this.updatedAt,
    this.originalConfidence,
    this.problematicSegments,
  });
  
  /// Creates a copy with some fields replaced
  TranslationFeedbackModel copyWith({
    String? id,
    String? messageId,
    String? userId,
    String? sourceLanguage,
    String? targetLanguage,
    String? originalText,
    String? translatedText,
    String? suggestedCorrection,
    TranslationQuality? quality,
    TranslationFeedbackType? feedbackType,
    String? comments,
    bool? isReviewed,
    bool? isIncorporated,
    DateTime? createdAt,
    DateTime? updatedAt,
    double? originalConfidence,
    List<TextSegment>? problematicSegments,
  }) {
    return TranslationFeedbackModel(
      id: id ?? this.id,
      messageId: messageId ?? this.messageId,
      userId: userId ?? this.userId,
      sourceLanguage: sourceLanguage ?? this.sourceLanguage,
      targetLanguage: targetLanguage ?? this.targetLanguage,
      originalText: originalText ?? this.originalText,
      translatedText: translatedText ?? this.translatedText,
      suggestedCorrection: suggestedCorrection ?? this.suggestedCorrection,
      quality: quality ?? this.quality,
      feedbackType: feedbackType ?? this.feedbackType,
      comments: comments ?? this.comments,
      isReviewed: isReviewed ?? this.isReviewed,
      isIncorporated: isIncorporated ?? this.isIncorporated,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      originalConfidence: originalConfidence ?? this.originalConfidence,
      problematicSegments: problematicSegments ?? this.problematicSegments,
    );
  }
  
  /// Converts to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'messageId': messageId,
      'userId': userId,
      'sourceLanguage': sourceLanguage,
      'targetLanguage': targetLanguage,
      'originalText': originalText,
      'translatedText': translatedText,
      'suggestedCorrection': suggestedCorrection,
      'quality': quality.index,
      'feedbackType': feedbackType.index,
      'comments': comments,
      'isReviewed': isReviewed,
      'isIncorporated': isIncorporated,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'originalConfidence': originalConfidence,
      'problematicSegments': problematicSegments?.map((segment) => segment.toJson()).toList(),
    };
  }
  
  /// Creates from JSON
  factory TranslationFeedbackModel.fromJson(Map<String, dynamic> json) {
    return TranslationFeedbackModel(
      id: json['id'] as String,
      messageId: json['messageId'] as String,
      userId: json['userId'] as String,
      sourceLanguage: json['sourceLanguage'] as String,
      targetLanguage: json['targetLanguage'] as String,
      originalText: json['originalText'] as String,
      translatedText: json['translatedText'] as String,
      suggestedCorrection: json['suggestedCorrection'] as String?,
      quality: TranslationQuality.values[json['quality'] as int],
      feedbackType: TranslationFeedbackType.values[json['feedbackType'] as int],
      comments: json['comments'] as String?,
      isReviewed: json['isReviewed'] as bool? ?? false,
      isIncorporated: json['isIncorporated'] as bool? ?? false,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
      originalConfidence: (json['originalConfidence'] as num?)?.toDouble(),
      problematicSegments: json['problematicSegments'] != null
          ? (json['problematicSegments'] as List)
              .map((e) => TextSegment.fromJson(e as Map<String, dynamic>))
              .toList()
          : null,
    );
  }
}

/// A model representing a problematic text segment in a translation
class TextSegment {
  /// Start index in the translated text
  final int startIndex;
  
  /// End index in the translated text
  final int endIndex;
  
  /// The problematic text
  final String text;
  
  /// The suggested correction
  final String? suggestedText;
  
  /// The type of issue with this segment
  final String issueType;
  
  /// Creates a new text segment
  const TextSegment({
    required this.startIndex,
    required this.endIndex,
    required this.text,
    this.suggestedText,
    required this.issueType,
  });
  
  /// Converts to JSON
  Map<String, dynamic> toJson() {
    return {
      'startIndex': startIndex,
      'endIndex': endIndex,
      'text': text,
      'suggestedText': suggestedText,
      'issueType': issueType,
    };
  }
  
  /// Creates from JSON
  factory TextSegment.fromJson(Map<String, dynamic> json) {
    return TextSegment(
      startIndex: json['startIndex'] as int,
      endIndex: json['endIndex'] as int,
      text: json['text'] as String,
      suggestedText: json['suggestedText'] as String?,
      issueType: json['issueType'] as String,
    );
  }
}
