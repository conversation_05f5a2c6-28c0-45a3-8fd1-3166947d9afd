import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:uuid/uuid.dart';
import 'package:culture_connect/models/translation/translation_confidence_model.dart';
import 'package:culture_connect/models/translation/translation_cultural_context.dart';
import 'package:culture_connect/utils/rtl_utils.dart';

/// Status of an image text translation
enum ImageTextTranslationStatus {
  /// Translation is in progress
  inProgress,

  /// Translation is complete
  completed,

  /// Translation failed
  failed,

  /// Translation is pending
  pending,
}

/// A model representing a recognized text block in an image
class RecognizedTextBlock {
  /// The text content
  final String text;

  /// The bounding box of the text in the image (normalized coordinates)
  final Rect boundingBox;

  /// The confidence score (0.0 to 1.0)
  final double confidence;

  /// The language code of the text (if detected)
  final String? languageCode;

  /// Creates a new recognized text block
  const RecognizedTextBlock({
    required this.text,
    required this.boundingBox,
    required this.confidence,
    this.languageCode,
  });

  /// Creates a copy of this block with the given fields replaced
  RecognizedTextBlock copyWith({
    String? text,
    Rect? boundingBox,
    double? confidence,
    String? languageCode,
  }) {
    return RecognizedTextBlock(
      text: text ?? this.text,
      boundingBox: boundingBox ?? this.boundingBox,
      confidence: confidence ?? this.confidence,
      languageCode: languageCode ?? this.languageCode,
    );
  }

  /// Converts this block to a JSON map
  Map<String, dynamic> toJson() {
    return {
      'text': text,
      'boundingBox': {
        'left': boundingBox.left,
        'top': boundingBox.top,
        'right': boundingBox.right,
        'bottom': boundingBox.bottom,
      },
      'confidence': confidence,
      'languageCode': languageCode,
    };
  }

  /// Creates a block from a JSON map
  factory RecognizedTextBlock.fromJson(Map<String, dynamic> json) {
    final boundingBoxJson = json['boundingBox'] as Map<String, dynamic>;

    return RecognizedTextBlock(
      text: json['text'] as String,
      boundingBox: Rect.fromLTRB(
        boundingBoxJson['left'] as double,
        boundingBoxJson['top'] as double,
        boundingBoxJson['right'] as double,
        boundingBoxJson['bottom'] as double,
      ),
      confidence: json['confidence'] as double,
      languageCode: json['languageCode'] as String?,
    );
  }
}

/// A model representing an image text translation
class ImageTextTranslationModel {
  /// Unique identifier for the translation
  final String id;

  /// Path to the image file
  final String imagePath;

  /// Recognized text blocks in the image
  final List<RecognizedTextBlock> recognizedTextBlocks;

  /// Combined recognized text
  final String? recognizedText;

  /// Translated text
  final String? translatedText;

  /// Source language code
  final String? sourceLanguage;

  /// Target language code
  final String targetLanguage;

  /// Status of the translation
  final ImageTextTranslationStatus status;

  /// Error message if status is failed
  final String? errorMessage;

  /// Timestamp of the translation
  final DateTime timestamp;

  /// Whether the translation is favorited
  final bool isFavorite;

  /// Whether the translation was done offline
  final bool isOfflineTranslation;

  /// Confidence information for the translation
  final TranslationConfidenceModel? confidence;

  /// Cultural context information for the translation
  final TranslationCulturalContext? culturalContext;

  /// Whether to show cultural context information
  final bool showCulturalContext;

  /// Creates a new image text translation model
  const ImageTextTranslationModel({
    required this.id,
    required this.imagePath,
    this.recognizedTextBlocks = const [],
    this.recognizedText,
    this.translatedText,
    this.sourceLanguage,
    required this.targetLanguage,
    required this.status,
    this.errorMessage,
    required this.timestamp,
    this.isFavorite = false,
    this.isOfflineTranslation = false,
    this.confidence,
    this.culturalContext,
    this.showCulturalContext = true,
  });

  /// Creates a new image text translation model with a generated ID
  factory ImageTextTranslationModel.create({
    required String imagePath,
    List<RecognizedTextBlock> recognizedTextBlocks = const [],
    String? recognizedText,
    String? translatedText,
    String? sourceLanguage,
    required String targetLanguage,
    ImageTextTranslationStatus status = ImageTextTranslationStatus.pending,
    String? errorMessage,
    bool isFavorite = false,
    bool isOfflineTranslation = false,
    TranslationConfidenceModel? confidence,
    TranslationCulturalContext? culturalContext,
    bool showCulturalContext = true,
  }) {
    return ImageTextTranslationModel(
      id: const Uuid().v4(),
      imagePath: imagePath,
      recognizedTextBlocks: recognizedTextBlocks,
      recognizedText: recognizedText,
      translatedText: translatedText,
      sourceLanguage: sourceLanguage,
      targetLanguage: targetLanguage,
      status: status,
      errorMessage: errorMessage,
      timestamp: DateTime.now(),
      isFavorite: isFavorite,
      isOfflineTranslation: isOfflineTranslation,
      confidence: confidence,
      culturalContext: culturalContext,
      showCulturalContext: showCulturalContext,
    );
  }

  /// Creates a copy of this model with the given fields replaced
  ImageTextTranslationModel copyWith({
    String? id,
    String? imagePath,
    List<RecognizedTextBlock>? recognizedTextBlocks,
    String? recognizedText,
    String? translatedText,
    String? sourceLanguage,
    String? targetLanguage,
    ImageTextTranslationStatus? status,
    String? errorMessage,
    DateTime? timestamp,
    bool? isFavorite,
    bool? isOfflineTranslation,
    TranslationConfidenceModel? confidence,
    TranslationCulturalContext? culturalContext,
    bool? showCulturalContext,
  }) {
    return ImageTextTranslationModel(
      id: id ?? this.id,
      imagePath: imagePath ?? this.imagePath,
      recognizedTextBlocks: recognizedTextBlocks ?? this.recognizedTextBlocks,
      recognizedText: recognizedText ?? this.recognizedText,
      translatedText: translatedText ?? this.translatedText,
      sourceLanguage: sourceLanguage ?? this.sourceLanguage,
      targetLanguage: targetLanguage ?? this.targetLanguage,
      status: status ?? this.status,
      errorMessage: errorMessage ?? this.errorMessage,
      timestamp: timestamp ?? this.timestamp,
      isFavorite: isFavorite ?? this.isFavorite,
      isOfflineTranslation: isOfflineTranslation ?? this.isOfflineTranslation,
      confidence: confidence ?? this.confidence,
      culturalContext: culturalContext ?? this.culturalContext,
      showCulturalContext: showCulturalContext ?? this.showCulturalContext,
    );
  }

  /// Converts this model to a JSON map
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'imagePath': imagePath,
      'recognizedTextBlocks':
          recognizedTextBlocks.map((block) => block.toJson()).toList(),
      'recognizedText': recognizedText,
      'translatedText': translatedText,
      'sourceLanguage': sourceLanguage,
      'targetLanguage': targetLanguage,
      'status': status.index,
      'errorMessage': errorMessage,
      'timestamp': timestamp.toIso8601String(),
      'isFavorite': isFavorite,
      'isOfflineTranslation': isOfflineTranslation,
      'confidence': confidence?.toJson(),
      'culturalContext': culturalContext?.toJson(),
      'showCulturalContext': showCulturalContext,
    };
  }

  /// Creates a model from a JSON map
  factory ImageTextTranslationModel.fromJson(Map<String, dynamic> json) {
    return ImageTextTranslationModel(
      id: json['id'] as String,
      imagePath: json['imagePath'] as String,
      recognizedTextBlocks: (json['recognizedTextBlocks'] as List)
          .map((block) =>
              RecognizedTextBlock.fromJson(block as Map<String, dynamic>))
          .toList(),
      recognizedText: json['recognizedText'] as String?,
      translatedText: json['translatedText'] as String?,
      sourceLanguage: json['sourceLanguage'] as String?,
      targetLanguage: json['targetLanguage'] as String,
      status: ImageTextTranslationStatus.values[json['status'] as int],
      errorMessage: json['errorMessage'] as String?,
      timestamp: DateTime.parse(json['timestamp'] as String),
      isFavorite: json['isFavorite'] as bool? ?? false,
      isOfflineTranslation: json['isOfflineTranslation'] as bool? ?? false,
      confidence: json['confidence'] != null
          ? TranslationConfidenceModel.fromJson(
              json['confidence'] as Map<String, dynamic>)
          : null,
      culturalContext: json['culturalContext'] != null
          ? TranslationCulturalContext.fromJson(
              json['culturalContext'] as Map<String, dynamic>)
          : null,
      showCulturalContext: json['showCulturalContext'] as bool? ?? true,
    );
  }

  /// Get the image file
  File get imageFile => File(imagePath);

  /// Check if the image file exists
  Future<bool> imageExists() async {
    return await imageFile.exists();
  }

  /// Check if the source language is RTL
  bool get isSourceRTL =>
      sourceLanguage != null && RTLUtils.isRTL(sourceLanguage!);

  /// Check if the target language is RTL
  bool get isTargetRTL => RTLUtils.isRTL(targetLanguage);

  /// Get the text direction for the source language
  TextDirection get sourceTextDirection => sourceLanguage != null
      ? RTLUtils.getTextDirection(sourceLanguage!)
      : TextDirection.ltr;

  /// Get the text direction for the target language
  TextDirection get targetTextDirection =>
      RTLUtils.getTextDirection(targetLanguage);

  /// Get the text alignment for the source language
  TextAlign get sourceTextAlign => sourceLanguage != null
      ? RTLUtils.getTextAlignment(sourceLanguage!)
      : TextAlign.left;

  /// Get the text alignment for the target language
  TextAlign get targetTextAlign => RTLUtils.getTextAlignment(targetLanguage);

  @override
  String toString() {
    return 'ImageTextTranslationModel(id: $id, status: $status, sourceLanguage: $sourceLanguage, targetLanguage: $targetLanguage)';
  }
}
