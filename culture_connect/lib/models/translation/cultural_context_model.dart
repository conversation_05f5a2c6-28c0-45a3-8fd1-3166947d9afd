import 'package:flutter/material.dart';

/// Type of cultural context
enum CulturalContextType {
  /// General cultural information
  general,
  
  /// Idiom or expression
  idiom,
  
  /// Cultural reference
  reference,
  
  /// Cultural taboo or sensitive topic
  taboo,
  
  /// Regional usage
  regional,
  
  /// Slang or colloquialism
  slang,
  
  /// Formal vs informal usage
  formality,
  
  /// Historical context
  historical,
  
  /// Religious or spiritual context
  religious,
}

/// Extension on CulturalContextType to provide additional functionality
extension CulturalContextTypeExtension on CulturalContextType {
  /// Get the display name of the cultural context type
  String get displayName {
    switch (this) {
      case CulturalContextType.general:
        return 'Cultural Note';
      case CulturalContextType.idiom:
        return 'Idiom';
      case CulturalContextType.reference:
        return 'Cultural Reference';
      case CulturalContextType.taboo:
        return 'Cultural Sensitivity';
      case CulturalContextType.regional:
        return 'Regional Usage';
      case CulturalContextType.slang:
        return 'Slang';
      case CulturalContextType.formality:
        return 'Formality';
      case CulturalContextType.historical:
        return 'Historical Context';
      case CulturalContextType.religious:
        return 'Religious Context';
    }
  }
  
  /// Get the icon associated with the cultural context type
  IconData get icon {
    switch (this) {
      case CulturalContextType.general:
        return Icons.info_outline;
      case CulturalContextType.idiom:
        return Icons.format_quote;
      case CulturalContextType.reference:
        return Icons.emoji_objects_outlined;
      case CulturalContextType.taboo:
        return Icons.warning_amber_outlined;
      case CulturalContextType.regional:
        return Icons.location_on_outlined;
      case CulturalContextType.slang:
        return Icons.chat_bubble_outline;
      case CulturalContextType.formality:
        return Icons.person_outline;
      case CulturalContextType.historical:
        return Icons.history;
      case CulturalContextType.religious:
        return Icons.church_outlined;
    }
  }
  
  /// Get the color associated with the cultural context type
  Color get color {
    switch (this) {
      case CulturalContextType.general:
        return Colors.blue;
      case CulturalContextType.idiom:
        return Colors.purple;
      case CulturalContextType.reference:
        return Colors.amber;
      case CulturalContextType.taboo:
        return Colors.red;
      case CulturalContextType.regional:
        return Colors.green;
      case CulturalContextType.slang:
        return Colors.orange;
      case CulturalContextType.formality:
        return Colors.teal;
      case CulturalContextType.historical:
        return Colors.brown;
      case CulturalContextType.religious:
        return Colors.indigo;
    }
  }
}

/// A model representing a cultural context note
class CulturalContextNote {
  /// Unique identifier for the note
  final String id;
  
  /// The text segment this note applies to
  final String textSegment;
  
  /// Start index in the translated text
  final int startIndex;
  
  /// End index in the translated text
  final int endIndex;
  
  /// The type of cultural context
  final CulturalContextType type;
  
  /// The explanation of the cultural context
  final String explanation;
  
  /// Alternative phrasings or translations
  final List<String>? alternatives;
  
  /// The language code this note applies to
  final String languageCode;
  
  /// The region or dialect this note is specific to (if any)
  final String? region;
  
  /// The formality level (if applicable)
  final String? formalityLevel;
  
  /// Whether this is a sensitive topic
  final bool isSensitive;
  
  /// Additional resources or references
  final List<String>? resources;
  
  /// Creates a new cultural context note
  const CulturalContextNote({
    required this.id,
    required this.textSegment,
    required this.startIndex,
    required this.endIndex,
    required this.type,
    required this.explanation,
    this.alternatives,
    required this.languageCode,
    this.region,
    this.formalityLevel,
    this.isSensitive = false,
    this.resources,
  });
  
  /// Creates a copy with some fields replaced
  CulturalContextNote copyWith({
    String? id,
    String? textSegment,
    int? startIndex,
    int? endIndex,
    CulturalContextType? type,
    String? explanation,
    List<String>? alternatives,
    String? languageCode,
    String? region,
    String? formalityLevel,
    bool? isSensitive,
    List<String>? resources,
  }) {
    return CulturalContextNote(
      id: id ?? this.id,
      textSegment: textSegment ?? this.textSegment,
      startIndex: startIndex ?? this.startIndex,
      endIndex: endIndex ?? this.endIndex,
      type: type ?? this.type,
      explanation: explanation ?? this.explanation,
      alternatives: alternatives ?? this.alternatives,
      languageCode: languageCode ?? this.languageCode,
      region: region ?? this.region,
      formalityLevel: formalityLevel ?? this.formalityLevel,
      isSensitive: isSensitive ?? this.isSensitive,
      resources: resources ?? this.resources,
    );
  }
  
  /// Converts to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'textSegment': textSegment,
      'startIndex': startIndex,
      'endIndex': endIndex,
      'type': type.index,
      'explanation': explanation,
      'alternatives': alternatives,
      'languageCode': languageCode,
      'region': region,
      'formalityLevel': formalityLevel,
      'isSensitive': isSensitive,
      'resources': resources,
    };
  }
  
  /// Creates from JSON
  factory CulturalContextNote.fromJson(Map<String, dynamic> json) {
    return CulturalContextNote(
      id: json['id'] as String,
      textSegment: json['textSegment'] as String,
      startIndex: json['startIndex'] as int,
      endIndex: json['endIndex'] as int,
      type: CulturalContextType.values[json['type'] as int],
      explanation: json['explanation'] as String,
      alternatives: json['alternatives'] != null
          ? List<String>.from(json['alternatives'] as List)
          : null,
      languageCode: json['languageCode'] as String,
      region: json['region'] as String?,
      formalityLevel: json['formalityLevel'] as String?,
      isSensitive: json['isSensitive'] as bool? ?? false,
      resources: json['resources'] != null
          ? List<String>.from(json['resources'] as List)
          : null,
    );
  }
}
