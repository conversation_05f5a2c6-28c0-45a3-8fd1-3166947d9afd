import 'package:flutter/material.dart';
import 'package:culture_connect/models/translation/translation_confidence_model.dart';
import 'package:culture_connect/models/translation/translation_cultural_context.dart';
import 'package:culture_connect/models/translation/translation_pronunciation.dart';
import 'package:culture_connect/models/translation/translation_slang_idiom.dart';

/// Quality rating for a translation
enum TranslationQuality {
  /// Excellent translation quality
  excellent,

  /// Good translation quality
  good,

  /// Fair translation quality
  fair,

  /// Poor translation quality
  poor,

  /// Incorrect translation
  incorrect,
}

/// Extension on TranslationQuality to provide additional functionality
extension TranslationQualityExtension on TranslationQuality {
  /// Get the display name of the translation quality
  String get displayName {
    switch (this) {
      case TranslationQuality.excellent:
        return 'Excellent';
      case TranslationQuality.good:
        return 'Good';
      case TranslationQuality.fair:
        return 'Fair';
      case TranslationQuality.poor:
        return 'Poor';
      case TranslationQuality.incorrect:
        return 'Incorrect';
    }
  }

  /// Get the color associated with the translation quality
  Color get color {
    switch (this) {
      case TranslationQuality.excellent:
        return Colors.green;
      case TranslationQuality.good:
        return Colors.lightGreen;
      case TranslationQuality.fair:
        return Colors.amber;
      case TranslationQuality.poor:
        return Colors.orange;
      case TranslationQuality.incorrect:
        return Colors.red;
    }
  }

  /// Get the icon associated with the translation quality
  IconData get icon {
    switch (this) {
      case TranslationQuality.excellent:
        return Icons.sentiment_very_satisfied;
      case TranslationQuality.good:
        return Icons.sentiment_satisfied;
      case TranslationQuality.fair:
        return Icons.sentiment_neutral;
      case TranslationQuality.poor:
        return Icons.sentiment_dissatisfied;
      case TranslationQuality.incorrect:
        return Icons.sentiment_very_dissatisfied;
    }
  }
}

/// A model representing translation metadata for a message
class MessageTranslationMetadata {
  /// Original text before translation
  final String originalText;

  /// Translated text
  final String translatedText;

  /// Source language code
  final String sourceLanguage;

  /// Target language code
  final String targetLanguage;

  /// Timestamp of the translation
  final DateTime translatedAt;

  /// Quality rating of the translation (if provided)
  final TranslationQuality? quality;

  /// Whether audio is available for the translation
  final bool isAudioAvailable;

  /// Path to the translated audio file (if available)
  final String? translatedAudioPath;

  /// Confidence information for the translation
  final TranslationConfidenceModel? confidence;

  /// Whether this translation has received feedback
  final bool hasFeedback;

  /// ID of the feedback if available
  final String? feedbackId;

  /// Cultural context information
  final TranslationCulturalContext? culturalContext;

  /// Whether to show cultural context information
  final bool showCulturalContext;

  /// Slang and idiom information
  final TranslationSlangIdiom? slangIdiom;

  /// Whether to show slang and idiom information
  final bool showSlangIdiom;

  /// Pronunciation information
  final TranslationPronunciation? pronunciation;

  /// Whether to show pronunciation information
  final bool showPronunciation;

  /// Creates a new message translation metadata
  const MessageTranslationMetadata({
    required this.originalText,
    required this.translatedText,
    required this.sourceLanguage,
    required this.targetLanguage,
    required this.translatedAt,
    this.quality,
    this.isAudioAvailable = false,
    this.translatedAudioPath,
    this.confidence,
    this.hasFeedback = false,
    this.feedbackId,
    this.culturalContext,
    this.showCulturalContext = true,
    this.slangIdiom,
    this.showSlangIdiom = true,
    this.pronunciation,
    this.showPronunciation = true,
  });

  /// Creates a copy with some fields replaced
  MessageTranslationMetadata copyWith({
    String? originalText,
    String? translatedText,
    String? sourceLanguage,
    String? targetLanguage,
    DateTime? translatedAt,
    TranslationQuality? quality,
    bool? isAudioAvailable,
    String? translatedAudioPath,
    TranslationConfidenceModel? confidence,
    bool? hasFeedback,
    String? feedbackId,
    TranslationCulturalContext? culturalContext,
    bool? showCulturalContext,
    TranslationSlangIdiom? slangIdiom,
    bool? showSlangIdiom,
    TranslationPronunciation? pronunciation,
    bool? showPronunciation,
  }) {
    return MessageTranslationMetadata(
      originalText: originalText ?? this.originalText,
      translatedText: translatedText ?? this.translatedText,
      sourceLanguage: sourceLanguage ?? this.sourceLanguage,
      targetLanguage: targetLanguage ?? this.targetLanguage,
      translatedAt: translatedAt ?? this.translatedAt,
      quality: quality ?? this.quality,
      isAudioAvailable: isAudioAvailable ?? this.isAudioAvailable,
      translatedAudioPath: translatedAudioPath ?? this.translatedAudioPath,
      confidence: confidence ?? this.confidence,
      hasFeedback: hasFeedback ?? this.hasFeedback,
      feedbackId: feedbackId ?? this.feedbackId,
      culturalContext: culturalContext ?? this.culturalContext,
      showCulturalContext: showCulturalContext ?? this.showCulturalContext,
      slangIdiom: slangIdiom ?? this.slangIdiom,
      showSlangIdiom: showSlangIdiom ?? this.showSlangIdiom,
      pronunciation: pronunciation ?? this.pronunciation,
      showPronunciation: showPronunciation ?? this.showPronunciation,
    );
  }

  /// Converts to JSON
  Map<String, dynamic> toJson() {
    return {
      'originalText': originalText,
      'translatedText': translatedText,
      'sourceLanguage': sourceLanguage,
      'targetLanguage': targetLanguage,
      'translatedAt': translatedAt.toIso8601String(),
      'quality': quality?.index,
      'isAudioAvailable': isAudioAvailable,
      'translatedAudioPath': translatedAudioPath,
      'confidence': confidence?.toJson(),
      'hasFeedback': hasFeedback,
      'feedbackId': feedbackId,
      'culturalContext': culturalContext?.toJson(),
      'showCulturalContext': showCulturalContext,
      'slangIdiom': slangIdiom?.toJson(),
      'showSlangIdiom': showSlangIdiom,
      'pronunciation': pronunciation?.toJson(),
      'showPronunciation': showPronunciation,
    };
  }

  /// Creates from JSON
  factory MessageTranslationMetadata.fromJson(Map<String, dynamic> json) {
    return MessageTranslationMetadata(
      originalText: json['originalText'] as String,
      translatedText: json['translatedText'] as String,
      sourceLanguage: json['sourceLanguage'] as String,
      targetLanguage: json['targetLanguage'] as String,
      translatedAt: DateTime.parse(json['translatedAt'] as String),
      quality: json['quality'] != null
          ? TranslationQuality.values[json['quality'] as int]
          : null,
      isAudioAvailable: json['isAudioAvailable'] as bool? ?? false,
      translatedAudioPath: json['translatedAudioPath'] as String?,
      confidence: json['confidence'] != null
          ? TranslationConfidenceModel.fromJson(
              json['confidence'] as Map<String, dynamic>)
          : null,
      hasFeedback: json['hasFeedback'] as bool? ?? false,
      feedbackId: json['feedbackId'] as String?,
      culturalContext: json['culturalContext'] != null
          ? TranslationCulturalContext.fromJson(
              json['culturalContext'] as Map<String, dynamic>)
          : null,
      showCulturalContext: json['showCulturalContext'] as bool? ?? true,
      slangIdiom: json['slangIdiom'] != null
          ? TranslationSlangIdiom.fromJson(
              json['slangIdiom'] as Map<String, dynamic>)
          : null,
      showSlangIdiom: json['showSlangIdiom'] as bool? ?? true,
      pronunciation: json['pronunciation'] != null
          ? TranslationPronunciation.fromJson(
              json['pronunciation'] as Map<String, dynamic>)
          : null,
      showPronunciation: json['showPronunciation'] as bool? ?? true,
    );
  }
}
