import 'package:flutter/material.dart';

/// Difficulty level of pronunciation
enum PronunciationDifficulty {
  /// Very easy to pronounce
  veryEasy,
  
  /// Easy to pronounce
  easy,
  
  /// Moderate difficulty to pronounce
  moderate,
  
  /// Difficult to pronounce
  difficult,
  
  /// Very difficult to pronounce
  veryDifficult,
}

/// Extension on PronunciationDifficulty to provide additional functionality
extension PronunciationDifficultyExtension on PronunciationDifficulty {
  /// Get the display name of the pronunciation difficulty
  String get displayName {
    switch (this) {
      case PronunciationDifficulty.veryEasy:
        return 'Very Easy';
      case PronunciationDifficulty.easy:
        return 'Easy';
      case PronunciationDifficulty.moderate:
        return 'Moderate';
      case PronunciationDifficulty.difficult:
        return 'Difficult';
      case PronunciationDifficulty.veryDifficult:
        return 'Very Difficult';
    }
  }
  
  /// Get the color associated with the pronunciation difficulty
  Color get color {
    switch (this) {
      case PronunciationDifficulty.veryEasy:
        return Colors.green;
      case PronunciationDifficulty.easy:
        return Colors.lightGreen;
      case PronunciationDifficulty.moderate:
        return Colors.amber;
      case PronunciationDifficulty.difficult:
        return Colors.orange;
      case PronunciationDifficulty.veryDifficult:
        return Colors.red;
    }
  }
  
  /// Get the icon associated with the pronunciation difficulty
  IconData get icon {
    switch (this) {
      case PronunciationDifficulty.veryEasy:
        return Icons.sentiment_very_satisfied;
      case PronunciationDifficulty.easy:
        return Icons.sentiment_satisfied;
      case PronunciationDifficulty.moderate:
        return Icons.sentiment_neutral;
      case PronunciationDifficulty.difficult:
        return Icons.sentiment_dissatisfied;
      case PronunciationDifficulty.veryDifficult:
        return Icons.sentiment_very_dissatisfied;
    }
  }
  
  /// Create a difficulty level from a score (0.0 to 1.0)
  static PronunciationDifficulty fromScore(double score) {
    if (score >= 0.9) {
      return PronunciationDifficulty.veryEasy;
    } else if (score >= 0.7) {
      return PronunciationDifficulty.easy;
    } else if (score >= 0.5) {
      return PronunciationDifficulty.moderate;
    } else if (score >= 0.3) {
      return PronunciationDifficulty.difficult;
    } else {
      return PronunciationDifficulty.veryDifficult;
    }
  }
}

/// Type of pronunciation guide
enum PronunciationGuideType {
  /// International Phonetic Alphabet
  ipa,
  
  /// Simplified phonetic spelling
  simplified,
  
  /// Respelling using familiar sounds
  respelling,
  
  /// Syllable breakdown
  syllables,
}

/// Extension on PronunciationGuideType to provide additional functionality
extension PronunciationGuideTypeExtension on PronunciationGuideType {
  /// Get the display name of the pronunciation guide type
  String get displayName {
    switch (this) {
      case PronunciationGuideType.ipa:
        return 'IPA';
      case PronunciationGuideType.simplified:
        return 'Simplified';
      case PronunciationGuideType.respelling:
        return 'Respelling';
      case PronunciationGuideType.syllables:
        return 'Syllables';
    }
  }
  
  /// Get the description of the pronunciation guide type
  String get description {
    switch (this) {
      case PronunciationGuideType.ipa:
        return 'International Phonetic Alphabet notation';
      case PronunciationGuideType.simplified:
        return 'Simplified phonetic spelling';
      case PronunciationGuideType.respelling:
        return 'Respelling using familiar sounds';
      case PronunciationGuideType.syllables:
        return 'Breakdown by syllables with stress marks';
    }
  }
  
  /// Get the icon associated with the pronunciation guide type
  IconData get icon {
    switch (this) {
      case PronunciationGuideType.ipa:
        return Icons.language;
      case PronunciationGuideType.simplified:
        return Icons.text_format;
      case PronunciationGuideType.respelling:
        return Icons.spellcheck;
      case PronunciationGuideType.syllables:
        return Icons.segment;
    }
  }
}

/// A model representing a pronunciation guide for a word or phrase
class PronunciationGuide {
  /// Unique identifier for the guide
  final String id;
  
  /// The word or phrase
  final String text;
  
  /// Start index in the translated text
  final int startIndex;
  
  /// End index in the translated text
  final int endIndex;
  
  /// The type of pronunciation guide
  final PronunciationGuideType type;
  
  /// The pronunciation representation
  final String pronunciation;
  
  /// The difficulty level of pronunciation
  final PronunciationDifficulty difficulty;
  
  /// Audio path for the pronunciation (if available)
  final String? audioPath;
  
  /// The language code this guide is for
  final String languageCode;
  
  /// The region or dialect this guide is specific to (if any)
  final String? region;
  
  /// Tips for pronouncing this word or phrase
  final List<String>? tips;
  
  /// Common mistakes to avoid
  final List<String>? commonMistakes;
  
  /// Creates a new pronunciation guide
  const PronunciationGuide({
    required this.id,
    required this.text,
    required this.startIndex,
    required this.endIndex,
    required this.type,
    required this.pronunciation,
    required this.difficulty,
    this.audioPath,
    required this.languageCode,
    this.region,
    this.tips,
    this.commonMistakes,
  });
  
  /// Creates a copy with some fields replaced
  PronunciationGuide copyWith({
    String? id,
    String? text,
    int? startIndex,
    int? endIndex,
    PronunciationGuideType? type,
    String? pronunciation,
    PronunciationDifficulty? difficulty,
    String? audioPath,
    String? languageCode,
    String? region,
    List<String>? tips,
    List<String>? commonMistakes,
  }) {
    return PronunciationGuide(
      id: id ?? this.id,
      text: text ?? this.text,
      startIndex: startIndex ?? this.startIndex,
      endIndex: endIndex ?? this.endIndex,
      type: type ?? this.type,
      pronunciation: pronunciation ?? this.pronunciation,
      difficulty: difficulty ?? this.difficulty,
      audioPath: audioPath ?? this.audioPath,
      languageCode: languageCode ?? this.languageCode,
      region: region ?? this.region,
      tips: tips ?? this.tips,
      commonMistakes: commonMistakes ?? this.commonMistakes,
    );
  }
  
  /// Converts to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'text': text,
      'startIndex': startIndex,
      'endIndex': endIndex,
      'type': type.index,
      'pronunciation': pronunciation,
      'difficulty': difficulty.index,
      'audioPath': audioPath,
      'languageCode': languageCode,
      'region': region,
      'tips': tips,
      'commonMistakes': commonMistakes,
    };
  }
  
  /// Creates from JSON
  factory PronunciationGuide.fromJson(Map<String, dynamic> json) {
    return PronunciationGuide(
      id: json['id'] as String,
      text: json['text'] as String,
      startIndex: json['startIndex'] as int,
      endIndex: json['endIndex'] as int,
      type: PronunciationGuideType.values[json['type'] as int],
      pronunciation: json['pronunciation'] as String,
      difficulty: PronunciationDifficulty.values[json['difficulty'] as int],
      audioPath: json['audioPath'] as String?,
      languageCode: json['languageCode'] as String,
      region: json['region'] as String?,
      tips: json['tips'] != null
          ? List<String>.from(json['tips'] as List)
          : null,
      commonMistakes: json['commonMistakes'] != null
          ? List<String>.from(json['commonMistakes'] as List)
          : null,
    );
  }
}
