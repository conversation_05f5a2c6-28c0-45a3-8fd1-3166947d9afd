/// Category of a custom vocabulary term
enum VocabularyCategory {
  /// General terms
  general,
  
  /// Medical terms
  medical,
  
  /// Technical terms
  technical,
  
  /// Business terms
  business,
  
  /// Legal terms
  legal,
  
  /// Academic terms
  academic,
  
  /// Cultural terms
  cultural,
  
  /// Travel terms
  travel,
  
  /// Food terms
  food,
  
  /// Custom category
  custom,
}

/// A model representing a custom vocabulary term
class CustomVocabularyModel {
  /// The unique identifier for the term
  final String id;
  
  /// The original term
  final String originalTerm;
  
  /// The language code of the original term
  final String originalLanguageCode;
  
  /// The translations of the term
  final Map<String, String> translations;
  
  /// The category of the term
  final VocabularyCategory category;
  
  /// The custom category name (if category is custom)
  final String? customCategory;
  
  /// The description or context of the term
  final String? description;
  
  /// When the term was created
  final DateTime createdAt;
  
  /// When the term was last updated
  final DateTime updatedAt;
  
  /// How many times the term has been used
  final int usageCount;
  
  /// Whether the term is favorited
  final bool isFavorite;
  
  /// Creates a new custom vocabulary model
  const CustomVocabularyModel({
    required this.id,
    required this.originalTerm,
    required this.originalLanguageCode,
    required this.translations,
    required this.category,
    this.customCategory,
    this.description,
    required this.createdAt,
    required this.updatedAt,
    this.usageCount = 0,
    this.isFavorite = false,
  });
  
  /// Creates a copy with some fields replaced
  CustomVocabularyModel copyWith({
    String? id,
    String? originalTerm,
    String? originalLanguageCode,
    Map<String, String>? translations,
    VocabularyCategory? category,
    String? customCategory,
    String? description,
    DateTime? createdAt,
    DateTime? updatedAt,
    int? usageCount,
    bool? isFavorite,
  }) {
    return CustomVocabularyModel(
      id: id ?? this.id,
      originalTerm: originalTerm ?? this.originalTerm,
      originalLanguageCode: originalLanguageCode ?? this.originalLanguageCode,
      translations: translations ?? this.translations,
      category: category ?? this.category,
      customCategory: customCategory ?? this.customCategory,
      description: description ?? this.description,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      usageCount: usageCount ?? this.usageCount,
      isFavorite: isFavorite ?? this.isFavorite,
    );
  }
  
  /// Get the translation for a specific language
  String? getTranslation(String languageCode) {
    return translations[languageCode];
  }
  
  /// Get the category name
  String getCategoryName() {
    if (category == VocabularyCategory.custom && customCategory != null) {
      return customCategory!;
    }
    
    switch (category) {
      case VocabularyCategory.general:
        return 'General';
      case VocabularyCategory.medical:
        return 'Medical';
      case VocabularyCategory.technical:
        return 'Technical';
      case VocabularyCategory.business:
        return 'Business';
      case VocabularyCategory.legal:
        return 'Legal';
      case VocabularyCategory.academic:
        return 'Academic';
      case VocabularyCategory.cultural:
        return 'Cultural';
      case VocabularyCategory.travel:
        return 'Travel';
      case VocabularyCategory.food:
        return 'Food';
      case VocabularyCategory.custom:
        return 'Custom';
    }
  }
  
  /// Converts to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'originalTerm': originalTerm,
      'originalLanguageCode': originalLanguageCode,
      'translations': translations,
      'category': category.index,
      'customCategory': customCategory,
      'description': description,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'usageCount': usageCount,
      'isFavorite': isFavorite,
    };
  }
  
  /// Creates from JSON
  factory CustomVocabularyModel.fromJson(Map<String, dynamic> json) {
    return CustomVocabularyModel(
      id: json['id'] as String,
      originalTerm: json['originalTerm'] as String,
      originalLanguageCode: json['originalLanguageCode'] as String,
      translations: Map<String, String>.from(json['translations'] as Map),
      category: VocabularyCategory.values[json['category'] as int],
      customCategory: json['customCategory'] as String?,
      description: json['description'] as String?,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
      usageCount: json['usageCount'] as int? ?? 0,
      isFavorite: json['isFavorite'] as bool? ?? false,
    );
  }
}
