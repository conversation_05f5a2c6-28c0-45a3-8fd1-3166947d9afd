import 'package:flutter/material.dart';
import 'package:culture_connect/models/translation/dialect_model.dart';
import 'package:culture_connect/models/translation/accent_model.dart';
import 'package:culture_connect/utils/rtl_utils.dart';

/// Status of a voice translation
enum VoiceTranslationStatus {
  /// Initial state
  initial,

  /// Recording in progress
  recording,

  /// Processing the recording
  processing,

  /// Translation completed
  completed,

  /// Error occurred
  error,
}

/// Extension on VoiceTranslationStatus to provide additional functionality
extension VoiceTranslationStatusExtension on VoiceTranslationStatus {
  /// Get the display name of the voice translation status
  String get displayName {
    switch (this) {
      case VoiceTranslationStatus.initial:
        return 'Ready';
      case VoiceTranslationStatus.recording:
        return 'Recording';
      case VoiceTranslationStatus.processing:
        return 'Processing';
      case VoiceTranslationStatus.completed:
        return 'Completed';
      case VoiceTranslationStatus.error:
        return 'Error';
    }
  }

  /// Get the color of the voice translation status
  Color get color {
    switch (this) {
      case VoiceTranslationStatus.initial:
        return Colors.blue;
      case VoiceTranslationStatus.recording:
        return Colors.red;
      case VoiceTranslationStatus.processing:
        return Colors.orange;
      case VoiceTranslationStatus.completed:
        return Colors.green;
      case VoiceTranslationStatus.error:
        return Colors.red;
    }
  }

  /// Get the icon of the voice translation status
  IconData get icon {
    switch (this) {
      case VoiceTranslationStatus.initial:
        return Icons.mic;
      case VoiceTranslationStatus.recording:
        return Icons.mic;
      case VoiceTranslationStatus.processing:
        return Icons.hourglass_top;
      case VoiceTranslationStatus.completed:
        return Icons.check_circle;
      case VoiceTranslationStatus.error:
        return Icons.error;
    }
  }
}

/// A model representing a voice translation
class VoiceTranslationModel {
  /// Unique identifier for the translation
  final String id;

  /// Original audio file path
  final String? originalAudioPath;

  /// Translated audio file path
  final String? translatedAudioPath;

  /// Original text
  final String? originalText;

  /// Translated text
  final String? translatedText;

  /// Source language code
  final String sourceLanguage;

  /// Target language code
  final String targetLanguage;

  /// Status of the translation
  final VoiceTranslationStatus status;

  /// Error message if status is error
  final String? errorMessage;

  /// Timestamp of the translation
  final DateTime timestamp;

  /// Whether the translation is favorited
  final bool isFavorite;

  /// The detected dialect
  final DialectModel? dialect;

  /// The detected accent
  final AccentModel? accent;

  /// The dialect confidence score (0.0 to 1.0)
  final double? dialectConfidence;

  /// The accent confidence score (0.0 to 1.0)
  final double? accentConfidence;

  /// Cultural context information
  final String? culturalContext;

  /// Slang/idiom information
  final String? slangIdiom;

  /// Pronunciation guidance
  final String? pronunciation;

  /// Creates a new voice translation model
  const VoiceTranslationModel({
    required this.id,
    this.originalAudioPath,
    this.translatedAudioPath,
    this.originalText,
    this.translatedText,
    required this.sourceLanguage,
    required this.targetLanguage,
    required this.status,
    this.errorMessage,
    required this.timestamp,
    this.isFavorite = false,
    this.dialect,
    this.accent,
    this.dialectConfidence,
    this.accentConfidence,
    this.culturalContext,
    this.slangIdiom,
    this.pronunciation,
  });

  /// Creates a copy with some fields replaced
  VoiceTranslationModel copyWith({
    String? id,
    String? originalAudioPath,
    String? translatedAudioPath,
    String? originalText,
    String? translatedText,
    String? sourceLanguage,
    String? targetLanguage,
    VoiceTranslationStatus? status,
    String? errorMessage,
    DateTime? timestamp,
    bool? isFavorite,
    DialectModel? dialect,
    AccentModel? accent,
    double? dialectConfidence,
    double? accentConfidence,
    String? culturalContext,
    String? slangIdiom,
    String? pronunciation,
  }) {
    return VoiceTranslationModel(
      id: id ?? this.id,
      originalAudioPath: originalAudioPath ?? this.originalAudioPath,
      translatedAudioPath: translatedAudioPath ?? this.translatedAudioPath,
      originalText: originalText ?? this.originalText,
      translatedText: translatedText ?? this.translatedText,
      sourceLanguage: sourceLanguage ?? this.sourceLanguage,
      targetLanguage: targetLanguage ?? this.targetLanguage,
      status: status ?? this.status,
      errorMessage: errorMessage ?? this.errorMessage,
      timestamp: timestamp ?? this.timestamp,
      isFavorite: isFavorite ?? this.isFavorite,
      dialect: dialect ?? this.dialect,
      accent: accent ?? this.accent,
      dialectConfidence: dialectConfidence ?? this.dialectConfidence,
      accentConfidence: accentConfidence ?? this.accentConfidence,
      culturalContext: culturalContext ?? this.culturalContext,
      slangIdiom: slangIdiom ?? this.slangIdiom,
      pronunciation: pronunciation ?? this.pronunciation,
    );
  }

  /// Get the source language name
  String getSourceLanguageName() {
    return _getLanguageName(sourceLanguage);
  }

  /// Get the target language name
  String getTargetLanguageName() {
    return _getLanguageName(targetLanguage);
  }

  /// Get the language name from a language code
  String _getLanguageName(String code) {
    switch (code) {
      case 'en':
        return 'English';
      case 'fr':
        return 'French';
      case 'yo':
        return 'Yoruba';
      case 'ig':
        return 'Igbo';
      case 'ha':
        return 'Hausa';
      case 'sw':
        return 'Swahili';
      default:
        return code;
    }
  }

  /// Get the formatted timestamp
  String get formattedTimestamp {
    return '${timestamp.year}-${timestamp.month.toString().padLeft(2, '0')}-${timestamp.day.toString().padLeft(2, '0')} ${timestamp.hour.toString().padLeft(2, '0')}:${timestamp.minute.toString().padLeft(2, '0')}';
  }

  /// Converts to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'originalAudioPath': originalAudioPath,
      'translatedAudioPath': translatedAudioPath,
      'originalText': originalText,
      'translatedText': translatedText,
      'sourceLanguage': sourceLanguage,
      'targetLanguage': targetLanguage,
      'status': status.index,
      'errorMessage': errorMessage,
      'timestamp': timestamp.toIso8601String(),
      'isFavorite': isFavorite,
      'dialect': dialect?.toJson(),
      'accent': accent?.toJson(),
      'dialectConfidence': dialectConfidence,
      'accentConfidence': accentConfidence,
      'culturalContext': culturalContext,
      'slangIdiom': slangIdiom,
      'pronunciation': pronunciation,
    };
  }

  /// Creates from JSON
  factory VoiceTranslationModel.fromJson(Map<String, dynamic> json) {
    return VoiceTranslationModel(
      id: json['id'] as String,
      originalAudioPath: json['originalAudioPath'] as String?,
      translatedAudioPath: json['translatedAudioPath'] as String?,
      originalText: json['originalText'] as String?,
      translatedText: json['translatedText'] as String?,
      sourceLanguage: json['sourceLanguage'] as String,
      targetLanguage: json['targetLanguage'] as String,
      status: VoiceTranslationStatus.values[json['status'] as int],
      errorMessage: json['errorMessage'] as String?,
      timestamp: DateTime.parse(json['timestamp'] as String),
      isFavorite: json['isFavorite'] as bool? ?? false,
      dialect: json['dialect'] != null
          ? DialectModel.fromJson(json['dialect'] as Map<String, dynamic>)
          : null,
      accent: json['accent'] != null
          ? AccentModel.fromJson(json['accent'] as Map<String, dynamic>)
          : null,
      dialectConfidence: json['dialectConfidence'] != null
          ? (json['dialectConfidence'] as num).toDouble()
          : null,
      accentConfidence: json['accentConfidence'] != null
          ? (json['accentConfidence'] as num).toDouble()
          : null,
      culturalContext: json['culturalContext'] as String?,
      slangIdiom: json['slangIdiom'] as String?,
      pronunciation: json['pronunciation'] as String?,
    );
  }

  /// Check if the source language is RTL
  bool get isSourceRTL => RTLUtils.isRTL(sourceLanguage);

  /// Check if the target language is RTL
  bool get isTargetRTL => RTLUtils.isRTL(targetLanguage);

  /// Get the text direction for the source language
  TextDirection get sourceTextDirection =>
      RTLUtils.getTextDirection(sourceLanguage);

  /// Get the text direction for the target language
  TextDirection get targetTextDirection =>
      RTLUtils.getTextDirection(targetLanguage);

  /// Get the text alignment for the source language
  TextAlign get sourceTextAlign => RTLUtils.getTextAlignment(sourceLanguage);

  /// Get the text alignment for the target language
  TextAlign get targetTextAlign => RTLUtils.getTextAlignment(targetLanguage);
}
