import 'package:equatable/equatable.dart';

/// The type of translation
enum TranslationType {
  /// Text to text translation
  textToText,
  
  /// Speech to text translation
  speechToText,
  
  /// Text to speech translation
  textToSpeech,
  
  /// Speech to speech translation
  speechToSpeech,
}

/// The status of a translation
enum TranslationStatus {
  /// The translation is in progress
  inProgress,
  
  /// The translation completed successfully
  completed,
  
  /// The translation failed
  failed,
  
  /// The translation was canceled
  canceled,
}

/// A model representing a translation history entry
class TranslationHistoryEntry extends Equatable {
  /// The unique identifier
  final String id;
  
  /// The source text or audio path
  final String source;
  
  /// The translated text or audio path
  final String? translation;
  
  /// The source language code
  final String sourceLanguageCode;
  
  /// The target language code
  final String targetLanguageCode;
  
  /// The source language name
  final String sourceLanguageName;
  
  /// The target language name
  final String targetLanguageName;
  
  /// The type of translation
  final TranslationType type;
  
  /// The status of the translation
  final TranslationStatus status;
  
  /// The timestamp when the translation was created
  final DateTime timestamp;
  
  /// Whether the translation is favorited
  final bool isFavorite;
  
  /// The category of the translation
  final String? category;
  
  /// The source audio path (for speech translations)
  final String? sourceAudioPath;
  
  /// The translated audio path (for speech translations)
  final String? translatedAudioPath;
  
  /// The error message (if the translation failed)
  final String? errorMessage;
  
  /// Creates a new translation history entry
  const TranslationHistoryEntry({
    required this.id,
    required this.source,
    this.translation,
    required this.sourceLanguageCode,
    required this.targetLanguageCode,
    required this.sourceLanguageName,
    required this.targetLanguageName,
    required this.type,
    required this.status,
    required this.timestamp,
    this.isFavorite = false,
    this.category,
    this.sourceAudioPath,
    this.translatedAudioPath,
    this.errorMessage,
  });
  
  /// Creates a copy of this entry with the given fields replaced
  TranslationHistoryEntry copyWith({
    String? id,
    String? source,
    String? translation,
    String? sourceLanguageCode,
    String? targetLanguageCode,
    String? sourceLanguageName,
    String? targetLanguageName,
    TranslationType? type,
    TranslationStatus? status,
    DateTime? timestamp,
    bool? isFavorite,
    String? category,
    String? sourceAudioPath,
    String? translatedAudioPath,
    String? errorMessage,
  }) {
    return TranslationHistoryEntry(
      id: id ?? this.id,
      source: source ?? this.source,
      translation: translation ?? this.translation,
      sourceLanguageCode: sourceLanguageCode ?? this.sourceLanguageCode,
      targetLanguageCode: targetLanguageCode ?? this.targetLanguageCode,
      sourceLanguageName: sourceLanguageName ?? this.sourceLanguageName,
      targetLanguageName: targetLanguageName ?? this.targetLanguageName,
      type: type ?? this.type,
      status: status ?? this.status,
      timestamp: timestamp ?? this.timestamp,
      isFavorite: isFavorite ?? this.isFavorite,
      category: category ?? this.category,
      sourceAudioPath: sourceAudioPath ?? this.sourceAudioPath,
      translatedAudioPath: translatedAudioPath ?? this.translatedAudioPath,
      errorMessage: errorMessage ?? this.errorMessage,
    );
  }
  
  /// Creates a translation history entry from JSON
  factory TranslationHistoryEntry.fromJson(Map<String, dynamic> json) {
    return TranslationHistoryEntry(
      id: json['id'] as String,
      source: json['source'] as String,
      translation: json['translation'] as String?,
      sourceLanguageCode: json['sourceLanguageCode'] as String,
      targetLanguageCode: json['targetLanguageCode'] as String,
      sourceLanguageName: json['sourceLanguageName'] as String,
      targetLanguageName: json['targetLanguageName'] as String,
      type: TranslationType.values.firstWhere(
        (e) => e.toString() == json['type'],
        orElse: () => TranslationType.textToText,
      ),
      status: TranslationStatus.values.firstWhere(
        (e) => e.toString() == json['status'],
        orElse: () => TranslationStatus.completed,
      ),
      timestamp: DateTime.parse(json['timestamp'] as String),
      isFavorite: json['isFavorite'] as bool? ?? false,
      category: json['category'] as String?,
      sourceAudioPath: json['sourceAudioPath'] as String?,
      translatedAudioPath: json['translatedAudioPath'] as String?,
      errorMessage: json['errorMessage'] as String?,
    );
  }
  
  /// Converts the translation history entry to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'source': source,
      'translation': translation,
      'sourceLanguageCode': sourceLanguageCode,
      'targetLanguageCode': targetLanguageCode,
      'sourceLanguageName': sourceLanguageName,
      'targetLanguageName': targetLanguageName,
      'type': type.toString(),
      'status': status.toString(),
      'timestamp': timestamp.toIso8601String(),
      'isFavorite': isFavorite,
      'category': category,
      'sourceAudioPath': sourceAudioPath,
      'translatedAudioPath': translatedAudioPath,
      'errorMessage': errorMessage,
    };
  }
  
  @override
  List<Object?> get props => [
        id,
        source,
        translation,
        sourceLanguageCode,
        targetLanguageCode,
        sourceLanguageName,
        targetLanguageName,
        type,
        status,
        timestamp,
        isFavorite,
        category,
        sourceAudioPath,
        translatedAudioPath,
        errorMessage,
      ];
      
  @override
  String toString() {
    return 'TranslationHistoryEntry{id: $id, source: $source, translation: $translation, '
        'sourceLanguageCode: $sourceLanguageCode, targetLanguageCode: $targetLanguageCode, '
        'type: $type, status: $status, timestamp: $timestamp}';
  }
}
