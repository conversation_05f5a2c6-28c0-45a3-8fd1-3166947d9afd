import 'package:culture_connect/models/translation/slang_idiom_model.dart';

/// A model representing slang and idiom information for a translation
class TranslationSlangIdiom {
  /// The list of expressions
  final List<SlangIdiomExpression> expressions;
  
  /// Whether slang and idiom information is available
  final bool hasExpressions;
  
  /// The source language code
  final String sourceLanguage;
  
  /// The target language code
  final String targetLanguage;
  
  /// The source region or dialect (if applicable)
  final String? sourceRegion;
  
  /// The target region or dialect (if applicable)
  final String? targetRegion;
  
  /// General notes about slang and idioms in this language pair
  final String? generalNotes;
  
  /// Creates a new translation slang idiom
  const TranslationSlangIdiom({
    required this.expressions,
    required this.hasExpressions,
    required this.sourceLanguage,
    required this.targetLanguage,
    this.sourceRegion,
    this.targetRegion,
    this.generalNotes,
  });
  
  /// Creates an empty slang idiom
  factory TranslationSlangIdiom.empty({
    required String sourceLanguage,
    required String targetLanguage,
  }) {
    return TranslationSlangIdiom(
      expressions: const [],
      hasExpressions: false,
      sourceLanguage: sourceLanguage,
      targetLanguage: targetLanguage,
    );
  }
  
  /// Creates a copy with some fields replaced
  TranslationSlangIdiom copyWith({
    List<SlangIdiomExpression>? expressions,
    bool? hasExpressions,
    String? sourceLanguage,
    String? targetLanguage,
    String? sourceRegion,
    String? targetRegion,
    String? generalNotes,
  }) {
    return TranslationSlangIdiom(
      expressions: expressions ?? this.expressions,
      hasExpressions: hasExpressions ?? this.hasExpressions,
      sourceLanguage: sourceLanguage ?? this.sourceLanguage,
      targetLanguage: targetLanguage ?? this.targetLanguage,
      sourceRegion: sourceRegion ?? this.sourceRegion,
      targetRegion: targetRegion ?? this.targetRegion,
      generalNotes: generalNotes ?? this.generalNotes,
    );
  }
  
  /// Converts to JSON
  Map<String, dynamic> toJson() {
    return {
      'expressions': expressions.map((expression) => expression.toJson()).toList(),
      'hasExpressions': hasExpressions,
      'sourceLanguage': sourceLanguage,
      'targetLanguage': targetLanguage,
      'sourceRegion': sourceRegion,
      'targetRegion': targetRegion,
      'generalNotes': generalNotes,
    };
  }
  
  /// Creates from JSON
  factory TranslationSlangIdiom.fromJson(Map<String, dynamic> json) {
    return TranslationSlangIdiom(
      expressions: (json['expressions'] as List)
          .map((expressionJson) => SlangIdiomExpression.fromJson(expressionJson as Map<String, dynamic>))
          .toList(),
      hasExpressions: json['hasExpressions'] as bool,
      sourceLanguage: json['sourceLanguage'] as String,
      targetLanguage: json['targetLanguage'] as String,
      sourceRegion: json['sourceRegion'] as String?,
      targetRegion: json['targetRegion'] as String?,
      generalNotes: json['generalNotes'] as String?,
    );
  }
  
  /// Get expressions of a specific type
  List<SlangIdiomExpression> getExpressionsOfType(ExpressionType type) {
    return expressions.where((expression) => expression.type == type).toList();
  }
  
  /// Get expressions for a specific text segment
  List<SlangIdiomExpression> getExpressionsForSegment(String segment) {
    return expressions.where((expression) => 
      expression.expression.contains(segment) || 
      segment.contains(expression.expression)
    ).toList();
  }
  
  /// Get expressions for a specific position in the text
  List<SlangIdiomExpression> getExpressionsForPosition(int position) {
    return expressions.where((expression) => 
      position >= expression.startIndex && 
      position <= expression.endIndex
    ).toList();
  }
  
  /// Get potentially offensive expressions
  List<SlangIdiomExpression> getPotentiallyOffensiveExpressions() {
    return expressions.where((expression) => expression.isPotentiallyOffensive).toList();
  }
  
  /// Check if there are any potentially offensive expressions
  bool get hasPotentiallyOffensiveContent => 
      expressions.any((expression) => expression.isPotentiallyOffensive);
  
  /// Get the number of expressions
  int get expressionCount => expressions.length;
  
  /// Get the number of slang expressions
  int get slangCount => 
      expressions.where((expression) => expression.type == ExpressionType.slang).length;
  
  /// Get the number of idiom expressions
  int get idiomCount => 
      expressions.where((expression) => expression.type == ExpressionType.idiom).length;
  
  /// Check if there are any expressions
  bool get hasAnyExpressions => expressions.isNotEmpty;
  
  /// Get expressions by formality level
  List<SlangIdiomExpression> getExpressionsByFormalityLevel(FormalityLevel level) {
    return expressions.where((expression) => expression.formalityLevel == level).toList();
  }
  
  /// Get expressions by region
  List<SlangIdiomExpression> getExpressionsByRegion(String region) {
    return expressions.where((expression) => expression.region == region).toList();
  }
}
