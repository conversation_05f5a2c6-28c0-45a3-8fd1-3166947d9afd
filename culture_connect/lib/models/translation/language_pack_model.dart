import 'package:culture_connect/models/translation/language_model.dart';

/// Status of a language pack
enum LanguagePackStatus {
  /// Not downloaded
  notDownloaded,
  
  /// Downloading
  downloading,
  
  /// Downloaded
  downloaded,
  
  /// Error occurred during download
  error,
  
  /// Update available
  updateAvailable,
  
  /// Updating
  updating,
}

/// A model representing a downloaded language pack
class LanguagePackModel {
  /// The language code
  final String languageCode;
  
  /// The version of the language pack
  final String version;
  
  /// The size of the language pack in MB
  final double sizeMB;
  
  /// The local path to the language pack files
  final String localPath;
  
  /// When the language pack was downloaded
  final DateTime downloadedAt;
  
  /// When the language pack was last used
  final DateTime lastUsedAt;
  
  /// The status of the language pack
  final LanguagePackStatus status;
  
  /// The download progress (0.0 to 1.0)
  final double downloadProgress;
  
  /// The selected dialect for this language pack
  final String? selectedDialect;
  
  /// Whether the language pack is the primary language
  final bool isPrimary;
  
  /// Creates a new language pack model
  const LanguagePackModel({
    required this.languageCode,
    required this.version,
    required this.sizeMB,
    required this.localPath,
    required this.downloadedAt,
    required this.lastUsedAt,
    required this.status,
    this.downloadProgress = 0.0,
    this.selectedDialect,
    this.isPrimary = false,
  });
  
  /// Creates a copy with some fields replaced
  LanguagePackModel copyWith({
    String? languageCode,
    String? version,
    double? sizeMB,
    String? localPath,
    DateTime? downloadedAt,
    DateTime? lastUsedAt,
    LanguagePackStatus? status,
    double? downloadProgress,
    String? selectedDialect,
    bool? isPrimary,
  }) {
    return LanguagePackModel(
      languageCode: languageCode ?? this.languageCode,
      version: version ?? this.version,
      sizeMB: sizeMB ?? this.sizeMB,
      localPath: localPath ?? this.localPath,
      downloadedAt: downloadedAt ?? this.downloadedAt,
      lastUsedAt: lastUsedAt ?? this.lastUsedAt,
      status: status ?? this.status,
      downloadProgress: downloadProgress ?? this.downloadProgress,
      selectedDialect: selectedDialect ?? this.selectedDialect,
      isPrimary: isPrimary ?? this.isPrimary,
    );
  }
  
  /// Get the language name
  String get languageName {
    final language = supportedLanguages.firstWhere(
      (lang) => lang.code == languageCode,
      orElse: () => const LanguageModel(
        code: 'unknown',
        name: 'Unknown',
        flag: '🏳️',
      ),
    );
    return language.name;
  }
  
  /// Get the language flag
  String get languageFlag {
    final language = supportedLanguages.firstWhere(
      (lang) => lang.code == languageCode,
      orElse: () => const LanguageModel(
        code: 'unknown',
        name: 'Unknown',
        flag: '🏳️',
      ),
    );
    return language.flag;
  }
  
  /// Get the formatted downloaded date
  String get formattedDownloadedAt {
    return '${downloadedAt.year}-${downloadedAt.month.toString().padLeft(2, '0')}-${downloadedAt.day.toString().padLeft(2, '0')}';
  }
  
  /// Get the formatted last used date
  String get formattedLastUsedAt {
    return '${lastUsedAt.year}-${lastUsedAt.month.toString().padLeft(2, '0')}-${lastUsedAt.day.toString().padLeft(2, '0')}';
  }
  
  /// Converts to JSON
  Map<String, dynamic> toJson() {
    return {
      'languageCode': languageCode,
      'version': version,
      'sizeMB': sizeMB,
      'localPath': localPath,
      'downloadedAt': downloadedAt.toIso8601String(),
      'lastUsedAt': lastUsedAt.toIso8601String(),
      'status': status.index,
      'downloadProgress': downloadProgress,
      'selectedDialect': selectedDialect,
      'isPrimary': isPrimary,
    };
  }
  
  /// Creates from JSON
  factory LanguagePackModel.fromJson(Map<String, dynamic> json) {
    return LanguagePackModel(
      languageCode: json['languageCode'] as String,
      version: json['version'] as String,
      sizeMB: (json['sizeMB'] as num).toDouble(),
      localPath: json['localPath'] as String,
      downloadedAt: DateTime.parse(json['downloadedAt'] as String),
      lastUsedAt: DateTime.parse(json['lastUsedAt'] as String),
      status: LanguagePackStatus.values[json['status'] as int],
      downloadProgress: (json['downloadProgress'] as num?)?.toDouble() ?? 0.0,
      selectedDialect: json['selectedDialect'] as String?,
      isPrimary: json['isPrimary'] as bool? ?? false,
    );
  }
}
