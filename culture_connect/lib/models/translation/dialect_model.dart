import 'package:flutter/foundation.dart';

/// A model representing a language dialect
class DialectModel {
  /// The dialect code (e.g., 'en-us', 'en-gb')
  final String code;
  
  /// The display name of the dialect
  final String name;
  
  /// The region where this dialect is primarily spoken
  final String region;
  
  /// The flag emoji of the region
  final String flag;
  
  /// The language code this dialect belongs to
  final String languageCode;
  
  /// Whether this dialect is available offline
  final bool isOfflineAvailable;
  
  /// Whether this dialect is currently downloaded
  final bool isDownloaded;
  
  /// The download size in MB
  final double downloadSizeMB;
  
  /// The download progress (0.0 to 1.0)
  final double downloadProgress;
  
  /// Whether this dialect is currently downloading
  final bool isDownloading;
  
  /// The list of common words or phrases specific to this dialect
  final List<String> dialectSpecificTerms;
  
  /// The list of accent variants associated with this dialect
  final List<String> accentVariants;
  
  /// Creates a new dialect model
  const DialectModel({
    required this.code,
    required this.name,
    required this.region,
    required this.flag,
    required this.languageCode,
    this.isOfflineAvailable = false,
    this.isDownloaded = false,
    this.downloadSizeMB = 0.0,
    this.downloadProgress = 0.0,
    this.isDownloading = false,
    this.dialectSpecificTerms = const [],
    this.accentVariants = const [],
  });
  
  /// Creates a copy with some fields replaced
  DialectModel copyWith({
    String? code,
    String? name,
    String? region,
    String? flag,
    String? languageCode,
    bool? isOfflineAvailable,
    bool? isDownloaded,
    double? downloadSizeMB,
    double? downloadProgress,
    bool? isDownloading,
    List<String>? dialectSpecificTerms,
    List<String>? accentVariants,
  }) {
    return DialectModel(
      code: code ?? this.code,
      name: name ?? this.name,
      region: region ?? this.region,
      flag: flag ?? this.flag,
      languageCode: languageCode ?? this.languageCode,
      isOfflineAvailable: isOfflineAvailable ?? this.isOfflineAvailable,
      isDownloaded: isDownloaded ?? this.isDownloaded,
      downloadSizeMB: downloadSizeMB ?? this.downloadSizeMB,
      downloadProgress: downloadProgress ?? this.downloadProgress,
      isDownloading: isDownloading ?? this.isDownloading,
      dialectSpecificTerms: dialectSpecificTerms ?? this.dialectSpecificTerms,
      accentVariants: accentVariants ?? this.accentVariants,
    );
  }
  
  /// Converts to JSON
  Map<String, dynamic> toJson() {
    return {
      'code': code,
      'name': name,
      'region': region,
      'flag': flag,
      'languageCode': languageCode,
      'isOfflineAvailable': isOfflineAvailable,
      'isDownloaded': isDownloaded,
      'downloadSizeMB': downloadSizeMB,
      'downloadProgress': downloadProgress,
      'isDownloading': isDownloading,
      'dialectSpecificTerms': dialectSpecificTerms,
      'accentVariants': accentVariants,
    };
  }
  
  /// Creates from JSON
  factory DialectModel.fromJson(Map<String, dynamic> json) {
    return DialectModel(
      code: json['code'] as String,
      name: json['name'] as String,
      region: json['region'] as String,
      flag: json['flag'] as String,
      languageCode: json['languageCode'] as String,
      isOfflineAvailable: json['isOfflineAvailable'] as bool? ?? false,
      isDownloaded: json['isDownloaded'] as bool? ?? false,
      downloadSizeMB: (json['downloadSizeMB'] as num?)?.toDouble() ?? 0.0,
      downloadProgress: (json['downloadProgress'] as num?)?.toDouble() ?? 0.0,
      isDownloading: json['isDownloading'] as bool? ?? false,
      dialectSpecificTerms: json['dialectSpecificTerms'] != null
          ? List<String>.from(json['dialectSpecificTerms'] as List)
          : const [],
      accentVariants: json['accentVariants'] != null
          ? List<String>.from(json['accentVariants'] as List)
          : const [],
    );
  }
  
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    
    return other is DialectModel &&
        other.code == code &&
        other.name == name &&
        other.region == region &&
        other.flag == flag &&
        other.languageCode == languageCode &&
        other.isOfflineAvailable == isOfflineAvailable &&
        other.isDownloaded == isDownloaded &&
        other.downloadSizeMB == downloadSizeMB &&
        other.downloadProgress == downloadProgress &&
        other.isDownloading == isDownloading &&
        listEquals(other.dialectSpecificTerms, dialectSpecificTerms) &&
        listEquals(other.accentVariants, accentVariants);
  }
  
  @override
  int get hashCode {
    return code.hashCode ^
        name.hashCode ^
        region.hashCode ^
        flag.hashCode ^
        languageCode.hashCode ^
        isOfflineAvailable.hashCode ^
        isDownloaded.hashCode ^
        downloadSizeMB.hashCode ^
        downloadProgress.hashCode ^
        isDownloading.hashCode ^
        dialectSpecificTerms.hashCode ^
        accentVariants.hashCode;
  }
}

/// A model representing a detection result for a dialect
class DialectDetectionResult {
  /// The detected dialect
  final DialectModel dialect;
  
  /// The confidence score (0.0 to 1.0)
  final double confidence;
  
  /// The detected dialect-specific terms
  final List<String> detectedTerms;
  
  /// Creates a new dialect detection result
  const DialectDetectionResult({
    required this.dialect,
    required this.confidence,
    this.detectedTerms = const [],
  });
  
  /// Creates a copy with some fields replaced
  DialectDetectionResult copyWith({
    DialectModel? dialect,
    double? confidence,
    List<String>? detectedTerms,
  }) {
    return DialectDetectionResult(
      dialect: dialect ?? this.dialect,
      confidence: confidence ?? this.confidence,
      detectedTerms: detectedTerms ?? this.detectedTerms,
    );
  }
  
  /// Converts to JSON
  Map<String, dynamic> toJson() {
    return {
      'dialect': dialect.toJson(),
      'confidence': confidence,
      'detectedTerms': detectedTerms,
    };
  }
  
  /// Creates from JSON
  factory DialectDetectionResult.fromJson(Map<String, dynamic> json) {
    return DialectDetectionResult(
      dialect: DialectModel.fromJson(json['dialect'] as Map<String, dynamic>),
      confidence: (json['confidence'] as num).toDouble(),
      detectedTerms: json['detectedTerms'] != null
          ? List<String>.from(json['detectedTerms'] as List)
          : const [],
    );
  }
}
