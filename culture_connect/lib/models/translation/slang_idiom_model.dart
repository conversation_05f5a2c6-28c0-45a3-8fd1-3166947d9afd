import 'package:flutter/material.dart';

/// Type of expression
enum ExpressionType {
  /// Slang expression
  slang,
  
  /// Idiom expression
  idiom,
  
  /// Colloquialism
  colloquialism,
  
  /// Proverb
  proverb,
  
  /// Metaphor
  metaphor,
  
  /// Euphemism
  euphemism,
  
  /// Jargon
  jargon,
}

/// Extension on ExpressionType to provide additional functionality
extension ExpressionTypeExtension on ExpressionType {
  /// Get the display name of the expression type
  String get displayName {
    switch (this) {
      case ExpressionType.slang:
        return 'Slang';
      case ExpressionType.idiom:
        return 'Idiom';
      case ExpressionType.colloquialism:
        return 'Colloquialism';
      case ExpressionType.proverb:
        return 'Proverb';
      case ExpressionType.metaphor:
        return 'Metaphor';
      case ExpressionType.euphemism:
        return 'Euphemism';
      case ExpressionType.jargon:
        return 'Jargon';
    }
  }
  
  /// Get the icon associated with the expression type
  IconData get icon {
    switch (this) {
      case ExpressionType.slang:
        return Icons.chat_bubble_outline;
      case ExpressionType.idiom:
        return Icons.format_quote;
      case ExpressionType.colloquialism:
        return Icons.people_outline;
      case ExpressionType.proverb:
        return Icons.auto_stories_outlined;
      case ExpressionType.metaphor:
        return Icons.compare_arrows;
      case ExpressionType.euphemism:
        return Icons.filter_alt_outlined;
      case ExpressionType.jargon:
        return Icons.work_outline;
    }
  }
  
  /// Get the color associated with the expression type
  Color get color {
    switch (this) {
      case ExpressionType.slang:
        return Colors.orange;
      case ExpressionType.idiom:
        return Colors.purple;
      case ExpressionType.colloquialism:
        return Colors.teal;
      case ExpressionType.proverb:
        return Colors.indigo;
      case ExpressionType.metaphor:
        return Colors.deepPurple;
      case ExpressionType.euphemism:
        return Colors.pink;
      case ExpressionType.jargon:
        return Colors.brown;
    }
  }
}

/// Formality level of an expression
enum FormalityLevel {
  /// Very informal expression
  veryInformal,
  
  /// Informal expression
  informal,
  
  /// Neutral expression
  neutral,
  
  /// Formal expression
  formal,
  
  /// Very formal expression
  veryFormal,
}

/// Extension on FormalityLevel to provide additional functionality
extension FormalityLevelExtension on FormalityLevel {
  /// Get the display name of the formality level
  String get displayName {
    switch (this) {
      case FormalityLevel.veryInformal:
        return 'Very Informal';
      case FormalityLevel.informal:
        return 'Informal';
      case FormalityLevel.neutral:
        return 'Neutral';
      case FormalityLevel.formal:
        return 'Formal';
      case FormalityLevel.veryFormal:
        return 'Very Formal';
    }
  }
  
  /// Get the color associated with the formality level
  Color get color {
    switch (this) {
      case FormalityLevel.veryInformal:
        return Colors.red;
      case FormalityLevel.informal:
        return Colors.orange;
      case FormalityLevel.neutral:
        return Colors.blue;
      case FormalityLevel.formal:
        return Colors.green;
      case FormalityLevel.veryFormal:
        return Colors.purple;
    }
  }
}

/// A model representing a slang or idiom expression
class SlangIdiomExpression {
  /// Unique identifier for the expression
  final String id;
  
  /// The expression text
  final String expression;
  
  /// Start index in the text
  final int startIndex;
  
  /// End index in the text
  final int endIndex;
  
  /// The type of expression
  final ExpressionType type;
  
  /// The literal meaning of the expression
  final String literalMeaning;
  
  /// The actual meaning of the expression
  final String actualMeaning;
  
  /// Usage examples
  final List<String>? examples;
  
  /// The language code this expression belongs to
  final String languageCode;
  
  /// The region or dialect this expression is specific to (if any)
  final String? region;
  
  /// The formality level of this expression
  final FormalityLevel formalityLevel;
  
  /// Whether this expression is potentially offensive
  final bool isPotentiallyOffensive;
  
  /// The origin or etymology of the expression
  final String? origin;
  
  /// Standard alternatives to this expression
  final List<String>? standardAlternatives;
  
  /// Additional notes about the expression
  final String? notes;
  
  /// Creates a new slang or idiom expression
  const SlangIdiomExpression({
    required this.id,
    required this.expression,
    required this.startIndex,
    required this.endIndex,
    required this.type,
    required this.literalMeaning,
    required this.actualMeaning,
    this.examples,
    required this.languageCode,
    this.region,
    required this.formalityLevel,
    this.isPotentiallyOffensive = false,
    this.origin,
    this.standardAlternatives,
    this.notes,
  });
  
  /// Creates a copy with some fields replaced
  SlangIdiomExpression copyWith({
    String? id,
    String? expression,
    int? startIndex,
    int? endIndex,
    ExpressionType? type,
    String? literalMeaning,
    String? actualMeaning,
    List<String>? examples,
    String? languageCode,
    String? region,
    FormalityLevel? formalityLevel,
    bool? isPotentiallyOffensive,
    String? origin,
    List<String>? standardAlternatives,
    String? notes,
  }) {
    return SlangIdiomExpression(
      id: id ?? this.id,
      expression: expression ?? this.expression,
      startIndex: startIndex ?? this.startIndex,
      endIndex: endIndex ?? this.endIndex,
      type: type ?? this.type,
      literalMeaning: literalMeaning ?? this.literalMeaning,
      actualMeaning: actualMeaning ?? this.actualMeaning,
      examples: examples ?? this.examples,
      languageCode: languageCode ?? this.languageCode,
      region: region ?? this.region,
      formalityLevel: formalityLevel ?? this.formalityLevel,
      isPotentiallyOffensive: isPotentiallyOffensive ?? this.isPotentiallyOffensive,
      origin: origin ?? this.origin,
      standardAlternatives: standardAlternatives ?? this.standardAlternatives,
      notes: notes ?? this.notes,
    );
  }
  
  /// Converts to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'expression': expression,
      'startIndex': startIndex,
      'endIndex': endIndex,
      'type': type.index,
      'literalMeaning': literalMeaning,
      'actualMeaning': actualMeaning,
      'examples': examples,
      'languageCode': languageCode,
      'region': region,
      'formalityLevel': formalityLevel.index,
      'isPotentiallyOffensive': isPotentiallyOffensive,
      'origin': origin,
      'standardAlternatives': standardAlternatives,
      'notes': notes,
    };
  }
  
  /// Creates from JSON
  factory SlangIdiomExpression.fromJson(Map<String, dynamic> json) {
    return SlangIdiomExpression(
      id: json['id'] as String,
      expression: json['expression'] as String,
      startIndex: json['startIndex'] as int,
      endIndex: json['endIndex'] as int,
      type: ExpressionType.values[json['type'] as int],
      literalMeaning: json['literalMeaning'] as String,
      actualMeaning: json['actualMeaning'] as String,
      examples: json['examples'] != null
          ? List<String>.from(json['examples'] as List)
          : null,
      languageCode: json['languageCode'] as String,
      region: json['region'] as String?,
      formalityLevel: FormalityLevel.values[json['formalityLevel'] as int],
      isPotentiallyOffensive: json['isPotentiallyOffensive'] as bool? ?? false,
      origin: json['origin'] as String?,
      standardAlternatives: json['standardAlternatives'] != null
          ? List<String>.from(json['standardAlternatives'] as List)
          : null,
      notes: json['notes'] as String?,
    );
  }
}
