import 'package:equatable/equatable.dart';

/// A model representing dialect and accent information
class DialectAccentModel extends Equatable {
  /// The language code (e.g., 'en', 'es')
  final String languageCode;
  
  /// The region code (e.g., 'US', 'GB')
  final String? regionCode;
  
  /// The name of the dialect
  final String dialectName;
  
  /// The name of the accent
  final String accentName;
  
  /// The confidence score (0.0 to 1.0)
  final double confidenceScore;
  
  /// Alternative dialects that might match
  final List<String> alternativeDialects;
  
  /// Common expressions used in this dialect/region
  final List<String> regionalExpressions;
  
  /// Tips for pronouncing words in this dialect/accent
  final List<String> pronunciationTips;
  
  /// Creates a new dialect and accent model
  const DialectAccentModel({
    required this.languageCode,
    this.regionCode,
    required this.dialectName,
    required this.accentName,
    required this.confidenceScore,
    required this.alternativeDialects,
    required this.regionalExpressions,
    required this.pronunciationTips,
  });
  
  /// Creates an empty dialect and accent model
  factory DialectAccentModel.empty({
    required String languageCode,
    String? regionCode,
  }) {
    return DialectAccentModel(
      languageCode: languageCode,
      regionCode: regionCode,
      dialectName: 'Unknown',
      accentName: 'Unknown',
      confidenceScore: 0.0,
      alternativeDialects: const [],
      regionalExpressions: const [],
      pronunciationTips: const [],
    );
  }
  
  /// Creates a dialect and accent model from JSON
  factory DialectAccentModel.fromJson(Map<String, dynamic> json) {
    return DialectAccentModel(
      languageCode: json['languageCode'] as String,
      regionCode: json['regionCode'] as String?,
      dialectName: json['dialectName'] as String,
      accentName: json['accentName'] as String,
      confidenceScore: json['confidenceScore'] as double,
      alternativeDialects: (json['alternativeDialects'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      regionalExpressions: (json['regionalExpressions'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      pronunciationTips: (json['pronunciationTips'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
    );
  }
  
  /// Converts the model to JSON
  Map<String, dynamic> toJson() {
    return {
      'languageCode': languageCode,
      'regionCode': regionCode,
      'dialectName': dialectName,
      'accentName': accentName,
      'confidenceScore': confidenceScore,
      'alternativeDialects': alternativeDialects,
      'regionalExpressions': regionalExpressions,
      'pronunciationTips': pronunciationTips,
    };
  }
  
  /// Creates a copy of this model with the given fields replaced
  DialectAccentModel copyWith({
    String? languageCode,
    String? regionCode,
    String? dialectName,
    String? accentName,
    double? confidenceScore,
    List<String>? alternativeDialects,
    List<String>? regionalExpressions,
    List<String>? pronunciationTips,
  }) {
    return DialectAccentModel(
      languageCode: languageCode ?? this.languageCode,
      regionCode: regionCode ?? this.regionCode,
      dialectName: dialectName ?? this.dialectName,
      accentName: accentName ?? this.accentName,
      confidenceScore: confidenceScore ?? this.confidenceScore,
      alternativeDialects: alternativeDialects ?? this.alternativeDialects,
      regionalExpressions: regionalExpressions ?? this.regionalExpressions,
      pronunciationTips: pronunciationTips ?? this.pronunciationTips,
    );
  }
  
  @override
  List<Object?> get props => [
        languageCode,
        regionCode,
        dialectName,
        accentName,
        confidenceScore,
        alternativeDialects,
        regionalExpressions,
        pronunciationTips,
      ];
      
  @override
  String toString() {
    return 'DialectAccentModel{languageCode: $languageCode, regionCode: $regionCode, '
        'dialectName: $dialectName, accentName: $accentName, '
        'confidenceScore: $confidenceScore}';
  }
}
