import 'package:flutter/foundation.dart';

/// Accent difficulty level
enum AccentDifficulty {
  /// Easy to understand
  easy,
  
  /// Moderate difficulty
  moderate,
  
  /// Difficult to understand
  difficult,
  
  /// Very difficult to understand
  veryDifficult,
}

/// Accent characteristic type
enum AccentCharacteristicType {
  /// Vowel sounds
  vowelSounds,
  
  /// Consonant sounds
  consonantSounds,
  
  /// Intonation patterns
  intonation,
  
  /// Rhythm patterns
  rhythm,
  
  /// Stress patterns
  stress,
  
  /// Speed of speech
  speed,
}

/// A model representing an accent characteristic
class AccentCharacteristic {
  /// The type of characteristic
  final AccentCharacteristicType type;
  
  /// The description of the characteristic
  final String description;
  
  /// Example words or phrases demonstrating this characteristic
  final List<String> examples;
  
  /// Audio file path for this characteristic
  final String? audioPath;
  
  /// Creates a new accent characteristic
  const AccentCharacteristic({
    required this.type,
    required this.description,
    this.examples = const [],
    this.audioPath,
  });
  
  /// Creates a copy with some fields replaced
  AccentCharacteristic copyWith({
    AccentCharacteristicType? type,
    String? description,
    List<String>? examples,
    String? audioPath,
  }) {
    return AccentCharacteristic(
      type: type ?? this.type,
      description: description ?? this.description,
      examples: examples ?? this.examples,
      audioPath: audioPath ?? this.audioPath,
    );
  }
  
  /// Converts to JSON
  Map<String, dynamic> toJson() {
    return {
      'type': type.index,
      'description': description,
      'examples': examples,
      'audioPath': audioPath,
    };
  }
  
  /// Creates from JSON
  factory AccentCharacteristic.fromJson(Map<String, dynamic> json) {
    return AccentCharacteristic(
      type: AccentCharacteristicType.values[json['type'] as int],
      description: json['description'] as String,
      examples: json['examples'] != null
          ? List<String>.from(json['examples'] as List)
          : const [],
      audioPath: json['audioPath'] as String?,
    );
  }
}

/// A model representing a language accent
class AccentModel {
  /// The accent code (e.g., 'en-us-southern', 'en-gb-cockney')
  final String code;
  
  /// The display name of the accent
  final String name;
  
  /// The region where this accent is primarily spoken
  final String region;
  
  /// The dialect code this accent belongs to
  final String dialectCode;
  
  /// The difficulty level of this accent
  final AccentDifficulty difficulty;
  
  /// The list of accent characteristics
  final List<AccentCharacteristic> characteristics;
  
  /// Whether this accent is available offline
  final bool isOfflineAvailable;
  
  /// Whether this accent is currently downloaded
  final bool isDownloaded;
  
  /// The download size in MB
  final double downloadSizeMB;
  
  /// Creates a new accent model
  const AccentModel({
    required this.code,
    required this.name,
    required this.region,
    required this.dialectCode,
    this.difficulty = AccentDifficulty.moderate,
    this.characteristics = const [],
    this.isOfflineAvailable = false,
    this.isDownloaded = false,
    this.downloadSizeMB = 0.0,
  });
  
  /// Creates a copy with some fields replaced
  AccentModel copyWith({
    String? code,
    String? name,
    String? region,
    String? dialectCode,
    AccentDifficulty? difficulty,
    List<AccentCharacteristic>? characteristics,
    bool? isOfflineAvailable,
    bool? isDownloaded,
    double? downloadSizeMB,
  }) {
    return AccentModel(
      code: code ?? this.code,
      name: name ?? this.name,
      region: region ?? this.region,
      dialectCode: dialectCode ?? this.dialectCode,
      difficulty: difficulty ?? this.difficulty,
      characteristics: characteristics ?? this.characteristics,
      isOfflineAvailable: isOfflineAvailable ?? this.isOfflineAvailable,
      isDownloaded: isDownloaded ?? this.isDownloaded,
      downloadSizeMB: downloadSizeMB ?? this.downloadSizeMB,
    );
  }
  
  /// Converts to JSON
  Map<String, dynamic> toJson() {
    return {
      'code': code,
      'name': name,
      'region': region,
      'dialectCode': dialectCode,
      'difficulty': difficulty.index,
      'characteristics': characteristics.map((c) => c.toJson()).toList(),
      'isOfflineAvailable': isOfflineAvailable,
      'isDownloaded': isDownloaded,
      'downloadSizeMB': downloadSizeMB,
    };
  }
  
  /// Creates from JSON
  factory AccentModel.fromJson(Map<String, dynamic> json) {
    return AccentModel(
      code: json['code'] as String,
      name: json['name'] as String,
      region: json['region'] as String,
      dialectCode: json['dialectCode'] as String,
      difficulty: AccentDifficulty.values[json['difficulty'] as int],
      characteristics: json['characteristics'] != null
          ? List<AccentCharacteristic>.from(
              (json['characteristics'] as List).map(
                (x) => AccentCharacteristic.fromJson(x as Map<String, dynamic>),
              ),
            )
          : const [],
      isOfflineAvailable: json['isOfflineAvailable'] as bool? ?? false,
      isDownloaded: json['isDownloaded'] as bool? ?? false,
      downloadSizeMB: (json['downloadSizeMB'] as num?)?.toDouble() ?? 0.0,
    );
  }
  
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    
    return other is AccentModel &&
        other.code == code &&
        other.name == name &&
        other.region == region &&
        other.dialectCode == dialectCode &&
        other.difficulty == difficulty &&
        listEquals(other.characteristics, characteristics) &&
        other.isOfflineAvailable == isOfflineAvailable &&
        other.isDownloaded == isDownloaded &&
        other.downloadSizeMB == downloadSizeMB;
  }
  
  @override
  int get hashCode {
    return code.hashCode ^
        name.hashCode ^
        region.hashCode ^
        dialectCode.hashCode ^
        difficulty.hashCode ^
        characteristics.hashCode ^
        isOfflineAvailable.hashCode ^
        isDownloaded.hashCode ^
        downloadSizeMB.hashCode;
  }
}

/// A model representing a detection result for an accent
class AccentDetectionResult {
  /// The detected accent
  final AccentModel accent;
  
  /// The confidence score (0.0 to 1.0)
  final double confidence;
  
  /// The detected accent characteristics
  final List<AccentCharacteristic> detectedCharacteristics;
  
  /// Creates a new accent detection result
  const AccentDetectionResult({
    required this.accent,
    required this.confidence,
    this.detectedCharacteristics = const [],
  });
  
  /// Creates a copy with some fields replaced
  AccentDetectionResult copyWith({
    AccentModel? accent,
    double? confidence,
    List<AccentCharacteristic>? detectedCharacteristics,
  }) {
    return AccentDetectionResult(
      accent: accent ?? this.accent,
      confidence: confidence ?? this.confidence,
      detectedCharacteristics: detectedCharacteristics ?? this.detectedCharacteristics,
    );
  }
  
  /// Converts to JSON
  Map<String, dynamic> toJson() {
    return {
      'accent': accent.toJson(),
      'confidence': confidence,
      'detectedCharacteristics': detectedCharacteristics.map((c) => c.toJson()).toList(),
    };
  }
  
  /// Creates from JSON
  factory AccentDetectionResult.fromJson(Map<String, dynamic> json) {
    return AccentDetectionResult(
      accent: AccentModel.fromJson(json['accent'] as Map<String, dynamic>),
      confidence: (json['confidence'] as num).toDouble(),
      detectedCharacteristics: json['detectedCharacteristics'] != null
          ? List<AccentCharacteristic>.from(
              (json['detectedCharacteristics'] as List).map(
                (x) => AccentCharacteristic.fromJson(x as Map<String, dynamic>),
              ),
            )
          : const [],
    );
  }
}
