import 'package:flutter/material.dart';
import 'package:culture_connect/models/translation/dialect_model.dart';
import 'package:culture_connect/utils/rtl_utils.dart';

/// A model representing a language
class LanguageModel {
  /// The language code (e.g., 'en', 'fr')
  final String code;

  /// The display name of the language
  final String name;

  /// The flag emoji of the language
  final String flag;

  /// Whether the language is available offline
  final bool isOfflineAvailable;

  /// The download size in MB
  final double downloadSizeMB;

  /// Whether the language pack is currently downloaded
  final bool isDownloaded;

  /// The download progress (0.0 to 1.0)
  final double downloadProgress;

  /// Whether the language pack is currently downloading
  final bool isDownloading;

  /// The list of supported dialects for this language
  final List<String> supportedDialects;

  /// The list of detailed dialect models for this language
  final List<DialectModel> dialects;

  /// The default dialect code for this language
  final String? defaultDialectCode;

  /// Whether to automatically detect dialects for this language
  final bool autoDetectDialect;

  /// Whether this language is written right-to-left
  bool get isRTL => RTLUtils.isRTL(code);

  /// The text direction for this language
  TextDirection get textDirection =>
      isRTL ? TextDirection.rtl : TextDirection.ltr;

  /// The text alignment for this language
  TextAlign get textAlignment => isRTL ? TextAlign.right : TextAlign.left;

  /// Creates a new language model
  const LanguageModel({
    required this.code,
    required this.name,
    required this.flag,
    this.isOfflineAvailable = false,
    this.downloadSizeMB = 0.0,
    this.isDownloaded = false,
    this.downloadProgress = 0.0,
    this.isDownloading = false,
    this.supportedDialects = const [],
    this.dialects = const [],
    this.defaultDialectCode,
    this.autoDetectDialect = true,
  });

  /// Creates a copy with some fields replaced
  LanguageModel copyWith({
    String? code,
    String? name,
    String? flag,
    bool? isOfflineAvailable,
    double? downloadSizeMB,
    bool? isDownloaded,
    double? downloadProgress,
    bool? isDownloading,
    List<String>? supportedDialects,
    List<DialectModel>? dialects,
    String? defaultDialectCode,
    bool? autoDetectDialect,
  }) {
    return LanguageModel(
      code: code ?? this.code,
      name: name ?? this.name,
      flag: flag ?? this.flag,
      isOfflineAvailable: isOfflineAvailable ?? this.isOfflineAvailable,
      downloadSizeMB: downloadSizeMB ?? this.downloadSizeMB,
      isDownloaded: isDownloaded ?? this.isDownloaded,
      downloadProgress: downloadProgress ?? this.downloadProgress,
      isDownloading: isDownloading ?? this.isDownloading,
      supportedDialects: supportedDialects ?? this.supportedDialects,
      dialects: dialects ?? this.dialects,
      defaultDialectCode: defaultDialectCode ?? this.defaultDialectCode,
      autoDetectDialect: autoDetectDialect ?? this.autoDetectDialect,
    );
  }

  /// Converts to JSON
  Map<String, dynamic> toJson() {
    return {
      'code': code,
      'name': name,
      'flag': flag,
      'isOfflineAvailable': isOfflineAvailable,
      'downloadSizeMB': downloadSizeMB,
      'isDownloaded': isDownloaded,
      'downloadProgress': downloadProgress,
      'isDownloading': isDownloading,
      'supportedDialects': supportedDialects,
      'dialects': dialects.map((d) => d.toJson()).toList(),
      'defaultDialectCode': defaultDialectCode,
      'autoDetectDialect': autoDetectDialect,
    };
  }

  /// Creates from JSON
  factory LanguageModel.fromJson(Map<String, dynamic> json) {
    return LanguageModel(
      code: json['code'] as String,
      name: json['name'] as String,
      flag: json['flag'] as String,
      isOfflineAvailable: json['isOfflineAvailable'] as bool? ?? false,
      downloadSizeMB: (json['downloadSizeMB'] as num?)?.toDouble() ?? 0.0,
      isDownloaded: json['isDownloaded'] as bool? ?? false,
      downloadProgress: (json['downloadProgress'] as num?)?.toDouble() ?? 0.0,
      isDownloading: json['isDownloading'] as bool? ?? false,
      supportedDialects: json['supportedDialects'] != null
          ? List<String>.from(json['supportedDialects'] as List)
          : const [],
      dialects: json['dialects'] != null
          ? List<DialectModel>.from(
              (json['dialects'] as List).map(
                (x) => DialectModel.fromJson(x as Map<String, dynamic>),
              ),
            )
          : const [],
      defaultDialectCode: json['defaultDialectCode'] as String?,
      autoDetectDialect: json['autoDetectDialect'] as bool? ?? true,
    );
  }
}

/// List of supported languages
final List<LanguageModel> supportedLanguages = [
  const LanguageModel(
    code: 'en',
    name: 'English',
    flag: '🇺🇸',
    isOfflineAvailable: true,
    downloadSizeMB: 45.2,
    isDownloaded: true,
    supportedDialects: [
      'American',
      'British',
      'Australian',
      'Canadian',
      'Indian',
    ],
    defaultDialectCode: 'en-us',
    dialects: [
      DialectModel(
        code: 'en-us',
        name: 'American English',
        region: 'United States',
        flag: '🇺🇸',
        languageCode: 'en',
        isOfflineAvailable: true,
        isDownloaded: true,
        downloadSizeMB: 15.3,
        dialectSpecificTerms: [
          'elevator',
          'apartment',
          'vacation',
          'sidewalk',
          'truck',
          'garbage',
          'fall',
          'cookie',
          'soccer',
          'gasoline',
        ],
        accentVariants: [
          'en-us-general',
          'en-us-southern',
          'en-us-midwest',
          'en-us-newyork',
          'en-us-california',
        ],
      ),
      DialectModel(
        code: 'en-gb',
        name: 'British English',
        region: 'United Kingdom',
        flag: '🇬🇧',
        languageCode: 'en',
        isOfflineAvailable: true,
        downloadSizeMB: 14.8,
        dialectSpecificTerms: [
          'lift',
          'flat',
          'holiday',
          'pavement',
          'lorry',
          'rubbish',
          'autumn',
          'biscuit',
          'football',
          'petrol',
        ],
        accentVariants: [
          'en-gb-rp',
          'en-gb-cockney',
          'en-gb-scouse',
          'en-gb-scottish',
          'en-gb-welsh',
        ],
      ),
      DialectModel(
        code: 'en-au',
        name: 'Australian English',
        region: 'Australia',
        flag: '🇦🇺',
        languageCode: 'en',
        isOfflineAvailable: true,
        downloadSizeMB: 13.9,
        dialectSpecificTerms: [
          'lift',
          'flat',
          'holiday',
          'footpath',
          'ute',
          'rubbish',
          'autumn',
          'biscuit',
          'footy',
          'petrol',
          'arvo',
          'barbie',
          'brekkie',
          'servo',
          'thongs',
        ],
        accentVariants: [
          'en-au-general',
          'en-au-broad',
          'en-au-cultivated',
        ],
      ),
    ],
  ),
  const LanguageModel(
    code: 'fr',
    name: 'French',
    flag: '🇫🇷',
    isOfflineAvailable: true,
    downloadSizeMB: 52.8,
    supportedDialects: [
      'Parisian',
      'Canadian',
      'Belgian',
      'Swiss',
      'African',
    ],
    defaultDialectCode: 'fr-fr',
    dialects: [
      DialectModel(
        code: 'fr-fr',
        name: 'Parisian French',
        region: 'France',
        flag: '🇫🇷',
        languageCode: 'fr',
        isOfflineAvailable: true,
        downloadSizeMB: 16.2,
        dialectSpecificTerms: [
          'voiture',
          'faire des achats',
          'épicerie',
          'ami',
          'petite amie',
          'week-end',
          'boisson',
          'bientôt',
          'actuellement',
          'de rien',
        ],
        accentVariants: [
          'fr-fr-parisian',
          'fr-fr-southern',
          'fr-fr-northern',
        ],
      ),
      DialectModel(
        code: 'fr-ca',
        name: 'Canadian French',
        region: 'Canada',
        flag: '🇨🇦',
        languageCode: 'fr',
        isOfflineAvailable: true,
        downloadSizeMB: 15.7,
        dialectSpecificTerms: [
          'char',
          'magasiner',
          'dépanneur',
          'chum',
          'blonde',
          'fin de semaine',
          'breuvage',
          'tantôt',
          'présentement',
          'bienvenue',
        ],
        accentVariants: [
          'fr-ca-quebec',
          'fr-ca-montreal',
          'fr-ca-acadian',
        ],
      ),
    ],
  ),
  const LanguageModel(
    code: 'yo',
    name: 'Yoruba',
    flag: '🇳🇬',
    isOfflineAvailable: true,
    downloadSizeMB: 38.5,
    supportedDialects: [
      'Nigerian',
      'Beninese',
      'Togolese',
    ],
  ),
  const LanguageModel(
    code: 'ig',
    name: 'Igbo',
    flag: '🇳🇬',
    isOfflineAvailable: true,
    downloadSizeMB: 36.2,
    supportedDialects: [
      'Standard',
      'Owerri',
      'Onitsha',
      'Umuahia',
    ],
  ),
  const LanguageModel(
    code: 'ha',
    name: 'Hausa',
    flag: '🇳🇬',
    isOfflineAvailable: true,
    downloadSizeMB: 41.7,
    supportedDialects: [
      'Nigerian',
      'Niger',
      'Ghanaian',
    ],
  ),
  const LanguageModel(
    code: 'sw',
    name: 'Swahili',
    flag: '🇰🇪',
    isOfflineAvailable: true,
    downloadSizeMB: 43.9,
    supportedDialects: [
      'Kenyan',
      'Tanzanian',
      'Ugandan',
      'Congolese',
    ],
  ),
  const LanguageModel(
    code: 'zu',
    name: 'Zulu',
    flag: '🇿🇦',
    isOfflineAvailable: true,
    downloadSizeMB: 35.6,
    supportedDialects: [
      'Standard',
      'Northern',
      'Southern',
    ],
  ),
  const LanguageModel(
    code: 'xh',
    name: 'Xhosa',
    flag: '🇿🇦',
    isOfflineAvailable: true,
    downloadSizeMB: 34.8,
    supportedDialects: [
      'Standard',
      'Western Cape',
      'Eastern Cape',
    ],
  ),
  const LanguageModel(
    code: 'ar',
    name: 'Arabic',
    flag: '🇪🇬',
    isOfflineAvailable: true,
    downloadSizeMB: 58.3,
    supportedDialects: [
      'Modern Standard',
      'Egyptian',
      'Levantine',
      'Gulf',
      'Maghrebi',
    ],
    defaultDialectCode: 'ar-eg',
    dialects: [
      DialectModel(
        code: 'ar-eg',
        name: 'Egyptian Arabic',
        region: 'Egypt',
        flag: '🇪🇬',
        languageCode: 'ar',
        isOfflineAvailable: true,
        downloadSizeMB: 18.5,
        dialectSpecificTerms: [
          'إزيك',
          'عايز',
          'دلوقتي',
          'إيه',
          'فين',
          'ماشي',
          'خالص',
          'أهو',
          'بس',
          'يلا',
        ],
        accentVariants: [
          'ar-eg-cairo',
          'ar-eg-alexandria',
          'ar-eg-upper',
        ],
      ),
      DialectModel(
        code: 'ar-sa',
        name: 'Gulf Arabic',
        region: 'Saudi Arabia',
        flag: '🇸🇦',
        languageCode: 'ar',
        isOfflineAvailable: true,
        downloadSizeMB: 17.8,
        dialectSpecificTerms: [
          'وش',
          'أبغى',
          'الحين',
          'وشو',
          'وين',
          'زين',
          'مرة',
          'هني',
          'بس',
          'يلا',
        ],
        accentVariants: [
          'ar-sa-riyadh',
          'ar-sa-jeddah',
          'ar-sa-eastern',
        ],
      ),
    ],
  ),
  const LanguageModel(
    code: 'pt',
    name: 'Portuguese',
    flag: '🇵🇹',
    isOfflineAvailable: true,
    downloadSizeMB: 49.5,
    supportedDialects: [
      'European',
      'Brazilian',
      'African',
    ],
  ),
  const LanguageModel(
    code: 'es',
    name: 'Spanish',
    flag: '🇪🇸',
    isOfflineAvailable: true,
    downloadSizeMB: 51.2,
    supportedDialects: [
      'Castilian',
      'Latin American',
      'Mexican',
      'Argentinian',
      'Colombian',
    ],
    defaultDialectCode: 'es-es',
    dialects: [
      DialectModel(
        code: 'es-es',
        name: 'Castilian Spanish',
        region: 'Spain',
        flag: '🇪🇸',
        languageCode: 'es',
        isOfflineAvailable: true,
        downloadSizeMB: 15.9,
        dialectSpecificTerms: [
          'chaval',
          'guay',
          'molar',
          'vale',
          'tío',
          'curro',
          'currar',
          'diga',
          'ahora mismo',
          'verdad',
        ],
        accentVariants: [
          'es-es-castilian',
          'es-es-andalusian',
          'es-es-canarian',
        ],
      ),
      DialectModel(
        code: 'es-mx',
        name: 'Mexican Spanish',
        region: 'Mexico',
        flag: '🇲🇽',
        languageCode: 'es',
        isOfflineAvailable: true,
        downloadSizeMB: 15.5,
        dialectSpecificTerms: [
          'chavo',
          'chido',
          'padre',
          'órale',
          'güey',
          'chamba',
          'chambear',
          'mande',
          'ahorita',
          'neta',
        ],
        accentVariants: [
          'es-mx-central',
          'es-mx-northern',
          'es-mx-coastal',
        ],
      ),
    ],
  ),
  const LanguageModel(
    code: 'de',
    name: 'German',
    flag: '🇩🇪',
    isOfflineAvailable: true,
    downloadSizeMB: 53.7,
    supportedDialects: [
      'Standard',
      'Austrian',
      'Swiss',
      'Bavarian',
    ],
  ),
];

/// Extension on String to provide language-related functionality
extension LanguageCodeExtension on String {
  /// Get the language model from a language code
  LanguageModel? toLanguageModel() {
    try {
      return supportedLanguages.firstWhere((lang) => lang.code == this);
    } catch (e) {
      return null;
    }
  }

  /// Get the language name from a language code
  String toLanguageName() {
    return toLanguageModel()?.name ?? this;
  }

  /// Get the language flag from a language code
  String toLanguageFlag() {
    return toLanguageModel()?.flag ?? '';
  }

  /// Check if a language code is RTL
  bool isRTL() {
    return toLanguageModel()?.isRTL ?? false;
  }

  /// Get the text direction for a language code
  TextDirection toTextDirection() {
    return toLanguageModel()?.textDirection ?? TextDirection.ltr;
  }

  /// Get the text alignment for a language code
  TextAlign toTextAlignment() {
    return toLanguageModel()?.textAlignment ?? TextAlign.left;
  }
}
