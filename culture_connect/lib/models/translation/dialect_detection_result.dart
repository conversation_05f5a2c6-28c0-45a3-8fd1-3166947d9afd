import 'dart:convert';
import 'package:culture_connect/models/translation/dialect_model.dart';
import 'package:culture_connect/models/translation/accent_model.dart';

/// A class representing the result of dialect detection
class DialectDetectionResult {
  /// The detected dialect
  final DialectModel dialect;

  /// The confidence score (0.0 to 1.0)
  final double confidence;

  /// The detected dialect-specific terms
  final List<String> detectedTerms;

  /// Creates a new dialect detection result
  const DialectDetectionResult({
    required this.dialect,
    required this.confidence,
    this.detectedTerms = const [],
  });

  /// Creates a copy of this result with the given fields replaced
  DialectDetectionResult copyWith({
    DialectModel? dialect,
    double? confidence,
    List<String>? detectedTerms,
  }) {
    return DialectDetectionResult(
      dialect: dialect ?? this.dialect,
      confidence: confidence ?? this.confidence,
      detectedTerms: detectedTerms ?? this.detectedTerms,
    );
  }

  /// Converts this result to a JSON map
  Map<String, dynamic> toJson() {
    return {
      'dialect': dialect.toJson(),
      'confidence': confidence,
      'detectedTerms': detectedTerms,
    };
  }

  /// Creates a result from a JSON map
  factory DialectDetectionResult.fromJson(Map<String, dynamic> json) {
    return DialectDetectionResult(
      dialect: DialectModel.fromJson(json['dialect']),
      confidence: json['confidence'],
      detectedTerms: List<String>.from(json['detectedTerms']),
    );
  }

  @override
  String toString() {
    return 'DialectDetectionResult(dialect: ${dialect.name}, confidence: $confidence, detectedTerms: $detectedTerms)';
  }
}

/// A class representing the result of accent detection
class AccentDetectionResult {
  /// The detected accent
  final AccentModel accent;

  /// The confidence score (0.0 to 1.0)
  final double confidence;

  /// The detected accent characteristics
  final List<AccentCharacteristic> detectedCharacteristics;

  /// Creates a new accent detection result
  const AccentDetectionResult({
    required this.accent,
    required this.confidence,
    this.detectedCharacteristics = const [],
  });

  /// Creates a copy of this result with the given fields replaced
  AccentDetectionResult copyWith({
    AccentModel? accent,
    double? confidence,
    List<AccentCharacteristic>? detectedCharacteristics,
  }) {
    return AccentDetectionResult(
      accent: accent ?? this.accent,
      confidence: confidence ?? this.confidence,
      detectedCharacteristics:
          detectedCharacteristics ?? this.detectedCharacteristics,
    );
  }

  /// Converts this result to a JSON map
  Map<String, dynamic> toJson() {
    return {
      'accent': accent.toJson(),
      'confidence': confidence,
      'detectedCharacteristics':
          detectedCharacteristics.map((c) => c.toJson()).toList(),
    };
  }

  /// Creates a result from a JSON map
  factory AccentDetectionResult.fromJson(Map<String, dynamic> json) {
    return AccentDetectionResult(
      accent: AccentModel.fromJson(json['accent']),
      confidence: json['confidence'],
      detectedCharacteristics: (json['detectedCharacteristics'] as List)
          .map((c) => AccentCharacteristic.fromJson(c))
          .toList(),
    );
  }

  @override
  String toString() {
    return 'AccentDetectionResult(accent: ${accent.name}, confidence: $confidence, detectedCharacteristics: ${detectedCharacteristics.length})';
  }
}
