import 'package:flutter/material.dart';

/// A model representing a guide
class Guide {
  final String id;
  final String name;
  final String? bio;
  final String? profileImageUrl;
  final double? rating;
  final int? reviewCount;
  final List<String> languages;
  final String? location;
  final Map<String, dynamic>? coordinates;
  final bool isVerified;
  final List<String> specialties;
  final int yearsOfExperience;
  final String? email;
  final String? phone;
  
  const Guide({
    required this.id,
    required this.name,
    this.bio,
    this.profileImageUrl,
    this.rating,
    this.reviewCount,
    this.languages = const [],
    this.location,
    this.coordinates,
    this.isVerified = false,
    this.specialties = const [],
    this.yearsOfExperience = 0,
    this.email,
    this.phone,
  });
  
  /// Create a Guide instance from a JSON map
  factory Guide.fromJson(Map<String, dynamic> json) {
    return Guide(
      id: json['id'] as String,
      name: json['name'] as String,
      bio: json['bio'] as String?,
      profileImageUrl: json['profileImageUrl'] as String?,
      rating: json['rating'] != null ? (json['rating'] as num).toDouble() : null,
      reviewCount: json['reviewCount'] as int?,
      languages: (json['languages'] as List<dynamic>?)?.cast<String>() ?? [],
      location: json['location'] as String?,
      coordinates: json['coordinates'] as Map<String, dynamic>?,
      isVerified: json['isVerified'] as bool? ?? false,
      specialties: (json['specialties'] as List<dynamic>?)?.cast<String>() ?? [],
      yearsOfExperience: json['yearsOfExperience'] as int? ?? 0,
      email: json['email'] as String?,
      phone: json['phone'] as String?,
    );
  }
  
  /// Convert this Guide instance to a JSON map
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'bio': bio,
      'profileImageUrl': profileImageUrl,
      'rating': rating,
      'reviewCount': reviewCount,
      'languages': languages,
      'location': location,
      'coordinates': coordinates,
      'isVerified': isVerified,
      'specialties': specialties,
      'yearsOfExperience': yearsOfExperience,
      'email': email,
      'phone': phone,
    };
  }
  
  /// Create a copy of this Guide instance with the given fields replaced with the new values
  Guide copyWith({
    String? id,
    String? name,
    String? bio,
    String? profileImageUrl,
    double? rating,
    int? reviewCount,
    List<String>? languages,
    String? location,
    Map<String, dynamic>? coordinates,
    bool? isVerified,
    List<String>? specialties,
    int? yearsOfExperience,
    String? email,
    String? phone,
  }) {
    return Guide(
      id: id ?? this.id,
      name: name ?? this.name,
      bio: bio ?? this.bio,
      profileImageUrl: profileImageUrl ?? this.profileImageUrl,
      rating: rating ?? this.rating,
      reviewCount: reviewCount ?? this.reviewCount,
      languages: languages ?? this.languages,
      location: location ?? this.location,
      coordinates: coordinates ?? this.coordinates,
      isVerified: isVerified ?? this.isVerified,
      specialties: specialties ?? this.specialties,
      yearsOfExperience: yearsOfExperience ?? this.yearsOfExperience,
      email: email ?? this.email,
      phone: phone ?? this.phone,
    );
  }
  
  /// Get the guide's initials
  String get initials {
    if (name.isEmpty) return '';
    
    final nameParts = name.split(' ');
    if (nameParts.length == 1) {
      return nameParts[0][0].toUpperCase();
    } else {
      return '${nameParts[0][0]}${nameParts[1][0]}'.toUpperCase();
    }
  }
  
  /// Get the guide's avatar widget
  Widget getAvatar({double radius = 20}) {
    return CircleAvatar(
      radius: radius,
      backgroundImage: profileImageUrl != null ? NetworkImage(profileImageUrl!) : null,
      child: profileImageUrl == null
          ? Text(
              initials,
              style: TextStyle(
                fontSize: radius * 0.8,
                fontWeight: FontWeight.bold,
              ),
            )
          : null,
    );
  }
  
  /// Get the guide's formatted rating
  String get formattedRating {
    if (rating == null) return 'N/A';
    return rating!.toStringAsFixed(1);
  }
  
  /// Get the guide's formatted review count
  String get formattedReviewCount {
    if (reviewCount == null) return '0 reviews';
    return '$reviewCount ${reviewCount == 1 ? 'review' : 'reviews'}';
  }
  
  /// Get the guide's formatted experience
  String get formattedExperience {
    return '$yearsOfExperience ${yearsOfExperience == 1 ? 'year' : 'years'} of experience';
  }
}
