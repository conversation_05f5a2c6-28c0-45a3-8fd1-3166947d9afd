import 'package:cloud_firestore/cloud_firestore.dart';

/// Enum representing the different types of trust factors
enum TrustFactorType {
  /// Identity verification
  identityVerification,
  
  /// Professional certification
  professionalCertification,
  
  /// Background check
  backgroundCheck,
  
  /// Positive reviews
  positiveReviews,
  
  /// Completed experiences
  completedExperiences,
  
  /// Response rate
  responseRate,
  
  /// Account age
  accountAge,
  
  /// Social media verification
  socialMediaVerification,
  
  /// Government license
  governmentLicense,
  
  /// Community standing
  communityStanding,
}

/// Model representing a trust factor
class TrustFactor {
  /// Unique identifier for the trust factor
  final String id;
  
  /// Type of trust factor
  final TrustFactorType type;
  
  /// Name of the trust factor
  final String name;
  
  /// Description of the trust factor
  final String description;
  
  /// Weight of the trust factor (0-100)
  final int weight;
  
  /// Icon URL for the trust factor
  final String? iconUrl;
  
  /// Whether this trust factor is active
  final bool isActive;

  /// Creates a new trust factor
  const TrustFactor({
    required this.id,
    required this.type,
    required this.name,
    required this.description,
    required this.weight,
    this.iconUrl,
    required this.isActive,
  });

  /// Creates a trust factor from a JSON map
  factory TrustFactor.fromJson(Map<String, dynamic> json) {
    return TrustFactor(
      id: json['id'] as String,
      type: TrustFactorType.values.firstWhere(
        (e) => e.toString().split('.').last == json['type'],
        orElse: () => TrustFactorType.identityVerification,
      ),
      name: json['name'] as String,
      description: json['description'] as String,
      weight: json['weight'] as int,
      iconUrl: json['iconUrl'] as String?,
      isActive: json['isActive'] as bool,
    );
  }

  /// Converts this trust factor to a JSON map
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type.toString().split('.').last,
      'name': name,
      'description': description,
      'weight': weight,
      'iconUrl': iconUrl,
      'isActive': isActive,
    };
  }
}

/// Model representing a user's trust score
class TrustScore {
  /// Unique identifier for the trust score
  final String id;
  
  /// ID of the user this trust score belongs to
  final String userId;
  
  /// Overall trust score (0-100)
  final int score;
  
  /// Individual factor scores
  final Map<String, int> factorScores;
  
  /// Date when the trust score was last calculated
  final DateTime calculatedAt;
  
  /// Additional metadata for the trust score
  final Map<String, dynamic>? metadata;

  /// Creates a new trust score
  const TrustScore({
    required this.id,
    required this.userId,
    required this.score,
    required this.factorScores,
    required this.calculatedAt,
    this.metadata,
  });

  /// Creates a trust score from a JSON map
  factory TrustScore.fromJson(Map<String, dynamic> json) {
    return TrustScore(
      id: json['id'] as String,
      userId: json['userId'] as String,
      score: json['score'] as int,
      factorScores: Map<String, int>.from(json['factorScores'] as Map),
      calculatedAt: (json['calculatedAt'] as Timestamp).toDate(),
      metadata: json['metadata'] as Map<String, dynamic>?,
    );
  }

  /// Converts this trust score to a JSON map
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userId': userId,
      'score': score,
      'factorScores': factorScores,
      'calculatedAt': Timestamp.fromDate(calculatedAt),
      'metadata': metadata,
    };
  }

  /// Creates a copy of this trust score with the given fields replaced with the new values
  TrustScore copyWith({
    String? id,
    String? userId,
    int? score,
    Map<String, int>? factorScores,
    DateTime? calculatedAt,
    Map<String, dynamic>? metadata,
  }) {
    return TrustScore(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      score: score ?? this.score,
      factorScores: factorScores ?? this.factorScores,
      calculatedAt: calculatedAt ?? this.calculatedAt,
      metadata: metadata ?? this.metadata,
    );
  }

  /// Returns the trust level based on the score
  TrustLevel get trustLevel {
    if (score >= 90) return TrustLevel.exceptional;
    if (score >= 80) return TrustLevel.excellent;
    if (score >= 70) return TrustLevel.great;
    if (score >= 60) return TrustLevel.good;
    if (score >= 50) return TrustLevel.average;
    if (score >= 40) return TrustLevel.fair;
    if (score >= 30) return TrustLevel.poor;
    return TrustLevel.untrusted;
  }

  /// Returns the factor score for the given factor type
  int getFactorScore(TrustFactorType factorType) {
    final key = factorType.toString().split('.').last;
    return factorScores[key] ?? 0;
  }
}

/// Enum representing the different trust levels
enum TrustLevel {
  /// Exceptional trust level (90-100)
  exceptional,
  
  /// Excellent trust level (80-89)
  excellent,
  
  /// Great trust level (70-79)
  great,
  
  /// Good trust level (60-69)
  good,
  
  /// Average trust level (50-59)
  average,
  
  /// Fair trust level (40-49)
  fair,
  
  /// Poor trust level (30-39)
  poor,
  
  /// Untrusted level (0-29)
  untrusted,
}

/// Extension on TrustLevel to provide helper methods
extension TrustLevelExtension on TrustLevel {
  /// Returns the display name for this trust level
  String get displayName {
    switch (this) {
      case TrustLevel.exceptional:
        return 'Exceptional';
      case TrustLevel.excellent:
        return 'Excellent';
      case TrustLevel.great:
        return 'Great';
      case TrustLevel.good:
        return 'Good';
      case TrustLevel.average:
        return 'Average';
      case TrustLevel.fair:
        return 'Fair';
      case TrustLevel.poor:
        return 'Poor';
      case TrustLevel.untrusted:
        return 'Untrusted';
    }
  }

  /// Returns the color for this trust level
  int get color {
    switch (this) {
      case TrustLevel.exceptional:
        return 0xFF4CAF50; // Green
      case TrustLevel.excellent:
        return 0xFF8BC34A; // Light Green
      case TrustLevel.great:
        return 0xFFCDDC39; // Lime
      case TrustLevel.good:
        return 0xFFFFEB3B; // Yellow
      case TrustLevel.average:
        return 0xFFFFC107; // Amber
      case TrustLevel.fair:
        return 0xFFFF9800; // Orange
      case TrustLevel.poor:
        return 0xFFFF5722; // Deep Orange
      case TrustLevel.untrusted:
        return 0xFFF44336; // Red
    }
  }

  /// Returns the minimum score for this trust level
  int get minScore {
    switch (this) {
      case TrustLevel.exceptional:
        return 90;
      case TrustLevel.excellent:
        return 80;
      case TrustLevel.great:
        return 70;
      case TrustLevel.good:
        return 60;
      case TrustLevel.average:
        return 50;
      case TrustLevel.fair:
        return 40;
      case TrustLevel.poor:
        return 30;
      case TrustLevel.untrusted:
        return 0;
    }
  }

  /// Returns the maximum score for this trust level
  int get maxScore {
    switch (this) {
      case TrustLevel.exceptional:
        return 100;
      case TrustLevel.excellent:
        return 89;
      case TrustLevel.great:
        return 79;
      case TrustLevel.good:
        return 69;
      case TrustLevel.average:
        return 59;
      case TrustLevel.fair:
        return 49;
      case TrustLevel.poor:
        return 39;
      case TrustLevel.untrusted:
        return 29;
    }
  }
}
