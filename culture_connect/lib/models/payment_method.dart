import 'package:flutter/material.dart';

/// Represents a payment method that can be used for transactions
class PaymentMethod {
  /// Unique identifier for the payment method
  final String id;
  
  /// Type of payment method (e.g., credit card, bank account)
  final PaymentMethodType type;
  
  /// Name of the payment method (e.g., "Visa ending in 4242")
  final String name;
  
  /// Last 4 digits of the card or account number
  final String? last4;
  
  /// Brand of the card (e.g., Visa, Mastercard)
  final String? brand;
  
  /// Expiry month of the card
  final int? expiryMonth;
  
  /// Expiry year of the card
  final int? expiryYear;
  
  /// Country of the payment method
  final String? country;
  
  /// Whether this is the default payment method
  final bool isDefault;
  
  /// When the payment method was created
  final DateTime createdAt;

  /// Creates a new payment method
  const PaymentMethod({
    required this.id,
    required this.type,
    required this.name,
    this.last4,
    this.brand,
    this.expiryMonth,
    this.expiryYear,
    this.country,
    this.isDefault = false,
    required this.createdAt,
  });

  /// Creates a payment method from a JSON map
  factory PaymentMethod.fromJson(Map<String, dynamic> json) {
    return PaymentMethod(
      id: json['id'] as String,
      type: PaymentMethodTypeExtension.fromString(json['type'] as String),
      name: json['name'] as String,
      last4: json['last4'] as String?,
      brand: json['brand'] as String?,
      expiryMonth: json['expiryMonth'] as int?,
      expiryYear: json['expiryYear'] as int?,
      country: json['country'] as String?,
      isDefault: json['isDefault'] as bool? ?? false,
      createdAt: DateTime.parse(json['createdAt'] as String),
    );
  }

  /// Converts this payment method to a JSON map
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type.toString().split('.').last,
      'name': name,
      if (last4 != null) 'last4': last4,
      if (brand != null) 'brand': brand,
      if (expiryMonth != null) 'expiryMonth': expiryMonth,
      if (expiryYear != null) 'expiryYear': expiryYear,
      if (country != null) 'country': country,
      'isDefault': isDefault,
      'createdAt': createdAt.toIso8601String(),
    };
  }

  /// Creates a copy of this payment method with the given fields replaced with the new values
  PaymentMethod copyWith({
    String? id,
    PaymentMethodType? type,
    String? name,
    String? last4,
    String? brand,
    int? expiryMonth,
    int? expiryYear,
    String? country,
    bool? isDefault,
    DateTime? createdAt,
  }) {
    return PaymentMethod(
      id: id ?? this.id,
      type: type ?? this.type,
      name: name ?? this.name,
      last4: last4 ?? this.last4,
      brand: brand ?? this.brand,
      expiryMonth: expiryMonth ?? this.expiryMonth,
      expiryYear: expiryYear ?? this.expiryYear,
      country: country ?? this.country,
      isDefault: isDefault ?? this.isDefault,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  /// Returns an icon for this payment method type
  IconData get icon {
    switch (type) {
      case PaymentMethodType.creditCard:
        if (brand?.toLowerCase() == 'visa') return Icons.credit_card;
        if (brand?.toLowerCase() == 'mastercard') return Icons.credit_card;
        if (brand?.toLowerCase() == 'amex') return Icons.credit_card;
        return Icons.credit_card;
      case PaymentMethodType.bankAccount:
        return Icons.account_balance;
      case PaymentMethodType.mobileMoney:
        return Icons.phone_android;
      case PaymentMethodType.paypal:
        return Icons.payment;
      case PaymentMethodType.applePay:
        return Icons.apple;
      case PaymentMethodType.googlePay:
        return Icons.g_mobiledata;
      case PaymentMethodType.other:
        return Icons.payment;
    }
  }

  /// Returns a color for this payment method type
  Color get color {
    switch (type) {
      case PaymentMethodType.creditCard:
        if (brand?.toLowerCase() == 'visa') return Colors.blue;
        if (brand?.toLowerCase() == 'mastercard') return Colors.deepOrange;
        if (brand?.toLowerCase() == 'amex') return Colors.indigo;
        return Colors.blueGrey;
      case PaymentMethodType.bankAccount:
        return Colors.green;
      case PaymentMethodType.mobileMoney:
        return Colors.orange;
      case PaymentMethodType.paypal:
        return Colors.blue;
      case PaymentMethodType.applePay:
        return Colors.black;
      case PaymentMethodType.googlePay:
        return Colors.blue;
      case PaymentMethodType.other:
        return Colors.grey;
    }
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is PaymentMethod && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

/// Types of payment methods
enum PaymentMethodType {
  creditCard,
  bankAccount,
  mobileMoney,
  paypal,
  applePay,
  googlePay,
  other,
}

/// Extension on PaymentMethodType to provide helper methods
extension PaymentMethodTypeExtension on PaymentMethodType {
  /// Returns a display name for this payment method type
  String get displayName {
    switch (this) {
      case PaymentMethodType.creditCard:
        return 'Credit Card';
      case PaymentMethodType.bankAccount:
        return 'Bank Account';
      case PaymentMethodType.mobileMoney:
        return 'Mobile Money';
      case PaymentMethodType.paypal:
        return 'PayPal';
      case PaymentMethodType.applePay:
        return 'Apple Pay';
      case PaymentMethodType.googlePay:
        return 'Google Pay';
      case PaymentMethodType.other:
        return 'Other';
    }
  }

  /// Creates a PaymentMethodType from a string
  static PaymentMethodType fromString(String type) {
    return PaymentMethodType.values.firstWhere(
      (e) => e.toString().split('.').last == type,
      orElse: () => PaymentMethodType.other,
    );
  }
}
