import 'package:flutter/foundation.dart';

/// Enum representing days of the week.
enum DayOfWeek {
  monday,
  tuesday,
  wednesday,
  thursday,
  friday,
  saturday,
  sunday
}

/// Extension on DayOfWeek to provide helper methods.
extension DayOfWeekExtension on DayOfWeek {
  String get displayName {
    switch (this) {
      case DayOfWeek.monday:
        return 'Monday';
      case DayOfWeek.tuesday:
        return 'Tuesday';
      case DayOfWeek.wednesday:
        return 'Wednesday';
      case DayOfWeek.thursday:
        return 'Thursday';
      case DayOfWeek.friday:
        return 'Friday';
      case DayOfWeek.saturday:
        return 'Saturday';
      case DayOfWeek.sunday:
        return 'Sunday';
    }
  }

  String get shortName {
    switch (this) {
      case DayOfWeek.monday:
        return 'Mon';
      case DayOfWeek.tuesday:
        return 'Tue';
      case DayOfWeek.wednesday:
        return 'Wed';
      case DayOfWeek.thursday:
        return 'Thu';
      case DayOfWeek.friday:
        return 'Fri';
      case DayOfWeek.saturday:
        return 'Sat';
      case DayOfWeek.sunday:
        return 'Sun';
    }
  }

  int get index {
    switch (this) {
      case DayOfWeek.monday:
        return 0;
      case DayOfWeek.tuesday:
        return 1;
      case DayOfWeek.wednesday:
        return 2;
      case DayOfWeek.thursday:
        return 3;
      case DayOfWeek.friday:
        return 4;
      case DayOfWeek.saturday:
        return 5;
      case DayOfWeek.sunday:
        return 6;
    }
  }

  static DayOfWeek fromIndex(int index) {
    switch (index) {
      case 0:
        return DayOfWeek.monday;
      case 1:
        return DayOfWeek.tuesday;
      case 2:
        return DayOfWeek.wednesday;
      case 3:
        return DayOfWeek.thursday;
      case 4:
        return DayOfWeek.friday;
      case 5:
        return DayOfWeek.saturday;
      case 6:
        return DayOfWeek.sunday;
      default:
        throw ArgumentError('Invalid day index: $index');
    }
  }

  static DayOfWeek fromDateTime(DateTime dateTime) {
    // DateTime weekday: 1 = Monday, 7 = Sunday
    return fromIndex(dateTime.weekday - 1);
  }
}

/// A model representing a time slot.
class TimeSlot {
  final String id;
  final String startTime; // Format: HH:MM (24-hour)
  final String endTime; // Format: HH:MM (24-hour)

  /// Creates a new TimeSlot instance.
  TimeSlot({
    required this.id,
    required this.startTime,
    required this.endTime,
  });

  /// Creates a TimeSlot from a JSON map.
  factory TimeSlot.fromJson(Map<String, dynamic> json) {
    return TimeSlot(
      id: json['id'] as String,
      startTime: json['startTime'] as String,
      endTime: json['endTime'] as String,
    );
  }

  /// Converts this TimeSlot to a JSON map.
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'startTime': startTime,
      'endTime': endTime,
    };
  }

  /// Creates a copy of this TimeSlot with the given fields replaced with the new values.
  TimeSlot copyWith({
    String? id,
    String? startTime,
    String? endTime,
  }) {
    return TimeSlot(
      id: id ?? this.id,
      startTime: startTime ?? this.startTime,
      endTime: endTime ?? this.endTime,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is TimeSlot &&
        other.id == id &&
        other.startTime == startTime &&
        other.endTime == endTime;
  }

  @override
  int get hashCode => id.hashCode ^ startTime.hashCode ^ endTime.hashCode;
}

/// A model representing a weekly schedule.
class WeeklySchedule {
  final Map<DayOfWeek, List<TimeSlot>> schedule;

  /// Creates a new WeeklySchedule instance.
  WeeklySchedule({
    required this.schedule,
  });

  /// Creates a WeeklySchedule from a JSON map.
  factory WeeklySchedule.fromJson(Map<String, dynamic> json) {
    final Map<DayOfWeek, List<TimeSlot>> schedule = {};

    json.forEach((key, value) {
      final day = DayOfWeek.values.firstWhere(
        (d) => d.toString().split('.').last == key,
        orElse: () => DayOfWeek.monday,
      );

      final slots = (value as List)
          .map((slot) => TimeSlot.fromJson(slot as Map<String, dynamic>))
          .toList();

      schedule[day] = slots;
    });

    return WeeklySchedule(schedule: schedule);
  }

  /// Converts this WeeklySchedule to a JSON map.
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> result = {};

    schedule.forEach((day, slots) {
      final dayString = day.toString().split('.').last;
      result[dayString] = slots.map((slot) => slot.toJson()).toList();
    });

    return result;
  }

  /// Creates a copy of this WeeklySchedule with the given fields replaced with the new values.
  WeeklySchedule copyWith({
    Map<DayOfWeek, List<TimeSlot>>? schedule,
  }) {
    return WeeklySchedule(
      schedule: schedule ?? Map.from(this.schedule),
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is WeeklySchedule && mapEquals(other.schedule, schedule);
  }

  @override
  int get hashCode => schedule.hashCode;
}

/// A model representing a date exception (e.g., holiday, time off).
class DateException {
  final String id;
  final DateTime date;
  final bool isAvailable;
  final List<TimeSlot>? timeSlots; // Only used if isAvailable is true

  /// Creates a new DateException instance.
  DateException({
    required this.id,
    required this.date,
    required this.isAvailable,
    this.timeSlots,
  });

  /// Creates a DateException from a JSON map.
  factory DateException.fromJson(Map<String, dynamic> json) {
    return DateException(
      id: json['id'] as String,
      date: DateTime.parse(json['date'] as String),
      isAvailable: json['isAvailable'] as bool,
      timeSlots: json['timeSlots'] != null
          ? (json['timeSlots'] as List)
              .map((slot) => TimeSlot.fromJson(slot as Map<String, dynamic>))
              .toList()
          : null,
    );
  }

  /// Converts this DateException to a JSON map.
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'date': date.toIso8601String().split('T').first, // YYYY-MM-DD
      'isAvailable': isAvailable,
      if (timeSlots != null)
        'timeSlots': timeSlots!.map((slot) => slot.toJson()).toList(),
    };
  }

  /// Creates a copy of this DateException with the given fields replaced with the new values.
  DateException copyWith({
    String? id,
    DateTime? date,
    bool? isAvailable,
    List<TimeSlot>? timeSlots,
  }) {
    return DateException(
      id: id ?? this.id,
      date: date ?? this.date,
      isAvailable: isAvailable ?? this.isAvailable,
      timeSlots: timeSlots ?? this.timeSlots,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is DateException &&
        other.id == id &&
        other.date == date &&
        other.isAvailable == isAvailable &&
        listEquals(other.timeSlots, timeSlots);
  }

  @override
  int get hashCode =>
      id.hashCode ^ date.hashCode ^ isAvailable.hashCode ^ timeSlots.hashCode;
}

/// A model representing a guide's availability.
class AvailabilityModel {
  final String id;
  final String guideId;
  final WeeklySchedule weeklySchedule;
  final List<DateException> dateExceptions;
  final int advanceBookingDays;
  final int maxBookingDays;
  final DateTime? lastUpdated;

  /// Creates a new AvailabilityModel instance.
  AvailabilityModel({
    required this.id,
    required this.guideId,
    required this.weeklySchedule,
    required this.dateExceptions,
    required this.advanceBookingDays,
    required this.maxBookingDays,
    this.lastUpdated,
  });

  /// Creates an AvailabilityModel from a JSON map.
  factory AvailabilityModel.fromJson(Map<String, dynamic> json) {
    return AvailabilityModel(
      id: json['id'] as String,
      guideId: json['guideId'] as String,
      weeklySchedule: WeeklySchedule.fromJson(
          json['weeklySchedule'] as Map<String, dynamic>),
      dateExceptions: (json['dateExceptions'] as List)
          .map((e) => DateException.fromJson(e as Map<String, dynamic>))
          .toList(),
      advanceBookingDays: json['advanceBookingDays'] as int,
      maxBookingDays: json['maxBookingDays'] as int,
      lastUpdated: json['lastUpdated'] != null
          ? DateTime.parse(json['lastUpdated'] as String)
          : null,
    );
  }

  /// Converts this AvailabilityModel to a JSON map.
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'guideId': guideId,
      'weeklySchedule': weeklySchedule.toJson(),
      'dateExceptions': dateExceptions.map((e) => e.toJson()).toList(),
      'advanceBookingDays': advanceBookingDays,
      'maxBookingDays': maxBookingDays,
      if (lastUpdated != null) 'lastUpdated': lastUpdated!.toIso8601String(),
    };
  }

  /// Creates a copy of this AvailabilityModel with the given fields replaced with the new values.
  AvailabilityModel copyWith({
    String? id,
    String? guideId,
    WeeklySchedule? weeklySchedule,
    List<DateException>? dateExceptions,
    int? advanceBookingDays,
    int? maxBookingDays,
    DateTime? lastUpdated,
  }) {
    return AvailabilityModel(
      id: id ?? this.id,
      guideId: guideId ?? this.guideId,
      weeklySchedule: weeklySchedule ?? this.weeklySchedule,
      dateExceptions: dateExceptions ?? this.dateExceptions,
      advanceBookingDays: advanceBookingDays ?? this.advanceBookingDays,
      maxBookingDays: maxBookingDays ?? this.maxBookingDays,
      lastUpdated: lastUpdated ?? this.lastUpdated,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is AvailabilityModel &&
        other.id == id &&
        other.guideId == guideId &&
        other.weeklySchedule == weeklySchedule &&
        listEquals(other.dateExceptions, dateExceptions) &&
        other.advanceBookingDays == advanceBookingDays &&
        other.maxBookingDays == maxBookingDays;
  }

  @override
  int get hashCode =>
      id.hashCode ^
      guideId.hashCode ^
      weeklySchedule.hashCode ^
      dateExceptions.hashCode ^
      advanceBookingDays.hashCode ^
      maxBookingDays.hashCode;
}
