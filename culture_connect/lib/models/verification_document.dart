import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:culture_connect/models/verification_model.dart';

/// Enum representing the different types of verification documents
enum DocumentType {
  /// Passport
  passport,
  
  /// Driver's license
  driversLicense,
  
  /// National ID card
  nationalId,
  
  /// Visa
  visa,
  
  /// Professional certification
  certification,
  
  /// Utility bill (for address verification)
  utilityBill,
  
  /// Bank statement
  bankStatement,
  
  /// Business license
  businessLicense,
  
  /// Insurance document
  insurance,
  
  /// Other document type
  other,
}

/// Extension to provide display names for document types
extension DocumentTypeExtension on DocumentType {
  /// Get the display name for this document type
  String get displayName {
    switch (this) {
      case DocumentType.passport:
        return 'Passport';
      case DocumentType.driversLicense:
        return 'Driver\'s License';
      case DocumentType.nationalId:
        return 'National ID Card';
      case DocumentType.visa:
        return 'Visa';
      case DocumentType.certification:
        return 'Professional Certification';
      case DocumentType.utilityBill:
        return 'Utility Bill';
      case DocumentType.bankStatement:
        return 'Bank Statement';
      case DocumentType.businessLicense:
        return 'Business License';
      case DocumentType.insurance:
        return 'Insurance Document';
      case DocumentType.other:
        return 'Other Document';
    }
  }
}

/// Model representing a verification document
class VerificationDocument {
  /// Unique identifier for the document
  final String id;
  
  /// Type of document
  final DocumentType type;
  
  /// File name of the document
  final String fileName;
  
  /// Date when the document was uploaded
  final DateTime uploadDate;
  
  /// Status of the document verification
  final VerificationStatus status;
  
  /// URL of the document file
  final String? fileUrl;
  
  /// Rejection reason (if applicable)
  final String? rejectionReason;
  
  /// Additional metadata for the document
  final Map<String, dynamic>? metadata;

  /// Creates a new verification document
  const VerificationDocument({
    required this.id,
    required this.type,
    required this.fileName,
    required this.uploadDate,
    required this.status,
    this.fileUrl,
    this.rejectionReason,
    this.metadata,
  });

  /// Creates a verification document from a JSON map
  factory VerificationDocument.fromJson(Map<String, dynamic> json) {
    return VerificationDocument(
      id: json['id'] as String,
      type: DocumentType.values.firstWhere(
        (e) => e.toString().split('.').last == json['type'],
        orElse: () => DocumentType.other,
      ),
      fileName: json['fileName'] as String,
      uploadDate: json['uploadDate'] is Timestamp
          ? (json['uploadDate'] as Timestamp).toDate()
          : DateTime.parse(json['uploadDate'] as String),
      status: VerificationStatus.values.firstWhere(
        (e) => e.toString().split('.').last == json['status'],
        orElse: () => VerificationStatus.pending,
      ),
      fileUrl: json['fileUrl'] as String?,
      rejectionReason: json['rejectionReason'] as String?,
      metadata: json['metadata'] as Map<String, dynamic>?,
    );
  }

  /// Converts this verification document to a JSON map
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type.toString().split('.').last,
      'fileName': fileName,
      'uploadDate': uploadDate.toIso8601String(),
      'status': status.toString().split('.').last,
      'fileUrl': fileUrl,
      'rejectionReason': rejectionReason,
      'metadata': metadata,
    };
  }

  /// Creates a copy of this verification document with the given fields replaced with the new values
  VerificationDocument copyWith({
    String? id,
    DocumentType? type,
    String? fileName,
    DateTime? uploadDate,
    VerificationStatus? status,
    String? fileUrl,
    String? rejectionReason,
    Map<String, dynamic>? metadata,
  }) {
    return VerificationDocument(
      id: id ?? this.id,
      type: type ?? this.type,
      fileName: fileName ?? this.fileName,
      uploadDate: uploadDate ?? this.uploadDate,
      status: status ?? this.status,
      fileUrl: fileUrl ?? this.fileUrl,
      rejectionReason: rejectionReason ?? this.rejectionReason,
      metadata: metadata ?? this.metadata,
    );
  }
}
