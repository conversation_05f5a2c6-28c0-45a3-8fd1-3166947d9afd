import 'package:flutter/material.dart';
import 'package:culture_connect/models/loyalty/loyalty_program_model.dart';

/// Enum representing different loyalty reward types
enum LoyaltyRewardType {
  /// Discount reward
  discount,
  
  /// Free night reward
  freeNight,
  
  /// Room upgrade reward
  roomUpgrade,
  
  /// Experience reward
  experience,
  
  /// Airport transfer reward
  airportTransfer,
  
  /// Spa treatment reward
  spaTreatment,
  
  /// Restaurant voucher reward
  restaurantVoucher,
  
  /// Gift card reward
  giftCard,
  
  /// Merchandise reward
  merchandise,
}

/// Extension on LoyaltyRewardType to provide additional functionality
extension LoyaltyRewardTypeExtension on LoyaltyRewardType {
  /// Get the display name of the loyalty reward type
  String get displayName {
    switch (this) {
      case LoyaltyRewardType.discount:
        return 'Discount';
      case LoyaltyRewardType.freeNight:
        return 'Free Night';
      case LoyaltyRewardType.roomUpgrade:
        return 'Room Upgrade';
      case LoyaltyRewardType.experience:
        return 'Experience';
      case LoyaltyRewardType.airportTransfer:
        return 'Airport Transfer';
      case LoyaltyRewardType.spaTreatment:
        return 'Spa Treatment';
      case LoyaltyRewardType.restaurantVoucher:
        return 'Restaurant Voucher';
      case LoyaltyRewardType.giftCard:
        return 'Gift Card';
      case LoyaltyRewardType.merchandise:
        return 'Merchandise';
    }
  }
  
  /// Get the icon of the loyalty reward type
  IconData get icon {
    switch (this) {
      case LoyaltyRewardType.discount:
        return Icons.percent;
      case LoyaltyRewardType.freeNight:
        return Icons.hotel;
      case LoyaltyRewardType.roomUpgrade:
        return Icons.upgrade;
      case LoyaltyRewardType.experience:
        return Icons.explore;
      case LoyaltyRewardType.airportTransfer:
        return Icons.airport_shuttle;
      case LoyaltyRewardType.spaTreatment:
        return Icons.spa;
      case LoyaltyRewardType.restaurantVoucher:
        return Icons.restaurant;
      case LoyaltyRewardType.giftCard:
        return Icons.card_giftcard;
      case LoyaltyRewardType.merchandise:
        return Icons.shopping_bag;
    }
  }
}

/// Enum representing different loyalty reward statuses
enum LoyaltyRewardStatus {
  /// Reward is available
  available,
  
  /// Reward is redeemed
  redeemed,
  
  /// Reward is expired
  expired,
  
  /// Reward is pending
  pending,
  
  /// Reward is cancelled
  cancelled,
}

/// Extension on LoyaltyRewardStatus to provide additional functionality
extension LoyaltyRewardStatusExtension on LoyaltyRewardStatus {
  /// Get the display name of the loyalty reward status
  String get displayName {
    switch (this) {
      case LoyaltyRewardStatus.available:
        return 'Available';
      case LoyaltyRewardStatus.redeemed:
        return 'Redeemed';
      case LoyaltyRewardStatus.expired:
        return 'Expired';
      case LoyaltyRewardStatus.pending:
        return 'Pending';
      case LoyaltyRewardStatus.cancelled:
        return 'Cancelled';
    }
  }
  
  /// Get the color of the loyalty reward status
  Color get color {
    switch (this) {
      case LoyaltyRewardStatus.available:
        return Colors.green;
      case LoyaltyRewardStatus.redeemed:
        return Colors.blue;
      case LoyaltyRewardStatus.expired:
        return Colors.red;
      case LoyaltyRewardStatus.pending:
        return Colors.orange;
      case LoyaltyRewardStatus.cancelled:
        return Colors.grey;
    }
  }
}

/// A model representing a loyalty reward
class LoyaltyReward {
  /// Unique identifier for the reward
  final String id;
  
  /// Name of the reward
  final String name;
  
  /// Description of the reward
  final String description;
  
  /// Type of reward
  final LoyaltyRewardType type;
  
  /// Status of the reward
  final LoyaltyRewardStatus status;
  
  /// Points required to redeem the reward
  final int pointsRequired;
  
  /// Minimum loyalty tier required to redeem the reward
  final LoyaltyTier? minimumTier;
  
  /// Value of the reward (e.g., discount percentage, voucher amount)
  final double? value;
  
  /// Currency of the reward value
  final String? currency;
  
  /// Image URL of the reward
  final String? imageUrl;
  
  /// Expiration date of the reward
  final DateTime? expirationDate;
  
  /// Redemption date of the reward
  final DateTime? redemptionDate;
  
  /// Redemption code of the reward
  final String? redemptionCode;
  
  /// Terms and conditions of the reward
  final String? termsAndConditions;
  
  /// Creates a new loyalty reward
  const LoyaltyReward({
    required this.id,
    required this.name,
    required this.description,
    required this.type,
    required this.status,
    required this.pointsRequired,
    this.minimumTier,
    this.value,
    this.currency,
    this.imageUrl,
    this.expirationDate,
    this.redemptionDate,
    this.redemptionCode,
    this.termsAndConditions,
  });
  
  /// Creates a copy with some fields replaced
  LoyaltyReward copyWith({
    String? id,
    String? name,
    String? description,
    LoyaltyRewardType? type,
    LoyaltyRewardStatus? status,
    int? pointsRequired,
    LoyaltyTier? minimumTier,
    double? value,
    String? currency,
    String? imageUrl,
    DateTime? expirationDate,
    DateTime? redemptionDate,
    String? redemptionCode,
    String? termsAndConditions,
  }) {
    return LoyaltyReward(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      type: type ?? this.type,
      status: status ?? this.status,
      pointsRequired: pointsRequired ?? this.pointsRequired,
      minimumTier: minimumTier ?? this.minimumTier,
      value: value ?? this.value,
      currency: currency ?? this.currency,
      imageUrl: imageUrl ?? this.imageUrl,
      expirationDate: expirationDate ?? this.expirationDate,
      redemptionDate: redemptionDate ?? this.redemptionDate,
      redemptionCode: redemptionCode ?? this.redemptionCode,
      termsAndConditions: termsAndConditions ?? this.termsAndConditions,
    );
  }
  
  /// Check if the reward is available
  bool get isAvailable => status == LoyaltyRewardStatus.available;
  
  /// Check if the reward is redeemed
  bool get isRedeemed => status == LoyaltyRewardStatus.redeemed;
  
  /// Check if the reward is expired
  bool get isExpired => status == LoyaltyRewardStatus.expired;
  
  /// Get the formatted value
  String? get formattedValue {
    if (value == null) return null;
    
    switch (type) {
      case LoyaltyRewardType.discount:
        return '${value!.toStringAsFixed(0)}%';
      case LoyaltyRewardType.giftCard:
      case LoyaltyRewardType.restaurantVoucher:
        return currency != null ? '$currency${value!.toStringAsFixed(2)}' : value!.toStringAsFixed(2);
      default:
        return value!.toStringAsFixed(2);
    }
  }
  
  /// Get the formatted expiration date
  String? get formattedExpirationDate {
    if (expirationDate == null) return null;
    return '${expirationDate!.year}-${expirationDate!.month.toString().padLeft(2, '0')}-${expirationDate!.day.toString().padLeft(2, '0')}';
  }
  
  /// Get the formatted redemption date
  String? get formattedRedemptionDate {
    if (redemptionDate == null) return null;
    return '${redemptionDate!.year}-${redemptionDate!.month.toString().padLeft(2, '0')}-${redemptionDate!.day.toString().padLeft(2, '0')}';
  }
  
  /// Converts to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'type': type.index,
      'status': status.index,
      'pointsRequired': pointsRequired,
      'minimumTier': minimumTier?.index,
      'value': value,
      'currency': currency,
      'imageUrl': imageUrl,
      'expirationDate': expirationDate?.toIso8601String(),
      'redemptionDate': redemptionDate?.toIso8601String(),
      'redemptionCode': redemptionCode,
      'termsAndConditions': termsAndConditions,
    };
  }
  
  /// Creates from JSON
  factory LoyaltyReward.fromJson(Map<String, dynamic> json) {
    return LoyaltyReward(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String,
      type: LoyaltyRewardType.values[json['type'] as int],
      status: LoyaltyRewardStatus.values[json['status'] as int],
      pointsRequired: json['pointsRequired'] as int,
      minimumTier: json['minimumTier'] != null
          ? LoyaltyTier.values[json['minimumTier'] as int]
          : null,
      value: json['value'] as double?,
      currency: json['currency'] as String?,
      imageUrl: json['imageUrl'] as String?,
      expirationDate: json['expirationDate'] != null
          ? DateTime.parse(json['expirationDate'] as String)
          : null,
      redemptionDate: json['redemptionDate'] != null
          ? DateTime.parse(json['redemptionDate'] as String)
          : null,
      redemptionCode: json['redemptionCode'] as String?,
      termsAndConditions: json['termsAndConditions'] as String?,
    );
  }
}
