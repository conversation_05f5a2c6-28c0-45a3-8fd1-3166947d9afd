import 'package:flutter/material.dart';
import 'package:culture_connect/models/loyalty/loyalty_points_transaction.dart';
import 'package:culture_connect/models/loyalty/loyalty_reward.dart';

/// Enum representing different loyalty tiers
enum LoyaltyTier {
  /// Bronze tier
  bronze,

  /// Silver tier
  silver,

  /// Gold tier
  gold,

  /// Platinum tier
  platinum,

  /// Diamond tier
  diamond,
}

/// Extension on LoyaltyTier to provide additional functionality
extension LoyaltyTierExtension on LoyaltyTier {
  /// Get the display name of the loyalty tier
  String get displayName {
    switch (this) {
      case LoyaltyTier.bronze:
        return 'Bronze';
      case LoyaltyTier.silver:
        return 'Silver';
      case LoyaltyTier.gold:
        return 'Gold';
      case LoyaltyTier.platinum:
        return 'Platinum';
      case LoyaltyTier.diamond:
        return 'Diamond';
    }
  }

  /// Get the color of the loyalty tier
  Color get color {
    switch (this) {
      case LoyaltyTier.bronze:
        return const Color(0xFFCD7F32);
      case LoyaltyTier.silver:
        return const Color(0xFFC0C0C0);
      case LoyaltyTier.gold:
        return const Color(0xFFFFD700);
      case LoyaltyTier.platinum:
        return const Color(0xFFE5E4E2);
      case LoyaltyTier.diamond:
        return const Color(0xFFB9F2FF);
    }
  }

  /// Get the icon of the loyalty tier
  IconData get icon {
    switch (this) {
      case LoyaltyTier.bronze:
        return Icons.workspace_premium;
      case LoyaltyTier.silver:
        return Icons.workspace_premium;
      case LoyaltyTier.gold:
        return Icons.workspace_premium;
      case LoyaltyTier.platinum:
        return Icons.workspace_premium;
      case LoyaltyTier.diamond:
        return Icons.diamond;
    }
  }

  /// Get the points required to reach this tier
  int get pointsRequired {
    switch (this) {
      case LoyaltyTier.bronze:
        return 0;
      case LoyaltyTier.silver:
        return 1000;
      case LoyaltyTier.gold:
        return 5000;
      case LoyaltyTier.platinum:
        return 10000;
      case LoyaltyTier.diamond:
        return 25000;
    }
  }

  /// Get the discount percentage for this tier
  double get discountPercentage {
    switch (this) {
      case LoyaltyTier.bronze:
        return 0.0;
      case LoyaltyTier.silver:
        return 5.0;
      case LoyaltyTier.gold:
        return 10.0;
      case LoyaltyTier.platinum:
        return 15.0;
      case LoyaltyTier.diamond:
        return 20.0;
    }
  }

  /// Get the next tier
  LoyaltyTier? get nextTier {
    switch (this) {
      case LoyaltyTier.bronze:
        return LoyaltyTier.silver;
      case LoyaltyTier.silver:
        return LoyaltyTier.gold;
      case LoyaltyTier.gold:
        return LoyaltyTier.platinum;
      case LoyaltyTier.platinum:
        return LoyaltyTier.diamond;
      case LoyaltyTier.diamond:
        return null;
    }
  }

  /// Get the points required to reach the next tier
  int? get pointsToNextTier {
    final next = nextTier;
    if (next == null) return null;
    return next.pointsRequired - pointsRequired;
  }

  /// Get the benefits of this tier
  List<String> get benefits {
    final List<String> benefits = [];

    // Add tier-specific benefits
    switch (this) {
      case LoyaltyTier.bronze:
        benefits.addAll([
          'Earn 1 point per \$1 spent',
          'Access to member-only deals',
          'Birthday reward',
        ]);
        break;
      case LoyaltyTier.silver:
        benefits.addAll([
          'Earn 1.25 points per \$1 spent',
          '5% discount on all bookings',
          'Priority customer service',
          'Early access to new experiences',
        ]);
        break;
      case LoyaltyTier.gold:
        benefits.addAll([
          'Earn 1.5 points per \$1 spent',
          '10% discount on all bookings',
          'Free room upgrades (subject to availability)',
          'Late checkout',
          'Welcome gift on arrival',
        ]);
        break;
      case LoyaltyTier.platinum:
        benefits.addAll([
          'Earn 2 points per \$1 spent',
          '15% discount on all bookings',
          'Dedicated concierge service',
          'Guaranteed room availability',
          'Complimentary breakfast',
          'Airport transfers',
        ]);
        break;
      case LoyaltyTier.diamond:
        benefits.addAll([
          'Earn 3 points per \$1 spent',
          '20% discount on all bookings',
          'Personal travel assistant',
          'Suite upgrades',
          'Exclusive access to VIP events',
          'Complimentary spa treatments',
          'Flexible cancellation policy',
        ]);
        break;
    }

    return benefits;
  }

  /// Get the points multiplier for this tier
  double get pointsMultiplier {
    switch (this) {
      case LoyaltyTier.bronze:
        return 1.0;
      case LoyaltyTier.silver:
        return 1.25;
      case LoyaltyTier.gold:
        return 1.5;
      case LoyaltyTier.platinum:
        return 2.0;
      case LoyaltyTier.diamond:
        return 3.0;
    }
  }
}

/// A model representing a loyalty program
class LoyaltyProgramModel {
  /// Unique identifier for the loyalty program
  final String id;

  /// Name of the loyalty program
  final String name;

  /// Description of the loyalty program
  final String description;

  /// Current points balance
  final int pointsBalance;

  /// Lifetime points earned
  final int lifetimePoints;

  /// Current loyalty tier
  final LoyaltyTier tier;

  /// Enrollment date
  final DateTime enrollmentDate;

  /// Last activity date
  final DateTime lastActivityDate;

  /// Points expiration date
  final DateTime? pointsExpirationDate;

  /// Points history
  final List<LoyaltyPointsTransaction> pointsHistory;

  /// Available rewards
  final List<LoyaltyReward> availableRewards;

  /// Redeemed rewards
  final List<LoyaltyReward> redeemedRewards;

  /// Creates a new loyalty program model
  const LoyaltyProgramModel({
    required this.id,
    required this.name,
    required this.description,
    required this.pointsBalance,
    required this.lifetimePoints,
    required this.tier,
    required this.enrollmentDate,
    required this.lastActivityDate,
    this.pointsExpirationDate,
    required this.pointsHistory,
    required this.availableRewards,
    required this.redeemedRewards,
  });

  /// Creates a copy with some fields replaced
  LoyaltyProgramModel copyWith({
    String? id,
    String? name,
    String? description,
    int? pointsBalance,
    int? lifetimePoints,
    LoyaltyTier? tier,
    DateTime? enrollmentDate,
    DateTime? lastActivityDate,
    DateTime? pointsExpirationDate,
    List<LoyaltyPointsTransaction>? pointsHistory,
    List<LoyaltyReward>? availableRewards,
    List<LoyaltyReward>? redeemedRewards,
  }) {
    return LoyaltyProgramModel(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      pointsBalance: pointsBalance ?? this.pointsBalance,
      lifetimePoints: lifetimePoints ?? this.lifetimePoints,
      tier: tier ?? this.tier,
      enrollmentDate: enrollmentDate ?? this.enrollmentDate,
      lastActivityDate: lastActivityDate ?? this.lastActivityDate,
      pointsExpirationDate: pointsExpirationDate ?? this.pointsExpirationDate,
      pointsHistory: pointsHistory ?? this.pointsHistory,
      availableRewards: availableRewards ?? this.availableRewards,
      redeemedRewards: redeemedRewards ?? this.redeemedRewards,
    );
  }

  /// Get the next tier
  LoyaltyTier? get nextTier => tier.nextTier;

  /// Get the points required to reach the next tier
  int? get pointsToNextTier {
    final next = nextTier;
    if (next == null) return null;
    return next.pointsRequired - lifetimePoints;
  }

  /// Get the progress to the next tier (0.0 to 1.0)
  double? get progressToNextTier {
    final next = nextTier;
    if (next == null) return null;

    final currentTierPoints = tier.pointsRequired;
    final nextTierPoints = next.pointsRequired;
    final pointsRange = nextTierPoints - currentTierPoints;
    final pointsEarned = lifetimePoints - currentTierPoints;

    return pointsEarned / pointsRange;
  }

  /// Converts to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'pointsBalance': pointsBalance,
      'lifetimePoints': lifetimePoints,
      'tier': tier.index,
      'enrollmentDate': enrollmentDate.toIso8601String(),
      'lastActivityDate': lastActivityDate.toIso8601String(),
      'pointsExpirationDate': pointsExpirationDate?.toIso8601String(),
      'pointsHistory':
          pointsHistory.map((transaction) => transaction.toJson()).toList(),
      'availableRewards':
          availableRewards.map((reward) => reward.toJson()).toList(),
      'redeemedRewards':
          redeemedRewards.map((reward) => reward.toJson()).toList(),
    };
  }

  /// Creates from JSON
  factory LoyaltyProgramModel.fromJson(Map<String, dynamic> json) {
    return LoyaltyProgramModel(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String,
      pointsBalance: json['pointsBalance'] as int,
      lifetimePoints: json['lifetimePoints'] as int,
      tier: LoyaltyTier.values[json['tier'] as int],
      enrollmentDate: DateTime.parse(json['enrollmentDate'] as String),
      lastActivityDate: DateTime.parse(json['lastActivityDate'] as String),
      pointsExpirationDate: json['pointsExpirationDate'] != null
          ? DateTime.parse(json['pointsExpirationDate'] as String)
          : null,
      pointsHistory: (json['pointsHistory'] as List)
          .map((e) =>
              LoyaltyPointsTransaction.fromJson(e as Map<String, dynamic>))
          .toList(),
      availableRewards: (json['availableRewards'] as List)
          .map((e) => LoyaltyReward.fromJson(e as Map<String, dynamic>))
          .toList(),
      redeemedRewards: (json['redeemedRewards'] as List)
          .map((e) => LoyaltyReward.fromJson(e as Map<String, dynamic>))
          .toList(),
    );
  }
}
