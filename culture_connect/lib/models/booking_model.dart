/// Enum representing the status of a booking.
enum BookingStatus { pending, approved, rejected, cancelled, completed, noShow }

/// Extension on BookingStatus to provide helper methods.
extension BookingStatusExtension on BookingStatus {
  String get displayName {
    switch (this) {
      case BookingStatus.pending:
        return 'Pending';
      case BookingStatus.approved:
        return 'Approved';
      case BookingStatus.rejected:
        return 'Rejected';
      case BookingStatus.cancelled:
        return 'Cancelled';
      case BookingStatus.completed:
        return 'Completed';
      case BookingStatus.noShow:
        return 'No Show';
    }
  }

  String get description {
    switch (this) {
      case BookingStatus.pending:
        return 'Waiting for guide approval';
      case BookingStatus.approved:
        return 'Booking confirmed by guide';
      case BookingStatus.rejected:
        return 'Booking rejected by guide';
      case BookingStatus.cancelled:
        return 'Booking cancelled by customer';
      case BookingStatus.completed:
        return 'Experience completed successfully';
      case BookingStatus.noShow:
        return 'Customer did not show up';
    }
  }

  bool get isActive {
    return this == BookingStatus.pending || this == BookingStatus.approved;
  }

  bool get isFinalized {
    return this == BookingStatus.completed ||
        this == BookingStatus.cancelled ||
        this == BookingStatus.rejected ||
        this == BookingStatus.noShow;
  }

  static BookingStatus fromString(String status) {
    return BookingStatus.values.firstWhere(
      (e) => e.toString().split('.').last == status,
      orElse: () => BookingStatus.pending,
    );
  }
}

/// Enum representing the payment status of a booking.
enum PaymentStatus {
  pending,
  paid,
  partiallyPaid,
  refunded,
  partiallyRefunded,
  failed
}

/// Extension on PaymentStatus to provide helper methods.
extension PaymentStatusExtension on PaymentStatus {
  String get displayName {
    switch (this) {
      case PaymentStatus.pending:
        return 'Pending';
      case PaymentStatus.paid:
        return 'Paid';
      case PaymentStatus.partiallyPaid:
        return 'Partially Paid';
      case PaymentStatus.refunded:
        return 'Refunded';
      case PaymentStatus.partiallyRefunded:
        return 'Partially Refunded';
      case PaymentStatus.failed:
        return 'Failed';
    }
  }

  static PaymentStatus fromString(String status) {
    return PaymentStatus.values.firstWhere(
      (e) => e.toString().split('.').last == status,
      orElse: () => PaymentStatus.pending,
    );
  }
}

/// A model representing a customer for a booking.
class BookingCustomer {
  final String id;
  final String name;
  final String email;
  final String? phone;
  final String? profilePicture;
  final double? rating;

  /// Creates a new BookingCustomer instance.
  BookingCustomer({
    required this.id,
    required this.name,
    required this.email,
    this.phone,
    this.profilePicture,
    this.rating,
  });

  /// Creates a BookingCustomer from a JSON map.
  factory BookingCustomer.fromJson(Map<String, dynamic> json) {
    return BookingCustomer(
      id: json['id'] as String,
      name: json['name'] as String,
      email: json['email'] as String,
      phone: json['phone'] as String?,
      profilePicture: json['profilePicture'] as String?,
      rating:
          json['rating'] != null ? (json['rating'] as num).toDouble() : null,
    );
  }

  /// Converts this BookingCustomer to a JSON map.
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'email': email,
      if (phone != null) 'phone': phone,
      if (profilePicture != null) 'profilePicture': profilePicture,
      if (rating != null) 'rating': rating,
    };
  }

  /// Creates a copy of this BookingCustomer with the given fields replaced with the new values.
  BookingCustomer copyWith({
    String? id,
    String? name,
    String? email,
    String? phone,
    String? profilePicture,
    double? rating,
  }) {
    return BookingCustomer(
      id: id ?? this.id,
      name: name ?? this.name,
      email: email ?? this.email,
      phone: phone ?? this.phone,
      profilePicture: profilePicture ?? this.profilePicture,
      rating: rating ?? this.rating,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is BookingCustomer && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

/// A model representing an experience for a booking.
class BookingExperience {
  final String id;
  final String title;
  final String description;
  final String category;
  final double price;
  final String currency;
  final int durationMinutes;
  final String? imageUrl;

  /// Creates a new BookingExperience instance.
  BookingExperience({
    required this.id,
    required this.title,
    required this.description,
    required this.category,
    required this.price,
    required this.currency,
    required this.durationMinutes,
    this.imageUrl,
  });

  /// Creates a BookingExperience from a JSON map.
  factory BookingExperience.fromJson(Map<String, dynamic> json) {
    return BookingExperience(
      id: json['id'] as String,
      title: json['title'] as String,
      description: json['description'] as String,
      category: json['category'] as String,
      price: (json['price'] as num).toDouble(),
      currency: json['currency'] as String,
      durationMinutes: json['durationMinutes'] as int,
      imageUrl: json['imageUrl'] as String?,
    );
  }

  /// Converts this BookingExperience to a JSON map.
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'category': category,
      'price': price,
      'currency': currency,
      'durationMinutes': durationMinutes,
      if (imageUrl != null) 'imageUrl': imageUrl,
    };
  }

  /// Creates a copy of this BookingExperience with the given fields replaced with the new values.
  BookingExperience copyWith({
    String? id,
    String? title,
    String? description,
    String? category,
    double? price,
    String? currency,
    int? durationMinutes,
    String? imageUrl,
  }) {
    return BookingExperience(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      category: category ?? this.category,
      price: price ?? this.price,
      currency: currency ?? this.currency,
      durationMinutes: durationMinutes ?? this.durationMinutes,
      imageUrl: imageUrl ?? this.imageUrl,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is BookingExperience && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

/// A model representing payment information for a booking.
class PaymentInfo {
  final String id;
  final PaymentStatus status;
  final double amount;
  final String currency;
  final String? transactionId;
  final String? paymentMethod;
  final DateTime? paymentDate;
  final double? refundAmount;
  final DateTime? refundDate;
  final String? refundReason;

  /// Creates a new PaymentInfo instance.
  PaymentInfo({
    required this.id,
    required this.status,
    required this.amount,
    required this.currency,
    this.transactionId,
    this.paymentMethod,
    this.paymentDate,
    this.refundAmount,
    this.refundDate,
    this.refundReason,
  });

  /// Creates a PaymentInfo from a JSON map.
  factory PaymentInfo.fromJson(Map<String, dynamic> json) {
    return PaymentInfo(
      id: json['id'] as String,
      status: PaymentStatusExtension.fromString(json['status'] as String),
      amount: (json['amount'] as num).toDouble(),
      currency: json['currency'] as String,
      transactionId: json['transactionId'] as String?,
      paymentMethod: json['paymentMethod'] as String?,
      paymentDate: json['paymentDate'] != null
          ? DateTime.parse(json['paymentDate'] as String)
          : null,
      refundAmount: json['refundAmount'] != null
          ? (json['refundAmount'] as num).toDouble()
          : null,
      refundDate: json['refundDate'] != null
          ? DateTime.parse(json['refundDate'] as String)
          : null,
      refundReason: json['refundReason'] as String?,
    );
  }

  /// Converts this PaymentInfo to a JSON map.
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'status': status.toString().split('.').last,
      'amount': amount,
      'currency': currency,
      if (transactionId != null) 'transactionId': transactionId,
      if (paymentMethod != null) 'paymentMethod': paymentMethod,
      if (paymentDate != null) 'paymentDate': paymentDate!.toIso8601String(),
      if (refundAmount != null) 'refundAmount': refundAmount,
      if (refundDate != null) 'refundDate': refundDate!.toIso8601String(),
      if (refundReason != null) 'refundReason': refundReason,
    };
  }

  /// Creates a copy of this PaymentInfo with the given fields replaced with the new values.
  PaymentInfo copyWith({
    String? id,
    PaymentStatus? status,
    double? amount,
    String? currency,
    String? transactionId,
    String? paymentMethod,
    DateTime? paymentDate,
    double? refundAmount,
    DateTime? refundDate,
    String? refundReason,
  }) {
    return PaymentInfo(
      id: id ?? this.id,
      status: status ?? this.status,
      amount: amount ?? this.amount,
      currency: currency ?? this.currency,
      transactionId: transactionId ?? this.transactionId,
      paymentMethod: paymentMethod ?? this.paymentMethod,
      paymentDate: paymentDate ?? this.paymentDate,
      refundAmount: refundAmount ?? this.refundAmount,
      refundDate: refundDate ?? this.refundDate,
      refundReason: refundReason ?? this.refundReason,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is PaymentInfo && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

/// A model representing a booking.
class BookingModel {
  final String id;
  final String guideId;
  final BookingCustomer customer;
  final BookingExperience experience;
  final DateTime bookingDate;
  final DateTime experienceDate;
  final String startTime;
  final int participantCount;
  final String? specialRequirements;
  final BookingStatus status;
  final PaymentInfo payment;
  final String? rejectionReason;
  final String? cancellationReason;
  final bool isReviewed;
  final DateTime createdAt;
  final DateTime updatedAt;

  /// Creates a new BookingModel instance.
  BookingModel({
    required this.id,
    required this.guideId,
    required this.customer,
    required this.experience,
    required this.bookingDate,
    required this.experienceDate,
    required this.startTime,
    required this.participantCount,
    this.specialRequirements,
    required this.status,
    required this.payment,
    this.rejectionReason,
    this.cancellationReason,
    required this.isReviewed,
    required this.createdAt,
    required this.updatedAt,
  });

  /// Creates a BookingModel from a JSON map.
  factory BookingModel.fromJson(Map<String, dynamic> json) {
    return BookingModel(
      id: json['id'] as String,
      guideId: json['guideId'] as String,
      customer:
          BookingCustomer.fromJson(json['customer'] as Map<String, dynamic>),
      experience: BookingExperience.fromJson(
          json['experience'] as Map<String, dynamic>),
      bookingDate: DateTime.parse(json['bookingDate'] as String),
      experienceDate: DateTime.parse(json['experienceDate'] as String),
      startTime: json['startTime'] as String,
      participantCount: json['participantCount'] as int,
      specialRequirements: json['specialRequirements'] as String?,
      status: BookingStatusExtension.fromString(json['status'] as String),
      payment: PaymentInfo.fromJson(json['payment'] as Map<String, dynamic>),
      rejectionReason: json['rejectionReason'] as String?,
      cancellationReason: json['cancellationReason'] as String?,
      isReviewed: json['isReviewed'] as bool,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
    );
  }

  /// Converts this BookingModel to a JSON map.
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'guideId': guideId,
      'customer': customer.toJson(),
      'experience': experience.toJson(),
      'bookingDate': bookingDate.toIso8601String(),
      'experienceDate': experienceDate.toIso8601String(),
      'startTime': startTime,
      'participantCount': participantCount,
      if (specialRequirements != null)
        'specialRequirements': specialRequirements,
      'status': status.toString().split('.').last,
      'payment': payment.toJson(),
      if (rejectionReason != null) 'rejectionReason': rejectionReason,
      if (cancellationReason != null) 'cancellationReason': cancellationReason,
      'isReviewed': isReviewed,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  /// Creates a copy of this BookingModel with the given fields replaced with the new values.
  BookingModel copyWith({
    String? id,
    String? guideId,
    BookingCustomer? customer,
    BookingExperience? experience,
    DateTime? bookingDate,
    DateTime? experienceDate,
    String? startTime,
    int? participantCount,
    String? specialRequirements,
    BookingStatus? status,
    PaymentInfo? payment,
    String? rejectionReason,
    String? cancellationReason,
    bool? isReviewed,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return BookingModel(
      id: id ?? this.id,
      guideId: guideId ?? this.guideId,
      customer: customer ?? this.customer,
      experience: experience ?? this.experience,
      bookingDate: bookingDate ?? this.bookingDate,
      experienceDate: experienceDate ?? this.experienceDate,
      startTime: startTime ?? this.startTime,
      participantCount: participantCount ?? this.participantCount,
      specialRequirements: specialRequirements ?? this.specialRequirements,
      status: status ?? this.status,
      payment: payment ?? this.payment,
      rejectionReason: rejectionReason ?? this.rejectionReason,
      cancellationReason: cancellationReason ?? this.cancellationReason,
      isReviewed: isReviewed ?? this.isReviewed,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is BookingModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
