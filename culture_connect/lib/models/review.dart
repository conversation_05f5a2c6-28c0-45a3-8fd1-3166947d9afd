import 'package:cloud_firestore/cloud_firestore.dart';

/// Represents a review for an experience
class Review {
  /// Unique identifier for the review
  final String id;

  /// ID of the experience being reviewed
  final String experienceId;

  /// ID of the booking associated with this review
  final String bookingId;

  /// ID of the user who wrote the review
  final String userId;

  /// Rating (1-5)
  final double rating;

  /// Review text
  final String comment;

  /// Photos attached to the review
  final List<String> photoUrls;

  /// When the review was created
  final DateTime createdAt;

  /// When the review was last updated
  final DateTime updatedAt;

  /// Whether the review is published
  final bool isPublished;

  /// Creates a new review
  const Review({
    required this.id,
    required this.experienceId,
    required this.bookingId,
    required this.userId,
    required this.rating,
    required this.comment,
    this.photoUrls = const [],
    required this.createdAt,
    required this.updatedAt,
    this.isPublished = true,
  });

  /// Creates a copy of this review with the given fields replaced with the new values
  Review copyWith({
    String? id,
    String? experienceId,
    String? bookingId,
    String? userId,
    double? rating,
    String? comment,
    List<String>? photoUrls,
    DateTime? createdAt,
    DateTime? updatedAt,
    bool? isPublished,
  }) {
    return Review(
      id: id ?? this.id,
      experienceId: experienceId ?? this.experienceId,
      bookingId: bookingId ?? this.bookingId,
      userId: userId ?? this.userId,
      rating: rating ?? this.rating,
      comment: comment ?? this.comment,
      photoUrls: photoUrls ?? this.photoUrls,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      isPublished: isPublished ?? this.isPublished,
    );
  }
}

/// Enhanced model representing a user review for an experience
class ReviewModel {
  /// Unique identifier for the review
  final String id;

  /// ID of the experience being reviewed
  final String experienceId;

  /// ID of the booking associated with this review (optional)
  final String? bookingId;

  /// ID of the user who wrote the review
  final String userId;

  /// User's name who wrote the review
  final String userName;

  /// User's profile image URL
  final String? userProfileImageUrl;

  /// Rating given by the user (1-5 stars)
  final double rating;

  /// Text content of the review
  final String content;

  /// Date when the review was posted
  final DateTime datePosted;

  /// Date when the review was last updated
  final DateTime? dateUpdated;

  /// Number of users who found this review helpful
  final int helpfulCount;

  /// IDs of users who marked this review as helpful
  final List<String> helpfulUserIds;

  /// URLs of photos attached to the review
  final List<String> photoUrls;

  /// Response from the guide/host to this review
  final GuideResponse? guideResponse;

  /// Whether the review is verified (user actually booked the experience)
  final bool isVerified;

  /// Whether the review is published and visible to others
  final bool isPublished;

  /// Tags associated with the review (e.g., "Informative", "Accurate", "Fun")
  final List<String> tags;

  /// Creates a new review model
  const ReviewModel({
    required this.id,
    required this.experienceId,
    this.bookingId,
    required this.userId,
    required this.userName,
    this.userProfileImageUrl,
    required this.rating,
    required this.content,
    required this.datePosted,
    this.dateUpdated,
    this.helpfulCount = 0,
    this.helpfulUserIds = const [],
    this.photoUrls = const [],
    this.guideResponse,
    this.isVerified = false,
    this.isPublished = true,
    this.tags = const [],
  });

  /// Creates a review model from a JSON map
  factory ReviewModel.fromJson(Map<String, dynamic> json) {
    return ReviewModel(
      id: json['id'] as String,
      experienceId: json['experienceId'] as String,
      bookingId: json['bookingId'] as String?,
      userId: json['userId'] as String,
      userName: json['userName'] as String,
      userProfileImageUrl: json['userProfileImageUrl'] as String?,
      rating: (json['rating'] as num).toDouble(),
      content: json['content'] as String,
      datePosted: (json['datePosted'] is Timestamp)
          ? (json['datePosted'] as Timestamp).toDate()
          : DateTime.parse(json['datePosted'] as String),
      dateUpdated: json['dateUpdated'] != null
          ? (json['dateUpdated'] is Timestamp)
              ? (json['dateUpdated'] as Timestamp).toDate()
              : DateTime.parse(json['dateUpdated'] as String)
          : null,
      helpfulCount: json['helpfulCount'] as int? ?? 0,
      helpfulUserIds: (json['helpfulUserIds'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          [],
      photoUrls: (json['photoUrls'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          [],
      guideResponse: json['guideResponse'] != null
          ? GuideResponse.fromJson(
              json['guideResponse'] as Map<String, dynamic>)
          : null,
      isVerified: json['isVerified'] as bool? ?? false,
      isPublished: json['isPublished'] as bool? ?? true,
      tags:
          (json['tags'] as List<dynamic>?)?.map((e) => e as String).toList() ??
              [],
    );
  }

  /// Converts this review model to a JSON map
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'experienceId': experienceId,
      'bookingId': bookingId,
      'userId': userId,
      'userName': userName,
      'userProfileImageUrl': userProfileImageUrl,
      'rating': rating,
      'content': content,
      'datePosted': datePosted.toIso8601String(),
      'dateUpdated': dateUpdated?.toIso8601String(),
      'helpfulCount': helpfulCount,
      'helpfulUserIds': helpfulUserIds,
      'photoUrls': photoUrls,
      'guideResponse': guideResponse?.toJson(),
      'isVerified': isVerified,
      'isPublished': isPublished,
      'tags': tags,
    };
  }

  /// Creates a copy of this review model with the given fields replaced with new values
  ReviewModel copyWith({
    String? id,
    String? experienceId,
    String? bookingId,
    String? userId,
    String? userName,
    String? userProfileImageUrl,
    double? rating,
    String? content,
    DateTime? datePosted,
    DateTime? dateUpdated,
    int? helpfulCount,
    List<String>? helpfulUserIds,
    List<String>? photoUrls,
    GuideResponse? guideResponse,
    bool? isVerified,
    bool? isPublished,
    List<String>? tags,
  }) {
    return ReviewModel(
      id: id ?? this.id,
      experienceId: experienceId ?? this.experienceId,
      bookingId: bookingId ?? this.bookingId,
      userId: userId ?? this.userId,
      userName: userName ?? this.userName,
      userProfileImageUrl: userProfileImageUrl ?? this.userProfileImageUrl,
      rating: rating ?? this.rating,
      content: content ?? this.content,
      datePosted: datePosted ?? this.datePosted,
      dateUpdated: dateUpdated ?? this.dateUpdated,
      helpfulCount: helpfulCount ?? this.helpfulCount,
      helpfulUserIds: helpfulUserIds ?? this.helpfulUserIds,
      photoUrls: photoUrls ?? this.photoUrls,
      guideResponse: guideResponse ?? this.guideResponse,
      isVerified: isVerified ?? this.isVerified,
      isPublished: isPublished ?? this.isPublished,
      tags: tags ?? this.tags,
    );
  }

  @override
  String toString() {
    return 'ReviewModel(id: $id, experienceId: $experienceId, userId: $userId, rating: $rating, content: $content, datePosted: $datePosted, helpfulCount: $helpfulCount)';
  }
}

/// Model representing a guide's response to a review
class GuideResponse {
  /// ID of the guide who responded
  final String guideId;

  /// Name of the guide who responded
  final String guideName;

  /// Content of the response
  final String content;

  /// Date when the response was posted
  final DateTime datePosted;

  /// Creates a new guide response
  const GuideResponse({
    required this.guideId,
    required this.guideName,
    required this.content,
    required this.datePosted,
  });

  /// Creates a guide response from a JSON map
  factory GuideResponse.fromJson(Map<String, dynamic> json) {
    return GuideResponse(
      guideId: json['guideId'] as String,
      guideName: json['guideName'] as String,
      content: json['content'] as String,
      datePosted: (json['datePosted'] is Timestamp)
          ? (json['datePosted'] as Timestamp).toDate()
          : DateTime.parse(json['datePosted'] as String),
    );
  }

  /// Converts this guide response to a JSON map
  Map<String, dynamic> toJson() {
    return {
      'guideId': guideId,
      'guideName': guideName,
      'content': content,
      'datePosted': datePosted.toIso8601String(),
    };
  }

  /// Creates a copy of this guide response with the given fields replaced with new values
  GuideResponse copyWith({
    String? guideId,
    String? guideName,
    String? content,
    DateTime? datePosted,
  }) {
    return GuideResponse(
      guideId: guideId ?? this.guideId,
      guideName: guideName ?? this.guideName,
      content: content ?? this.content,
      datePosted: datePosted ?? this.datePosted,
    );
  }

  @override
  String toString() {
    return 'GuideResponse(guideId: $guideId, guideName: $guideName, content: $content, datePosted: $datePosted)';
  }
}

/// Enum representing the sort options for reviews
enum ReviewSortOption {
  /// Sort by most recent first
  newest,

  /// Sort by oldest first
  oldest,

  /// Sort by highest rating first
  highestRated,

  /// Sort by lowest rating first
  lowestRated,

  /// Sort by most helpful first
  mostHelpful,

  /// Sort by reviews with photos first
  withPhotos,
}

/// Extension to provide display names for review sort options
extension ReviewSortOptionExtension on ReviewSortOption {
  /// Gets the display name for this sort option
  String get displayName {
    switch (this) {
      case ReviewSortOption.newest:
        return 'Most Recent';
      case ReviewSortOption.oldest:
        return 'Oldest First';
      case ReviewSortOption.highestRated:
        return 'Highest Rated';
      case ReviewSortOption.lowestRated:
        return 'Lowest Rated';
      case ReviewSortOption.mostHelpful:
        return 'Most Helpful';
      case ReviewSortOption.withPhotos:
        return 'With Photos';
    }
  }
}
