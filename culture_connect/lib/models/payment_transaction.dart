/// Represents a payment transaction
class PaymentTransaction {
  /// Unique identifier for the transaction
  final String id;
  
  /// ID of the booking associated with this transaction
  final String bookingId;
  
  /// ID of the payment method used for this transaction
  final String paymentMethodId;
  
  /// Type of transaction
  final TransactionType type;
  
  /// Status of the transaction
  final TransactionStatus status;
  
  /// Amount of the transaction
  final double amount;
  
  /// Currency of the transaction
  final String currency;
  
  /// Fee charged for the transaction
  final double? fee;
  
  /// Net amount after fees
  final double? netAmount;
  
  /// Transaction ID from the payment provider
  final String? providerTransactionId;
  
  /// Name of the payment provider
  final String provider;
  
  /// Additional data from the payment provider
  final Map<String, dynamic>? metadata;
  
  /// Error message if the transaction failed
  final String? errorMessage;
  
  /// Error code if the transaction failed
  final String? errorCode;
  
  /// When the transaction was created
  final DateTime createdAt;
  
  /// When the transaction was last updated
  final DateTime updatedAt;

  /// Creates a new payment transaction
  const PaymentTransaction({
    required this.id,
    required this.bookingId,
    required this.paymentMethodId,
    required this.type,
    required this.status,
    required this.amount,
    required this.currency,
    this.fee,
    this.netAmount,
    this.providerTransactionId,
    required this.provider,
    this.metadata,
    this.errorMessage,
    this.errorCode,
    required this.createdAt,
    required this.updatedAt,
  });

  /// Creates a payment transaction from a JSON map
  factory PaymentTransaction.fromJson(Map<String, dynamic> json) {
    return PaymentTransaction(
      id: json['id'] as String,
      bookingId: json['bookingId'] as String,
      paymentMethodId: json['paymentMethodId'] as String,
      type: TransactionTypeExtension.fromString(json['type'] as String),
      status: TransactionStatusExtension.fromString(json['status'] as String),
      amount: (json['amount'] as num).toDouble(),
      currency: json['currency'] as String,
      fee: json['fee'] != null ? (json['fee'] as num).toDouble() : null,
      netAmount: json['netAmount'] != null ? (json['netAmount'] as num).toDouble() : null,
      providerTransactionId: json['providerTransactionId'] as String?,
      provider: json['provider'] as String,
      metadata: json['metadata'] as Map<String, dynamic>?,
      errorMessage: json['errorMessage'] as String?,
      errorCode: json['errorCode'] as String?,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
    );
  }

  /// Converts this payment transaction to a JSON map
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'bookingId': bookingId,
      'paymentMethodId': paymentMethodId,
      'type': type.toString().split('.').last,
      'status': status.toString().split('.').last,
      'amount': amount,
      'currency': currency,
      if (fee != null) 'fee': fee,
      if (netAmount != null) 'netAmount': netAmount,
      if (providerTransactionId != null) 'providerTransactionId': providerTransactionId,
      'provider': provider,
      if (metadata != null) 'metadata': metadata,
      if (errorMessage != null) 'errorMessage': errorMessage,
      if (errorCode != null) 'errorCode': errorCode,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  /// Creates a copy of this payment transaction with the given fields replaced with the new values
  PaymentTransaction copyWith({
    String? id,
    String? bookingId,
    String? paymentMethodId,
    TransactionType? type,
    TransactionStatus? status,
    double? amount,
    String? currency,
    double? fee,
    double? netAmount,
    String? providerTransactionId,
    String? provider,
    Map<String, dynamic>? metadata,
    String? errorMessage,
    String? errorCode,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return PaymentTransaction(
      id: id ?? this.id,
      bookingId: bookingId ?? this.bookingId,
      paymentMethodId: paymentMethodId ?? this.paymentMethodId,
      type: type ?? this.type,
      status: status ?? this.status,
      amount: amount ?? this.amount,
      currency: currency ?? this.currency,
      fee: fee ?? this.fee,
      netAmount: netAmount ?? this.netAmount,
      providerTransactionId: providerTransactionId ?? this.providerTransactionId,
      provider: provider ?? this.provider,
      metadata: metadata ?? this.metadata,
      errorMessage: errorMessage ?? this.errorMessage,
      errorCode: errorCode ?? this.errorCode,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// Returns a formatted amount with currency symbol
  String get formattedAmount {
    String symbol = _getCurrencySymbol(currency);
    return '$symbol${amount.toStringAsFixed(2)}';
  }

  /// Returns a formatted net amount with currency symbol
  String? get formattedNetAmount {
    if (netAmount == null) return null;
    String symbol = _getCurrencySymbol(currency);
    return '$symbol${netAmount!.toStringAsFixed(2)}';
  }

  /// Returns a formatted fee with currency symbol
  String? get formattedFee {
    if (fee == null) return null;
    String symbol = _getCurrencySymbol(currency);
    return '$symbol${fee!.toStringAsFixed(2)}';
  }

  /// Returns a currency symbol for the given currency code
  String _getCurrencySymbol(String currencyCode) {
    switch (currencyCode.toUpperCase()) {
      case 'USD':
        return '\$';
      case 'EUR':
        return '€';
      case 'GBP':
        return '£';
      case 'NGN':
        return '₦';
      case 'KES':
        return 'KSh';
      case 'ZAR':
        return 'R';
      default:
        return currencyCode;
    }
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is PaymentTransaction && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

/// Types of payment transactions
enum TransactionType {
  payment,
  refund,
  payout,
  fee,
  other,
}

/// Extension on TransactionType to provide helper methods
extension TransactionTypeExtension on TransactionType {
  /// Returns a display name for this transaction type
  String get displayName {
    switch (this) {
      case TransactionType.payment:
        return 'Payment';
      case TransactionType.refund:
        return 'Refund';
      case TransactionType.payout:
        return 'Payout';
      case TransactionType.fee:
        return 'Fee';
      case TransactionType.other:
        return 'Other';
    }
  }

  /// Creates a TransactionType from a string
  static TransactionType fromString(String type) {
    return TransactionType.values.firstWhere(
      (e) => e.toString().split('.').last == type,
      orElse: () => TransactionType.other,
    );
  }
}

/// Status of a payment transaction
enum TransactionStatus {
  pending,
  processing,
  completed,
  failed,
  refunded,
  partiallyRefunded,
  disputed,
  canceled,
}

/// Extension on TransactionStatus to provide helper methods
extension TransactionStatusExtension on TransactionStatus {
  /// Returns a display name for this transaction status
  String get displayName {
    switch (this) {
      case TransactionStatus.pending:
        return 'Pending';
      case TransactionStatus.processing:
        return 'Processing';
      case TransactionStatus.completed:
        return 'Completed';
      case TransactionStatus.failed:
        return 'Failed';
      case TransactionStatus.refunded:
        return 'Refunded';
      case TransactionStatus.partiallyRefunded:
        return 'Partially Refunded';
      case TransactionStatus.disputed:
        return 'Disputed';
      case TransactionStatus.canceled:
        return 'Canceled';
    }
  }

  /// Creates a TransactionStatus from a string
  static TransactionStatus fromString(String status) {
    return TransactionStatus.values.firstWhere(
      (e) => e.toString().split('.').last == status,
      orElse: () => TransactionStatus.pending,
    );
  }
}
