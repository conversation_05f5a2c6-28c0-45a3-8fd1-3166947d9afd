import 'package:culture_connect/models/payment/payment_method_model.dart';

/// A model representing a card payment method
class CardPaymentMethodModel extends PaymentMethodModel {
  /// The card number (masked)
  final String maskedCardNumber;
  
  /// The card holder name
  final String cardHolderName;
  
  /// The expiration month
  final int expirationMonth;
  
  /// The expiration year
  final int expirationYear;
  
  /// The card type
  final CardType cardType;
  
  /// The billing address
  final Map<String, String>? billingAddress;
  
  /// Creates a new card payment method model
  CardPaymentMethodModel({
    required String id,
    required PaymentMethodType type,
    required String name,
    bool isDefault = false,
    DateTime? lastUsed,
    Map<String, dynamic> details = const {},
    required this.maskedCardNumber,
    required this.cardHolderName,
    required this.expirationMonth,
    required this.expirationYear,
    required this.cardType,
    this.billingAddress,
  }) : super(
          id: id,
          type: type,
          name: name,
          isDefault: isDefault,
          lastUsed: lastUsed,
          details: details,
        );
  
  /// Creates a copy with some fields replaced
  @override
  CardPaymentMethodModel copyWith({
    String? id,
    PaymentMethodType? type,
    String? name,
    bool? isDefault,
    DateTime? lastUsed,
    Map<String, dynamic>? details,
    String? maskedCardNumber,
    String? cardHolderName,
    int? expirationMonth,
    int? expirationYear,
    CardType? cardType,
    Map<String, String>? billingAddress,
  }) {
    return CardPaymentMethodModel(
      id: id ?? this.id,
      type: type ?? this.type,
      name: name ?? this.name,
      isDefault: isDefault ?? this.isDefault,
      lastUsed: lastUsed ?? this.lastUsed,
      details: details ?? this.details,
      maskedCardNumber: maskedCardNumber ?? this.maskedCardNumber,
      cardHolderName: cardHolderName ?? this.cardHolderName,
      expirationMonth: expirationMonth ?? this.expirationMonth,
      expirationYear: expirationYear ?? this.expirationYear,
      cardType: cardType ?? this.cardType,
      billingAddress: billingAddress ?? this.billingAddress,
    );
  }
  
  /// Get the formatted expiration date
  String get formattedExpirationDate {
    final month = expirationMonth.toString().padLeft(2, '0');
    final year = expirationYear.toString().substring(2);
    return '$month/$year';
  }
  
  /// Get the last 4 digits of the card number
  String get last4Digits {
    if (maskedCardNumber.length < 4) return maskedCardNumber;
    return maskedCardNumber.substring(maskedCardNumber.length - 4);
  }
  
  /// Check if the card is expired
  bool get isExpired {
    final now = DateTime.now();
    return (expirationYear < now.year) ||
        (expirationYear == now.year && expirationMonth < now.month);
  }
  
  /// Get the formatted billing address
  String? get formattedBillingAddress {
    if (billingAddress == null) return null;
    
    final addressParts = <String>[];
    
    if (billingAddress!.containsKey('street')) {
      addressParts.add(billingAddress!['street']!);
    }
    
    if (billingAddress!.containsKey('city')) {
      addressParts.add(billingAddress!['city']!);
    }
    
    if (billingAddress!.containsKey('state')) {
      addressParts.add(billingAddress!['state']!);
    }
    
    if (billingAddress!.containsKey('postalCode')) {
      addressParts.add(billingAddress!['postalCode']!);
    }
    
    if (billingAddress!.containsKey('country')) {
      addressParts.add(billingAddress!['country']!);
    }
    
    return addressParts.join(', ');
  }
  
  /// Converts to JSON
  @override
  Map<String, dynamic> toJson() {
    final json = super.toJson();
    json.addAll({
      'maskedCardNumber': maskedCardNumber,
      'cardHolderName': cardHolderName,
      'expirationMonth': expirationMonth,
      'expirationYear': expirationYear,
      'cardType': cardType.index,
      'billingAddress': billingAddress,
    });
    return json;
  }
  
  /// Creates from JSON
  factory CardPaymentMethodModel.fromJson(Map<String, dynamic> json) {
    return CardPaymentMethodModel(
      id: json['id'] as String,
      type: PaymentMethodType.values[json['type'] as int],
      name: json['name'] as String,
      isDefault: json['isDefault'] as bool? ?? false,
      lastUsed: json['lastUsed'] != null
          ? DateTime.parse(json['lastUsed'] as String)
          : null,
      details: json['details'] as Map<String, dynamic>? ?? {},
      maskedCardNumber: json['maskedCardNumber'] as String,
      cardHolderName: json['cardHolderName'] as String,
      expirationMonth: json['expirationMonth'] as int,
      expirationYear: json['expirationYear'] as int,
      cardType: CardType.values[json['cardType'] as int],
      billingAddress: json['billingAddress'] != null
          ? Map<String, String>.from(json['billingAddress'] as Map)
          : null,
    );
  }
  
  /// Detect card type from card number
  static CardType detectCardType(String cardNumber) {
    // Remove all non-digit characters
    final cleanNumber = cardNumber.replaceAll(RegExp(r'\D'), '');
    
    if (cleanNumber.isEmpty) {
      return CardType.unknown;
    }
    
    // Visa: Starts with 4
    if (RegExp(r'^4').hasMatch(cleanNumber)) {
      return CardType.visa;
    }
    
    // Mastercard: Starts with 51-55 or 2221-2720
    if (RegExp(r'^5[1-5]').hasMatch(cleanNumber) ||
        RegExp(r'^2[2-7][2-7]\d').hasMatch(cleanNumber)) {
      return CardType.mastercard;
    }
    
    // American Express: Starts with 34 or 37
    if (RegExp(r'^3[47]').hasMatch(cleanNumber)) {
      return CardType.amex;
    }
    
    // Discover: Starts with 6011, 622126-622925, 644-649, or 65
    if (RegExp(r'^6011').hasMatch(cleanNumber) ||
        RegExp(r'^622(12[6-9]|1[3-9]|[2-8]|9[0-1]|92[0-5])').hasMatch(cleanNumber) ||
        RegExp(r'^6[4-5]').hasMatch(cleanNumber)) {
      return CardType.discover;
    }
    
    // Diners Club: Starts with 300-305, 36, or 38-39
    if (RegExp(r'^3(0[0-5]|[68])').hasMatch(cleanNumber)) {
      return CardType.dinersClub;
    }
    
    // JCB: Starts with 2131, 1800, or 35
    if (RegExp(r'^(2131|1800|35)').hasMatch(cleanNumber)) {
      return CardType.jcb;
    }
    
    // Union Pay: Starts with 62
    if (RegExp(r'^62').hasMatch(cleanNumber)) {
      return CardType.unionPay;
    }
    
    return CardType.unknown;
  }
  
  /// Validate a card number using the Luhn algorithm
  static bool validateCardNumber(String cardNumber) {
    // Remove all non-digit characters
    final cleanNumber = cardNumber.replaceAll(RegExp(r'\D'), '');
    
    if (cleanNumber.isEmpty) {
      return false;
    }
    
    // Luhn algorithm
    int sum = 0;
    bool alternate = false;
    
    for (int i = cleanNumber.length - 1; i >= 0; i--) {
      int digit = int.parse(cleanNumber[i]);
      
      if (alternate) {
        digit *= 2;
        if (digit > 9) {
          digit -= 9;
        }
      }
      
      sum += digit;
      alternate = !alternate;
    }
    
    return sum % 10 == 0;
  }
}
