import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:culture_connect/models/payment/payment_method.dart';

/// Model representing a digital wallet payment method (PayPal, Apple Pay, Google Pay)
class DigitalWalletPaymentMethod extends PaymentMethod {
  /// The email associated with the digital wallet
  final String email;

  /// The account ID or username for the wallet
  final String accountId;

  /// Creates a new digital wallet payment method
  const DigitalWalletPaymentMethod({
    required super.id,
    required super.type,
    required super.userId,
    required this.email,
    required this.accountId,
    super.isDefault,
    required super.createdAt,
    required super.updatedAt,
  }) : assert(
          type == PaymentMethodType.paypal ||
              type == PaymentMethodType.applePay ||
              type == PaymentMethodType.googlePay,
          'Digital wallet type must be paypal, applePay, or googlePay',
        );

  /// Creates a digital wallet payment method from a JSON map
  factory DigitalWalletPaymentMethod.fromJson(Map<String, dynamic> json) {
    final typeStr = json['type'] as String;
    PaymentMethodType type;

    switch (typeStr) {
      case 'paypal':
        type = PaymentMethodType.paypal;
        break;
      case 'applePay':
        type = PaymentMethodType.applePay;
        break;
      case 'googlePay':
        type = PaymentMethodType.googlePay;
        break;
      default:
        throw ArgumentError('Invalid digital wallet type: $typeStr');
    }

    return DigitalWalletPaymentMethod(
      id: json['id'] as String,
      type: type,
      userId: json['userId'] as String,
      email: json['email'] as String,
      accountId: json['accountId'] as String,
      isDefault: json['isDefault'] as bool? ?? false,
      createdAt: (json['createdAt'] is Timestamp)
          ? (json['createdAt'] as Timestamp).toDate()
          : DateTime.parse(json['createdAt'] as String),
      updatedAt: (json['updatedAt'] is Timestamp)
          ? (json['updatedAt'] as Timestamp).toDate()
          : DateTime.parse(json['updatedAt'] as String),
    );
  }

  @override
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type.toString().split('.').last,
      'userId': userId,
      'email': email,
      'accountId': accountId,
      'isDefault': isDefault,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  @override
  String getMaskedDetails() {
    // Mask the email for display
    final parts = email.split('@');
    if (parts.length != 2) return email;

    final username = parts[0];
    final domain = parts[1];

    String maskedUsername;
    if (username.length <= 2) {
      maskedUsername = username;
    } else {
      maskedUsername = username.substring(0, 2) + '•' * (username.length - 2);
    }

    return '$maskedUsername@$domain';
  }

  /// Creates a copy of this digital wallet payment method with the given fields replaced with new values
  DigitalWalletPaymentMethod copyWith({
    String? id,
    PaymentMethodType? type,
    String? userId,
    String? email,
    String? accountId,
    bool? isDefault,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return DigitalWalletPaymentMethod(
      id: id ?? this.id,
      type: type ?? this.type,
      userId: userId ?? this.userId,
      email: email ?? this.email,
      accountId: accountId ?? this.accountId,
      isDefault: isDefault ?? this.isDefault,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// Validates an email address
  static bool validateEmail(String email) {
    final emailRegex = RegExp(
      r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$',
    );
    return emailRegex.hasMatch(email);
  }
}
