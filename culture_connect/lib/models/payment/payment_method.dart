import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:encrypt/encrypt.dart' as encrypt;

/// Enum representing different payment method types
enum PaymentMethodType {
  /// Credit or debit card
  creditCard,

  /// PayPal digital wallet
  paypal,

  /// Apple Pay digital wallet
  applePay,

  /// Google Pay digital wallet
  googlePay,

  /// Bitcoin cryptocurrency
  bitcoin,

  /// Ethereum cryptocurrency
  ethereum,

  /// Bank transfer
  bankTransfer,
}

/// Extension to provide display names for payment method types
extension PaymentMethodTypeExtension on PaymentMethodType {
  /// Gets the display name for this payment method type
  String get displayName {
    switch (this) {
      case PaymentMethodType.creditCard:
        return 'Credit/Debit Card';
      case PaymentMethodType.paypal:
        return 'PayPal';
      case PaymentMethodType.applePay:
        return 'Apple Pay';
      case PaymentMethodType.googlePay:
        return 'Google Pay';
      case PaymentMethodType.bitcoin:
        return 'Bitcoin';
      case PaymentMethodType.ethereum:
        return 'Ethereum';
      case PaymentMethodType.bankTransfer:
        return 'Bank Transfer';
    }
  }

  /// Gets the icon name for this payment method type
  String get iconName {
    switch (this) {
      case PaymentMethodType.creditCard:
        return 'credit_card';
      case PaymentMethodType.paypal:
        return 'paypal';
      case PaymentMethodType.applePay:
        return 'apple_pay';
      case PaymentMethodType.googlePay:
        return 'google_pay';
      case PaymentMethodType.bitcoin:
        return 'bitcoin';
      case PaymentMethodType.ethereum:
        return 'ethereum';
      case PaymentMethodType.bankTransfer:
        return 'bank_transfer';
    }
  }
}

/// Base class for payment methods
abstract class PaymentMethod {
  /// Unique identifier for the payment method
  final String id;

  /// Type of payment method
  final PaymentMethodType type;

  /// User ID who owns this payment method
  final String userId;

  /// Whether this is the default payment method
  final bool isDefault;

  /// When the payment method was created
  final DateTime createdAt;

  /// When the payment method was last updated
  final DateTime updatedAt;

  /// Creates a new payment method
  const PaymentMethod({
    required this.id,
    required this.type,
    required this.userId,
    this.isDefault = false,
    required this.createdAt,
    required this.updatedAt,
  });

  /// Converts this payment method to a JSON map
  Map<String, dynamic> toJson();

  /// Returns a masked version of the payment method details for display
  String getMaskedDetails();
}

/// Model representing a credit card payment method
class CreditCardPaymentMethod extends PaymentMethod {
  /// The encrypted card number
  final String encryptedCardNumber;

  /// The last 4 digits of the card number (not encrypted for display purposes)
  final String last4;

  /// The card holder name
  final String cardHolderName;

  /// The expiration month (1-12)
  final int expiryMonth;

  /// The expiration year (4 digits)
  final int expiryYear;

  /// The card brand (Visa, Mastercard, etc.)
  final String brand;

  /// Creates a new credit card payment method
  const CreditCardPaymentMethod({
    required super.id,
    required super.userId,
    required this.encryptedCardNumber,
    required this.last4,
    required this.cardHolderName,
    required this.expiryMonth,
    required this.expiryYear,
    required this.brand,
    super.isDefault,
    required super.createdAt,
    required super.updatedAt,
  }) : super(
          type: PaymentMethodType.creditCard,
        );

  /// Creates a credit card payment method from a JSON map
  factory CreditCardPaymentMethod.fromJson(Map<String, dynamic> json) {
    return CreditCardPaymentMethod(
      id: json['id'] as String,
      userId: json['userId'] as String,
      encryptedCardNumber: json['encryptedCardNumber'] as String,
      last4: json['last4'] as String,
      cardHolderName: json['cardHolderName'] as String,
      expiryMonth: json['expiryMonth'] as int,
      expiryYear: json['expiryYear'] as int,
      brand: json['brand'] as String,
      isDefault: json['isDefault'] as bool? ?? false,
      createdAt: (json['createdAt'] is Timestamp)
          ? (json['createdAt'] as Timestamp).toDate()
          : DateTime.parse(json['createdAt'] as String),
      updatedAt: (json['updatedAt'] is Timestamp)
          ? (json['updatedAt'] as Timestamp).toDate()
          : DateTime.parse(json['updatedAt'] as String),
    );
  }

  @override
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type.toString().split('.').last,
      'userId': userId,
      'encryptedCardNumber': encryptedCardNumber,
      'last4': last4,
      'cardHolderName': cardHolderName,
      'expiryMonth': expiryMonth,
      'expiryYear': expiryYear,
      'brand': brand,
      'isDefault': isDefault,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  @override
  String getMaskedDetails() {
    return '•••• •••• •••• $last4';
  }

  /// Creates a copy of this credit card payment method with the given fields replaced with new values
  CreditCardPaymentMethod copyWith({
    String? id,
    String? userId,
    String? encryptedCardNumber,
    String? last4,
    String? cardHolderName,
    int? expiryMonth,
    int? expiryYear,
    String? brand,
    bool? isDefault,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return CreditCardPaymentMethod(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      encryptedCardNumber: encryptedCardNumber ?? this.encryptedCardNumber,
      last4: last4 ?? this.last4,
      cardHolderName: cardHolderName ?? this.cardHolderName,
      expiryMonth: expiryMonth ?? this.expiryMonth,
      expiryYear: expiryYear ?? this.expiryYear,
      brand: brand ?? this.brand,
      isDefault: isDefault ?? this.isDefault,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// Encrypts a card number using AES encryption
  static String encryptCardNumber(String cardNumber, String encryptionKey) {
    final key = encrypt.Key.fromUtf8(encryptionKey);
    final iv = encrypt.IV.fromLength(16);
    final encrypter = encrypt.Encrypter(encrypt.AES(key));

    final encrypted = encrypter.encrypt(cardNumber, iv: iv);
    return encrypted.base64;
  }

  /// Decrypts an encrypted card number using AES encryption
  static String decryptCardNumber(
      String encryptedCardNumber, String encryptionKey) {
    final key = encrypt.Key.fromUtf8(encryptionKey);
    final iv = encrypt.IV.fromLength(16);
    final encrypter = encrypt.Encrypter(encrypt.AES(key));

    final encrypted = encrypt.Encrypted.fromBase64(encryptedCardNumber);
    return encrypter.decrypt(encrypted, iv: iv);
  }

  /// Validates a credit card number using the Luhn algorithm
  static bool validateCardNumber(String cardNumber) {
    // Remove any non-digit characters
    final cleanNumber = cardNumber.replaceAll(RegExp(r'\D'), '');

    if (cleanNumber.isEmpty) {
      return false;
    }

    // Luhn algorithm
    int sum = 0;
    bool alternate = false;

    for (int i = cleanNumber.length - 1; i >= 0; i--) {
      int digit = int.parse(cleanNumber[i]);

      if (alternate) {
        digit *= 2;
        if (digit > 9) {
          digit -= 9;
        }
      }

      sum += digit;
      alternate = !alternate;
    }

    return sum % 10 == 0;
  }

  /// Determines the card brand based on the card number
  static String getCardBrand(String cardNumber) {
    // Remove any non-digit characters
    final cleanNumber = cardNumber.replaceAll(RegExp(r'\D'), '');

    if (cleanNumber.isEmpty) {
      return 'Unknown';
    }

    // Visa
    if (cleanNumber.startsWith(RegExp(r'4'))) {
      return 'Visa';
    }

    // Mastercard
    if (cleanNumber.startsWith(RegExp(r'5[1-5]'))) {
      return 'Mastercard';
    }

    // American Express
    if (cleanNumber.startsWith(RegExp(r'3[47]'))) {
      return 'American Express';
    }

    // Discover
    if (cleanNumber.startsWith(RegExp(r'6(?:011|5[0-9]{2})'))) {
      return 'Discover';
    }

    return 'Unknown';
  }
}
