import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:encrypt/encrypt.dart' as encrypt;
import 'package:culture_connect/models/payment/payment_method.dart';

/// Model representing a bank transfer payment method
class BankTransferPaymentMethod extends PaymentMethod {
  /// The encrypted account number
  final String encryptedAccountNumber;

  /// The last 4 digits of the account number (not encrypted for display purposes)
  final String last4;

  /// The account holder name
  final String accountHolderName;

  /// The bank name
  final String bankName;

  /// The routing number
  final String routingNumber;

  /// The account type (checking, savings)
  final String accountType;

  /// Creates a new bank transfer payment method
  const BankTransferPaymentMethod({
    required super.id,
    required super.userId,
    required this.encryptedAccountNumber,
    required this.last4,
    required this.accountHolderName,
    required this.bankName,
    required this.routingNumber,
    required this.accountType,
    super.isDefault,
    required super.createdAt,
    required super.updatedAt,
  }) : super(
          type: PaymentMethodType.bankTransfer,
        );

  /// Creates a bank transfer payment method from a JSON map
  factory BankTransferPaymentMethod.fromJson(Map<String, dynamic> json) {
    return BankTransferPaymentMethod(
      id: json['id'] as String,
      userId: json['userId'] as String,
      encryptedAccountNumber: json['encryptedAccountNumber'] as String,
      last4: json['last4'] as String,
      accountHolderName: json['accountHolderName'] as String,
      bankName: json['bankName'] as String,
      routingNumber: json['routingNumber'] as String,
      accountType: json['accountType'] as String,
      isDefault: json['isDefault'] as bool? ?? false,
      createdAt: (json['createdAt'] is Timestamp)
          ? (json['createdAt'] as Timestamp).toDate()
          : DateTime.parse(json['createdAt'] as String),
      updatedAt: (json['updatedAt'] is Timestamp)
          ? (json['updatedAt'] as Timestamp).toDate()
          : DateTime.parse(json['updatedAt'] as String),
    );
  }

  @override
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type.toString().split('.').last,
      'userId': userId,
      'encryptedAccountNumber': encryptedAccountNumber,
      'last4': last4,
      'accountHolderName': accountHolderName,
      'bankName': bankName,
      'routingNumber': routingNumber,
      'accountType': accountType,
      'isDefault': isDefault,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  @override
  String getMaskedDetails() {
    return '$bankName •••• $last4';
  }

  /// Creates a copy of this bank transfer payment method with the given fields replaced with new values
  BankTransferPaymentMethod copyWith({
    String? id,
    String? userId,
    String? encryptedAccountNumber,
    String? last4,
    String? accountHolderName,
    String? bankName,
    String? routingNumber,
    String? accountType,
    bool? isDefault,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return BankTransferPaymentMethod(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      encryptedAccountNumber:
          encryptedAccountNumber ?? this.encryptedAccountNumber,
      last4: last4 ?? this.last4,
      accountHolderName: accountHolderName ?? this.accountHolderName,
      bankName: bankName ?? this.bankName,
      routingNumber: routingNumber ?? this.routingNumber,
      accountType: accountType ?? this.accountType,
      isDefault: isDefault ?? this.isDefault,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// Encrypts an account number using AES encryption
  static String encryptAccountNumber(
      String accountNumber, String encryptionKey) {
    final key = encrypt.Key.fromUtf8(encryptionKey);
    final iv = encrypt.IV.fromLength(16);
    final encrypter = encrypt.Encrypter(encrypt.AES(key));

    final encrypted = encrypter.encrypt(accountNumber, iv: iv);
    return encrypted.base64;
  }

  /// Decrypts an encrypted account number using AES encryption
  static String decryptAccountNumber(
      String encryptedAccountNumber, String encryptionKey) {
    final key = encrypt.Key.fromUtf8(encryptionKey);
    final iv = encrypt.IV.fromLength(16);
    final encrypter = encrypt.Encrypter(encrypt.AES(key));

    final encrypted = encrypt.Encrypted.fromBase64(encryptedAccountNumber);
    return encrypter.decrypt(encrypted, iv: iv);
  }

  /// Validates a US routing number using the checksum algorithm
  static bool validateRoutingNumber(String routingNumber) {
    // Remove any non-digit characters
    final cleanNumber = routingNumber.replaceAll(RegExp(r'\D'), '');

    if (cleanNumber.length != 9) {
      return false;
    }

    // Routing number checksum algorithm
    int sum = 0;
    for (int i = 0; i < 9; i += 3) {
      sum += int.parse(cleanNumber[i]) * 3;
      sum += int.parse(cleanNumber[i + 1]) * 7;
      sum += int.parse(cleanNumber[i + 2]);
    }

    return sum % 10 == 0;
  }

  /// Validates a bank account number (basic validation)
  static bool validateAccountNumber(String accountNumber) {
    // Remove any non-digit characters
    final cleanNumber = accountNumber.replaceAll(RegExp(r'\D'), '');

    // Basic validation: account numbers are typically 8-17 digits
    return cleanNumber.length >= 8 && cleanNumber.length <= 17;
  }
}
