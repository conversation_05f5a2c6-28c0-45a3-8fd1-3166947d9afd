import 'package:cloud_firestore/cloud_firestore.dart';

/// Model representing an invoice or receipt for a transaction
class Invoice {
  /// Unique identifier for the invoice
  final String id;

  /// Transaction ID associated with this invoice
  final String transactionId;

  /// User ID who owns this invoice
  final String userId;

  /// Experience ID associated with this invoice
  final String experienceId;

  /// Booking ID associated with this invoice
  final String bookingId;

  /// Invoice number (for reference)
  final String invoiceNumber;

  /// Date when the invoice was issued
  final DateTime issueDate;

  /// Date when the invoice is due (for pending payments)
  final DateTime? dueDate;

  /// Date when the invoice was paid
  final DateTime? paidDate;

  /// Subtotal amount before taxes and fees
  final double subtotal;

  /// Tax amount
  final double taxAmount;

  /// Service fee amount
  final double serviceFeeAmount;

  /// Total amount including taxes and fees
  final double totalAmount;

  /// Currency of the invoice
  final String currency;

  /// Status of the invoice (paid, pending, canceled)
  final String status;

  /// Notes or additional information
  final String? notes;

  /// Line items in the invoice
  final List<InvoiceLineItem> lineItems;

  /// Creates a new invoice
  const Invoice({
    required this.id,
    required this.transactionId,
    required this.userId,
    required this.experienceId,
    required this.bookingId,
    required this.invoiceNumber,
    required this.issueDate,
    this.dueDate,
    this.paidDate,
    required this.subtotal,
    required this.taxAmount,
    required this.serviceFeeAmount,
    required this.totalAmount,
    required this.currency,
    required this.status,
    this.notes,
    required this.lineItems,
  });

  /// Creates an invoice from a JSON map
  factory Invoice.fromJson(Map<String, dynamic> json) {
    return Invoice(
      id: json['id'] as String,
      transactionId: json['transactionId'] as String,
      userId: json['userId'] as String,
      experienceId: json['experienceId'] as String,
      bookingId: json['bookingId'] as String,
      invoiceNumber: json['invoiceNumber'] as String,
      issueDate: (json['issueDate'] is Timestamp)
          ? (json['issueDate'] as Timestamp).toDate()
          : DateTime.parse(json['issueDate'] as String),
      dueDate: json['dueDate'] != null
          ? (json['dueDate'] is Timestamp)
              ? (json['dueDate'] as Timestamp).toDate()
              : DateTime.parse(json['dueDate'] as String)
          : null,
      paidDate: json['paidDate'] != null
          ? (json['paidDate'] is Timestamp)
              ? (json['paidDate'] as Timestamp).toDate()
              : DateTime.parse(json['paidDate'] as String)
          : null,
      subtotal: (json['subtotal'] as num).toDouble(),
      taxAmount: (json['taxAmount'] as num).toDouble(),
      serviceFeeAmount: (json['serviceFeeAmount'] as num).toDouble(),
      totalAmount: (json['totalAmount'] as num).toDouble(),
      currency: json['currency'] as String,
      status: json['status'] as String,
      notes: json['notes'] as String?,
      lineItems: (json['lineItems'] as List<dynamic>)
          .map((item) => InvoiceLineItem.fromJson(item as Map<String, dynamic>))
          .toList(),
    );
  }

  /// Converts this invoice to a JSON map
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'transactionId': transactionId,
      'userId': userId,
      'experienceId': experienceId,
      'bookingId': bookingId,
      'invoiceNumber': invoiceNumber,
      'issueDate': issueDate.toIso8601String(),
      'dueDate': dueDate?.toIso8601String(),
      'paidDate': paidDate?.toIso8601String(),
      'subtotal': subtotal,
      'taxAmount': taxAmount,
      'serviceFeeAmount': serviceFeeAmount,
      'totalAmount': totalAmount,
      'currency': currency,
      'status': status,
      'notes': notes,
      'lineItems': lineItems.map((item) => item.toJson()).toList(),
    };
  }

  /// Creates a copy of this invoice with the given fields replaced with new values
  Invoice copyWith({
    String? id,
    String? transactionId,
    String? userId,
    String? experienceId,
    String? bookingId,
    String? invoiceNumber,
    DateTime? issueDate,
    DateTime? dueDate,
    DateTime? paidDate,
    double? subtotal,
    double? taxAmount,
    double? serviceFeeAmount,
    double? totalAmount,
    String? currency,
    String? status,
    String? notes,
    List<InvoiceLineItem>? lineItems,
  }) {
    return Invoice(
      id: id ?? this.id,
      transactionId: transactionId ?? this.transactionId,
      userId: userId ?? this.userId,
      experienceId: experienceId ?? this.experienceId,
      bookingId: bookingId ?? this.bookingId,
      invoiceNumber: invoiceNumber ?? this.invoiceNumber,
      issueDate: issueDate ?? this.issueDate,
      dueDate: dueDate ?? this.dueDate,
      paidDate: paidDate ?? this.paidDate,
      subtotal: subtotal ?? this.subtotal,
      taxAmount: taxAmount ?? this.taxAmount,
      serviceFeeAmount: serviceFeeAmount ?? this.serviceFeeAmount,
      totalAmount: totalAmount ?? this.totalAmount,
      currency: currency ?? this.currency,
      status: status ?? this.status,
      notes: notes ?? this.notes,
      lineItems: lineItems ?? this.lineItems,
    );
  }

  /// Formats an amount with the currency symbol
  String formatAmount(double amount) {
    String symbol;
    switch (currency.toUpperCase()) {
      case 'USD':
        symbol = '\$';
        break;
      case 'EUR':
        symbol = '€';
        break;
      case 'GBP':
        symbol = '£';
        break;
      default:
        symbol = currency;
    }

    return '$symbol${amount.toStringAsFixed(2)}';
  }

  /// Gets the formatted subtotal
  String getFormattedSubtotal() {
    return formatAmount(subtotal);
  }

  /// Gets the formatted tax amount
  String getFormattedTaxAmount() {
    return formatAmount(taxAmount);
  }

  /// Gets the formatted service fee amount
  String getFormattedServiceFeeAmount() {
    return formatAmount(serviceFeeAmount);
  }

  /// Gets the formatted total amount
  String getFormattedTotalAmount() {
    return formatAmount(totalAmount);
  }
}

/// Model representing a line item in an invoice
class InvoiceLineItem {
  /// Description of the line item
  final String description;

  /// Quantity of the item
  final int quantity;

  /// Unit price of the item
  final double unitPrice;

  /// Total price for this line item (quantity * unitPrice)
  final double totalPrice;

  /// Creates a new invoice line item
  const InvoiceLineItem({
    required this.description,
    required this.quantity,
    required this.unitPrice,
    required this.totalPrice,
  });

  /// Creates an invoice line item from a JSON map
  factory InvoiceLineItem.fromJson(Map<String, dynamic> json) {
    return InvoiceLineItem(
      description: json['description'] as String,
      quantity: json['quantity'] as int,
      unitPrice: (json['unitPrice'] as num).toDouble(),
      totalPrice: (json['totalPrice'] as num).toDouble(),
    );
  }

  /// Converts this invoice line item to a JSON map
  Map<String, dynamic> toJson() {
    return {
      'description': description,
      'quantity': quantity,
      'unitPrice': unitPrice,
      'totalPrice': totalPrice,
    };
  }

  /// Creates a copy of this invoice line item with the given fields replaced with new values
  InvoiceLineItem copyWith({
    String? description,
    int? quantity,
    double? unitPrice,
    double? totalPrice,
  }) {
    return InvoiceLineItem(
      description: description ?? this.description,
      quantity: quantity ?? this.quantity,
      unitPrice: unitPrice ?? this.unitPrice,
      totalPrice: totalPrice ?? this.totalPrice,
    );
  }
}
