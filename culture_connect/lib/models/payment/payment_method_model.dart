import 'package:flutter/material.dart';

/// Enum representing different payment method types
enum PaymentMethodType {
  /// Credit card payment method
  creditCard,
  
  /// Debit card payment method
  debitCard,
  
  /// PayPal payment method
  paypal,
  
  /// Apple Pay payment method
  applePay,
  
  /// Google Pay payment method
  googlePay,
  
  /// Bank transfer payment method
  bankTransfer,
  
  /// Cash payment method
  cash,
  
  /// Gift card payment method
  giftCard,
  
  /// Loyalty points payment method
  loyaltyPoints,
  
  /// Other payment method
  other,
}

/// Extension on PaymentMethodType to provide additional functionality
extension PaymentMethodTypeExtension on PaymentMethodType {
  /// Get the display name of the payment method type
  String get displayName {
    switch (this) {
      case PaymentMethodType.creditCard:
        return 'Credit Card';
      case PaymentMethodType.debitCard:
        return 'Debit Card';
      case PaymentMethodType.paypal:
        return 'PayPal';
      case PaymentMethodType.applePay:
        return 'Apple Pay';
      case PaymentMethodType.googlePay:
        return 'Google Pay';
      case PaymentMethodType.bankTransfer:
        return 'Bank Transfer';
      case PaymentMethodType.cash:
        return 'Cash';
      case PaymentMethodType.giftCard:
        return 'Gift Card';
      case PaymentMethodType.loyaltyPoints:
        return 'Loyalty Points';
      case PaymentMethodType.other:
        return 'Other';
    }
  }
  
  /// Get the icon of the payment method type
  IconData get icon {
    switch (this) {
      case PaymentMethodType.creditCard:
      case PaymentMethodType.debitCard:
        return Icons.credit_card;
      case PaymentMethodType.paypal:
        return Icons.account_balance_wallet;
      case PaymentMethodType.applePay:
        return Icons.apple;
      case PaymentMethodType.googlePay:
        return Icons.g_mobiledata;
      case PaymentMethodType.bankTransfer:
        return Icons.account_balance;
      case PaymentMethodType.cash:
        return Icons.money;
      case PaymentMethodType.giftCard:
        return Icons.card_giftcard;
      case PaymentMethodType.loyaltyPoints:
        return Icons.stars;
      case PaymentMethodType.other:
        return Icons.payment;
    }
  }
}

/// Enum representing different card types
enum CardType {
  /// Visa card
  visa,
  
  /// Mastercard card
  mastercard,
  
  /// American Express card
  amex,
  
  /// Discover card
  discover,
  
  /// Diners Club card
  dinersClub,
  
  /// JCB card
  jcb,
  
  /// Union Pay card
  unionPay,
  
  /// Other card type
  other,
  
  /// Unknown card type
  unknown,
}

/// Extension on CardType to provide additional functionality
extension CardTypeExtension on CardType {
  /// Get the display name of the card type
  String get displayName {
    switch (this) {
      case CardType.visa:
        return 'Visa';
      case CardType.mastercard:
        return 'Mastercard';
      case CardType.amex:
        return 'American Express';
      case CardType.discover:
        return 'Discover';
      case CardType.dinersClub:
        return 'Diners Club';
      case CardType.jcb:
        return 'JCB';
      case CardType.unionPay:
        return 'Union Pay';
      case CardType.other:
        return 'Other';
      case CardType.unknown:
        return 'Unknown';
    }
  }
  
  /// Get the logo asset path of the card type
  String get logoAsset {
    switch (this) {
      case CardType.visa:
        return 'assets/images/payment/visa.png';
      case CardType.mastercard:
        return 'assets/images/payment/mastercard.png';
      case CardType.amex:
        return 'assets/images/payment/amex.png';
      case CardType.discover:
        return 'assets/images/payment/discover.png';
      case CardType.dinersClub:
        return 'assets/images/payment/diners.png';
      case CardType.jcb:
        return 'assets/images/payment/jcb.png';
      case CardType.unionPay:
        return 'assets/images/payment/unionpay.png';
      case CardType.other:
      case CardType.unknown:
        return 'assets/images/payment/generic_card.png';
    }
  }
}

/// A model representing a payment method
class PaymentMethodModel {
  /// Unique identifier for the payment method
  final String id;
  
  /// Type of payment method
  final PaymentMethodType type;
  
  /// Name of the payment method
  final String name;
  
  /// Whether this is the default payment method
  final bool isDefault;
  
  /// Last used date
  final DateTime? lastUsed;
  
  /// Additional details about the payment method
  final Map<String, dynamic> details;
  
  /// Creates a new payment method model
  const PaymentMethodModel({
    required this.id,
    required this.type,
    required this.name,
    this.isDefault = false,
    this.lastUsed,
    this.details = const {},
  });
  
  /// Creates a copy with some fields replaced
  PaymentMethodModel copyWith({
    String? id,
    PaymentMethodType? type,
    String? name,
    bool? isDefault,
    DateTime? lastUsed,
    Map<String, dynamic>? details,
  }) {
    return PaymentMethodModel(
      id: id ?? this.id,
      type: type ?? this.type,
      name: name ?? this.name,
      isDefault: isDefault ?? this.isDefault,
      lastUsed: lastUsed ?? this.lastUsed,
      details: details ?? this.details,
    );
  }
  
  /// Converts to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type.index,
      'name': name,
      'isDefault': isDefault,
      'lastUsed': lastUsed?.toIso8601String(),
      'details': details,
    };
  }
  
  /// Creates from JSON
  factory PaymentMethodModel.fromJson(Map<String, dynamic> json) {
    return PaymentMethodModel(
      id: json['id'] as String,
      type: PaymentMethodType.values[json['type'] as int],
      name: json['name'] as String,
      isDefault: json['isDefault'] as bool? ?? false,
      lastUsed: json['lastUsed'] != null
          ? DateTime.parse(json['lastUsed'] as String)
          : null,
      details: json['details'] as Map<String, dynamic>? ?? {},
    );
  }
}
