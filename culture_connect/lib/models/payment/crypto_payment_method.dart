import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:culture_connect/models/payment/payment_method.dart';

/// Model representing a cryptocurrency payment method (Bitcoin, Ethereum)
class CryptoPaymentMethod extends PaymentMethod {
  /// The wallet address for the cryptocurrency
  final String walletAddress;

  /// Creates a new cryptocurrency payment method
  const CryptoPaymentMethod({
    required super.id,
    required super.type,
    required super.userId,
    required this.walletAddress,
    super.isDefault,
    required super.createdAt,
    required super.updatedAt,
  }) : assert(
          type == PaymentMethodType.bitcoin ||
              type == PaymentMethodType.ethereum,
          'Crypto type must be bitcoin or ethereum',
        );

  /// Creates a cryptocurrency payment method from a JSON map
  factory CryptoPaymentMethod.fromJson(Map<String, dynamic> json) {
    final typeStr = json['type'] as String;
    PaymentMethodType type;

    switch (typeStr) {
      case 'bitcoin':
        type = PaymentMethodType.bitcoin;
        break;
      case 'ethereum':
        type = PaymentMethodType.ethereum;
        break;
      default:
        throw ArgumentError('Invalid cryptocurrency type: $typeStr');
    }

    return CryptoPaymentMethod(
      id: json['id'] as String,
      type: type,
      userId: json['userId'] as String,
      walletAddress: json['walletAddress'] as String,
      isDefault: json['isDefault'] as bool? ?? false,
      createdAt: (json['createdAt'] is Timestamp)
          ? (json['createdAt'] as Timestamp).toDate()
          : DateTime.parse(json['createdAt'] as String),
      updatedAt: (json['updatedAt'] is Timestamp)
          ? (json['updatedAt'] as Timestamp).toDate()
          : DateTime.parse(json['updatedAt'] as String),
    );
  }

  @override
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type.toString().split('.').last,
      'userId': userId,
      'walletAddress': walletAddress,
      'isDefault': isDefault,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  @override
  String getMaskedDetails() {
    // Mask the wallet address for display
    if (walletAddress.length <= 10) return walletAddress;

    final start = walletAddress.substring(0, 6);
    final end = walletAddress.substring(walletAddress.length - 4);

    return '$start....$end';
  }

  /// Creates a copy of this cryptocurrency payment method with the given fields replaced with new values
  CryptoPaymentMethod copyWith({
    String? id,
    PaymentMethodType? type,
    String? userId,
    String? walletAddress,
    bool? isDefault,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return CryptoPaymentMethod(
      id: id ?? this.id,
      type: type ?? this.type,
      userId: userId ?? this.userId,
      walletAddress: walletAddress ?? this.walletAddress,
      isDefault: isDefault ?? this.isDefault,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// Validates a Bitcoin wallet address
  static bool validateBitcoinAddress(String address) {
    // Basic validation for Bitcoin addresses
    final bitcoinRegex = RegExp(
      r'^(bc1|[13])[a-zA-HJ-NP-Z0-9]{25,39}$',
    );
    return bitcoinRegex.hasMatch(address);
  }

  /// Validates an Ethereum wallet address
  static bool validateEthereumAddress(String address) {
    // Basic validation for Ethereum addresses
    final ethereumRegex = RegExp(
      r'^0x[a-fA-F0-9]{40}$',
    );
    return ethereumRegex.hasMatch(address);
  }

  /// Validates a cryptocurrency wallet address based on the type
  static bool validateWalletAddress(String address, PaymentMethodType type) {
    switch (type) {
      case PaymentMethodType.bitcoin:
        return validateBitcoinAddress(address);
      case PaymentMethodType.ethereum:
        return validateEthereumAddress(address);
      default:
        return false;
    }
  }
}
