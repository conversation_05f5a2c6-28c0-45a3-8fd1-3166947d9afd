import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:culture_connect/models/payment/payment_method.dart';

/// Enum representing different transaction statuses
enum TransactionStatus {
  /// Transaction is pending
  pending,
  
  /// Transaction is completed successfully
  completed,
  
  /// Transaction failed
  failed,
  
  /// Transaction is being processed
  processing,
  
  /// Transaction was refunded
  refunded,
  
  /// Transaction was partially refunded
  partiallyRefunded,
  
  /// Transaction was disputed
  disputed,
  
  /// Transaction was canceled
  canceled,
}

/// Extension to provide display names for transaction statuses
extension TransactionStatusExtension on TransactionStatus {
  /// Gets the display name for this transaction status
  String get displayName {
    switch (this) {
      case TransactionStatus.pending:
        return 'Pending';
      case TransactionStatus.completed:
        return 'Completed';
      case TransactionStatus.failed:
        return 'Failed';
      case TransactionStatus.processing:
        return 'Processing';
      case TransactionStatus.refunded:
        return 'Refunded';
      case TransactionStatus.partiallyRefunded:
        return 'Partially Refunded';
      case TransactionStatus.disputed:
        return 'Disputed';
      case TransactionStatus.canceled:
        return 'Canceled';
    }
  }
  
  /// Gets the color name for this transaction status
  String get colorName {
    switch (this) {
      case TransactionStatus.pending:
        return 'orange';
      case TransactionStatus.completed:
        return 'green';
      case TransactionStatus.failed:
        return 'red';
      case TransactionStatus.processing:
        return 'blue';
      case TransactionStatus.refunded:
        return 'purple';
      case TransactionStatus.partiallyRefunded:
        return 'purple';
      case TransactionStatus.disputed:
        return 'red';
      case TransactionStatus.canceled:
        return 'grey';
    }
  }
}

/// Model representing a payment transaction
class Transaction {
  /// Unique identifier for the transaction
  final String id;
  
  /// User ID who made the transaction
  final String userId;
  
  /// Experience ID associated with the transaction
  final String experienceId;
  
  /// Booking ID associated with the transaction
  final String bookingId;
  
  /// Amount of the transaction
  final double amount;
  
  /// Currency of the transaction (e.g., USD, EUR)
  final String currency;
  
  /// Status of the transaction
  final TransactionStatus status;
  
  /// Type of payment method used
  final PaymentMethodType paymentMethodType;
  
  /// ID of the payment method used
  final String paymentMethodId;
  
  /// Description of the transaction
  final String description;
  
  /// Error message if the transaction failed
  final String? errorMessage;
  
  /// Transaction reference ID from the payment processor
  final String? processorTransactionId;
  
  /// When the transaction was created
  final DateTime createdAt;
  
  /// When the transaction was last updated
  final DateTime updatedAt;
  
  /// Creates a new transaction
  const Transaction({
    required this.id,
    required this.userId,
    required this.experienceId,
    required this.bookingId,
    required this.amount,
    required this.currency,
    required this.status,
    required this.paymentMethodType,
    required this.paymentMethodId,
    required this.description,
    this.errorMessage,
    this.processorTransactionId,
    required this.createdAt,
    required this.updatedAt,
  });
  
  /// Creates a transaction from a JSON map
  factory Transaction.fromJson(Map<String, dynamic> json) {
    final statusStr = json['status'] as String;
    final paymentMethodTypeStr = json['paymentMethodType'] as String;
    
    return Transaction(
      id: json['id'] as String,
      userId: json['userId'] as String,
      experienceId: json['experienceId'] as String,
      bookingId: json['bookingId'] as String,
      amount: (json['amount'] as num).toDouble(),
      currency: json['currency'] as String,
      status: TransactionStatus.values.firstWhere(
        (e) => e.toString().split('.').last == statusStr,
        orElse: () => TransactionStatus.pending,
      ),
      paymentMethodType: PaymentMethodType.values.firstWhere(
        (e) => e.toString().split('.').last == paymentMethodTypeStr,
        orElse: () => PaymentMethodType.creditCard,
      ),
      paymentMethodId: json['paymentMethodId'] as String,
      description: json['description'] as String,
      errorMessage: json['errorMessage'] as String?,
      processorTransactionId: json['processorTransactionId'] as String?,
      createdAt: (json['createdAt'] is Timestamp)
          ? (json['createdAt'] as Timestamp).toDate()
          : DateTime.parse(json['createdAt'] as String),
      updatedAt: (json['updatedAt'] is Timestamp)
          ? (json['updatedAt'] as Timestamp).toDate()
          : DateTime.parse(json['updatedAt'] as String),
    );
  }
  
  /// Converts this transaction to a JSON map
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userId': userId,
      'experienceId': experienceId,
      'bookingId': bookingId,
      'amount': amount,
      'currency': currency,
      'status': status.toString().split('.').last,
      'paymentMethodType': paymentMethodType.toString().split('.').last,
      'paymentMethodId': paymentMethodId,
      'description': description,
      'errorMessage': errorMessage,
      'processorTransactionId': processorTransactionId,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }
  
  /// Creates a copy of this transaction with the given fields replaced with new values
  Transaction copyWith({
    String? id,
    String? userId,
    String? experienceId,
    String? bookingId,
    double? amount,
    String? currency,
    TransactionStatus? status,
    PaymentMethodType? paymentMethodType,
    String? paymentMethodId,
    String? description,
    String? errorMessage,
    String? processorTransactionId,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Transaction(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      experienceId: experienceId ?? this.experienceId,
      bookingId: bookingId ?? this.bookingId,
      amount: amount ?? this.amount,
      currency: currency ?? this.currency,
      status: status ?? this.status,
      paymentMethodType: paymentMethodType ?? this.paymentMethodType,
      paymentMethodId: paymentMethodId ?? this.paymentMethodId,
      description: description ?? this.description,
      errorMessage: errorMessage ?? this.errorMessage,
      processorTransactionId: processorTransactionId ?? this.processorTransactionId,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
  
  /// Formats the amount with the currency symbol
  String getFormattedAmount() {
    String symbol;
    switch (currency.toUpperCase()) {
      case 'USD':
        symbol = '\$';
        break;
      case 'EUR':
        symbol = '€';
        break;
      case 'GBP':
        symbol = '£';
        break;
      default:
        symbol = currency;
    }
    
    return '$symbol${amount.toStringAsFixed(2)}';
  }
}
