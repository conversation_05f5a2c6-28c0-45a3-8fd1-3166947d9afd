import 'package:flutter/material.dart';
import 'package:culture_connect/models/offline/sync_status.dart';
import 'package:culture_connect/models/offline/content_priority.dart';
import 'package:culture_connect/models/offline/content_sync_policy.dart';
import 'package:culture_connect/models/offline/content_conflict_resolution.dart';

/// A class representing content that can be stored offline
class OfflineContent {
  /// The unique identifier for the content
  final String id;

  /// The title of the content
  final String title;

  /// The type of content (e.g., 'itinerary', 'experience', 'hotel')
  final String contentType;

  /// The size of the content in bytes
  final int? contentSize;

  /// The current sync status
  final SyncStatus syncStatus;

  /// The time when the content was last successfully synced
  final DateTime? lastSyncTime;

  /// The time when the last sync attempt was made
  final DateTime? lastSyncAttempt;

  /// The error message if the last sync attempt failed
  final String? errorMessage;

  /// The expiration time for the offline content
  final DateTime? expirationTime;

  /// Additional metadata for the content
  final Map<String, dynamic>? metadata;

  /// The priority of the content
  final ContentPriority priority;

  /// The sync policy for the content
  final ContentSyncPolicy syncPolicy;

  /// The conflict resolution strategy for the content
  final ContentConflictResolution conflictResolution;

  /// The version of the content
  final int version;

  /// The time when the content was last modified
  final DateTime? lastModifiedTime;

  /// The dependencies of this content (IDs of other content items)
  final List<String>? dependencies;

  /// The bandwidth used to download this content (in bytes)
  final int? bandwidthUsed;

  /// The estimated bandwidth required to sync this content (in bytes)
  final int? estimatedBandwidth;

  /// Creates a new offline content
  const OfflineContent({
    required this.id,
    required this.title,
    required this.contentType,
    this.contentSize,
    this.syncStatus = SyncStatus.notSynced,
    this.lastSyncTime,
    this.lastSyncAttempt,
    this.errorMessage,
    this.expirationTime,
    this.metadata,
    this.priority = ContentPriority.medium,
    this.syncPolicy = ContentSyncPolicy.autoWifi,
    this.conflictResolution = ContentConflictResolution.serverWins,
    this.version = 1,
    this.lastModifiedTime,
    this.dependencies,
    this.bandwidthUsed,
    this.estimatedBandwidth,
  });

  /// Create a copy of this offline content with the given fields replaced
  OfflineContent copyWith({
    String? id,
    String? title,
    String? contentType,
    int? contentSize,
    SyncStatus? syncStatus,
    DateTime? lastSyncTime,
    DateTime? lastSyncAttempt,
    String? errorMessage,
    DateTime? expirationTime,
    Map<String, dynamic>? metadata,
    ContentPriority? priority,
    ContentSyncPolicy? syncPolicy,
    ContentConflictResolution? conflictResolution,
    int? version,
    DateTime? lastModifiedTime,
    List<String>? dependencies,
    int? bandwidthUsed,
    int? estimatedBandwidth,
  }) {
    return OfflineContent(
      id: id ?? this.id,
      title: title ?? this.title,
      contentType: contentType ?? this.contentType,
      contentSize: contentSize ?? this.contentSize,
      syncStatus: syncStatus ?? this.syncStatus,
      lastSyncTime: lastSyncTime ?? this.lastSyncTime,
      lastSyncAttempt: lastSyncAttempt ?? this.lastSyncAttempt,
      errorMessage: errorMessage ?? this.errorMessage,
      expirationTime: expirationTime ?? this.expirationTime,
      metadata: metadata ?? this.metadata,
      priority: priority ?? this.priority,
      syncPolicy: syncPolicy ?? this.syncPolicy,
      conflictResolution: conflictResolution ?? this.conflictResolution,
      version: version ?? this.version,
      lastModifiedTime: lastModifiedTime ?? this.lastModifiedTime,
      dependencies: dependencies ?? this.dependencies,
      bandwidthUsed: bandwidthUsed ?? this.bandwidthUsed,
      estimatedBandwidth: estimatedBandwidth ?? this.estimatedBandwidth,
    );
  }

  /// Create an offline content from JSON
  factory OfflineContent.fromJson(Map<String, dynamic> json) {
    return OfflineContent(
      id: json['id'],
      title: json['title'],
      contentType: json['contentType'],
      contentSize: json['contentSize'],
      syncStatus: SyncStatus.values.firstWhere(
        (status) => status.name == json['syncStatus'],
        orElse: () => SyncStatus.notSynced,
      ),
      lastSyncTime: json['lastSyncTime'] != null
          ? DateTime.parse(json['lastSyncTime'])
          : null,
      lastSyncAttempt: json['lastSyncAttempt'] != null
          ? DateTime.parse(json['lastSyncAttempt'])
          : null,
      errorMessage: json['errorMessage'],
      expirationTime: json['expirationTime'] != null
          ? DateTime.parse(json['expirationTime'])
          : null,
      metadata: json['metadata'] != null
          ? Map<String, dynamic>.from(json['metadata'])
          : null,
      priority: json['priority'] != null
          ? ContentPriority.values.firstWhere(
              (priority) => priority.name == json['priority'],
              orElse: () => ContentPriority.medium,
            )
          : ContentPriority.medium,
      syncPolicy: json['syncPolicy'] != null
          ? ContentSyncPolicy.values.firstWhere(
              (policy) => policy.name == json['syncPolicy'],
              orElse: () => ContentSyncPolicy.autoWifi,
            )
          : ContentSyncPolicy.autoWifi,
      conflictResolution: json['conflictResolution'] != null
          ? ContentConflictResolution.values.firstWhere(
              (resolution) => resolution.name == json['conflictResolution'],
              orElse: () => ContentConflictResolution.serverWins,
            )
          : ContentConflictResolution.serverWins,
      version: json['version'] ?? 1,
      lastModifiedTime: json['lastModifiedTime'] != null
          ? DateTime.parse(json['lastModifiedTime'])
          : null,
      dependencies: json['dependencies'] != null
          ? List<String>.from(json['dependencies'])
          : null,
      bandwidthUsed: json['bandwidthUsed'],
      estimatedBandwidth: json['estimatedBandwidth'],
    );
  }

  /// Convert this offline content to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'contentType': contentType,
      'contentSize': contentSize,
      'syncStatus': syncStatus.name,
      'lastSyncTime': lastSyncTime?.toIso8601String(),
      'lastSyncAttempt': lastSyncAttempt?.toIso8601String(),
      'errorMessage': errorMessage,
      'expirationTime': expirationTime?.toIso8601String(),
      'metadata': metadata,
      'priority': priority.name,
      'syncPolicy': syncPolicy.name,
      'conflictResolution': conflictResolution.name,
      'version': version,
      'lastModifiedTime': lastModifiedTime?.toIso8601String(),
      'dependencies': dependencies,
      'bandwidthUsed': bandwidthUsed,
      'estimatedBandwidth': estimatedBandwidth,
    };
  }

  /// Check if the content is expired
  bool get isExpired {
    if (expirationTime == null) {
      return false;
    }

    return DateTime.now().isAfter(expirationTime!);
  }

  /// Get the formatted content size
  String get formattedSize {
    if (contentSize == null) {
      return 'Unknown size';
    }

    if (contentSize! < 1024) {
      return '$contentSize B';
    } else if (contentSize! < 1024 * 1024) {
      final kb = contentSize! / 1024;
      return '${kb.toStringAsFixed(1)} KB';
    } else if (contentSize! < 1024 * 1024 * 1024) {
      final mb = contentSize! / (1024 * 1024);
      return '${mb.toStringAsFixed(1)} MB';
    } else {
      final gb = contentSize! / (1024 * 1024 * 1024);
      return '${gb.toStringAsFixed(1)} GB';
    }
  }

  /// Get the formatted last sync time
  String get formattedLastSyncTime {
    if (lastSyncTime == null) {
      return 'Never';
    }

    final now = DateTime.now();
    final difference = now.difference(lastSyncTime!);

    if (difference.inSeconds < 60) {
      return 'Just now';
    } else if (difference.inMinutes < 60) {
      return '${difference.inMinutes} ${difference.inMinutes == 1 ? 'minute' : 'minutes'} ago';
    } else if (difference.inHours < 24) {
      return '${difference.inHours} ${difference.inHours == 1 ? 'hour' : 'hours'} ago';
    } else if (difference.inDays < 30) {
      return '${difference.inDays} ${difference.inDays == 1 ? 'day' : 'days'} ago';
    } else {
      return '${lastSyncTime!.year}-${lastSyncTime!.month.toString().padLeft(2, '0')}-${lastSyncTime!.day.toString().padLeft(2, '0')}';
    }
  }

  /// Get the formatted expiration time
  String get formattedExpirationTime {
    if (expirationTime == null) {
      return 'Never expires';
    }

    final now = DateTime.now();
    final difference = expirationTime!.difference(now);

    if (difference.isNegative) {
      return 'Expired';
    } else if (difference.inDays < 1) {
      if (difference.inHours < 1) {
        return 'Expires in ${difference.inMinutes} ${difference.inMinutes == 1 ? 'minute' : 'minutes'}';
      } else {
        return 'Expires in ${difference.inHours} ${difference.inHours == 1 ? 'hour' : 'hours'}';
      }
    } else if (difference.inDays < 30) {
      return 'Expires in ${difference.inDays} ${difference.inDays == 1 ? 'day' : 'days'}';
    } else {
      return 'Expires on ${expirationTime!.year}-${expirationTime!.month.toString().padLeft(2, '0')}-${expirationTime!.day.toString().padLeft(2, '0')}';
    }
  }

  /// Get the icon name for the content type
  String get contentTypeIcon {
    switch (contentType) {
      case 'itinerary':
        return 'map';
      case 'experience':
        return 'attractions';
      case 'hotel':
        return 'hotel';
      case 'flight':
        return 'flight';
      case 'restaurant':
        return 'restaurant';
      case 'car_rental':
        return 'directions_car';
      case 'cruise':
        return 'directions_boat';
      default:
        return 'file_copy';
    }
  }

  /// Get the display name for the content type
  String get contentTypeDisplayName {
    switch (contentType) {
      case 'itinerary':
        return 'Itinerary';
      case 'experience':
        return 'Experience';
      case 'hotel':
        return 'Hotel';
      case 'flight':
        return 'Flight';
      case 'restaurant':
        return 'Restaurant';
      case 'car_rental':
        return 'Car Rental';
      case 'cruise':
        return 'Cruise';
      case 'ar_content':
        return 'AR Content';
      case 'timeline':
        return 'Timeline';
      default:
        return contentType;
    }
  }

  /// Get the formatted bandwidth used
  String get formattedBandwidthUsed {
    if (bandwidthUsed == null) {
      return 'Unknown';
    }

    return _formatBytes(bandwidthUsed!);
  }

  /// Get the formatted estimated bandwidth
  String get formattedEstimatedBandwidth {
    if (estimatedBandwidth == null) {
      return 'Unknown';
    }

    return _formatBytes(estimatedBandwidth!);
  }

  /// Format bytes to a human-readable string
  String _formatBytes(int bytes) {
    if (bytes < 1024) {
      return '$bytes B';
    } else if (bytes < 1024 * 1024) {
      final kb = bytes / 1024;
      return '${kb.toStringAsFixed(1)} KB';
    } else if (bytes < 1024 * 1024 * 1024) {
      final mb = bytes / (1024 * 1024);
      return '${mb.toStringAsFixed(1)} MB';
    } else {
      final gb = bytes / (1024 * 1024 * 1024);
      return '${gb.toStringAsFixed(1)} GB';
    }
  }

  /// Get the formatted last modified time
  String get formattedLastModifiedTime {
    if (lastModifiedTime == null) {
      return 'Never';
    }

    final now = DateTime.now();
    final difference = now.difference(lastModifiedTime!);

    if (difference.inSeconds < 60) {
      return 'Just now';
    } else if (difference.inMinutes < 60) {
      return '${difference.inMinutes} ${difference.inMinutes == 1 ? 'minute' : 'minutes'} ago';
    } else if (difference.inHours < 24) {
      return '${difference.inHours} ${difference.inHours == 1 ? 'hour' : 'hours'} ago';
    } else if (difference.inDays < 30) {
      return '${difference.inDays} ${difference.inDays == 1 ? 'day' : 'days'} ago';
    } else {
      return '${lastModifiedTime!.year}-${lastModifiedTime!.month.toString().padLeft(2, '0')}-${lastModifiedTime!.day.toString().padLeft(2, '0')}';
    }
  }

  /// Check if this content has conflicts
  bool get hasConflicts {
    return metadata != null &&
        metadata!.containsKey('conflicts') &&
        metadata!['conflicts'] == true;
  }

  /// Check if this content is being edited offline
  bool get isBeingEditedOffline {
    return metadata != null &&
        metadata!.containsKey('offlineEdits') &&
        metadata!['offlineEdits'] == true;
  }

  /// Check if this content has dependencies
  bool get hasDependencies {
    return dependencies != null && dependencies!.isNotEmpty;
  }

  /// Check if this content is a dependency of other content
  bool isDependencyOf(List<OfflineContent> allContent) {
    return allContent.any((content) =>
        content.dependencies != null && content.dependencies!.contains(id));
  }

  /// Get the color for the content priority
  Color get priorityColor {
    return priority.color;
  }

  /// Get the icon for the content priority
  IconData get priorityIcon {
    return priority.icon;
  }

  /// Get the display name for the content priority
  String get priorityDisplayName {
    return priority.displayName;
  }

  /// Get the color for the sync policy
  Color get syncPolicyColor {
    return syncPolicy.color;
  }

  /// Get the icon for the sync policy
  IconData get syncPolicyIcon {
    return syncPolicy.icon;
  }

  /// Get the display name for the sync policy
  String get syncPolicyDisplayName {
    return syncPolicy.displayName;
  }

  /// Get the color for the conflict resolution
  Color get conflictResolutionColor {
    return conflictResolution.color;
  }

  /// Get the icon for the conflict resolution
  IconData get conflictResolutionIcon {
    return conflictResolution.icon;
  }

  /// Get the display name for the conflict resolution
  String get conflictResolutionDisplayName {
    return conflictResolution.displayName;
  }
}
