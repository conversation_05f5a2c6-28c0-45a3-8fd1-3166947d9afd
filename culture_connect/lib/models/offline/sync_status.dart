import 'package:flutter/material.dart';

/// Status of content synchronization
enum SyncStatus {
  /// Content is not synced for offline use
  notSynced,

  /// Content is pending synchronization
  pending,

  /// Content is currently being synchronized
  syncing,

  /// Content is synchronized and available offline
  synced,

  /// Synchronization failed
  failed,
}

/// Extension for sync status
extension SyncStatusExtension on SyncStatus {
  /// Get the name of the sync status
  String get name {
    switch (this) {
      case SyncStatus.notSynced:
        return 'notSynced';
      case SyncStatus.pending:
        return 'pending';
      case SyncStatus.syncing:
        return 'syncing';
      case SyncStatus.synced:
        return 'synced';
      case SyncStatus.failed:
        return 'failed';
    }
  }

  /// Get a sync status from a string
  static SyncStatus fromString(String value) {
    switch (value) {
      case 'notSynced':
        return SyncStatus.notSynced;
      case 'pending':
        return SyncStatus.pending;
      case 'syncing':
        return SyncStatus.syncing;
      case 'synced':
        return SyncStatus.synced;
      case 'failed':
        return SyncStatus.failed;
      default:
        return SyncStatus.notSynced;
    }
  }

  /// Get the display name for the sync status
  String get displayName {
    switch (this) {
      case SyncStatus.notSynced:
        return 'Not Available Offline';
      case SyncStatus.pending:
        return 'Pending Download';
      case SyncStatus.syncing:
        return 'Downloading...';
      case SyncStatus.synced:
        return 'Available Offline';
      case SyncStatus.failed:
        return 'Download Failed';
    }
  }

  /// Get the icon for the sync status
  IconData get icon {
    switch (this) {
      case SyncStatus.notSynced:
        return Icons.cloud_outlined;
      case SyncStatus.pending:
        return Icons.pending_outlined;
      case SyncStatus.syncing:
        return Icons.sync;
      case SyncStatus.synced:
        return Icons.offline_pin;
      case SyncStatus.failed:
        return Icons.error_outline;
    }
  }

  /// Get the color for the sync status
  Color get color {
    switch (this) {
      case SyncStatus.notSynced:
        return Colors.grey;
      case SyncStatus.pending:
        return Colors.orange;
      case SyncStatus.syncing:
        return Colors.blue;
      case SyncStatus.synced:
        return Colors.green;
      case SyncStatus.failed:
        return Colors.red;
    }
  }

  /// Get the background color for the sync status
  Color get backgroundColor {
    return color.withAlpha(25); // Approximately 0.1 opacity
  }

  /// Get the border color for the sync status
  Color get borderColor {
    return color.withAlpha(128); // Approximately 0.5 opacity
  }

  /// Check if the status is a loading state
  bool get isLoading {
    return this == SyncStatus.pending || this == SyncStatus.syncing;
  }

  /// Check if the status is an error state
  bool get isError {
    return this == SyncStatus.failed;
  }

  /// Check if the content is available offline
  bool get isAvailableOffline {
    return this == SyncStatus.synced;
  }
}

/// A class representing a sync status update
class SyncStatusUpdate {
  /// The ID of the content being updated
  final String contentId;

  /// The new sync status
  final SyncStatus status;

  /// A message describing the update
  final String message;

  /// Progress of the sync (0.0 to 1.0)
  final double? progress;

  /// Creates a new sync status update
  const SyncStatusUpdate({
    required this.contentId,
    required this.status,
    required this.message,
    this.progress,
  });
}
