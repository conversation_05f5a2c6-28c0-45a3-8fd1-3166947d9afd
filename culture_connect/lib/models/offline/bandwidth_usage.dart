import 'package:flutter/material.dart';

/// Network type for bandwidth usage
enum NetworkType {
  /// WiFi network
  wifi,

  /// Mobile network
  mobile,

  /// Unknown network type
  unknown,
}

/// Extension methods for NetworkType
extension NetworkTypeExtension on NetworkType {
  /// Get the display name for the network type
  String get displayName {
    switch (this) {
      case NetworkType.wifi:
        return 'WiFi';
      case NetworkType.mobile:
        return 'Mobile Data';
      case NetworkType.unknown:
        return 'Unknown';
    }
  }

  /// Get the icon for the network type
  IconData get icon {
    switch (this) {
      case NetworkType.wifi:
        return Icons.wifi;
      case NetworkType.mobile:
        return Icons.network_cell;
      case NetworkType.unknown:
        return Icons.device_unknown;
    }
  }

  /// Get the color for the network type
  Color get color {
    switch (this) {
      case NetworkType.wifi:
        return Colors.blue;
      case NetworkType.mobile:
        return Colors.orange;
      case NetworkType.unknown:
        return Colors.grey;
    }
  }
}

/// A class representing bandwidth usage
class BandwidthUsage {
  /// The unique identifier
  final String id;

  /// The date of the usage
  final DateTime date;

  /// The network type
  final NetworkType networkType;

  /// The content type
  final String contentType;

  /// The content ID
  final String? contentId;

  /// The bytes downloaded
  final int bytesDownloaded;

  /// The bytes uploaded
  final int bytesUploaded;

  /// Creates a new bandwidth usage
  const BandwidthUsage({
    required this.id,
    required this.date,
    required this.networkType,
    required this.contentType,
    this.contentId,
    required this.bytesDownloaded,
    required this.bytesUploaded,
  });

  /// Get the total bytes
  int get totalBytes => bytesDownloaded + bytesUploaded;

  /// Get the formatted bytes downloaded
  String get formattedBytesDownloaded => _formatBytes(bytesDownloaded);

  /// Get the formatted bytes uploaded
  String get formattedBytesUploaded => _formatBytes(bytesUploaded);

  /// Get the formatted total bytes
  String get formattedTotalBytes => _formatBytes(totalBytes);

  /// Get the download size in MB
  double get downloadMB => bytesDownloaded / (1024 * 1024);

  /// Get the upload size in MB
  double get uploadMB => bytesUploaded / (1024 * 1024);

  /// Get the total size in MB
  double get totalMB => totalBytes / (1024 * 1024);

  /// Format bytes to a human-readable string
  String _formatBytes(int bytes) {
    if (bytes < 1024) {
      return '$bytes B';
    } else if (bytes < 1024 * 1024) {
      final kb = bytes / 1024;
      return '${kb.toStringAsFixed(1)} KB';
    } else if (bytes < 1024 * 1024 * 1024) {
      final mb = bytes / (1024 * 1024);
      return '${mb.toStringAsFixed(1)} MB';
    } else {
      final gb = bytes / (1024 * 1024 * 1024);
      return '${gb.toStringAsFixed(1)} GB';
    }
  }

  /// Create a copy of this bandwidth usage with the given fields replaced
  BandwidthUsage copyWith({
    String? id,
    DateTime? date,
    NetworkType? networkType,
    String? contentType,
    String? contentId,
    int? bytesDownloaded,
    int? bytesUploaded,
  }) {
    return BandwidthUsage(
      id: id ?? this.id,
      date: date ?? this.date,
      networkType: networkType ?? this.networkType,
      contentType: contentType ?? this.contentType,
      contentId: contentId ?? this.contentId,
      bytesDownloaded: bytesDownloaded ?? this.bytesDownloaded,
      bytesUploaded: bytesUploaded ?? this.bytesUploaded,
    );
  }

  /// Create a bandwidth usage from JSON
  factory BandwidthUsage.fromJson(Map<String, dynamic> json) {
    return BandwidthUsage(
      id: json['id'],
      date: DateTime.parse(json['date']),
      networkType: NetworkType.values.firstWhere(
        (type) => type.name == json['networkType'],
        orElse: () => NetworkType.unknown,
      ),
      contentType: json['contentType'],
      contentId: json['contentId'],
      bytesDownloaded: json['bytesDownloaded'],
      bytesUploaded: json['bytesUploaded'],
    );
  }

  /// Convert this bandwidth usage to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'date': date.toIso8601String(),
      'networkType': networkType.name,
      'contentType': contentType,
      'contentId': contentId,
      'bytesDownloaded': bytesDownloaded,
      'bytesUploaded': bytesUploaded,
    };
  }
}

/// A class representing daily bandwidth usage
class DailyBandwidthUsage {
  /// The date
  final DateTime date;

  /// The usage by network type
  final Map<NetworkType, int> usageByNetworkType;

  /// The usage by content type
  final Map<String, int> usageByContentType;

  /// The total bytes downloaded
  final int totalBytesDownloaded;

  /// The total bytes uploaded
  final int totalBytesUploaded;

  /// Creates a new daily bandwidth usage
  const DailyBandwidthUsage({
    required this.date,
    required this.usageByNetworkType,
    required this.usageByContentType,
    required this.totalBytesDownloaded,
    required this.totalBytesUploaded,
  });

  /// Get the total bytes
  int get totalBytes => totalBytesDownloaded + totalBytesUploaded;

  /// Get the formatted total bytes
  String get formattedTotalBytes => _formatBytes(totalBytes);

  /// Get the formatted total bytes downloaded
  String get formattedTotalBytesDownloaded =>
      _formatBytes(totalBytesDownloaded);

  /// Get the formatted total bytes uploaded
  String get formattedTotalBytesUploaded => _formatBytes(totalBytesUploaded);

  /// Get the download size in MB
  double get downloadMB => totalBytesDownloaded / (1024 * 1024);

  /// Get the upload size in MB
  double get uploadMB => totalBytesUploaded / (1024 * 1024);

  /// Get the total size in MB
  double get totalMB => totalBytes / (1024 * 1024);

  /// Format bytes to a human-readable string
  String _formatBytes(int bytes) {
    if (bytes < 1024) {
      return '$bytes B';
    } else if (bytes < 1024 * 1024) {
      final kb = bytes / 1024;
      return '${kb.toStringAsFixed(1)} KB';
    } else if (bytes < 1024 * 1024 * 1024) {
      final mb = bytes / (1024 * 1024);
      return '${mb.toStringAsFixed(1)} MB';
    } else {
      final gb = bytes / (1024 * 1024 * 1024);
      return '${gb.toStringAsFixed(1)} GB';
    }
  }

  /// Create a daily bandwidth usage from a list of bandwidth usages
  factory DailyBandwidthUsage.fromUsages(
      List<BandwidthUsage> usages, DateTime date) {
    // Filter usages for the given date
    final filteredUsages = usages.where((usage) {
      return usage.date.year == date.year &&
          usage.date.month == date.month &&
          usage.date.day == date.day;
    }).toList();

    // Calculate usage by network type
    final usageByNetworkType = <NetworkType, int>{};
    for (final usage in filteredUsages) {
      usageByNetworkType[usage.networkType] =
          (usageByNetworkType[usage.networkType] ?? 0) + usage.totalBytes;
    }

    // Calculate usage by content type
    final usageByContentType = <String, int>{};
    for (final usage in filteredUsages) {
      usageByContentType[usage.contentType] =
          (usageByContentType[usage.contentType] ?? 0) + usage.totalBytes;
    }

    // Calculate total bytes
    int totalBytesDownloaded = 0;
    int totalBytesUploaded = 0;
    for (final usage in filteredUsages) {
      totalBytesDownloaded += usage.bytesDownloaded;
      totalBytesUploaded += usage.bytesUploaded;
    }

    return DailyBandwidthUsage(
      date: date,
      usageByNetworkType: usageByNetworkType,
      usageByContentType: usageByContentType,
      totalBytesDownloaded: totalBytesDownloaded,
      totalBytesUploaded: totalBytesUploaded,
    );
  }
}

/// A class representing bandwidth settings
class BandwidthSettings {
  /// Whether to sync on WiFi only
  final bool syncOnWifiOnly;

  /// Whether to sync automatically
  final bool syncAutomatically;

  /// The maximum mobile data usage per day (in bytes)
  final int maxMobileDataUsagePerDay;

  /// The maximum WiFi data usage per day (in bytes)
  final int maxWifiDataUsagePerDay;

  /// Creates new bandwidth settings
  const BandwidthSettings({
    this.syncOnWifiOnly = true,
    this.syncAutomatically = true,
    this.maxMobileDataUsagePerDay = 10 * 1024 * 1024, // 10 MB
    this.maxWifiDataUsagePerDay = 100 * 1024 * 1024, // 100 MB
  });

  /// Creates bandwidth settings from JSON
  factory BandwidthSettings.fromJson(Map<String, dynamic> json) {
    return BandwidthSettings(
      syncOnWifiOnly: json['syncOnWifiOnly'] as bool? ?? true,
      syncAutomatically: json['syncAutomatically'] as bool? ?? true,
      maxMobileDataUsagePerDay:
          json['maxMobileDataUsagePerDay'] as int? ?? 10 * 1024 * 1024,
      maxWifiDataUsagePerDay:
          json['maxWifiDataUsagePerDay'] as int? ?? 100 * 1024 * 1024,
    );
  }

  /// Converts the bandwidth settings to JSON
  Map<String, dynamic> toJson() {
    return {
      'syncOnWifiOnly': syncOnWifiOnly,
      'syncAutomatically': syncAutomatically,
      'maxMobileDataUsagePerDay': maxMobileDataUsagePerDay,
      'maxWifiDataUsagePerDay': maxWifiDataUsagePerDay,
    };
  }

  /// Creates a copy of the bandwidth settings with updated values
  BandwidthSettings copyWith({
    bool? syncOnWifiOnly,
    bool? syncAutomatically,
    int? maxMobileDataUsagePerDay,
    int? maxWifiDataUsagePerDay,
  }) {
    return BandwidthSettings(
      syncOnWifiOnly: syncOnWifiOnly ?? this.syncOnWifiOnly,
      syncAutomatically: syncAutomatically ?? this.syncAutomatically,
      maxMobileDataUsagePerDay:
          maxMobileDataUsagePerDay ?? this.maxMobileDataUsagePerDay,
      maxWifiDataUsagePerDay:
          maxWifiDataUsagePerDay ?? this.maxWifiDataUsagePerDay,
    );
  }
}
