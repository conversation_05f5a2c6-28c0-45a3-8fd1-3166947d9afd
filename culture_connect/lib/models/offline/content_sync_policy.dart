import 'package:flutter/material.dart';

/// Sync policy for offline content
enum ContentSyncPolicy {
  /// Manual sync only
  manual,

  /// Automatic sync on WiFi only
  autoWifi,

  /// Automatic sync on any network
  autoAny,

  /// Scheduled sync
  scheduled,
}

/// Extension methods for ContentSyncPolicy
extension ContentSyncPolicyExtension on ContentSyncPolicy {
  /// Get the display name for the sync policy
  String get displayName {
    switch (this) {
      case ContentSyncPolicy.manual:
        return 'Manual';
      case ContentSyncPolicy.autoWifi:
        return 'Auto (WiFi Only)';
      case ContentSyncPolicy.autoAny:
        return 'Auto (Any Network)';
      case ContentSyncPolicy.scheduled:
        return 'Scheduled';
    }
  }

  /// Get the description for the sync policy
  String get description {
    switch (this) {
      case ContentSyncPolicy.manual:
        return 'Only sync when manually requested';
      case ContentSyncPolicy.autoWifi:
        return 'Automatically sync when connected to WiFi';
      case ContentSyncPolicy.autoAny:
        return 'Automatically sync on any network connection';
      case ContentSyncPolicy.scheduled:
        return 'Sync according to a schedule';
    }
  }

  /// Get the icon for the sync policy
  IconData get icon {
    switch (this) {
      case ContentSyncPolicy.manual:
        return Icons.sync;
      case ContentSyncPolicy.autoWifi:
        return Icons.wifi;
      case ContentSyncPolicy.autoAny:
        return Icons.network_cell;
      case ContentSyncPolicy.scheduled:
        return Icons.schedule;
    }
  }

  /// Get the color for the sync policy
  Color get color {
    switch (this) {
      case ContentSyncPolicy.manual:
        return Colors.blue;
      case ContentSyncPolicy.autoWifi:
        return Colors.green;
      case ContentSyncPolicy.autoAny:
        return Colors.orange;
      case ContentSyncPolicy.scheduled:
        return Colors.purple;
    }
  }

  /// Check if this policy allows automatic syncing
  bool get isAutomatic {
    return this == ContentSyncPolicy.autoWifi ||
        this == ContentSyncPolicy.autoAny ||
        this == ContentSyncPolicy.scheduled;
  }

  /// Check if this policy requires WiFi
  bool get requiresWifi {
    return this == ContentSyncPolicy.autoWifi;
  }

  /// Check if this policy is scheduled
  bool get isScheduled {
    return this == ContentSyncPolicy.scheduled;
  }

  /// Get the background color for the sync policy (lighter version)
  Color get backgroundColor {
    switch (this) {
      case ContentSyncPolicy.manual:
        return Colors.blue.withAlpha(26); // 0.1 opacity = 26 alpha
      case ContentSyncPolicy.autoWifi:
        return Colors.green.withAlpha(26);
      case ContentSyncPolicy.autoAny:
        return Colors.orange.withAlpha(26);
      case ContentSyncPolicy.scheduled:
        return Colors.purple.withAlpha(26);
    }
  }

  /// Get the border color for the sync policy
  Color get borderColor {
    switch (this) {
      case ContentSyncPolicy.manual:
        return Colors.blue.withAlpha(77); // 0.3 opacity = 77 alpha
      case ContentSyncPolicy.autoWifi:
        return Colors.green.withAlpha(77);
      case ContentSyncPolicy.autoAny:
        return Colors.orange.withAlpha(77);
      case ContentSyncPolicy.scheduled:
        return Colors.purple.withAlpha(77);
    }
  }
}
