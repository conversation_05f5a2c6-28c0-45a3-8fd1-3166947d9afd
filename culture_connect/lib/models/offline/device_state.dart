import 'package:flutter/material.dart';
import 'package:culture_connect/models/offline/bandwidth_usage.dart';

/// A class representing the state of the device
class DeviceState {
  /// Whether the device is connected to the internet
  final bool isConnected;
  
  /// The network type
  final NetworkType networkType;
  
  /// Whether the device is charging
  final bool isCharging;
  
  /// The battery level (0-100)
  final int batteryLevel;
  
  /// Whether the device is in power save mode
  final bool isPowerSaveMode;
  
  /// Whether the device is in idle mode
  final bool isIdle;
  
  /// The available storage space in bytes
  final int availableStorage;
  
  /// Creates a new device state
  const DeviceState({
    required this.isConnected,
    required this.networkType,
    required this.isCharging,
    required this.batteryLevel,
    required this.isPowerSaveMode,
    required this.isIdle,
    required this.availableStorage,
  });
  
  /// Check if the device is on WiFi
  bool get isOnWifi => isConnected && networkType == NetworkType.wifi;
  
  /// Check if the device is on mobile data
  bool get isOnMobileData => isConnected && networkType == NetworkType.mobile;
  
  /// Check if the device has low battery
  bool get hasLowBattery => batteryLevel < 20;
  
  /// Check if the device has sufficient storage
  bool get hasSufficientStorage => availableStorage > 100 * 1024 * 1024; // 100 MB
  
  /// Get the formatted battery level
  String get formattedBatteryLevel => '$batteryLevel%';
  
  /// Get the formatted available storage
  String get formattedAvailableStorage => _formatBytes(availableStorage);
  
  /// Format bytes to a human-readable string
  String _formatBytes(int bytes) {
    if (bytes < 1024) {
      return '$bytes B';
    } else if (bytes < 1024 * 1024) {
      final kb = bytes / 1024;
      return '${kb.toStringAsFixed(1)} KB';
    } else if (bytes < 1024 * 1024 * 1024) {
      final mb = bytes / (1024 * 1024);
      return '${mb.toStringAsFixed(1)} MB';
    } else {
      final gb = bytes / (1024 * 1024 * 1024);
      return '${gb.toStringAsFixed(1)} GB';
    }
  }
  
  /// Get the icon for the network type
  IconData get networkTypeIcon => networkType.icon;
  
  /// Get the color for the network type
  Color get networkTypeColor => networkType.color;
  
  /// Get the display name for the network type
  String get networkTypeDisplayName => networkType.displayName;
  
  /// Get the icon for the battery status
  IconData get batteryIcon {
    if (isCharging) {
      return Icons.battery_charging_full;
    } else if (batteryLevel <= 10) {
      return Icons.battery_alert;
    } else if (batteryLevel <= 30) {
      return Icons.battery_1_bar;
    } else if (batteryLevel <= 50) {
      return Icons.battery_2_bar;
    } else if (batteryLevel <= 70) {
      return Icons.battery_3_bar;
    } else if (batteryLevel <= 90) {
      return Icons.battery_4_bar;
    } else {
      return Icons.battery_full;
    }
  }
  
  /// Get the color for the battery status
  Color get batteryColor {
    if (isCharging) {
      return Colors.green;
    } else if (batteryLevel <= 20) {
      return Colors.red;
    } else if (batteryLevel <= 50) {
      return Colors.orange;
    } else {
      return Colors.green;
    }
  }
  
  /// Create a copy of this device state with the given fields replaced
  DeviceState copyWith({
    bool? isConnected,
    NetworkType? networkType,
    bool? isCharging,
    int? batteryLevel,
    bool? isPowerSaveMode,
    bool? isIdle,
    int? availableStorage,
  }) {
    return DeviceState(
      isConnected: isConnected ?? this.isConnected,
      networkType: networkType ?? this.networkType,
      isCharging: isCharging ?? this.isCharging,
      batteryLevel: batteryLevel ?? this.batteryLevel,
      isPowerSaveMode: isPowerSaveMode ?? this.isPowerSaveMode,
      isIdle: isIdle ?? this.isIdle,
      availableStorage: availableStorage ?? this.availableStorage,
    );
  }
  
  /// Create a device state from JSON
  factory DeviceState.fromJson(Map<String, dynamic> json) {
    return DeviceState(
      isConnected: json['isConnected'],
      networkType: NetworkType.values.firstWhere(
        (type) => type.name == json['networkType'],
        orElse: () => NetworkType.unknown,
      ),
      isCharging: json['isCharging'],
      batteryLevel: json['batteryLevel'],
      isPowerSaveMode: json['isPowerSaveMode'],
      isIdle: json['isIdle'],
      availableStorage: json['availableStorage'],
    );
  }
  
  /// Convert this device state to JSON
  Map<String, dynamic> toJson() {
    return {
      'isConnected': isConnected,
      'networkType': networkType.name,
      'isCharging': isCharging,
      'batteryLevel': batteryLevel,
      'isPowerSaveMode': isPowerSaveMode,
      'isIdle': isIdle,
      'availableStorage': availableStorage,
    };
  }
}
