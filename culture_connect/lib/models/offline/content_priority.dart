import 'package:flutter/material.dart';

/// Priority level for offline content
enum ContentPriority {
  /// Critical content that must be available offline
  critical,

  /// High priority content that should be available offline
  high,

  /// Medium priority content that can be available offline
  medium,

  /// Low priority content that is nice to have offline
  low,
}

/// Extension methods for ContentPriority
extension ContentPriorityExtension on ContentPriority {
  /// Get the display name for the priority
  String get displayName {
    switch (this) {
      case ContentPriority.critical:
        return 'Critical';
      case ContentPriority.high:
        return 'High';
      case ContentPriority.medium:
        return 'Medium';
      case ContentPriority.low:
        return 'Low';
    }
  }

  /// Get the numeric value for the priority (higher is more important)
  int get value {
    switch (this) {
      case ContentPriority.critical:
        return 4;
      case ContentPriority.high:
        return 3;
      case ContentPriority.medium:
        return 2;
      case ContentPriority.low:
        return 1;
    }
  }

  /// Get the color for the priority
  Color get color {
    switch (this) {
      case ContentPriority.critical:
        return Colors.red;
      case ContentPriority.high:
        return Colors.orange;
      case ContentPriority.medium:
        return Colors.blue;
      case ContentPriority.low:
        return Colors.green;
    }
  }

  /// Get the icon for the priority
  IconData get icon {
    switch (this) {
      case ContentPriority.critical:
        return Icons.priority_high;
      case ContentPriority.high:
        return Icons.arrow_upward;
      case ContentPriority.medium:
        return Icons.remove;
      case ContentPriority.low:
        return Icons.arrow_downward;
    }
  }

  /// Check if this priority is higher than another priority
  bool isHigherThan(ContentPriority other) {
    return value > other.value;
  }

  /// Check if this priority is lower than another priority
  bool isLowerThan(ContentPriority other) {
    return value < other.value;
  }

  /// Get the background color for the priority (lighter version)
  Color get backgroundColor {
    switch (this) {
      case ContentPriority.critical:
        return Colors.red.withAlpha(26); // 0.1 opacity = 26 alpha
      case ContentPriority.high:
        return Colors.orange.withAlpha(26);
      case ContentPriority.medium:
        return Colors.blue.withAlpha(26);
      case ContentPriority.low:
        return Colors.green.withAlpha(26);
    }
  }

  /// Get the border color for the priority
  Color get borderColor {
    switch (this) {
      case ContentPriority.critical:
        return Colors.red.withAlpha(77); // 0.3 opacity = 77 alpha
      case ContentPriority.high:
        return Colors.orange.withAlpha(77);
      case ContentPriority.medium:
        return Colors.blue.withAlpha(77);
      case ContentPriority.low:
        return Colors.green.withAlpha(77);
    }
  }
}
