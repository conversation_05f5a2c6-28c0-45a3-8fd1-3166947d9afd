import 'package:flutter/material.dart';

/// A class representing a translation conflict
class TranslationConflict {
  /// The message ID
  final String messageId;
  
  /// The group ID
  final String groupId;
  
  /// The local version of the translation
  final String localVersion;
  
  /// The server version of the translation
  final String serverVersion;
  
  /// Whether the conflict has been resolved
  final bool resolved;
  
  /// The resolution type
  final String? resolutionType;
  
  /// The date the conflict was created
  final DateTime createdAt;
  
  /// The date the conflict was resolved
  final DateTime? resolvedAt;
  
  /// Creates a new translation conflict
  const TranslationConflict({
    required this.messageId,
    required this.groupId,
    required this.localVersion,
    required this.serverVersion,
    required this.resolved,
    this.resolutionType,
    required this.createdAt,
    this.resolvedAt,
  });
  
  /// Creates a translation conflict from a map
  factory TranslationConflict.fromMap(Map<String, dynamic> map) {
    return TranslationConflict(
      messageId: map['message_id'] as String,
      groupId: map['group_id'] as String? ?? 'unknown',
      localVersion: map['local_version'] as String,
      serverVersion: map['server_version'] as String,
      resolved: (map['resolved'] as int) == 1,
      resolutionType: map['resolution_type'] as String?,
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['created_at'] as int),
      resolvedAt: map['resolved_at'] != null
          ? DateTime.fromMillisecondsSinceEpoch(map['resolved_at'] as int)
          : null,
    );
  }
  
  /// Converts the translation conflict to a map
  Map<String, dynamic> toMap() {
    return {
      'message_id': messageId,
      'group_id': groupId,
      'local_version': localVersion,
      'server_version': serverVersion,
      'resolved': resolved ? 1 : 0,
      'resolution_type': resolutionType,
      'created_at': createdAt.millisecondsSinceEpoch,
      'resolved_at': resolvedAt?.millisecondsSinceEpoch,
    };
  }
  
  /// Creates a copy of this translation conflict with the given fields replaced
  TranslationConflict copyWith({
    String? messageId,
    String? groupId,
    String? localVersion,
    String? serverVersion,
    bool? resolved,
    String? resolutionType,
    DateTime? createdAt,
    DateTime? resolvedAt,
  }) {
    return TranslationConflict(
      messageId: messageId ?? this.messageId,
      groupId: groupId ?? this.groupId,
      localVersion: localVersion ?? this.localVersion,
      serverVersion: serverVersion ?? this.serverVersion,
      resolved: resolved ?? this.resolved,
      resolutionType: resolutionType ?? this.resolutionType,
      createdAt: createdAt ?? this.createdAt,
      resolvedAt: resolvedAt ?? this.resolvedAt,
    );
  }
  
  /// Resolves the conflict with the given resolution type
  TranslationConflict resolve(String resolutionType) {
    return copyWith(
      resolved: true,
      resolutionType: resolutionType,
      resolvedAt: DateTime.now(),
    );
  }
  
  /// Gets the color for the conflict
  Color get color {
    if (resolved) {
      return Colors.green;
    } else {
      return Colors.orange;
    }
  }
  
  /// Gets the icon for the conflict
  IconData get icon {
    if (resolved) {
      return Icons.check_circle;
    } else {
      return Icons.warning;
    }
  }
  
  /// Gets the status text for the conflict
  String get statusText {
    if (resolved) {
      return 'Resolved';
    } else {
      return 'Unresolved';
    }
  }
  
  /// Gets the resolution text for the conflict
  String get resolutionText {
    if (!resolved) {
      return 'Not resolved yet';
    }
    
    switch (resolutionType) {
      case 'local':
        return 'Kept local version';
      case 'server':
        return 'Used server version';
      case 'merged':
        return 'Merged versions';
      default:
        return 'Unknown resolution';
    }
  }
}
