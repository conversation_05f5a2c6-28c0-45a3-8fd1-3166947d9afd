import 'package:flutter/material.dart';

/// A class representing storage usage
class StorageUsage {
  /// The total storage space in bytes
  final int totalSpace;
  
  /// The used storage space in bytes
  final int usedSpace;
  
  /// The usage by content type
  final Map<String, int> usageByContentType;
  
  /// Creates a new storage usage
  const StorageUsage({
    required this.totalSpace,
    required this.usedSpace,
    required this.usageByContentType,
  });
  
  /// Get the free space in bytes
  int get freeSpace => totalSpace - usedSpace;
  
  /// Get the usage percentage
  double get usagePercentage => usedSpace / totalSpace;
  
  /// Get the formatted total space
  String get formattedTotalSpace => _formatBytes(totalSpace);
  
  /// Get the formatted used space
  String get formattedUsedSpace => _formatBytes(usedSpace);
  
  /// Get the formatted free space
  String get formattedFreeSpace => _formatBytes(freeSpace);
  
  /// Get the formatted usage percentage
  String get formattedUsagePercentage => '${(usagePercentage * 100).toStringAsFixed(1)}%';
  
  /// Format bytes to a human-readable string
  String _formatBytes(int bytes) {
    if (bytes < 1024) {
      return '$bytes B';
    } else if (bytes < 1024 * 1024) {
      final kb = bytes / 1024;
      return '${kb.toStringAsFixed(1)} KB';
    } else if (bytes < 1024 * 1024 * 1024) {
      final mb = bytes / (1024 * 1024);
      return '${mb.toStringAsFixed(1)} MB';
    } else {
      final gb = bytes / (1024 * 1024 * 1024);
      return '${gb.toStringAsFixed(1)} GB';
    }
  }
  
  /// Create a copy of this storage usage with the given fields replaced
  StorageUsage copyWith({
    int? totalSpace,
    int? usedSpace,
    Map<String, int>? usageByContentType,
  }) {
    return StorageUsage(
      totalSpace: totalSpace ?? this.totalSpace,
      usedSpace: usedSpace ?? this.usedSpace,
      usageByContentType: usageByContentType ?? this.usageByContentType,
    );
  }
  
  /// Create a storage usage from JSON
  factory StorageUsage.fromJson(Map<String, dynamic> json) {
    return StorageUsage(
      totalSpace: json['totalSpace'],
      usedSpace: json['usedSpace'],
      usageByContentType: Map<String, int>.from(json['usageByContentType']),
    );
  }
  
  /// Convert this storage usage to JSON
  Map<String, dynamic> toJson() {
    return {
      'totalSpace': totalSpace,
      'usedSpace': usedSpace,
      'usageByContentType': usageByContentType,
    };
  }
}

/// A class representing content type storage usage
class ContentTypeStorageUsage {
  /// The content type
  final String contentType;
  
  /// The display name for the content type
  final String displayName;
  
  /// The icon for the content type
  final IconData icon;
  
  /// The color for the content type
  final Color color;
  
  /// The used space in bytes
  final int usedSpace;
  
  /// The percentage of total used space
  final double percentage;
  
  /// Creates a new content type storage usage
  const ContentTypeStorageUsage({
    required this.contentType,
    required this.displayName,
    required this.icon,
    required this.color,
    required this.usedSpace,
    required this.percentage,
  });
  
  /// Get the formatted used space
  String get formattedUsedSpace => _formatBytes(usedSpace);
  
  /// Get the formatted percentage
  String get formattedPercentage => '${(percentage * 100).toStringAsFixed(1)}%';
  
  /// Format bytes to a human-readable string
  String _formatBytes(int bytes) {
    if (bytes < 1024) {
      return '$bytes B';
    } else if (bytes < 1024 * 1024) {
      final kb = bytes / 1024;
      return '${kb.toStringAsFixed(1)} KB';
    } else if (bytes < 1024 * 1024 * 1024) {
      final mb = bytes / (1024 * 1024);
      return '${mb.toStringAsFixed(1)} MB';
    } else {
      final gb = bytes / (1024 * 1024 * 1024);
      return '${gb.toStringAsFixed(1)} GB';
    }
  }
  
  /// Create a content type storage usage from a content type and used space
  factory ContentTypeStorageUsage.fromContentType({
    required String contentType,
    required int usedSpace,
    required int totalUsedSpace,
  }) {
    // Get the display name, icon, and color for the content type
    String displayName;
    IconData icon;
    Color color;
    
    switch (contentType) {
      case 'itinerary':
        displayName = 'Itineraries';
        icon = Icons.map;
        color = Colors.blue;
        break;
      case 'experience':
        displayName = 'Experiences';
        icon = Icons.attractions;
        color = Colors.orange;
        break;
      case 'hotel':
        displayName = 'Hotels';
        icon = Icons.hotel;
        color = Colors.purple;
        break;
      case 'flight':
        displayName = 'Flights';
        icon = Icons.flight;
        color = Colors.green;
        break;
      case 'restaurant':
        displayName = 'Restaurants';
        icon = Icons.restaurant;
        color = Colors.red;
        break;
      case 'car_rental':
        displayName = 'Car Rentals';
        icon = Icons.directions_car;
        color = Colors.amber;
        break;
      case 'cruise':
        displayName = 'Cruises';
        icon = Icons.directions_boat;
        color = Colors.indigo;
        break;
      case 'ar_content':
        displayName = 'AR Content';
        icon = Icons.view_in_ar;
        color = Colors.teal;
        break;
      case 'timeline':
        displayName = 'Timelines';
        icon = Icons.timeline;
        color = Colors.deepPurple;
        break;
      default:
        displayName = contentType;
        icon = Icons.file_copy;
        color = Colors.grey;
    }
    
    // Calculate the percentage
    final percentage = totalUsedSpace > 0 ? usedSpace / totalUsedSpace : 0.0;
    
    return ContentTypeStorageUsage(
      contentType: contentType,
      displayName: displayName,
      icon: icon,
      color: color,
      usedSpace: usedSpace,
      percentage: percentage,
    );
  }
}
