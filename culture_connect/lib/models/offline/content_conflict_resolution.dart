import 'package:flutter/material.dart';

/// Conflict resolution strategy for offline content
enum ContentConflictResolution {
  /// Server version wins
  serverWins,

  /// Client version wins
  clientWins,

  /// Merge changes if possible
  merge,

  /// Ask the user to resolve the conflict
  askUser,
}

/// Extension methods for ContentConflictResolution
extension ContentConflictResolutionExtension on ContentConflictResolution {
  /// Get the display name for the conflict resolution strategy
  String get displayName {
    switch (this) {
      case ContentConflictResolution.serverWins:
        return 'Server Wins';
      case ContentConflictResolution.clientWins:
        return 'Client Wins';
      case ContentConflictResolution.merge:
        return 'Merge Changes';
      case ContentConflictResolution.askUser:
        return 'Ask Me';
    }
  }

  /// Get the description for the conflict resolution strategy
  String get description {
    switch (this) {
      case ContentConflictResolution.serverWins:
        return 'Always use the server version in case of conflicts';
      case ContentConflictResolution.clientWins:
        return 'Always use your local changes in case of conflicts';
      case ContentConflictResolution.merge:
        return 'Try to merge changes automatically when possible';
      case ContentConflictResolution.askUser:
        return 'Ask me to decide for each conflict';
    }
  }

  /// Get the icon for the conflict resolution strategy
  IconData get icon {
    switch (this) {
      case ContentConflictResolution.serverWins:
        return Icons.cloud_done;
      case ContentConflictResolution.clientWins:
        return Icons.phone_android;
      case ContentConflictResolution.merge:
        return Icons.merge_type;
      case ContentConflictResolution.askUser:
        return Icons.help_outline;
    }
  }

  /// Get the color for the conflict resolution strategy
  Color get color {
    switch (this) {
      case ContentConflictResolution.serverWins:
        return Colors.blue;
      case ContentConflictResolution.clientWins:
        return Colors.green;
      case ContentConflictResolution.merge:
        return Colors.purple;
      case ContentConflictResolution.askUser:
        return Colors.orange;
    }
  }

  /// Check if this strategy requires user interaction
  bool get requiresUserInteraction {
    return this == ContentConflictResolution.askUser;
  }

  /// Check if this strategy attempts to merge changes
  bool get attemptsMerge {
    return this == ContentConflictResolution.merge;
  }

  /// Get the background color for the conflict resolution strategy (lighter version)
  Color get backgroundColor {
    switch (this) {
      case ContentConflictResolution.serverWins:
        return Colors.blue.withAlpha(26); // 0.1 opacity = 26 alpha
      case ContentConflictResolution.clientWins:
        return Colors.green.withAlpha(26);
      case ContentConflictResolution.merge:
        return Colors.purple.withAlpha(26);
      case ContentConflictResolution.askUser:
        return Colors.orange.withAlpha(26);
    }
  }

  /// Get the border color for the conflict resolution strategy
  Color get borderColor {
    switch (this) {
      case ContentConflictResolution.serverWins:
        return Colors.blue.withAlpha(77); // 0.3 opacity = 77 alpha
      case ContentConflictResolution.clientWins:
        return Colors.green.withAlpha(77);
      case ContentConflictResolution.merge:
        return Colors.purple.withAlpha(77);
      case ContentConflictResolution.askUser:
        return Colors.orange.withAlpha(77);
    }
  }
}
