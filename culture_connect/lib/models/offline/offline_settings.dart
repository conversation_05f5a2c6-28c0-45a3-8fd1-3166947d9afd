import 'package:culture_connect/models/offline/content_conflict_resolution.dart';

/// A class representing offline settings
class OfflineSettings {
  /// Whether to sync automatically
  final bool syncAutomatically;
  
  /// Whether to sync only on WiFi
  final bool syncOnlyOnWifi;
  
  /// Whether to sync only when charging
  final bool syncOnlyWhenCharging;
  
  /// Whether to sync only when the battery level is above a threshold
  final bool syncOnlyAboveBatteryLevel;
  
  /// The battery level threshold (0-100)
  final int batteryLevelThreshold;
  
  /// The maximum storage space to use for offline content (in bytes)
  final int maxStorageSpace;
  
  /// Whether to automatically clean up expired content
  final bool autoCleanupExpiredContent;
  
  /// Whether to automatically clean up content when storage is low
  final bool autoCleanupWhenStorageLow;
  
  /// The storage threshold for auto cleanup (in bytes)
  final int storageThresholdForCleanup;
  
  /// The default conflict resolution strategy
  final ContentConflictResolution defaultConflictResolution;
  
  /// The maximum daily bandwidth usage (in bytes, 0 for unlimited)
  final int maxDailyBandwidthUsage;
  
  /// Whether to show offline content indicators
  final bool showOfflineContentIndicators;
  
  /// Whether to show sync status notifications
  final bool showSyncStatusNotifications;
  
  /// Whether to enable background sync
  final bool enableBackgroundSync;
  
  /// The background sync frequency (in minutes)
  final int backgroundSyncFrequency;
  
  /// Creates a new offline settings
  const OfflineSettings({
    required this.syncAutomatically,
    required this.syncOnlyOnWifi,
    required this.syncOnlyWhenCharging,
    required this.syncOnlyAboveBatteryLevel,
    required this.batteryLevelThreshold,
    required this.maxStorageSpace,
    required this.autoCleanupExpiredContent,
    required this.autoCleanupWhenStorageLow,
    required this.storageThresholdForCleanup,
    required this.defaultConflictResolution,
    required this.maxDailyBandwidthUsage,
    required this.showOfflineContentIndicators,
    required this.showSyncStatusNotifications,
    required this.enableBackgroundSync,
    required this.backgroundSyncFrequency,
  });
  
  /// Get the default offline settings
  factory OfflineSettings.defaultSettings() {
    return const OfflineSettings(
      syncAutomatically: true,
      syncOnlyOnWifi: true,
      syncOnlyWhenCharging: false,
      syncOnlyAboveBatteryLevel: true,
      batteryLevelThreshold: 20,
      maxStorageSpace: 1024 * 1024 * 1024, // 1 GB
      autoCleanupExpiredContent: true,
      autoCleanupWhenStorageLow: true,
      storageThresholdForCleanup: 100 * 1024 * 1024, // 100 MB
      defaultConflictResolution: ContentConflictResolution.askUser,
      maxDailyBandwidthUsage: 0, // Unlimited
      showOfflineContentIndicators: true,
      showSyncStatusNotifications: true,
      enableBackgroundSync: true,
      backgroundSyncFrequency: 60, // 1 hour
    );
  }
  
  /// Get the formatted max storage space
  String get formattedMaxStorageSpace => _formatBytes(maxStorageSpace);
  
  /// Get the formatted storage threshold for cleanup
  String get formattedStorageThresholdForCleanup => _formatBytes(storageThresholdForCleanup);
  
  /// Get the formatted max daily bandwidth usage
  String get formattedMaxDailyBandwidthUsage {
    if (maxDailyBandwidthUsage <= 0) {
      return 'Unlimited';
    }
    
    return _formatBytes(maxDailyBandwidthUsage);
  }
  
  /// Format bytes to a human-readable string
  String _formatBytes(int bytes) {
    if (bytes < 1024) {
      return '$bytes B';
    } else if (bytes < 1024 * 1024) {
      final kb = bytes / 1024;
      return '${kb.toStringAsFixed(1)} KB';
    } else if (bytes < 1024 * 1024 * 1024) {
      final mb = bytes / (1024 * 1024);
      return '${mb.toStringAsFixed(1)} MB';
    } else {
      final gb = bytes / (1024 * 1024 * 1024);
      return '${gb.toStringAsFixed(1)} GB';
    }
  }
  
  /// Create a copy of this offline settings with the given fields replaced
  OfflineSettings copyWith({
    bool? syncAutomatically,
    bool? syncOnlyOnWifi,
    bool? syncOnlyWhenCharging,
    bool? syncOnlyAboveBatteryLevel,
    int? batteryLevelThreshold,
    int? maxStorageSpace,
    bool? autoCleanupExpiredContent,
    bool? autoCleanupWhenStorageLow,
    int? storageThresholdForCleanup,
    ContentConflictResolution? defaultConflictResolution,
    int? maxDailyBandwidthUsage,
    bool? showOfflineContentIndicators,
    bool? showSyncStatusNotifications,
    bool? enableBackgroundSync,
    int? backgroundSyncFrequency,
  }) {
    return OfflineSettings(
      syncAutomatically: syncAutomatically ?? this.syncAutomatically,
      syncOnlyOnWifi: syncOnlyOnWifi ?? this.syncOnlyOnWifi,
      syncOnlyWhenCharging: syncOnlyWhenCharging ?? this.syncOnlyWhenCharging,
      syncOnlyAboveBatteryLevel: syncOnlyAboveBatteryLevel ?? this.syncOnlyAboveBatteryLevel,
      batteryLevelThreshold: batteryLevelThreshold ?? this.batteryLevelThreshold,
      maxStorageSpace: maxStorageSpace ?? this.maxStorageSpace,
      autoCleanupExpiredContent: autoCleanupExpiredContent ?? this.autoCleanupExpiredContent,
      autoCleanupWhenStorageLow: autoCleanupWhenStorageLow ?? this.autoCleanupWhenStorageLow,
      storageThresholdForCleanup: storageThresholdForCleanup ?? this.storageThresholdForCleanup,
      defaultConflictResolution: defaultConflictResolution ?? this.defaultConflictResolution,
      maxDailyBandwidthUsage: maxDailyBandwidthUsage ?? this.maxDailyBandwidthUsage,
      showOfflineContentIndicators: showOfflineContentIndicators ?? this.showOfflineContentIndicators,
      showSyncStatusNotifications: showSyncStatusNotifications ?? this.showSyncStatusNotifications,
      enableBackgroundSync: enableBackgroundSync ?? this.enableBackgroundSync,
      backgroundSyncFrequency: backgroundSyncFrequency ?? this.backgroundSyncFrequency,
    );
  }
  
  /// Create offline settings from JSON
  factory OfflineSettings.fromJson(Map<String, dynamic> json) {
    return OfflineSettings(
      syncAutomatically: json['syncAutomatically'] ?? true,
      syncOnlyOnWifi: json['syncOnlyOnWifi'] ?? true,
      syncOnlyWhenCharging: json['syncOnlyWhenCharging'] ?? false,
      syncOnlyAboveBatteryLevel: json['syncOnlyAboveBatteryLevel'] ?? true,
      batteryLevelThreshold: json['batteryLevelThreshold'] ?? 20,
      maxStorageSpace: json['maxStorageSpace'] ?? 1024 * 1024 * 1024,
      autoCleanupExpiredContent: json['autoCleanupExpiredContent'] ?? true,
      autoCleanupWhenStorageLow: json['autoCleanupWhenStorageLow'] ?? true,
      storageThresholdForCleanup: json['storageThresholdForCleanup'] ?? 100 * 1024 * 1024,
      defaultConflictResolution: json['defaultConflictResolution'] != null
          ? ContentConflictResolution.values.firstWhere(
              (resolution) => resolution.name == json['defaultConflictResolution'],
              orElse: () => ContentConflictResolution.askUser,
            )
          : ContentConflictResolution.askUser,
      maxDailyBandwidthUsage: json['maxDailyBandwidthUsage'] ?? 0,
      showOfflineContentIndicators: json['showOfflineContentIndicators'] ?? true,
      showSyncStatusNotifications: json['showSyncStatusNotifications'] ?? true,
      enableBackgroundSync: json['enableBackgroundSync'] ?? true,
      backgroundSyncFrequency: json['backgroundSyncFrequency'] ?? 60,
    );
  }
  
  /// Convert this offline settings to JSON
  Map<String, dynamic> toJson() {
    return {
      'syncAutomatically': syncAutomatically,
      'syncOnlyOnWifi': syncOnlyOnWifi,
      'syncOnlyWhenCharging': syncOnlyWhenCharging,
      'syncOnlyAboveBatteryLevel': syncOnlyAboveBatteryLevel,
      'batteryLevelThreshold': batteryLevelThreshold,
      'maxStorageSpace': maxStorageSpace,
      'autoCleanupExpiredContent': autoCleanupExpiredContent,
      'autoCleanupWhenStorageLow': autoCleanupWhenStorageLow,
      'storageThresholdForCleanup': storageThresholdForCleanup,
      'defaultConflictResolution': defaultConflictResolution.name,
      'maxDailyBandwidthUsage': maxDailyBandwidthUsage,
      'showOfflineContentIndicators': showOfflineContentIndicators,
      'showSyncStatusNotifications': showSyncStatusNotifications,
      'enableBackgroundSync': enableBackgroundSync,
      'backgroundSyncFrequency': backgroundSyncFrequency,
    };
  }
}
