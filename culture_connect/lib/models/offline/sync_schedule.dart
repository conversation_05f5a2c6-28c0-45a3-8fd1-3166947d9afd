import 'package:flutter/material.dart';

/// Day of the week
enum DayOfWeek {
  /// Monday
  monday,
  
  /// Tuesday
  tuesday,
  
  /// Wednesday
  wednesday,
  
  /// Thursday
  thursday,
  
  /// Friday
  friday,
  
  /// Saturday
  saturday,
  
  /// Sunday
  sunday,
}

/// Extension methods for DayOfWeek
extension DayOfWeekExtension on DayOfWeek {
  /// Get the display name for the day of the week
  String get displayName {
    switch (this) {
      case DayOfWeek.monday:
        return 'Monday';
      case DayOfWeek.tuesday:
        return 'Tuesday';
      case DayOfWeek.wednesday:
        return 'Wednesday';
      case DayOfWeek.thursday:
        return 'Thursday';
      case DayOfWeek.friday:
        return 'Friday';
      case DayOfWeek.saturday:
        return 'Saturday';
      case DayOfWeek.sunday:
        return 'Sunday';
    }
  }
  
  /// Get the short display name for the day of the week
  String get shortDisplayName {
    switch (this) {
      case DayOfWeek.monday:
        return 'Mon';
      case DayOfWeek.tuesday:
        return 'Tue';
      case DayOfWeek.wednesday:
        return 'Wed';
      case DayOfWeek.thursday:
        return 'Thu';
      case DayOfWeek.friday:
        return 'Fri';
      case DayOfWeek.saturday:
        return 'Sat';
      case DayOfWeek.sunday:
        return 'Sun';
    }
  }
  
  /// Get the index of the day of the week (1-7)
  int get index {
    switch (this) {
      case DayOfWeek.monday:
        return 1;
      case DayOfWeek.tuesday:
        return 2;
      case DayOfWeek.wednesday:
        return 3;
      case DayOfWeek.thursday:
        return 4;
      case DayOfWeek.friday:
        return 5;
      case DayOfWeek.saturday:
        return 6;
      case DayOfWeek.sunday:
        return 7;
    }
  }
  
  /// Get the day of the week from a DateTime
  static DayOfWeek fromDateTime(DateTime dateTime) {
    switch (dateTime.weekday) {
      case 1:
        return DayOfWeek.monday;
      case 2:
        return DayOfWeek.tuesday;
      case 3:
        return DayOfWeek.wednesday;
      case 4:
        return DayOfWeek.thursday;
      case 5:
        return DayOfWeek.friday;
      case 6:
        return DayOfWeek.saturday;
      case 7:
        return DayOfWeek.sunday;
      default:
        return DayOfWeek.monday;
    }
  }
}

/// A class representing a time of day
class TimeOfDayRange {
  /// The start time
  final TimeOfDay startTime;
  
  /// The end time
  final TimeOfDay endTime;
  
  /// Creates a new time of day range
  const TimeOfDayRange({
    required this.startTime,
    required this.endTime,
  });
  
  /// Check if a time is within this range
  bool contains(TimeOfDay time) {
    final startMinutes = startTime.hour * 60 + startTime.minute;
    final endMinutes = endTime.hour * 60 + endTime.minute;
    final timeMinutes = time.hour * 60 + time.minute;
    
    if (startMinutes <= endMinutes) {
      // Normal range (e.g., 9:00 - 17:00)
      return timeMinutes >= startMinutes && timeMinutes <= endMinutes;
    } else {
      // Overnight range (e.g., 22:00 - 6:00)
      return timeMinutes >= startMinutes || timeMinutes <= endMinutes;
    }
  }
  
  /// Get the formatted start time
  String get formattedStartTime => _formatTimeOfDay(startTime);
  
  /// Get the formatted end time
  String get formattedEndTime => _formatTimeOfDay(endTime);
  
  /// Get the formatted time range
  String get formattedTimeRange => '$formattedStartTime - $formattedEndTime';
  
  /// Format a time of day to a human-readable string
  String _formatTimeOfDay(TimeOfDay timeOfDay) {
    final hour = timeOfDay.hour.toString().padLeft(2, '0');
    final minute = timeOfDay.minute.toString().padLeft(2, '0');
    return '$hour:$minute';
  }
  
  /// Create a copy of this time of day range with the given fields replaced
  TimeOfDayRange copyWith({
    TimeOfDay? startTime,
    TimeOfDay? endTime,
  }) {
    return TimeOfDayRange(
      startTime: startTime ?? this.startTime,
      endTime: endTime ?? this.endTime,
    );
  }
  
  /// Create a time of day range from JSON
  factory TimeOfDayRange.fromJson(Map<String, dynamic> json) {
    return TimeOfDayRange(
      startTime: _parseTimeOfDay(json['startTime']),
      endTime: _parseTimeOfDay(json['endTime']),
    );
  }
  
  /// Convert this time of day range to JSON
  Map<String, dynamic> toJson() {
    return {
      'startTime': '${startTime.hour.toString().padLeft(2, '0')}:${startTime.minute.toString().padLeft(2, '0')}',
      'endTime': '${endTime.hour.toString().padLeft(2, '0')}:${endTime.minute.toString().padLeft(2, '0')}',
    };
  }
  
  /// Parse a time of day from a string
  static TimeOfDay _parseTimeOfDay(String timeString) {
    final parts = timeString.split(':');
    return TimeOfDay(
      hour: int.parse(parts[0]),
      minute: int.parse(parts[1]),
    );
  }
}

/// A class representing a sync schedule
class SyncSchedule {
  /// The unique identifier
  final String id;
  
  /// The name of the schedule
  final String name;
  
  /// Whether the schedule is enabled
  final bool isEnabled;
  
  /// The days of the week to sync
  final List<DayOfWeek> daysOfWeek;
  
  /// The time ranges to sync
  final List<TimeOfDayRange> timeRanges;
  
  /// Whether to sync only on WiFi
  final bool syncOnlyOnWifi;
  
  /// Whether to sync only when charging
  final bool syncOnlyWhenCharging;
  
  /// Whether to sync only when the battery level is above a threshold
  final bool syncOnlyAboveBatteryLevel;
  
  /// The battery level threshold (0-100)
  final int batteryLevelThreshold;
  
  /// The content types to sync
  final List<String> contentTypes;
  
  /// Creates a new sync schedule
  const SyncSchedule({
    required this.id,
    required this.name,
    required this.isEnabled,
    required this.daysOfWeek,
    required this.timeRanges,
    required this.syncOnlyOnWifi,
    required this.syncOnlyWhenCharging,
    required this.syncOnlyAboveBatteryLevel,
    required this.batteryLevelThreshold,
    required this.contentTypes,
  });
  
  /// Check if the schedule is active for the current time
  bool isActiveNow() {
    final now = DateTime.now();
    final currentDay = DayOfWeekExtension.fromDateTime(now);
    final currentTime = TimeOfDay.fromDateTime(now);
    
    // Check if today is a scheduled day
    if (!daysOfWeek.contains(currentDay)) {
      return false;
    }
    
    // Check if the current time is within any of the scheduled time ranges
    for (final timeRange in timeRanges) {
      if (timeRange.contains(currentTime)) {
        return true;
      }
    }
    
    return false;
  }
  
  /// Get the formatted days of the week
  String get formattedDaysOfWeek {
    if (daysOfWeek.length == 7) {
      return 'Every day';
    } else if (daysOfWeek.length == 5 &&
               daysOfWeek.contains(DayOfWeek.monday) &&
               daysOfWeek.contains(DayOfWeek.tuesday) &&
               daysOfWeek.contains(DayOfWeek.wednesday) &&
               daysOfWeek.contains(DayOfWeek.thursday) &&
               daysOfWeek.contains(DayOfWeek.friday)) {
      return 'Weekdays';
    } else if (daysOfWeek.length == 2 &&
               daysOfWeek.contains(DayOfWeek.saturday) &&
               daysOfWeek.contains(DayOfWeek.sunday)) {
      return 'Weekends';
    } else {
      return daysOfWeek.map((day) => day.shortDisplayName).join(', ');
    }
  }
  
  /// Get the formatted time ranges
  String get formattedTimeRanges {
    return timeRanges.map((range) => range.formattedTimeRange).join(', ');
  }
  
  /// Create a copy of this sync schedule with the given fields replaced
  SyncSchedule copyWith({
    String? id,
    String? name,
    bool? isEnabled,
    List<DayOfWeek>? daysOfWeek,
    List<TimeOfDayRange>? timeRanges,
    bool? syncOnlyOnWifi,
    bool? syncOnlyWhenCharging,
    bool? syncOnlyAboveBatteryLevel,
    int? batteryLevelThreshold,
    List<String>? contentTypes,
  }) {
    return SyncSchedule(
      id: id ?? this.id,
      name: name ?? this.name,
      isEnabled: isEnabled ?? this.isEnabled,
      daysOfWeek: daysOfWeek ?? this.daysOfWeek,
      timeRanges: timeRanges ?? this.timeRanges,
      syncOnlyOnWifi: syncOnlyOnWifi ?? this.syncOnlyOnWifi,
      syncOnlyWhenCharging: syncOnlyWhenCharging ?? this.syncOnlyWhenCharging,
      syncOnlyAboveBatteryLevel: syncOnlyAboveBatteryLevel ?? this.syncOnlyAboveBatteryLevel,
      batteryLevelThreshold: batteryLevelThreshold ?? this.batteryLevelThreshold,
      contentTypes: contentTypes ?? this.contentTypes,
    );
  }
  
  /// Create a sync schedule from JSON
  factory SyncSchedule.fromJson(Map<String, dynamic> json) {
    return SyncSchedule(
      id: json['id'],
      name: json['name'],
      isEnabled: json['isEnabled'],
      daysOfWeek: (json['daysOfWeek'] as List)
          .map((day) => DayOfWeek.values.firstWhere(
                (d) => d.name == day,
                orElse: () => DayOfWeek.monday,
              ))
          .toList(),
      timeRanges: (json['timeRanges'] as List)
          .map((range) => TimeOfDayRange.fromJson(range))
          .toList(),
      syncOnlyOnWifi: json['syncOnlyOnWifi'],
      syncOnlyWhenCharging: json['syncOnlyWhenCharging'],
      syncOnlyAboveBatteryLevel: json['syncOnlyAboveBatteryLevel'],
      batteryLevelThreshold: json['batteryLevelThreshold'],
      contentTypes: List<String>.from(json['contentTypes']),
    );
  }
  
  /// Convert this sync schedule to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'isEnabled': isEnabled,
      'daysOfWeek': daysOfWeek.map((day) => day.name).toList(),
      'timeRanges': timeRanges.map((range) => range.toJson()).toList(),
      'syncOnlyOnWifi': syncOnlyOnWifi,
      'syncOnlyWhenCharging': syncOnlyWhenCharging,
      'syncOnlyAboveBatteryLevel': syncOnlyAboveBatteryLevel,
      'batteryLevelThreshold': batteryLevelThreshold,
      'contentTypes': contentTypes,
    };
  }
}
