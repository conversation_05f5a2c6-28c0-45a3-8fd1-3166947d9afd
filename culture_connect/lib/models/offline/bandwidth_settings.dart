/// A class representing bandwidth settings
class BandwidthSettings {
  /// Whether to sync on WiFi only
  final bool syncOnWifiOnly;

  /// Whether to sync automatically
  final bool syncAutomatically;

  /// The maximum mobile data usage per day (in bytes)
  final int maxMobileDataUsagePerDay;

  /// The maximum WiFi data usage per day (in bytes)
  final int maxWifiDataUsagePerDay;

  /// Creates new bandwidth settings
  const BandwidthSettings({
    this.syncOnWifiOnly = true,
    this.syncAutomatically = true,
    this.maxMobileDataUsagePerDay = 10 * 1024 * 1024, // 10 MB
    this.maxWifiDataUsagePerDay = 100 * 1024 * 1024, // 100 MB
  });

  /// Creates bandwidth settings from JSON
  factory BandwidthSettings.fromJson(Map<String, dynamic> json) {
    return BandwidthSettings(
      syncOnWifiOnly: json['syncOnWifiOnly'] as bool? ?? true,
      syncAutomatically: json['syncAutomatically'] as bool? ?? true,
      maxMobileDataUsagePerDay:
          json['maxMobileDataUsagePerDay'] as int? ?? 10 * 1024 * 1024,
      maxWifiDataUsagePerDay:
          json['maxWifiDataUsagePerDay'] as int? ?? 100 * 1024 * 1024,
    );
  }

  /// Converts the bandwidth settings to JSON
  Map<String, dynamic> toJson() {
    return {
      'syncOnWifiOnly': syncOnWifiOnly,
      'syncAutomatically': syncAutomatically,
      'maxMobileDataUsagePerDay': maxMobileDataUsagePerDay,
      'maxWifiDataUsagePerDay': maxWifiDataUsagePerDay,
    };
  }

  /// Creates a copy of the bandwidth settings with updated values
  BandwidthSettings copyWith({
    bool? syncOnWifiOnly,
    bool? syncAutomatically,
    int? maxMobileDataUsagePerDay,
    int? maxWifiDataUsagePerDay,
  }) {
    return BandwidthSettings(
      syncOnWifiOnly: syncOnWifiOnly ?? this.syncOnWifiOnly,
      syncAutomatically: syncAutomatically ?? this.syncAutomatically,
      maxMobileDataUsagePerDay:
          maxMobileDataUsagePerDay ?? this.maxMobileDataUsagePerDay,
      maxWifiDataUsagePerDay:
          maxWifiDataUsagePerDay ?? this.maxWifiDataUsagePerDay,
    );
  }
}
