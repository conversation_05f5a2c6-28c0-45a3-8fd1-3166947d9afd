import 'package:flutter/foundation.dart';

/// A model representing a historical exchange rate data point
class ExchangeRateHistoryPoint {
  /// The date of this data point
  final DateTime date;
  
  /// The exchange rate
  final double rate;
  
  /// Creates a new exchange rate history point
  const ExchangeRateHistoryPoint({
    required this.date,
    required this.rate,
  });
  
  /// Creates a copy with some fields replaced
  ExchangeRateHistoryPoint copyWith({
    DateTime? date,
    double? rate,
  }) {
    return ExchangeRateHistoryPoint(
      date: date ?? this.date,
      rate: rate ?? this.rate,
    );
  }
  
  /// Converts to JSON
  Map<String, dynamic> toJson() {
    return {
      'date': date.toIso8601String(),
      'rate': rate,
    };
  }
  
  /// Creates from JSON
  factory ExchangeRateHistoryPoint.fromJson(Map<String, dynamic> json) {
    return ExchangeRateHistoryPoint(
      date: DateTime.parse(json['date'] as String),
      rate: json['rate'] as double,
    );
  }
  
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
  
    return other is ExchangeRateHistoryPoint &&
      other.date == date &&
      other.rate == rate;
  }

  @override
  int get hashCode => date.hashCode ^ rate.hashCode;
}

/// A model representing historical exchange rate data
class CurrencyConversionHistoryModel {
  /// The base currency code
  final String baseCurrency;
  
  /// The target currency code
  final String targetCurrency;
  
  /// The historical data points
  final List<ExchangeRateHistoryPoint> dataPoints;
  
  /// The start date of the historical data
  final DateTime startDate;
  
  /// The end date of the historical data
  final DateTime endDate;
  
  /// The source of the historical data
  final String source;
  
  /// Whether this data is from cache
  final bool isFromCache;
  
  /// Creates a new currency conversion history model
  const CurrencyConversionHistoryModel({
    required this.baseCurrency,
    required this.targetCurrency,
    required this.dataPoints,
    required this.startDate,
    required this.endDate,
    this.source = 'API',
    this.isFromCache = false,
  });
  
  /// Creates a copy with some fields replaced
  CurrencyConversionHistoryModel copyWith({
    String? baseCurrency,
    String? targetCurrency,
    List<ExchangeRateHistoryPoint>? dataPoints,
    DateTime? startDate,
    DateTime? endDate,
    String? source,
    bool? isFromCache,
  }) {
    return CurrencyConversionHistoryModel(
      baseCurrency: baseCurrency ?? this.baseCurrency,
      targetCurrency: targetCurrency ?? this.targetCurrency,
      dataPoints: dataPoints ?? this.dataPoints,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      source: source ?? this.source,
      isFromCache: isFromCache ?? this.isFromCache,
    );
  }
  
  /// Converts to JSON
  Map<String, dynamic> toJson() {
    return {
      'baseCurrency': baseCurrency,
      'targetCurrency': targetCurrency,
      'dataPoints': dataPoints.map((point) => point.toJson()).toList(),
      'startDate': startDate.toIso8601String(),
      'endDate': endDate.toIso8601String(),
      'source': source,
      'isFromCache': isFromCache,
    };
  }
  
  /// Creates from JSON
  factory CurrencyConversionHistoryModel.fromJson(Map<String, dynamic> json) {
    return CurrencyConversionHistoryModel(
      baseCurrency: json['baseCurrency'] as String,
      targetCurrency: json['targetCurrency'] as String,
      dataPoints: (json['dataPoints'] as List)
          .map((e) => ExchangeRateHistoryPoint.fromJson(e as Map<String, dynamic>))
          .toList(),
      startDate: DateTime.parse(json['startDate'] as String),
      endDate: DateTime.parse(json['endDate'] as String),
      source: json['source'] as String? ?? 'API',
      isFromCache: json['isFromCache'] as bool? ?? false,
    );
  }
  
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
  
    return other is CurrencyConversionHistoryModel &&
      other.baseCurrency == baseCurrency &&
      other.targetCurrency == targetCurrency &&
      listEquals(other.dataPoints, dataPoints) &&
      other.startDate == startDate &&
      other.endDate == endDate &&
      other.source == source &&
      other.isFromCache == isFromCache;
  }

  @override
  int get hashCode {
    return baseCurrency.hashCode ^
      targetCurrency.hashCode ^
      dataPoints.hashCode ^
      startDate.hashCode ^
      endDate.hashCode ^
      source.hashCode ^
      isFromCache.hashCode;
  }
  
  /// Get the minimum rate in the historical data
  double get minRate {
    if (dataPoints.isEmpty) return 0;
    return dataPoints.map((point) => point.rate).reduce((a, b) => a < b ? a : b);
  }
  
  /// Get the maximum rate in the historical data
  double get maxRate {
    if (dataPoints.isEmpty) return 0;
    return dataPoints.map((point) => point.rate).reduce((a, b) => a > b ? a : b);
  }
  
  /// Get the average rate in the historical data
  double get averageRate {
    if (dataPoints.isEmpty) return 0;
    final sum = dataPoints.map((point) => point.rate).reduce((a, b) => a + b);
    return sum / dataPoints.length;
  }
  
  /// Get the latest rate in the historical data
  double get latestRate {
    if (dataPoints.isEmpty) return 0;
    return dataPoints.last.rate;
  }
  
  /// Get the rate change percentage over the period
  double get rateChangePercentage {
    if (dataPoints.length < 2) return 0;
    final firstRate = dataPoints.first.rate;
    final lastRate = dataPoints.last.rate;
    return ((lastRate - firstRate) / firstRate) * 100;
  }
  
  /// Get the rate change over the period
  double get rateChange {
    if (dataPoints.length < 2) return 0;
    final firstRate = dataPoints.first.rate;
    final lastRate = dataPoints.last.rate;
    return lastRate - firstRate;
  }
  
  /// Check if the rate is trending up
  bool get isTrendingUp {
    return rateChange > 0;
  }
  
  /// Check if the rate is trending down
  bool get isTrendingDown {
    return rateChange < 0;
  }
  
  /// Check if the rate is stable
  bool get isStable {
    return rateChange.abs() < 0.001;
  }
}
