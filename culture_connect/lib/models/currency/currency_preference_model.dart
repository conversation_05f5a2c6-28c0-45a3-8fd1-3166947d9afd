import 'package:flutter/foundation.dart';

/// A model representing a user's currency preferences
class CurrencyPreferenceModel {
  /// The user's preferred currency code
  final String preferredCurrency;
  
  /// Whether to automatically detect the local currency based on location
  final bool autoDetectCurrency;
  
  /// Whether to show the original price alongside the converted price
  final bool showOriginalPrice;
  
  /// Whether to show the exchange rate used for conversion
  final bool showExchangeRate;
  
  /// Whether to show the last updated time for exchange rates
  final bool showLastUpdated;
  
  /// The user's favorite currencies
  final List<String> favoriteCurrencies;
  
  /// The user's recently used currencies
  final List<String> recentlyUsedCurrencies;
  
  /// Creates a new currency preference model
  const CurrencyPreferenceModel({
    required this.preferredCurrency,
    this.autoDetectCurrency = true,
    this.showOriginalPrice = true,
    this.showExchangeRate = true,
    this.showLastUpdated = true,
    this.favoriteCurrencies = const [],
    this.recentlyUsedCurrencies = const [],
  });
  
  /// Creates a copy with some fields replaced
  CurrencyPreferenceModel copyWith({
    String? preferredCurrency,
    bool? autoDetectCurrency,
    bool? showOriginalPrice,
    bool? showExchangeRate,
    bool? showLastUpdated,
    List<String>? favoriteCurrencies,
    List<String>? recentlyUsedCurrencies,
  }) {
    return CurrencyPreferenceModel(
      preferredCurrency: preferredCurrency ?? this.preferredCurrency,
      autoDetectCurrency: autoDetectCurrency ?? this.autoDetectCurrency,
      showOriginalPrice: showOriginalPrice ?? this.showOriginalPrice,
      showExchangeRate: showExchangeRate ?? this.showExchangeRate,
      showLastUpdated: showLastUpdated ?? this.showLastUpdated,
      favoriteCurrencies: favoriteCurrencies ?? this.favoriteCurrencies,
      recentlyUsedCurrencies: recentlyUsedCurrencies ?? this.recentlyUsedCurrencies,
    );
  }
  
  /// Converts to JSON
  Map<String, dynamic> toJson() {
    return {
      'preferredCurrency': preferredCurrency,
      'autoDetectCurrency': autoDetectCurrency,
      'showOriginalPrice': showOriginalPrice,
      'showExchangeRate': showExchangeRate,
      'showLastUpdated': showLastUpdated,
      'favoriteCurrencies': favoriteCurrencies,
      'recentlyUsedCurrencies': recentlyUsedCurrencies,
    };
  }
  
  /// Creates from JSON
  factory CurrencyPreferenceModel.fromJson(Map<String, dynamic> json) {
    return CurrencyPreferenceModel(
      preferredCurrency: json['preferredCurrency'] as String,
      autoDetectCurrency: json['autoDetectCurrency'] as bool? ?? true,
      showOriginalPrice: json['showOriginalPrice'] as bool? ?? true,
      showExchangeRate: json['showExchangeRate'] as bool? ?? true,
      showLastUpdated: json['showLastUpdated'] as bool? ?? true,
      favoriteCurrencies: (json['favoriteCurrencies'] as List?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      recentlyUsedCurrencies: (json['recentlyUsedCurrencies'] as List?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
    );
  }
  
  /// Default preferences with USD as the preferred currency
  static CurrencyPreferenceModel get defaultPreferences {
    return const CurrencyPreferenceModel(
      preferredCurrency: 'USD',
      autoDetectCurrency: true,
      showOriginalPrice: true,
      showExchangeRate: true,
      showLastUpdated: true,
      favoriteCurrencies: ['USD', 'EUR', 'GBP', 'JPY', 'CAD', 'AUD'],
      recentlyUsedCurrencies: ['USD', 'EUR', 'GBP'],
    );
  }
  
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
  
    return other is CurrencyPreferenceModel &&
      other.preferredCurrency == preferredCurrency &&
      other.autoDetectCurrency == autoDetectCurrency &&
      other.showOriginalPrice == showOriginalPrice &&
      other.showExchangeRate == showExchangeRate &&
      other.showLastUpdated == showLastUpdated &&
      listEquals(other.favoriteCurrencies, favoriteCurrencies) &&
      listEquals(other.recentlyUsedCurrencies, recentlyUsedCurrencies);
  }

  @override
  int get hashCode {
    return preferredCurrency.hashCode ^
      autoDetectCurrency.hashCode ^
      showOriginalPrice.hashCode ^
      showExchangeRate.hashCode ^
      showLastUpdated.hashCode ^
      favoriteCurrencies.hashCode ^
      recentlyUsedCurrencies.hashCode;
  }
}
