import 'package:flutter/material.dart';

/// A model representing a currency
class CurrencyModel {
  /// The currency code (e.g., USD, EUR, GBP)
  final String code;
  
  /// The currency name (e.g., US Dollar, Euro, British Pound)
  final String name;
  
  /// The currency symbol (e.g., $, €, £)
  final String symbol;
  
  /// The flag emoji for the currency's country
  final String flag;
  
  /// The number of decimal places typically used for this currency
  final int decimalPlaces;
  
  /// Whether this currency is a major global currency
  final bool isMajor;
  
  /// Whether this currency is a cryptocurrency
  final bool isCrypto;
  
  /// Creates a new currency model
  const CurrencyModel({
    required this.code,
    required this.name,
    required this.symbol,
    required this.flag,
    this.decimalPlaces = 2,
    this.isMajor = false,
    this.isCrypto = false,
  });
  
  /// Creates a copy with some fields replaced
  CurrencyModel copyWith({
    String? code,
    String? name,
    String? symbol,
    String? flag,
    int? decimalPlaces,
    bool? isMajor,
    bool? isCrypto,
  }) {
    return CurrencyModel(
      code: code ?? this.code,
      name: name ?? this.name,
      symbol: symbol ?? this.symbol,
      flag: flag ?? this.flag,
      decimalPlaces: decimalPlaces ?? this.decimalPlaces,
      isMajor: isMajor ?? this.isMajor,
      isCrypto: isCrypto ?? this.isCrypto,
    );
  }
  
  /// Converts to JSON
  Map<String, dynamic> toJson() {
    return {
      'code': code,
      'name': name,
      'symbol': symbol,
      'flag': flag,
      'decimalPlaces': decimalPlaces,
      'isMajor': isMajor,
      'isCrypto': isCrypto,
    };
  }
  
  /// Creates from JSON
  factory CurrencyModel.fromJson(Map<String, dynamic> json) {
    return CurrencyModel(
      code: json['code'] as String,
      name: json['name'] as String,
      symbol: json['symbol'] as String,
      flag: json['flag'] as String,
      decimalPlaces: json['decimalPlaces'] as int? ?? 2,
      isMajor: json['isMajor'] as bool? ?? false,
      isCrypto: json['isCrypto'] as bool? ?? false,
    );
  }
  
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
  
    return other is CurrencyModel &&
      other.code == code &&
      other.name == name &&
      other.symbol == symbol &&
      other.flag == flag &&
      other.decimalPlaces == decimalPlaces &&
      other.isMajor == isMajor &&
      other.isCrypto == isCrypto;
  }

  @override
  int get hashCode {
    return code.hashCode ^
      name.hashCode ^
      symbol.hashCode ^
      flag.hashCode ^
      decimalPlaces.hashCode ^
      isMajor.hashCode ^
      isCrypto.hashCode;
  }
  
  /// Format an amount according to this currency's rules
  String formatAmount(double amount) {
    return '$symbol${amount.toStringAsFixed(decimalPlaces)}';
  }
  
  /// Format an amount with the currency code
  String formatAmountWithCode(double amount) {
    return '${amount.toStringAsFixed(decimalPlaces)} $code';
  }
  
  /// Format an amount with the flag and code
  String formatAmountWithFlagAndCode(double amount) {
    return '$flag ${amount.toStringAsFixed(decimalPlaces)} $code';
  }
  
  /// Get the display name with flag
  String get displayName {
    return '$flag $name ($code)';
  }
  
  /// Get the short display name
  String get shortDisplayName {
    return '$flag $code';
  }
}
