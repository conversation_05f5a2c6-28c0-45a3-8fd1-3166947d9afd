/// A class representing a latitude and longitude coordinate
class LatLng {
  /// The latitude coordinate
  final double latitude;

  /// The longitude coordinate
  final double longitude;

  /// Creates a new LatLng instance
  const LatLng(this.latitude, this.longitude);

  /// Creates a LatLng from a JSON map
  factory LatLng.fromJson(Map<String, dynamic> json) {
    return LatLng(
      (json['latitude'] as num).toDouble(),
      (json['longitude'] as num).toDouble(),
    );
  }

  /// Converts this LatLng to a JSON map
  Map<String, dynamic> toJson() {
    return {
      'latitude': latitude,
      'longitude': longitude,
    };
  }

  /// Creates a copy of this LatLng with the given fields replaced with the new values
  LatLng copyWith({
    double? latitude,
    double? longitude,
  }) {
    return LatLng(
      latitude ?? this.latitude,
      longitude ?? this.longitude,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is LatLng &&
        other.latitude == latitude &&
        other.longitude == longitude;
  }

  @override
  int get hashCode => latitude.hashCode ^ longitude.hashCode;

  @override
  String toString() => 'LatLng(latitude: $latitude, longitude: $longitude)';
}

/// Extension methods for LatLng
extension LatLngExtension on LatLng {
  /// Calculates the distance between this LatLng and another LatLng in kilometers
  double distanceTo(LatLng other) {
    const double earthRadius = 6371; // in kilometers
    final double lat1Rad = _degreesToRadians(latitude);
    final double lat2Rad = _degreesToRadians(other.latitude);
    final double lon1Rad = _degreesToRadians(longitude);
    final double lon2Rad = _degreesToRadians(other.longitude);

    final double dLat = lat2Rad - lat1Rad;
    final double dLon = lon2Rad - lon1Rad;

    final double a =
        _haversin(dLat) + _haversin(dLon) * _cos(lat1Rad) * _cos(lat2Rad);
    final double c = 2 * _atan2(_sqrt(a), _sqrt(1 - a));

    return earthRadius * c;
  }

  /// Converts degrees to radians
  double _degreesToRadians(double degrees) {
    return degrees * (3.141592653589793 / 180);
  }

  /// Haversin function
  double _haversin(double value) {
    return _pow(_sin(value / 2), 2);
  }

  /// Sine function
  double _sin(double value) {
    return _sinImpl(value);
  }

  /// Cosine function
  double _cos(double value) {
    return _cosImpl(value);
  }

  /// Square root function
  double _sqrt(double value) {
    return _sqrtImpl(value);
  }

  /// Power function
  double _pow(double base, int exponent) {
    return _powImpl(base, exponent);
  }

  /// Arctangent function
  double _atan2(double y, double x) {
    return _atan2Impl(y, x);
  }

  // Implementation of math functions
  double _sinImpl(double value) {
    // Simple implementation of sine
    return value -
        (value * value * value) / 6 +
        (value * value * value * value * value) / 120;
  }

  double _cosImpl(double value) {
    // Simple implementation of cosine
    return 1 - (value * value) / 2 + (value * value * value * value) / 24;
  }

  double _sqrtImpl(double value) {
    // Simple implementation of square root using Newton's method
    if (value == 0) return 0;
    double x = value;
    double y = 1;
    double e = 0.000001; // Precision
    while (x - y > e) {
      x = (x + y) / 2;
      y = value / x;
    }
    return x;
  }

  double _powImpl(double base, int exponent) {
    // Simple implementation of power function
    double result = 1;
    for (int i = 0; i < exponent; i++) {
      result *= base;
    }
    return result;
  }

  double _atan2Impl(double y, double x) {
    // Simple implementation of atan2
    if (x > 0) {
      return _atanImpl(y / x);
    } else if (x < 0) {
      return y >= 0
          ? _atanImpl(y / x) + 3.141592653589793
          : _atanImpl(y / x) - 3.141592653589793;
    } else {
      return y > 0 ? 1.5707963267948966 : -1.5707963267948966;
    }
  }

  double _atanImpl(double value) {
    // Simple implementation of arctangent
    return value -
        (value * value * value) / 3 +
        (value * value * value * value * value) / 5;
  }
}
