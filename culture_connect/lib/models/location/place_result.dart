/// A class representing a place result from a location search
class PlaceResult {
  /// The unique identifier of the place
  final String id;

  /// The name of the place
  final String name;

  /// The address of the place
  final String address;

  /// The city of the place
  final String city;

  /// The country of the place
  final String country;

  /// The postal code of the place
  final String postalCode;

  /// The latitude of the place
  final double latitude;

  /// The longitude of the place
  final double longitude;

  /// The type of the place (e.g., airport, hotel, restaurant)
  final String? type;

  /// Creates a new place result
  const PlaceResult({
    required this.id,
    required this.name,
    required this.address,
    required this.city,
    required this.country,
    required this.postalCode,
    required this.latitude,
    required this.longitude,
    this.type,
  });

  /// Creates a place result from JSON
  factory PlaceResult.fromJson(Map<String, dynamic> json) {
    return PlaceResult(
      id: json['id'] as String,
      name: json['name'] as String,
      address: json['address'] as String,
      city: json['city'] as String,
      country: json['country'] as String,
      postalCode: json['postalCode'] as String,
      latitude: (json['latitude'] as num).toDouble(),
      longitude: (json['longitude'] as num).toDouble(),
      type: json['type'] as String?,
    );
  }

  /// Converts this place result to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'address': address,
      'city': city,
      'country': country,
      'postalCode': postalCode,
      'latitude': latitude,
      'longitude': longitude,
      'type': type,
    };
  }

  /// Creates a copy of this place result with the given fields replaced with the new values
  PlaceResult copyWith({
    String? id,
    String? name,
    String? address,
    String? city,
    String? country,
    String? postalCode,
    double? latitude,
    double? longitude,
    String? type,
  }) {
    return PlaceResult(
      id: id ?? this.id,
      name: name ?? this.name,
      address: address ?? this.address,
      city: city ?? this.city,
      country: country ?? this.country,
      postalCode: postalCode ?? this.postalCode,
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      type: type ?? this.type,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is PlaceResult &&
        other.id == id &&
        other.name == name &&
        other.address == address &&
        other.city == city &&
        other.country == country &&
        other.postalCode == postalCode &&
        other.latitude == latitude &&
        other.longitude == longitude &&
        other.type == type;
  }

  @override
  int get hashCode =>
      id.hashCode ^
      name.hashCode ^
      address.hashCode ^
      city.hashCode ^
      country.hashCode ^
      postalCode.hashCode ^
      latitude.hashCode ^
      longitude.hashCode ^
      (type?.hashCode ?? 0);

  @override
  String toString() {
    return 'PlaceResult(id: $id, name: $name, address: $address, city: $city, country: $country, postalCode: $postalCode, latitude: $latitude, longitude: $longitude, type: $type)';
  }
}
