/// A class representing a geographic location with latitude and longitude
class GeoLocation {
  /// The latitude coordinate
  final double latitude;

  /// The longitude coordinate
  final double longitude;

  /// The altitude (optional)
  final double? altitude;

  /// The accuracy of the location in meters (optional)
  final double? accuracy;

  /// Creates a new geographic location
  const GeoLocation({
    required this.latitude,
    required this.longitude,
    this.altitude,
    this.accuracy,
  });

  /// Creates a geographic location from JSON
  factory GeoLocation.fromJson(Map<String, dynamic> json) {
    return GeoLocation(
      latitude: (json['latitude'] as num).toDouble(),
      longitude: (json['longitude'] as num).toDouble(),
      altitude: json['altitude'] != null ? (json['altitude'] as num).toDouble() : null,
      accuracy: json['accuracy'] != null ? (json['accuracy'] as num).toDouble() : null,
    );
  }

  /// Converts this geographic location to JSON
  Map<String, dynamic> toJson() {
    return {
      'latitude': latitude,
      'longitude': longitude,
      if (altitude != null) 'altitude': altitude,
      if (accuracy != null) 'accuracy': accuracy,
    };
  }

  /// Creates a copy of this geographic location with the given fields replaced with the new values
  GeoLocation copyWith({
    double? latitude,
    double? longitude,
    double? altitude,
    double? accuracy,
  }) {
    return GeoLocation(
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      altitude: altitude ?? this.altitude,
      accuracy: accuracy ?? this.accuracy,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is GeoLocation &&
        other.latitude == latitude &&
        other.longitude == longitude &&
        other.altitude == altitude &&
        other.accuracy == accuracy;
  }

  @override
  int get hashCode =>
      latitude.hashCode ^
      longitude.hashCode ^
      altitude.hashCode ^
      accuracy.hashCode;

  @override
  String toString() =>
      'GeoLocation(latitude: $latitude, longitude: $longitude, altitude: $altitude, accuracy: $accuracy)';

  /// Calculates the distance between this location and another location in kilometers
  double distanceTo(GeoLocation other) {
    const double earthRadius = 6371; // in kilometers
    final double lat1Rad = _degreesToRadians(latitude);
    final double lat2Rad = _degreesToRadians(other.latitude);
    final double lon1Rad = _degreesToRadians(longitude);
    final double lon2Rad = _degreesToRadians(other.longitude);

    final double dLat = lat2Rad - lat1Rad;
    final double dLon = lon2Rad - lon1Rad;

    final double a = _sin(dLat / 2) * _sin(dLat / 2) +
        _cos(lat1Rad) * _cos(lat2Rad) * _sin(dLon / 2) * _sin(dLon / 2);
    final double c = 2 * _atan2(_sqrt(a), _sqrt(1 - a));

    return earthRadius * c;
  }

  /// Converts degrees to radians
  double _degreesToRadians(double degrees) {
    return degrees * (3.141592653589793 / 180);
  }

  /// Sine function
  double _sin(double x) {
    return x - (x * x * x) / 6 + (x * x * x * x * x) / 120;
  }

  /// Cosine function
  double _cos(double x) {
    return 1 - (x * x) / 2 + (x * x * x * x) / 24;
  }

  /// Square root function
  double _sqrt(double x) {
    if (x == 0) return 0;
    double t = x;
    double y = 1;
    double e = 0.000001; // Precision
    while (t - y > e) {
      t = (t + y) / 2;
      y = x / t;
    }
    return t;
  }

  /// Arctangent function
  double _atan2(double y, double x) {
    if (x > 0) return _atan(y / x);
    if (x < 0) return y >= 0 ? _atan(y / x) + 3.14159 : _atan(y / x) - 3.14159;
    return y > 0 ? 1.5708 : -1.5708;
  }

  /// Arctangent function
  double _atan(double x) {
    return x - (x * x * x) / 3 + (x * x * x * x * x) / 5;
  }
}
