import 'package:cloud_firestore/cloud_firestore.dart';

enum MessageType {
  text,
  image,
  video,
  audio,
  location,
  contact,
  file,
  system, // For system messages like "User X joined the group"
}

enum MessageStatus {
  sending,
  sent,
  delivered,
  read,
  failed,
  reported,
  blocked,
  pending, // For messages waiting to be sent when offline
}

/// Available reaction types for messages
enum ReactionType {
  like,
  love,
  laugh,
  wow,
  sad,
  angry,
}

/// Extension to get emoji for reaction type
extension ReactionTypeExtension on ReactionType {
  String get emoji {
    switch (this) {
      case ReactionType.like:
        return '👍';
      case ReactionType.love:
        return '❤️';
      case ReactionType.laugh:
        return '😂';
      case ReactionType.wow:
        return '😮';
      case ReactionType.sad:
        return '😢';
      case ReactionType.angry:
        return '😡';
    }
  }
}

class MessageModel {
  final String id;
  final String chatId;
  final String senderId;
  final String recipientId; // For direct messages; empty for group chats
  final String text;
  final DateTime timestamp;
  final MessageStatus status;
  final MessageType type;
  final String mediaUrl;
  final Map<String, dynamic>? metadata;
  final bool isTranslated;
  final String originalLanguage;
  final Map<String, String>? translations;
  final Map<String, Map<String, String>>? reactions;
  final bool isGroupMessage; // Whether this is a group message
  final List<String>? mentionedUserIds; // Users mentioned in the message
  final Map<String, MessageStatus>?
      deliveryStatus; // Delivery status for each recipient in a group
  final bool isForwarded; // Whether this message is forwarded
  final String? originalSenderId; // Original sender ID for forwarded messages

  MessageModel({
    required this.id,
    required this.chatId,
    required this.senderId,
    required this.recipientId,
    required this.text,
    required this.timestamp,
    required this.status,
    required this.type,
    this.mediaUrl = '',
    this.metadata,
    this.isTranslated = false,
    this.originalLanguage = 'en',
    this.translations,
    this.reactions,
    this.isGroupMessage = false,
    this.mentionedUserIds,
    this.deliveryStatus,
    this.isForwarded = false,
    this.originalSenderId,
  });

  factory MessageModel.fromJson(Map<String, dynamic> json) {
    // Parse delivery status if present
    Map<String, MessageStatus>? deliveryStatus;
    if (json['deliveryStatus'] != null) {
      deliveryStatus = (json['deliveryStatus'] as Map).map(
        (key, value) => MapEntry(
          key as String,
          MessageStatus.values.firstWhere(
            (e) => e.toString() == 'MessageStatus.$value',
            orElse: () => MessageStatus.sent,
          ),
        ),
      );
    }

    return MessageModel(
      id: json['id'] as String,
      chatId: json['chatId'] as String,
      senderId: json['senderId'] as String,
      recipientId: json['recipientId'] as String? ?? '',
      text: json['text'] as String,
      timestamp: (json['timestamp'] as Timestamp).toDate(),
      status: MessageStatus.values.firstWhere(
        (e) => e.toString() == 'MessageStatus.${json['status']}',
        orElse: () => MessageStatus.sent,
      ),
      type: MessageType.values.firstWhere(
        (e) => e.toString() == 'MessageType.${json['type']}',
        orElse: () => MessageType.text,
      ),
      mediaUrl: json['mediaUrl'] as String? ?? '',
      metadata: json['metadata'] as Map<String, dynamic>?,
      isTranslated: json['isTranslated'] as bool? ?? false,
      originalLanguage: json['originalLanguage'] as String? ?? 'en',
      translations: json['translations'] != null
          ? Map<String, String>.from(json['translations'] as Map)
          : null,
      reactions: json['reactions'] != null
          ? (json['reactions'] as Map).map(
              (key, value) => MapEntry(
                key as String,
                (value as Map).map(
                  (k, v) => MapEntry(k as String, v as String),
                ),
              ),
            )
          : null,
      isGroupMessage: json['isGroupMessage'] as bool? ?? false,
      mentionedUserIds: json['mentionedUserIds'] != null
          ? List<String>.from(json['mentionedUserIds'] as List)
          : null,
      deliveryStatus: deliveryStatus,
      isForwarded: json['isForwarded'] as bool? ?? false,
      originalSenderId: json['originalSenderId'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    // Convert delivery status to a map of strings
    Map<String, String>? deliveryStatusJson;
    if (deliveryStatus != null) {
      deliveryStatusJson = deliveryStatus!.map(
        (key, value) => MapEntry(key, value.toString().split('.').last),
      );
    }

    return {
      'id': id,
      'chatId': chatId,
      'senderId': senderId,
      'recipientId': recipientId,
      'text': text,
      'timestamp': timestamp,
      'status': status.toString().split('.').last,
      'type': type.toString().split('.').last,
      'mediaUrl': mediaUrl,
      'metadata': metadata,
      'isTranslated': isTranslated,
      'originalLanguage': originalLanguage,
      'translations': translations,
      'reactions': reactions,
      'isGroupMessage': isGroupMessage,
      'mentionedUserIds': mentionedUserIds,
      'deliveryStatus': deliveryStatusJson,
      'isForwarded': isForwarded,
      'originalSenderId': originalSenderId,
    };
  }

  MessageModel copyWith({
    String? id,
    String? chatId,
    String? senderId,
    String? recipientId,
    String? text,
    DateTime? timestamp,
    MessageStatus? status,
    MessageType? type,
    String? mediaUrl,
    Map<String, dynamic>? metadata,
    bool? isTranslated,
    String? originalLanguage,
    Map<String, String>? translations,
    Map<String, Map<String, String>>? reactions,
    bool? isGroupMessage,
    List<String>? mentionedUserIds,
    Map<String, MessageStatus>? deliveryStatus,
    bool? isForwarded,
    String? originalSenderId,
  }) {
    return MessageModel(
      id: id ?? this.id,
      chatId: chatId ?? this.chatId,
      senderId: senderId ?? this.senderId,
      recipientId: recipientId ?? this.recipientId,
      text: text ?? this.text,
      timestamp: timestamp ?? this.timestamp,
      status: status ?? this.status,
      type: type ?? this.type,
      mediaUrl: mediaUrl ?? this.mediaUrl,
      metadata: metadata ?? this.metadata,
      isTranslated: isTranslated ?? this.isTranslated,
      originalLanguage: originalLanguage ?? this.originalLanguage,
      translations: translations ?? this.translations,
      reactions: reactions ?? this.reactions,
      isGroupMessage: isGroupMessage ?? this.isGroupMessage,
      mentionedUserIds: mentionedUserIds ?? this.mentionedUserIds,
      deliveryStatus: deliveryStatus ?? this.deliveryStatus,
      isForwarded: isForwarded ?? this.isForwarded,
      originalSenderId: originalSenderId ?? this.originalSenderId,
    );
  }
}

class ChatModel {
  final String id;
  final List<String> participants;
  final DateTime createdAt;
  final DateTime lastMessageAt;
  final String lastMessageText;
  final String lastMessageSenderId;
  final int unreadCount;
  final bool isActive;

  ChatModel({
    required this.id,
    required this.participants,
    required this.createdAt,
    required this.lastMessageAt,
    required this.lastMessageText,
    required this.lastMessageSenderId,
    required this.unreadCount,
    required this.isActive,
  });

  factory ChatModel.fromJson(Map<String, dynamic> json) {
    return ChatModel(
      id: json['id'] as String,
      participants: List<String>.from(json['participants'] as List),
      createdAt: (json['createdAt'] as Timestamp).toDate(),
      lastMessageAt: (json['lastMessageAt'] as Timestamp).toDate(),
      lastMessageText: json['lastMessageText'] as String? ?? '',
      lastMessageSenderId: json['lastMessageSenderId'] as String? ?? '',
      unreadCount: json['unreadCount'] as int? ?? 0,
      isActive: json['isActive'] as bool? ?? true,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'participants': participants,
      'createdAt': createdAt,
      'lastMessageAt': lastMessageAt,
      'lastMessageText': lastMessageText,
      'lastMessageSenderId': lastMessageSenderId,
      'unreadCount': unreadCount,
      'isActive': isActive,
    };
  }

  ChatModel copyWith({
    String? id,
    List<String>? participants,
    DateTime? createdAt,
    DateTime? lastMessageAt,
    String? lastMessageText,
    String? lastMessageSenderId,
    int? unreadCount,
    bool? isActive,
  }) {
    return ChatModel(
      id: id ?? this.id,
      participants: participants ?? this.participants,
      createdAt: createdAt ?? this.createdAt,
      lastMessageAt: lastMessageAt ?? this.lastMessageAt,
      lastMessageText: lastMessageText ?? this.lastMessageText,
      lastMessageSenderId: lastMessageSenderId ?? this.lastMessageSenderId,
      unreadCount: unreadCount ?? this.unreadCount,
      isActive: isActive ?? this.isActive,
    );
  }
}
