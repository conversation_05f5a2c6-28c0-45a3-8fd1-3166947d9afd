import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:culture_connect/services/ar_backend_service.dart';
import 'package:culture_connect/models/landmark.dart';

/// Provider for the AR backend service
final arBackendServiceProvider = Provider<ARBackendService>((ref) {
  return ARBackendService();
});

/// Provider for nearby landmarks
final nearbyLandmarksProvider = FutureProvider.family<List<Landmark>, ({double latitude, double longitude, double radius})>((ref, params) async {
  final arBackendService = ref.watch(arBackendServiceProvider);
  return arBackendService.getLandmarks(
    latitude: params.latitude,
    longitude: params.longitude,
    radius: params.radius,
  );
});

/// Provider for a specific landmark
final landmarkProvider = FutureProvider.family<Landmark?, String>((ref, id) async {
  final arBackendService = ref.watch(arBackendServiceProvider);
  final landmarks = await arBackendService.getLandmarks(
    latitude: 0,
    longitude: 0,
    radius: 10000,
  );
  
  try {
    return landmarks.firstWhere((landmark) => landmark.id == id);
  } catch (e) {
    return null;
  }
});

/// Provider for AR model availability
final arModelAvailabilityProvider = FutureProvider.family<bool, String>((ref, landmarkId) async {
  final arBackendService = ref.watch(arBackendServiceProvider);
  final arModel = await arBackendService.getARModel(landmarkId);
  return arModel != null;
});
