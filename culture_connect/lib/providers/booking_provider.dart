import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:culture_connect/models/booking_model.dart';
import 'package:culture_connect/services/booking_service.dart'
    hide BookingModel;

/// Provider for the BookingService.
final bookingServiceProvider = Provider<BookingService>((ref) {
  return BookingService();
});

/// Provider for all bookings of the current guide.
final guideBookingsProvider = FutureProvider<List<BookingModel>>((ref) async {
  final bookingService = ref.watch(bookingServiceProvider);
  return bookingService.getGuideBookings();
});

/// Provider for upcoming bookings of the current guide.
final upcomingBookingsProvider =
    FutureProvider<List<BookingModel>>((ref) async {
  final bookingService = ref.watch(bookingServiceProvider);
  return bookingService.getUpcomingBookings();
});

/// Provider for past bookings of the current guide.
final pastBookingsProvider = FutureProvider<List<BookingModel>>((ref) async {
  final bookingService = ref.watch(bookingServiceProvider);
  return bookingService.getPastBookings();
});

/// Provider for pending bookings of the current guide.
final pendingBookingsProvider = FutureProvider<List<BookingModel>>((ref) async {
  final bookingService = ref.watch(bookingServiceProvider);
  return bookingService.getPendingBookings();
});

/// Provider for a specific booking by ID.
final bookingByIdProvider =
    FutureProvider.family<BookingModel?, String>((ref, bookingId) async {
  final bookingService = ref.watch(bookingServiceProvider);
  return bookingService.getBookingById(bookingId);
});

/// Notifier for managing bookings.
class BookingNotifier extends StateNotifier<AsyncValue<BookingModel?>> {
  final BookingService _bookingService;

  BookingNotifier(this._bookingService) : super(const AsyncValue.loading());

  /// Load a booking by ID.
  Future<void> loadBooking(String bookingId) async {
    try {
      state = const AsyncValue.loading();
      final booking = await _bookingService.getBookingById(bookingId);
      state = AsyncValue.data(booking);
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }

  /// Approve a booking.
  Future<void> approveBooking(String bookingId) async {
    try {
      state = const AsyncValue.loading();
      final booking = await _bookingService.approveBooking(bookingId);
      state = AsyncValue.data(booking);
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }

  /// Reject a booking.
  Future<void> rejectBooking(String bookingId, String rejectionReason) async {
    try {
      state = const AsyncValue.loading();
      final booking =
          await _bookingService.rejectBooking(bookingId, rejectionReason);
      state = AsyncValue.data(booking);
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }

  /// Mark a booking as completed.
  Future<void> completeBooking(String bookingId) async {
    try {
      state = const AsyncValue.loading();
      final booking = await _bookingService.completeBooking(bookingId);
      state = AsyncValue.data(booking);
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }

  /// Mark a booking as no-show.
  Future<void> markAsNoShow(String bookingId) async {
    try {
      state = const AsyncValue.loading();
      final booking = await _bookingService.markAsNoShow(bookingId);
      state = AsyncValue.data(booking);
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }

  /// Process a refund for a booking.
  Future<void> processRefund(
    String bookingId,
    double refundAmount,
    String refundReason,
  ) async {
    try {
      state = const AsyncValue.loading();
      final booking = await _bookingService.processRefund(
        bookingId,
        refundAmount,
        refundReason,
      );
      state = AsyncValue.data(booking);
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }

  /// Send a notification to a customer.
  Future<void> sendCustomerNotification(
    String bookingId,
    String message,
  ) async {
    try {
      await _bookingService.sendCustomerNotification(bookingId, message);
    } catch (e) {
      rethrow;
    }
  }

  /// Reset the state to null.
  void reset() {
    state = const AsyncValue.data(null);
  }
}

/// Provider for the BookingNotifier.
final bookingNotifierProvider =
    StateNotifierProvider<BookingNotifier, AsyncValue<BookingModel?>>((ref) {
  final bookingService = ref.watch(bookingServiceProvider);
  return BookingNotifier(bookingService);
});

/// Provider for filtered bookings based on status.
final filteredBookingsProvider =
    Provider.family<List<BookingModel>, BookingStatus?>((ref, status) {
  final bookingsAsync = ref.watch(guideBookingsProvider);

  return bookingsAsync.when(
    data: (bookings) {
      if (status == null) {
        return bookings;
      }
      return bookings.where((booking) => booking.status == status).toList();
    },
    loading: () => [],
    error: (_, __) => [],
  );
});

/// Provider for booking statistics.
final bookingStatsProvider = Provider((ref) {
  final bookingsAsync = ref.watch(guideBookingsProvider);

  return bookingsAsync.when(
    data: (bookings) {
      final totalBookings = bookings.length;
      final pendingBookings =
          bookings.where((b) => b.status == BookingStatus.pending).length;
      final approvedBookings =
          bookings.where((b) => b.status == BookingStatus.approved).length;
      final completedBookings =
          bookings.where((b) => b.status == BookingStatus.completed).length;
      final cancelledBookings = bookings
          .where((b) =>
              b.status == BookingStatus.cancelled ||
              b.status == BookingStatus.rejected)
          .length;
      final noShowBookings =
          bookings.where((b) => b.status == BookingStatus.noShow).length;

      // Calculate revenue from completed bookings
      double totalRevenue = 0.0;
      for (final booking in bookings) {
        if (booking.status == BookingStatus.completed) {
          totalRevenue += booking.payment.amount;
        }
      }

      // Calculate total refunds
      double totalRefunds = 0.0;
      for (final booking in bookings) {
        if (booking.payment.refundAmount != null) {
          totalRefunds += booking.payment.refundAmount!;
        }
      }

      return {
        'totalBookings': totalBookings,
        'pendingBookings': pendingBookings,
        'approvedBookings': approvedBookings,
        'completedBookings': completedBookings,
        'cancelledBookings': cancelledBookings,
        'noShowBookings': noShowBookings,
        'totalRevenue': totalRevenue,
        'totalRefunds': totalRefunds,
        'netRevenue': totalRevenue - totalRefunds,
      };
    },
    loading: () => {
      'totalBookings': 0,
      'pendingBookings': 0,
      'approvedBookings': 0,
      'completedBookings': 0,
      'cancelledBookings': 0,
      'noShowBookings': 0,
      'totalRevenue': 0.0,
      'totalRefunds': 0.0,
      'netRevenue': 0.0,
    },
    error: (_, __) => {
      'totalBookings': 0,
      'pendingBookings': 0,
      'approvedBookings': 0,
      'completedBookings': 0,
      'cancelledBookings': 0,
      'noShowBookings': 0,
      'totalRevenue': 0.0,
      'totalRefunds': 0.0,
      'netRevenue': 0.0,
    },
  );
});
