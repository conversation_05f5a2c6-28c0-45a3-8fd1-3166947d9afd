import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:culture_connect/models/translation/message_translation_metadata.dart';
import 'package:culture_connect/models/translation/translation_slang_idiom.dart';
import 'package:culture_connect/services/voice_translation/slang_idiom_service.dart';
import 'package:culture_connect/providers/common/reader.dart';

/// Provider for the slang and idiom service
final slangIdiomServiceProvider = Provider<SlangIdiomService>((ref) {
  throw UnimplementedError('SlangIdiomService provider not initialized');
});

/// Provider for slang and idiom events stream
final slangIdiomEventsProvider = StreamProvider<String>((ref) {
  final service = ref.watch(slangIdiomServiceProvider);
  return service.slangIdiomEvents;
});

/// Provider for whether to use slang and idiom detection
final useSlangIdiomDetectionProvider = StateProvider<bool>((ref) {
  final service = ref.watch(slangIdiomServiceProvider);
  return service.useSlangIdiomDetection;
});

/// Provider for whether to show potentially offensive content
final showPotentiallyOffensiveContentProvider = StateProvider<bool>((ref) {
  final service = ref.watch(slangIdiomServiceProvider);
  return service.showPotentiallyOffensiveContent;
});

/// Provider for slang and idiom information for a specific message
final messageSlangIdiomProvider =
    FutureProvider.family<TranslationSlangIdiom, MessageTranslationMetadata>(
        (ref, metadata) async {
  final service = ref.watch(slangIdiomServiceProvider);

  // If slang and idiom information is already available, return it
  if (metadata.slangIdiom != null) {
    return metadata.slangIdiom!;
  }

  // Otherwise, fetch it
  return service.getSlangIdiom(
    text: metadata.translatedText,
    sourceLanguage: metadata.sourceLanguage,
    targetLanguage: metadata.targetLanguage,
  );
});

/// Notifier for slang and idiom actions
class SlangIdiomNotifier extends StateNotifier<AsyncValue<void>> {
  final SlangIdiomService _service;
  final Reader _read;

  SlangIdiomNotifier(this._service, this._read)
      : super(const AsyncValue.data(null));

  /// Set whether to use slang and idiom detection
  void setUseSlangIdiomDetection(bool value) {
    _service.setUseSlangIdiomDetection(value);
    _read(useSlangIdiomDetectionProvider.notifier).state = value;
  }

  /// Set whether to show potentially offensive content
  void setShowPotentiallyOffensiveContent(bool value) {
    _service.setShowPotentiallyOffensiveContent(value);
    _read(showPotentiallyOffensiveContentProvider.notifier).state = value;
  }

  /// Get slang and idiom information for a message
  Future<TranslationSlangIdiom> getSlangIdiom(
      MessageTranslationMetadata metadata) async {
    state = const AsyncValue.loading();

    try {
      final slangIdiom = await _service.getSlangIdiom(
        text: metadata.translatedText,
        sourceLanguage: metadata.sourceLanguage,
        targetLanguage: metadata.targetLanguage,
      );

      state = const AsyncValue.data(null);
      return slangIdiom;
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
      return TranslationSlangIdiom.empty(
        sourceLanguage: metadata.sourceLanguage,
        targetLanguage: metadata.targetLanguage,
      );
    }
  }

  /// Clear the slang and idiom cache
  void clearCache() {
    _service.clearCache();
  }
}

/// Provider for the slang and idiom notifier
final slangIdiomNotifierProvider =
    StateNotifierProvider<SlangIdiomNotifier, AsyncValue<void>>((ref) {
  final service = ref.watch(slangIdiomServiceProvider);
  return SlangIdiomNotifier(service, ref.read);
});
