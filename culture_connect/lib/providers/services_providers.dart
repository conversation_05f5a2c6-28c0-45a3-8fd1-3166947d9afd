// Package imports
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

// Project imports
import 'package:culture_connect/services/location_service.dart';
import 'package:culture_connect/services/logging_service.dart';
import 'package:culture_connect/services/notification_service.dart'
    hide notificationServiceProvider;
import 'package:culture_connect/services/sharing_service.dart';
import 'package:culture_connect/services/storage_service.dart';
import 'package:culture_connect/services/travel/flight_search_service.dart';
import 'package:culture_connect/services/travel/hotel_review_service.dart';
import 'package:culture_connect/services/travel/price_alert_service.dart';
import 'package:culture_connect/services/travel/travel_services_service.dart';

/// Provider for the logging service
final loggingServiceProvider = Provider<LoggingService>((ref) {
  return LoggingService();
});

/// Provider for the storage service
final storageServiceProvider = Provider<StorageService>((ref) {
  final loggingService = ref.watch(loggingServiceProvider);
  return StorageService(
    loggingService: loggingService,
  );
});

/// Provider for the location service
final locationServiceProvider = Provider<LocationService>((ref) {
  final loggingService = ref.watch(loggingServiceProvider);
  return LocationService(
    loggingService: loggingService,
  );
});

/// Provider for the sharing service
final sharingServiceProvider = Provider<SharingService>((ref) {
  return SharingService();
});

/// Provider for the notification service
final notificationServiceProvider = Provider<NotificationService>((ref) {
  return NotificationService();
});

/// Provider for the travel services service
final travelServicesServiceProvider = Provider<TravelServicesService>((ref) {
  return TravelServicesService();
});

/// Provider for the hotel review service
final hotelReviewServiceProvider = Provider<HotelReviewService>((ref) {
  final firestore = FirebaseFirestore.instance;
  final storageService = ref.watch(storageServiceProvider);
  final loggingService = ref.watch(loggingServiceProvider);

  return HotelReviewService(
    firestore: firestore,
    storageService: storageService,
    loggingService: loggingService,
  );
});

/// Provider for the flight search service
final flightSearchServiceProvider = Provider<FlightSearchService>((ref) {
  final firestore = FirebaseFirestore.instance;
  final loggingService = ref.watch(loggingServiceProvider);

  return FlightSearchService(
    firestore: firestore,
    loggingService: loggingService,
  );
});

/// Provider for the price alert service
final priceAlertServiceProvider =
    Provider.family<PriceAlertService, String>((ref, userId) {
  final firestore = FirebaseFirestore.instance;
  final loggingService = ref.watch(loggingServiceProvider);
  final notificationService = ref.watch(notificationServiceProvider);

  return PriceAlertService(
    firestore: firestore,
    loggingService: loggingService,
    notificationService: notificationService,
    userId: userId,
  );
});
