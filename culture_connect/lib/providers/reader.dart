import 'package:flutter_riverpod/flutter_riverpod.dart';

/// A function that allows reading from providers.
///
/// This is a type alias for the `Reader` function provided by Riverpod.
/// It's used in notifiers to access other providers.
///
/// Example:
/// ```dart
/// class MyNotifier extends StateNotifier<AsyncValue<void>> {
///   final Reader _read;
///   
///   MyNotifier(this._read) : super(const AsyncValue.data(null));
///   
///   void doSomething() {
///     // Access another provider
///     final value = _read(someProvider);
///     // Do something with the value
///   }
/// }
/// ```
typedef Reader = T Function<T>(ProviderListenable<T> provider);
