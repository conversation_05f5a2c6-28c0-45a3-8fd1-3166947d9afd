import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:culture_connect/models/translation/message_translation_metadata.dart';
import 'package:culture_connect/models/translation/translation_cultural_context.dart';
import 'package:culture_connect/services/voice_translation/cultural_context_service.dart';
import 'package:culture_connect/providers/common/reader.dart';

/// Provider for the cultural context service
final culturalContextServiceProvider = Provider<CulturalContextService>((ref) {
  throw UnimplementedError('CulturalContextService provider not initialized');
});

/// Provider for cultural context events stream
final contextEventsProvider = StreamProvider<String>((ref) {
  final service = ref.watch(culturalContextServiceProvider);
  return service.contextEvents;
});

/// Provider for whether to use cultural context awareness
final useCulturalContextProvider = StateProvider<bool>((ref) {
  final service = ref.watch(culturalContextServiceProvider);
  return service.useCulturalContext;
});

/// Provider for whether to show sensitive content
final showSensitiveContentProvider = StateProvider<bool>((ref) {
  final service = ref.watch(culturalContextServiceProvider);
  return service.showSensitiveContent;
});

/// Provider for cultural context for a specific message
final messageCulturalContextProvider = FutureProvider.family<
    TranslationCulturalContext,
    MessageTranslationMetadata>((ref, metadata) async {
  final service = ref.watch(culturalContextServiceProvider);

  // If cultural context is already available, return it
  if (metadata.culturalContext != null) {
    return metadata.culturalContext!;
  }

  // Otherwise, fetch it
  return service.getCulturalContext(
    text: metadata.translatedText,
    sourceLanguage: metadata.sourceLanguage,
    targetLanguage: metadata.targetLanguage,
  );
});

/// Notifier for cultural context actions
class CulturalContextNotifier extends StateNotifier<AsyncValue<void>> {
  final CulturalContextService _service;
  final Reader _read;

  CulturalContextNotifier(this._service, this._read)
      : super(const AsyncValue.data(null));

  /// Set whether to use cultural context awareness
  void setUseCulturalContext(bool value) {
    _service.setUseCulturalContext(value);
    _read(useCulturalContextProvider.notifier).state = value;
  }

  /// Set whether to show sensitive content
  void setShowSensitiveContent(bool value) {
    _service.setShowSensitiveContent(value);
    _read(showSensitiveContentProvider.notifier).state = value;
  }

  /// Get cultural context for a message
  Future<TranslationCulturalContext> getCulturalContext(
      MessageTranslationMetadata metadata) async {
    state = const AsyncValue.loading();

    try {
      final context = await _service.getCulturalContext(
        text: metadata.translatedText,
        sourceLanguage: metadata.sourceLanguage,
        targetLanguage: metadata.targetLanguage,
      );

      state = const AsyncValue.data(null);
      return context;
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
      return TranslationCulturalContext.empty(
        sourceLanguage: metadata.sourceLanguage,
        targetLanguage: metadata.targetLanguage,
      );
    }
  }

  /// Clear the cultural context cache
  void clearCache() {
    _service.clearCache();
  }
}

/// Provider for the cultural context notifier
final culturalContextNotifierProvider =
    StateNotifierProvider<CulturalContextNotifier, AsyncValue<void>>((ref) {
  final service = ref.watch(culturalContextServiceProvider);
  return CulturalContextNotifier(service, ref.read);
});
