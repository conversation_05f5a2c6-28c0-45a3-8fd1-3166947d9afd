import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:culture_connect/models/translation/message_translation_metadata.dart';
import 'package:culture_connect/models/translation/translation_pronunciation.dart';
import 'package:culture_connect/services/voice_translation/pronunciation_service.dart';
import 'package:culture_connect/providers/common/reader.dart';

/// Provider for the pronunciation service
final pronunciationServiceProvider = Provider<PronunciationService>((ref) {
  throw UnimplementedError('PronunciationService provider not initialized');
});

/// Provider for pronunciation events stream
final pronunciationEventsProvider = StreamProvider<String>((ref) {
  final service = ref.watch(pronunciationServiceProvider);
  return service.pronunciationEvents;
});

/// Provider for whether to use pronunciation guidance
final usePronunciationGuidanceProvider = StateProvider<bool>((ref) {
  final service = ref.watch(pronunciationServiceProvider);
  return service.usePronunciationGuidance;
});

/// Provider for whether to use IPA notation
final useIpaNotationProvider = StateProvider<bool>((ref) {
  final service = ref.watch(pronunciationServiceProvider);
  return service.useIpaNotation;
});

/// Provider for whether to use simplified phonetics
final useSimplifiedPhoneticsProvider = StateProvider<bool>((ref) {
  final service = ref.watch(pronunciationServiceProvider);
  return service.useSimplifiedPhonetics;
});

/// Provider for whether to use syllable breakdown
final useSyllableBreakdownProvider = StateProvider<bool>((ref) {
  final service = ref.watch(pronunciationServiceProvider);
  return service.useSyllableBreakdown;
});

/// Provider for whether to show difficult pronunciations only
final showDifficultOnlyProvider = StateProvider<bool>((ref) {
  final service = ref.watch(pronunciationServiceProvider);
  return service.showDifficultOnly;
});

/// Provider for whether to auto-play pronunciation audio
final autoPlayPronunciationProvider = StateProvider<bool>((ref) {
  final service = ref.watch(pronunciationServiceProvider);
  return service.autoPlayPronunciation;
});

/// Provider for pronunciation information for a specific message
final messagePronunciationProvider =
    FutureProvider.family<TranslationPronunciation, MessageTranslationMetadata>(
        (ref, metadata) async {
  final service = ref.watch(pronunciationServiceProvider);

  // If pronunciation information is already available, return it
  if (metadata.pronunciation != null) {
    return metadata.pronunciation!;
  }

  // Otherwise, fetch it
  return service.getPronunciation(
    text: metadata.translatedText,
    sourceLanguage: metadata.sourceLanguage,
    targetLanguage: metadata.targetLanguage,
  );
});

/// Notifier for pronunciation actions
class PronunciationNotifier extends StateNotifier<AsyncValue<void>> {
  final PronunciationService _service;
  final Reader _read;

  PronunciationNotifier(this._service, this._read)
      : super(const AsyncValue.data(null));

  /// Set whether to use pronunciation guidance
  Future<void> setUsePronunciationGuidance(bool value) async {
    state = const AsyncValue.loading();

    try {
      await _service.setUsePronunciationGuidance(value);
      _read(usePronunciationGuidanceProvider.notifier).state = value;
      state = const AsyncValue.data(null);
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }

  /// Set whether to use IPA notation
  Future<void> setUseIpaNotation(bool value) async {
    state = const AsyncValue.loading();

    try {
      await _service.setUseIpaNotation(value);
      _read(useIpaNotationProvider.notifier).state = value;
      state = const AsyncValue.data(null);
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }

  /// Set whether to use simplified phonetics
  Future<void> setUseSimplifiedPhonetics(bool value) async {
    state = const AsyncValue.loading();

    try {
      await _service.setUseSimplifiedPhonetics(value);
      _read(useSimplifiedPhoneticsProvider.notifier).state = value;
      state = const AsyncValue.data(null);
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }

  /// Set whether to use syllable breakdown
  Future<void> setUseSyllableBreakdown(bool value) async {
    state = const AsyncValue.loading();

    try {
      await _service.setUseSyllableBreakdown(value);
      _read(useSyllableBreakdownProvider.notifier).state = value;
      state = const AsyncValue.data(null);
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }

  /// Set whether to show difficult pronunciations only
  Future<void> setShowDifficultOnly(bool value) async {
    state = const AsyncValue.loading();

    try {
      await _service.setShowDifficultOnly(value);
      _read(showDifficultOnlyProvider.notifier).state = value;
      state = const AsyncValue.data(null);
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }

  /// Set whether to auto-play pronunciation audio
  Future<void> setAutoPlayPronunciation(bool value) async {
    state = const AsyncValue.loading();

    try {
      await _service.setAutoPlayPronunciation(value);
      _read(autoPlayPronunciationProvider.notifier).state = value;
      state = const AsyncValue.data(null);
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }

  /// Get pronunciation information for a message
  Future<TranslationPronunciation> getPronunciation(
      MessageTranslationMetadata metadata) async {
    state = const AsyncValue.loading();

    try {
      final pronunciation = await _service.getPronunciation(
        text: metadata.translatedText,
        sourceLanguage: metadata.sourceLanguage,
        targetLanguage: metadata.targetLanguage,
      );

      state = const AsyncValue.data(null);
      return pronunciation;
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
      return TranslationPronunciation.empty(
        sourceLanguage: metadata.sourceLanguage,
        targetLanguage: metadata.targetLanguage,
      );
    }
  }

  /// Play pronunciation audio
  Future<void> playPronunciationAudio(String audioPath) async {
    state = const AsyncValue.loading();

    try {
      await _service.playPronunciationAudio(audioPath);
      state = const AsyncValue.data(null);
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }

  /// Stop audio playback
  Future<void> stopAudio() async {
    state = const AsyncValue.loading();

    try {
      await _service.stopAudio();
      state = const AsyncValue.data(null);
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }

  /// Clear the pronunciation cache
  void clearCache() {
    _service.clearCache();
  }
}

/// Provider for the pronunciation notifier
final pronunciationNotifierProvider =
    StateNotifierProvider<PronunciationNotifier, AsyncValue<void>>((ref) {
  final service = ref.watch(pronunciationServiceProvider);
  return PronunciationNotifier(service, ref.read);
});
