import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:culture_connect/models/message_model.dart';
import 'package:culture_connect/models/translation/language_model.dart';
import 'package:culture_connect/models/translation/message_translation_metadata.dart';
import 'package:culture_connect/providers/voice_translation_provider.dart';
import 'package:culture_connect/services/voice_translation/message_translation_service.dart';
import 'package:culture_connect/providers/common/reader.dart';

/// Provider for the message translation service
final messageTranslationServiceProvider =
    Provider<MessageTranslationService>((ref) {
  throw UnimplementedError(
      'MessageTranslationService provider not initialized');
});

/// Provider for whether to auto-translate incoming messages
final autoTranslateMessagesProvider = StateProvider<bool>((ref) => false);

/// Provider for the default target language for message translation
final messageTranslationTargetLanguageProvider =
    StateProvider<LanguageModel>((ref) {
  return ref.watch(targetLanguageProvider);
});

/// Provider for the translation status of a specific message
final messageTranslationStatusProvider =
    Provider.family<bool, String>((ref, messageId) {
  // This would typically check if the message has been translated
  // For now, we'll return false
  return false;
});

/// Provider for the translation metadata of a specific message
final messageTranslationMetadataProvider = FutureProvider.family<
    MessageTranslationMetadata?,
    MessageTranslationRequest>((ref, request) async {
  final service = ref.watch(messageTranslationServiceProvider);
  final message = request.message;
  final targetLanguage = request.targetLanguage;

  try {
    return await service.translateMessage(message, targetLanguage);
  } catch (e) {
    return null;
  }
});

/// Provider for the translation events stream
final messageTranslationEventsProvider = StreamProvider<String>((ref) {
  final service = ref.watch(messageTranslationServiceProvider);
  return service.translationEvents;
});

/// A request for message translation
class MessageTranslationRequest {
  /// The message to translate
  final MessageModel message;

  /// The target language code
  final String targetLanguage;

  /// Creates a new message translation request
  const MessageTranslationRequest({
    required this.message,
    required this.targetLanguage,
  });

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is MessageTranslationRequest &&
        other.message.id == message.id &&
        other.targetLanguage == targetLanguage;
  }

  @override
  int get hashCode => message.id.hashCode ^ targetLanguage.hashCode;
}

/// Notifier for message translation actions
class MessageTranslationNotifier extends StateNotifier<AsyncValue<void>> {
  final MessageTranslationService _service;

  MessageTranslationNotifier(this._service, Reader _)
      : super(const AsyncValue.data(null));

  /// Translate a message
  Future<MessageTranslationMetadata?> translateMessage(
    MessageModel message,
    String targetLanguage,
  ) async {
    state = const AsyncValue.loading();

    try {
      final metadata = await _service.translateMessage(message, targetLanguage);
      state = const AsyncValue.data(null);
      return metadata;
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
      return null;
    }
  }

  /// Play translated audio
  Future<void> playTranslatedAudio(String audioPath) async {
    try {
      await _service.playTranslatedAudio(audioPath);
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }

  /// Stop audio playback
  Future<void> stopAudio() async {
    try {
      await _service.stopAudio();
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }

  /// Rate translation quality
  Future<MessageTranslationMetadata?> rateTranslation(
    String messageId,
    String targetLanguage,
    TranslationQuality quality,
  ) async {
    try {
      final metadata = await _service.rateTranslation(
        messageId,
        targetLanguage,
        quality,
      );
      return metadata;
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
      return null;
    }
  }

  /// Clear the translation cache
  void clearCache() {
    _service.clearCache();
  }
}

/// Provider for the message translation notifier
final messageTranslationNotifierProvider =
    StateNotifierProvider<MessageTranslationNotifier, AsyncValue<void>>((ref) {
  final service = ref.watch(messageTranslationServiceProvider);
  return MessageTranslationNotifier(service, ref.read);
});
