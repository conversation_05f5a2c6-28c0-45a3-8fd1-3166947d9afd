import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:culture_connect/models/message_model.dart';
import 'package:culture_connect/providers/group_chat_provider.dart';

/// The state of the paginated messages
class PaginatedMessagesState {
  /// The list of messages
  final List<MessageModel> messages;
  
  /// Whether there are more messages to load
  final bool hasMore;
  
  /// Whether the messages are loading
  final bool isLoading;
  
  /// The last document snapshot
  final DocumentSnapshot? lastDocument;
  
  /// Creates a new paginated messages state
  const PaginatedMessagesState({
    required this.messages,
    required this.hasMore,
    this.isLoading = false,
    this.lastDocument,
  });
  
  /// Creates a copy with some fields replaced
  PaginatedMessagesState copyWith({
    List<MessageModel>? messages,
    bool? hasMore,
    bool? isLoading,
    DocumentSnapshot? lastDocument,
  }) {
    return PaginatedMessagesState(
      messages: messages ?? this.messages,
      hasMore: hasMore ?? this.hasMore,
      isLoading: isLoading ?? this.isLoading,
      lastDocument: lastDocument ?? this.lastDocument,
    );
  }
}

/// A notifier for paginated messages
class PaginatedMessagesNotifier extends StateNotifier<PaginatedMessagesState> {
  /// The ID of the group chat
  final String groupId;
  
  /// The Firestore instance
  final FirebaseFirestore _firestore;
  
  /// The number of messages to load per page
  final int _pageSize;
  
  /// Creates a new paginated messages notifier
  PaginatedMessagesNotifier(this.groupId, this._firestore, {int pageSize = 50})
      : _pageSize = pageSize,
        super(const PaginatedMessagesState(messages: [], hasMore: true)) {
    // Load the first page
    loadMoreMessages();
  }
  
  /// Loads more messages
  Future<void> loadMoreMessages() async {
    // If we're already loading or there are no more messages, return
    if (state.isLoading || !state.hasMore) return;
    
    // Set loading state
    state = state.copyWith(isLoading: true);
    
    try {
      // Create the query
      Query query = _firestore
          .collection('messages')
          .where('chatId', isEqualTo: groupId)
          .where('isGroupMessage', isEqualTo: true)
          .orderBy('timestamp', descending: true)
          .limit(_pageSize);
      
      // If we have a last document, start after it
      if (state.lastDocument != null) {
        query = query.startAfterDocument(state.lastDocument!);
      }
      
      // Get the documents
      final snapshot = await query.get();
      final docs = snapshot.docs;
      
      // If we got fewer documents than the page size, there are no more messages
      final hasMore = docs.length >= _pageSize;
      
      // Get the last document
      final lastDocument = docs.isNotEmpty ? docs.last : null;
      
      // Convert the documents to message models
      final newMessages = docs
          .map((doc) => MessageModel.fromJson(doc.data() as Map<String, dynamic>))
          .toList();
      
      // Add the new messages to the state
      state = state.copyWith(
        messages: [...state.messages, ...newMessages],
        hasMore: hasMore,
        isLoading: false,
        lastDocument: lastDocument,
      );
    } catch (e) {
      debugPrint('Error loading messages: $e');
      state = state.copyWith(isLoading: false);
    }
  }
  
  /// Adds a new message to the list
  void addMessage(MessageModel message) {
    state = state.copyWith(
      messages: [message, ...state.messages],
    );
  }
  
  /// Updates a message in the list
  void updateMessage(MessageModel message) {
    final index = state.messages.indexWhere((m) => m.id == message.id);
    if (index >= 0) {
      final updatedMessages = List<MessageModel>.from(state.messages);
      updatedMessages[index] = message;
      state = state.copyWith(messages: updatedMessages);
    }
  }
  
  /// Removes a message from the list
  void removeMessage(String messageId) {
    state = state.copyWith(
      messages: state.messages.where((m) => m.id != messageId).toList(),
    );
  }
  
  /// Refreshes the messages
  Future<void> refreshMessages() async {
    state = const PaginatedMessagesState(messages: [], hasMore: true);
    await loadMoreMessages();
  }
}

/// Provider for paginated messages
final paginatedMessagesProvider = StateNotifierProvider.family<PaginatedMessagesNotifier, PaginatedMessagesState, String>(
  (ref, groupId) => PaginatedMessagesNotifier(
    groupId,
    FirebaseFirestore.instance,
  ),
);

/// Provider for sorted messages (chronological order)
final sortedMessagesProvider = Provider.family<List<MessageModel>, String>(
  (ref, groupId) {
    final paginatedState = ref.watch(paginatedMessagesProvider(groupId));
    
    // Sort messages by timestamp (oldest first)
    final sortedMessages = List<MessageModel>.from(paginatedState.messages);
    sortedMessages.sort((a, b) => a.timestamp.compareTo(b.timestamp));
    
    return sortedMessages;
  },
);

/// Provider for the loading state
final messagesLoadingProvider = Provider.family<bool, String>(
  (ref, groupId) {
    final paginatedState = ref.watch(paginatedMessagesProvider(groupId));
    return paginatedState.isLoading;
  },
);

/// Provider for whether there are more messages to load
final hasMoreMessagesProvider = Provider.family<bool, String>(
  (ref, groupId) {
    final paginatedState = ref.watch(paginatedMessagesProvider(groupId));
    return paginatedState.hasMore;
  },
);
