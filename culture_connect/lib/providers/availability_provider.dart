import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:uuid/uuid.dart';

import 'package:culture_connect/models/availability_model.dart';
import 'package:culture_connect/services/availability_service.dart';

/// Provider for the AvailabilityService.
final availabilityServiceProvider = Provider<AvailabilityService>((ref) {
  return AvailabilityService();
});

/// Provider for the current guide's availability.
final currentGuideAvailabilityProvider = FutureProvider<AvailabilityModel?>((ref) async {
  final availabilityService = ref.watch(availabilityServiceProvider);
  return availabilityService.getCurrentGuideAvailability();
});

/// Provider for a specific guide's availability by guide ID.
final guideAvailabilityByGuideIdProvider = FutureProvider.family<AvailabilityModel?, String>((ref, guideId) async {
  final availabilityService = ref.watch(availabilityServiceProvider);
  return availabilityService.getGuideAvailabilityByGuideId(guideId);
});

/// Notifier for managing guide availability.
class AvailabilityNotifier extends StateNotifier<AsyncValue<AvailabilityModel?>> {
  final AvailabilityService _availabilityService;
  final Uuid _uuid = const Uuid();

  AvailabilityNotifier(this._availabilityService) : super(const AsyncValue.loading()) {
    _loadCurrentGuideAvailability();
  }

  Future<void> _loadCurrentGuideAvailability() async {
    try {
      state = const AsyncValue.loading();
      final availability = await _availabilityService.getCurrentGuideAvailability();
      state = AsyncValue.data(availability);
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }

  /// Create a new availability schedule.
  Future<void> createAvailability({
    required WeeklySchedule weeklySchedule,
    required List<DateException> dateExceptions,
    required int advanceBookingDays,
    required int maxBookingDays,
  }) async {
    try {
      state = const AsyncValue.loading();
      final availability = await _availabilityService.createAvailability(
        weeklySchedule: weeklySchedule,
        dateExceptions: dateExceptions,
        advanceBookingDays: advanceBookingDays,
        maxBookingDays: maxBookingDays,
      );
      state = AsyncValue.data(availability);
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }

  /// Update an existing availability schedule.
  Future<void> updateAvailability({
    required String availabilityId,
    WeeklySchedule? weeklySchedule,
    List<DateException>? dateExceptions,
    int? advanceBookingDays,
    int? maxBookingDays,
  }) async {
    try {
      state = const AsyncValue.loading();
      final availability = await _availabilityService.updateAvailability(
        availabilityId: availabilityId,
        weeklySchedule: weeklySchedule,
        dateExceptions: dateExceptions,
        advanceBookingDays: advanceBookingDays,
        maxBookingDays: maxBookingDays,
      );
      state = AsyncValue.data(availability);
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }

  /// Add a time slot to a specific day in the weekly schedule.
  Future<void> addTimeSlotToWeeklySchedule({
    required String availabilityId,
    required DayOfWeek day,
    required String startTime,
    required String endTime,
  }) async {
    try {
      state = const AsyncValue.loading();
      final timeSlot = TimeSlot(
        id: _uuid.v4(),
        startTime: startTime,
        endTime: endTime,
      );
      final availability = await _availabilityService.addTimeSlotToWeeklySchedule(
        availabilityId: availabilityId,
        day: day,
        timeSlot: timeSlot,
      );
      state = AsyncValue.data(availability);
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }

  /// Remove a time slot from a specific day in the weekly schedule.
  Future<void> removeTimeSlotFromWeeklySchedule({
    required String availabilityId,
    required DayOfWeek day,
    required String timeSlotId,
  }) async {
    try {
      state = const AsyncValue.loading();
      final availability = await _availabilityService.removeTimeSlotFromWeeklySchedule(
        availabilityId: availabilityId,
        day: day,
        timeSlotId: timeSlotId,
      );
      state = AsyncValue.data(availability);
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }

  /// Add a date exception.
  Future<void> addDateException({
    required String availabilityId,
    required DateTime date,
    required bool isAvailable,
    List<TimeSlot>? timeSlots,
  }) async {
    try {
      state = const AsyncValue.loading();
      final dateException = DateException(
        id: _uuid.v4(),
        date: date,
        isAvailable: isAvailable,
        timeSlots: timeSlots,
      );
      final availability = await _availabilityService.addDateException(
        availabilityId: availabilityId,
        dateException: dateException,
      );
      state = AsyncValue.data(availability);
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }

  /// Remove a date exception.
  Future<void> removeDateException({
    required String availabilityId,
    required String dateExceptionId,
  }) async {
    try {
      state = const AsyncValue.loading();
      final availability = await _availabilityService.removeDateException(
        availabilityId: availabilityId,
        dateExceptionId: dateExceptionId,
      );
      state = AsyncValue.data(availability);
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }

  /// Refresh the current guide's availability.
  Future<void> refreshAvailability() async {
    await _loadCurrentGuideAvailability();
  }
}

/// Provider for the AvailabilityNotifier.
final availabilityNotifierProvider = StateNotifierProvider<AvailabilityNotifier, AsyncValue<AvailabilityModel?>>((ref) {
  final availabilityService = ref.watch(availabilityServiceProvider);
  return AvailabilityNotifier(availabilityService);
});
