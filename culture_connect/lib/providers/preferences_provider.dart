import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';

// Keys for SharedPreferences
const String _kThemeModeKey = 'themeMode';
const String _kLanguageKey = 'language';
const String _kNotificationsEnabledKey = 'notificationsEnabled';
const String _kCulturalInterestsKey = 'culturalInterests';
const String _kLanguagePreferencesKey = 'languagePreferences';

class UserPreferences {
  final ThemeMode themeMode;
  final String language;
  final bool notificationsEnabled;
  final List<String> culturalInterests;
  final List<String> languagePreferences;

  const UserPreferences({
    this.themeMode = ThemeMode.system,
    this.language = 'en',
    this.notificationsEnabled = true,
    this.culturalInterests = const [],
    this.languagePreferences = const [],
  });

  UserPreferences copyWith({
    ThemeMode? themeMode,
    String? language,
    bool? notificationsEnabled,
    List<String>? culturalInterests,
    List<String>? languagePreferences,
  }) {
    return UserPreferences(
      themeMode: themeMode ?? this.themeMode,
      language: language ?? this.language,
      notificationsEnabled: notificationsEnabled ?? this.notificationsEnabled,
      culturalInterests: culturalInterests ?? this.culturalInterests,
      languagePreferences: languagePreferences ?? this.languagePreferences,
    );
  }
}

class UserPreferencesNotifier extends StateNotifier<UserPreferences> {
  final SharedPreferences _prefs;

  UserPreferencesNotifier(this._prefs)
      : super(UserPreferencesNotifier._loadFromPrefs(_prefs));

  static UserPreferences _loadFromPrefs(SharedPreferences prefs) {
    return UserPreferences(
      themeMode: ThemeMode.values[prefs.getInt(_kThemeModeKey) ?? 0],
      language: prefs.getString(_kLanguageKey) ?? 'en',
      notificationsEnabled: prefs.getBool(_kNotificationsEnabledKey) ?? true,
      culturalInterests: prefs.getStringList(_kCulturalInterestsKey) ?? [],
      languagePreferences: prefs.getStringList(_kLanguagePreferencesKey) ?? [],
    );
  }

  // Theme mode
  Future<void> setThemeMode(ThemeMode mode) async {
    await _prefs.setInt(_kThemeModeKey, mode.index);
    state = state.copyWith(themeMode: mode);
  }

  // Language
  Future<void> setLanguage(String language) async {
    await _prefs.setString(_kLanguageKey, language);
    state = state.copyWith(language: language);
  }

  // Notifications
  Future<void> setNotificationsEnabled(bool enabled) async {
    await _prefs.setBool(_kNotificationsEnabledKey, enabled);
    state = state.copyWith(notificationsEnabled: enabled);
  }

  // Cultural interests
  Future<void> setCulturalInterests(List<String> interests) async {
    await _prefs.setStringList(_kCulturalInterestsKey, interests);
    state = state.copyWith(culturalInterests: interests);
  }

  Future<void> addCulturalInterest(String interest) async {
    final interests = List<String>.from(state.culturalInterests);
    if (!interests.contains(interest)) {
      interests.add(interest);
      await setCulturalInterests(interests);
    }
  }

  Future<void> removeCulturalInterest(String interest) async {
    final interests = List<String>.from(state.culturalInterests);
    if (interests.contains(interest)) {
      interests.remove(interest);
      await setCulturalInterests(interests);
    }
  }

  // Language preferences
  Future<void> setLanguagePreferences(List<String> languages) async {
    await _prefs.setStringList(_kLanguagePreferencesKey, languages);
    state = state.copyWith(languagePreferences: languages);
  }

  Future<void> addLanguagePreference(String language) async {
    final languages = List<String>.from(state.languagePreferences);
    if (!languages.contains(language)) {
      languages.add(language);
      await setLanguagePreferences(languages);
    }
  }

  Future<void> removeLanguagePreference(String language) async {
    final languages = List<String>.from(state.languagePreferences);
    if (languages.contains(language)) {
      languages.remove(language);
      await setLanguagePreferences(languages);
    }
  }

  // Sync with user profile
  Future<void> syncWithUserProfile({
    List<String>? culturalInterests,
    List<String>? languagePreferences,
  }) async {
    if (culturalInterests != null) {
      await setCulturalInterests(culturalInterests);
    }
    if (languagePreferences != null) {
      await setLanguagePreferences(languagePreferences);
    }
  }
}

// Provider for SharedPreferences
final sharedPreferencesProvider = Provider<SharedPreferences>((ref) {
  throw UnimplementedError('SharedPreferences provider not initialized');
});

// Provider for UserPreferences
final userPreferencesProvider =
    StateNotifierProvider<UserPreferencesNotifier, UserPreferences>((ref) {
  final prefs = ref.watch(sharedPreferencesProvider);
  return UserPreferencesNotifier(prefs);
});

// Convenience providers for specific preferences
final themeModeProvider = Provider<ThemeMode>((ref) {
  return ref.watch(userPreferencesProvider).themeMode;
});

final languageProvider = Provider<String>((ref) {
  return ref.watch(userPreferencesProvider).language;
});

final notificationsEnabledProvider = Provider<bool>((ref) {
  return ref.watch(userPreferencesProvider).notificationsEnabled;
});

final culturalInterestsProvider = Provider<List<String>>((ref) {
  return ref.watch(userPreferencesProvider).culturalInterests;
});

final languagePreferencesProvider = Provider<List<String>>((ref) {
  return ref.watch(userPreferencesProvider).languagePreferences;
});
