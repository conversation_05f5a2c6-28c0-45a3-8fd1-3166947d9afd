import 'dart:math' show sin, cos, sqrt, atan2, pi;

import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:culture_connect/models/ar/ar_content_marker.dart';
import 'package:culture_connect/services/ar/ar_content_service.dart';

/// Provider for the AR content service
final arContentServiceProvider = Provider<ARContentService>((ref) {
  // This is a simplified version for demonstration purposes
  // In a real app, you would inject the actual dependencies
  // For now, we'll create a fake implementation that returns empty data
  return _FakeARContentService();
});

/// A fake implementation of ARContentService for demonstration purposes
class _FakeARContentService implements ARContentService {
  @override
  Future<void> initialize() async {}

  @override
  Future<List<ARContentMarker>> getAllARContentMarkers() async {
    return [];
  }

  @override
  Future<ARContentMarker?> getARContentMarker(String id) async {
    return null;
  }

  @override
  Future<ARContentMarker> createARContentMarker(ARContentMarker marker) async {
    return marker;
  }

  @override
  Future<ARContentMarker> updateARContentMarker(ARContentMarker marker) async {
    return marker;
  }

  @override
  Future<bool> deleteARContentMarker(String id) async {
    return true;
  }

  @override
  Future<List<ARContentMarker>> getARContentMarkersForLocation(
      String location) async {
    return [];
  }

  @override
  Future<List<ARContentMarker>> getARContentMarkersForCoordinates(
      double latitude, double longitude, double radiusInMeters) async {
    return [];
  }

  @override
  Future<bool> downloadARContentForOfflineUse(String markerId) async {
    return true;
  }

  @override
  Future<bool> removeARContentFromOfflineStorage(String markerId) async {
    return true;
  }

  @override
  dynamic noSuchMethod(Invocation invocation) => super.noSuchMethod(invocation);
}

/// Notifier for AR content markers
class ARContentMarkersNotifier
    extends StateNotifier<AsyncValue<List<ARContentMarker>>> {
  ARContentMarkersNotifier(this.ref) : super(const AsyncValue.loading()) {
    loadAllMarkers();
  }

  final Ref ref;

  /// Load all AR content markers
  Future<void> loadAllMarkers() async {
    state = const AsyncValue.loading();
    try {
      final arContentService = ref.read(arContentServiceProvider);
      final markers = await arContentService.getAllARContentMarkers();
      state = AsyncValue.data(markers);
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }

  /// Create a new AR content marker
  Future<ARContentMarker?> createMarker(ARContentMarker marker) async {
    try {
      final arContentService = ref.read(arContentServiceProvider);
      final createdMarker =
          await arContentService.createARContentMarker(marker);

      // Update the state with the new marker
      state.whenData((markers) {
        state = AsyncValue.data([...markers, createdMarker]);
      });

      return createdMarker;
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
      return null;
    }
  }

  /// Update an AR content marker
  Future<ARContentMarker?> updateMarker(ARContentMarker marker) async {
    try {
      final arContentService = ref.read(arContentServiceProvider);
      final updatedMarker =
          await arContentService.updateARContentMarker(marker);

      // Update the state with the updated marker
      state.whenData((markers) {
        final index = markers.indexWhere((m) => m.id == marker.id);
        if (index != -1) {
          final newList = List<ARContentMarker>.from(markers);
          newList[index] = updatedMarker;
          state = AsyncValue.data(newList);
        }
      });

      return updatedMarker;
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
      return null;
    }
  }

  /// Delete an AR content marker
  Future<bool> deleteMarker(String id) async {
    try {
      final arContentService = ref.read(arContentServiceProvider);
      final result = await arContentService.deleteARContentMarker(id);

      if (result) {
        // Update the state by removing the deleted marker
        state.whenData((markers) {
          state = AsyncValue.data(markers.where((m) => m.id != id).toList());
        });
      }

      return result;
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
      return false;
    }
  }
}

/// Provider for all AR content markers
final arContentMarkersProvider = StateNotifierProvider<ARContentMarkersNotifier,
    AsyncValue<List<ARContentMarker>>>((ref) {
  return ARContentMarkersNotifier(ref);
});

/// Provider for a specific AR content marker
final arContentMarkerProvider =
    Provider.family<AsyncValue<ARContentMarker?>, String>((ref, id) {
  final markersAsyncValue = ref.watch(arContentMarkersProvider);

  return markersAsyncValue.when(
    data: (markers) {
      // Find the marker with the given ID, or return null if not found
      ARContentMarker? marker;
      try {
        marker = markers.firstWhere((m) => m.id == id);
      } catch (_) {
        marker = null;
      }
      return AsyncValue.data(marker);
    },
    loading: () => const AsyncValue.loading(),
    error: (error, stackTrace) => AsyncValue.error(error, stackTrace),
  );
});

/// Provider for AR content markers for a location
final arContentMarkersForLocationProvider =
    Provider.family<AsyncValue<List<ARContentMarker>>, String>((ref, location) {
  final markersAsyncValue = ref.watch(arContentMarkersProvider);

  return markersAsyncValue.when(
    data: (markers) {
      final filteredMarkers =
          markers.where((m) => m.location == location).toList();
      return AsyncValue.data(filteredMarkers);
    },
    loading: () => const AsyncValue.loading(),
    error: (error, stackTrace) => AsyncValue.error(error, stackTrace),
  );
});

/// Provider for AR content markers for coordinates
final arContentMarkersForCoordinatesProvider = Provider.family<
    AsyncValue<List<ARContentMarker>>,
    ({
      double latitude,
      double longitude,
      double radiusInMeters
    })>((ref, params) {
  final markersAsyncValue = ref.watch(arContentMarkersProvider);

  return markersAsyncValue.when(
    data: (markers) {
      // Filter markers by coordinates
      final filteredMarkers = markers.where((m) {
        if (m.coordinates == null) return false;

        final markerLat = m.coordinates!['lat']!;
        final markerLng = m.coordinates!['lng']!;

        // Calculate distance using Haversine formula
        const earthRadius = 6371000; // in meters
        final dLat = _degreesToRadians(markerLat - params.latitude);
        final dLng = _degreesToRadians(markerLng - params.longitude);

        final a = sin(dLat / 2) * sin(dLat / 2) +
            cos(_degreesToRadians(params.latitude)) *
                cos(_degreesToRadians(markerLat)) *
                sin(dLng / 2) *
                sin(dLng / 2);

        final c = 2 * atan2(sqrt(a), sqrt(1 - a));
        final distance = earthRadius * c;

        return distance <= params.radiusInMeters;
      }).toList();

      return AsyncValue.data(filteredMarkers);
    },
    loading: () => const AsyncValue.loading(),
    error: (error, stackTrace) => AsyncValue.error(error, stackTrace),
  );
});

/// Convert degrees to radians
double _degreesToRadians(double degrees) {
  return degrees * (pi / 180);
}

/// Provider for the current AR content marker
final currentARContentMarkerProvider = StateNotifierProvider<
    CurrentARContentMarkerNotifier, AsyncValue<ARContentMarker?>>((ref) {
  final arContentService = ref.watch(arContentServiceProvider);
  return CurrentARContentMarkerNotifier(arContentService);
});

/// Notifier for the current AR content marker
class CurrentARContentMarkerNotifier
    extends StateNotifier<AsyncValue<ARContentMarker?>> {
  /// The AR content service
  final ARContentService _arContentService;

  /// Creates a new current AR content marker notifier
  CurrentARContentMarkerNotifier(this._arContentService)
      : super(const AsyncValue.loading());

  /// Load an AR content marker
  Future<void> loadARContentMarker(String id) async {
    state = const AsyncValue.loading();

    try {
      final marker = await _arContentService.getARContentMarker(id);
      state = AsyncValue.data(marker);
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }

  /// Create a new AR content marker
  Future<void> createARContentMarker({
    required String title,
    String? description,
    required ARContentType contentType,
    required String contentUrl,
    String? thumbnailUrl,
    int? contentSize,
    int? durationSeconds,
    String? location,
    Map<String, double>? coordinates,
  }) async {
    state = const AsyncValue.loading();

    try {
      final marker = ARContentMarker(
        title: title,
        description: description,
        contentType: contentType,
        contentUrl: contentUrl,
        thumbnailUrl: thumbnailUrl,
        contentSize: contentSize,
        durationSeconds: durationSeconds,
        location: location,
        coordinates: coordinates,
      );

      final createdMarker =
          await _arContentService.createARContentMarker(marker);
      state = AsyncValue.data(createdMarker);
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }

  /// Update the current AR content marker
  Future<void> updateARContentMarker({
    String? title,
    String? description,
    ARContentType? contentType,
    String? contentUrl,
    String? thumbnailUrl,
    int? contentSize,
    int? durationSeconds,
    String? location,
    Map<String, double>? coordinates,
  }) async {
    final currentState = state;
    if (currentState is! AsyncData<ARContentMarker?>) return;
    if (currentState.value == null) return;

    final currentMarker = currentState.value!;

    try {
      final updatedMarker = currentMarker.copyWith(
        title: title,
        description: description,
        contentType: contentType,
        contentUrl: contentUrl,
        thumbnailUrl: thumbnailUrl,
        contentSize: contentSize,
        durationSeconds: durationSeconds,
        location: location,
        coordinates: coordinates,
      );

      state = AsyncValue.data(updatedMarker);

      final savedMarker =
          await _arContentService.updateARContentMarker(updatedMarker);
      state = AsyncValue.data(savedMarker);
    } catch (e, stackTrace) {
      // Preserve the current marker but indicate an error occurred
      state = AsyncValue.error(e, stackTrace);
    }
  }

  /// Download the current AR content marker for offline use
  Future<bool> downloadForOfflineUse() async {
    final currentState = state;
    if (currentState is! AsyncData<ARContentMarker?>) return false;
    if (currentState.value == null) return false;

    final currentMarker = currentState.value!;

    try {
      final result = await _arContentService
          .downloadARContentForOfflineUse(currentMarker.id);

      if (result) {
        final updatedMarker = currentMarker.copyWith(
          isAvailableOffline: true,
        );

        state = AsyncValue.data(updatedMarker);
      }

      return result;
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
      return false;
    }
  }

  /// Remove the current AR content marker from offline storage
  Future<bool> removeFromOfflineStorage() async {
    final currentState = state;
    if (currentState is! AsyncData<ARContentMarker?>) return false;
    if (currentState.value == null) return false;

    final currentMarker = currentState.value!;

    try {
      final result = await _arContentService
          .removeARContentFromOfflineStorage(currentMarker.id);

      if (result) {
        final updatedMarker = currentMarker.copyWith(
          isAvailableOffline: false,
        );

        state = AsyncValue.data(updatedMarker);
      }

      return result;
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
      return false;
    }
  }

  /// Delete the current AR content marker
  Future<void> deleteARContentMarker() async {
    final currentState = state;
    if (currentState is! AsyncData<ARContentMarker?>) return;
    if (currentState.value == null) return;

    final currentMarker = currentState.value!;

    try {
      await _arContentService.deleteARContentMarker(currentMarker.id);
      state = const AsyncValue.data(null);
    } catch (e, stackTrace) {
      // Preserve the current marker but indicate an error occurred
      state = AsyncValue.error(e, stackTrace);
    }
  }

  /// Clear the current AR content marker
  void clearARContentMarker() {
    state = const AsyncValue.data(null);
  }
}
