import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:culture_connect/models/translation/message_translation_metadata.dart';
import 'package:culture_connect/models/translation/translation_feedback_model.dart';
import 'package:culture_connect/providers/auth_provider.dart';
import 'package:culture_connect/services/voice_translation/translation_feedback_service.dart';
import 'package:culture_connect/providers/common/reader.dart';

/// Provider for the translation feedback service
final translationFeedbackServiceProvider =
    Provider<TranslationFeedbackService>((ref) {
  throw UnimplementedError(
      'TranslationFeedbackService provider not initialized');
});

/// Provider for feedback events stream
final feedbackEventsProvider = StreamProvider<String>((ref) {
  final service = ref.watch(translationFeedbackServiceProvider);
  return service.feedbackEvents;
});

/// Provider for feedback for a specific message
final messageFeedbackProvider =
    FutureProvider.family<List<TranslationFeedbackModel>, String>(
        (ref, messageId) async {
  final service = ref.watch(translationFeedbackServiceProvider);
  return service.getMessageFeedback(messageId);
});

/// Provider for feedback for the current user
final userFeedbackProvider =
    FutureProvider<List<TranslationFeedbackModel>>((ref) async {
  final service = ref.watch(translationFeedbackServiceProvider);
  final userAsync = ref.watch(currentUserModelProvider);

  return userAsync.when(
    data: (user) {
      if (user == null) return [];
      return service.getUserFeedback(user.id);
    },
    loading: () => [],
    error: (_, __) => [],
  );
});

/// Provider for feedback statistics
final feedbackStatisticsProvider =
    FutureProvider<Map<String, dynamic>>((ref) async {
  final service = ref.watch(translationFeedbackServiceProvider);
  return service.getFeedbackStatistics();
});

/// Notifier for translation feedback actions
class TranslationFeedbackNotifier extends StateNotifier<AsyncValue<void>> {
  final TranslationFeedbackService _service;
  final Reader _read;

  TranslationFeedbackNotifier(this._service, this._read)
      : super(const AsyncValue.data(null));

  /// Submit feedback for a translation
  Future<TranslationFeedbackModel?> submitFeedback({
    required String messageId,
    required String sourceLanguage,
    required String targetLanguage,
    required String originalText,
    required String translatedText,
    String? suggestedCorrection,
    required TranslationQuality quality,
    required TranslationFeedbackType feedbackType,
    String? comments,
    double? originalConfidence,
    List<TextSegment>? problematicSegments,
  }) async {
    state = const AsyncValue.loading();

    try {
      final userAsync = await _read(currentUserModelProvider.future);
      if (userAsync == null) {
        state = const AsyncValue.error('User not found', StackTrace.empty);
        return null;
      }

      final feedback = await _service.submitFeedback(
        messageId: messageId,
        userId: userAsync.id,
        sourceLanguage: sourceLanguage,
        targetLanguage: targetLanguage,
        originalText: originalText,
        translatedText: translatedText,
        suggestedCorrection: suggestedCorrection,
        quality: quality,
        feedbackType: feedbackType,
        comments: comments,
        originalConfidence: originalConfidence,
        problematicSegments: problematicSegments,
      );

      state = const AsyncValue.data(null);
      return feedback;
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
      return null;
    }
  }

  /// Get feedback for a message
  Future<TranslationFeedbackModel?> getFeedback(String feedbackId) async {
    try {
      return await _service.getFeedback(feedbackId);
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
      return null;
    }
  }

  /// Clear the feedback cache
  void clearCache() {
    _service.clearCache();
  }
}

/// Provider for the translation feedback notifier
final translationFeedbackNotifierProvider =
    StateNotifierProvider<TranslationFeedbackNotifier, AsyncValue<void>>((ref) {
  final service = ref.watch(translationFeedbackServiceProvider);
  return TranslationFeedbackNotifier(service, ref.read);
});
