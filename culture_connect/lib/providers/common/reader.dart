import 'package:flutter_riverpod/flutter_riverpod.dart';

/// A function that can read a provider's state.
///
/// This is typically used to allow reading providers from outside of the build method.
/// It's useful for services and notifiers that need to access other providers.
typedef Reader = T Function<T>(ProviderListenable<T> provider);

/// Extension methods for [Reader]
extension ReaderExtension on Reader {
  /// Reads a provider and returns its state
  T read<T>(ProviderListenable<T> provider) => this(provider);
}
