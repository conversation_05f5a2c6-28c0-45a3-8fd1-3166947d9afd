import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:culture_connect/models/user_model.dart';
import 'package:culture_connect/models/chat/chat_model.dart';
import 'package:culture_connect/providers/auth_provider.dart';

/// Provider for a specific user by ID
final userProvider =
    FutureProvider.family<UserModel?, String>((ref, userId) async {
  try {
    final snapshot =
        await FirebaseFirestore.instance.collection('users').doc(userId).get();

    if (!snapshot.exists) {
      return null;
    }

    return UserModel.fromJson({...snapshot.data()!, 'id': userId});
  } catch (e) {
    return null;
  }
});

/// Provider for chat details by ID
final chatDetailsProvider =
    FutureProvider.family<ChatModel?, String>((ref, chatId) async {
  try {
    final snapshot =
        await FirebaseFirestore.instance.collection('chats').doc(chatId).get();

    if (!snapshot.exists) {
      return null;
    }

    return ChatModel.fromJson({...snapshot.data()!, 'id': chatId});
  } catch (e) {
    return null;
  }
});

/// Provider for all users
final allUsersProvider = FutureProvider<List<UserModel>>((ref) async {
  try {
    final currentUser = ref.watch(currentUserModelProvider).value;
    if (currentUser == null) {
      return [];
    }

    final snapshot = await FirebaseFirestore.instance.collection('users').get();

    return snapshot.docs
        .map((doc) => UserModel.fromJson({...doc.data(), 'id': doc.id}))
        .where((user) => user.id != currentUser.id) // Exclude current user
        .toList();
  } catch (e) {
    return [];
  }
});

/// Provider for recent contacts
final recentContactsProvider = FutureProvider<List<UserModel>>((ref) async {
  try {
    final currentUser = ref.watch(currentUserModelProvider).value;
    if (currentUser == null) {
      return [];
    }

    // Get chats where current user is a participant
    final chatsSnapshot = await FirebaseFirestore.instance
        .collection('chats')
        .where('participants', arrayContains: currentUser.id)
        .orderBy('lastMessageAt', descending: true)
        .limit(10)
        .get();

    // Extract other participants' IDs
    final contactIds = <String>{};
    for (final doc in chatsSnapshot.docs) {
      final participants =
          List<String>.from(doc.data()['participants'] as List);
      for (final participantId in participants) {
        if (participantId != currentUser.id) {
          contactIds.add(participantId);
        }
      }
    }

    // Fetch user data for each contact
    final contacts = <UserModel>[];
    for (final contactId in contactIds) {
      final userDoc = await FirebaseFirestore.instance
          .collection('users')
          .doc(contactId)
          .get();

      if (userDoc.exists) {
        contacts.add(UserModel.fromJson({...userDoc.data()!, 'id': contactId}));
      }
    }

    return contacts;
  } catch (e) {
    return [];
  }
});
