import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:culture_connect/services/startup_optimization_service.dart';

/// Provider for the startup optimization service
final startupOptimizationServiceProvider = Provider<StartupOptimizationService>((ref) {
  return StartupOptimizationService();
});

/// Provider for tracking app initialization status
final appInitializationProvider = FutureProvider<bool>((ref) async {
  final service = ref.watch(startupOptimizationServiceProvider);
  return service.initializeApp();
});

/// Provider for tracking network connectivity
final connectivityProvider = FutureProvider<bool>((ref) async {
  final service = ref.watch(startupOptimizationServiceProvider);
  return service.checkConnectivity();
});

/// Provider for app initialization state
final appInitializationStateProvider = StateNotifierProvider<AppInitializationNotifier, AppInitializationState>((ref) {
  final service = ref.watch(startupOptimizationServiceProvider);
  return AppInitializationNotifier(service);
});

/// App initialization state
class AppInitializationState {
  final bool isInitialized;
  final bool isLoading;
  final String? error;
  final double progress;

  AppInitializationState({
    this.isInitialized = false,
    this.isLoading = true,
    this.error,
    this.progress = 0.0,
  });

  AppInitializationState copyWith({
    bool? isInitialized,
    bool? isLoading,
    String? error,
    double? progress,
  }) {
    return AppInitializationState(
      isInitialized: isInitialized ?? this.isInitialized,
      isLoading: isLoading ?? this.isLoading,
      error: error,
      progress: progress ?? this.progress,
    );
  }
}

/// App initialization notifier
class AppInitializationNotifier extends StateNotifier<AppInitializationState> {
  final StartupOptimizationService _service;

  AppInitializationNotifier(this._service) : super(AppInitializationState()) {
    _initialize();
  }

  Future<void> _initialize() async {
    try {
      // Update progress to show initialization started
      state = state.copyWith(isLoading: true, progress: 0.1);
      
      // Wait a bit to show progress
      await Future.delayed(const Duration(milliseconds: 100));
      state = state.copyWith(progress: 0.3);
      
      // Initialize the app
      await Future.delayed(const Duration(milliseconds: 100));
      state = state.copyWith(progress: 0.6);
      
      final result = await _service.initializeApp();
      
      // Update state based on initialization result
      state = state.copyWith(
        isInitialized: result,
        isLoading: false,
        progress: 1.0,
      );
    } catch (e) {
      // Update state with error
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
        progress: 1.0,
      );
    }
  }

  /// Retry initialization if it failed
  Future<void> retryInitialization() async {
    state = state.copyWith(isLoading: true, error: null, progress: 0.0);
    await _initialize();
  }
}
