import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:culture_connect/models/message_model.dart';
import 'package:culture_connect/models/translation/language_model.dart';
import 'package:culture_connect/models/translation/message_translation_metadata.dart';
import 'package:culture_connect/models/translation/group_translation_model.dart';
import 'package:culture_connect/services/group_translation_service.dart';
import 'package:culture_connect/services/language_detection_service.dart';
import 'package:culture_connect/providers/common/reader.dart';

/// Provider for group translation settings
final groupTranslationSettingsProvider =
    FutureProvider.family<GroupTranslationSettings, String>(
        (ref, groupId) async {
  final service = ref.watch(groupTranslationServiceProvider);
  return service.getGroupTranslationSettings(groupId);
});

/// Provider for group message translations
final groupMessageTranslationProvider =
    FutureProvider.family<GroupMessageTranslation?, String>(
        (ref, messageId) async {
  final service = ref.watch(groupTranslationServiceProvider);
  return service.getGroupMessageTranslation(messageId);
});

/// Provider for translated text for a user
final translatedTextForUserProvider = FutureProvider.family<
    String,
    ({
      MessageModel message,
      String userId,
      String groupId
    })>((ref, params) async {
  final service = ref.watch(groupTranslationServiceProvider);
  final settings =
      await ref.watch(groupTranslationSettingsProvider(params.groupId).future);

  return service.getTranslationForUser(
    params.message,
    params.userId,
    settings,
  );
});

/// Provider for full translation metadata for a user
final translationMetadataForUserProvider = FutureProvider.family<
    MessageTranslationMetadata?,
    ({
      MessageModel message,
      String userId,
      String groupId
    })>((ref, params) async {
  final service = ref.watch(groupTranslationServiceProvider);
  final settings =
      await ref.watch(groupTranslationSettingsProvider(params.groupId).future);

  return service.getTranslationMetadataForUser(
    params.message,
    params.userId,
    settings,
  );
});

/// Provider for participant language preferences in a group
final participantLanguagePreferencesProvider =
    FutureProvider.family<Map<String, ParticipantLanguagePreference>, String>(
        (ref, groupId) async {
  final settings =
      await ref.watch(groupTranslationSettingsProvider(groupId).future);
  return settings.participantPreferences;
});

/// Provider for a specific participant's language preference
final participantLanguagePreferenceProvider = FutureProvider.family<
    ParticipantLanguagePreference?,
    ({String groupId, String userId})>((ref, params) async {
  final settings =
      await ref.watch(groupTranslationSettingsProvider(params.groupId).future);
  return settings.participantPreferences[params.userId];
});

/// Provider for unique languages used in a group
final uniqueGroupLanguagesProvider =
    FutureProvider.family<List<LanguageModel>, String>((ref, groupId) async {
  final settings =
      await ref.watch(groupTranslationSettingsProvider(groupId).future);
  return settings.uniqueLanguages;
});

/// Provider for supported languages for detection
final supportedLanguagesProvider = Provider<List<LanguageModel>>((ref) {
  final service = ref.watch(languageDetectionServiceProvider);
  return service.supportedLanguages;
});

/// Provider for auto-detected language
final detectedLanguageProvider =
    FutureProvider.family<String, String>((ref, text) async {
  final service = ref.watch(languageDetectionServiceProvider);
  return service.detectLanguage(text);
});

/// Notifier for group translation actions
class GroupTranslationNotifier extends StateNotifier<AsyncValue<void>> {
  final GroupTranslationService _service;
  final Reader _read;

  GroupTranslationNotifier(this._service, this._read)
      : super(const AsyncValue.data(null));

  /// Update group translation settings
  Future<void> updateGroupTranslationSettings(
      GroupTranslationSettings settings) async {
    state = const AsyncValue.loading();

    try {
      await _service.updateGroupTranslationSettings(settings);
      state = const AsyncValue.data(null);
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }

  /// Update a participant's language preference
  Future<void> updateParticipantPreference(
    String groupId,
    ParticipantLanguagePreference preference,
  ) async {
    state = const AsyncValue.loading();

    try {
      await _service.updateParticipantPreference(groupId, preference);
      state = const AsyncValue.data(null);
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }

  /// Remove a participant's language preference
  Future<void> removeParticipantPreference(
      String groupId, String userId) async {
    state = const AsyncValue.loading();

    try {
      await _service.removeParticipantPreference(groupId, userId);
      state = const AsyncValue.data(null);
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }

  /// Translate a message for a group
  Future<GroupMessageTranslation> translateGroupMessage(
    MessageModel message,
    String groupId,
  ) async {
    state = const AsyncValue.loading();

    try {
      final settings =
          await _read(groupTranslationSettingsProvider(groupId).future);
      final translation =
          await _service.translateGroupMessage(message, settings);
      state = const AsyncValue.data(null);
      return translation;
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
      rethrow;
    }
  }

  /// Clear the translation cache for a group
  Future<void> clearGroupTranslationCache(String groupId) async {
    state = const AsyncValue.loading();

    try {
      await _service.clearGroupTranslationCache(groupId);
      state = const AsyncValue.data(null);
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }

  /// Toggle auto-translation for a participant
  Future<void> toggleAutoTranslateForParticipant(
    String groupId,
    String userId,
    bool value,
  ) async {
    state = const AsyncValue.loading();

    try {
      // Get current preference
      final settings =
          await _read(groupTranslationSettingsProvider(groupId).future);
      final preference = settings.participantPreferences[userId];

      if (preference != null) {
        // Update preference
        final updatedPreference = preference.copyWith(autoTranslate: value);
        await _service.updateParticipantPreference(groupId, updatedPreference);
      }

      state = const AsyncValue.data(null);
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }

  /// Toggle show original text for a participant
  Future<void> toggleShowOriginalTextForParticipant(
    String groupId,
    String userId,
    bool value,
  ) async {
    state = const AsyncValue.loading();

    try {
      // Get current preference
      final settings =
          await _read(groupTranslationSettingsProvider(groupId).future);
      final preference = settings.participantPreferences[userId];

      if (preference != null) {
        // Update preference
        final updatedPreference = preference.copyWith(showOriginalText: value);
        await _service.updateParticipantPreference(groupId, updatedPreference);
      }

      state = const AsyncValue.data(null);
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }

  /// Update preferred language for a participant
  Future<void> updatePreferredLanguageForParticipant(
    String groupId,
    String userId,
    LanguageModel language,
  ) async {
    state = const AsyncValue.loading();

    try {
      // Get current preference
      final settings =
          await _read(groupTranslationSettingsProvider(groupId).future);
      final preference = settings.participantPreferences[userId];

      if (preference != null) {
        // Update preference
        final updatedPreference =
            preference.copyWith(preferredLanguage: language);
        await _service.updateParticipantPreference(groupId, updatedPreference);
      } else {
        // Create new preference
        final newPreference = ParticipantLanguagePreference(
          userId: userId,
          displayName: 'User', // This should be replaced with actual user name
          preferredLanguage: language,
        );
        await _service.updateParticipantPreference(groupId, newPreference);
      }

      state = const AsyncValue.data(null);
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }

  /// Toggle feature for a group
  Future<void> toggleFeatureForGroup(
    String groupId,
    String feature,
    bool value,
  ) async {
    state = const AsyncValue.loading();

    try {
      // Get current settings
      final settings =
          await _read(groupTranslationSettingsProvider(groupId).future);

      // Update settings based on feature
      GroupTranslationSettings updatedSettings;

      switch (feature) {
        case 'autoDetectLanguages':
          updatedSettings = settings.copyWith(autoDetectLanguages: value);
          break;
        case 'showTranslationIndicators':
          updatedSettings = settings.copyWith(showTranslationIndicators: value);
          break;
        case 'enableRealTimeTranslation':
          updatedSettings = settings.copyWith(enableRealTimeTranslation: value);
          break;
        case 'translateMediaCaptions':
          updatedSettings = settings.copyWith(translateMediaCaptions: value);
          break;
        case 'enableCulturalContext':
          updatedSettings = settings.copyWith(enableCulturalContext: value);
          break;
        case 'enableSlangIdiomDetection':
          updatedSettings = settings.copyWith(enableSlangIdiomDetection: value);
          break;
        case 'enablePronunciationGuidance':
          updatedSettings =
              settings.copyWith(enablePronunciationGuidance: value);
          break;
        default:
          throw Exception('Unknown feature: $feature');
      }

      // Save updated settings
      await _service.updateGroupTranslationSettings(updatedSettings);

      state = const AsyncValue.data(null);
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }
}

/// Provider for the group translation notifier
final groupTranslationNotifierProvider =
    StateNotifierProvider<GroupTranslationNotifier, AsyncValue<void>>((ref) {
  final service = ref.watch(groupTranslationServiceProvider);
  return GroupTranslationNotifier(service, ref.read);
});
