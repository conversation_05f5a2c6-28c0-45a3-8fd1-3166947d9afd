import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:culture_connect/models/offline/offline_settings.dart';
import 'package:culture_connect/models/offline/content_conflict_resolution.dart';
import 'package:culture_connect/providers/shared_preferences_provider.dart';

/// A notifier for offline settings
class OfflineSettingsNotifier extends StateNotifier<OfflineSettings> {
  /// The shared preferences instance
  final SharedPreferences _prefs;
  
  /// Creates a new offline settings notifier
  OfflineSettingsNotifier(this._prefs) : super(OfflineSettings.defaultSettings()) {
    _loadSettings();
  }
  
  /// Load settings from shared preferences
  Future<void> _loadSettings() async {
    try {
      final syncAutomatically = _prefs.getBool('sync_automatically') ?? true;
      final syncOnlyOnWifi = _prefs.getBool('sync_only_on_wifi') ?? true;
      final syncOnlyWhenCharging = _prefs.getBool('sync_only_when_charging') ?? false;
      final syncOnlyAboveBatteryLevel = _prefs.getBool('sync_only_above_battery_level') ?? true;
      final batteryLevelThreshold = _prefs.getInt('battery_level_threshold') ?? 20;
      final maxStorageSpace = _prefs.getInt('max_storage_space') ?? 1024 * 1024 * 1024; // 1 GB
      final autoCleanupExpiredContent = _prefs.getBool('auto_cleanup_expired_content') ?? true;
      final autoCleanupWhenStorageLow = _prefs.getBool('auto_cleanup_when_storage_low') ?? true;
      final storageThresholdForCleanup = _prefs.getInt('storage_threshold_for_cleanup') ?? 100 * 1024 * 1024; // 100 MB
      final defaultConflictResolutionIndex = _prefs.getInt('default_conflict_resolution') ?? 0;
      final defaultConflictResolution = ContentConflictResolution.values[defaultConflictResolutionIndex];
      final maxDailyBandwidthUsage = _prefs.getInt('max_daily_bandwidth_usage') ?? 0; // Unlimited
      final showOfflineContentIndicators = _prefs.getBool('show_offline_content_indicators') ?? true;
      final showSyncStatusNotifications = _prefs.getBool('show_sync_status_notifications') ?? true;
      final enableBackgroundSync = _prefs.getBool('enable_background_sync') ?? true;
      final backgroundSyncFrequency = _prefs.getInt('background_sync_frequency') ?? 60; // 1 hour
      
      state = OfflineSettings(
        syncAutomatically: syncAutomatically,
        syncOnlyOnWifi: syncOnlyOnWifi,
        syncOnlyWhenCharging: syncOnlyWhenCharging,
        syncOnlyAboveBatteryLevel: syncOnlyAboveBatteryLevel,
        batteryLevelThreshold: batteryLevelThreshold,
        maxStorageSpace: maxStorageSpace,
        autoCleanupExpiredContent: autoCleanupExpiredContent,
        autoCleanupWhenStorageLow: autoCleanupWhenStorageLow,
        storageThresholdForCleanup: storageThresholdForCleanup,
        defaultConflictResolution: defaultConflictResolution,
        maxDailyBandwidthUsage: maxDailyBandwidthUsage,
        showOfflineContentIndicators: showOfflineContentIndicators,
        showSyncStatusNotifications: showSyncStatusNotifications,
        enableBackgroundSync: enableBackgroundSync,
        backgroundSyncFrequency: backgroundSyncFrequency,
      );
    } catch (e) {
      // If there's an error, use default settings
      state = OfflineSettings.defaultSettings();
    }
  }
  
  /// Save settings to shared preferences
  Future<void> _saveSettings() async {
    await _prefs.setBool('sync_automatically', state.syncAutomatically);
    await _prefs.setBool('sync_only_on_wifi', state.syncOnlyOnWifi);
    await _prefs.setBool('sync_only_when_charging', state.syncOnlyWhenCharging);
    await _prefs.setBool('sync_only_above_battery_level', state.syncOnlyAboveBatteryLevel);
    await _prefs.setInt('battery_level_threshold', state.batteryLevelThreshold);
    await _prefs.setInt('max_storage_space', state.maxStorageSpace);
    await _prefs.setBool('auto_cleanup_expired_content', state.autoCleanupExpiredContent);
    await _prefs.setBool('auto_cleanup_when_storage_low', state.autoCleanupWhenStorageLow);
    await _prefs.setInt('storage_threshold_for_cleanup', state.storageThresholdForCleanup);
    await _prefs.setInt('default_conflict_resolution', state.defaultConflictResolution.index);
    await _prefs.setInt('max_daily_bandwidth_usage', state.maxDailyBandwidthUsage);
    await _prefs.setBool('show_offline_content_indicators', state.showOfflineContentIndicators);
    await _prefs.setBool('show_sync_status_notifications', state.showSyncStatusNotifications);
    await _prefs.setBool('enable_background_sync', state.enableBackgroundSync);
    await _prefs.setInt('background_sync_frequency', state.backgroundSyncFrequency);
  }
  
  /// Set sync automatically
  Future<void> setSyncAutomatically(bool value) async {
    state = state.copyWith(syncAutomatically: value);
    await _saveSettings();
  }
  
  /// Set sync only on WiFi
  Future<void> setSyncOnlyOnWifi(bool value) async {
    state = state.copyWith(syncOnlyOnWifi: value);
    await _saveSettings();
  }
  
  /// Set sync only when charging
  Future<void> setSyncOnlyWhenCharging(bool value) async {
    state = state.copyWith(syncOnlyWhenCharging: value);
    await _saveSettings();
  }
  
  /// Set sync only above battery level
  Future<void> setSyncOnlyAboveBatteryLevel(bool value) async {
    state = state.copyWith(syncOnlyAboveBatteryLevel: value);
    await _saveSettings();
  }
  
  /// Set battery level threshold
  Future<void> setBatteryLevelThreshold(int value) async {
    state = state.copyWith(batteryLevelThreshold: value);
    await _saveSettings();
  }
  
  /// Set max storage space
  Future<void> setMaxStorageSpace(int value) async {
    state = state.copyWith(maxStorageSpace: value);
    await _saveSettings();
  }
  
  /// Set auto cleanup expired content
  Future<void> setAutoCleanupExpiredContent(bool value) async {
    state = state.copyWith(autoCleanupExpiredContent: value);
    await _saveSettings();
  }
  
  /// Set auto cleanup when storage low
  Future<void> setAutoCleanupWhenStorageLow(bool value) async {
    state = state.copyWith(autoCleanupWhenStorageLow: value);
    await _saveSettings();
  }
  
  /// Set storage threshold for cleanup
  Future<void> setStorageThresholdForCleanup(int value) async {
    state = state.copyWith(storageThresholdForCleanup: value);
    await _saveSettings();
  }
  
  /// Set default conflict resolution
  Future<void> setDefaultConflictResolution(ContentConflictResolution value) async {
    state = state.copyWith(defaultConflictResolution: value);
    await _saveSettings();
  }
  
  /// Set max daily bandwidth usage
  Future<void> setMaxDailyBandwidthUsage(int value) async {
    state = state.copyWith(maxDailyBandwidthUsage: value);
    await _saveSettings();
  }
  
  /// Set show offline content indicators
  Future<void> setShowOfflineContentIndicators(bool value) async {
    state = state.copyWith(showOfflineContentIndicators: value);
    await _saveSettings();
  }
  
  /// Set show sync status notifications
  Future<void> setShowSyncStatusNotifications(bool value) async {
    state = state.copyWith(showSyncStatusNotifications: value);
    await _saveSettings();
  }
  
  /// Set enable background sync
  Future<void> setEnableBackgroundSync(bool value) async {
    state = state.copyWith(enableBackgroundSync: value);
    await _saveSettings();
  }
  
  /// Set background sync frequency
  Future<void> setBackgroundSyncFrequency(int value) async {
    state = state.copyWith(backgroundSyncFrequency: value);
    await _saveSettings();
  }
}

/// Provider for offline settings
final offlineSettingsProvider = StateNotifierProvider<OfflineSettingsNotifier, OfflineSettings>((ref) {
  final prefs = ref.watch(sharedPreferencesProvider);
  return OfflineSettingsNotifier(prefs);
});
