import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:culture_connect/models/offline/offline_content.dart';
import 'package:culture_connect/models/offline/sync_status.dart';
import 'package:culture_connect/providers/services_providers.dart';
import 'package:culture_connect/providers/shared_preferences_provider.dart';
import 'package:culture_connect/services/error_handling_service.dart';
import 'package:culture_connect/services/offline_mode_service.dart';

/// Provider for the offline mode service
final offlineModeServiceProvider = Provider<OfflineModeService>((ref) {
  final connectivity = Connectivity();
  final sharedPreferences = ref.watch(sharedPreferencesProvider);
  final loggingService = ref.watch(loggingServiceProvider);
  final errorHandlingService = ref.watch(errorHandlingServiceProvider);

  final service = OfflineModeService(
    connectivity,
    sharedPreferences,
    loggingService,
    errorHandlingService,
  );

  // Initialize the service
  service.initialize();

  // Dispose the service when the provider is disposed
  ref.onDispose(() {
    service.dispose();
  });

  return service;
});

/// Provider for the current connectivity status
final connectivityStatusProvider = StreamProvider<bool>((ref) {
  final offlineModeService = ref.watch(offlineModeServiceProvider);
  return offlineModeService.connectivityStream;
});

/// Provider for the current connectivity status (non-stream version)
final isOnlineProvider = Provider<bool>((ref) {
  final offlineModeService = ref.watch(offlineModeServiceProvider);
  return offlineModeService.isOnline;
});

/// Provider for sync status updates
final syncStatusUpdatesProvider = StreamProvider<SyncStatusUpdate>((ref) {
  final offlineModeService = ref.watch(offlineModeServiceProvider);
  return offlineModeService.syncStatusStream;
});

/// Provider for all offline content
final allOfflineContentProvider = Provider<List<OfflineContent>>((ref) {
  final offlineModeService = ref.watch(offlineModeServiceProvider);
  return offlineModeService.getAllOfflineContent();
});

/// Provider for offline content by type
final offlineContentByTypeProvider =
    Provider.family<List<OfflineContent>, String>((ref, contentType) {
  final offlineModeService = ref.watch(offlineModeServiceProvider);
  return offlineModeService.getOfflineContentByType(contentType);
});

/// Provider for checking if content is available offline
final isContentAvailableOfflineProvider =
    Provider.family<bool, String>((ref, contentId) {
  final offlineModeService = ref.watch(offlineModeServiceProvider);
  return offlineModeService.isContentAvailableOffline(contentId);
});

/// Provider for getting the sync status of content
final contentSyncStatusProvider =
    Provider.family<SyncStatus, String>((ref, contentId) {
  final offlineModeService = ref.watch(offlineModeServiceProvider);
  return offlineModeService.getContentSyncStatus(contentId);
});

/// Provider for the total size of offline content
final totalOfflineContentSizeProvider = FutureProvider<int>((ref) async {
  final offlineModeService = ref.watch(offlineModeServiceProvider);
  return offlineModeService.getTotalOfflineContentSize();
});

/// Provider for the current sync settings
final syncSettingsProvider = Provider<Map<String, dynamic>>((ref) {
  final offlineModeService = ref.watch(offlineModeServiceProvider);
  return offlineModeService.getSyncSettings();
});

/// Notifier for managing offline content
class OfflineContentNotifier
    extends StateNotifier<AsyncValue<List<OfflineContent>>> {
  final OfflineModeService _offlineModeService;

  OfflineContentNotifier(this._offlineModeService)
      : super(const AsyncValue.loading()) {
    _loadOfflineContent();
  }

  /// Load all offline content
  Future<void> _loadOfflineContent() async {
    state = const AsyncValue.loading();

    try {
      final content = _offlineModeService.getAllOfflineContent();
      state = AsyncValue.data(content);
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }

  /// Add content for offline use
  Future<bool> addContentForOfflineUse(OfflineContent content) async {
    try {
      final result = await _offlineModeService.addContentForOfflineUse(content);

      // Refresh the state
      _loadOfflineContent();

      return result;
    } catch (e) {
      return false;
    }
  }

  /// Remove content from offline storage
  Future<bool> removeOfflineContent(String contentId) async {
    try {
      final result = await _offlineModeService.removeOfflineContent(contentId);

      // Refresh the state
      _loadOfflineContent();

      return result;
    } catch (e) {
      return false;
    }
  }

  /// Sync all pending offline content
  Future<void> syncOfflineContent() async {
    await _offlineModeService.syncOfflineContent();

    // Refresh the state
    _loadOfflineContent();
  }

  /// Clear all offline content
  Future<bool> clearAllOfflineContent() async {
    try {
      final result = await _offlineModeService.clearAllOfflineContent();

      // Refresh the state
      _loadOfflineContent();

      return result;
    } catch (e) {
      return false;
    }
  }

  /// Update sync settings
  Future<void> updateSyncSettings({
    String? syncFrequency,
    bool? syncWifiOnly,
    bool? syncRequiresCharging,
  }) async {
    await _offlineModeService.updateSyncSettings(
      syncFrequency: syncFrequency,
      syncWifiOnly: syncWifiOnly,
      syncRequiresCharging: syncRequiresCharging,
    );
  }
}

/// Provider for the offline content notifier
final offlineContentNotifierProvider = StateNotifierProvider<
    OfflineContentNotifier, AsyncValue<List<OfflineContent>>>((ref) {
  final offlineModeService = ref.watch(offlineModeServiceProvider);
  return OfflineContentNotifier(offlineModeService);
});
