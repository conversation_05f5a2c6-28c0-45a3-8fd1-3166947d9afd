import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:culture_connect/models/travel/hotel_filter.dart';
import 'package:culture_connect/models/travel/travel.dart';
import 'package:culture_connect/providers/travel/travel_services_provider.dart';

/// Provider for the hotel filter
final hotelFilterProvider = StateProvider<HotelFilter>((ref) {
  return const HotelFilter();
});

/// Provider for filtered hotels
final filteredHotelsProvider = Provider<AsyncValue<List<Hotel>>>((ref) {
  final hotelsAsyncValue = ref.watch(hotelsProvider);
  final filter = ref.watch(hotelFilterProvider);
  
  return hotelsAsyncValue.when(
    data: (hotels) {
      final filtered = filter.apply(hotels);
      return AsyncValue.data(filtered);
    },
    loading: () => const AsyncValue.loading(),
    error: (error, stackTrace) => AsyncValue.error(error, stackTrace),
  );
});
