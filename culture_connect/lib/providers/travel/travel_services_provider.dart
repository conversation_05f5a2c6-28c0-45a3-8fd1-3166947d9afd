import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:culture_connect/models/travel/travel.dart';
import 'package:culture_connect/services/travel/travel_services_service.dart';

/// Provider for the travel services service
final travelServicesServiceProvider = Provider<TravelServicesService>((ref) {
  return TravelServicesService();
});

/// Provider for all travel services
final allTravelServicesProvider = FutureProvider<List<TravelService>>((ref) async {
  final travelServicesService = ref.watch(travelServicesServiceProvider);
  return travelServicesService.getAllTravelServices();
});

/// Provider for featured travel services
final featuredTravelServicesProvider = FutureProvider<List<TravelService>>((ref) async {
  final travelServicesService = ref.watch(travelServicesServiceProvider);
  return travelServicesService.getFeaturedTravelServices();
});

/// Provider for travel services on sale
final onSaleTravelServicesProvider = FutureProvider<List<TravelService>>((ref) async {
  final travelServicesService = ref.watch(travelServicesServiceProvider);
  return travelServicesService.getOnSaleTravelServices();
});

/// Provider for car rentals
final carRentalsProvider = FutureProvider<List<CarRental>>((ref) async {
  final travelServicesService = ref.watch(travelServicesServiceProvider);
  return travelServicesService.getCarRentals();
});

/// Provider for private security services
final privateSecurityServicesProvider = FutureProvider<List<PrivateSecurity>>((ref) async {
  final travelServicesService = ref.watch(travelServicesServiceProvider);
  return travelServicesService.getPrivateSecurityServices();
});

/// Provider for hotels
final hotelsProvider = FutureProvider<List<Hotel>>((ref) async {
  final travelServicesService = ref.watch(travelServicesServiceProvider);
  return travelServicesService.getHotels();
});

/// Provider for restaurants
final restaurantsProvider = FutureProvider<List<Restaurant>>((ref) async {
  final travelServicesService = ref.watch(travelServicesServiceProvider);
  return travelServicesService.getRestaurants();
});

/// Provider for flights
final flightsProvider = FutureProvider<List<Flight>>((ref) async {
  final travelServicesService = ref.watch(travelServicesServiceProvider);
  return travelServicesService.getFlights();
});

/// Provider for cruises
final cruisesProvider = FutureProvider<List<Cruise>>((ref) async {
  final travelServicesService = ref.watch(travelServicesServiceProvider);
  return travelServicesService.getCruises();
});

/// Provider for loyalty programs
final loyaltyProgramsProvider = FutureProvider<List<LoyaltyProgram>>((ref) async {
  final travelServicesService = ref.watch(travelServicesServiceProvider);
  return travelServicesService.getLoyaltyPrograms();
});

/// Provider for a specific travel service by ID
final travelServiceByIdProvider = FutureProvider.family<TravelService?, String>((ref, id) async {
  final travelServicesService = ref.watch(travelServicesServiceProvider);
  return travelServicesService.getTravelServiceById(id);
});

/// Provider for a specific car rental by ID
final carRentalByIdProvider = FutureProvider.family<CarRental?, String>((ref, id) async {
  final travelServicesService = ref.watch(travelServicesServiceProvider);
  return travelServicesService.getCarRentalById(id);
});

/// Provider for a specific private security service by ID
final privateSecurityByIdProvider = FutureProvider.family<PrivateSecurity?, String>((ref, id) async {
  final travelServicesService = ref.watch(travelServicesServiceProvider);
  return travelServicesService.getPrivateSecurityById(id);
});

/// Provider for a specific hotel by ID
final hotelByIdProvider = FutureProvider.family<Hotel?, String>((ref, id) async {
  final travelServicesService = ref.watch(travelServicesServiceProvider);
  return travelServicesService.getHotelById(id);
});

/// Provider for a specific restaurant by ID
final restaurantByIdProvider = FutureProvider.family<Restaurant?, String>((ref, id) async {
  final travelServicesService = ref.watch(travelServicesServiceProvider);
  return travelServicesService.getRestaurantById(id);
});

/// Provider for a specific flight by ID
final flightByIdProvider = FutureProvider.family<Flight?, String>((ref, id) async {
  final travelServicesService = ref.watch(travelServicesServiceProvider);
  return travelServicesService.getFlightById(id);
});

/// Provider for a specific cruise by ID
final cruiseByIdProvider = FutureProvider.family<Cruise?, String>((ref, id) async {
  final travelServicesService = ref.watch(travelServicesServiceProvider);
  return travelServicesService.getCruiseById(id);
});

/// Provider for a specific loyalty program by ID
final loyaltyProgramByIdProvider = FutureProvider.family<LoyaltyProgram?, String>((ref, id) async {
  final travelServicesService = ref.watch(travelServicesServiceProvider);
  return travelServicesService.getLoyaltyProgramById(id);
});

/// Provider for travel services by type
final travelServicesByTypeProvider = FutureProvider.family<List<TravelService>, TravelServiceType>((ref, type) async {
  final travelServicesService = ref.watch(travelServicesServiceProvider);
  return travelServicesService.getTravelServicesByType(type);
});

/// Provider for price alerts
final priceAlertsProvider = FutureProvider<List<PriceAlert>>((ref) async {
  final travelServicesService = ref.watch(travelServicesServiceProvider);
  return travelServicesService.getPriceAlerts();
});

/// Provider for price comparisons
final priceComparisonsProvider = FutureProvider.family<List<PriceComparison>, String>((ref, travelServiceId) async {
  final travelServicesService = ref.watch(travelServicesServiceProvider);
  return travelServicesService.getPriceComparisons(travelServiceId);
});

/// A model representing a price alert
class PriceAlert {
  /// Unique identifier for the price alert
  final String id;
  
  /// ID of the travel service
  final String travelServiceId;
  
  /// Type of travel service
  final TravelServiceType travelServiceType;
  
  /// Name of the travel service
  final String travelServiceName;
  
  /// Current price
  final double currentPrice;
  
  /// Target price
  final double targetPrice;
  
  /// Currency
  final String currency;
  
  /// When the price alert was created
  final DateTime createdAt;
  
  /// When the price alert was last updated
  final DateTime updatedAt;
  
  /// Creates a new price alert
  const PriceAlert({
    required this.id,
    required this.travelServiceId,
    required this.travelServiceType,
    required this.travelServiceName,
    required this.currentPrice,
    required this.targetPrice,
    required this.currency,
    required this.createdAt,
    required this.updatedAt,
  });
  
  /// Get the formatted current price
  String get formattedCurrentPrice {
    return '$currency${currentPrice.toStringAsFixed(2)}';
  }
  
  /// Get the formatted target price
  String get formattedTargetPrice {
    return '$currency${targetPrice.toStringAsFixed(2)}';
  }
  
  /// Get the price difference
  double get priceDifference {
    return currentPrice - targetPrice;
  }
  
  /// Get the formatted price difference
  String get formattedPriceDifference {
    return '$currency${priceDifference.abs().toStringAsFixed(2)}';
  }
  
  /// Get the price difference percentage
  double get priceDifferencePercentage {
    return (priceDifference / currentPrice) * 100;
  }
  
  /// Get the formatted price difference percentage
  String get formattedPriceDifferencePercentage {
    return '${priceDifferencePercentage.abs().toStringAsFixed(2)}%';
  }
}

/// A model representing a price comparison
class PriceComparison {
  /// Unique identifier for the price comparison
  final String id;
  
  /// ID of the travel service
  final String travelServiceId;
  
  /// Type of travel service
  final TravelServiceType travelServiceType;
  
  /// Name of the travel service
  final String travelServiceName;
  
  /// Provider name
  final String providerName;
  
  /// Provider logo URL
  final String providerLogoUrl;
  
  /// Price
  final double price;
  
  /// Currency
  final String currency;
  
  /// URL to the provider's website
  final String providerUrl;
  
  /// When the price comparison was created
  final DateTime createdAt;
  
  /// When the price comparison was last updated
  final DateTime updatedAt;
  
  /// Creates a new price comparison
  const PriceComparison({
    required this.id,
    required this.travelServiceId,
    required this.travelServiceType,
    required this.travelServiceName,
    required this.providerName,
    required this.providerLogoUrl,
    required this.price,
    required this.currency,
    required this.providerUrl,
    required this.createdAt,
    required this.updatedAt,
  });
  
  /// Get the formatted price
  String get formattedPrice {
    return '$currency${price.toStringAsFixed(2)}';
  }
}
