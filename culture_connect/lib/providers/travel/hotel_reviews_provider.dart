import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:culture_connect/models/travel/hotel_review.dart';
import 'package:culture_connect/providers/services_providers.dart';

/// Provider for hotel reviews
final hotelReviewsProvider =
    FutureProvider.family<List<HotelReview>, String>((ref, hotelId) async {
  final hotelReviewService = ref.watch(hotelReviewServiceProvider);
  return hotelReviewService.getReviewsForHotel(hotelId);
});

/// Provider for a specific hotel review by ID
final hotelReviewByIdProvider =
    FutureProvider.family<HotelReview?, String>((ref, reviewId) async {
  final hotelReviewService = ref.watch(hotelReviewServiceProvider);
  return hotelReviewService.getReviewById(reviewId);
});

/// Provider for hotel reviews by user
final hotelReviewsByUserProvider =
    FutureProvider.family<List<HotelReview>, String>((ref, userId) async {
  final hotelReviewService = ref.watch(hotelReviewServiceProvider);
  return hotelReviewService.getReviewsByUser(userId);
});

/// Provider for hotel review statistics
final hotelReviewStatsProvider =
    FutureProvider.family<Map<String, dynamic>, String>((ref, hotelId) async {
  final hotelReviewService = ref.watch(hotelReviewServiceProvider);
  return hotelReviewService.getReviewStats(hotelId);
});
