import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:culture_connect/models/travel/timeline.dart';
import 'package:culture_connect/models/travel/timeline_event.dart';
import 'package:culture_connect/models/travel/itinerary.dart';
import 'package:culture_connect/services/travel/timeline_service.dart';
import 'package:culture_connect/services/logging_service.dart';
import 'package:culture_connect/services/error_handling_service.dart';
import 'package:culture_connect/providers/auth_provider.dart';
import 'package:culture_connect/providers/shared_preferences_provider.dart';

/// Provider for the timeline service
final timelineServiceProvider = Provider<TimelineService>((ref) {
  // Get the required dependencies
  final sharedPreferences = ref.watch(sharedPreferencesProvider);
  final loggingService = ref.watch(loggingServiceProvider);
  final errorHandlingService = ref.watch(errorHandlingServiceProvider);

  return TimelineService(
    sharedPreferences,
    loggingService,
    errorHandlingService,
  );
});

/// Provider for all timelines
final timelinesProvider = FutureProvider<List<Timeline>>((ref) async {
  final timelineService = ref.watch(timelineServiceProvider);
  final userId = ref.watch(currentUserIdProvider) ?? 'all';
  // In a real implementation, we would check the auth state
  // For now, we'll just return all timelines for the current user or 'all' if no user is logged in
  return timelineService.getTimelines(userId);
});

/// Provider for a specific timeline
final timelineProvider =
    FutureProvider.family<Timeline?, String>((ref, id) async {
  final timelineService = ref.watch(timelineServiceProvider);
  return timelineService.getTimeline(id);
});

/// Provider for timelines for an itinerary
final timelinesForItineraryProvider =
    FutureProvider.family<List<Timeline>, String>((ref, itineraryId) async {
  final timelineService = ref.watch(timelineServiceProvider);
  return timelineService.getTimelinesForItinerary(itineraryId);
});

/// Provider for the current timeline
final currentTimelineProvider =
    StateNotifierProvider<CurrentTimelineNotifier, AsyncValue<Timeline?>>(
        (ref) {
  final timelineService = ref.watch(timelineServiceProvider);
  return CurrentTimelineNotifier(timelineService);
});

/// Notifier for the current timeline
class CurrentTimelineNotifier extends StateNotifier<AsyncValue<Timeline?>> {
  /// The timeline service
  final TimelineService _timelineService;

  /// Creates a new current timeline notifier
  CurrentTimelineNotifier(this._timelineService)
      : super(const AsyncValue.loading());

  /// Load a timeline
  Future<void> loadTimeline(String id) async {
    state = const AsyncValue.loading();

    try {
      final timeline = await _timelineService.getTimeline(id);
      state = AsyncValue.data(timeline);
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }

  /// Create a new timeline
  Future<void> createTimeline({
    required String userId,
    required String title,
    String? description,
    required DateTime startDate,
    required DateTime endDate,
    String? itineraryId,
    TimelineTheme theme = TimelineTheme.standard,
    TimelineVisibility visibility = TimelineVisibility.private,
  }) async {
    state = const AsyncValue.loading();

    try {
      final timeline = Timeline(
        userId: userId,
        title: title,
        description: description,
        startDate: startDate,
        endDate: endDate,
        itineraryId: itineraryId,
        theme: theme,
        visibility: visibility,
      );

      final createdTimeline = await _timelineService.createTimeline(timeline);
      state = AsyncValue.data(createdTimeline);
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }

  /// Create a timeline from an itinerary
  Future<void> createTimelineFromItinerary(Itinerary itinerary) async {
    state = const AsyncValue.loading();

    try {
      final timeline =
          await _timelineService.createTimelineFromItinerary(itinerary);
      state = AsyncValue.data(timeline);
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }

  /// Update the current timeline
  Future<void> updateTimeline({
    String? title,
    String? description,
    DateTime? startDate,
    DateTime? endDate,
    TimelineTheme? theme,
    TimelineVisibility? visibility,
  }) async {
    final currentState = state;
    if (currentState is! AsyncData<Timeline?>) return;
    if (currentState.value == null) return;

    final currentTimeline = currentState.value!;

    try {
      final updatedTimeline = currentTimeline.copyWith(
        title: title,
        description: description,
        startDate: startDate,
        endDate: endDate,
        theme: theme,
        visibility: visibility,
      );

      state = AsyncValue.data(updatedTimeline);

      final savedTimeline =
          await _timelineService.updateTimeline(updatedTimeline);
      state = AsyncValue.data(savedTimeline);
    } catch (e, stackTrace) {
      state = AsyncValue.data(currentTimeline);
      state = AsyncValue.error(e, stackTrace);
    }
  }

  /// Add an event to the current timeline
  Future<void> addEvent(TimelineEvent event) async {
    final currentState = state;
    if (currentState is! AsyncData<Timeline?>) return;
    if (currentState.value == null) return;

    final currentTimeline = currentState.value!;

    try {
      final updatedTimeline = currentTimeline.addEvent(event);

      state = AsyncValue.data(updatedTimeline);

      final savedTimeline =
          await _timelineService.updateTimeline(updatedTimeline);
      state = AsyncValue.data(savedTimeline);
    } catch (e, stackTrace) {
      state = AsyncValue.data(currentTimeline);
      state = AsyncValue.error(e, stackTrace);
    }
  }

  /// Update an event in the current timeline
  Future<void> updateEvent(TimelineEvent event) async {
    final currentState = state;
    if (currentState is! AsyncData<Timeline?>) return;
    if (currentState.value == null) return;

    final currentTimeline = currentState.value!;

    try {
      final updatedTimeline = currentTimeline.updateEvent(event);

      state = AsyncValue.data(updatedTimeline);

      final savedTimeline =
          await _timelineService.updateTimeline(updatedTimeline);
      state = AsyncValue.data(savedTimeline);
    } catch (e, stackTrace) {
      state = AsyncValue.data(currentTimeline);
      state = AsyncValue.error(e, stackTrace);
    }
  }

  /// Remove an event from the current timeline
  Future<void> removeEvent(String eventId) async {
    final currentState = state;
    if (currentState is! AsyncData<Timeline?>) return;
    if (currentState.value == null) return;

    final currentTimeline = currentState.value!;

    try {
      final updatedTimeline = currentTimeline.removeEvent(eventId);

      state = AsyncValue.data(updatedTimeline);

      final savedTimeline =
          await _timelineService.updateTimeline(updatedTimeline);
      state = AsyncValue.data(savedTimeline);
    } catch (e, stackTrace) {
      state = AsyncValue.data(currentTimeline);
      state = AsyncValue.error(e, stackTrace);
    }
  }

  /// Delete the current timeline
  Future<void> deleteTimeline() async {
    final currentState = state;
    if (currentState is! AsyncData<Timeline?>) return;
    if (currentState.value == null) return;

    final currentTimeline = currentState.value!;

    try {
      await _timelineService.deleteTimeline(
          currentTimeline.id, currentTimeline.userId);
      state = const AsyncValue.data(null);
    } catch (e, stackTrace) {
      state = AsyncValue.data(currentTimeline);
      state = AsyncValue.error(e, stackTrace);
    }
  }

  /// Clear the current timeline
  void clearTimeline() {
    state = const AsyncValue.data(null);
  }

  /// Add AR content to an event
  Future<void> addARContentToEvent(String eventId, String arContentId) async {
    final currentState = state;
    if (currentState is! AsyncData<Timeline?>) return;
    if (currentState.value == null) return;

    final currentTimeline = currentState.value!;

    try {
      // Find the event
      final eventIndex =
          currentTimeline.events.indexWhere((e) => e.id == eventId);
      if (eventIndex == -1) return;

      // Update the event
      final event = currentTimeline.events[eventIndex];
      final updatedEvent = event.copyWith(
        arContentId: arContentId,
        hasARContent: true,
      );

      // Update the timeline
      final updatedTimeline = currentTimeline.updateEvent(updatedEvent);

      state = AsyncValue.data(updatedTimeline);

      final savedTimeline =
          await _timelineService.updateTimeline(updatedTimeline);
      state = AsyncValue.data(savedTimeline);
    } catch (e, stackTrace) {
      state = AsyncValue.data(currentTimeline);
      state = AsyncValue.error(e, stackTrace);
    }
  }

  /// Remove AR content from an event
  Future<void> removeARContentFromEvent(String eventId) async {
    final currentState = state;
    if (currentState is! AsyncData<Timeline?>) return;
    if (currentState.value == null) return;

    final currentTimeline = currentState.value!;

    try {
      // Find the event
      final eventIndex =
          currentTimeline.events.indexWhere((e) => e.id == eventId);
      if (eventIndex == -1) return;

      // Update the event
      final event = currentTimeline.events[eventIndex];
      final updatedEvent = event.copyWith(
        arContentId: null,
        hasARContent: false,
      );

      // Update the timeline
      final updatedTimeline = currentTimeline.updateEvent(updatedEvent);

      state = AsyncValue.data(updatedTimeline);

      final savedTimeline =
          await _timelineService.updateTimeline(updatedTimeline);
      state = AsyncValue.data(savedTimeline);
    } catch (e, stackTrace) {
      state = AsyncValue.data(currentTimeline);
      state = AsyncValue.error(e, stackTrace);
    }
  }

  /// Get events with AR content
  List<TimelineEvent> getEventsWithARContent() {
    final currentState = state;
    if (currentState is! AsyncData<Timeline?>) return [];
    if (currentState.value == null) return [];

    return currentState.value!.eventsWithARContent;
  }
}
