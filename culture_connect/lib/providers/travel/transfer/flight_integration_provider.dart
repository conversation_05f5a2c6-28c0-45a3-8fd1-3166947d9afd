import 'package:flutter/material.dart';
import 'package:culture_connect/services/travel/transfer/flight_integration_service.dart';
import 'package:culture_connect/models/travel/flight/flight_info.dart';

/// Provider for flight integration
class FlightIntegrationProvider extends ChangeNotifier {
  // Services
  final FlightIntegrationService _flightService = FlightIntegrationService();

  // State
  final Map<String, FlightInfo> _flights = {};
  bool _isLoading = false;
  String? _error;

  /// Get loading state
  bool get isLoading => _isLoading;

  /// Get error
  String? get error => _error;

  /// Initialize the provider
  Future<void> initialize() async {
    await _flightService.initialize();
  }

  /// Get flight information
  Future<FlightInfo?> getFlightInfo(String flightNumber, DateTime date) async {
    final key = '${flightNumber}_${date.year}-${date.month}-${date.day}';

    // Check if we already have the flight info
    if (_flights.containsKey(key)) {
      return _flights[key];
    }

    // Otherwise, fetch it
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      final flightInfo = await _flightService.getFlightInfo(flightNumber, date);
      if (flightInfo != null) {
        _flights[key] = flightInfo;
      }
      _isLoading = false;
      notifyListeners();
      return flightInfo;
    } catch (e) {
      _isLoading = false;
      _error = 'Failed to get flight information: $e';
      notifyListeners();
      return null;
    }
  }

  /// Search for flights
  Future<List<FlightInfo>> searchFlights({
    String? departureAirportCode,
    String? arrivalAirportCode,
    DateTime? date,
    String? airlineCode,
  }) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      final flights = await _flightService.searchFlights(
        departureAirportCode: departureAirportCode,
        arrivalAirportCode: arrivalAirportCode,
        date: date,
        airlineCode: airlineCode,
      );

      // Cache the flights
      for (final flight in flights) {
        final key =
            '${flight.flightNumber}_${flight.scheduledDepartureTime.year}-${flight.scheduledDepartureTime.month}-${flight.scheduledDepartureTime.day}';
        _flights[key] = flight;
      }

      _isLoading = false;
      notifyListeners();
      return flights;
    } catch (e) {
      _isLoading = false;
      _error = 'Failed to search flights: $e';
      notifyListeners();
      return [];
    }
  }

  /// Track a flight
  Future<FlightInfo?> trackFlight(String flightNumber, DateTime date) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      final flightInfo = await _flightService.trackFlight(flightNumber, date);
      if (flightInfo != null) {
        final key = '${flightNumber}_${date.year}-${date.month}-${date.day}';
        _flights[key] = flightInfo;
      }
      _isLoading = false;
      notifyListeners();
      return flightInfo;
    } catch (e) {
      _isLoading = false;
      _error = 'Failed to track flight: $e';
      notifyListeners();
      return null;
    }
  }

  /// Parse a flight number
  static (String airlineCode, String flightNumber)? parseFlightNumber(
      String input) {
    // Remove any spaces
    final cleanInput = input.replaceAll(' ', '');

    // Flight numbers typically have 2-3 letter airline code followed by 1-4 digits
    final regex = RegExp(r'^([A-Z]{2,3})(\d{1,4})$', caseSensitive: false);
    final match = regex.firstMatch(cleanInput);

    if (match != null) {
      final airlineCode = match.group(1)!.toUpperCase();
      final flightNumber = match.group(2)!;
      return (airlineCode, flightNumber);
    }

    return null;
  }

  /// Format a flight number
  static String formatFlightNumber(String airlineCode, String flightNumber) {
    return '$airlineCode $flightNumber';
  }
}
