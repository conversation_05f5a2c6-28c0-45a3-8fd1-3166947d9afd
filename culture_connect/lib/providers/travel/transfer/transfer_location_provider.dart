import 'package:flutter/material.dart';
import 'package:culture_connect/models/location/geo_location.dart';
import 'package:culture_connect/models/travel/transfer/transfer_location.dart';
import 'package:culture_connect/services/travel/transfer/transfer_location_service.dart';

/// Provider for transfer locations
class TransferLocationProvider extends ChangeNotifier {
  // Services
  final TransferLocationService _locationService = TransferLocationService();

  // State
  List<TransferLocation> _commonLocations = [];
  List<TransferLocation> _savedLocations = [];
  List<TransferLocation> _searchResults = [];
  bool _isLoading = false;
  String? _error;

  /// Get common locations
  List<TransferLocation> get commonLocations => _commonLocations;

  /// Get saved locations
  List<TransferLocation> get savedLocations => _savedLocations;

  /// Get search results
  List<TransferLocation> get searchResults => _searchResults;

  /// Get loading state
  bool get isLoading => _isLoading;

  /// Get error
  String? get error => _error;

  /// Get airports
  List<TransferLocation> get airports => _commonLocations
      .where((location) => location.type == TransferLocationType.airport)
      .toList();

  /// Get hotels
  List<TransferLocation> get hotels => _commonLocations
      .where((location) => location.type == TransferLocationType.hotel)
      .toList();

  /// Initialize the provider
  Future<void> initialize() async {
    await Future.wait([
      loadCommonLocations(),
      loadSavedLocations(),
    ]);
  }

  /// Load common locations
  Future<void> loadCommonLocations() async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      _commonLocations = await _locationService.getCommonLocations();
      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _isLoading = false;
      _error = 'Failed to load common locations: $e';
      notifyListeners();
    }
  }

  /// Load saved locations
  Future<void> loadSavedLocations() async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      _savedLocations = await _locationService.getSavedLocations();
      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _isLoading = false;
      _error = 'Failed to load saved locations: $e';
      notifyListeners();
    }
  }

  /// Get a location by ID
  Future<TransferLocation?> getLocation(String id) async {
    try {
      return await _locationService.getLocation(id);
    } catch (e) {
      _error = 'Failed to get location: $e';
      notifyListeners();
      return null;
    }
  }

  /// Search for locations
  Future<List<TransferLocation>> searchLocations(String query) async {
    if (query.isEmpty) {
      _searchResults = [];
      notifyListeners();
      return [];
    }

    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      _searchResults = await _locationService.searchLocations(query);
      _isLoading = false;
      notifyListeners();
      return _searchResults;
    } catch (e) {
      _isLoading = false;
      _error = 'Failed to search locations: $e';
      notifyListeners();
      return [];
    }
  }

  /// Get airports near a location
  Future<List<TransferLocation>> getNearbyAirports(GeoLocation coordinates,
      {double radiusKm = 50.0}) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      final airports = await _locationService.getNearbyAirports(coordinates,
          radiusKm: radiusKm);
      _isLoading = false;
      notifyListeners();
      return airports;
    } catch (e) {
      _isLoading = false;
      _error = 'Failed to get nearby airports: $e';
      notifyListeners();
      return [];
    }
  }

  /// Save a location
  Future<TransferLocation?> saveLocation(TransferLocation location) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      final savedLocation = await _locationService.saveLocation(location);
      if (!_savedLocations.any((loc) => loc.id == savedLocation.id)) {
        _savedLocations.add(savedLocation);
      }
      _isLoading = false;
      notifyListeners();
      return savedLocation;
    } catch (e) {
      _isLoading = false;
      _error = 'Failed to save location: $e';
      notifyListeners();
      return null;
    }
  }

  /// Remove a saved location
  Future<bool> removeSavedLocation(String id) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      await _locationService.removeSavedLocation(id);
      _savedLocations.removeWhere((location) => location.id == id);
      _isLoading = false;
      notifyListeners();
      return true;
    } catch (e) {
      _isLoading = false;
      _error = 'Failed to remove saved location: $e';
      notifyListeners();
      return false;
    }
  }
}
