import 'package:flutter/material.dart';
import 'package:culture_connect/models/travel/transfer/transfers.dart';
import 'package:culture_connect/services/travel/transfer/transfer_service.dart'
    as transfer_service;

// Type aliases to make the code more readable
typedef TransferServiceModel = TransferService;

/// Provider for transfer services and bookings
class TransferProvider extends ChangeNotifier {
  // Services
  final transfer_service.TransferServiceManager _transferService =
      transfer_service.TransferServiceManager();

  // State
  List<TransferServiceModel> _transfers = [];
  List<TransferBooking> _bookings = [];
  bool _isLoading = false;
  String? _error;

  /// Get all transfers
  List<TransferService> get transfers => _transfers;

  /// Get all bookings
  List<TransferBooking> get bookings => _bookings;

  /// Get loading state
  bool get isLoading => _isLoading;

  /// Get error
  String? get error => _error;

  /// Get featured transfers
  List<TransferService> get featuredTransfers =>
      _transfers.where((transfer) => transfer.isFeatured).toList();

  /// Get transfers on sale
  List<TransferService> get transfersOnSale =>
      _transfers.where((transfer) => transfer.isOnSale).toList();

  /// Get upcoming bookings
  List<TransferBooking> get upcomingBookings => _bookings
      .where((booking) =>
          booking.status == TransferBookingStatus.confirmed ||
          booking.status == TransferBookingStatus.pending)
      .where((booking) => booking.pickupDateTime.isAfter(DateTime.now()))
      .toList()
    ..sort((a, b) => a.pickupDateTime.compareTo(b.pickupDateTime));

  /// Get past bookings
  List<TransferBooking> get pastBookings => _bookings
      .where((booking) =>
          booking.status == TransferBookingStatus.completed ||
          booking.pickupDateTime.isBefore(DateTime.now()))
      .toList()
    ..sort((a, b) => b.pickupDateTime.compareTo(a.pickupDateTime));

  /// Get cancelled bookings
  List<TransferBooking> get cancelledBookings => _bookings
      .where((booking) =>
          booking.status == TransferBookingStatus.cancelled ||
          booking.status == TransferBookingStatus.refunded ||
          booking.status == TransferBookingStatus.noShow)
      .toList()
    ..sort((a, b) => b.pickupDateTime.compareTo(a.pickupDateTime));

  /// Initialize the provider
  Future<void> initialize() async {
    await Future.wait([
      loadTransfers(),
      loadBookings(),
    ]);
  }

  /// Load all transfers
  Future<void> loadTransfers() async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      final results = await _transferService.getTransfers();
      _transfers = results.cast<TransferServiceModel>();
      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _isLoading = false;
      _error = 'Failed to load transfers: $e';
      notifyListeners();
    }
  }

  /// Load all bookings
  Future<void> loadBookings() async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      // In a real app, we would pass the current user ID
      _bookings = await _transferService.getBookings('user1');
      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _isLoading = false;
      _error = 'Failed to load bookings: $e';
      notifyListeners();
    }
  }

  /// Get a transfer by ID
  Future<TransferServiceModel?> getTransfer(String id) async {
    try {
      final result = await _transferService.getTransfer(id);
      return result as TransferServiceModel?;
    } catch (e) {
      _error = 'Failed to get transfer: $e';
      notifyListeners();
      return null;
    }
  }

  /// Get a booking by ID
  Future<TransferBooking?> getBooking(String id) async {
    try {
      return await _transferService.getBooking(id);
    } catch (e) {
      _error = 'Failed to get booking: $e';
      notifyListeners();
      return null;
    }
  }

  /// Search for transfers
  Future<List<TransferServiceModel>> searchTransfers({
    String? location,
    TransferVehicleType? vehicleType,
    int? passengerCount,
    int? luggageCount,
    bool? isPrivate,
    double? maxPrice,
  }) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      final results = await _transferService.searchTransfers(
        location: location,
        vehicleType: vehicleType,
        passengerCount: passengerCount,
        luggageCount: luggageCount,
        isPrivate: isPrivate,
        maxPrice: maxPrice,
      );
      _isLoading = false;
      notifyListeners();
      return results.cast<TransferServiceModel>();
    } catch (e) {
      _isLoading = false;
      _error = 'Failed to search transfers: $e';
      notifyListeners();
      return [];
    }
  }

  /// Book a transfer
  Future<TransferBooking?> bookTransfer(TransferBooking booking) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      final newBooking = await _transferService.createBooking(booking);
      _bookings.add(newBooking);
      _isLoading = false;
      notifyListeners();
      return newBooking;
    } catch (e) {
      _isLoading = false;
      _error = 'Failed to book transfer: $e';
      notifyListeners();
      return null;
    }
  }

  /// Update a booking
  Future<TransferBooking?> updateBooking(TransferBooking booking) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      final updatedBooking = await _transferService.updateBooking(booking);
      final index = _bookings.indexWhere((b) => b.id == booking.id);
      if (index != -1) {
        _bookings[index] = updatedBooking;
      }
      _isLoading = false;
      notifyListeners();
      return updatedBooking;
    } catch (e) {
      _isLoading = false;
      _error = 'Failed to update booking: $e';
      notifyListeners();
      return null;
    }
  }

  /// Cancel a booking
  Future<TransferBooking?> cancelBooking(String id, {String? reason}) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      final updatedBooking = await _transferService.cancelBooking(
          id, reason ?? 'Cancelled by user');
      final index = _bookings.indexWhere((b) => b.id == id);
      if (index != -1) {
        _bookings[index] = updatedBooking;
      }
      _isLoading = false;
      notifyListeners();
      return updatedBooking;
    } catch (e) {
      _isLoading = false;
      _error = 'Failed to cancel booking: $e';
      notifyListeners();
      return null;
    }
  }
}
