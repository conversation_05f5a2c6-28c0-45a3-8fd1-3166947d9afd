import 'package:flutter/material.dart';
import 'package:culture_connect/models/travel/document/visa_requirement.dart';
import 'package:culture_connect/services/travel/document/visa_requirement_service.dart';

/// Provider for visa requirements
class VisaRequirementProvider extends ChangeNotifier {
  // Services
  final VisaRequirementService _requirementService = VisaRequirementService();
  
  // State
  Map<String, VisaRequirement> _requirements = {};
  bool _isLoading = false;
  String? _error;
  
  /// Get all requirements
  List<VisaRequirement> get requirements => _requirements.values.toList();
  
  /// Get loading state
  bool get isLoading => _isLoading;
  
  /// Get error
  String? get error => _error;
  
  /// Initialize the provider
  Future<void> initialize() async {
    await loadRequirements();
  }
  
  /// Load all requirements
  Future<void> loadRequirements() async {
    _isLoading = true;
    _error = null;
    notifyListeners();
    
    try {
      final requirements = await _requirementService.getAllVisaRequirements();
      _requirements = {
        for (var req in requirements) '${req.countryFrom}_${req.countryTo}': req
      };
      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _isLoading = false;
      _error = 'Failed to load visa requirements: $e';
      notifyListeners();
    }
  }
  
  /// Get visa requirement between two countries
  Future<VisaRequirement?> getVisaRequirement(String countryFrom, String countryTo) async {
    final key = '${countryFrom}_$countryTo';
    
    // Check if we already have the requirement
    if (_requirements.containsKey(key)) {
      return _requirements[key];
    }
    
    // Otherwise, fetch it
    _isLoading = true;
    _error = null;
    notifyListeners();
    
    try {
      final requirement = await _requirementService.getVisaRequirement(countryFrom, countryTo);
      if (requirement != null) {
        _requirements[key] = requirement;
      }
      _isLoading = false;
      notifyListeners();
      return requirement;
    } catch (e) {
      _isLoading = false;
      _error = 'Failed to get visa requirement: $e';
      notifyListeners();
      return null;
    }
  }
  
  /// Check if a visa is required for a destination
  Future<bool> isVisaRequired(String countryFrom, String countryTo) async {
    try {
      return await _requirementService.isVisaRequired(countryFrom, countryTo);
    } catch (e) {
      _error = 'Failed to check visa requirement: $e';
      notifyListeners();
      return true; // Assume visa is required if there's an error
    }
  }
  
  /// Add a new visa requirement
  Future<VisaRequirement?> addVisaRequirement(VisaRequirement requirement) async {
    _isLoading = true;
    _error = null;
    notifyListeners();
    
    try {
      final newRequirement = await _requirementService.addVisaRequirement(requirement);
      final key = '${newRequirement.countryFrom}_${newRequirement.countryTo}';
      _requirements[key] = newRequirement;
      _isLoading = false;
      notifyListeners();
      return newRequirement;
    } catch (e) {
      _isLoading = false;
      _error = 'Failed to add visa requirement: $e';
      notifyListeners();
      return null;
    }
  }
  
  /// Update a visa requirement
  Future<VisaRequirement?> updateVisaRequirement(VisaRequirement requirement) async {
    _isLoading = true;
    _error = null;
    notifyListeners();
    
    try {
      final updatedRequirement = await _requirementService.updateVisaRequirement(requirement);
      final key = '${updatedRequirement.countryFrom}_${updatedRequirement.countryTo}';
      _requirements[key] = updatedRequirement;
      _isLoading = false;
      notifyListeners();
      return updatedRequirement;
    } catch (e) {
      _isLoading = false;
      _error = 'Failed to update visa requirement: $e';
      notifyListeners();
      return null;
    }
  }
}
