// Flutter imports
import 'package:flutter/material.dart';

// Package imports
import 'package:flutter_riverpod/flutter_riverpod.dart';

// Project imports
import 'package:culture_connect/models/travel/flight/additional_service.dart';
import 'package:culture_connect/models/travel/flight/booking_info.dart';
import 'package:culture_connect/models/travel/flight/passenger_info.dart';
import 'package:culture_connect/models/travel/flight_filter.dart';
import 'package:culture_connect/models/travel/flight_search_params.dart';
import 'package:culture_connect/models/travel/travel.dart';
import 'package:culture_connect/providers/services_providers.dart';
import 'package:culture_connect/services/travel/flight_search_service.dart'
    hide AdditionalService;

/// Helper method to get a default date of birth based on passenger type
DateTime _getDefaultDateOfBirth(PassengerType type) {
  final now = DateTime.now();
  switch (type) {
    case PassengerType.adult:
      return DateTime(now.year - 30, now.month, now.day); // 30 years ago
    case PassengerType.child:
      return DateTime(now.year - 10, now.month, now.day); // 10 years ago
    case PassengerType.infant:
      return DateTime(now.year - 1, now.month, now.day); // 1 year ago
  }
}

/// Notifier for flight search parameters
class FlightSearchParamsNotifier extends StateNotifier<FlightSearchParams> {
  FlightSearchParamsNotifier() : super(FlightSearchParams.defaultParams());

  /// Update the search parameters
  void updateSearchParams(FlightSearchParams params) {
    state = params;
  }

  /// Update trip type
  void updateTripType(TripType tripType) {
    state = state.copyWith(tripType: tripType);
  }

  /// Update routes
  void updateRoutes(List<FlightRoute> routes) {
    state = state.copyWith(routes: routes);
  }

  /// Update origin for route at index
  void updateOrigin(int routeIndex, String code, String name, String city) {
    final routes = List<FlightRoute>.from(state.routes);
    if (routeIndex < routes.length) {
      routes[routeIndex] = routes[routeIndex].copyWith(
        originCode: code,
        originName: name,
        originCity: city,
      );
      state = state.copyWith(routes: routes);
    }
  }

  /// Update destination for route at index
  void updateDestination(
      int routeIndex, String code, String name, String city) {
    final routes = List<FlightRoute>.from(state.routes);
    if (routeIndex < routes.length) {
      routes[routeIndex] = routes[routeIndex].copyWith(
        destinationCode: code,
        destinationName: name,
        destinationCity: city,
      );
      state = state.copyWith(routes: routes);
    }
  }

  /// Update departure date for route at index
  void updateDepartureDate(int routeIndex, DateTime date) {
    final routes = List<FlightRoute>.from(state.routes);
    if (routeIndex < routes.length) {
      routes[routeIndex] = routes[routeIndex].copyWith(
        departureDate: date,
      );
      state = state.copyWith(routes: routes);
    }
  }

  /// Update passengers
  void updatePassengers(List<Passenger> passengers) {
    state = state.copyWith(passengers: passengers);
  }

  /// Update passenger count by type
  void updatePassengerCount(PassengerType type, int count) {
    final passengers = List<Passenger>.from(state.passengers);
    final index = passengers.indexWhere((p) => p.type == type);

    if (index >= 0) {
      passengers[index] = passengers[index].copyWith(count: count);
    } else {
      passengers.add(Passenger(type: type, count: count));
    }

    state = state.copyWith(passengers: passengers);
  }

  /// Update flight class
  void updateFlightClass(FlightClass flightClass) {
    state = state.copyWith(flightClass: flightClass);
  }

  /// Update direct flights only
  void updateDirectFlightsOnly(bool directFlightsOnly) {
    state = state.copyWith(directFlightsOnly: directFlightsOnly);
  }

  /// Update flexible dates
  void updateFlexibleDates(bool flexibleDates) {
    state = state.copyWith(flexibleDates: flexibleDates);
  }

  /// Update preferred airlines
  void updatePreferredAirlines(List<String> preferredAirlines) {
    state = state.copyWith(preferredAirlines: preferredAirlines);
  }

  /// Reset to default parameters
  void reset() {
    state = FlightSearchParams.defaultParams();
  }
}

/// Provider for flight search parameters
final flightSearchParamsProvider =
    StateNotifierProvider<FlightSearchParamsNotifier, FlightSearchParams>(
        (ref) {
  return FlightSearchParamsNotifier();
});

/// Notifier for flight filter
class FlightFilterNotifier extends StateNotifier<FlightFilter> {
  FlightFilterNotifier() : super(const FlightFilter());

  /// Update the filter
  void updateFilter(FlightFilter filter) {
    state = filter;
  }

  /// Update price range
  void updatePriceRange(RangeValues priceRange) {
    state = state.copyWith(priceRange: priceRange);
  }

  /// Update departure time range
  void updateDepartureTimeRange(TimeOfDay start, TimeOfDay end) {
    final timeRange = TimeRange.fromTimeOfDay(start: start, end: end);
    state = state.copyWith(departureTimeRange: timeRange);
  }

  /// Update arrival time range
  void updateArrivalTimeRange(TimeOfDay start, TimeOfDay end) {
    final timeRange = TimeRange.fromTimeOfDay(start: start, end: end);
    state = state.copyWith(arrivalTimeRange: timeRange);
  }

  /// Update maximum duration
  void updateMaxDuration(int minutes) {
    state = state.copyWith(maxDurationMinutes: minutes);
  }

  /// Update flight types
  void updateFlightTypes(List<FlightType> types) {
    state = state.copyWith(flightTypes: types);
  }

  /// Update airlines
  void updateAirlines(List<String> airlines) {
    state = state.copyWith(airlines: airlines);
  }

  /// Update flight classes
  void updateFlightClasses(List<FlightClass> classes) {
    state = state.copyWith(flightClasses: classes);
  }

  /// Update sort option
  void updateSortOption(FlightSortOption option) {
    state = state.copyWith(sortOption: option);
  }

  /// Reset to default filter
  void reset() {
    state = const FlightFilter();
  }
}

/// Provider for flight filter
final flightFilterProvider =
    StateNotifierProvider<FlightFilterNotifier, FlightFilter>((ref) {
  return FlightFilterNotifier();
});

/// Provider for searched flights
final searchedFlightsProvider = FutureProvider<List<Flight>>((ref) async {
  final flightSearchService = ref.watch(flightSearchServiceProvider);
  final searchParams = ref.watch(flightSearchParamsProvider);

  return flightSearchService.searchFlights(searchParams);
});

/// Provider for filtered flights
final filteredFlightsProvider = Provider<AsyncValue<List<Flight>>>((ref) {
  final searchedFlightsAsyncValue = ref.watch(searchedFlightsProvider);
  final filter = ref.watch(flightFilterProvider);

  return searchedFlightsAsyncValue.when(
    data: (flights) {
      final filtered = filter.apply(flights);
      return AsyncValue.data(filtered);
    },
    loading: () => const AsyncValue.loading(),
    error: (error, stackTrace) => AsyncValue.error(error, stackTrace),
  );
});

/// Provider for available airports
final airportsProvider = FutureProvider<List<Airport>>((ref) async {
  final flightSearchService = ref.watch(flightSearchServiceProvider);
  return flightSearchService.getAirports();
});

/// Provider for popular airports
final popularAirportsProvider = FutureProvider<List<Airport>>((ref) async {
  final flightSearchService = ref.watch(flightSearchServiceProvider);
  return flightSearchService.getPopularAirports();
});

/// Provider for recent airports
final recentAirportsProvider = FutureProvider<List<Airport>>((ref) async {
  final flightSearchService = ref.watch(flightSearchServiceProvider);
  return flightSearchService.getRecentAirports();
});

/// Provider for available airlines
final airlinesProvider = FutureProvider<List<Airline>>((ref) async {
  final flightSearchService = ref.watch(flightSearchServiceProvider);
  return flightSearchService.getAirlines();
});

/// Provider for fare calendar
final fareCalendarProvider =
    FutureProvider.family<Map<DateTime, double>, FlightRoute>(
        (ref, route) async {
  final flightSearchService = ref.watch(flightSearchServiceProvider);
  return flightSearchService.getFareCalendar(route);
});

/// Provider for price history
final priceHistoryProvider =
    FutureProvider.family<List<PriceHistoryPoint>, FlightRoute>(
        (ref, route) async {
  final flightSearchService = ref.watch(flightSearchServiceProvider);
  return flightSearchService.getPriceHistory(route);
});

/// Provider for flight details
final flightDetailsProvider =
    FutureProvider.family<FlightDetails?, String>((ref, flightId) async {
  final flightSearchService = ref.watch(flightSearchServiceProvider);
  return flightSearchService.getFlightDetails(flightId);
});

/// Provider for seat map
final seatMapProvider =
    FutureProvider.family<SeatMap?, String>((ref, flightId) async {
  final flightSearchService = ref.watch(flightSearchServiceProvider);
  return flightSearchService.getSeatMap(flightId);
});

/// Provider for baggage information
final baggageInfoProvider =
    FutureProvider.family<BaggageInfo?, String>((ref, flightId) async {
  final flightSearchService = ref.watch(flightSearchServiceProvider);
  return flightSearchService.getBaggageInfo(flightId);
});

/// Provider for flight status
final flightStatusProvider =
    FutureProvider.family<FlightStatus?, String>((ref, flightId) async {
  final flightSearchService = ref.watch(flightSearchServiceProvider);
  return flightSearchService.getFlightStatus(flightId);
});

/// Provider for fare breakdown
final fareBreakdownProvider =
    FutureProvider.family<FareBreakdown?, String>((ref, flightId) async {
  final flightSearchService = ref.watch(flightSearchServiceProvider);
  return flightSearchService.getFareBreakdown(flightId);
});

/// Notifier for passenger information
class PassengerInfoNotifier extends StateNotifier<List<PassengerInfo>> {
  PassengerInfoNotifier(this.ref) : super([]) {
    _initializeFromSearchParams();
  }

  final Ref ref;

  void _initializeFromSearchParams() {
    final searchParams = ref.read(flightSearchParamsProvider);
    final passengerInfo = <PassengerInfo>[];

    // Create passenger info based on search parameters
    for (final passenger in searchParams.passengers) {
      for (var i = 0; i < passenger.count; i++) {
        passengerInfo.add(
          PassengerInfo(
            type: passenger.type,
            firstName: '',
            lastName: '',
            dateOfBirth: _getDefaultDateOfBirth(passenger.type),
            gender: null, // Will be selected by user
            passportNumber: '',
            passportExpiryDate:
                DateTime.now().add(const Duration(days: 365 * 5)),
            nationality: '',
            specialAssistance: false,
            specialAssistanceDetails: '',
            seatPreference: SeatPreference.none, // Default preference
            mealPreference: MealPreference.none, // Default preference
          ),
        );
      }
    }

    state = passengerInfo;
  }

  /// Add a passenger
  void addPassenger(PassengerInfo passenger) {
    state = [...state, passenger];
  }

  /// Update a passenger
  void updatePassenger(int index, PassengerInfo passenger) {
    if (index >= 0 && index < state.length) {
      final newList = List<PassengerInfo>.from(state);
      newList[index] = passenger;
      state = newList;
    }
  }

  /// Remove a passenger
  void removePassenger(int index) {
    if (index >= 0 && index < state.length) {
      final newList = List<PassengerInfo>.from(state);
      newList.removeAt(index);
      state = newList;
    }
  }

  /// Clear all passengers
  void clear() {
    state = [];
  }
}

/// Provider for passenger information
final passengerInfoProvider =
    StateNotifierProvider<PassengerInfoNotifier, List<PassengerInfo>>((ref) {
  return PassengerInfoNotifier(ref);
});

/// Notifier for booking information
class BookingInfoNotifier extends StateNotifier<BookingInfo?> {
  BookingInfoNotifier() : super(null);

  /// Set booking info
  void setBookingInfo(BookingInfo bookingInfo) {
    state = bookingInfo;
  }

  /// Update booking info
  void updateBookingInfo({
    dynamic flight, // Use dynamic to avoid type conflicts
    List<PassengerInfo>? passengers,
    Map<String, String>? selectedSeats,
    List<AdditionalService>? additionalServices,
    String? contactName,
    String? contactEmail,
    String? contactPhone,
    double? totalPrice,
    String? currency,
    String? bookingReference,
    String? paymentMethod,
    String? paymentTransactionId,
  }) {
    if (state != null) {
      state = state!.copyWith(
        flight: flight,
        passengers: passengers,
        selectedSeats: selectedSeats,
        additionalServices: additionalServices,
        contactName: contactName,
        contactEmail: contactEmail,
        contactPhone: contactPhone,
        totalPrice: totalPrice,
        currency: currency,
        bookingReference: bookingReference,
        paymentMethod: paymentMethod,
        paymentTransactionId: paymentTransactionId,
      );
    }
  }

  /// Clear booking info
  void clear() {
    state = null;
  }
}

/// Provider for booking information
final bookingInfoProvider =
    StateNotifierProvider<BookingInfoNotifier, BookingInfo?>((ref) {
  return BookingInfoNotifier();
});

/// Notifier for selected flight
class SelectedFlightNotifier extends StateNotifier<dynamic> {
  SelectedFlightNotifier() : super(null);

  /// Set selected flight
  void setFlight(dynamic flight) {
    state = flight;
  }

  /// Clear selected flight
  void clear() {
    state = null;
  }
}

/// Provider for selected flight
final selectedFlightProvider =
    StateNotifierProvider<SelectedFlightNotifier, dynamic>((ref) {
  return SelectedFlightNotifier();
});

/// Notifier for selected seats
class SelectedSeatsNotifier extends StateNotifier<Map<String, String>> {
  SelectedSeatsNotifier() : super({});

  /// Select a seat for a passenger
  void selectSeat(String passengerId, String seatNumber) {
    state = {...state, passengerId: seatNumber};
  }

  /// Unselect a seat for a passenger
  void unselectSeat(String passengerId) {
    final newMap = Map<String, String>.from(state);
    newMap.remove(passengerId);
    state = newMap;
  }

  /// Clear all selected seats
  void clear() {
    state = {};
  }
}

/// Provider for selected seats
final selectedSeatsProvider =
    StateNotifierProvider<SelectedSeatsNotifier, Map<String, String>>((ref) {
  return SelectedSeatsNotifier();
});

/// Notifier for selected additional services
class SelectedServicesNotifier extends StateNotifier<List<AdditionalService>> {
  SelectedServicesNotifier() : super([]);

  /// Add a service
  void addService(AdditionalService service) {
    state = [...state, service];
  }

  /// Remove a service
  void removeService(AdditionalService service) {
    state = state.where((s) => s.id != service.id).toList();
  }

  /// Clear all services
  void clear() {
    state = [];
  }
}

/// Provider for selected additional services
final selectedServicesProvider =
    StateNotifierProvider<SelectedServicesNotifier, List<AdditionalService>>(
        (ref) {
  return SelectedServicesNotifier();
});

/// Provider for booking summary data
/// This is a simplified version that avoids type conflicts between different Flight classes
final bookingSummaryDataProvider = Provider<Map<String, dynamic>?>((ref) {
  final selectedFlight = ref.watch(selectedFlightProvider);
  final passengerInfo = ref.watch(passengerInfoProvider);
  final selectedSeats = ref.watch(selectedSeatsProvider);
  final selectedServices = ref.watch(selectedServicesProvider);

  if (selectedFlight == null) {
    return null;
  }

  try {
    // Try to safely access the flight ID
    final flightId = selectedFlight is Map
        ? selectedFlight['id']
        : (selectedFlight?.id ?? 'unknown');

    final fareBreakdownAsyncValue =
        ref.watch(fareBreakdownProvider(flightId.toString()));

    return fareBreakdownAsyncValue.when(
      data: (fareBreakdown) {
        if (fareBreakdown == null) {
          return null;
        }

        // Calculate total price
        double totalPrice = fareBreakdown.totalPrice;

        // Add price for selected services
        for (final service in selectedServices) {
          totalPrice += service.price;
        }

        // Safely extract flight properties
        final flightName = selectedFlight is Map
            ? selectedFlight['name']
            : (selectedFlight?.name ?? 'Unknown Flight');

        final flightPrice = selectedFlight is Map
            ? (selectedFlight['price'] ?? 0.0)
            : (selectedFlight?.price ?? 0.0);

        final flightCurrency = selectedFlight is Map
            ? (selectedFlight['currency'] ?? 'USD')
            : (selectedFlight?.currency ?? 'USD');

        // Return a map with all the necessary data
        // This avoids type conflicts between different Flight classes
        return {
          'flightId': flightId,
          'flightName': flightName,
          'flightPrice': flightPrice,
          'flightCurrency': flightCurrency,
          'passengers': passengerInfo,
          'selectedSeats': selectedSeats,
          'selectedServices': selectedServices,
          'totalPrice': totalPrice,
        };
      },
      loading: () => null,
      error: (_, __) => null,
    );
  } catch (e) {
    // Handle any errors that might occur when accessing properties
    debugPrint('Error in bookingSummaryDataProvider: $e');
    return null;
  }
});
