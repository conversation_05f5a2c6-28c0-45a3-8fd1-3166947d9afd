import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:culture_connect/services/ar_recording_service.dart';

/// Provider for the AR recording service
final arRecordingServiceProvider = Provider<ARRecordingService>((ref) {
  return ARRecordingService();
});

/// Provider for the recording state
final recordingStateProvider = StateProvider<bool>((ref) {
  final recordingService = ref.watch(arRecordingServiceProvider);
  return recordingService.isRecording;
});

/// Provider for the recording duration
final recordingDurationProvider = StreamProvider<int>((ref) {
  final recordingService = ref.watch(arRecordingServiceProvider);
  return Stream.periodic(
    const Duration(seconds: 1),
    (_) => recordingService.recordingDurationInSeconds,
  );
});

/// Provider for the recording screenshots
final recordingScreenshotsProvider = StateProvider<List<String>>((ref) {
  final recordingService = ref.watch(arRecordingServiceProvider);
  return recordingService.screenshots.map((file) => file.path).toList();
});

/// Provider for the recording video file
final recordingVideoFileProvider = StateProvider<String?>((ref) {
  final recordingService = ref.watch(arRecordingServiceProvider);
  return recordingService.videoFile?.path;
});
