import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:culture_connect/models/translation/voice_translation_model.dart';
import 'package:culture_connect/models/translation/language_model.dart';
import 'package:culture_connect/services/voice_translation/voice_translation_service.dart';
import 'package:culture_connect/providers/common/reader.dart';

/// Provider for the current voice translation
final currentVoiceTranslationProvider =
    StateProvider<VoiceTranslationModel?>((ref) => null);

/// Provider for the voice translation history
final voiceTranslationHistoryProvider =
    StreamProvider<List<VoiceTranslationModel>>((ref) {
  final service = ref.watch(voiceTranslationServiceProvider);
  return service.translationHistoryStream;
});

/// Provider for the selected source language
final sourceLanguageProvider = StateProvider<LanguageModel>((ref) {
  return supportedLanguages.firstWhere((lang) => lang.code == 'en');
});

/// Provider for the selected target language
final targetLanguageProvider = StateProvider<LanguageModel>((ref) {
  return supportedLanguages.firstWhere((lang) => lang.code == 'fr');
});

/// Provider for the recording state
final isRecordingProvider = StateProvider<bool>((ref) => false);

/// Provider for the playback state
final isPlayingProvider = StateProvider<bool>((ref) => false);

/// Provider for the amplitude stream
final amplitudeStreamProvider = StreamProvider<double>((ref) {
  final service = ref.watch(voiceTranslationServiceProvider);
  return service.recordingService.amplitudeStream;
});

/// Provider for the playback position stream
final playbackPositionStreamProvider = StreamProvider<Duration>((ref) {
  final service = ref.watch(voiceTranslationServiceProvider);
  return service.playbackService.positionStream;
});

/// Provider for the favorite translations
final favoriteTranslationsProvider =
    Provider<List<VoiceTranslationModel>>((ref) {
  final translations = ref.watch(voiceTranslationHistoryProvider).value ?? [];
  return translations.where((translation) => translation.isFavorite).toList();
});

/// Notifier for voice translation actions
class VoiceTranslationNotifier
    extends StateNotifier<AsyncValue<VoiceTranslationModel?>> {
  final VoiceTranslationService _service;
  final Reader _read;

  VoiceTranslationNotifier(this._service, this._read)
      : super(const AsyncValue.data(null));

  /// Start a new translation
  Future<void> startTranslation() async {
    state = const AsyncValue.loading();

    try {
      final sourceLanguage = _read(sourceLanguageProvider).code;
      final targetLanguage = _read(targetLanguageProvider).code;

      final translation = await _service.startTranslation(
        sourceLanguage: sourceLanguage,
        targetLanguage: targetLanguage,
      );

      state = AsyncValue.data(translation);
      _read(currentVoiceTranslationProvider.notifier).state = translation;
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }

  /// Start recording
  Future<void> startRecording() async {
    final translation = _read(currentVoiceTranslationProvider);
    if (translation == null) return;

    state = const AsyncValue.loading();

    try {
      final updatedTranslation = await _service.startRecording(translation.id);
      if (updatedTranslation != null) {
        state = AsyncValue.data(updatedTranslation);
        _read(currentVoiceTranslationProvider.notifier).state =
            updatedTranslation;
        _read(isRecordingProvider.notifier).state = true;
      } else {
        state =
            AsyncValue.error('Failed to start recording', StackTrace.current);
      }
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }

  /// Stop recording and translate
  Future<void> stopRecordingAndTranslate() async {
    final translation = _read(currentVoiceTranslationProvider);
    if (translation == null) return;

    state = const AsyncValue.loading();

    try {
      final updatedTranslation =
          await _service.stopRecordingAndTranslate(translation.id);
      if (updatedTranslation != null) {
        state = AsyncValue.data(updatedTranslation);
        _read(currentVoiceTranslationProvider.notifier).state =
            updatedTranslation;
        _read(isRecordingProvider.notifier).state = false;
      } else {
        state = AsyncValue.error(
            'Failed to stop recording and translate', StackTrace.current);
      }
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }

  /// Cancel recording
  Future<void> cancelRecording() async {
    final translation = _read(currentVoiceTranslationProvider);
    if (translation == null) return;

    state = const AsyncValue.loading();

    try {
      final updatedTranslation = await _service.cancelRecording(translation.id);
      if (updatedTranslation != null) {
        state = AsyncValue.data(updatedTranslation);
        _read(currentVoiceTranslationProvider.notifier).state =
            updatedTranslation;
        _read(isRecordingProvider.notifier).state = false;
      } else {
        state =
            AsyncValue.error('Failed to cancel recording', StackTrace.current);
      }
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }

  /// Play original audio
  Future<void> playOriginalAudio() async {
    final translation = _read(currentVoiceTranslationProvider);
    if (translation == null) return;

    try {
      await _service.playOriginalAudio(translation.id);
      _read(isPlayingProvider.notifier).state = true;
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }

  /// Play translated audio
  Future<void> playTranslatedAudio() async {
    final translation = _read(currentVoiceTranslationProvider);
    if (translation == null) return;

    try {
      await _service.playTranslatedAudio(translation.id);
      _read(isPlayingProvider.notifier).state = true;
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }

  /// Stop audio playback
  Future<void> stopAudio() async {
    try {
      await _service.stopAudio();
      _read(isPlayingProvider.notifier).state = false;
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }

  /// Toggle favorite status
  Future<void> toggleFavorite() async {
    final translation = _read(currentVoiceTranslationProvider);
    if (translation == null) return;

    try {
      final updatedTranslation = await _service.toggleFavorite(translation.id);
      if (updatedTranslation != null) {
        _read(currentVoiceTranslationProvider.notifier).state =
            updatedTranslation;
      }
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }

  /// Delete translation
  Future<void> deleteTranslation() async {
    final translation = _read(currentVoiceTranslationProvider);
    if (translation == null) return;

    try {
      final success = await _service.deleteTranslation(translation.id);
      if (success) {
        _read(currentVoiceTranslationProvider.notifier).state = null;
        state = const AsyncValue.data(null);
      }
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }

  /// Reset the state
  void reset() {
    state = const AsyncValue.data(null);
    _read(currentVoiceTranslationProvider.notifier).state = null;
  }
}

/// Provider for the voice translation notifier
final voiceTranslationNotifierProvider = StateNotifierProvider<
    VoiceTranslationNotifier, AsyncValue<VoiceTranslationModel?>>((ref) {
  final service = ref.watch(voiceTranslationServiceProvider);
  return VoiceTranslationNotifier(service, ref.read);
});
