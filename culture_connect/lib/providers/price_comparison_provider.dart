import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:culture_connect/models/travel/price_comparison_model.dart';
import 'package:culture_connect/models/travel/price_history_model.dart';
import 'package:culture_connect/services/price_comparison_service.dart';

/// Provider for the price comparison service
final priceComparisonServiceProvider = Provider<PriceComparisonService>((ref) {
  final service = PriceComparisonService();
  
  ref.onDispose(() {
    service.dispose();
  });
  
  return service;
});

/// Provider for comparing prices of a specific service
final priceComparisonProvider = FutureProvider.family<List<PricePoint>, MapEntry<String, String>>((ref, service) {
  return ref.watch(priceComparisonServiceProvider).comparePrices(
    service.key, // serviceType
    service.value, // serviceId
  );
});

/// Provider for getting price history of a specific service
final priceHistoryProvider = FutureProvider.family<PriceHistory, MapEntry<String, String>>((ref, service) {
  return ref.watch(priceComparisonServiceProvider).getPriceHistory(
    service.key, // serviceType
    service.value, // serviceId
  );
});

/// Provider for streaming price updates
final priceUpdatesProvider = StreamProvider<String>((ref) {
  return ref.watch(priceComparisonServiceProvider).priceUpdates;
});

/// Provider for checking if the device is online
final isPriceComparisonOnlineProvider = Provider<bool>((ref) {
  return ref.watch(priceComparisonServiceProvider).isOnline;
});

/// Provider for refreshing prices of a specific service
final refreshPricesProvider = FutureProvider.family<List<PricePoint>, MapEntry<String, String>>((ref, service) {
  return ref.watch(priceComparisonServiceProvider).refreshPrices(
    service.key, // serviceType
    service.value, // serviceId
  );
});

/// Provider for the best price of a specific service
final bestPriceProvider = FutureProvider.family<PricePoint?, MapEntry<String, String>>((ref, service) async {
  final prices = await ref.watch(priceComparisonProvider(service).future);
  if (prices.isEmpty) return null;
  
  // Sort by final price (lowest first)
  final sortedPrices = List<PricePoint>.from(prices)
    ..sort((a, b) => a.finalPrice.compareTo(b.finalPrice));
  
  return sortedPrices.first;
});

/// Provider for the price range of a specific service
final priceRangeProvider = FutureProvider.family<MapEntry<double, double>?, MapEntry<String, String>>((ref, service) async {
  final prices = await ref.watch(priceComparisonProvider(service).future);
  if (prices.isEmpty) return null;
  
  // Find min and max prices
  double minPrice = double.infinity;
  double maxPrice = 0;
  
  for (final price in prices) {
    if (price.finalPrice < minPrice) {
      minPrice = price.finalPrice;
    }
    if (price.finalPrice > maxPrice) {
      maxPrice = price.finalPrice;
    }
  }
  
  return MapEntry(minPrice, maxPrice);
});

/// Provider for the price trend of a specific service
final priceTrendProvider = FutureProvider.family<double?, MapEntry<String, String>>((ref, service) async {
  final history = await ref.watch(priceHistoryProvider(service).future);
  return history.priceTrend;
});

/// Provider for the price forecast of a specific service
final priceForecastProvider = FutureProvider.family<double?, MapEntry<String, String>>((ref, service) async {
  final history = await ref.watch(priceHistoryProvider(service).future);
  return history.priceForecast;
});
