import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'dart:io';

import 'package:culture_connect/models/experience_creation_model.dart';
import 'package:culture_connect/services/experience_creation_service.dart';

/// Provider for the ExperienceCreationService.
final experienceCreationServiceProvider = Provider<ExperienceCreationService>((ref) {
  return ExperienceCreationService();
});

/// Provider for experiences created by the current guide.
final guideExperiencesProvider = FutureProvider<List<ExperienceCreationModel>>((ref) async {
  final experienceCreationService = ref.watch(experienceCreationServiceProvider);
  return experienceCreationService.getGuideExperiences();
});

/// Provider for a specific experience by ID.
final experienceByIdProvider = FutureProvider.family<ExperienceCreationModel?, String>((ref, experienceId) async {
  final experienceCreationService = ref.watch(experienceCreationServiceProvider);
  return experienceCreationService.getExperienceById(experienceId);
});

/// Notifier for managing experience creation and updates.
class ExperienceCreationNotifier extends StateNotifier<AsyncValue<ExperienceCreationModel?>> {
  final ExperienceCreationService _experienceCreationService;

  ExperienceCreationNotifier(this._experienceCreationService) : super(const AsyncValue.loading());

  /// Create a new experience.
  Future<void> createExperience(ExperienceCreationModel experience) async {
    try {
      state = const AsyncValue.loading();
      final createdExperience = await _experienceCreationService.createExperience(experience);
      state = AsyncValue.data(createdExperience);
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }

  /// Update an existing experience.
  Future<void> updateExperience(ExperienceCreationModel experience) async {
    try {
      state = const AsyncValue.loading();
      final updatedExperience = await _experienceCreationService.updateExperience(experience);
      state = AsyncValue.data(updatedExperience);
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }

  /// Delete an experience.
  Future<void> deleteExperience(String experienceId) async {
    try {
      state = const AsyncValue.loading();
      await _experienceCreationService.deleteExperience(experienceId);
      state = const AsyncValue.data(null);
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }

  /// Upload an image for an experience.
  Future<String> uploadExperienceImage(File imageFile) async {
    return _experienceCreationService.uploadExperienceImage(imageFile);
  }

  /// Publish an experience (change from draft to published).
  Future<void> publishExperience(String experienceId) async {
    try {
      state = const AsyncValue.loading();
      final publishedExperience = await _experienceCreationService.publishExperience(experienceId);
      state = AsyncValue.data(publishedExperience);
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }

  /// Load an experience by ID.
  Future<void> loadExperience(String experienceId) async {
    try {
      state = const AsyncValue.loading();
      final experience = await _experienceCreationService.getExperienceById(experienceId);
      state = AsyncValue.data(experience);
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }

  /// Reset the state to null (for creating a new experience).
  void reset() {
    state = const AsyncValue.data(null);
  }
}

/// Provider for the ExperienceCreationNotifier.
final experienceCreationNotifierProvider = StateNotifierProvider<ExperienceCreationNotifier, AsyncValue<ExperienceCreationModel?>>((ref) {
  final experienceCreationService = ref.watch(experienceCreationServiceProvider);
  return ExperienceCreationNotifier(experienceCreationService);
});
