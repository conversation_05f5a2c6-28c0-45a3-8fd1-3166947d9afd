import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:culture_connect/services/ar_voice_command_service.dart';

/// Provider for the AR voice command service
final arVoiceCommandServiceProvider = Provider<ARVoiceCommandService>((ref) {
  return ARVoiceCommandService();
});

/// Provider for the voice command listening state
final voiceCommandListeningProvider = StateProvider<bool>((ref) {
  return false;
});

/// Provider for the last recognized voice command
final lastRecognizedCommandProvider = StateProvider<String>((ref) {
  return '';
});

/// Provider for the voice command confidence level
final voiceCommandConfidenceProvider = StateProvider<double>((ref) {
  return 0.0;
});

/// Provider for the voice command history
final voiceCommandHistoryProvider = StateProvider<List<String>>((ref) {
  return [];
});
