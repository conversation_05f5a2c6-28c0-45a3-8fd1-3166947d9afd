import 'package:flutter/material.dart';

/// App colors
class AppColors {
  /// Primary color
  static const Color primary = Color(0xFF1976D2);

  /// Secondary color
  static const Color secondary = Color(0xFF03DAC6);

  /// Background color
  static const Color background = Color(0xFFF5F5F5);

  /// Surface color
  static const Color surface = Colors.white;

  /// Error color
  static const Color error = Color(0xFFB00020);

  /// Success color
  static const Color success = Color(0xFF4CAF50);

  /// Warning color
  static const Color warning = Color(0xFFFFC107);

  /// Info color
  static const Color info = Color(0xFF2196F3);

  /// Text color
  static const Color text = Color(0xFF212121);

  /// Primary text color (alias for text)
  static const Color textPrimary = text;

  /// Secondary text color
  static const Color textSecondary = Color(0xFF757575);

  /// Disabled text color
  static const Color textDisabled = Color(0xFFBDBDBD);

  /// Divider color
  static const Color divider = Color(0xFFE0E0E0);

  /// Border color
  static const Color border = divider;

  /// Shadow color
  static const Color shadow = Color(0x40000000);

  /// Overlay color
  static const Color overlay = Color(0x80000000);

  /// Primary light color
  static const Color primaryLight = Color(0xFFBBDEFB);

  /// Primary dark color
  static const Color primaryDark = Color(0xFF0D47A1);

  /// Secondary light color
  static const Color secondaryLight = Color(0xFFB2EBF2);

  /// Secondary dark color
  static const Color secondaryDark = Color(0xFF00796B);

  /// Get a color scheme based on the app colors
  static ColorScheme get colorScheme {
    return const ColorScheme(
      primary: primary,
      primaryContainer: primaryLight,
      secondary: secondary,
      secondaryContainer: secondaryLight,
      surface: surface,
      error: error,
      onPrimary: Colors.white,
      onSecondary: Colors.black,
      onSurface: text,
      onError: Colors.white,
      brightness: Brightness.light,
    );
  }

  /// Get a dark color scheme based on the app colors
  static ColorScheme get darkColorScheme {
    return const ColorScheme(
      primary: primary,
      primaryContainer: primaryDark,
      secondary: secondary,
      secondaryContainer: secondaryDark,
      surface: Color(0xFF121212),
      error: error,
      onPrimary: Colors.white,
      onSecondary: Colors.black,
      onSurface: Colors.white,
      onError: Colors.white,
      brightness: Brightness.dark,
    );
  }
}
