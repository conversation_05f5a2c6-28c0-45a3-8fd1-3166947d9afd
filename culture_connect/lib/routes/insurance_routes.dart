import 'package:flutter/material.dart';
import 'package:culture_connect/screens/travel/insurance/insurance_home_screen.dart';
import 'package:culture_connect/screens/travel/insurance/insurance_policy_details_screen.dart';
import 'package:culture_connect/screens/travel/insurance/insurance_search_screen.dart';
import 'package:culture_connect/screens/travel/insurance/insurance_purchase_screen.dart';
import 'package:culture_connect/screens/travel/insurance/insurance_provider_details_screen.dart';
import 'package:culture_connect/screens/travel/insurance/insurance_claim_details_screen.dart';
import 'package:culture_connect/screens/travel/insurance/insurance_claim_form_screen.dart';
import 'package:culture_connect/screens/travel/insurance/insurance_claim_update_screen.dart';

/// Register all insurance-related routes
Map<String, WidgetBuilder> insuranceRoutes = {
  '/travel/insurance': (context) => const InsuranceHomeScreen(),
  '/travel/insurance/policy': (context) {
    final args = ModalRoute.of(context)!.settings.arguments as String;
    return InsurancePolicyDetailsScreen(policyId: args);
  },
  '/travel/insurance/search': (context) {
    final args =
        ModalRoute.of(context)!.settings.arguments as Map<String, dynamic>?;
    return InsuranceSearchScreen(initialFilters: args);
  },
  '/travel/insurance/purchase': (context) {
    final args = ModalRoute.of(context)!.settings.arguments as String;
    return InsurancePurchaseScreen(policyId: args);
  },
  '/travel/insurance/provider': (context) {
    final args = ModalRoute.of(context)!.settings.arguments as String;
    return InsuranceProviderDetailsScreen(providerId: args);
  },
  '/travel/insurance/claim': (context) {
    final args = ModalRoute.of(context)!.settings.arguments as String;
    return InsuranceClaimDetailsScreen(claimId: args);
  },
  '/travel/insurance/claim/new': (context) {
    final args =
        ModalRoute.of(context)!.settings.arguments as Map<String, dynamic>?;
    return InsuranceClaimFormScreen(policyId: args?['policyId']);
  },
  '/travel/insurance/claim/update': (context) {
    final args = ModalRoute.of(context)!.settings.arguments as String;
    return InsuranceClaimUpdateScreen(claimId: args);
  },
};
