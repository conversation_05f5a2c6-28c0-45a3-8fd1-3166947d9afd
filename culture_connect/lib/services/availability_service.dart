import 'package:uuid/uuid.dart';

import 'package:culture_connect/models/availability_model.dart';

/// Service for managing guide availability.
class AvailabilityService {
  final String _baseUrl = 'https://api.cultureconnect.com/v1';
  final Uuid _uuid = const Uuid();

  // Mock data for development
  AvailabilityModel? _mockAvailability;

  AvailabilityService() {
    _initializeMockData();
  }

  void _initializeMockData() {
    // Create a mock weekly schedule
    final Map<DayOfWeek, List<TimeSlot>> weeklySchedule = {
      DayOfWeek.monday: [
        TimeSlot(
          id: 'ts_mon_1',
          startTime: '09:00',
          endTime: '12:00',
        ),
        TimeSlot(
          id: 'ts_mon_2',
          startTime: '14:00',
          endTime: '17:00',
        ),
      ],
      DayOfWeek.tuesday: [
        TimeSlot(
          id: 'ts_tue_1',
          startTime: '09:00',
          endTime: '12:00',
        ),
        TimeSlot(
          id: 'ts_tue_2',
          startTime: '14:00',
          endTime: '17:00',
        ),
      ],
      DayOfWeek.wednesday: [
        TimeSlot(
          id: 'ts_wed_1',
          startTime: '09:00',
          endTime: '12:00',
        ),
        TimeSlot(
          id: 'ts_wed_2',
          startTime: '14:00',
          endTime: '17:00',
        ),
      ],
      DayOfWeek.thursday: [
        TimeSlot(
          id: 'ts_thu_1',
          startTime: '09:00',
          endTime: '12:00',
        ),
        TimeSlot(
          id: 'ts_thu_2',
          startTime: '14:00',
          endTime: '17:00',
        ),
      ],
      DayOfWeek.friday: [
        TimeSlot(
          id: 'ts_fri_1',
          startTime: '09:00',
          endTime: '12:00',
        ),
        TimeSlot(
          id: 'ts_fri_2',
          startTime: '14:00',
          endTime: '17:00',
        ),
      ],
      DayOfWeek.saturday: [
        TimeSlot(
          id: 'ts_sat_1',
          startTime: '10:00',
          endTime: '15:00',
        ),
      ],
      DayOfWeek.sunday: [],
    };

    // Create some date exceptions
    final List<DateException> dateExceptions = [
      DateException(
        id: 'de_001',
        date: DateTime.now().add(const Duration(days: 5)),
        isAvailable: false,
      ),
      DateException(
        id: 'de_002',
        date: DateTime.now().add(const Duration(days: 10)),
        isAvailable: true,
        timeSlots: [
          TimeSlot(
            id: 'ts_special_1',
            startTime: '18:00',
            endTime: '21:00',
          ),
        ],
      ),
    ];

    // Create a mock availability model
    _mockAvailability = AvailabilityModel(
      id: 'avail_001',
      guideId: 'guide_001',
      weeklySchedule: WeeklySchedule(schedule: weeklySchedule),
      dateExceptions: dateExceptions,
      advanceBookingDays: 90,
      maxBookingDays: 180,
      lastUpdated: DateTime.now(),
    );
  }

  /// Get the current guide's availability.
  Future<AvailabilityModel?> getCurrentGuideAvailability() async {
    try {
      // In a real app, we would make an API call here
      // final response = await http.get(
      //   Uri.parse('$_baseUrl/availability/current'),
      //   headers: {'Authorization': 'Bearer $token'},
      // );
      // if (response.statusCode == 200) {
      //   final data = json.decode(response.body);
      //   return AvailabilityModel.fromJson(data);
      // } else if (response.statusCode == 404) {
      //   return null;
      // } else {
      //   throw Exception('Failed to load availability');
      // }

      // For development, return mock data
      await Future.delayed(
          const Duration(milliseconds: 800)); // Simulate network delay
      return _mockAvailability;
    } catch (e) {
      throw Exception('Failed to load availability: $e');
    }
  }

  /// Get a guide's availability by guide ID.
  Future<AvailabilityModel?> getGuideAvailabilityByGuideId(
      String guideId) async {
    try {
      // In a real app, we would make an API call here
      // final response = await http.get(
      //   Uri.parse('$_baseUrl/availability/guide/$guideId'),
      //   headers: {'Authorization': 'Bearer $token'},
      // );
      // if (response.statusCode == 200) {
      //   final data = json.decode(response.body);
      //   return AvailabilityModel.fromJson(data);
      // } else if (response.statusCode == 404) {
      //   return null;
      // } else {
      //   throw Exception('Failed to load availability');
      // }

      // For development, return mock data if the guide ID matches
      await Future.delayed(
          const Duration(milliseconds: 800)); // Simulate network delay
      if (_mockAvailability != null && _mockAvailability!.guideId == guideId) {
        return _mockAvailability;
      }
      return null;
    } catch (e) {
      throw Exception('Failed to load availability: $e');
    }
  }

  /// Create a new availability schedule.
  Future<AvailabilityModel> createAvailability({
    required WeeklySchedule weeklySchedule,
    required List<DateException> dateExceptions,
    required int advanceBookingDays,
    required int maxBookingDays,
  }) async {
    try {
      // In a real app, we would make an API call here
      // final response = await http.post(
      //   Uri.parse('$_baseUrl/availability'),
      //   headers: {
      //     'Authorization': 'Bearer $token',
      //     'Content-Type': 'application/json',
      //   },
      //   body: json.encode({
      //     'weeklySchedule': weeklySchedule.toJson(),
      //     'dateExceptions': dateExceptions.map((e) => e.toJson()).toList(),
      //     'advanceBookingDays': advanceBookingDays,
      //     'maxBookingDays': maxBookingDays,
      //   }),
      // );
      // if (response.statusCode == 201) {
      //   final data = json.decode(response.body);
      //   return AvailabilityModel.fromJson(data);
      // } else {
      //   throw Exception('Failed to create availability');
      // }

      // For development, create a new mock availability
      await Future.delayed(
          const Duration(milliseconds: 800)); // Simulate network delay
      final availability = AvailabilityModel(
        id: _uuid.v4(),
        guideId: 'guide_001',
        weeklySchedule: weeklySchedule,
        dateExceptions: dateExceptions,
        advanceBookingDays: advanceBookingDays,
        maxBookingDays: maxBookingDays,
        lastUpdated: DateTime.now(),
      );
      _mockAvailability = availability;
      return availability;
    } catch (e) {
      throw Exception('Failed to create availability: $e');
    }
  }

  /// Update an existing availability schedule.
  Future<AvailabilityModel> updateAvailability({
    required String availabilityId,
    WeeklySchedule? weeklySchedule,
    List<DateException>? dateExceptions,
    int? advanceBookingDays,
    int? maxBookingDays,
  }) async {
    try {
      // In a real app, we would make an API call here
      // final response = await http.put(
      //   Uri.parse('$_baseUrl/availability/$availabilityId'),
      //   headers: {
      //     'Authorization': 'Bearer $token',
      //     'Content-Type': 'application/json',
      //   },
      //   body: json.encode({
      //     if (weeklySchedule != null) 'weeklySchedule': weeklySchedule.toJson(),
      //     if (dateExceptions != null) 'dateExceptions': dateExceptions.map((e) => e.toJson()).toList(),
      //     if (advanceBookingDays != null) 'advanceBookingDays': advanceBookingDays,
      //     if (maxBookingDays != null) 'maxBookingDays': maxBookingDays,
      //   }),
      // );
      // if (response.statusCode == 200) {
      //   final data = json.decode(response.body);
      //   return AvailabilityModel.fromJson(data);
      // } else {
      //   throw Exception('Failed to update availability');
      // }

      // For development, update the mock availability
      await Future.delayed(
          const Duration(milliseconds: 800)); // Simulate network delay
      if (_mockAvailability == null ||
          _mockAvailability!.id != availabilityId) {
        throw Exception('Availability not found');
      }

      final availability = _mockAvailability!.copyWith(
        weeklySchedule: weeklySchedule,
        dateExceptions: dateExceptions,
        advanceBookingDays: advanceBookingDays,
        maxBookingDays: maxBookingDays,
        lastUpdated: DateTime.now(),
      );
      _mockAvailability = availability;
      return availability;
    } catch (e) {
      throw Exception('Failed to update availability: $e');
    }
  }

  /// Add a time slot to a specific day in the weekly schedule.
  Future<AvailabilityModel> addTimeSlotToWeeklySchedule({
    required String availabilityId,
    required DayOfWeek day,
    required TimeSlot timeSlot,
  }) async {
    try {
      // In a real app, we would make an API call here
      // final response = await http.post(
      //   Uri.parse('$_baseUrl/availability/$availabilityId/weekly-schedule/$day'),
      //   headers: {
      //     'Authorization': 'Bearer $token',
      //     'Content-Type': 'application/json',
      //   },
      //   body: json.encode(timeSlot.toJson()),
      // );
      // if (response.statusCode == 200) {
      //   final data = json.decode(response.body);
      //   return AvailabilityModel.fromJson(data);
      // } else {
      //   throw Exception('Failed to add time slot');
      // }

      // For development, update the mock availability
      await Future.delayed(
          const Duration(milliseconds: 500)); // Simulate network delay
      if (_mockAvailability == null ||
          _mockAvailability!.id != availabilityId) {
        throw Exception('Availability not found');
      }

      final schedule = Map<DayOfWeek, List<TimeSlot>>.from(
          _mockAvailability!.weeklySchedule.schedule);
      if (schedule.containsKey(day)) {
        schedule[day] = [...schedule[day]!, timeSlot];
      } else {
        schedule[day] = [timeSlot];
      }

      final availability = _mockAvailability!.copyWith(
        weeklySchedule: WeeklySchedule(schedule: schedule),
        lastUpdated: DateTime.now(),
      );
      _mockAvailability = availability;
      return availability;
    } catch (e) {
      throw Exception('Failed to add time slot: $e');
    }
  }

  /// Remove a time slot from a specific day in the weekly schedule.
  Future<AvailabilityModel> removeTimeSlotFromWeeklySchedule({
    required String availabilityId,
    required DayOfWeek day,
    required String timeSlotId,
  }) async {
    try {
      // In a real app, we would make an API call here
      // final response = await http.delete(
      //   Uri.parse('$_baseUrl/availability/$availabilityId/weekly-schedule/$day/$timeSlotId'),
      //   headers: {'Authorization': 'Bearer $token'},
      // );
      // if (response.statusCode == 200) {
      //   final data = json.decode(response.body);
      //   return AvailabilityModel.fromJson(data);
      // } else {
      //   throw Exception('Failed to remove time slot');
      // }

      // For development, update the mock availability
      await Future.delayed(
          const Duration(milliseconds: 500)); // Simulate network delay
      if (_mockAvailability == null ||
          _mockAvailability!.id != availabilityId) {
        throw Exception('Availability not found');
      }

      final schedule = Map<DayOfWeek, List<TimeSlot>>.from(
          _mockAvailability!.weeklySchedule.schedule);
      if (schedule.containsKey(day)) {
        schedule[day] =
            schedule[day]!.where((slot) => slot.id != timeSlotId).toList();
      }

      final availability = _mockAvailability!.copyWith(
        weeklySchedule: WeeklySchedule(schedule: schedule),
        lastUpdated: DateTime.now(),
      );
      _mockAvailability = availability;
      return availability;
    } catch (e) {
      throw Exception('Failed to remove time slot: $e');
    }
  }

  /// Add a date exception.
  Future<AvailabilityModel> addDateException({
    required String availabilityId,
    required DateException dateException,
  }) async {
    try {
      // In a real app, we would make an API call here
      // final response = await http.post(
      //   Uri.parse('$_baseUrl/availability/$availabilityId/date-exceptions'),
      //   headers: {
      //     'Authorization': 'Bearer $token',
      //     'Content-Type': 'application/json',
      //   },
      //   body: json.encode(dateException.toJson()),
      // );
      // if (response.statusCode == 200) {
      //   final data = json.decode(response.body);
      //   return AvailabilityModel.fromJson(data);
      // } else {
      //   throw Exception('Failed to add date exception');
      // }

      // For development, update the mock availability
      await Future.delayed(
          const Duration(milliseconds: 500)); // Simulate network delay
      if (_mockAvailability == null ||
          _mockAvailability!.id != availabilityId) {
        throw Exception('Availability not found');
      }

      final dateExceptions = [
        ..._mockAvailability!.dateExceptions,
        dateException
      ];
      final availability = _mockAvailability!.copyWith(
        dateExceptions: dateExceptions,
        lastUpdated: DateTime.now(),
      );
      _mockAvailability = availability;
      return availability;
    } catch (e) {
      throw Exception('Failed to add date exception: $e');
    }
  }

  /// Remove a date exception.
  Future<AvailabilityModel> removeDateException({
    required String availabilityId,
    required String dateExceptionId,
  }) async {
    try {
      // In a real app, we would make an API call here
      // final response = await http.delete(
      //   Uri.parse('$_baseUrl/availability/$availabilityId/date-exceptions/$dateExceptionId'),
      //   headers: {'Authorization': 'Bearer $token'},
      // );
      // if (response.statusCode == 200) {
      //   final data = json.decode(response.body);
      //   return AvailabilityModel.fromJson(data);
      // } else {
      //   throw Exception('Failed to remove date exception');
      // }

      // For development, update the mock availability
      await Future.delayed(
          const Duration(milliseconds: 500)); // Simulate network delay
      if (_mockAvailability == null ||
          _mockAvailability!.id != availabilityId) {
        throw Exception('Availability not found');
      }

      final dateExceptions = _mockAvailability!.dateExceptions
          .where((exception) => exception.id != dateExceptionId)
          .toList();
      final availability = _mockAvailability!.copyWith(
        dateExceptions: dateExceptions,
        lastUpdated: DateTime.now(),
      );
      _mockAvailability = availability;
      return availability;
    } catch (e) {
      throw Exception('Failed to remove date exception: $e');
    }
  }
}
