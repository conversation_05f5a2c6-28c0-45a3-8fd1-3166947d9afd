import 'dart:async';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:path_provider/path_provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_app_check/firebase_app_check.dart';
import 'package:flutter_native_splash/flutter_native_splash.dart';
import 'package:connectivity_plus/connectivity_plus.dart';

/// A service that handles startup optimization for the app.
class StartupOptimizationService {
  static final StartupOptimizationService _instance =
      StartupOptimizationService._internal();
  factory StartupOptimizationService() => _instance;
  StartupOptimizationService._internal();

  /// Flag to track if the app has been initialized
  bool _isInitialized = false;

  /// Flag to track if assets are preloaded
  bool _assetsPreloaded = false;

  /// Flag to track if Firebase is initialized
  bool _firebaseInitialized = false;

  /// Shared preferences instance
  SharedPreferences? _preferences;

  /// Completer for tracking initialization
  final Completer<bool> _initializationCompleter = Completer<bool>();

  /// Get initialization status
  bool get isInitialized => _isInitialized;

  /// Get initialization future
  Future<bool> get initialized => _initializationCompleter.future;

  /// Initialize the app with optimized startup
  Future<bool> initializeApp() async {
    if (_isInitialized) return true;

    final stopwatch = Stopwatch()..start();
    debugPrint('🚀 Starting app initialization');

    try {
      // Keep the splash screen visible until initialization is complete
      FlutterNativeSplash.preserve(
          widgetsBinding: WidgetsFlutterBinding.ensureInitialized());

      // Run critical initializations in parallel
      await Future.wait([
        _initializePreferences(),
        _initializeFirebaseMinimal(),
        _preloadCriticalAssets(),
      ]);

      // Set system UI overlay style
      _configureSystemUI();

      // Mark as initialized
      _isInitialized = true;
      _initializationCompleter.complete(true);

      // Log initialization time
      stopwatch.stop();
      debugPrint(
          '✅ App initialization completed in ${stopwatch.elapsedMilliseconds}ms');

      // Remove the splash screen
      FlutterNativeSplash.remove();

      // Run non-critical initializations after app is visible
      _runDeferredInitializations();

      return true;
    } catch (e) {
      debugPrint('❌ Error during app initialization: $e');
      _initializationCompleter.completeError(e);

      // Remove the splash screen even if there's an error
      FlutterNativeSplash.remove();

      return false;
    }
  }

  /// Initialize shared preferences
  Future<void> _initializePreferences() async {
    try {
      final stopwatch = Stopwatch()..start();
      _preferences = await SharedPreferences.getInstance();
      stopwatch.stop();
      debugPrint(
          '✅ SharedPreferences initialized in ${stopwatch.elapsedMilliseconds}ms');
    } catch (e) {
      debugPrint('❌ Error initializing SharedPreferences: $e');
      rethrow;
    }
  }

  /// Initialize Firebase with minimal configuration
  Future<void> _initializeFirebaseMinimal() async {
    if (_firebaseInitialized) return;

    try {
      final stopwatch = Stopwatch()..start();

      // Initialize Firebase with minimal configuration
      if (Platform.isIOS) {
        // For iOS, use explicit options from GoogleService-Info.plist
        await Firebase.initializeApp(
          options: const FirebaseOptions(
            apiKey: "AIzaSyB6zMQinwp9M-Y5cGUMWQOZzva1mf5foJk",
            appId: "1:232391156294:ios:c885b3f758e48c52252047",
            messagingSenderId: "232391156294",
            projectId: "cultureconnect-c5dfe",
            storageBucket: "cultureconnect-c5dfe.firebasestorage.app",
          ),
        );
      } else {
        // For Android and other platforms, use the default initialization
        await Firebase.initializeApp();
      }

      _firebaseInitialized = true;
      stopwatch.stop();
      debugPrint(
          '✅ Firebase core initialized in ${stopwatch.elapsedMilliseconds}ms');
    } catch (e) {
      debugPrint('❌ Error initializing Firebase: $e');
      // Don't rethrow, allow app to continue without Firebase
    }
  }

  /// Preload critical assets
  Future<void> _preloadCriticalAssets() async {
    if (_assetsPreloaded) return;

    try {
      final stopwatch = Stopwatch()..start();

      // Preload critical images
      await _cacheAssets([
        'assets/images/splash.png',
        'assets/animations/splash_animation.json',
      ]);

      _assetsPreloaded = true;
      stopwatch.stop();
      debugPrint(
          '✅ Critical assets preloaded in ${stopwatch.elapsedMilliseconds}ms');
    } catch (e) {
      debugPrint('❌ Error preloading assets: $e');
      // Don't rethrow, allow app to continue without preloaded assets
    }
  }

  /// Cache assets for faster loading
  Future<void> _cacheAssets(List<String> assetPaths) async {
    for (final path in assetPaths) {
      try {
        // Load asset into memory
        final data = await rootBundle.load(path);
        debugPrint('✅ Preloaded asset: $path (${data.lengthInBytes} bytes)');
      } catch (e) {
        debugPrint('❌ Error preloading asset $path: $e');
      }
    }
  }

  /// Configure system UI
  void _configureSystemUI() {
    // Set preferred orientations
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
    ]);

    // Set system UI overlay style
    SystemChrome.setSystemUIOverlayStyle(
      const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.dark,
        systemNavigationBarColor: Colors.white,
        systemNavigationBarIconBrightness: Brightness.dark,
      ),
    );
  }

  /// Run non-critical initializations after app is visible
  Future<void> _runDeferredInitializations() async {
    // Run these initializations in the background after the app is visible
    unawaited(_initializeFirebaseFull());
    unawaited(_preloadNonCriticalAssets());
    unawaited(_cleanupCache());
  }

  /// Initialize full Firebase features
  Future<void> _initializeFirebaseFull() async {
    if (!_firebaseInitialized) return;

    try {
      final stopwatch = Stopwatch()..start();

      // Initialize Firebase App Check
      await FirebaseAppCheck.instance.activate(
        webProvider: ReCaptchaV3Provider('recaptcha-v3-site-key'),
        androidProvider: AndroidProvider.playIntegrity,
        appleProvider: AppleProvider.appAttest,
      );

      stopwatch.stop();
      debugPrint(
          '✅ Firebase full features initialized in ${stopwatch.elapsedMilliseconds}ms');
    } catch (e) {
      debugPrint('❌ Error initializing Firebase full features: $e');
    }
  }

  /// Preload non-critical assets
  Future<void> _preloadNonCriticalAssets() async {
    try {
      final stopwatch = Stopwatch()..start();

      // Preload additional assets
      await _cacheAssets([
        'assets/images/onboarding_1.png',
        'assets/images/onboarding_2.png',
        'assets/images/onboarding_3.png',
      ]);

      stopwatch.stop();
      debugPrint(
          '✅ Non-critical assets preloaded in ${stopwatch.elapsedMilliseconds}ms');
    } catch (e) {
      debugPrint('❌ Error preloading non-critical assets: $e');
    }
  }

  /// Clean up cache to free space
  Future<void> _cleanupCache() async {
    try {
      final cacheDir = await getTemporaryDirectory();
      final cacheSize = await _calculateDirectorySize(cacheDir);

      // If cache is larger than 100MB, clean it up
      if (cacheSize > 100 * 1024 * 1024) {
        await _deleteDirectoryContents(cacheDir);
        debugPrint(
            '✅ Cache cleaned up (freed ${(cacheSize / 1024 / 1024).toStringAsFixed(2)}MB)');
      }
    } catch (e) {
      debugPrint('❌ Error cleaning up cache: $e');
    }
  }

  /// Calculate directory size
  Future<int> _calculateDirectorySize(Directory directory) async {
    int totalSize = 0;
    try {
      final files = directory.listSync(recursive: true, followLinks: false);
      for (final file in files) {
        if (file is File) {
          totalSize += await file.length();
        }
      }
    } catch (e) {
      debugPrint('Error calculating directory size: $e');
    }
    return totalSize;
  }

  /// Delete directory contents
  Future<void> _deleteDirectoryContents(Directory directory) async {
    try {
      final files = directory.listSync(recursive: false, followLinks: false);
      for (final file in files) {
        if (file is File) {
          await file.delete();
        } else if (file is Directory) {
          await file.delete(recursive: true);
        }
      }
    } catch (e) {
      debugPrint('Error deleting directory contents: $e');
    }
  }

  /// Check network connectivity
  Future<bool> checkConnectivity() async {
    try {
      final connectivityResult = await Connectivity().checkConnectivity();
      return connectivityResult != ConnectivityResult.none;
    } catch (e) {
      debugPrint('Error checking connectivity: $e');
      return true; // Assume connected if check fails
    }
  }
}

/// Helper function to run a future without awaiting it
void unawaited(Future<void> future) {
  // Intentionally not awaiting the future
  future.catchError((error) {
    debugPrint('Unawaited future error: $error');
  });
}
