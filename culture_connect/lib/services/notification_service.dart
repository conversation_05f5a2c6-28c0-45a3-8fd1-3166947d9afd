import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:timezone/timezone.dart' as tz;
import 'package:timezone/data/latest.dart' as tz_data;
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:culture_connect/models/booking.dart';

/// Provider for the notification service
final notificationServiceProvider = Provider<NotificationService>((ref) {
  return NotificationService();
});

/// Service for managing local notifications
class NotificationService {
  static final NotificationService _instance = NotificationService._internal();

  factory NotificationService() => _instance;

  NotificationService._internal();

  final FlutterLocalNotificationsPlugin _flutterLocalNotificationsPlugin =
      FlutterLocalNotificationsPlugin();

  bool _isInitialized = false;

  /// Initialize the notification service
  Future<void> initialize() async {
    if (_isInitialized) return;

    // Initialize timezone data
    tz_data.initializeTimeZones();

    // Initialize notification settings
    const AndroidInitializationSettings initializationSettingsAndroid =
        AndroidInitializationSettings('@mipmap/ic_launcher');

    const DarwinInitializationSettings initializationSettingsIOS =
        DarwinInitializationSettings(
      requestAlertPermission: true,
      requestBadgePermission: true,
      requestSoundPermission: true,
    );

    const InitializationSettings initializationSettings =
        InitializationSettings(
      android: initializationSettingsAndroid,
      iOS: initializationSettingsIOS,
    );

    await _flutterLocalNotificationsPlugin.initialize(
      initializationSettings,
      onDidReceiveNotificationResponse: _onNotificationTapped,
    );

    _isInitialized = true;
  }

  /// Handle notification tap
  void _onNotificationTapped(NotificationResponse response) {
    // Handle notification tap based on payload
    // This would typically navigate to the booking details screen
    debugPrint('Notification tapped: ${response.payload}');
  }

  /// Request notification permissions
  Future<bool> requestPermissions() async {
    await initialize();

    // Request permissions on iOS
    final bool? result = await _flutterLocalNotificationsPlugin
        .resolvePlatformSpecificImplementation<
            IOSFlutterLocalNotificationsPlugin>()
        ?.requestPermissions(
          alert: true,
          badge: true,
          sound: true,
        );

    return result ?? false;
  }

  /// Schedule a booking reminder notification
  Future<void> scheduleBookingReminder({
    required Booking booking,
    required Duration beforeBooking,
    required String title,
    required String body,
  }) async {
    await initialize();

    // Calculate notification time
    final bookingDateTime = DateTime(
      booking.date.year,
      booking.date.month,
      booking.date.day,
      booking.timeSlot.startTime.hour,
      booking.timeSlot.startTime.minute,
    );

    final notificationTime = bookingDateTime.subtract(beforeBooking);

    // Don't schedule if the notification time is in the past
    if (notificationTime.isBefore(DateTime.now())) {
      debugPrint('Notification time is in the past, skipping');
      return;
    }

    // Create notification details
    const AndroidNotificationDetails androidDetails =
        AndroidNotificationDetails(
      'booking_reminders',
      'Booking Reminders',
      channelDescription: 'Notifications for upcoming bookings',
      importance: Importance.high,
      priority: Priority.high,
      showWhen: true,
    );

    const DarwinNotificationDetails iosDetails = DarwinNotificationDetails(
      presentAlert: true,
      presentBadge: true,
      presentSound: true,
    );

    const NotificationDetails notificationDetails = NotificationDetails(
      android: androidDetails,
      iOS: iosDetails,
    );

    // Generate a unique ID for the notification
    final int notificationId = booking.id.hashCode + beforeBooking.inMinutes;

    // Schedule the notification
    await _flutterLocalNotificationsPlugin.zonedSchedule(
      notificationId,
      title,
      body,
      tz.TZDateTime.from(notificationTime, tz.local),
      notificationDetails,
      androidScheduleMode: AndroidScheduleMode.exactAllowWhileIdle,
      uiLocalNotificationDateInterpretation:
          UILocalNotificationDateInterpretation.absoluteTime,
      payload: booking.id,
    );

    debugPrint(
        'Scheduled notification for ${notificationTime.toIso8601String()}');
  }

  /// Set up all reminders for a booking (24h, 1h)
  Future<void> setupBookingReminders(Booking booking) async {
    await initialize();

    // 24 hours before
    await scheduleBookingReminder(
      booking: booking,
      beforeBooking: const Duration(hours: 24),
      title: 'Upcoming Experience Tomorrow',
      body:
          'Your "${booking.experienceId}" experience is scheduled for tomorrow at ${_formatTime(booking.timeSlot.startTime)}.',
    );

    // 1 hour before
    await scheduleBookingReminder(
      booking: booking,
      beforeBooking: const Duration(hours: 1),
      title: 'Experience Starting Soon',
      body:
          'Your "${booking.experienceId}" experience starts in 1 hour at ${_formatTime(booking.timeSlot.startTime)}.',
    );
  }

  /// Cancel all reminders for a booking
  Future<void> cancelBookingReminders(Booking booking) async {
    await initialize();

    // Calculate notification IDs
    final int dayBeforeId =
        booking.id.hashCode + const Duration(hours: 24).inMinutes;
    final int hourBeforeId =
        booking.id.hashCode + const Duration(hours: 1).inMinutes;

    // Cancel the notifications
    await _flutterLocalNotificationsPlugin.cancel(dayBeforeId);
    await _flutterLocalNotificationsPlugin.cancel(hourBeforeId);

    debugPrint('Cancelled reminders for booking ${booking.id}');
  }

  /// Format time for display in notifications
  String _formatTime(DateTime time) {
    final hour = time.hour > 12 ? time.hour - 12 : time.hour;
    final minute = time.minute.toString().padLeft(2, '0');
    final period = time.hour >= 12 ? 'PM' : 'AM';
    return '$hour:$minute $period';
  }

  /// Show a notification immediately
  Future<void> showNotification({
    required String title,
    required String body,
    Map<String, dynamic>? payload,
    String? imageUrl,
  }) async {
    await initialize();

    // Create notification details
    const AndroidNotificationDetails androidDetails =
        AndroidNotificationDetails(
      'general_notifications',
      'General Notifications',
      channelDescription: 'General notifications from CultureConnect',
      importance: Importance.high,
      priority: Priority.high,
      showWhen: true,
    );

    const DarwinNotificationDetails iosDetails = DarwinNotificationDetails(
      presentAlert: true,
      presentBadge: true,
      presentSound: true,
    );

    const NotificationDetails notificationDetails = NotificationDetails(
      android: androidDetails,
      iOS: iosDetails,
    );

    // Generate a unique ID for the notification
    final int notificationId = DateTime.now().millisecondsSinceEpoch ~/ 1000;

    // Convert payload to string if provided
    final String? payloadStr =
        payload?.entries.map((e) => '${e.key}=${e.value}').join('&');

    // Show the notification
    await _flutterLocalNotificationsPlugin.show(
      notificationId,
      title,
      body,
      notificationDetails,
      payload: payloadStr,
    );

    debugPrint('Showed notification: $title');
  }
}
