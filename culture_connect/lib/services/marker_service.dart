import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:culture_connect/models/experience.dart';

class MarkerService {
  static final Map<String, BitmapDescriptor> _markerIcons = {};

  static Future<BitmapDescriptor> getMarkerIcon(String category) async {
    if (_markerIcons.containsKey(category)) {
      return _markerIcons[category]!;
    }

    // Default icon color based on category
    final Color iconColor = _getCategoryColor(category);

    // Create a custom marker icon
    BitmapDescriptor icon;
    try {
      icon = await BitmapDescriptor.asset(
        const ImageConfiguration(size: Size(48, 48)),
        'assets/icons/marker_${category.toLowerCase().replaceAll(' ', '_')}.png',
      );
    } catch (_) {
      // Fallback to a default marker with category color
      icon = BitmapDescriptor.defaultMarkerWithHue(
        _getMarkerHue(iconColor),
      );
    }

    _markerIcons[category] = icon;
    return icon;
  }

  static Color _getCategoryColor(String category) {
    switch (category.toLowerCase()) {
      case 'cultural tours':
        return Colors.blue;
      case 'cooking classes':
        return Colors.orange;
      case 'art & craft':
        return Colors.purple;
      case 'music & dance':
        return Colors.red;
      case 'language exchange':
        return Colors.green;
      case 'local events':
        return Colors.pink;
      default:
        return Colors.grey;
    }
  }

  static double _getMarkerHue(Color color) {
    // Convert RGB to HSV and return the hue value
    final HSVColor hsv = HSVColor.fromColor(color);
    return hsv.hue;
  }

  static Future<Marker> createMarker(
    Experience experience, {
    VoidCallback? onTap,
  }) async {
    final BitmapDescriptor icon = await getMarkerIcon(experience.category);

    return Marker(
      markerId: MarkerId(experience.id),
      position: experience.coordinates,
      icon: icon,
      onTap: onTap,
      infoWindow: InfoWindow(
        title: experience.title,
        snippet: experience.location,
      ),
    );
  }
}
