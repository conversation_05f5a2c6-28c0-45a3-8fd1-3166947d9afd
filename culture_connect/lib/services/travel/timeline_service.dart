import 'dart:convert';

import 'package:shared_preferences/shared_preferences.dart';

import 'package:culture_connect/models/travel/timeline.dart';
import 'package:culture_connect/models/travel/timeline_event.dart';
import 'package:culture_connect/models/travel/itinerary.dart';
import 'package:culture_connect/services/logging_service.dart';
import 'package:culture_connect/services/error_handling_service.dart';

/// Service for managing timelines
class TimelineService {
  /// The shared preferences instance
  final SharedPreferences _preferences;

  /// The logging service
  final LoggingService _loggingService;

  /// The error handling service (optional)
  final ErrorHandlingService? _errorHandlingService;

  /// Creates a new timeline service
  ///
  /// The [errorHandlingService] is optional and can be null
  TimelineService(
    this._preferences,
    this._loggingService,
    this._errorHandlingService,
  );

  /// Get all timelines for a user
  Future<List<Timeline>> getTimelines(String userId) async {
    try {
      final timelineList =
          _preferences.getStringList('timelines_$userId') ?? [];

      final timelines = <Timeline>[];
      for (final timelineJson in timelineList) {
        try {
          final timeline = Timeline.fromJson(jsonDecode(timelineJson));
          timelines.add(timeline);
        } catch (e) {
          _loggingService.error(
              'TimelineService', 'Failed to parse timeline', e);
        }
      }

      // Sort by updated at (newest first)
      timelines.sort((a, b) => b.updatedAt.compareTo(a.updatedAt));

      return timelines;
    } catch (e, stackTrace) {
      await _handleError(
        error: e,
        context: 'Failed to get timelines',
        stackTrace: stackTrace,
      );
      return [];
    }
  }

  /// Get a timeline by ID
  Future<Timeline?> getTimeline(String id) async {
    try {
      final timelineJson = _preferences.getString('timeline_$id');
      if (timelineJson == null) return null;

      return Timeline.fromJson(jsonDecode(timelineJson));
    } catch (e, stackTrace) {
      await _handleError(
        error: e,
        context: 'Failed to get timeline',
        stackTrace: stackTrace,
      );
      return null;
    }
  }

  /// Create a new timeline
  Future<Timeline> createTimeline(Timeline timeline) async {
    try {
      // Save the timeline
      await _saveTimeline(timeline);

      // Add to the user's timeline list
      final timelineList =
          _preferences.getStringList('timelines_${timeline.userId}') ?? [];
      timelineList.add(jsonEncode(timeline.toJson()));
      await _preferences.setStringList(
          'timelines_${timeline.userId}', timelineList);

      return timeline;
    } catch (e, stackTrace) {
      await _handleError(
        error: e,
        context: 'Failed to create timeline',
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  /// Update a timeline
  Future<Timeline> updateTimeline(Timeline timeline) async {
    try {
      // Get the existing timeline
      final existingTimeline = await getTimeline(timeline.id);
      if (existingTimeline == null) {
        throw Exception('Timeline not found');
      }

      // Update the timeline
      final updatedTimeline = timeline.copyWith(
        updatedAt: DateTime.now(),
      );

      // Save the timeline
      await _saveTimeline(updatedTimeline);

      // Update the user's timeline list
      final timelineList =
          _preferences.getStringList('timelines_${timeline.userId}') ?? [];
      final index = timelineList.indexWhere((t) {
        try {
          final decoded = jsonDecode(t);
          return decoded['id'] == timeline.id;
        } catch (_) {
          return false;
        }
      });

      if (index != -1) {
        timelineList[index] = jsonEncode(updatedTimeline.toJson());
        await _preferences.setStringList(
            'timelines_${timeline.userId}', timelineList);
      }

      return updatedTimeline;
    } catch (e, stackTrace) {
      _loggingService.error(
          'TimelineService', 'Failed to update timeline', e, stackTrace);
      if (_errorHandlingService != null) {
        await _errorHandlingService!.handleError(
          error: e,
          context: 'TimelineService.updateTimeline',
          stackTrace: stackTrace,
        );
      }
      rethrow;
    }
  }

  /// Delete a timeline
  Future<bool> deleteTimeline(String id, String userId) async {
    try {
      // Remove the timeline
      await _preferences.remove('timeline_$id');

      // Remove from the user's timeline list
      final timelineList =
          _preferences.getStringList('timelines_$userId') ?? [];
      final filteredList = timelineList.where((t) {
        try {
          final decoded = jsonDecode(t);
          return decoded['id'] != id;
        } catch (_) {
          return true;
        }
      }).toList();

      await _preferences.setStringList('timelines_$userId', filteredList);

      return true;
    } catch (e, stackTrace) {
      _loggingService.error(
          'TimelineService', 'Failed to delete timeline', e, stackTrace);
      if (_errorHandlingService != null) {
        await _errorHandlingService!.handleError(
          error: e,
          context: 'TimelineService.deleteTimeline',
          stackTrace: stackTrace,
        );
      }
      return false;
    }
  }

  /// Add an event to a timeline
  Future<Timeline> addEventToTimeline(
      String timelineId, TimelineEvent event) async {
    try {
      // Get the timeline
      final timeline = await getTimeline(timelineId);
      if (timeline == null) {
        throw Exception('Timeline not found');
      }

      // Add the event
      final updatedTimeline = timeline.addEvent(event);

      // Save the timeline
      await _saveTimeline(updatedTimeline);

      return updatedTimeline;
    } catch (e, stackTrace) {
      _loggingService.error(
          'TimelineService', 'Failed to add event to timeline', e, stackTrace);
      if (_errorHandlingService != null) {
        await _errorHandlingService!.handleError(
          error: e,
          context: 'TimelineService.addEventToTimeline',
          stackTrace: stackTrace,
        );
      }
      rethrow;
    }
  }

  /// Update an event in a timeline
  Future<Timeline> updateEventInTimeline(
      String timelineId, TimelineEvent event) async {
    try {
      // Get the timeline
      final timeline = await getTimeline(timelineId);
      if (timeline == null) {
        throw Exception('Timeline not found');
      }

      // Update the event
      final updatedTimeline = timeline.updateEvent(event);

      // Save the timeline
      await _saveTimeline(updatedTimeline);

      return updatedTimeline;
    } catch (e, stackTrace) {
      _loggingService.error('TimelineService',
          'Failed to update event in timeline', e, stackTrace);
      if (_errorHandlingService != null) {
        await _errorHandlingService!.handleError(
          error: e,
          context: 'TimelineService.updateEventInTimeline',
          stackTrace: stackTrace,
        );
      }
      rethrow;
    }
  }

  /// Remove an event from a timeline
  Future<Timeline> removeEventFromTimeline(
      String timelineId, String eventId) async {
    try {
      // Get the timeline
      final timeline = await getTimeline(timelineId);
      if (timeline == null) {
        throw Exception('Timeline not found');
      }

      // Remove the event
      final updatedTimeline = timeline.removeEvent(eventId);

      // Save the timeline
      await _saveTimeline(updatedTimeline);

      return updatedTimeline;
    } catch (e, stackTrace) {
      _loggingService.error('TimelineService',
          'Failed to remove event from timeline', e, stackTrace);
      if (_errorHandlingService != null) {
        await _errorHandlingService!.handleError(
          error: e,
          context: 'TimelineService.removeEventFromTimeline',
          stackTrace: stackTrace,
        );
      }
      rethrow;
    }
  }

  /// Create a timeline from an itinerary
  Future<Timeline> createTimelineFromItinerary(Itinerary itinerary) async {
    try {
      // Create the timeline
      final timeline = Timeline.fromItinerary(itinerary);

      // Save the timeline
      await _saveTimeline(timeline);

      // Add to the user's timeline list
      final timelineList =
          _preferences.getStringList('timelines_${timeline.userId}') ?? [];
      timelineList.add(jsonEncode(timeline.toJson()));
      await _preferences.setStringList(
          'timelines_${timeline.userId}', timelineList);

      return timeline;
    } catch (e, stackTrace) {
      _loggingService.error('TimelineService',
          'Failed to create timeline from itinerary', e, stackTrace);
      if (_errorHandlingService != null) {
        await _errorHandlingService!.handleError(
          error: e,
          context: 'TimelineService.createTimelineFromItinerary',
          stackTrace: stackTrace,
        );
      }
      rethrow;
    }
  }

  /// Get timelines for an itinerary
  Future<List<Timeline>> getTimelinesForItinerary(String itineraryId) async {
    try {
      // Get all timelines
      final allTimelines = await getTimelines('all');

      // Filter by itinerary ID
      return allTimelines.where((t) => t.itineraryId == itineraryId).toList();
    } catch (e, stackTrace) {
      _loggingService.error('TimelineService',
          'Failed to get timelines for itinerary', e, stackTrace);
      if (_errorHandlingService != null) {
        await _errorHandlingService!.handleError(
          error: e,
          context: 'TimelineService.getTimelinesForItinerary',
          stackTrace: stackTrace,
        );
      }
      return [];
    }
  }

  /// Save a timeline
  Future<void> _saveTimeline(Timeline timeline) async {
    await _preferences.setString(
        'timeline_${timeline.id}', jsonEncode(timeline.toJson()));
  }

  /// Helper method to handle errors consistently
  Future<void> _handleError({
    required Object error,
    required String context,
    required StackTrace stackTrace,
  }) async {
    _loggingService.error('TimelineService', context, error, stackTrace);

    // Only call error handling service if it's available
    _errorHandlingService?.handleError(
      error: error,
      context: context,
      stackTrace: stackTrace,
    );
  }
}
