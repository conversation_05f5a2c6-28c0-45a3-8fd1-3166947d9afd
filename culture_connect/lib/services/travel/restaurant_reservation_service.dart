import 'package:flutter/material.dart';
import 'package:culture_connect/models/travel/restaurant_reservation.dart';

/// Service for managing restaurant reservations
class RestaurantReservationService {
  // In-memory storage for reservations (would be replaced with Firebase in production)
  final List<RestaurantReservation> _reservations = [];

  // In-memory storage for time slots (would be generated from restaurant opening hours in production)
  final Map<String, Map<String, List<RestaurantTimeSlot>>> _timeSlots = {};

  /// Get all reservations for a user
  Future<List<RestaurantReservation>> getUserReservations(String userId) async {
    // Simulate network delay
    await Future.delayed(const Duration(milliseconds: 500));

    return _reservations
        .where((reservation) => reservation.userId == userId)
        .toList();
  }

  /// Get upcoming reservations for a user
  Future<List<RestaurantReservation>> getUpcomingReservations(
      String userId) async {
    // Simulate network delay
    await Future.delayed(const Duration(milliseconds: 500));

    final now = DateTime.now();

    return _reservations
        .where((reservation) =>
            reservation.userId == userId &&
            reservation.date.isAfter(now) &&
            reservation.status != ReservationStatus.cancelled)
        .toList();
  }

  /// Get past reservations for a user
  Future<List<RestaurantReservation>> getPastReservations(String userId) async {
    // Simulate network delay
    await Future.delayed(const Duration(milliseconds: 500));

    final now = DateTime.now();

    return _reservations
        .where((reservation) =>
            reservation.userId == userId &&
            (reservation.date.isBefore(now) ||
                reservation.status == ReservationStatus.completed ||
                reservation.status == ReservationStatus.noShow))
        .toList();
  }

  /// Get a reservation by ID
  Future<RestaurantReservation?> getReservationById(
      String reservationId) async {
    // Simulate network delay
    await Future.delayed(const Duration(milliseconds: 300));

    try {
      return _reservations
          .firstWhere((reservation) => reservation.id == reservationId);
    } catch (e) {
      return null;
    }
  }

  /// Get all reservations for a restaurant
  Future<List<RestaurantReservation>> getReservationsForRestaurant(
      String restaurantId) async {
    // Simulate network delay
    await Future.delayed(const Duration(milliseconds: 500));

    return _reservations
        .where((reservation) => reservation.restaurantId == restaurantId)
        .toList();
  }

  /// Create a new reservation
  Future<RestaurantReservation> createReservation(
      RestaurantReservation reservation) async {
    // Simulate network delay
    await Future.delayed(const Duration(milliseconds: 800));

    // Check if the time slot is available
    final availableSlots =
        await getAvailableTimeSlots(reservation.restaurantId, reservation.date);
    final isSlotAvailable = availableSlots.any((slot) =>
        slot.startTime.hour == reservation.timeSlot.startTime.hour &&
        slot.startTime.minute == reservation.timeSlot.startTime.minute);

    if (!isSlotAvailable) {
      throw Exception('This time slot is no longer available');
    }

    // Add to in-memory storage
    _reservations.add(reservation);

    // Update time slot availability
    _updateTimeSlotAvailability(
        reservation.restaurantId, reservation.date, reservation.timeSlot.id);

    return reservation;
  }

  /// Update an existing reservation
  Future<RestaurantReservation> updateReservation(
      RestaurantReservation updatedReservation) async {
    // Simulate network delay
    await Future.delayed(const Duration(milliseconds: 800));

    // Find the existing reservation
    final index =
        _reservations.indexWhere((r) => r.id == updatedReservation.id);
    if (index == -1) {
      throw Exception('Reservation not found');
    }

    // If the date or time slot has changed, check availability
    final existingReservation = _reservations[index];
    if (existingReservation.date != updatedReservation.date ||
        existingReservation.timeSlot.id != updatedReservation.timeSlot.id) {
      // Check if the new time slot is available
      final availableSlots = await getAvailableTimeSlots(
          updatedReservation.restaurantId, updatedReservation.date);
      final isSlotAvailable = availableSlots.any((slot) =>
          slot.startTime.hour == updatedReservation.timeSlot.startTime.hour &&
          slot.startTime.minute ==
              updatedReservation.timeSlot.startTime.minute);

      if (!isSlotAvailable) {
        throw Exception('This time slot is no longer available');
      }

      // Free up the old time slot
      _updateTimeSlotAvailability(existingReservation.restaurantId,
          existingReservation.date, existingReservation.timeSlot.id,
          decrement: true);

      // Update the new time slot
      _updateTimeSlotAvailability(updatedReservation.restaurantId,
          updatedReservation.date, updatedReservation.timeSlot.id);
    }

    // Update the reservation
    _reservations[index] = updatedReservation;

    return updatedReservation;
  }

  /// Delete a reservation
  Future<void> deleteReservation(String reservationId) async {
    // Simulate network delay
    await Future.delayed(const Duration(milliseconds: 500));

    // Find the reservation
    final index = _reservations.indexWhere((r) => r.id == reservationId);
    if (index == -1) {
      throw Exception('Reservation not found');
    }

    // Free up the time slot
    final reservation = _reservations[index];
    _updateTimeSlotAvailability(
        reservation.restaurantId, reservation.date, reservation.timeSlot.id,
        decrement: true);

    // Remove the reservation
    _reservations.removeAt(index);
  }

  /// Get available time slots for a restaurant on a specific date
  Future<List<RestaurantTimeSlot>> getAvailableTimeSlots(
      String restaurantId, DateTime date) async {
    // Simulate network delay
    await Future.delayed(const Duration(milliseconds: 500));

    // Format date as YYYY-MM-DD for map key
    final dateKey =
        '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';

    // Check if we already have time slots for this restaurant and date
    if (_timeSlots.containsKey(restaurantId) &&
        _timeSlots[restaurantId]!.containsKey(dateKey)) {
      return _timeSlots[restaurantId]![dateKey]!
          .where((slot) => slot.isAvailable)
          .toList();
    }

    // Generate time slots for this restaurant and date
    final slots = _generateTimeSlots(restaurantId, date);

    // Store the slots
    if (!_timeSlots.containsKey(restaurantId)) {
      _timeSlots[restaurantId] = {};
    }
    _timeSlots[restaurantId]![dateKey] = slots;

    return slots.where((slot) => slot.isAvailable).toList();
  }

  /// Generate time slots for a restaurant based on opening hours
  List<RestaurantTimeSlot> _generateTimeSlots(
      String restaurantId, DateTime date) {
    // In a real app, we would get the restaurant's opening hours for the specific day
    // For this mock implementation, we'll generate slots from 11 AM to 10 PM with 30-minute intervals

    final slots = <RestaurantTimeSlot>[];

    // Start at 11 AM
    int hour = 11;
    int minute = 0;

    // Generate slots until 10 PM (22:00)
    while (hour < 22 || (hour == 22 && minute == 0)) {
      // Create a time slot
      final startTime = TimeOfDay(hour: hour, minute: minute);

      // Calculate end time (1.5 hours later)
      int endHour = hour;
      int endMinute = minute + 90; // 1.5 hours in minutes

      // Adjust for overflow
      if (endMinute >= 60) {
        endHour += endMinute ~/ 60;
        endMinute = endMinute % 60;
      }

      final endTime = TimeOfDay(hour: endHour, minute: endMinute);

      // Create the slot with random availability
      final maxReservations =
          4 + (DateTime.now().millisecondsSinceEpoch % 4); // Random between 4-7
      final currentReservations =
          DateTime.now().millisecondsSinceEpoch % 3; // Random between 0-2

      slots.add(RestaurantTimeSlot(
        startTime: startTime,
        endTime: endTime,
        maxReservations: maxReservations,
        currentReservations: currentReservations.toInt(),
      ));

      // Move to next slot (30 minutes later)
      minute += 30;
      if (minute >= 60) {
        hour += 1;
        minute = 0;
      }
    }

    return slots;
  }

  /// Update time slot availability
  void _updateTimeSlotAvailability(
      String restaurantId, DateTime date, String timeSlotId,
      {bool decrement = false}) {
    // Format date as YYYY-MM-DD for map key
    final dateKey =
        '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';

    // Check if we have time slots for this restaurant and date
    if (_timeSlots.containsKey(restaurantId) &&
        _timeSlots[restaurantId]!.containsKey(dateKey)) {
      // Find the time slot
      final slotIndex = _timeSlots[restaurantId]![dateKey]!
          .indexWhere((slot) => slot.id == timeSlotId);
      if (slotIndex != -1) {
        // Update the current reservations count
        final slot = _timeSlots[restaurantId]![dateKey]![slotIndex];
        final newCount = decrement
            ? (slot.currentReservations - 1).clamp(0, slot.maxReservations)
            : (slot.currentReservations + 1).clamp(0, slot.maxReservations);

        _timeSlots[restaurantId]![dateKey]![slotIndex] = slot.copyWith(
          currentReservations: newCount,
        );
      }
    }
  }

  /// Initialize with some mock data
  Future<void> initializeMockData() async {
    // Clear existing data
    _reservations.clear();
    _timeSlots.clear();

    // Generate some mock reservations
    final mockReservations = [
      RestaurantReservation(
        id: 'res1',
        restaurantId: 'restaurant1',
        restaurantName: 'The Fancy Bistro',
        date: DateTime.now().add(const Duration(days: 2)),
        timeSlot: RestaurantTimeSlot(
          id: 'slot1',
          startTime: const TimeOfDay(hour: 19, minute: 0),
          endTime: const TimeOfDay(hour: 20, minute: 30),
          maxReservations: 5,
          currentReservations: 3,
        ),
        partySize: 2,
        specialRequests: 'Window seat if possible',
        status: ReservationStatus.confirmed,
        userId: 'user1',
        userName: 'John Doe',
        contactPhone: '+1234567890',
        contactEmail: '<EMAIL>',
        isConfirmedByRestaurant: true,
        createdAt: DateTime.now().subtract(const Duration(days: 5)),
        updatedAt: DateTime.now().subtract(const Duration(days: 5)),
      ),
      RestaurantReservation(
        id: 'res2',
        restaurantId: 'restaurant2',
        restaurantName: 'Sushi Paradise',
        date: DateTime.now().add(const Duration(days: 5)),
        timeSlot: RestaurantTimeSlot(
          id: 'slot2',
          startTime: const TimeOfDay(hour: 18, minute: 30),
          endTime: const TimeOfDay(hour: 20, minute: 0),
          maxReservations: 6,
          currentReservations: 2,
        ),
        partySize: 4,
        specialRequests: 'Celebrating a birthday',
        status: ReservationStatus.pending,
        userId: 'user1',
        userName: 'John Doe',
        contactPhone: '+1234567890',
        contactEmail: '<EMAIL>',
        createdAt: DateTime.now().subtract(const Duration(days: 2)),
        updatedAt: DateTime.now().subtract(const Duration(days: 2)),
      ),
    ];

    _reservations.addAll(mockReservations);
  }
}
