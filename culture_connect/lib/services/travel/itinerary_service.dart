import 'dart:async';
import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:uuid/uuid.dart';

import 'package:culture_connect/models/travel/ai_recommendation.dart';
import 'package:culture_connect/models/travel/itinerary.dart';
import 'package:culture_connect/models/travel/itinerary_item.dart'
    as item_model;
import 'package:culture_connect/services/logging_service.dart';

/// Service for managing itineraries
class ItineraryService {
  /// The logging service
  final LoggingService _loggingService;

  /// The shared preferences instance
  final SharedPreferences _sharedPreferences;

  /// The itineraries
  final List<Itinerary> _itineraries = [];

  /// Whether the service has been initialized
  bool _isInitialized = false;

  /// Creates a new itinerary service
  ItineraryService(this._loggingService, this._sharedPreferences);

  /// Initialize the service
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      await _loadItineraries();
      _isInitialized = true;
    } catch (e, stackTrace) {
      _loggingService.error(
          'ItineraryService', 'Failed to initialize', e, stackTrace);
      rethrow;
    }
  }

  /// Load itineraries from shared preferences
  Future<void> _loadItineraries() async {
    try {
      final itinerariesJson =
          _sharedPreferences.getStringList('itineraries') ?? [];

      _itineraries.clear();
      for (final json in itinerariesJson) {
        try {
          final itinerary = Itinerary.fromJson(jsonDecode(json));
          _itineraries.add(itinerary);
        } catch (e, stackTrace) {
          _loggingService.error(
              'ItineraryService', 'Failed to parse itinerary', e, stackTrace);
        }
      }

      _loggingService.debug(
          'ItineraryService', 'Loaded ${_itineraries.length} itineraries');
    } catch (e, stackTrace) {
      _loggingService.error(
          'ItineraryService', 'Failed to load itineraries', e, stackTrace);
      rethrow;
    }
  }

  /// Save itineraries to shared preferences
  Future<void> _saveItineraries() async {
    try {
      final itinerariesJson =
          _itineraries.map((i) => jsonEncode(i.toJson())).toList();
      await _sharedPreferences.setStringList('itineraries', itinerariesJson);

      _loggingService.debug(
          'ItineraryService', 'Saved ${_itineraries.length} itineraries');
    } catch (e, stackTrace) {
      _loggingService.error(
          'ItineraryService', 'Failed to save itineraries', e, stackTrace);
      rethrow;
    }
  }

  /// Get all itineraries for a user
  Future<List<Itinerary>> getItineraries(String userId) async {
    await initialize();
    return _itineraries.where((i) => i.userId == userId).toList();
  }

  /// Get an itinerary by ID
  Future<Itinerary?> getItinerary(String id) async {
    await initialize();
    try {
      return _itineraries.firstWhere((i) => i.id == id);
    } catch (e, stackTrace) {
      _loggingService.error(
        'ItineraryService',
        'Itinerary not found with ID: $id',
        e,
        stackTrace,
      );
      return null;
    }
  }

  /// Create a new itinerary
  Future<Itinerary> createItinerary(Itinerary itinerary) async {
    await initialize();

    _itineraries.add(itinerary);
    await _saveItineraries();

    _loggingService.debug(
        'ItineraryService', 'Created itinerary ${itinerary.id}');
    return itinerary;
  }

  /// Update an itinerary
  Future<Itinerary> updateItinerary(Itinerary itinerary) async {
    await initialize();

    final index = _itineraries.indexWhere((i) => i.id == itinerary.id);
    if (index == -1) {
      throw Exception('Itinerary not found');
    }

    _itineraries[index] = itinerary;
    await _saveItineraries();

    _loggingService.debug(
        'ItineraryService', 'Updated itinerary ${itinerary.id}');
    return itinerary;
  }

  /// Delete an itinerary
  Future<void> deleteItinerary(String id) async {
    await initialize();

    _itineraries.removeWhere((i) => i.id == id);
    await _saveItineraries();

    _loggingService.debug('ItineraryService', 'Deleted itinerary $id');
  }

  /// Add an item to an itinerary day
  Future<Itinerary> addItemToDay(
      String itineraryId, String dayId, ItineraryItem item) async {
    await initialize();

    final itinerary = await getItinerary(itineraryId);
    if (itinerary == null) {
      throw Exception('Itinerary not found');
    }

    final dayIndex = itinerary.days.indexWhere((d) => d.id == dayId);
    if (dayIndex == -1) {
      throw Exception('Day not found');
    }

    final updatedDay = itinerary.days[dayIndex].addItem(item);
    final updatedDays = List<ItineraryDay>.from(itinerary.days);
    updatedDays[dayIndex] = updatedDay;

    final updatedItinerary = itinerary.copyWith(days: updatedDays);
    return await updateItinerary(updatedItinerary);
  }

  /// Remove an item from an itinerary day
  Future<Itinerary> removeItemFromDay(
      String itineraryId, String dayId, String itemId) async {
    await initialize();

    final itinerary = await getItinerary(itineraryId);
    if (itinerary == null) {
      throw Exception('Itinerary not found');
    }

    final dayIndex = itinerary.days.indexWhere((d) => d.id == dayId);
    if (dayIndex == -1) {
      throw Exception('Day not found');
    }

    final updatedDay = itinerary.days[dayIndex].removeItem(itemId);
    final updatedDays = List<ItineraryDay>.from(itinerary.days);
    updatedDays[dayIndex] = updatedDay;

    final updatedItinerary = itinerary.copyWith(days: updatedDays);
    return await updateItinerary(updatedItinerary);
  }

  /// Update an item in an itinerary day
  Future<Itinerary> updateItemInDay(
      String itineraryId, String dayId, ItineraryItem item) async {
    await initialize();

    final itinerary = await getItinerary(itineraryId);
    if (itinerary == null) {
      throw Exception('Itinerary not found');
    }

    final dayIndex = itinerary.days.indexWhere((d) => d.id == dayId);
    if (dayIndex == -1) {
      throw Exception('Day not found');
    }

    final updatedDay = itinerary.days[dayIndex].updateItem(item);
    final updatedDays = List<ItineraryDay>.from(itinerary.days);
    updatedDays[dayIndex] = updatedDay;

    final updatedItinerary = itinerary.copyWith(days: updatedDays);
    return await updateItinerary(updatedItinerary);
  }

  /// Move an item to a different day
  Future<Itinerary> moveItemToDay(String itineraryId, String sourceDayId,
      String targetDayId, String itemId) async {
    await initialize();

    final itinerary = await getItinerary(itineraryId);
    if (itinerary == null) {
      throw Exception('Itinerary not found');
    }

    final sourceDayIndex =
        itinerary.days.indexWhere((d) => d.id == sourceDayId);
    if (sourceDayIndex == -1) {
      throw Exception('Source day not found');
    }

    final targetDayIndex =
        itinerary.days.indexWhere((d) => d.id == targetDayId);
    if (targetDayIndex == -1) {
      throw Exception('Target day not found');
    }

    final item =
        itinerary.days[sourceDayIndex].items.firstWhere((i) => i.id == itemId);

    // Remove from source day
    final updatedSourceDay = itinerary.days[sourceDayIndex].removeItem(itemId);

    // Add to target day
    final updatedTargetDay = itinerary.days[targetDayIndex].addItem(item);

    final updatedDays = List<ItineraryDay>.from(itinerary.days);
    updatedDays[sourceDayIndex] = updatedSourceDay;
    updatedDays[targetDayIndex] = updatedTargetDay;

    final updatedItinerary = itinerary.copyWith(days: updatedDays);
    return await updateItinerary(updatedItinerary);
  }

  /// Generate AI recommendations for an itinerary
  Future<List<AIRecommendation>> generateRecommendations(
    String itineraryId, {
    int limit = 5,
    List<ItineraryItemType>? itemTypes,
    DateTime? date,
  }) async {
    await initialize();

    // In a real app, this would call the AI recommendation API
    // For now, we'll generate mock recommendations

    final itinerary = await getItinerary(itineraryId);
    if (itinerary == null) {
      throw Exception('Itinerary not found');
    }

    // Mock delay to simulate API call
    await Future.delayed(const Duration(seconds: 2));

    // Generate mock recommendations
    final recommendations = <AIRecommendation>[];

    // Add some mock recommendations
    recommendations.add(_createMockRecommendation(
      'Visit the National Museum',
      ItineraryItemType.activity,
      'A must-visit cultural attraction with artifacts from the region\'s rich history.',
      85.5,
      RecommendationCategory.popularity,
      const TimeOfDay(hour: 10, minute: 0),
      const TimeOfDay(hour: 12, minute: 30),
      'National Museum, City Center',
      15.0,
    ));

    recommendations.add(_createMockRecommendation(
      'Lunch at Coastal Flavors',
      ItineraryItemType.food,
      'Highly rated seafood restaurant with local specialties and ocean views.',
      92.3,
      RecommendationCategory.userPreferences,
      const TimeOfDay(hour: 13, minute: 0),
      const TimeOfDay(hour: 14, minute: 30),
      'Coastal Flavors, Harbor District',
      35.0,
    ));

    recommendations.add(_createMockRecommendation(
      'Sunset Beach Walk',
      ItineraryItemType.activity,
      'Enjoy the beautiful sunset at the beach, perfect for photos and relaxation.',
      78.9,
      RecommendationCategory.weather,
      const TimeOfDay(hour: 17, minute: 30),
      const TimeOfDay(hour: 19, minute: 0),
      'Main Beach, Coastal Road',
      0.0,
    ));

    recommendations.add(_createMockRecommendation(
      'Dinner at Mountain View Restaurant',
      ItineraryItemType.food,
      'Fine dining with panoramic views of the city and mountains.',
      81.2,
      RecommendationCategory.location,
      const TimeOfDay(hour: 19, minute: 30),
      const TimeOfDay(hour: 21, minute: 30),
      'Mountain View Restaurant, Hillside Drive',
      55.0,
    ));

    recommendations.add(_createMockRecommendation(
      'Local Market Tour',
      ItineraryItemType.activity,
      'Explore the vibrant local market with fresh produce, crafts, and street food.',
      88.7,
      RecommendationCategory.userHistory,
      const TimeOfDay(hour: 9, minute: 0),
      const TimeOfDay(hour: 11, minute: 0),
      'Central Market, Downtown',
      10.0,
    ));

    // Filter by item types if specified
    if (itemTypes != null && itemTypes.isNotEmpty) {
      recommendations.removeWhere((r) {
        // Convert the item type to the corresponding type in itinerary.dart
        final itemType = getMatchingItemType(r.item.type);
        return !itemTypes.contains(itemType);
      });
    }

    // Return limited number of recommendations
    return recommendations.take(limit).toList();
  }

  /// Create a mock recommendation
  AIRecommendation _createMockRecommendation(
    String title,
    ItineraryItemType type,
    String description,
    double confidenceScore,
    RecommendationCategory category,
    TimeOfDay startTime,
    TimeOfDay endTime,
    String location,
    double price,
  ) {
    final item = item_model.ItineraryItem(
      title: title,
      description: description,
      type: _convertItemType(type),
      startTime: startTime,
      endTime: endTime,
      location: location,
      price: price,
      currency: 'USD',
      recommendationScore: confidenceScore,
    );

    return AIRecommendation(
      id: const Uuid().v4(),
      item: item,
      confidenceScore: confidenceScore,
      reason:
          'This ${type.toString().split('.').last.toLowerCase()} matches your preferences and is highly rated by travelers with similar interests.',
      category: category,
    );
  }

  /// Convert ItineraryItemType from itinerary.dart to ItineraryItemType from itinerary_item.dart
  item_model.ItineraryItemType _convertItemType(ItineraryItemType type) {
    switch (type) {
      case ItineraryItemType.activity:
        return item_model.ItineraryItemType.activity;
      case ItineraryItemType.food:
        return item_model.ItineraryItemType.food;
      case ItineraryItemType.accommodation:
        return item_model.ItineraryItemType.accommodation;
      case ItineraryItemType.transportation:
        return item_model.ItineraryItemType.transportation;
      default:
        return item_model.ItineraryItemType.custom;
    }
  }

  /// Get the matching ItineraryItemType from itinerary.dart for an ItineraryItemType from itinerary_item.dart
  ItineraryItemType getMatchingItemType(item_model.ItineraryItemType type) {
    switch (type) {
      case item_model.ItineraryItemType.activity:
        return ItineraryItemType.activity;
      case item_model.ItineraryItemType.food:
        return ItineraryItemType.food;
      case item_model.ItineraryItemType.accommodation:
        return ItineraryItemType.accommodation;
      case item_model.ItineraryItemType.transportation:
        return ItineraryItemType.transportation;
      case item_model.ItineraryItemType.custom:
        return ItineraryItemType.custom;
    }
  }
}
