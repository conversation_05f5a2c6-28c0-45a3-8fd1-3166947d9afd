/// Flight search service
///
/// This file contains the service for searching flights, including mock data generation
/// for testing and development purposes.
library flight_search_service;

// Dart imports
import 'dart:math';

// Package imports
import 'package:cloud_firestore/cloud_firestore.dart';

// Project imports - Models
import 'package:culture_connect/models/travel/travel.dart';
import 'package:culture_connect/models/travel/flight_search_params.dart';
import 'package:culture_connect/models/location/geo_location.dart';

// Project imports - Services
import 'package:culture_connect/services/logging_service.dart';

/// Enum for flight status types
enum FlightStatusType {
  /// Flight is on time
  onTime,

  /// Flight is delayed
  delayed,

  /// Flight is cancelled
  cancelled,

  /// Flight is diverted
  diverted,
}

/// Enum for baggage types
enum BaggageType {
  /// Checked baggage
  checked,

  /// Carry-on baggage
  carryOn,

  /// Personal item
  personalItem,
}

/// Enum for additional service types
enum AdditionalServiceType {
  /// Meal service
  meal,

  /// Wi-Fi service
  wifi,

  /// Lounge access
  lounge,

  /// Priority boarding
  priority,

  /// Travel insurance
  insurance,

  /// Extra baggage
  baggage,
}

/// A model representing flight details
class FlightDetails {
  /// Flight ID
  final String flightId;

  /// Aircraft type
  final String aircraft;

  /// Aircraft registration
  final String aircraftRegistration;

  /// Aircraft age in years
  final int aircraftAge;

  /// Seat capacity
  final int seatCapacity;

  /// Amenities
  final List<String> amenities;

  /// Meal options
  final List<String> mealOptions;

  /// Special services
  final List<String> specialServices;

  /// Baggage allowance in kilograms
  final double baggageAllowance;

  /// Carry-on allowance in kilograms
  final double carryOnAllowance;

  /// Cancellation policy
  final String cancellationPolicy;

  /// Change policy
  final String changePolicy;

  /// Refund policy
  final String refundPolicy;

  /// Creates a new flight details
  const FlightDetails({
    required this.flightId,
    required this.aircraft,
    required this.aircraftRegistration,
    required this.aircraftAge,
    required this.seatCapacity,
    required this.amenities,
    required this.mealOptions,
    required this.specialServices,
    required this.baggageAllowance,
    required this.carryOnAllowance,
    required this.cancellationPolicy,
    required this.changePolicy,
    required this.refundPolicy,
  });
}

/// A model representing a seat on a flight
class Seat {
  /// Seat ID (e.g., "1A")
  final String id;

  /// Row number
  final int row;

  /// Column number
  final int column;

  /// Seat class
  final SeatClass seatClass;

  /// Seat type
  final SeatType seatType;

  /// Whether the seat is available
  final bool isAvailable;

  /// Whether the seat is in an exit row
  final bool isExitRow;

  /// Price for the seat
  final double price;

  /// Creates a new seat
  const Seat({
    required this.id,
    required this.row,
    required this.column,
    required this.seatClass,
    required this.seatType,
    required this.isAvailable,
    required this.isExitRow,
    required this.price,
  });
}

/// Enum for seat types
enum SeatType {
  /// Window seat
  window,

  /// Middle seat
  middle,

  /// Aisle seat
  aisle,

  /// Exit row seat
  exit,
}

/// Enum for seat classes
enum SeatClass {
  /// Economy class
  economy,

  /// Premium economy class
  premiumEconomy,

  /// Business class
  business,

  /// First class
  first,
}

/// A model representing a seat map
class SeatMap {
  /// Flight ID
  final String flightId;

  /// Aircraft type
  final String aircraft;

  /// Number of rows
  final int rows;

  /// Number of columns
  final int columns;

  /// Seats
  final List<Seat> seats;

  /// Creates a new seat map
  const SeatMap({
    required this.flightId,
    required this.aircraft,
    required this.rows,
    required this.columns,
    required this.seats,
  });
}

/// A model representing baggage information
class BaggageInfo {
  /// Flight ID
  final String flightId;

  /// Checked baggage allowance in kilograms
  final double checkedBaggageAllowance;

  /// Carry-on allowance in kilograms
  final double carryOnAllowance;

  /// Personal item allowance in kilograms
  final double personalItemAllowance;

  /// Overweight fee
  final double overweightFee;

  /// Oversize fee
  final double oversizeFee;

  /// Additional baggage fees
  final List<AdditionalBaggageFee> additionalBaggageFees;

  /// Baggage restrictions
  final List<String> restrictions;

  /// Creates new baggage information
  const BaggageInfo({
    required this.flightId,
    required this.checkedBaggageAllowance,
    required this.carryOnAllowance,
    required this.personalItemAllowance,
    required this.overweightFee,
    required this.oversizeFee,
    required this.additionalBaggageFees,
    required this.restrictions,
  });
}

/// A model representing an additional baggage fee
class AdditionalBaggageFee {
  /// Baggage type
  final BaggageType baggageType;

  /// Price
  final double price;

  /// Description
  final String description;

  /// Creates a new additional baggage fee
  const AdditionalBaggageFee({
    required this.baggageType,
    required this.price,
    required this.description,
  });
}

/// A model representing flight status
class FlightStatus {
  /// Flight ID
  final String flightId;

  /// Status type
  final FlightStatusType statusType;

  /// Scheduled departure time
  final DateTime scheduledDepartureTime;

  /// Actual departure time
  final DateTime actualDepartureTime;

  /// Scheduled arrival time
  final DateTime scheduledArrivalTime;

  /// Actual arrival time
  final DateTime actualArrivalTime;

  /// Gate
  final String gate;

  /// Terminal
  final String terminal;

  /// Delay reason
  final String? delayReason;

  /// Last updated
  final DateTime lastUpdated;

  /// Creates a new flight status
  const FlightStatus({
    required this.flightId,
    required this.statusType,
    required this.scheduledDepartureTime,
    required this.actualDepartureTime,
    required this.scheduledArrivalTime,
    required this.actualArrivalTime,
    required this.gate,
    required this.terminal,
    this.delayReason,
    required this.lastUpdated,
  });
}

/// A model representing a fare component
class FareComponent {
  /// Description
  final String description;

  /// Amount
  final double amount;

  /// Creates a new fare component
  const FareComponent({
    required this.description,
    required this.amount,
  });
}

/// A model representing a fare breakdown
class FareBreakdown {
  /// Flight ID
  final String flightId;

  /// Base fare
  final double baseFare;

  /// Taxes
  final double taxes;

  /// Fees
  final double fees;

  /// Surcharges
  final double surcharges;

  /// Total price
  final double totalPrice;

  /// Currency
  final String currency;

  /// Fare rules
  final List<String> fareRules;

  /// Fare components
  final List<FareComponent> fareComponents;

  /// Creates a new fare breakdown
  const FareBreakdown({
    required this.flightId,
    required this.baseFare,
    required this.taxes,
    required this.fees,
    required this.surcharges,
    required this.totalPrice,
    required this.currency,
    required this.fareRules,
    required this.fareComponents,
  });
}

/// A model representing an additional service
class AdditionalService {
  /// Service ID
  final String id;

  /// Service type
  final AdditionalServiceType type;

  /// Service name
  final String name;

  /// Service description
  final String description;

  /// Price
  final double price;

  /// Currency
  final String currency;

  /// Image URL
  final String imageUrl;

  /// Creates a new additional service
  const AdditionalService({
    required this.id,
    required this.type,
    required this.name,
    required this.description,
    required this.price,
    required this.currency,
    required this.imageUrl,
  });
}

/// A model representing an airport
class Airport {
  /// Airport ID
  final String id;

  /// Airport code (IATA)
  final String code;

  /// Airport name
  final String name;

  /// City
  final String city;

  /// Country
  final String country;

  /// Coordinates
  final GeoLocation coordinates;

  /// Whether the airport is popular
  final bool isPopular;

  /// Creates a new airport
  const Airport({
    required this.id,
    required this.code,
    required this.name,
    required this.city,
    required this.country,
    required this.coordinates,
    this.isPopular = false,
  });

  /// Create from JSON
  factory Airport.fromJson(Map<String, dynamic> json) {
    return Airport(
      id: json['id'] as String,
      code: json['code'] as String,
      name: json['name'] as String,
      city: json['city'] as String,
      country: json['country'] as String,
      coordinates: GeoLocation(
        latitude: (json['latitude'] as num).toDouble(),
        longitude: (json['longitude'] as num).toDouble(),
      ),
      isPopular: json['isPopular'] as bool? ?? false,
    );
  }
}

/// A model representing an airline
class Airline {
  /// Airline ID
  final String id;

  /// Airline code (IATA)
  final String code;

  /// Airline name
  final String name;

  /// Country
  final String country;

  /// Logo URL
  final String logoUrl;

  /// Creates a new airline
  const Airline({
    required this.id,
    required this.code,
    required this.name,
    required this.country,
    required this.logoUrl,
  });

  /// Create from JSON
  factory Airline.fromJson(Map<String, dynamic> json) {
    return Airline(
      id: json['id'] as String,
      code: json['code'] as String,
      name: json['name'] as String,
      country: json['country'] as String,
      logoUrl: json['logoUrl'] as String,
    );
  }
}

/// A model representing a price history point
class PriceHistoryPoint {
  /// Date
  final DateTime date;

  /// Price
  final double price;

  /// Creates a new price history point
  const PriceHistoryPoint({
    required this.date,
    required this.price,
  });
}

/// Service for searching flights
///
/// This service provides functionality to search for flights based on various criteria
/// such as origin, destination, dates, and passenger information.
class FlightSearchService {
  /// Firestore instance (optional)
  /// If not provided, the service will use mock data
  final FirebaseFirestore? _firestore;

  /// Logging service for error tracking and debugging
  final LoggingService _loggingService;

  /// Whether the service has been initialized
  bool _isInitialized = false;

  /// Mock data for flights
  List<Flight> _flights = [];

  /// Mock data for airports
  List<Airport> _airports = [];

  /// Mock data for airlines
  List<Airline> _airlines = [];

  /// Creates a new flight search service
  ///
  /// [firestore] is optional. If not provided, the service will use mock data.
  /// [loggingService] is required for logging.
  FlightSearchService({
    FirebaseFirestore? firestore,
    LoggingService? loggingService,
  })  : _firestore = firestore,
        _loggingService = loggingService ?? LoggingService();

  /// Initialize the service
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      _loggingService.debug(
        'FlightSearchService',
        'Initializing flight search service',
      );

      // Try to get data from Firestore if available
      if (_firestore != null) {
        try {
          // Use a local variable to avoid repeated null checks
          final firestore = _firestore;
          if (firestore == null) {
            // This should never happen since we already checked _firestore != null,
            // but we're being extra safe
            throw Exception('Firestore instance is null');
          }

          // Get flights from Firestore
          final flightsSnapshot =
              await firestore.collection('flights').limit(50).get();

          if (flightsSnapshot.docs.isNotEmpty) {
            _flights = flightsSnapshot.docs
                .map((doc) => _flightFromFirestore(doc))
                .toList();
          } else {
            // Fallback to mock data
            _generateMockData();
          }

          // Get airports from Firestore
          final airportsSnapshot = await firestore.collection('airports').get();

          if (airportsSnapshot.docs.isNotEmpty) {
            _airports = airportsSnapshot.docs
                .map((doc) {
                  try {
                    return Airport.fromJson({...doc.data(), 'id': doc.id});
                  } catch (e) {
                    _loggingService.error(
                      'FlightSearchService',
                      'Error parsing airport data',
                      e,
                      StackTrace.current,
                    );
                    return null;
                  }
                })
                .whereType<Airport>() // Filter out nulls
                .toList();
          } else {
            // Fallback to mock airports
            _generateMockAirports();
          }

          // Get airlines from Firestore
          final airlinesSnapshot = await firestore.collection('airlines').get();

          if (airlinesSnapshot.docs.isNotEmpty) {
            _airlines = airlinesSnapshot.docs
                .map((doc) {
                  try {
                    return Airline.fromJson({...doc.data(), 'id': doc.id});
                  } catch (e) {
                    _loggingService.error(
                      'FlightSearchService',
                      'Error parsing airline data',
                      e,
                      StackTrace.current,
                    );
                    return null;
                  }
                })
                .whereType<Airline>() // Filter out nulls
                .toList();
          } else {
            // Fallback to mock airlines
            _generateMockAirlines();
          }
        } catch (e) {
          _loggingService.error(
            'FlightSearchService',
            'Error getting data from Firestore',
            e,
            StackTrace.current,
          );
          // Fallback to mock data
          _generateMockData();
          _generateMockAirports();
          _generateMockAirlines();
        }
      } else {
        // No Firestore, use mock data
        _generateMockData();
        _generateMockAirports();
        _generateMockAirlines();
      }

      _isInitialized = true;

      _loggingService.debug(
        'FlightSearchService',
        'Flight search service initialized with ${_flights.length} flights, ${_airports.length} airports, and ${_airlines.length} airlines',
      );
    } catch (e, stackTrace) {
      _loggingService.error(
        'FlightSearchService',
        'Error initializing flight search service',
        e,
        stackTrace,
      );
      rethrow;
    }
  }

  /// Search for flights based on search parameters
  Future<List<Flight>> searchFlights(FlightSearchParams params) async {
    await initialize();

    try {
      _loggingService.debug(
        'FlightSearchService',
        'Searching flights with params: ${params.tripType}, ${params.routes.first.originCode} to ${params.routes.first.destinationCode}',
      );

      // In a real app, this would query an API or Firestore
      // For now, filter the mock data

      // Add a delay to simulate network request
      await Future.delayed(const Duration(milliseconds: 800));

      // Filter flights based on search parameters
      final filteredFlights = _flights.where((flight) {
        // Check if the flight matches the route
        final route = params.routes.first;
        final matchesRoute = flight.departureAirportCode == route.originCode &&
            flight.arrivalAirportCode == route.destinationCode;

        // Check if the flight is on the requested date
        final matchesDate =
            flight.departureTime.year == route.departureDate.year &&
                flight.departureTime.month == route.departureDate.month &&
                flight.departureTime.day == route.departureDate.day;

        // Check if the flight class matches
        final matchesClass = flight.flightClass == params.flightClass;

        // Check if direct flights only
        final matchesDirect =
            !params.directFlightsOnly || flight.layovers.isEmpty;

        return matchesRoute && matchesDate && matchesClass && matchesDirect;
      }).toList();

      return filteredFlights;
    } catch (e, stackTrace) {
      _loggingService.error(
        'FlightSearchService',
        'Error searching flights',
        e,
        stackTrace,
      );
      rethrow;
    }
  }

  /// Get all airports
  Future<List<Airport>> getAirports() async {
    await initialize();
    return _airports;
  }

  /// Get popular airports
  Future<List<Airport>> getPopularAirports() async {
    await initialize();
    // Return top 10 airports by passenger count
    return _airports.where((airport) => airport.isPopular).take(10).toList();
  }

  /// Get recent airports
  Future<List<Airport>> getRecentAirports() async {
    await initialize();
    // In a real app, this would come from user history
    // For now, return a subset of airports
    return _airports.take(5).toList();
  }

  /// Get all airlines
  Future<List<Airline>> getAirlines() async {
    await initialize();
    return _airlines;
  }

  /// Get fare calendar for a route
  Future<Map<DateTime, double>> getFareCalendar(FlightRoute route) async {
    await initialize();

    // In a real app, this would come from an API
    // For now, generate random prices for the next 30 days
    final now = DateTime.now();
    final random = Random();
    final calendar = <DateTime, double>{};

    for (int i = 0; i < 30; i++) {
      final date = DateTime(now.year, now.month, now.day + i);
      final basePrice = 200.0 + random.nextDouble() * 300.0;
      calendar[date] = basePrice;
    }

    return calendar;
  }

  /// Get price history for a route
  Future<List<PriceHistoryPoint>> getPriceHistory(FlightRoute route) async {
    await initialize();

    // In a real app, this would come from an API
    // For now, generate random price history for the last 30 days
    final now = DateTime.now();
    final random = Random();
    final history = <PriceHistoryPoint>[];

    for (int i = 30; i >= 0; i--) {
      final date = DateTime(now.year, now.month, now.day - i);
      final price = 200.0 + random.nextDouble() * 300.0;
      history.add(PriceHistoryPoint(date: date, price: price));
    }

    return history;
  }

  /// Get flight details
  Future<FlightDetails?> getFlightDetails(String flightId) async {
    await initialize();

    try {
      // Find the flight
      final flight = _flights.firstWhere((f) => f.id == flightId);

      // In a real app, this would come from an API
      // For now, generate mock details
      return FlightDetails(
        flightId: flightId,
        aircraft: flight.segments.first.aircraftType,
        aircraftRegistration: 'N12345',
        aircraftAge: 5,
        seatCapacity: 180,
        amenities: flight.amenities,
        mealOptions: ['Vegetarian', 'Kosher', 'Standard'],
        specialServices: ['Wheelchair assistance', 'Unaccompanied minor'],
        baggageAllowance: flight.baggageAllowance,
        carryOnAllowance: 7.0,
        cancellationPolicy: flight.cancellationPolicy,
        changePolicy: 'Changes allowed with fee',
        refundPolicy:
            flight.isRefundable ? 'Refundable with fee' : 'Non-refundable',
      );
    } catch (e) {
      return null;
    }
  }

  /// Get seat map for a flight
  Future<SeatMap?> getSeatMap(String flightId) async {
    await initialize();

    try {
      // Find the flight
      final flight = _flights.firstWhere((f) => f.id == flightId);

      // In a real app, this would come from an API
      // For now, generate a mock seat map
      final random = Random();
      const rows = 30;
      const columns = 6;
      final seats = <Seat>[];

      for (int row = 1; row <= rows; row++) {
        for (int col = 0; col < columns; col++) {
          final seatClass = row <= 3 ? SeatClass.business : SeatClass.economy;
          final seatType = _getSeatType(col);
          final isAvailable = random.nextDouble() > 0.3;
          final price = seatClass == SeatClass.business
              ? 50.0
              : (seatType == SeatType.exit
                  ? 30.0
                  : (seatType == SeatType.window || seatType == SeatType.aisle
                      ? 20.0
                      : 0.0));

          seats.add(Seat(
            id: '$row${String.fromCharCode(65 + col)}',
            row: row,
            column: col,
            seatClass: seatClass,
            seatType: seatType,
            isAvailable: isAvailable,
            isExitRow: row == 10 || row == 11,
            price: price,
          ));
        }
      }

      return SeatMap(
        flightId: flightId,
        aircraft: flight.segments.first.aircraftType,
        rows: rows,
        columns: columns,
        seats: seats,
      );
    } catch (e) {
      return null;
    }
  }

  /// Get baggage information for a flight
  Future<BaggageInfo?> getBaggageInfo(String flightId) async {
    await initialize();

    try {
      // Find the flight
      final flight = _flights.firstWhere((f) => f.id == flightId);

      // In a real app, this would come from an API
      // For now, generate mock baggage info
      return BaggageInfo(
        flightId: flightId,
        checkedBaggageAllowance: flight.baggageAllowance,
        carryOnAllowance: 7.0,
        personalItemAllowance: 2.0,
        overweightFee: 50.0,
        oversizeFee: 75.0,
        additionalBaggageFees: [
          const AdditionalBaggageFee(
            baggageType: BaggageType.checked,
            price: 35.0,
            description: 'First additional checked bag',
          ),
          const AdditionalBaggageFee(
            baggageType: BaggageType.checked,
            price: 50.0,
            description: 'Second additional checked bag',
          ),
          const AdditionalBaggageFee(
            baggageType: BaggageType.carryOn,
            price: 25.0,
            description: 'Additional carry-on bag',
          ),
        ],
        restrictions: [
          'Maximum dimensions for checked baggage: 62 linear inches (158 cm)',
          'Maximum weight for checked baggage: 50 lbs (23 kg)',
          'Maximum dimensions for carry-on: 22 x 14 x 9 inches (56 x 36 x 23 cm)',
          'Maximum weight for carry-on: 22 lbs (10 kg)',
        ],
      );
    } catch (e) {
      return null;
    }
  }

  /// Get flight status
  Future<FlightStatus?> getFlightStatus(String flightId) async {
    await initialize();

    try {
      // Find the flight
      final flight = _flights.firstWhere((f) => f.id == flightId);

      // In a real app, this would come from an API
      // For now, generate mock flight status
      final random = Random();
      final isDelayed = random.nextDouble() < 0.2;
      final isCancelled = random.nextDouble() < 0.05;
      final isDiverted = !isCancelled && random.nextDouble() < 0.02;

      final statusType = isCancelled
          ? FlightStatusType.cancelled
          : isDiverted
              ? FlightStatusType.diverted
              : isDelayed
                  ? FlightStatusType.delayed
                  : FlightStatusType.onTime;

      final delayMinutes = isDelayed ? 15 + random.nextInt(120) : 0;

      return FlightStatus(
        flightId: flightId,
        statusType: statusType,
        scheduledDepartureTime: flight.departureTime,
        actualDepartureTime: isDelayed
            ? flight.departureTime.add(Duration(minutes: delayMinutes))
            : flight.departureTime,
        scheduledArrivalTime: flight.arrivalTime,
        actualArrivalTime: isDelayed
            ? flight.arrivalTime.add(Duration(minutes: delayMinutes))
            : flight.arrivalTime,
        gate: 'G${random.nextInt(30) + 1}',
        terminal: 'T${random.nextInt(5) + 1}',
        delayReason: isDelayed ? _getRandomDelayReason() : null,
        lastUpdated: DateTime.now(),
      );
    } catch (e) {
      return null;
    }
  }

  /// Get fare breakdown
  Future<FareBreakdown?> getFareBreakdown(String flightId) async {
    await initialize();

    try {
      // Find the flight
      final flight = _flights.firstWhere((f) => f.id == flightId);

      // In a real app, this would come from an API
      // For now, generate mock fare breakdown
      final baseFare = flight.price * 0.7;
      final taxes = flight.price * 0.15;
      final fees = flight.price * 0.1;
      final surcharges = flight.price * 0.05;

      return FareBreakdown(
        flightId: flightId,
        baseFare: baseFare,
        taxes: taxes,
        fees: fees,
        surcharges: surcharges,
        totalPrice: flight.price,
        currency: flight.currency,
        fareRules: [
          'Non-refundable',
          'Changes allowed with fee',
          'Valid for travel on ${flight.departureTime.toString().substring(0, 10)}',
        ],
        fareComponents: [
          FareComponent(
            description: 'Base fare',
            amount: baseFare,
          ),
          FareComponent(
            description: 'Taxes',
            amount: taxes,
          ),
          FareComponent(
            description: 'Fees',
            amount: fees,
          ),
          FareComponent(
            description: 'Surcharges',
            amount: surcharges,
          ),
        ],
      );
    } catch (e) {
      return null;
    }
  }

  /// Get additional services for a flight
  Future<List<AdditionalService>> getAdditionalServices(String flightId) async {
    await initialize();

    // In a real app, this would come from an API
    // For now, generate mock additional services
    return [
      const AdditionalService(
        id: 'meal-1',
        type: AdditionalServiceType.meal,
        name: 'Premium Meal',
        description: 'Gourmet meal with wine pairing',
        price: 25.0,
        currency: 'USD',
        imageUrl: 'https://example.com/images/premium-meal.jpg',
      ),
      const AdditionalService(
        id: 'meal-2',
        type: AdditionalServiceType.meal,
        name: 'Vegetarian Meal',
        description: 'Healthy vegetarian option',
        price: 20.0,
        currency: 'USD',
        imageUrl: 'https://example.com/images/vegetarian-meal.jpg',
      ),
      const AdditionalService(
        id: 'wifi',
        type: AdditionalServiceType.wifi,
        name: 'In-flight Wi-Fi',
        description: 'High-speed internet access for the entire flight',
        price: 15.0,
        currency: 'USD',
        imageUrl: 'https://example.com/images/wifi.jpg',
      ),
      const AdditionalService(
        id: 'lounge',
        type: AdditionalServiceType.lounge,
        name: 'Airport Lounge Access',
        description: 'Access to premium lounge before departure',
        price: 45.0,
        currency: 'USD',
        imageUrl: 'https://example.com/images/lounge.jpg',
      ),
      const AdditionalService(
        id: 'priority',
        type: AdditionalServiceType.priority,
        name: 'Priority Boarding',
        description: 'Be among the first to board the aircraft',
        price: 15.0,
        currency: 'USD',
        imageUrl: 'https://example.com/images/priority.jpg',
      ),
      const AdditionalService(
        id: 'insurance',
        type: AdditionalServiceType.insurance,
        name: 'Travel Insurance',
        description: 'Comprehensive travel insurance coverage',
        price: 30.0,
        currency: 'USD',
        imageUrl: 'https://example.com/images/insurance.jpg',
      ),
    ];
  }

  /// Get a random delay reason
  String _getRandomDelayReason() {
    const reasons = [
      'Weather conditions',
      'Air traffic congestion',
      'Operational issues',
      'Technical maintenance',
      'Crew availability',
      'Security checks',
      'Late arrival of connecting aircraft',
    ];

    return reasons[Random().nextInt(reasons.length)];
  }

  /// Get seat type based on column
  SeatType _getSeatType(int column) {
    if (column == 0) {
      return SeatType.window;
    } else if (column == 5) {
      return SeatType.window;
    } else if (column == 2 || column == 3) {
      return SeatType.aisle;
    } else {
      return SeatType.middle;
    }
  }

  /// Generate mock flight data
  void _generateMockData() {
    final random = Random();
    final now = DateTime.now();

    // Generate flights for the next 30 days
    for (int day = 1; day <= 30; day++) {
      final date = DateTime(now.year, now.month, now.day + day);

      // Generate flights between major cities
      _generateFlightsBetween(
        'JFK',
        'New York',
        'LAX',
        'Los Angeles',
        date,
        random,
        5,
      );

      _generateFlightsBetween(
        'SFO',
        'San Francisco',
        'ORD',
        'Chicago',
        date,
        random,
        3,
      );

      _generateFlightsBetween(
        'ATL',
        'Atlanta',
        'DFW',
        'Dallas',
        date,
        random,
        4,
      );

      _generateFlightsBetween(
        'LHR',
        'London',
        'CDG',
        'Paris',
        date,
        random,
        6,
      );

      _generateFlightsBetween(
        'NRT',
        'Tokyo',
        'SYD',
        'Sydney',
        date,
        random,
        2,
      );
    }
  }

  /// Generate flights between two cities
  void _generateFlightsBetween(
    String originCode,
    String originCity,
    String destCode,
    String destCity,
    DateTime date,
    Random random,
    int count,
  ) {
    for (int i = 0; i < count; i++) {
      // Generate random departure time
      final hour = 6 + random.nextInt(14); // Between 6 AM and 8 PM
      final minute = random.nextInt(4) * 15; // 0, 15, 30, or 45
      final departureTime = DateTime(
        date.year,
        date.month,
        date.day,
        hour,
        minute,
      );

      // Generate random duration (2-12 hours)
      final durationMinutes = 120 + random.nextInt(600);

      // Calculate arrival time
      final arrivalTime = departureTime.add(Duration(minutes: durationMinutes));

      // Generate random price
      final price = 200.0 + random.nextDouble() * 800.0;

      // Determine if it's on sale
      final isOnSale = random.nextDouble() < 0.2;
      final originalPrice =
          isOnSale ? price * (1.0 + random.nextDouble() * 0.3) : null;
      final discountPercentage =
          isOnSale ? (originalPrice! - price) / originalPrice * 100 : null;

      // Generate flight segments
      final segments = <FlightSegment>[];
      final layovers = <Layover>[];

      // Determine if it's a direct flight or has layovers
      final isDirectFlight = random.nextDouble() < 0.7;

      if (isDirectFlight) {
        // Direct flight
        segments.add(_generateFlightSegment(
          originCode,
          originCity,
          destCode,
          destCity,
          departureTime,
          arrivalTime,
          durationMinutes,
          random,
        ));
      } else {
        // Flight with layover
        final layoverDurationMinutes = 45 + random.nextInt(120);
        final firstSegmentDuration =
            (durationMinutes - layoverDurationMinutes) ~/ 2;
        final secondSegmentDuration =
            durationMinutes - firstSegmentDuration - layoverDurationMinutes;

        // Generate layover airport
        final layoverAirport =
            _getRandomLayoverAirport(originCode, destCode, random);

        // First segment
        final firstSegmentArrival =
            departureTime.add(Duration(minutes: firstSegmentDuration));
        segments.add(_generateFlightSegment(
          originCode,
          originCity,
          layoverAirport.code,
          layoverAirport.city,
          departureTime,
          firstSegmentArrival,
          firstSegmentDuration,
          random,
        ));

        // Layover
        layovers.add(Layover(
          airportCode: layoverAirport.code,
          airportName: layoverAirport.name,
          city: layoverAirport.city,
          durationMinutes: layoverDurationMinutes,
        ));

        // Second segment
        final secondSegmentDeparture =
            firstSegmentArrival.add(Duration(minutes: layoverDurationMinutes));
        segments.add(_generateFlightSegment(
          layoverAirport.code,
          layoverAirport.city,
          destCode,
          destCity,
          secondSegmentDeparture,
          arrivalTime,
          secondSegmentDuration,
          random,
        ));
      }

      // Create the flight
      final flight = Flight(
        id: 'flight-$originCode-$destCode-${date.day}-$i',
        name: '$originCode to $destCode',
        description: 'Flight from $originCity to $destCity',
        price: price,
        currency: 'USD',
        rating: 3.5 + random.nextDouble() * 1.5,
        reviewCount: random.nextInt(500) + 50,
        imageUrl:
            'https://example.com/images/flights/${originCode.toLowerCase()}_${destCode.toLowerCase()}.jpg',
        additionalImages: [
          'https://example.com/images/flights/cabin1.jpg',
          'https://example.com/images/flights/cabin2.jpg',
          'https://example.com/images/flights/meal.jpg',
        ],
        provider: segments.first.airlineName,
        location: '$originCity to $destCity',
        coordinates: const GeoLocation(
            latitude: 0, longitude: 0), // Not relevant for flights
        isAvailable: true,
        isFeatured: random.nextDouble() < 0.1,
        isOnSale: isOnSale,
        originalPrice: originalPrice,
        discountPercentage: discountPercentage,
        tags: _generateRandomTags(random),
        amenities: _generateRandomAmenities(random),
        cancellationPolicy:
            random.nextDouble() < 0.3 ? 'Fully refundable' : 'Non-refundable',
        createdAt: DateTime.now().subtract(const Duration(days: 30)),
        updatedAt: DateTime.now(),
        flightType: _getRandomFlightType(random),
        flightClass: _getRandomFlightClass(random),
        segments: segments,
        layovers: layovers,
        totalDurationMinutes: durationMinutes,
        departureAirportCode: originCode,
        departureCity: originCity,
        departureTime: departureTime,
        arrivalAirportCode: destCode,
        arrivalCity: destCity,
        arrivalTime: arrivalTime,
        passengerCount: 1,
        baggageAllowance: 23.0,
        isRefundable: random.nextDouble() < 0.3,
        isChangeable: random.nextDouble() < 0.5,
        changeFee: random.nextDouble() < 0.5
            ? 50.0 + random.nextDouble() * 100.0
            : null,
        hasInFlightEntertainment: random.nextBool(),
        hasWifi: random.nextBool(),
        hasPowerOutlets: random.nextBool(),
        hasMeal: random.nextBool(),
        hasLoungeAccess: random.nextDouble() < 0.2,
        hasPriorityBoarding: random.nextDouble() < 0.3,
        hasExtraLegroom: random.nextDouble() < 0.4,
      );

      _flights.add(flight);
    }
  }

  /// Generate a flight segment
  FlightSegment _generateFlightSegment(
    String originCode,
    String originCity,
    String destCode,
    String destCity,
    DateTime departureTime,
    DateTime arrivalTime,
    int durationMinutes,
    Random random,
  ) {
    final airline = _getRandomAirline(random);

    return FlightSegment(
      id: '$originCode-$destCode-${departureTime.millisecondsSinceEpoch}',
      airlineCode: airline.code,
      airlineName: airline.name,
      airlineLogoUrl: airline.logoUrl,
      flightNumber: '${airline.code}${100 + random.nextInt(900)}',
      departureAirportCode: originCode,
      departureAirportName: '$originCity Airport',
      departureCity: originCity,
      departureTerminal: 'T${1 + random.nextInt(5)}',
      departureGate: 'G${1 + random.nextInt(30)}',
      departureTime: departureTime,
      arrivalAirportCode: destCode,
      arrivalAirportName: '$destCity Airport',
      arrivalCity: destCity,
      arrivalTerminal: 'T${1 + random.nextInt(5)}',
      arrivalGate: 'G${1 + random.nextInt(30)}',
      arrivalTime: arrivalTime,
      durationMinutes: durationMinutes,
      aircraftType: _getRandomAircraftType(random),
      distance: 500.0 + random.nextDouble() * 5000.0,
      isOnTime: random.nextDouble() < 0.8,
      delayMinutes: random.nextDouble() < 0.2 ? 15 + random.nextInt(120) : null,
    );
  }

  /// Generate random tags
  List<String> _generateRandomTags(Random random) {
    final allTags = [
      'Direct',
      'Wi-Fi',
      'Entertainment',
      'Meals',
      'Power Outlets',
      'Extra Legroom',
      'Priority Boarding',
      'Lounge Access',
    ];

    final tagCount = 2 + random.nextInt(4);
    final selectedTags = <String>[];

    for (int i = 0; i < tagCount; i++) {
      final tag = allTags[random.nextInt(allTags.length)];
      if (!selectedTags.contains(tag)) {
        selectedTags.add(tag);
      }
    }

    return selectedTags;
  }

  /// Generate random amenities
  List<String> _generateRandomAmenities(Random random) {
    final allAmenities = [
      'Wi-Fi',
      'In-flight entertainment',
      'Power outlets',
      'USB ports',
      'Meal service',
      'Snack service',
      'Beverage service',
      'Blankets',
      'Pillows',
      'Headphones',
      'Eye masks',
      'Amenity kits',
    ];

    final amenityCount = 3 + random.nextInt(6);
    final selectedAmenities = <String>[];

    for (int i = 0; i < amenityCount; i++) {
      final amenity = allAmenities[random.nextInt(allAmenities.length)];
      if (!selectedAmenities.contains(amenity)) {
        selectedAmenities.add(amenity);
      }
    }

    return selectedAmenities;
  }

  /// Get a random flight type
  FlightType _getRandomFlightType(Random random) {
    final types = [
      FlightType.direct,
      FlightType.oneStop,
      FlightType.multiStop,
    ];

    return types[random.nextInt(types.length)];
  }

  /// Get a random flight class
  FlightClass _getRandomFlightClass(Random random) {
    final classes = [
      FlightClass.economy,
      FlightClass.premiumEconomy,
      FlightClass.business,
      FlightClass.first,
    ];

    return classes[random.nextInt(classes.length)];
  }

  /// Get a random aircraft type
  String _getRandomAircraftType(Random random) {
    final types = [
      'Boeing 737-800',
      'Boeing 787-9',
      'Airbus A320',
      'Airbus A350-900',
      'Embraer E190',
      'Bombardier CRJ900',
    ];

    return types[random.nextInt(types.length)];
  }

  /// Get a random airline
  Airline _getRandomAirline(Random random) {
    if (_airlines.isEmpty) {
      _generateMockAirlines();
    }

    return _airlines[random.nextInt(_airlines.length)];
  }

  /// Get a random layover airport
  Airport _getRandomLayoverAirport(
      String originCode, String destCode, Random random) {
    if (_airports.isEmpty) {
      _generateMockAirports();
    }

    // Filter out origin and destination airports
    final possibleLayovers = _airports
        .where(
            (airport) => airport.code != originCode && airport.code != destCode)
        .toList();

    return possibleLayovers[random.nextInt(possibleLayovers.length)];
  }

  /// Generate mock airports
  void _generateMockAirports() {
    _airports = [
      const Airport(
        id: 'airport-1',
        code: 'JFK',
        name: 'John F. Kennedy International Airport',
        city: 'New York',
        country: 'United States',
        coordinates: GeoLocation(latitude: 40.6413, longitude: -73.7781),
        isPopular: true,
      ),
      const Airport(
        id: 'airport-2',
        code: 'LAX',
        name: 'Los Angeles International Airport',
        city: 'Los Angeles',
        country: 'United States',
        coordinates: GeoLocation(latitude: 33.9416, longitude: -118.4085),
        isPopular: true,
      ),
      const Airport(
        id: 'airport-3',
        code: 'ORD',
        name: 'O\'Hare International Airport',
        city: 'Chicago',
        country: 'United States',
        coordinates: GeoLocation(latitude: 41.9742, longitude: -87.9073),
        isPopular: true,
      ),
      const Airport(
        id: 'airport-4',
        code: 'ATL',
        name: 'Hartsfield-Jackson Atlanta International Airport',
        city: 'Atlanta',
        country: 'United States',
        coordinates: GeoLocation(latitude: 33.6407, longitude: -84.4277),
        isPopular: true,
      ),
      const Airport(
        id: 'airport-5',
        code: 'DFW',
        name: 'Dallas/Fort Worth International Airport',
        city: 'Dallas',
        country: 'United States',
        coordinates: GeoLocation(latitude: 32.8998, longitude: -97.0403),
        isPopular: true,
      ),
      const Airport(
        id: 'airport-6',
        code: 'SFO',
        name: 'San Francisco International Airport',
        city: 'San Francisco',
        country: 'United States',
        coordinates: GeoLocation(latitude: 37.6213, longitude: -122.3790),
        isPopular: true,
      ),
      const Airport(
        id: 'airport-7',
        code: 'LHR',
        name: 'London Heathrow Airport',
        city: 'London',
        country: 'United Kingdom',
        coordinates: GeoLocation(latitude: 51.4700, longitude: -0.4543),
        isPopular: true,
      ),
      const Airport(
        id: 'airport-8',
        code: 'CDG',
        name: 'Charles de Gaulle Airport',
        city: 'Paris',
        country: 'France',
        coordinates: GeoLocation(latitude: 49.0097, longitude: 2.5479),
        isPopular: true,
      ),
      const Airport(
        id: 'airport-9',
        code: 'NRT',
        name: 'Narita International Airport',
        city: 'Tokyo',
        country: 'Japan',
        coordinates: GeoLocation(latitude: 35.7647, longitude: 140.3864),
        isPopular: true,
      ),
      const Airport(
        id: 'airport-10',
        code: 'SYD',
        name: 'Sydney Airport',
        city: 'Sydney',
        country: 'Australia',
        coordinates: GeoLocation(latitude: -33.9399, longitude: 151.1753),
        isPopular: true,
      ),
      const Airport(
        id: 'airport-11',
        code: 'DEN',
        name: 'Denver International Airport',
        city: 'Denver',
        country: 'United States',
        coordinates: GeoLocation(latitude: 39.8561, longitude: -104.6737),
        isPopular: false,
      ),
      const Airport(
        id: 'airport-12',
        code: 'DXB',
        name: 'Dubai International Airport',
        city: 'Dubai',
        country: 'United Arab Emirates',
        coordinates: GeoLocation(latitude: 25.2532, longitude: 55.3657),
        isPopular: true,
      ),
    ];
  }

  /// Generate mock airlines
  void _generateMockAirlines() {
    _airlines = [
      const Airline(
        id: 'airline-1',
        code: 'AA',
        name: 'American Airlines',
        country: 'United States',
        logoUrl: 'https://example.com/images/airlines/aa.png',
      ),
      const Airline(
        id: 'airline-2',
        code: 'DL',
        name: 'Delta Air Lines',
        country: 'United States',
        logoUrl: 'https://example.com/images/airlines/dl.png',
      ),
      const Airline(
        id: 'airline-3',
        code: 'UA',
        name: 'United Airlines',
        country: 'United States',
        logoUrl: 'https://example.com/images/airlines/ua.png',
      ),
      const Airline(
        id: 'airline-4',
        code: 'BA',
        name: 'British Airways',
        country: 'United Kingdom',
        logoUrl: 'https://example.com/images/airlines/ba.png',
      ),
      const Airline(
        id: 'airline-5',
        code: 'LH',
        name: 'Lufthansa',
        country: 'Germany',
        logoUrl: 'https://example.com/images/airlines/lh.png',
      ),
      const Airline(
        id: 'airline-6',
        code: 'AF',
        name: 'Air France',
        country: 'France',
        logoUrl: 'https://example.com/images/airlines/af.png',
      ),
      const Airline(
        id: 'airline-7',
        code: 'JL',
        name: 'Japan Airlines',
        country: 'Japan',
        logoUrl: 'https://example.com/images/airlines/jl.png',
      ),
      const Airline(
        id: 'airline-8',
        code: 'QF',
        name: 'Qantas',
        country: 'Australia',
        logoUrl: 'https://example.com/images/airlines/qf.png',
      ),
    ];
  }

  /// Convert a Firestore document to a Flight
  ///
  /// This method safely converts a Firestore document to a Flight object,
  /// handling potential null values and type mismatches.
  Flight _flightFromFirestore(DocumentSnapshot doc) {
    // Safely cast the data to Map<String, dynamic>
    final data = doc.data();
    if (data == null) {
      throw Exception('Document data is null for document ID: ${doc.id}');
    }

    // Ensure data is a Map<String, dynamic>
    data as Map<String, dynamic>;

    return Flight(
      id: doc.id,
      name: data['name'] as String,
      description: data['description'] as String,
      price: (data['price'] as num).toDouble(),
      currency: data['currency'] as String,
      rating: (data['rating'] as num).toDouble(),
      reviewCount: data['reviewCount'] as int,
      imageUrl: data['imageUrl'] as String,
      additionalImages: List<String>.from(data['additionalImages'] as List),
      provider: data['provider'] as String,
      location: data['location'] as String,
      coordinates: GeoLocation(
        latitude: (data['latitude'] as num).toDouble(),
        longitude: (data['longitude'] as num).toDouble(),
      ),
      isAvailable: data['isAvailable'] as bool,
      isFeatured: data['isFeatured'] as bool,
      isOnSale: data['isOnSale'] as bool,
      originalPrice: data['originalPrice'] != null
          ? (data['originalPrice'] as num).toDouble()
          : null,
      discountPercentage: data['discountPercentage'] != null
          ? (data['discountPercentage'] as num).toDouble()
          : null,
      tags: List<String>.from(data['tags'] as List),
      amenities: List<String>.from(data['amenities'] as List),
      cancellationPolicy: data['cancellationPolicy'] as String,
      createdAt: (data['createdAt'] as Timestamp).toDate(),
      updatedAt: (data['updatedAt'] as Timestamp).toDate(),
      flightType: FlightType.values.firstWhere(
        (type) => type.toString() == data['flightType'] as String,
        orElse: () => FlightType.direct,
      ),
      flightClass: FlightClass.values.firstWhere(
        (cls) => cls.toString() == data['flightClass'] as String,
        orElse: () => FlightClass.economy,
      ),
      segments: (data['segments'] as List)
          .map((segment) => FlightSegment(
                id: segment['id'] as String? ??
                    '${segment['departureAirportCode']}-${segment['arrivalAirportCode']}',
                airlineCode: segment['airlineCode'] as String,
                airlineName: segment['airlineName'] as String,
                airlineLogoUrl: segment['airlineLogoUrl'] as String,
                flightNumber: segment['flightNumber'] as String,
                departureAirportCode: segment['departureAirportCode'] as String,
                departureAirportName:
                    segment['departureAirportName'] as String? ??
                        '${segment['departureCity']} Airport',
                departureCity: segment['departureCity'] as String,
                departureTerminal: segment['departureTerminal'] as String?,
                departureGate: segment['departureGate'] as String?,
                departureTime: (segment['departureTime'] as Timestamp).toDate(),
                arrivalAirportCode: segment['arrivalAirportCode'] as String,
                arrivalAirportName: segment['arrivalAirportName'] as String? ??
                    '${segment['arrivalCity']} Airport',
                arrivalCity: segment['arrivalCity'] as String,
                arrivalTerminal: segment['arrivalTerminal'] as String?,
                arrivalGate: segment['arrivalGate'] as String?,
                arrivalTime: (segment['arrivalTime'] as Timestamp).toDate(),
                durationMinutes: segment['durationMinutes'] as int,
                aircraftType: segment['aircraftType'] as String,
                distance: (segment['distance'] as num).toDouble(),
                isOnTime: segment['isOnTime'] as bool,
                delayMinutes: segment['delayMinutes'] as int?,
              ))
          .toList(),
      layovers: (data['layovers'] as List)
          .map((layover) => Layover(
                airportCode: layover['airport'] as String,
                airportName: '${layover['city']} Airport',
                city: layover['city'] as String,
                durationMinutes: layover['duration'] as int,
              ))
          .toList(),
      totalDurationMinutes: data['totalDurationMinutes'] as int,
      departureAirportCode: data['departureAirportCode'] as String,
      departureCity: data['departureCity'] as String,
      departureTime: (data['departureTime'] as Timestamp).toDate(),
      arrivalAirportCode: data['arrivalAirportCode'] as String,
      arrivalCity: data['arrivalCity'] as String,
      arrivalTime: (data['arrivalTime'] as Timestamp).toDate(),
      passengerCount: data['passengerCount'] as int,
      baggageAllowance: (data['baggageAllowance'] as num).toDouble(),
      isRefundable: data['isRefundable'] as bool,
      isChangeable: data['isChangeable'] as bool,
      changeFee: data['changeFee'] != null
          ? (data['changeFee'] as num).toDouble()
          : null,
      hasInFlightEntertainment: data['hasInFlightEntertainment'] as bool,
      hasWifi: data['hasWifi'] as bool,
      hasPowerOutlets: data['hasPowerOutlets'] as bool,
      hasMeal: data['hasMeal'] as bool,
      hasLoungeAccess: data['hasLoungeAccess'] as bool,
      hasPriorityBoarding: data['hasPriorityBoarding'] as bool,
      hasExtraLegroom: data['hasExtraLegroom'] as bool,
    );
  }
}
