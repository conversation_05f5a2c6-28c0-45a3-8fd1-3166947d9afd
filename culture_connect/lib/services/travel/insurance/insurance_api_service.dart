import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;

// Project imports
import 'package:culture_connect/models/travel/insurance/insurance_claim.dart';
import 'package:culture_connect/models/travel/insurance/insurance_policy.dart';
import 'package:culture_connect/models/travel/insurance/insurance_provider.dart';
import 'package:culture_connect/models/travel/insurance/insurance_coverage_type.dart';
import 'package:culture_connect/models/travel/insurance/insurance_claim_status.dart';
import 'package:culture_connect/models/payment/payment_method_model.dart';
import 'package:culture_connect/utils/exceptions/api_exception.dart';

/// Service for interacting with the insurance API
class InsuranceApiService {
  /// Base URL for insurance API endpoints
  final String baseUrl;

  /// Endpoint for insurance policies
  final String policiesEndpoint;

  /// Endpoint for insurance providers
  final String providersEndpoint;

  /// Endpoint for insurance claims
  final String claimsEndpoint;

  /// HTTP client for making API requests
  final http.Client httpClient;

  /// Creates a new insurance API service
  InsuranceApiService({
    http.Client? httpClient,
    String? baseUrl,
    String? policiesEndpoint,
    String? providersEndpoint,
    String? claimsEndpoint,
  })  : httpClient = httpClient ?? http.Client(),
        baseUrl = baseUrl ?? 'https://api.cultureconnect.app/v1/insurance',
        policiesEndpoint = policiesEndpoint ?? '/policies',
        providersEndpoint = providersEndpoint ?? '/providers',
        claimsEndpoint = claimsEndpoint ?? '/claims';

  /// Get all available insurance policies
  Future<List<InsurancePolicy>> getAvailablePolicies() async {
    try {
      final response = await _get(policiesEndpoint);
      final List<dynamic> data = response['policies'];
      return data.map((json) => InsurancePolicy.fromJson(json)).toList();
    } catch (e) {
      debugPrint('Error getting available policies: $e');
      rethrow;
    }
  }

  /// Get purchased insurance policies for the current user
  Future<List<InsurancePolicy>> getPurchasedPolicies() async {
    try {
      final response = await _get('$policiesEndpoint/purchased');
      final List<dynamic> data = response['policies'];
      return data.map((json) => InsurancePolicy.fromJson(json)).toList();
    } catch (e) {
      debugPrint('Error getting purchased policies: $e');
      rethrow;
    }
  }

  /// Get active insurance policies for the current user
  Future<List<InsurancePolicy>> getActivePolicies() async {
    try {
      final response = await _get('$policiesEndpoint/active');
      final List<dynamic> data = response['policies'];
      return data.map((json) => InsurancePolicy.fromJson(json)).toList();
    } catch (e) {
      debugPrint('Error getting active policies: $e');
      rethrow;
    }
  }

  /// Get an insurance policy by ID
  Future<InsurancePolicy?> getPolicyById(String policyId) async {
    try {
      final response = await _get('$policiesEndpoint/$policyId');
      return InsurancePolicy.fromJson(response['policy']);
    } catch (e) {
      if (e is ApiException && e.statusCode == 404) {
        return null;
      }
      debugPrint('Error getting policy by ID: $e');
      rethrow;
    }
  }

  /// Get all insurance providers
  Future<List<InsuranceProvider>> getInsuranceProviders() async {
    try {
      final response = await _get(providersEndpoint);
      final List<dynamic> data = response['providers'];
      return data.map((json) => InsuranceProvider.fromJson(json)).toList();
    } catch (e) {
      debugPrint('Error getting insurance providers: $e');
      rethrow;
    }
  }

  /// Get an insurance provider by ID
  Future<InsuranceProvider?> getProviderById(String providerId) async {
    try {
      final response = await _get('$providersEndpoint/$providerId');
      return InsuranceProvider.fromJson(response['provider']);
    } catch (e) {
      if (e is ApiException && e.statusCode == 404) {
        return null;
      }
      debugPrint('Error getting provider by ID: $e');
      rethrow;
    }
  }

  /// Get featured insurance providers
  Future<List<InsuranceProvider>> getFeaturedProviders() async {
    try {
      final response = await _get('$providersEndpoint/featured');
      final List<dynamic> data = response['providers'];
      return data.map((json) => InsuranceProvider.fromJson(json)).toList();
    } catch (e) {
      debugPrint('Error getting featured providers: $e');
      rethrow;
    }
  }

  /// Get partner insurance providers
  Future<List<InsuranceProvider>> getPartnerProviders() async {
    try {
      final response = await _get('$providersEndpoint/partners');
      final List<dynamic> data = response['providers'];
      return data.map((json) => InsuranceProvider.fromJson(json)).toList();
    } catch (e) {
      debugPrint('Error getting partner providers: $e');
      rethrow;
    }
  }

  /// Get all insurance claims for the current user
  Future<List<InsuranceClaim>> getClaims() async {
    try {
      final response = await _get(claimsEndpoint);
      final List<dynamic> data = response['claims'];
      return data.map((json) => InsuranceClaim.fromJson(json)).toList();
    } catch (e) {
      debugPrint('Error getting claims: $e');
      rethrow;
    }
  }

  /// Get an insurance claim by ID
  Future<InsuranceClaim?> getClaimById(String claimId) async {
    try {
      final response = await _get('$claimsEndpoint/$claimId');
      return InsuranceClaim.fromJson(response['claim']);
    } catch (e) {
      if (e is ApiException && e.statusCode == 404) {
        return null;
      }
      debugPrint('Error getting claim by ID: $e');
      rethrow;
    }
  }

  /// Get insurance claims by policy ID
  Future<List<InsuranceClaim>> getClaimsByPolicyId(String policyId) async {
    try {
      final response = await _get('$policiesEndpoint/$policyId/claims');
      final List<dynamic> data = response['claims'];
      return data.map((json) => InsuranceClaim.fromJson(json)).toList();
    } catch (e) {
      debugPrint('Error getting claims by policy ID: $e');
      rethrow;
    }
  }

  /// Get active insurance claims
  Future<List<InsuranceClaim>> getActiveClaims() async {
    try {
      final response = await _get('$claimsEndpoint/active');
      final List<dynamic> data = response['claims'];
      return data.map((json) => InsuranceClaim.fromJson(json)).toList();
    } catch (e) {
      debugPrint('Error getting active claims: $e');
      rethrow;
    }
  }

  /// Purchase an insurance policy
  Future<InsurancePolicy> purchasePolicy({
    required String policyId,
    required DateTime startDate,
    required DateTime endDate,
    required List<String> destinationCountries,
    required int travelerCount,
    required PaymentMethodModel paymentMethod,
    required List<Map<String, String>> travelers,
  }) async {
    try {
      final requestBody = {
        'policyId': policyId,
        'startDate': startDate.toIso8601String(),
        'endDate': endDate.toIso8601String(),
        'destinationCountries': destinationCountries,
        'travelerCount': travelerCount,
        'paymentMethodId': paymentMethod.id,
        'travelers': travelers,
      };

      final response = await _post('$policiesEndpoint/purchase', requestBody);
      return InsurancePolicy.fromJson(response['policy']);
    } catch (e) {
      debugPrint('Error purchasing policy: $e');
      rethrow;
    }
  }

  /// Cancel an insurance policy
  Future<InsurancePolicy> cancelPolicy(String policyId) async {
    try {
      final response = await _post('$policiesEndpoint/$policyId/cancel', {});
      return InsurancePolicy.fromJson(response['policy']);
    } catch (e) {
      debugPrint('Error cancelling policy: $e');
      rethrow;
    }
  }

  /// File an insurance claim
  Future<InsuranceClaim> fileClaim({
    required String policyId,
    required InsuranceCoverageType coverageType,
    required DateTime incidentDate,
    required String incidentDescription,
    required String incidentLocation,
    required double claimAmount,
    required String currency,
    required List<File> documents,
  }) async {
    try {
      // First, upload documents
      final documentUrls = await _uploadClaimDocuments(documents);

      // Then, file the claim
      final requestBody = {
        'policyId': policyId,
        'coverageType': coverageType.toString().split('.').last,
        'incidentDate': incidentDate.toIso8601String(),
        'incidentDescription': incidentDescription,
        'incidentLocation': incidentLocation,
        'claimAmount': claimAmount,
        'currency': currency,
        'documentUrls': documentUrls,
      };

      final response = await _post(claimsEndpoint, requestBody);
      return InsuranceClaim.fromJson(response['claim']);
    } catch (e) {
      debugPrint('Error filing claim: $e');
      rethrow;
    }
  }

  /// Update an insurance claim
  Future<InsuranceClaim> updateClaim({
    required String claimId,
    InsuranceClaimStatus? status,
    String? incidentDescription,
    double? claimAmount,
    List<File>? additionalDocuments,
    String? additionalInfoRequested,
  }) async {
    try {
      // First, upload additional documents if any
      List<String>? additionalDocumentUrls;
      if (additionalDocuments != null && additionalDocuments.isNotEmpty) {
        additionalDocumentUrls =
            await _uploadClaimDocuments(additionalDocuments);
      }

      // Then, update the claim
      final requestBody = <String, dynamic>{};

      if (status != null) {
        requestBody['status'] = status.toString().split('.').last;
      }

      if (incidentDescription != null) {
        requestBody['incidentDescription'] = incidentDescription;
      }

      if (claimAmount != null) {
        requestBody['claimAmount'] = claimAmount;
      }

      if (additionalDocumentUrls != null) {
        requestBody['additionalDocumentUrls'] = additionalDocumentUrls;
      }

      if (additionalInfoRequested != null) {
        requestBody['additionalInfoRequested'] = additionalInfoRequested;
      }

      final response = await _patch('$claimsEndpoint/$claimId', requestBody);
      return InsuranceClaim.fromJson(response['claim']);
    } catch (e) {
      debugPrint('Error updating claim: $e');
      rethrow;
    }
  }

  /// Get recommended policies based on destination and trip details
  Future<List<InsurancePolicy>> getRecommendedPolicies({
    required List<String> destinationCountries,
    required DateTime startDate,
    required DateTime endDate,
    required int travelerCount,
    List<InsuranceCoverageType>? desiredCoverageTypes,
  }) async {
    try {
      final queryParams = {
        'destinationCountries': destinationCountries.join(','),
        'startDate': startDate.toIso8601String(),
        'endDate': endDate.toIso8601String(),
        'travelerCount': travelerCount.toString(),
      };

      if (desiredCoverageTypes != null && desiredCoverageTypes.isNotEmpty) {
        queryParams['coverageTypes'] = desiredCoverageTypes
            .map((type) => type.toString().split('.').last)
            .join(',');
      }

      final response = await _get(
        '$policiesEndpoint/recommended',
        queryParams: queryParams,
      );

      final List<dynamic> data = response['policies'];
      return data.map((json) => InsurancePolicy.fromJson(json)).toList();
    } catch (e) {
      debugPrint('Error getting recommended policies: $e');
      rethrow;
    }
  }

  /// Compare insurance policies
  Future<Map<String, List<dynamic>>> comparePolicies(
      List<String> policyIds) async {
    try {
      final queryParams = {
        'policyIds': policyIds.join(','),
      };

      final response = await _get(
        '$policiesEndpoint/compare',
        queryParams: queryParams,
      );

      return Map<String, List<dynamic>>.from(response['comparison']);
    } catch (e) {
      debugPrint('Error comparing policies: $e');
      rethrow;
    }
  }

  /// Upload claim documents
  Future<List<String>> _uploadClaimDocuments(List<File> documents) async {
    try {
      final documentUrls = <String>[];

      for (final document in documents) {
        // Create multipart request
        final request = http.MultipartRequest(
          'POST',
          Uri.parse('$baseUrl$claimsEndpoint/documents'),
        );

        // Add authorization header
        request.headers.addAll(_getHeaders());

        // Add file
        request.files.add(await http.MultipartFile.fromPath(
          'document',
          document.path,
        ));

        // Send request
        final streamedResponse = await httpClient.send(request);
        final response = await http.Response.fromStream(streamedResponse);

        // Parse response
        final responseData = json.decode(response.body);

        if (response.statusCode == 200 || response.statusCode == 201) {
          documentUrls.add(responseData['documentUrl']);
        } else {
          throw ApiException(
            message: responseData['message'] ?? 'Failed to upload document',
            statusCode: response.statusCode,
          );
        }
      }

      return documentUrls;
    } catch (e) {
      debugPrint('Error uploading claim documents: $e');
      rethrow;
    }
  }

  /// Make a GET request to the API
  Future<Map<String, dynamic>> _get(String endpoint,
      {Map<String, String>? queryParams}) async {
    final uri = Uri.parse('$baseUrl$endpoint').replace(
      queryParameters: queryParams,
    );

    final response = await httpClient.get(
      uri,
      headers: _getHeaders(),
    );

    return _handleResponse(response);
  }

  /// Make a POST request to the API
  Future<Map<String, dynamic>> _post(
      String endpoint, Map<String, dynamic> body) async {
    final uri = Uri.parse('$baseUrl$endpoint');

    final response = await httpClient.post(
      uri,
      headers: _getHeaders(),
      body: json.encode(body),
    );

    return _handleResponse(response);
  }

  /// Make a PATCH request to the API
  Future<Map<String, dynamic>> _patch(
      String endpoint, Map<String, dynamic> body) async {
    final uri = Uri.parse('$baseUrl$endpoint');

    final response = await httpClient.patch(
      uri,
      headers: _getHeaders(),
      body: json.encode(body),
    );

    return _handleResponse(response);
  }

  /// Handle the API response
  Map<String, dynamic> _handleResponse(http.Response response) {
    final responseData = json.decode(response.body);

    if (response.statusCode >= 200 && response.statusCode < 300) {
      return responseData;
    } else {
      throw ApiException(
        message: responseData['message'] ?? 'API request failed',
        statusCode: response.statusCode,
        data: responseData,
      );
    }
  }

  /// Get the headers for API requests
  Map<String, String> _getHeaders() {
    return {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      // Add authorization headers here if needed
    };
  }
}
