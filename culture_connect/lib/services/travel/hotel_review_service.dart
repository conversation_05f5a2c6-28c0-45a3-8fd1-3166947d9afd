import 'dart:io';
import 'dart:async';
import 'package:uuid/uuid.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:culture_connect/models/travel/hotel_review.dart';
import 'package:culture_connect/services/logging_service.dart';
import 'package:culture_connect/services/storage_service.dart';

/// Service for managing hotel reviews
class HotelReviewService {
  final FirebaseFirestore _firestore;
  final StorageService _storageService;
  final LoggingService _loggingService;
  final Uuid _uuid;

  // In-memory reviews for mock data
  final List<HotelReview> _reviews = [];

  /// Creates a new hotel review service
  HotelReviewService({
    required FirebaseFirestore firestore,
    required StorageService storageService,
    required LoggingService loggingService,
  })  : _firestore = firestore,
        _storageService = storageService,
        _loggingService = loggingService,
        _uuid = const Uuid() {
    _generateMockReviews();
  }

  // This would be used with shared preferences in a real implementation
  // static const String _cacheKeyPrefix = 'hotel_reviews_';

  /// Get reviews for a hotel with pagination support
  ///
  /// [hotelId] - The ID of the hotel to get reviews for
  /// [limit] - The maximum number of reviews to return (default: 10)
  /// [offset] - The number of reviews to skip (default: 0)
  /// [sortBy] - The field to sort by (default: 'datePosted')
  /// [sortOrder] - The sort order (default: 'desc')
  /// [forceRefresh] - Whether to force a refresh from the server (default: false)
  Future<List<HotelReview>> getReviewsForHotel(
    String hotelId, {
    int limit = 10,
    int offset = 0,
    String sortBy = 'datePosted',
    String sortOrder = 'desc',
    bool forceRefresh = false,
  }) async {
    try {
      _loggingService.debug(
        'HotelReviewService',
        'Getting reviews for hotel: $hotelId (limit: $limit, offset: $offset, sortBy: $sortBy, sortOrder: $sortOrder, forceRefresh: $forceRefresh)',
      );

      // Cache key is used in the implementation with shared preferences
      // For now, we're using in-memory cache

      // Try to get reviews from cache if not forcing refresh
      if (!forceRefresh) {
        final cachedReviews = _getCachedReviews(hotelId);
        if (cachedReviews.isNotEmpty) {
          _loggingService.debug(
            'HotelReviewService',
            'Using cached reviews for hotel: $hotelId (${cachedReviews.length} reviews)',
          );

          // Apply pagination and sorting to cached reviews
          return _applyPaginationAndSorting(
            cachedReviews,
            limit: limit,
            offset: offset,
            sortBy: sortBy,
            sortOrder: sortOrder,
          );
        }
      }

      try {
        // Try to get reviews from Firestore
        var query = _firestore
            .collection('hotel_reviews')
            .where('hotelId', isEqualTo: hotelId)
            .where('isPublished', isEqualTo: true);

        // Apply sorting
        if (sortOrder.toLowerCase() == 'asc') {
          query = query.orderBy(sortBy, descending: false);
        } else {
          query = query.orderBy(sortBy, descending: true);
        }

        // Apply pagination - Firestore doesn't have a direct offset method
        // We'll use limit and startAfter for pagination
        // For simplicity, we'll just use limit here
        query = query.limit(limit);

        // In a real implementation, we would use startAfter with a document snapshot
        // This would require keeping track of the last document from previous queries

        // Execute query
        final querySnapshot = await query.get();

        if (querySnapshot.docs.isNotEmpty) {
          // Convert documents to reviews
          final reviews = querySnapshot.docs
              .map((doc) => HotelReview.fromJson({...doc.data(), 'id': doc.id}))
              .toList();

          // Cache reviews
          _cacheReviews(hotelId, reviews);

          return reviews;
        }
      } catch (firestoreError, firestoreStackTrace) {
        _loggingService.warning(
          'HotelReviewService',
          'Error getting reviews from Firestore, falling back to in-memory reviews: $firestoreError',
          firestoreStackTrace,
        );
      }

      // Fallback to in-memory reviews for mock data
      await Future.delayed(const Duration(milliseconds: 500));
      final mockReviews = _reviews
          .where((review) => review.hotelId == hotelId && review.isPublished)
          .toList();

      // Apply pagination and sorting to mock reviews
      return _applyPaginationAndSorting(
        mockReviews,
        limit: limit,
        offset: offset,
        sortBy: sortBy,
        sortOrder: sortOrder,
      );
    } catch (e, stackTrace) {
      _loggingService.error(
        'HotelReviewService',
        'Error getting reviews for hotel: $hotelId',
        e,
        stackTrace,
      );
      rethrow;
    }
  }

  /// Apply pagination and sorting to a list of reviews
  List<HotelReview> _applyPaginationAndSorting(
    List<HotelReview> reviews, {
    required int limit,
    required int offset,
    required String sortBy,
    required String sortOrder,
  }) {
    // Sort reviews
    reviews.sort((a, b) {
      dynamic valueA;
      dynamic valueB;

      // Get values to compare
      switch (sortBy) {
        case 'datePosted':
          valueA = a.datePosted;
          valueB = b.datePosted;
          break;
        case 'overallRating':
          valueA = a.overallRating;
          valueB = b.overallRating;
          break;
        case 'helpfulCount':
          valueA = a.helpfulCount;
          valueB = b.helpfulCount;
          break;
        default:
          valueA = a.datePosted;
          valueB = b.datePosted;
      }

      // Compare values
      int comparison;
      if (valueA is DateTime && valueB is DateTime) {
        comparison = valueA.compareTo(valueB);
      } else if (valueA is num && valueB is num) {
        comparison = valueA.compareTo(valueB);
      } else if (valueA is String && valueB is String) {
        comparison = valueA.compareTo(valueB);
      } else {
        comparison = 0;
      }

      // Apply sort order
      return sortOrder.toLowerCase() == 'asc' ? comparison : -comparison;
    });

    // Apply pagination
    if (offset >= reviews.length) {
      return [];
    }

    final end = offset + limit;
    return reviews.sublist(offset, end > reviews.length ? reviews.length : end);
  }

  /// Get cached reviews for a hotel
  List<HotelReview> _getCachedReviews(String hotelId) {
    // In a real implementation, this would retrieve reviews from a local database or shared preferences
    // For now, we'll just return the in-memory reviews
    return _reviews
        .where((review) => review.hotelId == hotelId && review.isPublished)
        .toList();
  }

  /// Cache reviews for a hotel
  void _cacheReviews(String hotelId, List<HotelReview> reviews) {
    // In a real implementation, this would store reviews in a local database or shared preferences
    // For now, we'll just update the in-memory reviews

    // Remove existing reviews for this hotel
    _reviews.removeWhere((review) => review.hotelId == hotelId);

    // Add new reviews
    _reviews.addAll(reviews);
  }

  /// Get a review by ID
  Future<HotelReview?> getReviewById(String reviewId) async {
    try {
      _loggingService.debug(
        'HotelReviewService',
        'Getting review: $reviewId',
      );

      // Try to get review from Firestore
      final docSnapshot =
          await _firestore.collection('hotel_reviews').doc(reviewId).get();

      if (docSnapshot.exists) {
        return HotelReview.fromJson({...docSnapshot.data()!, 'id': reviewId});
      } else {
        // Fallback to in-memory reviews for mock data
        await Future.delayed(const Duration(milliseconds: 500));
        try {
          return _reviews.firstWhere((review) => review.id == reviewId);
        } catch (e) {
          return null;
        }
      }
    } catch (e, stackTrace) {
      _loggingService.error(
        'HotelReviewService',
        'Error getting review: $reviewId',
        e,
        stackTrace,
      );
      rethrow;
    }
  }

  /// Get reviews by user
  Future<List<HotelReview>> getReviewsByUser(String userId) async {
    try {
      _loggingService.debug(
        'HotelReviewService',
        'Getting reviews by user: $userId',
      );

      // Try to get reviews from Firestore
      final querySnapshot = await _firestore
          .collection('hotel_reviews')
          .where('userId', isEqualTo: userId)
          .get();

      if (querySnapshot.docs.isNotEmpty) {
        return querySnapshot.docs
            .map((doc) => HotelReview.fromJson({...doc.data(), 'id': doc.id}))
            .toList();
      } else {
        // Fallback to in-memory reviews for mock data
        await Future.delayed(const Duration(milliseconds: 500));
        return _reviews.where((review) => review.userId == userId).toList();
      }
    } catch (e, stackTrace) {
      _loggingService.error(
        'HotelReviewService',
        'Error getting reviews by user: $userId',
        e,
        stackTrace,
      );
      rethrow;
    }
  }

  /// Get review statistics for a hotel
  Future<Map<String, dynamic>> getReviewStats(String hotelId) async {
    try {
      _loggingService.debug(
        'HotelReviewService',
        'Getting review stats for hotel: $hotelId',
      );

      final reviews = await getReviewsForHotel(hotelId);

      if (reviews.isEmpty) {
        return {
          'averageRating': 0.0,
          'totalReviews': 0,
          'ratingDistribution': {
            '5': 0,
            '4': 0,
            '3': 0,
            '2': 0,
            '1': 0,
          },
          'categoryRatings': {},
        };
      }

      // Calculate average rating
      final totalRating = reviews.fold<double>(
        0,
        (accumulator, review) => accumulator + review.overallRating,
      );
      final averageRating = totalRating / reviews.length;

      // Calculate rating distribution
      final ratingDistribution = <String, int>{
        '5': 0,
        '4': 0,
        '3': 0,
        '2': 0,
        '1': 0,
      };

      for (final review in reviews) {
        final rating = review.overallRating.round().toString();
        if (ratingDistribution.containsKey(rating)) {
          ratingDistribution[rating] = (ratingDistribution[rating] ?? 0) + 1;
        }
      }

      // Calculate category ratings
      final categoryRatings = <String, double>{};
      final categoryCounts = <String, int>{};

      for (final review in reviews) {
        for (final entry in review.categoryRatings.entries) {
          final category = HotelReview.categoryToString(entry.key);
          final rating = entry.value;

          categoryRatings[category] = (categoryRatings[category] ?? 0) + rating;
          categoryCounts[category] = (categoryCounts[category] ?? 0) + 1;
        }
      }

      // Calculate average category ratings
      final averageCategoryRatings = <String, double>{};

      for (final category in categoryRatings.keys) {
        final count = categoryCounts[category] ?? 0;
        if (count > 0) {
          averageCategoryRatings[category] = categoryRatings[category]! / count;
        }
      }

      return {
        'averageRating': averageRating,
        'totalReviews': reviews.length,
        'ratingDistribution': ratingDistribution,
        'categoryRatings': averageCategoryRatings,
      };
    } catch (e, stackTrace) {
      _loggingService.error(
        'HotelReviewService',
        'Error getting review stats for hotel: $hotelId',
        e,
        stackTrace,
      );
      rethrow;
    }
  }

  /// Create a new review
  ///
  /// [hotelId] - The ID of the hotel to create a review for
  /// [bookingId] - The ID of the booking associated with this review
  /// [userId] - The ID of the user creating the review
  /// [userName] - The name of the user creating the review
  /// [userProfileImageUrl] - The URL of the user's profile image (optional)
  /// [overallRating] - The overall rating (1-5)
  /// [categoryRatings] - Ratings for specific categories
  /// [content] - The content of the review
  /// [photos] - Photos to upload with the review (optional)
  /// [tags] - Tags for the review (optional)
  /// [tripType] - The type of trip (e.g., business, leisure)
  /// [roomType] - The type of room stayed in
  /// [stayDuration] - The duration of the stay in nights
  /// [onProgress] - Callback for upload progress (optional)
  Future<HotelReview> createReview({
    required String hotelId,
    required String bookingId,
    required String userId,
    required String userName,
    String? userProfileImageUrl,
    required double overallRating,
    required Map<HotelReviewCategory, double> categoryRatings,
    required String content,
    List<File> photos = const [],
    List<String> tags = const [],
    String? tripType,
    String? roomType,
    int? stayDuration,
    Function(double progress, String message)? onProgress,
  }) async {
    try {
      _loggingService.debug(
        'HotelReviewService',
        'Creating review for hotel: $hotelId by user: $userId',
      );

      // Update progress
      onProgress?.call(0.1, 'Checking previous reviews...');

      // Check if user has already reviewed this hotel
      final existingReviews = await getReviewsByUser(userId);
      final hasReviewed = existingReviews.any((r) => r.hotelId == hotelId);

      if (hasReviewed) {
        onProgress?.call(-1.0, 'You have already reviewed this hotel');
        throw Exception('You have already reviewed this hotel');
      }

      // Update progress
      onProgress?.call(0.2, 'Preparing to upload photos...');

      // Upload photos if any
      List<String> photoUrls = [];

      if (photos.isNotEmpty) {
        // Calculate total bytes for progress tracking
        int totalBytes = 0;
        for (final photo in photos) {
          totalBytes += await photo.length();
        }

        int uploadedBytes = 0;
        int successfulUploads = 0;

        for (int i = 0; i < photos.length; i++) {
          final photo = photos[i];

          try {
            // Update progress for this photo
            onProgress?.call(
              0.2 + (0.6 * (uploadedBytes / totalBytes)),
              'Uploading photo ${i + 1}/${photos.length}...',
            );

            final timestamp = DateTime.now().millisecondsSinceEpoch;
            final fileName = photo.path.split('/').last;
            final path =
                'hotel_reviews/$hotelId/$userId/${timestamp}_$fileName';

            final url = await _storageService.uploadFile(
              file: photo,
              path: path,
              onProgress: (bytesTransferred, totalBytesForFile) {
                // Calculate overall progress
                final currentProgress = 0.2 +
                    (0.6 * ((uploadedBytes + bytesTransferred) / totalBytes));
                onProgress?.call(
                  currentProgress,
                  'Uploading photo ${i + 1}/${photos.length}: ${((bytesTransferred / totalBytesForFile) * 100).toStringAsFixed(0)}%',
                );
              },
            );

            if (url != null) {
              photoUrls.add(url);
              successfulUploads++;
            }

            // Update uploaded bytes
            uploadedBytes += await photo.length();
          } catch (photoError) {
            _loggingService.warning(
              'HotelReviewService',
              'Error uploading photo ${i + 1}/${photos.length}: $photoError',
            );

            // Notify about the error but continue
            onProgress?.call(
              0.2 + (0.6 * (uploadedBytes / totalBytes)),
              'Failed to upload photo ${i + 1}. Continuing with other photos...',
            );

            // Continue with the next photo
            continue;
          }
        }

        // Notify about upload completion
        if (successfulUploads > 0) {
          onProgress?.call(
              0.8, 'Successfully uploaded $successfulUploads photos');
        } else if (photos.isNotEmpty) {
          onProgress?.call(0.8, 'Failed to upload any photos');
        }
      } else {
        // Skip to 80% if no photos to upload
        onProgress?.call(0.8, 'No photos to upload');
      }

      // Update progress
      onProgress?.call(0.9, 'Creating review...');

      // Create review document
      final reviewId = _uuid.v4();
      final now = DateTime.now();
      final review = HotelReview(
        id: reviewId,
        hotelId: hotelId,
        bookingId: bookingId,
        userId: userId,
        userName: userName,
        userProfileImageUrl: userProfileImageUrl,
        overallRating: overallRating,
        categoryRatings: categoryRatings,
        content: content,
        datePosted: now,
        photoUrls: photoUrls,
        isVerified: true,
        tags: tags,
        tripType: tripType,
        roomType: roomType,
        stayDuration: stayDuration,
      );

      // Save to Firestore
      try {
        await _firestore
            .collection('hotel_reviews')
            .doc(reviewId)
            .set(review.toJson());

        // For mock data, add to in-memory reviews
        _reviews.add(review);

        // Update progress
        onProgress?.call(1.0, 'Review submitted successfully!');
      } catch (firestoreError) {
        _loggingService.error(
          'HotelReviewService',
          'Error saving review to Firestore: $firestoreError',
        );

        // Try to save to in-memory storage as fallback
        _reviews.add(review);

        // Update progress with warning
        onProgress?.call(0.95,
            'Review saved locally. Will sync when connection is restored.');
      }

      return review;
    } catch (e, stackTrace) {
      _loggingService.error(
        'HotelReviewService',
        'Error creating review',
        e,
        stackTrace,
      );

      // Update progress with error
      onProgress?.call(-1.0, 'Error creating review: ${e.toString()}');

      rethrow;
    }
  }

  /// Update an existing review
  Future<HotelReview> updateReview({
    required String reviewId,
    double? overallRating,
    Map<HotelReviewCategory, double>? categoryRatings,
    String? content,
    List<File>? newPhotos,
    List<String>? photoUrlsToKeep,
    List<String>? tags,
  }) async {
    try {
      _loggingService.debug(
        'HotelReviewService',
        'Updating review: $reviewId',
      );

      // Get the existing review
      final existingReview = await getReviewById(reviewId);

      if (existingReview == null) {
        throw Exception('Review not found');
      }

      // Process photos
      List<String> updatedPhotoUrls =
          photoUrlsToKeep ?? existingReview.photoUrls;

      if (newPhotos != null && newPhotos.isNotEmpty) {
        for (final photo in newPhotos) {
          final url = await _storageService.uploadFile(
            file: photo,
            path:
                'hotel_reviews/${existingReview.hotelId}/${existingReview.userId}/${DateTime.now().millisecondsSinceEpoch}',
          );

          if (url != null) {
            updatedPhotoUrls.add(url);
          }
        }
      }

      // Create updated review
      final updatedReview = existingReview.copyWith(
        overallRating: overallRating,
        categoryRatings: categoryRatings,
        content: content,
        photoUrls: updatedPhotoUrls,
        dateUpdated: DateTime.now(),
        tags: tags,
      );

      // Update in Firestore
      await _firestore
          .collection('hotel_reviews')
          .doc(reviewId)
          .update(updatedReview.toJson());

      // Update in-memory reviews for mock data
      final index = _reviews.indexWhere((r) => r.id == reviewId);
      if (index >= 0) {
        _reviews[index] = updatedReview;
      }

      return updatedReview;
    } catch (e, stackTrace) {
      _loggingService.error(
        'HotelReviewService',
        'Error updating review: $reviewId',
        e,
        stackTrace,
      );
      rethrow;
    }
  }

  /// Delete a review
  Future<void> deleteReview(String reviewId) async {
    try {
      _loggingService.debug(
        'HotelReviewService',
        'Deleting review: $reviewId',
      );

      // Delete from Firestore
      await _firestore.collection('hotel_reviews').doc(reviewId).delete();

      // Delete from in-memory reviews for mock data
      _reviews.removeWhere((r) => r.id == reviewId);
    } catch (e, stackTrace) {
      _loggingService.error(
        'HotelReviewService',
        'Error deleting review: $reviewId',
        e,
        stackTrace,
      );
      rethrow;
    }
  }

  /// Mark a review as helpful
  Future<void> markReviewAsHelpful(String reviewId, String userId) async {
    try {
      _loggingService.debug(
        'HotelReviewService',
        'Marking review $reviewId as helpful by user: $userId',
      );

      // Get the existing review
      final existingReview = await getReviewById(reviewId);

      if (existingReview == null) {
        throw Exception('Review not found');
      }

      // Check if user has already marked this review as helpful
      if (existingReview.helpfulUserIds.contains(userId)) {
        throw Exception('You have already marked this review as helpful');
      }

      // Update helpful count and user IDs
      final updatedHelpfulUserIds = [...existingReview.helpfulUserIds, userId];
      final updatedHelpfulCount = existingReview.helpfulCount + 1;

      // Update in Firestore
      await _firestore.collection('hotel_reviews').doc(reviewId).update({
        'helpfulCount': updatedHelpfulCount,
        'helpfulUserIds': updatedHelpfulUserIds,
      });

      // Update in-memory reviews for mock data
      final index = _reviews.indexWhere((r) => r.id == reviewId);
      if (index >= 0) {
        _reviews[index] = existingReview.copyWith(
          helpfulCount: updatedHelpfulCount,
          helpfulUserIds: updatedHelpfulUserIds,
        );
      }
    } catch (e, stackTrace) {
      _loggingService.error(
        'HotelReviewService',
        'Error marking review as helpful',
        e,
        stackTrace,
      );
      rethrow;
    }
  }

  /// Generate mock reviews for testing
  void _generateMockReviews() {
    // Mock reviews for hotel1
    _reviews.add(
      HotelReview(
        id: 'review1',
        hotelId: 'hotel1',
        bookingId: 'booking1',
        userId: 'user1',
        userName: 'John Smith',
        userProfileImageUrl: 'https://example.com/users/john.jpg',
        overallRating: 4.5,
        categoryRatings: {
          HotelReviewCategory.cleanliness: 5.0,
          HotelReviewCategory.service: 4.5,
          HotelReviewCategory.location: 4.0,
          HotelReviewCategory.value: 4.0,
          HotelReviewCategory.comfort: 4.5,
          HotelReviewCategory.facilities: 4.0,
        },
        content:
            'Excellent hotel with great amenities. The staff was very friendly and helpful. The room was clean and comfortable. The location is perfect for exploring the city.',
        datePosted: DateTime.now().subtract(const Duration(days: 30)),
        helpfulCount: 12,
        helpfulUserIds: ['user2', 'user3', 'user4'],
        photoUrls: [
          'https://example.com/reviews/hotel1/photo1.jpg',
          'https://example.com/reviews/hotel1/photo2.jpg',
        ],
        isVerified: true,
        tags: ['Clean', 'Friendly Staff', 'Great Location'],
        tripType: 'Business',
        roomType: 'Deluxe Room',
        stayDuration: 3,
      ),
    );

    _reviews.add(
      HotelReview(
        id: 'review2',
        hotelId: 'hotel1',
        bookingId: 'booking2',
        userId: 'user2',
        userName: 'Jane Doe',
        userProfileImageUrl: 'https://example.com/users/jane.jpg',
        overallRating: 5.0,
        categoryRatings: {
          HotelReviewCategory.cleanliness: 5.0,
          HotelReviewCategory.service: 5.0,
          HotelReviewCategory.location: 5.0,
          HotelReviewCategory.value: 4.5,
          HotelReviewCategory.comfort: 5.0,
          HotelReviewCategory.facilities: 5.0,
        },
        content:
            'Absolutely amazing stay! The hotel exceeded all my expectations. The room was spacious and luxurious, the staff was attentive, and the facilities were top-notch. I especially loved the pool and spa area. Will definitely stay here again!',
        datePosted: DateTime.now().subtract(const Duration(days: 15)),
        helpfulCount: 8,
        helpfulUserIds: ['user1', 'user3'],
        photoUrls: [
          'https://example.com/reviews/hotel1/photo3.jpg',
          'https://example.com/reviews/hotel1/photo4.jpg',
          'https://example.com/reviews/hotel1/photo5.jpg',
        ],
        isVerified: true,
        tags: ['Luxury', 'Spa', 'Pool', 'Friendly Staff'],
        tripType: 'Couple',
        roomType: 'Suite',
        stayDuration: 5,
        hotelResponse: HotelResponse(
          staffId: 'staff1',
          staffName: 'Michael Johnson',
          staffTitle: 'Guest Relations Manager',
          content:
              'Thank you for your wonderful review, Jane! We are delighted to hear that you enjoyed your stay with us. We look forward to welcoming you back soon!',
          datePosted: DateTime.now().subtract(const Duration(days: 14)),
        ),
      ),
    );

    // Add more mock reviews as needed
  }
}
