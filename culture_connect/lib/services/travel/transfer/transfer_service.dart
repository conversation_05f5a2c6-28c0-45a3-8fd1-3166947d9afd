import 'dart:convert';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import 'package:hive/hive.dart';
import 'package:uuid/uuid.dart';

import 'package:culture_connect/models/location/geo_location.dart';
import 'package:culture_connect/models/travel/transfer/transfers.dart';
import 'package:culture_connect/models/travel/transfer/transfer_service.dart'
    as models;

import 'package:culture_connect/services/logging_service.dart';

/// Service for managing airport transfers
class TransferServiceManager {
  // Singleton instance
  static final TransferServiceManager _instance =
      TransferServiceManager._internal();
  factory TransferServiceManager() => _instance;
  TransferServiceManager._internal() {
    _initHiveBoxes();
  }

  // Firebase instances
  final FirebaseFirestore? _firestore =
      kIsWeb ? null : FirebaseFirestore.instance;

  // Hive boxes for caching
  Box<String>? _transfersBox;
  Box<String>? _bookingsBox;

  // Services
  final LoggingService _loggingService = LoggingService();

  // In-memory storage
  final List<models.TransferService> _transfers = [];
  final List<TransferBooking> _bookings = [];

  /// Initialize Hive boxes
  void _initHiveBoxes() {
    try {
      if (Hive.isBoxOpen('transfers')) {
        _transfersBox = Hive.box<String>('transfers');
      }

      if (Hive.isBoxOpen('transfer_bookings')) {
        _bookingsBox = Hive.box<String>('transfer_bookings');
      }
    } catch (e, stackTrace) {
      _loggingService.error(
        'TransferServiceManager',
        'Error initializing Hive boxes',
        e,
        stackTrace,
      );
    }
  }

  /// Initialize the service
  Future<void> initialize() async {
    if (_transfers.isNotEmpty) return;

    try {
      // Try to get data from Hive cache
      if (_transfersBox != null) {
        final cachedTransfers = _transfersBox!.values.toList();
        if (cachedTransfers.isNotEmpty) {
          _transfers.clear();
          for (final jsonStr in cachedTransfers) {
            _transfers
                .add(models.TransferService.fromJson(jsonDecode(jsonStr)));
          }
        }
      }

      if (_bookingsBox != null) {
        final cachedBookings = _bookingsBox!.values.toList();
        if (cachedBookings.isNotEmpty) {
          _bookings.clear();
          for (final jsonStr in cachedBookings) {
            _bookings.add(TransferBooking.fromJson(jsonDecode(jsonStr)));
          }
        }
      }

      // If cache is not empty, return
      if (_transfers.isNotEmpty && _bookingsBox != null) {
        return;
      }

      // Try to get data from Firestore
      if (_firestore != null) {
        final transfersSnapshot =
            await _firestore!.collection('transfers').get();
        if (transfersSnapshot.docs.isNotEmpty) {
          _transfers.clear();
          for (final doc in transfersSnapshot.docs) {
            _transfers.add(
                models.TransferService.fromJson({...doc.data(), 'id': doc.id}));
          }

          // Cache the transfers
          if (_transfersBox != null) {
            for (final transfer in _transfers) {
              await _transfersBox!
                  .put(transfer.id, jsonEncode(transfer.toJson()));
            }
          }
        }

        final bookingsSnapshot =
            await _firestore!.collection('transfer_bookings').get();
        if (bookingsSnapshot.docs.isNotEmpty) {
          _bookings.clear();
          for (final doc in bookingsSnapshot.docs) {
            _bookings
                .add(TransferBooking.fromJson({...doc.data(), 'id': doc.id}));
          }

          // Cache the bookings
          if (_bookingsBox != null) {
            for (final booking in _bookings) {
              await _bookingsBox!.put(booking.id, jsonEncode(booking.toJson()));
            }
          }
        }

        // If Firestore data is not empty, return
        if (_transfers.isNotEmpty && _bookings.isNotEmpty) {
          return;
        }
      }

      // If no data is available, generate mock data
      await _generateMockData();
    } catch (e, stackTrace) {
      _loggingService.error(
        'TransferService',
        'Error initializing transfer service',
        e,
        stackTrace,
      );

      // If there's an error, generate mock data
      await _generateMockData();
    }
  }

  /// Get all available transfer services
  Future<List<models.TransferService>> getTransfers() async {
    await initialize();
    return _transfers.where((transfer) => transfer.isAvailable).toList();
  }

  /// Get a transfer service by ID
  Future<models.TransferService?> getTransfer(String id) async {
    await initialize();
    try {
      return _transfers.firstWhere((transfer) => transfer.id == id);
    } catch (e, stackTrace) {
      _loggingService.error(
        'TransferServiceManager',
        'Error getting transfer with ID: $id',
        e,
        stackTrace,
      );
      return null;
    }
  }

  /// Search for transfer services
  Future<List<models.TransferService>> searchTransfers({
    String? location,
    DateTime? date,
    int? passengerCount,
    int? luggageCount,
    TransferVehicleType? vehicleType,
    bool? isPrivate,
    double? maxPrice,
    String? sortBy,
    bool? sortAscending,
  }) async {
    await initialize();

    var results = _transfers.where((transfer) => transfer.isAvailable).toList();

    // Apply filters
    if (location != null) {
      results = results
          .where((transfer) =>
              transfer.location.toLowerCase().contains(location.toLowerCase()))
          .toList();
    }

    if (passengerCount != null) {
      results = results
          .where((transfer) => transfer.passengerCapacity >= passengerCount)
          .toList();
    }

    if (luggageCount != null) {
      results = results
          .where((transfer) => transfer.luggageCapacity >= luggageCount)
          .toList();
    }

    if (vehicleType != null) {
      results = results
          .where((transfer) => transfer.vehicleType == vehicleType)
          .toList();
    }

    if (isPrivate != null) {
      results =
          results.where((transfer) => transfer.isPrivate == isPrivate).toList();
    }

    if (maxPrice != null) {
      results =
          results.where((transfer) => transfer.price <= maxPrice).toList();
    }

    // Apply sorting
    if (sortBy != null) {
      switch (sortBy) {
        case 'price':
          results.sort((a, b) => a.price.compareTo(b.price));
          break;
        case 'rating':
          results.sort((a, b) => b.rating.compareTo(a.rating));
          break;
        case 'passengerCapacity':
          results.sort(
              (a, b) => b.passengerCapacity.compareTo(a.passengerCapacity));
          break;
        default:
          results.sort((a, b) => a.price.compareTo(b.price));
      }

      if (sortAscending == false) {
        results = results.reversed.toList();
      }
    }

    return results;
  }

  /// Get all bookings for a user
  Future<List<TransferBooking>> getBookings(String userId) async {
    await initialize();
    return _bookings.where((booking) => booking.userId == userId).toList();
  }

  /// Get a booking by ID
  Future<TransferBooking?> getBooking(String id) async {
    await initialize();
    try {
      return _bookings.firstWhere((booking) => booking.id == id);
    } catch (e) {
      return null;
    }
  }

  /// Create a new booking
  Future<TransferBooking> createBooking(TransferBooking booking) async {
    await initialize();

    // Generate a new booking with a unique ID
    final newBooking = TransferBooking(
      id: const Uuid().v4(),
      userId: booking.userId,
      transferId: booking.transferId,
      transferService: booking.transferService,
      pickupLocation: booking.pickupLocation,
      dropoffLocation: booking.dropoffLocation,
      pickupDateTime: booking.pickupDateTime,
      passengerCount: booking.passengerCount,
      luggageCount: booking.luggageCount,
      specialRequests: booking.specialRequests,
      contactName: booking.contactName,
      contactPhone: booking.contactPhone,
      contactEmail: booking.contactEmail,
      flightInfo: booking.flightInfo,
      status: TransferBookingStatus.pending,
      totalPrice: booking.totalPrice,
      currency: booking.currency,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );

    _bookings.add(newBooking);

    // Save to Firestore
    if (_firestore != null) {
      try {
        await _firestore!
            .collection('transfer_bookings')
            .doc(newBooking.id)
            .set(newBooking.toJson());
      } catch (e, stackTrace) {
        _loggingService.error(
          'TransferService',
          'Error adding booking to Firestore',
          e,
          stackTrace,
        );
      }
    }

    // Cache the booking
    if (_bookingsBox != null) {
      try {
        await _bookingsBox!.put(newBooking.id, jsonEncode(newBooking.toJson()));
      } catch (e, stackTrace) {
        _loggingService.error(
          'TransferService',
          'Error caching booking',
          e,
          stackTrace,
        );
      }
    }

    return newBooking;
  }

  /// Update a booking
  Future<TransferBooking> updateBooking(TransferBooking booking) async {
    await initialize();

    final index = _bookings.indexWhere((b) => b.id == booking.id);
    if (index == -1) {
      throw Exception('Booking not found');
    }

    // Create an updated booking
    final updatedBooking = TransferBooking(
      id: booking.id,
      userId: booking.userId,
      transferId: booking.transferId,
      transferService: booking.transferService,
      pickupLocation: booking.pickupLocation,
      dropoffLocation: booking.dropoffLocation,
      pickupDateTime: booking.pickupDateTime,
      passengerCount: booking.passengerCount,
      luggageCount: booking.luggageCount,
      specialRequests: booking.specialRequests,
      contactName: booking.contactName,
      contactPhone: booking.contactPhone,
      contactEmail: booking.contactEmail,
      flightInfo: booking.flightInfo,
      status: booking.status,
      confirmationCode: booking.confirmationCode,
      totalPrice: booking.totalPrice,
      currency: booking.currency,
      paymentMethod: booking.paymentMethod,
      paymentTransactionId: booking.paymentTransactionId,
      driverId: booking.driverId,
      driverName: booking.driverName,
      driverPhone: booking.driverPhone,
      vehicleDetails: booking.vehicleDetails,
      vehicleLicensePlate: booking.vehicleLicensePlate,
      trackingUrl: booking.trackingUrl,
      cancellationReason: booking.cancellationReason,
      cancellationDate: booking.cancellationDate,
      refundAmount: booking.refundAmount,
      refundDate: booking.refundDate,
      createdAt: booking.createdAt,
      updatedAt: DateTime.now(),
    );

    _bookings[index] = updatedBooking;

    // Update in Firestore
    if (_firestore != null) {
      try {
        await _firestore!
            .collection('transfer_bookings')
            .doc(updatedBooking.id)
            .update(updatedBooking.toJson());
      } catch (e, stackTrace) {
        _loggingService.error(
          'TransferService',
          'Error updating booking in Firestore',
          e,
          stackTrace,
        );
      }
    }

    // Update cache
    if (_bookingsBox != null) {
      try {
        await _bookingsBox!
            .put(updatedBooking.id, jsonEncode(updatedBooking.toJson()));
      } catch (e, stackTrace) {
        _loggingService.error(
          'TransferService',
          'Error updating cached booking',
          e,
          stackTrace,
        );
      }
    }

    return updatedBooking;
  }

  /// Cancel a booking
  Future<TransferBooking> cancelBooking(String id, String reason) async {
    await initialize();

    final booking = await getBooking(id);
    if (booking == null) {
      throw Exception('Booking not found');
    }

    // Create a cancelled booking
    final cancelledBooking = TransferBooking(
      id: booking.id,
      userId: booking.userId,
      transferId: booking.transferId,
      transferService: booking.transferService,
      pickupLocation: booking.pickupLocation,
      dropoffLocation: booking.dropoffLocation,
      pickupDateTime: booking.pickupDateTime,
      passengerCount: booking.passengerCount,
      luggageCount: booking.luggageCount,
      specialRequests: booking.specialRequests,
      contactName: booking.contactName,
      contactPhone: booking.contactPhone,
      contactEmail: booking.contactEmail,
      flightInfo: booking.flightInfo,
      status: TransferBookingStatus.cancelled,
      confirmationCode: booking.confirmationCode,
      totalPrice: booking.totalPrice,
      currency: booking.currency,
      paymentMethod: booking.paymentMethod,
      paymentTransactionId: booking.paymentTransactionId,
      driverId: booking.driverId,
      driverName: booking.driverName,
      driverPhone: booking.driverPhone,
      vehicleDetails: booking.vehicleDetails,
      vehicleLicensePlate: booking.vehicleLicensePlate,
      trackingUrl: booking.trackingUrl,
      cancellationReason: reason,
      cancellationDate: DateTime.now(),
      refundAmount: booking.totalPrice, // Full refund for demo
      refundDate: DateTime.now(),
      createdAt: booking.createdAt,
      updatedAt: DateTime.now(),
    );

    return await updateBooking(cancelledBooking);
  }

  /// Calculate the price for a transfer
  Future<double> calculatePrice(
    String transferId,
    TransferLocation pickupLocation,
    TransferLocation dropoffLocation,
    DateTime pickupDateTime,
    int passengerCount,
    int luggageCount,
  ) async {
    await initialize();

    final transfer = await getTransfer(transferId);
    if (transfer == null) {
      throw Exception('Transfer not found');
    }

    // In a real app, this would calculate the price based on distance, time, etc.
    // For demo purposes, we'll use a simple calculation

    // Base price
    double price = transfer.price;

    // Add extra for passengers beyond the base capacity
    final extraPassengers = passengerCount - 2;
    if (extraPassengers > 0) {
      price += extraPassengers * 10.0;
    }

    // Add extra for luggage beyond the base capacity
    final extraLuggage = luggageCount - 2;
    if (extraLuggage > 0) {
      price += extraLuggage * 5.0;
    }

    // Add extra for peak hours (6-9 AM, 4-7 PM)
    final hour = pickupDateTime.hour;
    if ((hour >= 6 && hour <= 9) || (hour >= 16 && hour <= 19)) {
      price *= 1.2; // 20% surcharge
    }

    // Add extra for weekends
    final day = pickupDateTime.weekday;
    if (day == 6 || day == 7) {
      // Saturday or Sunday
      price *= 1.1; // 10% surcharge
    }

    return price;
  }

  /// Generate mock data for testing
  Future<void> _generateMockData() async {
    _transfers.clear();
    _bookings.clear();

    // Add mock transfers
    final mockTransfers = [
      TransferService(
        id: '1',
        name: 'Economy Airport Transfer',
        description: 'Affordable airport transfer service with standard sedan.',
        price: 50.0,
        currency: 'USD',
        rating: 4.2,
        reviewCount: 128,
        imageUrl: 'https://example.com/transfers/economy.jpg',
        additionalImages: [
          'https://example.com/transfers/economy_1.jpg',
          'https://example.com/transfers/economy_2.jpg',
        ],
        provider: 'City Transfers',
        location: 'New York',
        coordinates: const GeoLocation(latitude: 40.7128, longitude: -74.0060),
        isAvailable: true,
        isFeatured: false,
        isOnSale: false,
        tags: ['Economy', 'Airport', 'Sedan'],
        amenities: ['Air Conditioning', 'Bottled Water'],
        cancellationPolicy: 'Free cancellation up to 24 hours before pickup',
        createdAt: DateTime.now().subtract(const Duration(days: 90)),
        updatedAt: DateTime.now().subtract(const Duration(days: 30)),
        vehicleType: TransferVehicleType.sedan,
        vehicle: TransferVehicle(
          type: TransferVehicleType.sedan,
          make: 'Toyota',
          model: 'Camry',
          year: 2020,
          color: 'Black',
          passengerCapacity: 4,
          luggageCapacity: 3,
          features: ['Air Conditioning', 'Bluetooth Audio'],
          imageUrl: 'https://example.com/vehicles/camry.jpg',
          additionalImages: [
            'https://example.com/vehicles/camry_1.jpg',
            'https://example.com/vehicles/camry_2.jpg',
          ],
          hasAirConditioning: true,
          hasWifi: false,
          hasUsb: true,
          hasChildSeat: false,
          hasWheelchairAccess: false,
        ),
        driver: TransferDriver(
          id: 'driver1',
          name: 'John Smith',
          rating: 4.8,
          reviewCount: 352,
          photoUrl: 'https://example.com/drivers/john.jpg',
          languages: ['English', 'Spanish'],
          yearsOfExperience: 5,
          isVerified: true,
        ),
        passengerCapacity: 4,
        luggageCapacity: 3,
        isPrivate: true,
        includesMeetAndGreet: false,
        includesFlightTracking: true,
        includesWaitingTime: true,
        freeWaitingTimeMinutes: 30,
        additionalWaitingTimeCostPerHour: 15.0,
        isAvailable24Hours: false,
        minimumNoticeHours: 4,
        freeCancellationHours: 24,
      ),
      TransferService(
        id: '2',
        name: 'Premium Airport Transfer',
        description: 'Luxury airport transfer service with premium sedan.',
        price: 80.0,
        currency: 'USD',
        rating: 4.7,
        reviewCount: 95,
        imageUrl: 'https://example.com/transfers/premium.jpg',
        additionalImages: [
          'https://example.com/transfers/premium_1.jpg',
          'https://example.com/transfers/premium_2.jpg',
        ],
        provider: 'Luxury Transfers',
        location: 'New York',
        coordinates: const GeoLocation(latitude: 40.7128, longitude: -74.0060),
        isAvailable: true,
        isFeatured: true,
        isOnSale: false,
        tags: ['Premium', 'Airport', 'Luxury'],
        amenities: ['Air Conditioning', 'Bottled Water', 'WiFi', 'Newspaper'],
        cancellationPolicy: 'Free cancellation up to 24 hours before pickup',
        createdAt: DateTime.now().subtract(const Duration(days: 90)),
        updatedAt: DateTime.now().subtract(const Duration(days: 30)),
        vehicleType: TransferVehicleType.luxurySedan,
        vehicle: TransferVehicle(
          type: TransferVehicleType.luxurySedan,
          make: 'Mercedes-Benz',
          model: 'E-Class',
          year: 2022,
          color: 'Black',
          passengerCapacity: 4,
          luggageCapacity: 3,
          features: [
            'Air Conditioning',
            'Leather Seats',
            'WiFi',
            'USB Charging'
          ],
          imageUrl: 'https://example.com/vehicles/mercedes.jpg',
          additionalImages: [
            'https://example.com/vehicles/mercedes_1.jpg',
            'https://example.com/vehicles/mercedes_2.jpg',
          ],
          hasAirConditioning: true,
          hasWifi: true,
          hasUsb: true,
          hasChildSeat: true,
          hasWheelchairAccess: false,
        ),
        driver: TransferDriver(
          id: 'driver2',
          name: 'Michael Johnson',
          rating: 4.9,
          reviewCount: 215,
          photoUrl: 'https://example.com/drivers/michael.jpg',
          languages: ['English', 'French'],
          yearsOfExperience: 8,
          isVerified: true,
        ),
        passengerCapacity: 4,
        luggageCapacity: 3,
        isPrivate: true,
        includesMeetAndGreet: true,
        includesFlightTracking: true,
        includesWaitingTime: true,
        freeWaitingTimeMinutes: 60,
        additionalWaitingTimeCostPerHour: 20.0,
        isAvailable24Hours: true,
        minimumNoticeHours: 2,
        freeCancellationHours: 24,
      ),
    ];

    for (final transfer in mockTransfers) {
      _transfers.add(transfer);

      // Cache the transfer
      if (_transfersBox != null) {
        await _transfersBox!.put(transfer.id, jsonEncode(transfer.toJson()));
      }
    }

    // Add mock bookings
    final mockBookings = [
      TransferBooking(
        id: '1',
        userId: 'user1',
        transferId: '1',
        transferService: _transfers.firstWhere((t) => t.id == '1'),
        pickupLocation: TransferLocation(
          id: 'pickup1',
          type: TransferLocationType.airport,
          name: 'JFK International Airport',
          address: 'JFK Airport, Queens, NY 11430',
          city: 'New York',
          country: 'United States',
          coordinates:
              const GeoLocation(latitude: 40.6413, longitude: -73.7781),
          terminal: 'Terminal 4',
          flightNumber: 'BA178',
        ),
        dropoffLocation: TransferLocation(
          id: 'dropoff1',
          type: TransferLocationType.hotel,
          name: 'Hilton Midtown',
          address: '1335 Avenue of the Americas, New York, NY 10019',
          city: 'New York',
          country: 'United States',
          coordinates:
              const GeoLocation(latitude: 40.7621, longitude: -73.9793),
        ),
        pickupDateTime: DateTime.now().add(const Duration(days: 7)),
        passengerCount: 2,
        luggageCount: 2,
        contactName: 'John Doe',
        contactPhone: '******-123-4567',
        contactEmail: '<EMAIL>',
        flightInfo: 'BA178 arriving at 14:30',
        status: TransferBookingStatus.confirmed,
        confirmationCode: 'ABC123',
        totalPrice: 50.0,
        currency: 'USD',
        createdAt: DateTime.now().subtract(const Duration(days: 2)),
        updatedAt: DateTime.now().subtract(const Duration(days: 2)),
      ),
    ];

    for (final booking in mockBookings) {
      _bookings.add(booking);

      // Cache the booking
      if (_bookingsBox != null) {
        await _bookingsBox!.put(booking.id, jsonEncode(booking.toJson()));
      }
    }
  }
}
