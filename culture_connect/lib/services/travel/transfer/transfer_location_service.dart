import 'dart:convert';
import 'dart:math' as math;

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:hive/hive.dart';
import 'package:culture_connect/models/travel/transfer/transfer_location.dart';
import 'package:culture_connect/models/location/geo_location.dart';
import 'package:culture_connect/services/auth_service.dart';
import 'package:culture_connect/services/connectivity_service.dart';
import 'package:culture_connect/services/logging_service.dart';
import 'package:culture_connect/services/location_service.dart';

/// Service for managing transfer locations
class TransferLocationService {
  // Singleton instance
  static final TransferLocationService _instance =
      TransferLocationService._internal();
  factory TransferLocationService() => _instance;
  TransferLocationService._internal();

  // Services
  final AuthService _authService = AuthService();
  final ConnectivityService _connectivityService = ConnectivityService();
  final LoggingService _loggingService = LoggingService();
  final LocationService _locationService = LocationService();

  // Firestore reference
  final FirebaseFirestore? _firestore = FirebaseFirestore.instance;

  // Hive box for caching locations
  Box<String>? _locationsBox;

  // In-memory storage for locations
  final List<TransferLocation> _locations = [];
  final List<TransferLocation> _savedLocations = [];

  // Flag to track initialization
  bool _isInitialized = false;

  /// Initialize the service
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // Open Hive box for caching
      _locationsBox = await Hive.openBox<String>('transfer_locations');

      // Try to get data from Firestore
      if (_firestore != null) {
        // Get common locations
        final locationsSnapshot =
            await _firestore!.collection('transfer_locations').get();
        if (locationsSnapshot.docs.isNotEmpty) {
          _locations.clear();
          for (final doc in locationsSnapshot.docs) {
            final location =
                TransferLocation.fromJson({...doc.data(), 'id': doc.id});
            _locations.add(location);

            // Cache the location
            if (_locationsBox != null) {
              await _locationsBox!
                  .put(location.id, jsonEncode(location.toJson()));
            }
          }
        }

        // Get saved locations for the current user
        final userId = _authService.currentUser?.uid;
        if (userId != null) {
          final savedLocationsSnapshot = await _firestore!
              .collection('saved_locations')
              .where('userId', isEqualTo: userId)
              .get();

          if (savedLocationsSnapshot.docs.isNotEmpty) {
            _savedLocations.clear();
            for (final doc in savedLocationsSnapshot.docs) {
              final location = TransferLocation.fromJson(
                  {...doc.data()['location'], 'id': doc.id});
              _savedLocations.add(location);

              // Cache the location
              if (_locationsBox != null) {
                await _locationsBox!
                    .put('saved_${location.id}', jsonEncode(location.toJson()));
              }
            }
          }
        }

        _isInitialized = true;
        return;
      }

      // If no data is available from Firestore, try to load from cache
      if (_locationsBox != null && _locationsBox!.isNotEmpty) {
        _locations.clear();
        _savedLocations.clear();
        for (final key in _locationsBox!.keys) {
          final json =
              jsonDecode(_locationsBox!.get(key)!) as Map<String, dynamic>;
          final location = TransferLocation.fromJson(json);
          if (key.startsWith('saved_')) {
            _savedLocations.add(location);
          } else {
            _locations.add(location);
          }
        }

        _isInitialized = true;
        return;
      }

      // If no data is available, generate mock data
      await _generateMockData();
      _isInitialized = true;
    } catch (e, stackTrace) {
      _loggingService.error(
        'TransferLocationService',
        'Error initializing transfer location service',
        e,
        stackTrace,
      );

      // If there's an error, generate mock data
      await _generateMockData();
      _isInitialized = true;
    }
  }

  /// Get all common locations
  Future<List<TransferLocation>> getCommonLocations() async {
    await initialize();
    return _locations;
  }

  /// Get all saved locations for the current user
  Future<List<TransferLocation>> getSavedLocations() async {
    await initialize();
    return _savedLocations;
  }

  /// Get a location by ID
  Future<TransferLocation?> getLocation(String id) async {
    await initialize();
    try {
      return _locations.firstWhere((location) => location.id == id);
    } catch (e) {
      try {
        return _savedLocations.firstWhere((location) => location.id == id);
      } catch (e) {
        return null;
      }
    }
  }

  /// Search for locations
  Future<List<TransferLocation>> searchLocations(String query) async {
    await initialize();

    // First, search in-memory locations
    final results = <TransferLocation>[];

    // Search common locations
    results.addAll(_locations.where((location) {
      return location.name.toLowerCase().contains(query.toLowerCase()) ||
          location.address.toLowerCase().contains(query.toLowerCase()) ||
          location.city.toLowerCase().contains(query.toLowerCase());
    }));

    // Search saved locations
    results.addAll(_savedLocations.where((location) {
      return location.name.toLowerCase().contains(query.toLowerCase()) ||
          location.address.toLowerCase().contains(query.toLowerCase()) ||
          location.city.toLowerCase().contains(query.toLowerCase());
    }));

    // If we have enough results, return them
    if (results.length >= 5) {
      return results;
    }

    // Otherwise, try to search using the location service
    final isConnected = await _connectivityService.isConnected();
    if (isConnected) {
      try {
        final apiResults = await _locationService.searchPlaces(query);
        for (final place in apiResults) {
          // Convert place to TransferLocation
          final location = TransferLocation(
            id: place.id,
            type: _getLocationTypeFromType(place.type),
            name: place.name,
            address: place.address,
            city: place.city,
            country: place.country,
            postalCode: place.postalCode,
            coordinates: GeoLocation(
              latitude: place.latitude,
              longitude: place.longitude,
            ),
          );

          // Add to results if not already present
          if (!results.any((l) => l.id == location.id)) {
            results.add(location);
          }
        }
      } catch (e, stackTrace) {
        _loggingService.error(
          'TransferLocationService',
          'Error searching locations',
          e,
          stackTrace,
        );
      }
    }

    return results;
  }

  /// Get airports near a location
  Future<List<TransferLocation>> getNearbyAirports(GeoLocation coordinates,
      {double radiusKm = 50.0}) async {
    await initialize();

    // First, check in-memory airports
    final airports = _locations
        .where((location) => location.type == TransferLocationType.airport)
        .toList();

    // If we have airports, filter by distance
    if (airports.isNotEmpty) {
      return airports.where((airport) {
        final distance = _calculateDistance(
          coordinates.latitude,
          coordinates.longitude,
          airport.coordinates.latitude,
          airport.coordinates.longitude,
        );
        return distance <= radiusKm;
      }).toList();
    }

    // Otherwise, try to search using the location service
    final isConnected = await _connectivityService.isConnected();
    if (isConnected) {
      try {
        final apiResults = await _locationService.searchNearbyPlaces(
          coordinates.latitude,
          coordinates.longitude,
          radiusKm * 1000, // Convert to meters
        );

        final results = <TransferLocation>[];
        for (final place in apiResults) {
          // Convert place to TransferLocation
          final location = TransferLocation(
            id: place.id,
            type: _getLocationTypeFromType(place.type),
            name: place.name,
            address: place.address,
            city: place.city,
            country: place.country,
            postalCode: place.postalCode,
            coordinates: GeoLocation(
              latitude: place.latitude,
              longitude: place.longitude,
            ),
          );

          results.add(location);
        }

        return results;
      } catch (e, stackTrace) {
        _loggingService.error(
          'TransferLocationService',
          'Error searching nearby airports',
          e,
          stackTrace,
        );
      }
    }

    return [];
  }

  /// Save a location for the current user
  Future<TransferLocation> saveLocation(TransferLocation location) async {
    await initialize();

    // Check if the location is already saved
    if (_savedLocations.any((l) => l.id == location.id)) {
      return location;
    }

    // Add to saved locations
    _savedLocations.add(location);

    // Save to Firestore
    if (_firestore != null) {
      try {
        final userId = _authService.currentUser?.uid;
        if (userId != null) {
          await _firestore!.collection('saved_locations').add({
            'userId': userId,
            'location': location.toJson(),
            'createdAt': DateTime.now().toIso8601String(),
          });
        }
      } catch (e, stackTrace) {
        _loggingService.error(
          'TransferLocationService',
          'Error saving location to Firestore',
          e,
          stackTrace,
        );
      }
    }

    // Cache the location
    if (_locationsBox != null) {
      try {
        await _locationsBox!
            .put('saved_${location.id}', jsonEncode(location.toJson()));
      } catch (e, stackTrace) {
        _loggingService.error(
          'TransferLocationService',
          'Error caching saved location',
          e,
          stackTrace,
        );
      }
    }

    return location;
  }

  /// Remove a saved location
  Future<void> removeSavedLocation(String id) async {
    await initialize();

    // Remove from saved locations
    _savedLocations.removeWhere((location) => location.id == id);

    // Remove from Firestore
    if (_firestore != null) {
      try {
        final userId = _authService.currentUser?.uid;
        if (userId != null) {
          final snapshot = await _firestore!
              .collection('saved_locations')
              .where('userId', isEqualTo: userId)
              .where('location.id', isEqualTo: id)
              .get();

          for (final doc in snapshot.docs) {
            await doc.reference.delete();
          }
        }
      } catch (e, stackTrace) {
        _loggingService.error(
          'TransferLocationService',
          'Error removing saved location from Firestore',
          e,
          stackTrace,
        );
      }
    }

    // Remove from cache
    if (_locationsBox != null) {
      try {
        await _locationsBox!.delete('saved_$id');
      } catch (e, stackTrace) {
        _loggingService.error(
          'TransferLocationService',
          'Error removing saved location from cache',
          e,
          stackTrace,
        );
      }
    }
  }

  /// Get the location type based on a single place type
  TransferLocationType _getLocationTypeFromType(String? type) {
    if (type == null) return TransferLocationType.address;

    switch (type.toLowerCase()) {
      case 'airport':
        return TransferLocationType.airport;
      case 'lodging':
      case 'hotel':
        return TransferLocationType.hotel;
      case 'train_station':
        return TransferLocationType.trainStation;
      case 'bus_station':
        return TransferLocationType.busStation;
      case 'point_of_interest':
      case 'tourist_attraction':
      case 'attraction':
        return TransferLocationType.landmark;
      default:
        return TransferLocationType.address;
    }
  }

  /// Calculate distance between two coordinates using the Haversine formula
  double _calculateDistance(
      double lat1, double lon1, double lat2, double lon2) {
    const R = 6371.0; // Earth radius in kilometers
    final dLat = _toRadians(lat2 - lat1);
    final dLon = _toRadians(lon2 - lon1);

    // Using dart:math functions
    final a = math.sin(dLat / 2) * math.sin(dLat / 2) +
        math.cos(_toRadians(lat1)) *
            math.cos(_toRadians(lat2)) *
            math.sin(dLon / 2) *
            math.sin(dLon / 2);
    final c = 2 * math.atan2(math.sqrt(a), math.sqrt(1 - a));
    return R * c;
  }

  /// Convert degrees to radians
  double _toRadians(double degrees) {
    return degrees * (math.pi / 180);
  }

  /// Generate mock data for testing
  Future<void> _generateMockData() async {
    _locations.clear();
    _savedLocations.clear();

    // Add mock common locations
    _locations.add(
      TransferLocation(
        id: '1',
        type: TransferLocationType.airport,
        name: 'John F. Kennedy International Airport',
        address: 'Queens, NY 11430',
        city: 'New York',
        country: 'United States',
        postalCode: '11430',
        coordinates: const GeoLocation(latitude: 40.6413, longitude: -73.7781),
      ),
    );

    _locations.add(
      TransferLocation(
        id: '2',
        type: TransferLocationType.airport,
        name: 'LaGuardia Airport',
        address: 'Queens, NY 11371',
        city: 'New York',
        country: 'United States',
        postalCode: '11371',
        coordinates: const GeoLocation(latitude: 40.7769, longitude: -73.8740),
      ),
    );

    _locations.add(
      TransferLocation(
        id: '3',
        type: TransferLocationType.hotel,
        name: 'Hilton New York Midtown',
        address: '1335 Avenue of the Americas',
        city: 'New York',
        country: 'United States',
        postalCode: '10019',
        coordinates: const GeoLocation(latitude: 40.7625, longitude: -73.9780),
      ),
    );

    _locations.add(
      TransferLocation(
        id: '4',
        type: TransferLocationType.hotel,
        name: 'Marriott Marquis',
        address: '1535 Broadway',
        city: 'New York',
        country: 'United States',
        postalCode: '10036',
        coordinates: const GeoLocation(latitude: 40.7590, longitude: -73.9845),
      ),
    );

    _locations.add(
      TransferLocation(
        id: '5',
        type: TransferLocationType.trainStation,
        name: 'Grand Central Terminal',
        address: '89 E 42nd St',
        city: 'New York',
        country: 'United States',
        postalCode: '10017',
        coordinates: const GeoLocation(latitude: 40.7527, longitude: -73.9772),
      ),
    );

    // Add mock saved locations
    _savedLocations.add(
      TransferLocation(
        id: '6',
        type: TransferLocationType.address,
        name: 'Home',
        address: '123 Main St',
        city: 'New York',
        country: 'United States',
        postalCode: '10001',
        coordinates: const GeoLocation(latitude: 40.7505, longitude: -73.9934),
      ),
    );

    _savedLocations.add(
      TransferLocation(
        id: '7',
        type: TransferLocationType.address,
        name: 'Office',
        address: '456 Park Ave',
        city: 'New York',
        country: 'United States',
        postalCode: '10022',
        coordinates: const GeoLocation(latitude: 40.7580, longitude: -73.9712),
      ),
    );

    // Cache the locations
    if (_locationsBox != null) {
      for (final location in _locations) {
        await _locationsBox!.put(location.id, jsonEncode(location.toJson()));
      }
      for (final location in _savedLocations) {
        await _locationsBox!
            .put('saved_${location.id}', jsonEncode(location.toJson()));
      }
    }
  }
}
