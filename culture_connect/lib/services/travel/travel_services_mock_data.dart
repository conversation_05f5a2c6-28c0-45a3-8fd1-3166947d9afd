/// Mock data generation for travel services
///
/// This file contains extension methods for generating mock data for testing
/// and development purposes. It creates sample data for all travel service types.
library travel_services_mock_data;

// Package imports
import 'package:flutter/material.dart';

// Project imports - Models
import 'package:culture_connect/models/travel/travel.dart';
import 'package:culture_connect/models/location/geo_location.dart';

// Project imports - Services
import 'package:culture_connect/services/logging_service.dart';
import 'package:culture_connect/services/travel/travel_services_service.dart';

/// Extension for the TravelServicesService to add mock data generation
extension TravelServicesMockData on TravelServicesService {
  /// Generate mock data for testing
  Future<void> generateMockData() async {
    // Get the logging service
    final loggingService = LoggingService();

    try {
      loggingService.debug(
        'TravelServicesMockData',
        'Generating mock data for travel services',
      );

      // Clear existing data
      travelServices.clear();
      priceAlerts.clear();
      priceComparisons.clear();

      // Generate mock data for each travel service type
      await _generateMockCarRentals();
      await _generateMockPrivateSecurityServices();
      await _generateMockHotels();
      await _generateMockRestaurants();
      await _generateMockFlights();
      await _generateMockCruises();
      await _generateMockPriceAlerts();
      await _generateMockPriceComparisons();

      loggingService.debug(
        'TravelServicesMockData',
        'Successfully generated mock data for travel services',
      );
    } catch (e, stackTrace) {
      loggingService.error(
        'TravelServicesMockData',
        'Error generating mock data for travel services',
        e,
        stackTrace,
      );
      rethrow;
    }
  }

  /// Generate mock car rentals
  Future<void> _generateMockCarRentals() async {
    final carRentals = <CarRental>[
      CarRental(
        id: 'car1',
        name: '2023 Toyota Camry',
        description:
            'A comfortable and reliable sedan perfect for city driving and short trips.',
        price: 45.0,
        currency: 'USD',
        rating: 4.5,
        reviewCount: 120,
        imageUrl: 'https://example.com/cars/toyota_camry.jpg',
        additionalImages: [
          'https://example.com/cars/toyota_camry_interior.jpg',
          'https://example.com/cars/toyota_camry_trunk.jpg',
        ],
        provider: 'Hertz',
        location: 'Lagos, Nigeria',
        coordinates: const GeoLocation(latitude: 6.5244, longitude: 3.3792),
        isAvailable: true,
        isFeatured: true,
        isOnSale: false,
        tags: ['Sedan', 'Automatic', 'Air Conditioning', 'Bluetooth'],
        amenities: [
          'GPS Navigation',
          'Bluetooth',
          'USB Ports',
          'Backup Camera'
        ],
        cancellationPolicy: 'Free cancellation up to 24 hours before pickup',
        createdAt: DateTime.now().subtract(const Duration(days: 30)),
        updatedAt: DateTime.now().subtract(const Duration(days: 5)),
        make: 'Toyota',
        model: 'Camry',
        year: 2023,
        carType: CarType.midsize,
        transmission: TransmissionType.automatic,
        fuelType: FuelType.gasoline,
        seats: 5,
        doors: 4,
        hasAirConditioning: true,
        hasGPS: true,
        hasBluetooth: true,
        hasUSB: true,
        hasSunroof: false,
        luggageCapacity: 3,
        mileageLimit: 150,
        pickupLocation: 'Lagos International Airport',
        pickupCoordinates:
            const GeoLocation(latitude: 6.5774, longitude: 3.3212),
        dropoffLocation: 'Lagos International Airport',
        dropoffCoordinates:
            const GeoLocation(latitude: 6.5774, longitude: 3.3212),
        rentalCompany: 'Hertz',
        rentalCompanyLogoUrl: 'https://example.com/logos/hertz.png',
      ),
      CarRental(
        id: 'car2',
        name: '2023 Mercedes-Benz E-Class',
        description:
            'A luxury sedan with premium features and excellent performance.',
        price: 95.0,
        currency: 'USD',
        rating: 4.8,
        reviewCount: 85,
        imageUrl: 'https://example.com/cars/mercedes_e_class.jpg',
        additionalImages: [
          'https://example.com/cars/mercedes_e_class_interior.jpg',
          'https://example.com/cars/mercedes_e_class_trunk.jpg',
        ],
        provider: 'Avis',
        location: 'Lagos, Nigeria',
        coordinates: const GeoLocation(latitude: 6.5244, longitude: 3.3792),
        isAvailable: true,
        isFeatured: true,
        isOnSale: true,
        originalPrice: 120.0,
        discountPercentage: 20.0,
        tags: ['Luxury', 'Automatic', 'Air Conditioning', 'Leather Seats'],
        amenities: [
          'GPS Navigation',
          'Bluetooth',
          'USB Ports',
          'Backup Camera',
          'Heated Seats'
        ],
        cancellationPolicy: 'Free cancellation up to 48 hours before pickup',
        createdAt: DateTime.now().subtract(const Duration(days: 45)),
        updatedAt: DateTime.now().subtract(const Duration(days: 3)),
        make: 'Mercedes-Benz',
        model: 'E-Class',
        year: 2023,
        carType: CarType.luxury,
        transmission: TransmissionType.automatic,
        fuelType: FuelType.gasoline,
        seats: 5,
        doors: 4,
        hasAirConditioning: true,
        hasGPS: true,
        hasBluetooth: true,
        hasUSB: true,
        hasSunroof: true,
        luggageCapacity: 4,
        mileageLimit: 200,
        pickupLocation: 'Lagos International Airport',
        pickupCoordinates:
            const GeoLocation(latitude: 6.5774, longitude: 3.3212),
        dropoffLocation: 'Lagos International Airport',
        dropoffCoordinates:
            const GeoLocation(latitude: 6.5774, longitude: 3.3212),
        rentalCompany: 'Avis',
        rentalCompanyLogoUrl: 'https://example.com/logos/avis.png',
      ),
      CarRental(
        id: 'car3',
        name: '2023 Toyota Land Cruiser',
        description:
            'A rugged SUV perfect for off-road adventures and exploring the countryside.',
        price: 120.0,
        currency: 'USD',
        rating: 4.7,
        reviewCount: 95,
        imageUrl: 'https://example.com/cars/toyota_land_cruiser.jpg',
        additionalImages: [
          'https://example.com/cars/toyota_land_cruiser_interior.jpg',
          'https://example.com/cars/toyota_land_cruiser_trunk.jpg',
        ],
        provider: 'Enterprise',
        location: 'Lagos, Nigeria',
        coordinates: const GeoLocation(latitude: 6.5244, longitude: 3.3792),
        isAvailable: true,
        isFeatured: false,
        isOnSale: false,
        tags: ['SUV', 'Automatic', 'Air Conditioning', '4WD'],
        amenities: [
          'GPS Navigation',
          'Bluetooth',
          'USB Ports',
          'Backup Camera',
          'Roof Rack'
        ],
        cancellationPolicy: 'Free cancellation up to 24 hours before pickup',
        createdAt: DateTime.now().subtract(const Duration(days: 60)),
        updatedAt: DateTime.now().subtract(const Duration(days: 10)),
        make: 'Toyota',
        model: 'Land Cruiser',
        year: 2023,
        carType: CarType.suv,
        transmission: TransmissionType.automatic,
        fuelType: FuelType.diesel,
        seats: 7,
        doors: 5,
        hasAirConditioning: true,
        hasGPS: true,
        hasBluetooth: true,
        hasUSB: true,
        hasSunroof: false,
        luggageCapacity: 5,
        mileageLimit: 150,
        pickupLocation: 'Lagos International Airport',
        pickupCoordinates:
            const GeoLocation(latitude: 6.5774, longitude: 3.3212),
        dropoffLocation: 'Lagos International Airport',
        dropoffCoordinates:
            const GeoLocation(latitude: 6.5774, longitude: 3.3212),
        rentalCompany: 'Enterprise',
        rentalCompanyLogoUrl: 'https://example.com/logos/enterprise.png',
      ),
    ];

    // Add to in-memory storage
    travelServices.addAll(carRentals);
  }

  /// Generate mock private security services
  Future<void> _generateMockPrivateSecurityServices() async {
    final privateSecurityServices = <PrivateSecurity>[
      PrivateSecurity(
        id: 'sec1',
        name: 'Personal Bodyguard Service',
        description:
            'Professional bodyguard service for personal protection during your stay.',
        price: 150.0,
        currency: 'USD',
        rating: 4.9,
        reviewCount: 75,
        imageUrl: 'https://example.com/security/bodyguard.jpg',
        additionalImages: [
          'https://example.com/security/bodyguard_vehicle.jpg',
          'https://example.com/security/bodyguard_equipment.jpg',
        ],
        provider: 'SecureGuard',
        location: 'Lagos, Nigeria',
        coordinates: const GeoLocation(latitude: 6.5244, longitude: 3.3792),
        isAvailable: true,
        isFeatured: true,
        isOnSale: false,
        tags: ['Personal Protection', 'Professional', 'Trained', 'Discreet'],
        amenities: [
          '24/7 Service',
          'Secure Transportation',
          'Risk Assessment',
          'Emergency Response'
        ],
        cancellationPolicy: 'Free cancellation up to 48 hours before service',
        createdAt: DateTime.now().subtract(const Duration(days: 90)),
        updatedAt: DateTime.now().subtract(const Duration(days: 15)),
        serviceType: SecurityServiceType.personalBodyguard,
        personnelCount: 1,
        trainingLevel: SecurityTrainingLevel.expert,
        isArmed: false,
        isUniformed: false,
        hasVehicle: true,
        vehicleType: 'SUV',
        hasCommunicationEquipment: true,
        languages: ['English', 'Yoruba', 'French'],
        securityCompany: 'SecureGuard',
        securityCompanyLogoUrl: 'https://example.com/logos/secureguard.png',
        includesBackgroundChecks: true,
        includesRiskAssessment: true,
        includesEmergencyResponse: true,
        includes24HrSupport: true,
      ),
      PrivateSecurity(
        id: 'sec2',
        name: 'Executive Protection Team',
        description:
            'Comprehensive security team for executives and VIPs with multiple personnel and vehicles.',
        price: 450.0,
        currency: 'USD',
        rating: 5.0,
        reviewCount: 42,
        imageUrl: 'https://example.com/security/executive_team.jpg',
        additionalImages: [
          'https://example.com/security/executive_vehicle.jpg',
          'https://example.com/security/executive_equipment.jpg',
        ],
        provider: 'Elite Security',
        location: 'Lagos, Nigeria',
        coordinates: const GeoLocation(latitude: 6.5244, longitude: 3.3792),
        isAvailable: true,
        isFeatured: true,
        isOnSale: true,
        originalPrice: 550.0,
        discountPercentage: 18.0,
        tags: ['Executive Protection', 'VIP', 'Team', 'Professional'],
        amenities: [
          '24/7 Service',
          'Secure Transportation',
          'Risk Assessment',
          'Emergency Response',
          'Advance Team'
        ],
        cancellationPolicy: 'Free cancellation up to 72 hours before service',
        createdAt: DateTime.now().subtract(const Duration(days: 120)),
        updatedAt: DateTime.now().subtract(const Duration(days: 20)),
        serviceType: SecurityServiceType.executiveProtection,
        personnelCount: 3,
        trainingLevel: SecurityTrainingLevel.specialized,
        isArmed: true,
        isUniformed: false,
        hasVehicle: true,
        vehicleType: 'Armored SUV',
        hasCommunicationEquipment: true,
        languages: ['English', 'Yoruba', 'French', 'Arabic'],
        securityCompany: 'Elite Security',
        securityCompanyLogoUrl: 'https://example.com/logos/elite_security.png',
        includesBackgroundChecks: true,
        includesRiskAssessment: true,
        includesEmergencyResponse: true,
        includes24HrSupport: true,
      ),
    ];

    // Add to in-memory storage
    travelServices.addAll(privateSecurityServices);
  }

  /// Generate mock hotels
  Future<void> _generateMockHotels() async {
    final hotels = <Hotel>[
      Hotel(
        id: 'hotel1',
        name: 'Lagos Luxury Hotel',
        description:
            'A 5-star luxury hotel in the heart of Lagos with stunning views of the city.',
        price: 250.0,
        currency: 'USD',
        rating: 4.8,
        reviewCount: 320,
        imageUrl: 'https://example.com/hotels/lagos_luxury.jpg',
        additionalImages: [
          'https://example.com/hotels/lagos_luxury_lobby.jpg',
          'https://example.com/hotels/lagos_luxury_pool.jpg',
          'https://example.com/hotels/lagos_luxury_restaurant.jpg',
        ],
        provider: 'Booking.com',
        location: 'Victoria Island, Lagos, Nigeria',
        coordinates: const GeoLocation(latitude: 6.4281, longitude: 3.4219),
        isAvailable: true,
        isFeatured: true,
        isOnSale: true,
        originalPrice: 300.0,
        discountPercentage: 16.7,
        tags: ['Luxury', '5-Star', 'City Center', 'Pool', 'Spa'],
        amenities: [
          'Free WiFi',
          'Pool',
          'Spa',
          'Fitness Center',
          'Restaurant',
          'Bar',
          'Room Service'
        ],
        cancellationPolicy: 'Free cancellation up to 48 hours before check-in',
        createdAt: DateTime.now().subtract(const Duration(days: 180)),
        updatedAt: DateTime.now().subtract(const Duration(days: 30)),
        starRating: HotelStarRating.fiveStar,
        rooms: [
          const HotelRoom(
            id: 'room1',
            type: RoomType.deluxe,
            description: 'Spacious deluxe room with city view',
            pricePerNight: 250.0,
            currency: 'USD',
            maxGuests: 2,
            bedCount: 1,
            bedType: 'King',
            roomSize: '40 sq m',
            hasView: true,
            viewType: 'City View',
            hasBalcony: true,
            hasPrivateBathroom: true,
            hasAirConditioning: true,
            hasTV: true,
            hasMinibar: true,
            hasSafe: true,
            hasFreeWifi: true,
            isNonSmoking: true,
            isAccessible: false,
            imageUrl: 'https://example.com/hotels/lagos_luxury_deluxe.jpg',
            additionalImages: [
              'https://example.com/hotels/lagos_luxury_deluxe_bathroom.jpg',
              'https://example.com/hotels/lagos_luxury_deluxe_view.jpg',
            ],
          ),
          const HotelRoom(
            id: 'room2',
            type: RoomType.suite,
            description:
                'Luxury suite with separate living area and ocean view',
            pricePerNight: 450.0,
            currency: 'USD',
            maxGuests: 3,
            bedCount: 1,
            bedType: 'King',
            roomSize: '75 sq m',
            hasView: true,
            viewType: 'Ocean View',
            hasBalcony: true,
            hasPrivateBathroom: true,
            hasAirConditioning: true,
            hasTV: true,
            hasMinibar: true,
            hasSafe: true,
            hasFreeWifi: true,
            isNonSmoking: true,
            isAccessible: true,
            imageUrl: 'https://example.com/hotels/lagos_luxury_suite.jpg',
            additionalImages: [
              'https://example.com/hotels/lagos_luxury_suite_bathroom.jpg',
              'https://example.com/hotels/lagos_luxury_suite_view.jpg',
              'https://example.com/hotels/lagos_luxury_suite_living.jpg',
            ],
          ),
        ],
        checkInTime: const TimeOfDay(hour: 14, minute: 0),
        checkOutTime: const TimeOfDay(hour: 12, minute: 0),
        hasRestaurant: true,
        hasBar: true,
        hasPool: true,
        hasSpa: true,
        hasGym: true,
        hasFreeWifi: true,
        hasFreeParking: true,
        hasRoomService: true,
        hasBusinessCenter: true,
        hasConferenceRoom: true,
        hasKidsClub: true,
        hasConciergeService: true,
        hasLaundryService: true,
        hasShuttleService: true,
        has24HrFrontDesk: true,
        distanceFromCityCenter: 0.5,
        distanceFromAirport: 25.0,
        nearestAirport: 'Lagos International Airport',
      ),
    ];

    // Add to in-memory storage
    travelServices.addAll(hotels);
  }

  /// Generate mock restaurants
  Future<void> _generateMockRestaurants() async {
    // Implementation will be added in a separate file
  }

  /// Generate mock flights
  Future<void> _generateMockFlights() async {
    // Implementation will be added in a separate file
  }

  /// Generate mock cruises
  Future<void> _generateMockCruises() async {
    // Implementation will be added in a separate file
  }

  /// Generate mock price alerts
  Future<void> _generateMockPriceAlerts() async {
    // Implementation will be added in a separate file
  }

  /// Generate mock price comparisons
  Future<void> _generateMockPriceComparisons() async {
    // Implementation will be added in a separate file
  }
}
