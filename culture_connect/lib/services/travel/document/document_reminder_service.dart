import 'dart:convert';
import 'package:uuid/uuid.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:hive/hive.dart';
import 'package:culture_connect/models/travel/document/travel_documents.dart';
import 'package:culture_connect/services/auth_service.dart';
import 'package:culture_connect/services/logging_service.dart';
import 'package:culture_connect/services/notification_service.dart';
import 'package:culture_connect/services/notification_service_extension.dart';

/// Service for managing document reminders
class DocumentReminderService {
  // Singleton instance
  static final DocumentReminderService _instance =
      DocumentReminderService._internal();
  factory DocumentReminderService() => _instance;
  DocumentReminderService._internal();

  // Services
  final AuthService _authService = AuthService();
  final LoggingService _loggingService = LoggingService();
  final NotificationService _notificationService = NotificationService();

  // Firestore reference
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  // Hive box for caching reminders
  Box<String>? _remindersBox;

  // In-memory storage for reminders
  final List<DocumentReminder> _reminders = [];

  // Flag to track initialization
  bool _isInitialized = false;

  /// Initialize the service
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // Open Hive box for caching
      _remindersBox = await Hive.openBox<String>('document_reminders');

      // Try to get data from Firestore
      final userId = _authService.currentUser?.uid;
      if (userId != null) {
        final snapshot = await _firestore
            .collection('document_reminders')
            .where('userId', isEqualTo: userId)
            .get();

        if (snapshot.docs.isNotEmpty) {
          _reminders.clear();
          for (final doc in snapshot.docs) {
            final reminder =
                DocumentReminder.fromJson({...doc.data(), 'id': doc.id});
            _reminders.add(reminder);

            // Cache the reminder
            if (_remindersBox != null) {
              await _remindersBox!
                  .put(reminder.id, jsonEncode(reminder.toJson()));
            }

            // Schedule notification for the reminder
            await _scheduleNotification(reminder);
          }

          _isInitialized = true;
          return;
        }
      }

      // If no data is available from Firestore, try to load from cache
      if (_remindersBox != null && _remindersBox!.isNotEmpty) {
        _reminders.clear();
        for (final key in _remindersBox!.keys) {
          final json =
              jsonDecode(_remindersBox!.get(key)!) as Map<String, dynamic>;
          final reminder = DocumentReminder.fromJson(json);
          _reminders.add(reminder);

          // Schedule notification for the reminder
          await _scheduleNotification(reminder);
        }

        _isInitialized = true;
        return;
      }

      // If no data is available, generate mock data
      await _generateMockData();
      _isInitialized = true;
    } catch (e, stackTrace) {
      _loggingService.error(
        'DocumentReminderService',
        'Error initializing document reminder service',
        e,
        stackTrace,
      );

      // If there's an error, generate mock data
      await _generateMockData();
      _isInitialized = true;
    }
  }

  /// Get all reminders for the current user
  Future<List<DocumentReminder>> getUserReminders() async {
    await initialize();
    return _reminders;
  }

  /// Get all reminders for a specific user
  Future<List<DocumentReminder>> getReminders(String userId) async {
    await initialize();

    try {
      // Filter reminders by user ID
      final userReminders =
          _reminders.where((reminder) => reminder.userId == userId).toList();

      // If we have reminders in memory, return them
      if (userReminders.isNotEmpty) {
        return userReminders;
      }

      // If not in memory, try to fetch from Firestore
      try {
        final snapshot = await _firestore
            .collection('document_reminders')
            .where('userId', isEqualTo: userId)
            .get();

        if (snapshot.docs.isNotEmpty) {
          final reminders = <DocumentReminder>[];
          for (final doc in snapshot.docs) {
            final reminder =
                DocumentReminder.fromJson({...doc.data(), 'id': doc.id});
            _reminders.add(reminder); // Cache in memory
            reminders.add(reminder);

            // Cache the reminder
            if (_remindersBox != null) {
              await _remindersBox!
                  .put(reminder.id, jsonEncode(reminder.toJson()));
            }

            // Schedule notification for the reminder
            await _scheduleNotification(reminder);
          }

          return reminders;
        }
      } catch (e, stackTrace) {
        _loggingService.error(
          'DocumentReminderService',
          'Error fetching reminders for user from Firestore',
          e,
          stackTrace,
        );
      }

      // If not found in Firestore, return empty list
      return [];
    } catch (e, stackTrace) {
      _loggingService.error(
        'DocumentReminderService',
        'Error getting reminders for user',
        e,
        stackTrace,
      );
      return [];
    }
  }

  /// Get reminders for a specific document
  Future<List<DocumentReminder>> getDocumentReminders(String documentId) async {
    await initialize();
    return _reminders
        .where((reminder) => reminder.documentId == documentId)
        .toList();
  }

  /// Get reminders for a specific document (alias for getDocumentReminders)
  Future<List<DocumentReminder>> getRemindersForDocument(
      String documentId) async {
    return getDocumentReminders(documentId);
  }

  /// Get a reminder by ID
  Future<DocumentReminder?> getReminder(String id) async {
    await initialize();
    try {
      return _reminders.firstWhere((reminder) => reminder.id == id);
    } catch (e) {
      return null;
    }
  }

  /// Add a new reminder
  Future<DocumentReminder> addReminder(DocumentReminder reminder) async {
    await initialize();

    final newReminder = DocumentReminder(
      id: const Uuid().v4(),
      userId: _authService.currentUser?.uid ?? 'user1',
      documentId: reminder.documentId,
      type: reminder.type,
      title: reminder.title,
      message: reminder.message,
      reminderDate: reminder.reminderDate,
      isRead: false,
      isDismissed: false,
      isRecurring: reminder.isRecurring,
      recurrenceIntervalDays: reminder.recurrenceIntervalDays,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );

    _reminders.add(newReminder);

    // Save to Firestore
    try {
      await _firestore
          .collection('document_reminders')
          .doc(newReminder.id)
          .set(newReminder.toJson());
    } catch (e, stackTrace) {
      _loggingService.error(
        'DocumentReminderService',
        'Error adding reminder to Firestore',
        e,
        stackTrace,
      );
    }

    // Cache the reminder
    if (_remindersBox != null) {
      try {
        await _remindersBox!
            .put(newReminder.id, jsonEncode(newReminder.toJson()));
      } catch (e, stackTrace) {
        _loggingService.error(
          'DocumentReminderService',
          'Error caching reminder',
          e,
          stackTrace,
        );
      }
    }

    // Schedule notification
    await _scheduleNotification(newReminder);

    return newReminder;
  }

  /// Update a reminder
  Future<DocumentReminder> updateReminder(DocumentReminder reminder) async {
    await initialize();

    final index = _reminders.indexWhere((r) => r.id == reminder.id);
    if (index == -1) {
      throw Exception('Reminder not found');
    }

    final updatedReminder = reminder.copyWith(
      updatedAt: DateTime.now(),
    );

    _reminders[index] = updatedReminder;

    // Save to Firestore
    try {
      await _firestore
          .collection('document_reminders')
          .doc(updatedReminder.id)
          .update(updatedReminder.toJson());
    } catch (e, stackTrace) {
      _loggingService.error(
        'DocumentReminderService',
        'Error updating reminder in Firestore',
        e,
        stackTrace,
      );
    }

    // Cache the reminder
    if (_remindersBox != null) {
      try {
        await _remindersBox!
            .put(updatedReminder.id, jsonEncode(updatedReminder.toJson()));
      } catch (e, stackTrace) {
        _loggingService.error(
          'DocumentReminderService',
          'Error caching updated reminder',
          e,
          stackTrace,
        );
      }
    }

    // Update notification
    await _scheduleNotification(updatedReminder);

    return updatedReminder;
  }

  /// Delete a reminder
  Future<void> deleteReminder(String id) async {
    await initialize();

    final index = _reminders.indexWhere((r) => r.id == id);
    if (index == -1) {
      throw Exception('Reminder not found');
    }

    _reminders.removeAt(index);

    // Delete from Firestore
    try {
      await _firestore.collection('document_reminders').doc(id).delete();
    } catch (e, stackTrace) {
      _loggingService.error(
        'DocumentReminderService',
        'Error deleting reminder from Firestore',
        e,
        stackTrace,
      );
    }

    // Delete from cache
    if (_remindersBox != null) {
      try {
        await _remindersBox!.delete(id);
      } catch (e, stackTrace) {
        _loggingService.error(
          'DocumentReminderService',
          'Error deleting reminder from cache',
          e,
          stackTrace,
        );
      }
    }

    // Cancel notification
    await _notificationService.cancelNotification(id);
  }

  /// Mark a reminder as read
  Future<DocumentReminder> markReminderAsRead(String id) async {
    await initialize();

    final index = _reminders.indexWhere((r) => r.id == id);
    if (index == -1) {
      throw Exception('Reminder not found');
    }

    final reminder = _reminders[index];
    final updatedReminder = reminder.copyWith(
      isRead: true,
      updatedAt: DateTime.now(),
    );

    _reminders[index] = updatedReminder;

    // Save to Firestore
    try {
      await _firestore.collection('document_reminders').doc(id).update(
          {'isRead': true, 'updatedAt': DateTime.now().toIso8601String()});
    } catch (e, stackTrace) {
      _loggingService.error(
        'DocumentReminderService',
        'Error updating reminder in Firestore',
        e,
        stackTrace,
      );
    }

    // Cache the reminder
    if (_remindersBox != null) {
      try {
        await _remindersBox!.put(id, jsonEncode(updatedReminder.toJson()));
      } catch (e, stackTrace) {
        _loggingService.error(
          'DocumentReminderService',
          'Error caching updated reminder',
          e,
          stackTrace,
        );
      }
    }

    // Cancel notification
    await _notificationService.cancelNotification(id);

    return updatedReminder;
  }

  /// Dismiss a reminder
  Future<DocumentReminder> dismissReminder(String id) async {
    await initialize();

    final index = _reminders.indexWhere((r) => r.id == id);
    if (index == -1) {
      throw Exception('Reminder not found');
    }

    final reminder = _reminders[index];
    final updatedReminder = reminder.copyWith(
      isDismissed: true,
      updatedAt: DateTime.now(),
    );

    _reminders[index] = updatedReminder;

    // Save to Firestore
    try {
      await _firestore.collection('document_reminders').doc(id).update(
          {'isDismissed': true, 'updatedAt': DateTime.now().toIso8601String()});
    } catch (e, stackTrace) {
      _loggingService.error(
        'DocumentReminderService',
        'Error updating reminder in Firestore',
        e,
        stackTrace,
      );
    }

    // Cache the reminder
    if (_remindersBox != null) {
      try {
        await _remindersBox!.put(id, jsonEncode(updatedReminder.toJson()));
      } catch (e, stackTrace) {
        _loggingService.error(
          'DocumentReminderService',
          'Error caching updated reminder',
          e,
          stackTrace,
        );
      }
    }

    // Cancel notification
    await _notificationService.cancelNotification(id);

    return updatedReminder;
  }

  /// Create reminders for a document
  Future<List<DocumentReminder>> createDocumentReminders(
      TravelDocument document) async {
    await initialize();

    final reminders = <DocumentReminder>[];

    // Create a reminder 90 days before expiry
    if (document.daysUntilExpiry > 90) {
      final reminder = DocumentReminder(
        id: const Uuid().v4(),
        userId: document.userId,
        documentId: document.id,
        type: DocumentReminderType.expiry,
        title: '${document.name} expires in 90 days',
        message:
            'Your ${document.type.displayName} (${document.name}) will expire in 90 days on ${document.formattedExpiryDate}.',
        reminderDate: document.expiryDate.subtract(const Duration(days: 90)),
        isRead: false,
        isDismissed: false,
        isRecurring: false,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      reminders.add(await addReminder(reminder));
    }

    // Create a reminder 30 days before expiry
    if (document.daysUntilExpiry > 30) {
      final reminder = DocumentReminder(
        id: const Uuid().v4(),
        userId: document.userId,
        documentId: document.id,
        type: DocumentReminderType.expiry,
        title: '${document.name} expires in 30 days',
        message:
            'Your ${document.type.displayName} (${document.name}) will expire in 30 days on ${document.formattedExpiryDate}.',
        reminderDate: document.expiryDate.subtract(const Duration(days: 30)),
        isRead: false,
        isDismissed: false,
        isRecurring: false,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      reminders.add(await addReminder(reminder));
    }

    // Create a reminder 7 days before expiry
    if (document.daysUntilExpiry > 7) {
      final reminder = DocumentReminder(
        id: const Uuid().v4(),
        userId: document.userId,
        documentId: document.id,
        type: DocumentReminderType.expiry,
        title: '${document.name} expires in 7 days',
        message:
            'Your ${document.type.displayName} (${document.name}) will expire in 7 days on ${document.formattedExpiryDate}.',
        reminderDate: document.expiryDate.subtract(const Duration(days: 7)),
        isRead: false,
        isDismissed: false,
        isRecurring: false,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      reminders.add(await addReminder(reminder));
    }

    return reminders;
  }

  /// Update reminders for a document
  Future<List<DocumentReminder>> updateDocumentReminders(
      TravelDocument document) async {
    await initialize();

    // Delete existing reminders for the document
    await deleteDocumentReminders(document.id);

    // Create new reminders
    return createDocumentReminders(document);
  }

  /// Delete reminders for a document
  ///
  /// Returns true if all reminders were successfully deleted, false otherwise
  Future<bool> deleteDocumentReminders(String documentId) async {
    await initialize();

    try {
      final reminders = await getDocumentReminders(documentId);

      if (reminders.isEmpty) {
        return true; // No reminders to delete
      }

      bool allDeleted = true;
      for (final reminder in reminders) {
        try {
          await deleteReminder(reminder.id);
        } catch (e, stackTrace) {
          _loggingService.error(
            'DocumentReminderService',
            'Error deleting reminder ${reminder.id} for document $documentId',
            e,
            stackTrace,
          );
          allDeleted = false;
        }
      }

      return allDeleted;
    } catch (e, stackTrace) {
      _loggingService.error(
        'DocumentReminderService',
        'Error deleting reminders for document $documentId',
        e,
        stackTrace,
      );
      return false;
    }
  }

  /// Schedule a notification for a reminder
  Future<void> _scheduleNotification(DocumentReminder reminder) async {
    if (reminder.isRead || reminder.isDismissed) {
      // Cancel notification if the reminder is read or dismissed
      await _notificationService.cancelNotification(reminder.id);
      return;
    }

    // Schedule notification for the reminder date
    await _notificationService.scheduleNotification(
      id: reminder.id,
      title: reminder.title,
      body: reminder.message,
      scheduledDate: reminder.reminderDate,
      payload: jsonEncode({
        'type': 'document_reminder',
        'id': reminder.id,
        'documentId': reminder.documentId,
      }),
    );
  }

  /// Generate mock data for testing
  Future<void> _generateMockData() async {
    _reminders.clear();

    // Add mock reminders
    _reminders.add(
      DocumentReminder(
        id: '1',
        userId: _authService.currentUser?.uid ?? 'user1',
        documentId: '1', // Passport ID
        type: DocumentReminderType.expiry,
        title: 'Passport expires in 90 days',
        message: 'Your passport will expire in 90 days on May 15, 2031.',
        reminderDate: DateTime.now().add(const Duration(days: 90)),
        isRead: false,
        isDismissed: false,
        isRecurring: false,
        createdAt: DateTime.now().subtract(const Duration(days: 30)),
        updatedAt: DateTime.now().subtract(const Duration(days: 30)),
      ),
    );

    _reminders.add(
      DocumentReminder(
        id: '2',
        userId: _authService.currentUser?.uid ?? 'user1',
        documentId: '2', // Visa ID
        type: DocumentReminderType.expiry,
        title: 'Japan Tourist Visa expires soon',
        message: 'Your Japan Tourist Visa will expire in 7 days.',
        reminderDate: DateTime.now().add(const Duration(days: 7)),
        isRead: false,
        isDismissed: false,
        isRecurring: false,
        createdAt: DateTime.now().subtract(const Duration(days: 7)),
        updatedAt: DateTime.now().subtract(const Duration(days: 7)),
      ),
    );

    // Cache the reminders
    if (_remindersBox != null) {
      for (final reminder in _reminders) {
        await _remindersBox!.put(reminder.id, jsonEncode(reminder.toJson()));
      }
    }

    // Schedule notifications
    for (final reminder in _reminders) {
      await _scheduleNotification(reminder);
    }
  }
}
