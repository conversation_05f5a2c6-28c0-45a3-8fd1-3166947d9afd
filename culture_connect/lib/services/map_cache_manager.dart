import 'dart:convert';
import 'dart:io';
import 'dart:math';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:path_provider/path_provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:http/http.dart' as http;
import 'package:culture_connect/services/logging_service.dart';
import 'package:culture_connect/services/error_handling_service.dart';

/// Provider for the MapCacheManager service.
///
/// This provider creates and configures a [MapCacheManager] instance with the
/// appropriate dependencies injected. It's used throughout the application to
/// access map caching functionality.
///
/// Usage:
/// ```dart
/// final mapCacheManager = ref.watch(mapCacheManagerProvider);
/// await mapCacheManager.cacheMapRegion(...);
/// ```
final mapCacheManagerProvider = Provider<MapCacheManager>((ref) {
  final loggingService = ref.watch(loggingServiceProvider);
  final errorHandlingService = ref.watch(errorHandlingServiceProvider);
  return MapCacheManager(
    loggingService: loggingService,
    errorHandlingService: errorHandlingService,
  );
});

/// A service for managing offline map tile caching.
///
/// The MapCacheManager handles downloading, storing, and managing map tiles for
/// offline use. It provides functionality to:
/// - Cache specific map regions for offline access
/// - Manage cached regions (list, delete, clear)
/// - Track and limit cache size
/// - Provide access to cached map data
///
/// This service is essential for the app's offline functionality, allowing users
/// to access maps even without an internet connection.
class MapCacheManager {
  /// The logging service
  final LoggingService? _loggingService;

  /// The error handling service
  final ErrorHandlingService? _errorHandlingService;

  /// The cache directory
  Directory? _cacheDir;

  /// The maximum cache size in bytes (default: 100MB)
  final int _maxCacheSize;

  /// Constructor
  MapCacheManager({
    LoggingService? loggingService,
    ErrorHandlingService? errorHandlingService,
    int maxCacheSize = 100 * 1024 * 1024, // 100MB
  })  : _loggingService = loggingService,
        _errorHandlingService = errorHandlingService,
        _maxCacheSize = maxCacheSize {
    _initialize();
  }

  /// Initialize the cache manager
  Future<void> _initialize() async {
    try {
      // Get the application documents directory
      final appDir = await getApplicationDocumentsDirectory();

      // Create the cache directory if it doesn't exist
      _cacheDir = Directory('${appDir.path}/map_cache');
      if (!await _cacheDir!.exists()) {
        await _cacheDir!.create(recursive: true);
      }

      _log('Map cache initialized at ${_cacheDir!.path}');

      // Clean up old cache files if needed
      await _cleanupCache();
    } catch (e, stackTrace) {
      _logError('Error initializing map cache', e, stackTrace);
    }
  }

  /// Clean up old cache files if the cache size exceeds the maximum
  Future<void> _cleanupCache() async {
    try {
      if (_cacheDir == null) return;

      // Get all files in the cache directory
      final files = await _cacheDir!.list().toList();

      // Sort files by last modified time (oldest first)
      files.sort((a, b) {
        final aStats = a.statSync();
        final bStats = b.statSync();
        return aStats.modified.compareTo(bStats.modified);
      });

      // Calculate the total cache size
      int totalSize = 0;
      for (final file in files) {
        if (file is File) {
          totalSize += file.lengthSync();
        }
      }

      _log('Current cache size: ${_formatSize(totalSize)}');

      // Delete oldest files until the cache size is below the maximum
      int i = 0;
      while (totalSize > _maxCacheSize && i < files.length) {
        final file = files[i];
        if (file is File) {
          final fileSize = file.lengthSync();
          await file.delete();
          totalSize -= fileSize;
          _log('Deleted cache file: ${file.path} (${_formatSize(fileSize)})');
        }
        i++;
      }

      _log('Cache cleanup complete. New size: ${_formatSize(totalSize)}');
    } catch (e, stackTrace) {
      _logError('Error cleaning up cache', e, stackTrace);
    }
  }

  /// Format a size in bytes to a human-readable string
  String _formatSize(int bytes) {
    if (bytes < 1024) {
      return '$bytes B';
    } else if (bytes < 1024 * 1024) {
      return '${(bytes / 1024).toStringAsFixed(1)} KB';
    } else if (bytes < 1024 * 1024 * 1024) {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    } else {
      return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
    }
  }

  /// Downloads and caches map tiles for a specific region to enable offline use.
  ///
  /// This method downloads all map tiles within the specified [bounds] for zoom levels
  /// from [minZoom] to [maxZoom] inclusive. The tiles are stored in the local cache
  /// directory for later offline access.
  ///
  /// The [onProgress] callback is called periodically with a value between 0.0 and 1.0
  /// indicating the download progress. This can be used to update a progress indicator
  /// in the UI.
  ///
  /// Returns `true` if the caching operation was successful, `false` otherwise.
  ///
  /// Example:
  /// ```dart
  /// final bounds = LatLngBounds(
  ///   northeast: const LatLng(37.78, -122.41),
  ///   southwest: const LatLng(37.77, -122.42),
  /// );
  /// final success = await mapCacheManager.cacheMapRegion(
  ///   bounds,
  ///   12, // minZoom
  ///   15, // maxZoom
  ///   (progress) => setState(() => _downloadProgress = progress),
  /// );
  /// ```
  Future<bool> cacheMapRegion(
    LatLngBounds bounds,
    int minZoom,
    int maxZoom,
    Function(double)? onProgress,
  ) async {
    try {
      if (_cacheDir == null) {
        await _initialize();
        if (_cacheDir == null) return false;
      }

      // Calculate the number of tiles to download
      final tilesToDownload =
          _calculateTilesToDownload(bounds, minZoom, maxZoom);
      _log('Caching map region: ${tilesToDownload.length} tiles');

      // Download tiles
      int downloadedTiles = 0;
      for (final tile in tilesToDownload) {
        await _cacheTile(tile.x, tile.y, tile.zoom);
        downloadedTiles++;

        // Report progress
        if (onProgress != null) {
          onProgress(downloadedTiles / tilesToDownload.length);
        }
      }

      // Save the cached region metadata
      await _saveCachedRegionMetadata(bounds, minZoom, maxZoom);

      _log('Map region cached successfully');
      return true;
    } catch (e, stackTrace) {
      _logError('Error caching map region', e, stackTrace);
      return false;
    }
  }

  /// Calculate the tiles to download for a region
  List<MapTile> _calculateTilesToDownload(
    LatLngBounds bounds,
    int minZoom,
    int maxZoom,
  ) {
    final tiles = <MapTile>[];

    for (int zoom = minZoom; zoom <= maxZoom; zoom++) {
      // Convert bounds to tile coordinates
      final nwTile = _latLngToTile(
          bounds.northeast.latitude, bounds.southwest.longitude, zoom);
      final seTile = _latLngToTile(
          bounds.southwest.latitude, bounds.northeast.longitude, zoom);

      // Add all tiles in the region
      for (int x = nwTile.x; x <= seTile.x; x++) {
        for (int y = nwTile.y; y <= seTile.y; y++) {
          tiles.add(MapTile(x, y, zoom));
        }
      }
    }

    return tiles;
  }

  /// Convert a LatLng to a tile coordinate
  MapTile _latLngToTile(double lat, double lng, int zoom) {
    final n = pow(2, zoom);
    final x = ((lng + 180) / 360 * n).floor();
    final latRad = lat * pi / 180;
    final y = ((1 - log(tan(latRad) + 1 / cos(latRad)) / pi) / 2 * n).floor();
    return MapTile(x, y, zoom);
  }

  /// Cache a single map tile
  Future<void> _cacheTile(int x, int y, int zoom) async {
    try {
      if (_cacheDir == null) return;

      // Generate the tile URL (this is a placeholder, real implementation would use a map tile provider)
      final tileUrl = 'https://tile.openstreetmap.org/$zoom/$x/$y.png';

      // Generate the cache file path
      final cacheFilePath = '${_cacheDir!.path}/${zoom}_${x}_$y.png';

      // Check if the tile is already cached
      final cacheFile = File(cacheFilePath);
      if (await cacheFile.exists()) {
        // Tile is already cached
        return;
      }

      // Download the tile
      final response = await http.get(Uri.parse(tileUrl));
      if (response.statusCode == 200) {
        // Save the tile to the cache
        await cacheFile.writeAsBytes(response.bodyBytes);
        _log('Cached tile: $zoom/$x/$y');
      } else {
        _log('Failed to download tile: $zoom/$x/$y (${response.statusCode})');
      }
    } catch (e, stackTrace) {
      _logError('Error caching tile', e, stackTrace);
    }
  }

  /// Save metadata about a cached region
  Future<void> _saveCachedRegionMetadata(
    LatLngBounds bounds,
    int minZoom,
    int maxZoom,
  ) async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // Get existing cached regions
      final cachedRegionsJson = prefs.getStringList('cached_map_regions') ?? [];
      final cachedRegions = cachedRegionsJson
          .map((json) => jsonDecode(json) as Map<String, dynamic>)
          .toList();

      // Add the new region
      cachedRegions.add({
        'id': DateTime.now().millisecondsSinceEpoch.toString(),
        'name': 'Cached Region ${cachedRegions.length + 1}',
        'bounds': {
          'northeast': {
            'latitude': bounds.northeast.latitude,
            'longitude': bounds.northeast.longitude,
          },
          'southwest': {
            'latitude': bounds.southwest.latitude,
            'longitude': bounds.southwest.longitude,
          },
        },
        'minZoom': minZoom,
        'maxZoom': maxZoom,
        'timestamp': DateTime.now().millisecondsSinceEpoch,
      });

      // Save the updated list
      await prefs.setStringList(
        'cached_map_regions',
        cachedRegions.map((region) => jsonEncode(region)).toList(),
      );

      _log('Saved cached region metadata');
    } catch (e, stackTrace) {
      _logError('Error saving cached region metadata', e, stackTrace);
    }
  }

  /// Retrieves a list of all map regions that have been cached for offline use.
  ///
  /// This method returns information about each cached region, including its
  /// geographic bounds, zoom levels, and when it was cached. This information
  /// can be used to display a list of available offline maps to the user.
  ///
  /// Returns an empty list if no regions are cached or if an error occurs.
  ///
  /// Example:
  /// ```dart
  /// final cachedRegions = await mapCacheManager.getCachedRegions();
  /// for (final region in cachedRegions) {
  ///   print('${region.name} - Cached on ${region.timestamp}');
  /// }
  /// ```
  Future<List<CachedMapRegion>> getCachedRegions() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // Get cached regions
      final cachedRegionsJson = prefs.getStringList('cached_map_regions') ?? [];

      // Parse the regions
      return cachedRegionsJson.map((json) {
        final data = jsonDecode(json) as Map<String, dynamic>;
        final boundsData = data['bounds'] as Map<String, dynamic>;
        final neData = boundsData['northeast'] as Map<String, dynamic>;
        final swData = boundsData['southwest'] as Map<String, dynamic>;

        return CachedMapRegion(
          id: data['id'] as String,
          name: data['name'] as String,
          bounds: LatLngBounds(
            northeast: LatLng(
              neData['latitude'] as double,
              neData['longitude'] as double,
            ),
            southwest: LatLng(
              swData['latitude'] as double,
              swData['longitude'] as double,
            ),
          ),
          minZoom: data['minZoom'] as int,
          maxZoom: data['maxZoom'] as int,
          timestamp:
              DateTime.fromMillisecondsSinceEpoch(data['timestamp'] as int),
        );
      }).toList();
    } catch (e, stackTrace) {
      _logError('Error getting cached regions', e, stackTrace);
      return [];
    }
  }

  /// Deletes a specific cached map region by its ID.
  ///
  /// This method removes the metadata for the specified region from the list of
  /// cached regions. It does not immediately delete the associated tile files,
  /// but they may be removed during the next cache cleanup operation if the
  /// cache size exceeds the maximum allowed size.
  ///
  /// [regionId] is the unique identifier of the region to delete.
  ///
  /// Returns `true` if the region was successfully deleted, `false` otherwise
  /// (e.g., if the region was not found or an error occurred).
  ///
  /// Example:
  /// ```dart
  /// final regions = await mapCacheManager.getCachedRegions();
  /// if (regions.isNotEmpty) {
  ///   final success = await mapCacheManager.deleteCachedRegion(regions[0].id);
  ///   print('Region deleted: $success');
  /// }
  /// ```
  Future<bool> deleteCachedRegion(String regionId) async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // Get existing cached regions
      final cachedRegionsJson = prefs.getStringList('cached_map_regions') ?? [];
      final cachedRegions = cachedRegionsJson
          .map((json) => jsonDecode(json) as Map<String, dynamic>)
          .toList();

      // Find the region to delete
      final regionIndex =
          cachedRegions.indexWhere((region) => region['id'] == regionId);
      if (regionIndex == -1) {
        _log('Region not found: $regionId');
        return false;
      }

      // Remove the region
      cachedRegions.removeAt(regionIndex);

      // Save the updated list
      await prefs.setStringList(
        'cached_map_regions',
        cachedRegions.map((region) => jsonEncode(region)).toList(),
      );

      _log('Deleted cached region: $regionId');

      // Clean up cache files
      await _cleanupCache();

      return true;
    } catch (e, stackTrace) {
      _logError('Error deleting cached region', e, stackTrace);
      return false;
    }
  }

  /// Clears all cached map data, removing all tile files and region metadata.
  ///
  /// This method deletes all files in the cache directory and removes all
  /// cached region metadata from shared preferences. This is useful when the
  /// user wants to free up storage space or reset the offline maps.
  ///
  /// Returns `true` if the cache was successfully cleared, `false` otherwise.
  ///
  /// Example:
  /// ```dart
  /// final button = ElevatedButton(
  ///   onPressed: () async {
  ///     final success = await mapCacheManager.clearCache();
  ///     if (success) {
  ///       ScaffoldMessenger.of(context).showSnackBar(
  ///         const SnackBar(content: Text('Offline maps cleared')),
  ///       );
  ///     }
  ///   },
  ///   child: const Text('Clear Offline Maps'),
  /// );
  /// ```
  Future<bool> clearCache() async {
    try {
      if (_cacheDir == null) return false;

      // Delete all files in the cache directory
      await for (final file in _cacheDir!.list()) {
        if (file is File) {
          await file.delete();
        }
      }

      // Clear cached regions metadata
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('cached_map_regions');

      _log('Map cache cleared');
      return true;
    } catch (e, stackTrace) {
      _logError('Error clearing map cache', e, stackTrace);
      return false;
    }
  }

  /// Gets the total size of all cached map tiles in bytes.
  ///
  /// This method calculates the total size of all files in the cache directory.
  /// It can be used to display the current cache size to the user or to check
  /// if the cache is approaching the maximum allowed size.
  ///
  /// Returns the total size in bytes, or 0 if the cache directory doesn't exist
  /// or an error occurs.
  ///
  /// Example:
  /// ```dart
  /// final cacheSize = await mapCacheManager.getCacheSize();
  /// final formattedSize = _formatSize(cacheSize); // e.g., "15.2 MB"
  /// Text('Offline maps: $formattedSize');
  /// ```
  Future<int> getCacheSize() async {
    try {
      if (_cacheDir == null) return 0;

      int totalSize = 0;
      await for (final file in _cacheDir!.list()) {
        if (file is File) {
          totalSize += file.lengthSync();
        }
      }

      return totalSize;
    } catch (e, stackTrace) {
      _logError('Error getting cache size', e, stackTrace);
      return 0;
    }
  }

  /// Log a message
  void _log(String message) {
    if (_loggingService != null) {
      _loggingService!.debug('MapCacheManager', message);
    } else {
      debugPrint('MapCacheManager: $message');
    }
  }

  /// Log an error
  void _logError(String message, dynamic error, [StackTrace? stackTrace]) {
    if (_loggingService != null) {
      _loggingService!.error('MapCacheManager', message, error, stackTrace);

      if (_errorHandlingService != null) {
        _errorHandlingService!.handleError(
          error: error,
          context: 'MapCacheManager.$message',
          stackTrace: stackTrace ?? StackTrace.current,
          type: ErrorType
              .fileSystem, // Using fileSystem error type for cache errors
          severity: ErrorSeverity.medium,
        );
      }
    } else {
      debugPrint('MapCacheManager ERROR: $message - $error');
      if (stackTrace != null) {
        debugPrint(stackTrace.toString());
      }
    }
  }
}

/// A class representing a map tile in the XYZ tile coordinate system.
///
/// Map tiles are used to divide the world map into a grid of small, square images
/// that can be efficiently loaded and displayed. The XYZ tile coordinate system
/// is a common standard used by many map providers.
///
/// - X coordinates increase from west to east (left to right)
/// - Y coordinates increase from north to south (top to bottom)
/// - Zoom level determines the detail level, with higher values showing more detail
class MapTile {
  /// The X coordinate of the tile (increases from west to east)
  final int x;

  /// The Y coordinate of the tile (increases from north to south)
  final int y;

  /// The zoom level of the tile (higher values = more detail)
  final int zoom;

  /// Creates a new MapTile with the specified coordinates and zoom level.
  ///
  /// [x] is the X coordinate (west to east)
  /// [y] is the Y coordinate (north to south)
  /// [zoom] is the zoom level (typically 0-20, where 0 is the entire world)
  const MapTile(this.x, this.y, this.zoom);

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is MapTile &&
        other.x == x &&
        other.y == y &&
        other.zoom == zoom;
  }

  @override
  int get hashCode => Object.hash(x, y, zoom);
}

/// A class representing a geographic region that has been cached for offline use.
///
/// This class stores metadata about a map region that has been downloaded and
/// cached locally. It includes information about the geographic bounds of the
/// region, the zoom levels that were cached, and when the caching occurred.
///
/// This information is used to:
/// - Display a list of available offline maps to the user
/// - Determine if a particular area is available offline
/// - Manage the lifecycle of cached map data
class CachedMapRegion {
  /// Unique identifier for the cached region
  final String id;

  /// User-friendly name for the cached region (e.g., "Downtown San Francisco")
  final String name;

  /// Geographic boundaries of the cached region
  ///
  /// This defines the rectangular area on the map that has been cached,
  /// specified by its northeast and southwest corners.
  final LatLngBounds bounds;

  /// Minimum zoom level that was cached (lower = less detail, wider area)
  final int minZoom;

  /// Maximum zoom level that was cached (higher = more detail, smaller area)
  final int maxZoom;

  /// Date and time when the region was cached
  ///
  /// This can be used to display to users when the offline data was last updated
  /// and to implement cache expiration policies.
  final DateTime timestamp;

  /// Creates a new CachedMapRegion with the specified properties.
  ///
  /// All parameters are required:
  /// - [id]: Unique identifier for the region
  /// - [name]: User-friendly name for the region
  /// - [bounds]: Geographic boundaries of the region
  /// - [minZoom]: Minimum zoom level cached
  /// - [maxZoom]: Maximum zoom level cached
  /// - [timestamp]: When the region was cached
  const CachedMapRegion({
    required this.id,
    required this.name,
    required this.bounds,
    required this.minZoom,
    required this.maxZoom,
    required this.timestamp,
  });
}

/// Extension methods for double values used in map calculations.
///
/// These extensions provide utility methods for mathematical operations
/// that are needed for map tile calculations but aren't available in
/// the standard library without importing dart:math.
extension DoubleExtension on double {
  /// Raises this double to the power of the given exponent.
  ///
  /// This is a simple implementation of the power function that works
  /// for non-negative integer exponents. For more complex cases or better
  /// performance, use the pow function from dart:math.
  ///
  /// [exponent] must be a non-negative integer.
  ///
  /// Returns this number raised to the power of [exponent].
  double pow(int exponent) {
    if (exponent < 0) {
      throw ArgumentError('Exponent must be non-negative');
    }

    double result = 1.0;
    for (int i = 0; i < exponent; i++) {
      result *= this;
    }
    return result;
  }
}

/// Extension methods for integer values used in map calculations.
///
/// These extensions provide utility methods for mathematical operations
/// that are needed for map tile calculations but aren't available in
/// the standard library without importing dart:math.
extension IntExtension on int {
  /// Raises this integer to the power of the given exponent.
  ///
  /// This is a simple implementation of the power function that works
  /// for non-negative integer exponents. For more complex cases or better
  /// performance, use the pow function from dart:math.
  ///
  /// [exponent] must be a non-negative integer.
  ///
  /// Returns this number raised to the power of [exponent] as an integer.
  int pow(int exponent) {
    if (exponent < 0) {
      throw ArgumentError('Exponent must be non-negative');
    }

    int result = 1;
    for (int i = 0; i < exponent; i++) {
      result *= this;
    }
    return result;
  }
}
