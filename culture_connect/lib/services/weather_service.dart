import 'dart:convert';

// Flutter imports
import 'package:flutter/material.dart';

// Package imports
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';

// Project imports
import 'package:culture_connect/services/error_handling_service.dart';
import 'package:culture_connect/services/logging_service.dart';

/// Provider for the weather service
final weatherServiceProvider = Provider<WeatherService>((ref) {
  final loggingService = ref.watch(loggingServiceProvider);
  final errorHandlingService = ref.watch(errorHandlingServiceProvider);
  return WeatherService(
    loggingService: loggingService,
    errorHandlingService: errorHandlingService,
  );
});

/// Provider for weather data at a specific location
final weatherProvider =
    FutureProvider.family<WeatherData, LatLng>((ref, coordinates) async {
  final weatherService = ref.watch(weatherServiceProvider);
  return await weatherService.getWeatherForLocation(
    coordinates.latitude,
    coordinates.longitude,
  );
});

/// Provider for weather forecast at a specific location
final forecastProvider =
    FutureProvider.family<List<WeatherData>, LatLng>((ref, coordinates) async {
  final weatherService = ref.watch(weatherServiceProvider);
  return await weatherService.getForecastForLocation(
    coordinates.latitude,
    coordinates.longitude,
  );
});

/// A service for fetching weather data
class WeatherService {
  // OpenWeatherMap API key
  // In a real app, this would be stored in environment variables
  static const String _apiKey = 'demo_api_key';

  // Base URL for the OpenWeatherMap API
  static const String _baseUrl = 'https://api.openweathermap.org/data/2.5';

  // Cache duration in minutes
  static const int _cacheDuration = 30;

  // HTTP client
  final http.Client _client = http.Client();

  // Cache key prefix
  static const String _cacheKeyPrefix = 'weather_cache_';

  // For logging
  final LoggingService? _loggingService;
  final ErrorHandlingService? _errorHandlingService;

  // Constructor
  WeatherService({
    LoggingService? loggingService,
    ErrorHandlingService? errorHandlingService,
  })  : _loggingService = loggingService,
        _errorHandlingService = errorHandlingService;

  /// Get current weather for a location
  Future<WeatherData> getWeatherForLocation(
    double latitude,
    double longitude,
  ) async {
    try {
      // Check connectivity
      final connectivityResult = await Connectivity().checkConnectivity();
      final bool isOnline = connectivityResult != ConnectivityResult.none;

      // Check cache first
      final cacheKey = '$_cacheKeyPrefix${latitude}_$longitude';
      final prefs = await SharedPreferences.getInstance();
      final cachedData = prefs.getString(cacheKey);

      if (!isOnline && cachedData != null) {
        _log('Using cached weather data for ($latitude, $longitude)');
        final Map<String, dynamic> data = jsonDecode(cachedData);
        final timestamp = data['timestamp'] as int;
        final now = DateTime.now().millisecondsSinceEpoch;

        // Check if cache is still valid (within cache duration)
        if (now - timestamp < _cacheDuration * 60 * 1000) {
          return WeatherData.fromJson(data['weather']);
        }
      }

      if (!isOnline) {
        return _getMockWeatherData();
      }

      // Fetch from API
      final url = Uri.parse(
        '$_baseUrl/weather?lat=$latitude&lon=$longitude&units=metric&appid=$_apiKey',
      );

      final response = await _client.get(url);

      if (response.statusCode == 200) {
        final Map<String, dynamic> data = jsonDecode(response.body);
        final weatherData = WeatherData.fromJson(data);

        // Cache the data
        await prefs.setString(
            cacheKey,
            jsonEncode({
              'timestamp': DateTime.now().millisecondsSinceEpoch,
              'weather': data,
            }));

        _log('Fetched weather data for ($latitude, $longitude)');
        return weatherData;
      } else {
        _logError(
          'Failed to fetch weather data',
          Exception('API returned ${response.statusCode}: ${response.body}'),
        );
        return _getMockWeatherData();
      }
    } catch (e, stackTrace) {
      _logError('Error getting weather data', e, stackTrace);
      return _getMockWeatherData();
    }
  }

  /// Get weather forecast for a location
  Future<List<WeatherData>> getForecastForLocation(
    double latitude,
    double longitude,
  ) async {
    try {
      // Check connectivity
      final connectivityResult = await Connectivity().checkConnectivity();
      final bool isOnline = connectivityResult != ConnectivityResult.none;

      // Check cache first
      final cacheKey = '${_cacheKeyPrefix}forecast_${latitude}_$longitude';
      final prefs = await SharedPreferences.getInstance();
      final cachedData = prefs.getString(cacheKey);

      if (!isOnline && cachedData != null) {
        _log('Using cached forecast data for ($latitude, $longitude)');
        final Map<String, dynamic> data = jsonDecode(cachedData);
        final timestamp = data['timestamp'] as int;
        final now = DateTime.now().millisecondsSinceEpoch;

        // Check if cache is still valid (within cache duration)
        if (now - timestamp < _cacheDuration * 60 * 1000) {
          final List<dynamic> forecastList = data['forecast'];
          return forecastList
              .map((item) => WeatherData.fromJson(item))
              .toList();
        }
      }

      if (!isOnline) {
        return _getMockForecastData();
      }

      // Fetch from API
      final url = Uri.parse(
        '$_baseUrl/forecast?lat=$latitude&lon=$longitude&units=metric&appid=$_apiKey',
      );

      final response = await _client.get(url);

      if (response.statusCode == 200) {
        final Map<String, dynamic> data = jsonDecode(response.body);
        final List<dynamic> forecastList = data['list'];

        final forecast =
            forecastList.map((item) => WeatherData.fromJson(item)).toList();

        // Cache the data
        await prefs.setString(
            cacheKey,
            jsonEncode({
              'timestamp': DateTime.now().millisecondsSinceEpoch,
              'forecast': forecastList,
            }));

        _log('Fetched forecast data for ($latitude, $longitude)');
        return forecast;
      } else {
        _logError(
          'Failed to fetch forecast data',
          Exception('API returned ${response.statusCode}: ${response.body}'),
        );
        return _getMockForecastData();
      }
    } catch (e, stackTrace) {
      _logError('Error getting forecast data', e, stackTrace);
      return _getMockForecastData();
    }
  }

  /// Get mock weather data for demo purposes
  WeatherData _getMockWeatherData() {
    return WeatherData(
      temperature: 25.0,
      feelsLike: 26.0,
      minTemperature: 22.0,
      maxTemperature: 28.0,
      pressure: 1012,
      humidity: 65,
      weatherCondition: 'Clear',
      weatherDescription: 'clear sky',
      weatherIconCode: '01d',
      windSpeed: 3.5,
      windDirection: 120,
      cloudiness: 10,
      rainVolume: 0.0,
      snowVolume: 0.0,
      timestamp: DateTime.now(),
      sunrise: DateTime.now().subtract(const Duration(hours: 6)),
      sunset: DateTime.now().add(const Duration(hours: 6)),
      timezone: 0,
    );
  }

  /// Get mock forecast data for demo purposes
  List<WeatherData> _getMockForecastData() {
    final now = DateTime.now();
    return List.generate(5, (index) {
      return WeatherData(
        temperature: 25.0 + index,
        feelsLike: 26.0 + index,
        minTemperature: 22.0 + index,
        maxTemperature: 28.0 + index,
        pressure: 1012,
        humidity: 65,
        weatherCondition: index % 2 == 0 ? 'Clear' : 'Clouds',
        weatherDescription: index % 2 == 0 ? 'clear sky' : 'scattered clouds',
        weatherIconCode: index % 2 == 0 ? '01d' : '03d',
        windSpeed: 3.5,
        windDirection: 120,
        cloudiness: 10 * index,
        rainVolume: 0.0,
        snowVolume: 0.0,
        timestamp: now.add(Duration(days: index)),
        sunrise:
            now.add(Duration(days: index)).subtract(const Duration(hours: 6)),
        sunset: now.add(Duration(days: index)).add(const Duration(hours: 6)),
        timezone: 0,
      );
    });
  }

  /// Log a message
  void _log(String message) {
    _loggingService?.debug('WeatherService', message);
    debugPrint('WeatherService: $message');
  }

  /// Log an error
  void _logError(String message, dynamic error, [StackTrace? stackTrace]) {
    _loggingService?.error('WeatherService', message, error, stackTrace);
    _errorHandlingService?.handleError(
        error: error,
        context: 'WeatherService',
        stackTrace: stackTrace,
        additionalData: {'message': message});
    debugPrint('WeatherService Error: $message - $error');
  }

  /// Dispose of resources
  void dispose() {
    _client.close();
  }
}

/// A model representing weather data
class WeatherData {
  final double temperature;
  final double feelsLike;
  final double minTemperature;
  final double maxTemperature;
  final int pressure;
  final int humidity;
  final String weatherCondition;
  final String weatherDescription;
  final String weatherIconCode;
  final double windSpeed;
  final int windDirection;
  final int cloudiness;
  final double rainVolume;
  final double snowVolume;
  final DateTime timestamp;
  final DateTime sunrise;
  final DateTime sunset;
  final int timezone;

  WeatherData({
    required this.temperature,
    required this.feelsLike,
    required this.minTemperature,
    required this.maxTemperature,
    required this.pressure,
    required this.humidity,
    required this.weatherCondition,
    required this.weatherDescription,
    required this.weatherIconCode,
    required this.windSpeed,
    required this.windDirection,
    required this.cloudiness,
    required this.rainVolume,
    required this.snowVolume,
    required this.timestamp,
    required this.sunrise,
    required this.sunset,
    required this.timezone,
  });

  /// Create a WeatherData instance from a JSON map
  factory WeatherData.fromJson(Map<String, dynamic> json) {
    final weather = json['weather'][0];
    final main = json['main'];
    final wind = json['wind'];
    final clouds = json['clouds'];
    final rain = json['rain'];
    final snow = json['snow'];
    final sys = json['sys'];

    return WeatherData(
      temperature: (main['temp'] as num).toDouble(),
      feelsLike: (main['feels_like'] as num).toDouble(),
      minTemperature: (main['temp_min'] as num).toDouble(),
      maxTemperature: (main['temp_max'] as num).toDouble(),
      pressure: main['pressure'] as int,
      humidity: main['humidity'] as int,
      weatherCondition: weather['main'] as String,
      weatherDescription: weather['description'] as String,
      weatherIconCode: weather['icon'] as String,
      windSpeed: (wind['speed'] as num).toDouble(),
      windDirection: (wind['deg'] as num).toInt(),
      cloudiness: clouds['all'] as int,
      rainVolume: rain != null && rain['3h'] != null
          ? (rain['3h'] as num).toDouble()
          : 0.0,
      snowVolume: snow != null && snow['3h'] != null
          ? (snow['3h'] as num).toDouble()
          : 0.0,
      timestamp:
          DateTime.fromMillisecondsSinceEpoch((json['dt'] as int) * 1000),
      sunrise: sys != null && sys['sunrise'] != null
          ? DateTime.fromMillisecondsSinceEpoch((sys['sunrise'] as int) * 1000)
          : DateTime.now().subtract(const Duration(hours: 6)),
      sunset: sys != null && sys['sunset'] != null
          ? DateTime.fromMillisecondsSinceEpoch((sys['sunset'] as int) * 1000)
          : DateTime.now().add(const Duration(hours: 6)),
      timezone: json['timezone'] as int? ?? 0,
    );
  }

  /// Get weather icon URL
  String get weatherIconUrl =>
      'https://openweathermap.org/img/wn/$<EMAIL>';

  /// Get formatted temperature
  String get formattedTemperature => '${temperature.toStringAsFixed(1)}°C';

  /// Get formatted time
  String get formattedTime {
    final hour = timestamp.hour.toString().padLeft(2, '0');
    final minute = timestamp.minute.toString().padLeft(2, '0');
    return '$hour:$minute';
  }

  /// Get formatted date
  String get formattedDate {
    final day = timestamp.day.toString().padLeft(2, '0');
    final month = timestamp.month.toString().padLeft(2, '0');
    final year = timestamp.year;
    return '$day/$month/$year';
  }
}
