import 'dart:async';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import 'package:uuid/uuid.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:culture_connect/models/message_model.dart';
import 'package:culture_connect/models/group_chat_model.dart';
import 'package:culture_connect/models/translation/language_model.dart';
import 'package:culture_connect/models/translation/message_translation_metadata.dart';
import 'package:culture_connect/models/translation/group_translation_model.dart';
import 'package:culture_connect/models/translation/translation_cultural_context.dart';
import 'package:culture_connect/models/translation/translation_slang_idiom.dart';
import 'package:culture_connect/models/translation/translation_pronunciation.dart';
import 'package:culture_connect/models/offline/sync_status.dart';
import 'package:culture_connect/providers/shared_preferences_provider.dart';
import 'package:culture_connect/services/voice_translation/message_translation_service.dart';
import 'package:culture_connect/services/voice_translation/cultural_context_service.dart';
import 'package:culture_connect/services/voice_translation/slang_idiom_service.dart';
import 'package:culture_connect/services/voice_translation/pronunciation_service.dart';
import 'package:culture_connect/services/language_detection_service.dart';
import 'package:culture_connect/services/translation_metrics_service.dart';
import 'package:culture_connect/services/translation_sync_service.dart';
import 'package:culture_connect/database/translation_database_helper.dart';
import 'package:culture_connect/utils/lru_cache.dart';

/// A service for handling group conversation translations
class GroupTranslationService {
  /// The HTTP client
  final http.Client _client;

  /// The shared preferences instance
  final SharedPreferences _prefs;

  /// The message translation service
  final MessageTranslationService _messageTranslationService;

  /// The cultural context service
  final CulturalContextService _culturalContextService;

  /// The slang and idiom service
  final SlangIdiomService _slangIdiomService;

  /// The pronunciation service
  final PronunciationService _pronunciationService;

  /// The language detection service
  final LanguageDetectionService _languageDetectionService;

  /// The translation metrics service
  final TranslationMetricsService _metricsService;

  /// The translation database helper
  final TranslationDatabaseHelper _dbHelper;

  /// The translation sync service
  final TranslationSyncService? _syncService;

  /// Whether the device is online
  bool _isOnline = true;

  /// Cache for group translation settings
  final Map<String, GroupTranslationSettings> _groupSettingsCache = {};

  /// LRU cache for group message translations
  final TranslationLRUCache<String, GroupMessageTranslation> _translationCache;

  /// Map for storing translations that need to be persisted
  final Map<String, GroupMessageTranslation> _persistentTranslations = {};

  /// Stream controller for group translation events
  final StreamController<String> _groupTranslationEventsController =
      StreamController<String>.broadcast();

  /// The maximum number of translations to cache in memory
  static const int _maxCacheSize = 500;

  /// The maximum number of translations to persist
  static const int _maxPersistentCacheSize = 1000;

  /// Creates a new group translation service
  GroupTranslationService(
      this._client,
      this._prefs,
      this._messageTranslationService,
      this._culturalContextService,
      this._slangIdiomService,
      this._pronunciationService,
      this._languageDetectionService,
      this._metricsService,
      this._dbHelper,
      [this._syncService])
      : _translationCache =
            TranslationLRUCache<String, GroupMessageTranslation>(
                capacity: _maxCacheSize) {
    _loadCachedData();
    _initConnectivityListener();
  }

  /// Initialize connectivity listener
  void _initConnectivityListener() {
    Connectivity().checkConnectivity().then((result) {
      _isOnline = result != ConnectivityResult.none;
    });

    Connectivity().onConnectivityChanged.listen((result) {
      final wasOnline = _isOnline;
      _isOnline = result != ConnectivityResult.none;

      // If we just came back online, sync translations
      if (!wasOnline && _isOnline && _syncService != null) {
        _syncService!.syncTranslations();
      }
    });
  }

  /// Stream of group translation events
  Stream<String> get groupTranslationEvents =>
      _groupTranslationEventsController.stream;

  /// Load cached data from SQLite and shared preferences
  Future<void> _loadCachedData() async {
    try {
      // First try to load from SQLite
      try {
        // Load group settings from SQLite
        final db = await _dbHelper.database;
        final settingsMaps = await db.query('group_translation_settings');

        for (final map in settingsMaps) {
          try {
            final groupId = map['group_id'] as String;
            final settingsJson = map['settings'] as String;
            final settings =
                GroupTranslationSettings.fromJson(jsonDecode(settingsJson));
            _groupSettingsCache[groupId] = settings;
          } catch (e) {
            debugPrint('Error parsing group settings: $e');
          }
        }

        // Load message translations from SQLite
        final translationMaps = await db.query(
          'group_message_translations',
          limit: _maxPersistentCacheSize,
          orderBy: 'translated_at DESC',
        );

        int count = 0;
        for (final map in translationMaps) {
          try {
            final messageId = map['message_id'] as String;
            final originalText = map['original_text'] as String;
            final originalLanguageCode =
                map['original_language_code'] as String;
            final translationsJson = map['translations'] as String;
            final translatedAt = DateTime.fromMillisecondsSinceEpoch(
                map['translated_at'] as int);

            final translations =
                Map<String, String>.from(jsonDecode(translationsJson));

            final translation = GroupMessageTranslation(
              messageId: messageId,
              originalText: originalText,
              originalLanguageCode: originalLanguageCode,
              translations: translations,
              translatedAt: translatedAt,
            );

            _persistentTranslations[messageId] = translation;
            _translationCache.put(messageId, translation);
            count++;
          } catch (e) {
            debugPrint('Error parsing message translation: $e');
          }
        }

        debugPrint('Loaded $count translations from SQLite');
        return;
      } catch (e) {
        debugPrint(
            'Error loading from SQLite, falling back to shared preferences: $e');
      }

      // Fall back to shared preferences if SQLite fails
      // Load group settings
      final groupSettingsJson = _prefs.getString('group_translation_settings');
      if (groupSettingsJson != null) {
        final Map<String, dynamic> decoded = jsonDecode(groupSettingsJson);
        decoded.forEach((key, value) {
          _groupSettingsCache[key] =
              GroupTranslationSettings.fromJson(value as Map<String, dynamic>);
        });
      }

      // Load message translations
      final messageTranslationsJson =
          _prefs.getString('group_message_translations');
      if (messageTranslationsJson != null) {
        final Map<String, dynamic> decoded =
            jsonDecode(messageTranslationsJson);

        // Limit the number of translations loaded to avoid memory issues
        int count = 0;
        decoded.forEach((key, value) {
          if (count < _maxPersistentCacheSize) {
            final translation =
                GroupMessageTranslation.fromJson(value as Map<String, dynamic>);
            _persistentTranslations[key] = translation;
            _translationCache.put(key, translation);
            count++;
          }
        });

        debugPrint('Loaded $count translations from shared preferences');

        // Migrate to SQLite
        _migrateToSQLite();
      }
    } catch (e) {
      debugPrint('Error loading cached group translation data: $e');
    }
  }

  /// Migrate data from shared preferences to SQLite
  Future<void> _migrateToSQLite() async {
    try {
      debugPrint('Migrating translation data to SQLite');

      // Migrate group settings
      for (final entry in _groupSettingsCache.entries) {
        await _dbHelper.saveGroupTranslationSettings(
          entry.key,
          entry.value,
          syncStatus: SyncStatus.synced,
        );
      }

      // Migrate message translations
      for (final entry in _persistentTranslations.entries) {
        final messageId = entry.key;
        final translation = entry.value;

        // Extract group ID from message ID (assuming format: groupId_messageId)
        final parts = messageId.split('_');
        final groupId = parts.isNotEmpty ? parts[0] : 'unknown';

        await _dbHelper.saveGroupMessageTranslation(
          translation,
          groupId,
          createdLocally: false,
          modifiedLocally: false,
          syncStatus: SyncStatus.synced,
        );
      }

      debugPrint('Migration to SQLite completed');

      // Clear shared preferences data after successful migration
      await _prefs.remove('group_translation_settings');
      await _prefs.remove('group_message_translations');
    } catch (e) {
      debugPrint('Error migrating to SQLite: $e');
    }
  }

  /// Save cached data to SQLite
  Future<void> _saveCachedData() async {
    try {
      // Save group settings to SQLite
      for (final entry in _groupSettingsCache.entries) {
        await _dbHelper.saveGroupTranslationSettings(
          entry.key,
          entry.value,
        );
      }

      // Save message translations to SQLite
      for (final entry in _persistentTranslations.entries) {
        final messageId = entry.key;
        final translation = entry.value;

        // Extract group ID from message ID (assuming format: groupId_messageId)
        final parts = messageId.split('_');
        final groupId = parts.isNotEmpty ? parts[0] : 'unknown';

        await _dbHelper.saveGroupMessageTranslation(
          translation,
          groupId,
        );
      }

      // Log cache metrics
      final metrics = _translationCache.getMetrics();
      debugPrint('Translation cache metrics: $metrics');

      // Trigger sync if online
      if (_isOnline && _syncService != null) {
        _syncService!.syncTranslations();
      }
    } catch (e) {
      debugPrint('Error saving cached group translation data: $e');

      // Fall back to shared preferences if SQLite fails
      try {
        // Save group settings
        final groupSettingsJson = <String, dynamic>{};
        _groupSettingsCache.forEach((key, value) {
          groupSettingsJson[key] = value.toJson();
        });
        await _prefs.setString(
            'group_translation_settings', jsonEncode(groupSettingsJson));

        // Save message translations
        final messageTranslationsJson = <String, dynamic>{};
        _persistentTranslations.forEach((key, value) {
          messageTranslationsJson[key] = value.toJson();
        });
        await _prefs.setString(
            'group_message_translations', jsonEncode(messageTranslationsJson));

        debugPrint('Saved to shared preferences as fallback');
      } catch (e) {
        debugPrint('Error saving to shared preferences: $e');
      }
    }
  }

  /// Get group translation settings
  Future<GroupTranslationSettings> getGroupTranslationSettings(
      String groupId) async {
    // Check cache first
    if (_groupSettingsCache.containsKey(groupId)) {
      return _groupSettingsCache[groupId]!;
    }

    // If not in cache, create default settings
    final settings = GroupTranslationSettings(
      groupId: groupId,
      participantPreferences: {},
    );

    // Cache the settings
    _groupSettingsCache[groupId] = settings;
    await _saveCachedData();

    return settings;
  }

  /// Update group translation settings
  Future<GroupTranslationSettings> updateGroupTranslationSettings(
    GroupTranslationSettings settings,
  ) async {
    // Update cache
    _groupSettingsCache[settings.groupId] = settings;
    await _saveCachedData();

    // Notify listeners
    _groupTranslationEventsController
        .add('settings_updated_${settings.groupId}');

    return settings;
  }

  /// Add or update a participant's language preference
  Future<GroupTranslationSettings> updateParticipantPreference(
    String groupId,
    ParticipantLanguagePreference preference,
  ) async {
    // Get current settings
    final settings = await getGroupTranslationSettings(groupId);

    // Update participant preference
    final updatedSettings = settings.updateParticipantPreference(preference);

    // Save updated settings
    return updateGroupTranslationSettings(updatedSettings);
  }

  /// Remove a participant's language preference
  Future<GroupTranslationSettings> removeParticipantPreference(
    String groupId,
    String userId,
  ) async {
    // Get current settings
    final settings = await getGroupTranslationSettings(groupId);

    // Remove participant preference
    final updatedSettings = settings.removeParticipantPreference(userId);

    // Save updated settings
    return updateGroupTranslationSettings(updatedSettings);
  }

  /// Get translation for a group message
  Future<GroupMessageTranslation?> getGroupMessageTranslation(
      String messageId) async {
    // Check in-memory cache first (fastest)
    final cachedTranslation = _translationCache.get(messageId);
    if (cachedTranslation != null) {
      return cachedTranslation;
    }

    // Check persistent cache next
    if (_persistentTranslations.containsKey(messageId)) {
      final translation = _persistentTranslations[messageId]!;
      // Add to in-memory cache for faster access next time
      _translationCache.put(messageId, translation);
      return translation;
    }

    // Check SQLite database
    try {
      final translation = await _dbHelper.getGroupMessageTranslation(messageId);
      if (translation != null) {
        // Add to in-memory cache for faster access next time
        _translationCache.put(messageId, translation);
        _persistentTranslations[messageId] = translation;
        return translation;
      }
    } catch (e) {
      debugPrint('Error getting translation from SQLite: $e');
    }

    return null;
  }

  /// Translate a message for a group
  Future<GroupMessageTranslation> translateGroupMessage(
    MessageModel message,
    GroupTranslationSettings settings,
  ) async {
    final stopwatch = Stopwatch()..start();
    bool isCached = false;
    bool isOfflineTranslation = !_isOnline;

    // Check if we already have a translation for this message
    final existingTranslation = await getGroupMessageTranslation(message.id);
    if (existingTranslation != null) {
      stopwatch.stop();
      isCached = true;

      // Record metrics for cached translation
      await _metricsService.recordTranslation(
        sourceLanguage: existingTranslation.originalLanguageCode,
        targetLanguage: 'multiple',
        durationMs: stopwatch.elapsedMilliseconds,
        isCached: true,
      );

      return existingTranslation;
    }

    // Detect the language if not specified
    String originalLanguageCode = message.originalLanguage;
    if (settings.autoDetectLanguages && originalLanguageCode == 'auto') {
      if (_isOnline) {
        originalLanguageCode =
            await _languageDetectionService.detectLanguage(message.text);
      } else {
        // Fallback to English if offline and can't detect language
        originalLanguageCode = 'en';
      }
    }

    // Get all target languages needed for the group
    final targetLanguages = settings.uniqueLanguages
        .where((lang) => lang.code != originalLanguageCode)
        .map((lang) => lang.code)
        .toList();

    // Create a new translation object
    final translation = GroupMessageTranslation(
      messageId: message.id,
      originalText: message.text,
      originalLanguageCode: originalLanguageCode,
      translations: {},
      translatedAt: DateTime.now(),
    );

    // If offline and no cached translation, add a placeholder translation
    if (!_isOnline) {
      // Create a placeholder translation for each target language
      for (final targetLanguage in targetLanguages) {
        final offlineMessage = "[Offline] ${message.text}";
        final updatedTranslation = translation.addTranslation(
          targetLanguage,
          offlineMessage,
        );

        // Update our translation object in both caches
        _translationCache.put(message.id, updatedTranslation);
        _persistentTranslations[message.id] = updatedTranslation;
      }

      // Save to SQLite with pending sync status
      final parts = message.id.split('_');
      final groupId = parts.isNotEmpty ? parts[0] : message.chatId;

      await _dbHelper.saveGroupMessageTranslation(
        _translationCache.get(message.id) ?? translation,
        groupId,
        createdLocally: true,
        modifiedLocally: true,
        syncStatus: SyncStatus.pending,
      );

      // Notify listeners
      _groupTranslationEventsController
          .add('message_translated_offline_${message.id}');

      stopwatch.stop();

      // Record metrics for offline translation
      await _metricsService.recordTranslation(
        sourceLanguage: originalLanguageCode,
        targetLanguage: 'multiple',
        durationMs: stopwatch.elapsedMilliseconds,
        isCached: false,
        isOffline: true,
      );

      return _translationCache.get(message.id) ?? translation;
    }

    // Online translation flow
    for (final targetLanguage in targetLanguages) {
      try {
        final translationStopwatch = Stopwatch()..start();

        // Create a temporary message model for translation
        final tempMessage = MessageModel(
          id: const Uuid().v4(),
          chatId: message.chatId,
          senderId: message.senderId,
          recipientId: '',
          text: message.text,
          timestamp: DateTime.now(),
          status: MessageStatus.sent,
          type: MessageType.text,
          originalLanguage: originalLanguageCode,
        );

        // Use the message translation service to translate
        final metadata = await _messageTranslationService.translateMessage(
          tempMessage,
          targetLanguage,
        );

        translationStopwatch.stop();

        // Record metrics for individual language translation
        await _metricsService.recordTranslation(
          sourceLanguage: originalLanguageCode,
          targetLanguage: targetLanguage,
          durationMs: translationStopwatch.elapsedMilliseconds,
          isCached: false,
        );

        // Add the translation to our group translation
        final updatedTranslation = translation.addTranslation(
          targetLanguage,
          metadata.translatedText,
        );

        // Update our translation object in both caches
        _translationCache.put(message.id, updatedTranslation);
        _persistentTranslations[message.id] = updatedTranslation;

        // Save to SQLite
        final parts = message.id.split('_');
        final groupId = parts.isNotEmpty ? parts[0] : message.chatId;

        await _dbHelper.saveGroupMessageTranslation(
          updatedTranslation,
          groupId,
          createdLocally: true,
          modifiedLocally: true,
          syncStatus: SyncStatus.pending,
        );

        // Notify listeners
        _groupTranslationEventsController
            .add('message_translated_${message.id}');
      } catch (e) {
        debugPrint('Error translating message to $targetLanguage: $e');
        await _metricsService.recordFailedTranslation();
      }
    }

    stopwatch.stop();

    // Record metrics for the entire translation process
    await _metricsService.recordTranslation(
      sourceLanguage: originalLanguageCode,
      targetLanguage: 'multiple',
      durationMs: stopwatch.elapsedMilliseconds,
      isCached: isCached,
      isOffline: isOfflineTranslation,
    );

    // If we've exceeded the max persistent cache size, remove the oldest entries
    if (_persistentTranslations.length > _maxPersistentCacheSize) {
      final keysToRemove = _persistentTranslations.keys
          .take(_persistentTranslations.length - _maxPersistentCacheSize)
          .toList();
      for (final key in keysToRemove) {
        _persistentTranslations.remove(key);
      }
    }

    // Trigger sync if online
    if (_isOnline && _syncService != null) {
      _syncService!.syncTranslations();
    }

    return _translationCache.get(message.id) ?? translation;
  }

  /// Determine if a translation should be persisted
  bool _shouldPersistTranslation(MessageModel message) {
    // Persist translations for messages that are likely to be important
    // This is a simple heuristic that can be improved

    // Persist messages with longer text (likely more important)
    if (message.text.length > 50) {
      return true;
    }

    // Persist recent messages (within the last 24 hours)
    final oneDayAgo = DateTime.now().subtract(const Duration(days: 1));
    if (message.timestamp.isAfter(oneDayAgo)) {
      return true;
    }

    // By default, don't persist
    return false;
  }

  /// Get the appropriate translation for a user
  Future<String> getTranslationForUser(
    MessageModel message,
    String userId,
    GroupTranslationSettings settings,
  ) async {
    // Get the user's preferred language
    final preference = settings.participantPreferences[userId];
    if (preference == null) {
      // If no preference, return original text
      return message.text;
    }

    // If the message is already in the user's preferred language, return original text
    if (message.originalLanguage == preference.preferredLanguage.code) {
      return message.text;
    }

    // If auto-translate is disabled for this user, return original text
    if (!preference.autoTranslate) {
      return message.text;
    }

    // Get the translation for this message
    final translation = await translateGroupMessage(message, settings);

    // Get the translation for the user's preferred language
    final translatedText = translation
        .getTranslationForLanguage(preference.preferredLanguage.code);

    // If no translation is available, return original text
    if (translatedText == null) {
      return message.text;
    }

    return translatedText;
  }

  /// Get full translation metadata for a message and user
  Future<MessageTranslationMetadata?> getTranslationMetadataForUser(
    MessageModel message,
    String userId,
    GroupTranslationSettings settings,
  ) async {
    // Get the user's preferred language
    final preference = settings.participantPreferences[userId];
    if (preference == null) {
      // If no preference, return null
      return null;
    }

    // If the message is already in the user's preferred language, return null
    if (message.originalLanguage == preference.preferredLanguage.code) {
      return null;
    }

    // If auto-translate is disabled for this user, return null
    if (!preference.autoTranslate) {
      return null;
    }

    try {
      // Create a temporary message model for translation
      final tempMessage = MessageModel(
        id: message.id,
        chatId: message.chatId,
        senderId: message.senderId,
        recipientId: '',
        text: message.text,
        timestamp: message.timestamp,
        status: message.status,
        type: message.type,
        originalLanguage: message.originalLanguage,
      );

      // Use the message translation service to get full metadata
      return await _messageTranslationService.translateMessage(
        tempMessage,
        preference.preferredLanguage.code,
      );
    } catch (e) {
      debugPrint('Error getting translation metadata: $e');
      return null;
    }
  }

  /// Clear the translation cache for a group
  Future<void> clearGroupTranslationCache(String groupId) async {
    // Remove group settings
    _groupSettingsCache.remove(groupId);

    // Remove message translations from in-memory cache
    final keysToRemove = <String>[];
    for (final key in _translationCache.keys) {
      if (key.startsWith(groupId)) {
        keysToRemove.add(key);
      }
    }

    for (final key in keysToRemove) {
      _translationCache.remove(key);
    }

    // Remove from persistent cache
    _persistentTranslations
        .removeWhere((key, value) => key.startsWith(groupId));

    // Save updated cache
    await _saveCachedData();

    // Notify listeners
    _groupTranslationEventsController.add('cache_cleared_$groupId');
  }

  /// Get cache metrics
  Map<String, dynamic> getCacheMetrics() {
    final metrics = <String, dynamic>{
      'inMemoryCacheSize': _translationCache.size,
      'inMemoryCacheCapacity': _translationCache.capacity,
      'persistentCacheSize': _persistentTranslations.length,
      'persistentCacheCapacity': _maxPersistentCacheSize,
      'groupSettingsCacheSize': _groupSettingsCache.length,
    };

    // Add LRU cache metrics
    metrics.addAll(_translationCache.getMetrics());

    return metrics;
  }

  /// Dispose resources
  void dispose() {
    _groupTranslationEventsController.close();
    _client.close();
  }
}

/// Provider for the group translation service
final groupTranslationServiceProvider =
    Provider<GroupTranslationService>((ref) {
  final client = http.Client();
  final prefs = ref.watch(sharedPreferencesProvider);
  final messageTranslationService =
      ref.watch(messageTranslationServiceProvider);
  final culturalContextService = ref.watch(culturalContextServiceProvider);
  final slangIdiomService = ref.watch(slangIdiomServiceProvider);
  final pronunciationService = ref.watch(pronunciationServiceProvider);
  final languageDetectionService = ref.watch(languageDetectionServiceProvider);
  final metricsService = ref.watch(translationMetricsServiceProvider);
  final dbHelper = TranslationDatabaseHelper();
  final syncService = ref.watch(translationSyncServiceProvider);

  final service = GroupTranslationService(
    client,
    prefs,
    messageTranslationService,
    culturalContextService,
    slangIdiomService,
    pronunciationService,
    languageDetectionService,
    metricsService,
    dbHelper,
    syncService,
  );

  ref.onDispose(() {
    service.dispose();
  });

  return service;
});
