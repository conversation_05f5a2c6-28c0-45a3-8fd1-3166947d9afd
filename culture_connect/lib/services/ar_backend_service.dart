import 'dart:convert';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:dio/dio.dart';
import 'package:path_provider/path_provider.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:culture_connect/models/landmark.dart';
import 'package:culture_connect/models/ar_model.dart';

/// A service that integrates with backend services for AR content.
class ARBackendService {
  // Dio client for API requests
  final Dio _dio = Dio();
  
  // Base URL for the AR content API
  final String _baseUrl = 'https://api.cultureconnect.com/ar';
  
  // API endpoints
  final String _landmarksEndpoint = '/landmarks';
  final String _modelsEndpoint = '/models';
  final String _texturesEndpoint = '/textures';
  final String _userContentEndpoint = '/user-content';
  
  // Cache directory
  late final Directory _cacheDirectory;
  
  // Connectivity status
  bool _isOnline = true;
  
  // Singleton instance
  static final ARBackendService _instance = ARBackendService._internal();
  
  // Factory constructor
  factory ARBackendService() => _instance;
  
  // Internal constructor
  ARBackendService._internal();
  
  /// Initialize the AR backend service.
  Future<void> initialize() async {
    // Initialize cache directory
    _cacheDirectory = await getTemporaryDirectory();
    
    // Initialize connectivity monitoring
    Connectivity().onConnectivityChanged.listen((ConnectivityResult result) {
      _isOnline = result != ConnectivityResult.none;
    });
    
    // Check current connectivity
    final connectivityResult = await Connectivity().checkConnectivity();
    _isOnline = connectivityResult != ConnectivityResult.none;
    
    // Configure Dio
    _configureDio();
  }
  
  /// Configure Dio client.
  void _configureDio() {
    _dio.options.baseUrl = _baseUrl;
    _dio.options.connectTimeout = const Duration(seconds: 10);
    _dio.options.receiveTimeout = const Duration(seconds: 10);
    _dio.options.headers = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    };
    
    // Add interceptors for logging, authentication, etc.
    _dio.interceptors.add(LogInterceptor(
      requestBody: true,
      responseBody: true,
    ));
    
    // Add authentication interceptor
    _dio.interceptors.add(InterceptorsWrapper(
      onRequest: (options, handler) {
        // Add authentication token
        // options.headers['Authorization'] = 'Bearer $token';
        return handler.next(options);
      },
      onError: (DioException e, handler) {
        // Handle authentication errors
        if (e.response?.statusCode == 401) {
          // Refresh token or redirect to login
        }
        return handler.next(e);
      },
    ));
    
    // Add cache interceptor
    _dio.interceptors.add(InterceptorsWrapper(
      onRequest: (options, handler) async {
        // Check if we're offline and if we have a cached response
        if (!_isOnline) {
          final cachedResponse = await _getCachedResponse(options.uri.toString());
          if (cachedResponse != null) {
            return handler.resolve(
              Response(
                requestOptions: options,
                data: cachedResponse,
                statusCode: 200,
              ),
            );
          }
        }
        return handler.next(options);
      },
      onResponse: (response, handler) async {
        // Cache the response
        await _cacheResponse(
          response.requestOptions.uri.toString(),
          response.data,
        );
        return handler.next(response);
      },
    ));
  }
  
  /// Get cached response.
  Future<dynamic> _getCachedResponse(String url) async {
    try {
      final cacheKey = _getCacheKey(url);
      final cacheFile = File('${_cacheDirectory.path}/$cacheKey.json');
      
      if (await cacheFile.exists()) {
        final cacheData = await cacheFile.readAsString();
        return json.decode(cacheData);
      }
      
      return null;
    } catch (e) {
      debugPrint('Error getting cached response: $e');
      return null;
    }
  }
  
  /// Cache response.
  Future<void> _cacheResponse(String url, dynamic data) async {
    try {
      final cacheKey = _getCacheKey(url);
      final cacheFile = File('${_cacheDirectory.path}/$cacheKey.json');
      
      await cacheFile.writeAsString(json.encode(data));
    } catch (e) {
      debugPrint('Error caching response: $e');
    }
  }
  
  /// Get cache key from URL.
  String _getCacheKey(String url) {
    return url.replaceAll(RegExp(r'[^a-zA-Z0-9]'), '_');
  }
  
  /// Get landmarks from the backend.
  Future<List<Landmark>> getLandmarks({
    double? latitude,
    double? longitude,
    double? radius,
    List<String>? tags,
  }) async {
    try {
      final response = await _dio.get(
        _landmarksEndpoint,
        queryParameters: {
          if (latitude != null) 'latitude': latitude,
          if (longitude != null) 'longitude': longitude,
          if (radius != null) 'radius': radius,
          if (tags != null && tags.isNotEmpty) 'tags': tags.join(','),
        },
      );
      
      final List<dynamic> data = response.data['landmarks'];
      return data.map((json) => Landmark.fromJson(json)).toList();
    } catch (e) {
      debugPrint('Error getting landmarks: $e');
      
      // If offline, return cached landmarks
      if (!_isOnline) {
        final cachedLandmarks = await _getCachedLandmarks();
        if (cachedLandmarks.isNotEmpty) {
          return cachedLandmarks;
        }
      }
      
      // Return mock landmarks for demo purposes
      return _getMockLandmarks();
    }
  }
  
  /// Get cached landmarks.
  Future<List<Landmark>> _getCachedLandmarks() async {
    try {
      final cacheFile = File('${_cacheDirectory.path}/landmarks.json');
      
      if (await cacheFile.exists()) {
        final cacheData = await cacheFile.readAsString();
        final List<dynamic> data = json.decode(cacheData);
        return data.map((json) => Landmark.fromJson(json)).toList();
      }
      
      return [];
    } catch (e) {
      debugPrint('Error getting cached landmarks: $e');
      return [];
    }
  }
  
  /// Get mock landmarks for demo purposes.
  List<Landmark> _getMockLandmarks() {
    return [
      Landmark(
        id: '1',
        name: 'National Museum',
        description: 'A beautiful museum showcasing the rich cultural heritage of the country.',
        imageUrl: 'https://example.com/museum.jpg',
        location: {'latitude': 0.0, 'longitude': 0.0},
        rating: 4.5,
        reviewCount: 120,
        tags: ['Museum', 'History', 'Culture'],
        historicalSignificance: 'Built in 1950, this museum houses artifacts dating back to the 15th century.',
        arContent: {'modelUrl': 'https://example.com/models/museum.glb'},
        translations: {'fr': 'Musée National', 'es': 'Museo Nacional'},
      ),
      Landmark(
        id: '2',
        name: 'Freedom Square',
        description: 'A historic square where the declaration of independence was signed.',
        imageUrl: 'https://example.com/square.jpg',
        location: {'latitude': 0.1, 'longitude': 0.1},
        rating: 4.2,
        reviewCount: 85,
        tags: ['Historic', 'Square', 'Monument'],
        historicalSignificance: 'This square was the site of the famous independence declaration in 1960.',
        arContent: {'modelUrl': 'https://example.com/models/square.glb'},
        translations: {'fr': 'Place de la Liberté', 'es': 'Plaza de la Libertad'},
      ),
    ];
  }
  
  /// Get AR model from the backend.
  Future<ARModel?> getARModel(String modelId) async {
    try {
      final response = await _dio.get('$_modelsEndpoint/$modelId');
      return ARModel.fromMap(response.data);
    } catch (e) {
      debugPrint('Error getting AR model: $e');
      
      // If offline, return cached model
      if (!_isOnline) {
        final cachedModel = await _getCachedARModel(modelId);
        if (cachedModel != null) {
          return cachedModel;
        }
      }
      
      return null;
    }
  }
  
  /// Get cached AR model.
  Future<ARModel?> _getCachedARModel(String modelId) async {
    try {
      final cacheFile = File('${_cacheDirectory.path}/model_$modelId.json');
      
      if (await cacheFile.exists()) {
        final cacheData = await cacheFile.readAsString();
        return ARModel.fromMap(json.decode(cacheData));
      }
      
      return null;
    } catch (e) {
      debugPrint('Error getting cached AR model: $e');
      return null;
    }
  }
  
  /// Download AR model file.
  Future<File?> downloadARModelFile(String modelUrl) async {
    try {
      final modelFileName = modelUrl.split('/').last;
      final modelFile = File('${_cacheDirectory.path}/$modelFileName');
      
      // Check if the model file already exists
      if (await modelFile.exists()) {
        return modelFile;
      }
      
      // Download the model file
      final response = await _dio.get(
        modelUrl,
        options: Options(responseType: ResponseType.bytes),
      );
      
      await modelFile.writeAsBytes(response.data);
      return modelFile;
    } catch (e) {
      debugPrint('Error downloading AR model file: $e');
      return null;
    }
  }
  
  /// Upload user-created AR content.
  Future<bool> uploadUserARContent({
    required String name,
    required String description,
    required File modelFile,
    required File imageFile,
    List<File>? textureFiles,
    Map<String, dynamic>? metadata,
  }) async {
    try {
      // Create form data
      final formData = FormData.fromMap({
        'name': name,
        'description': description,
        'model': await MultipartFile.fromFile(modelFile.path),
        'image': await MultipartFile.fromFile(imageFile.path),
        if (metadata != null) 'metadata': json.encode(metadata),
      });
      
      // Add texture files
      if (textureFiles != null) {
        for (int i = 0; i < textureFiles.length; i++) {
          formData.files.add(
            MapEntry(
              'texture_$i',
              await MultipartFile.fromFile(textureFiles[i].path),
            ),
          );
        }
      }
      
      // Upload content
      await _dio.post(
        _userContentEndpoint,
        data: formData,
      );
      
      return true;
    } catch (e) {
      debugPrint('Error uploading user AR content: $e');
      return false;
    }
  }
  
  /// Share AR content with other users.
  Future<bool> shareARContent({
    required String contentId,
    required List<String> recipientIds,
    String? message,
  }) async {
    try {
      await _dio.post(
        '$_userContentEndpoint/$contentId/share',
        data: {
          'recipientIds': recipientIds,
          if (message != null) 'message': message,
        },
      );
      
      return true;
    } catch (e) {
      debugPrint('Error sharing AR content: $e');
      return false;
    }
  }
}

/// A provider for the AR backend service.
final arBackendServiceProvider = Provider<ARBackendService>((ref) {
  return ARBackendService();
});
