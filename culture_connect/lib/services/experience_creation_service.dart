import 'dart:io';
import 'package:uuid/uuid.dart';

import 'package:culture_connect/models/experience_creation_model.dart';

/// Service for creating and managing experiences.
class ExperienceCreationService {
  final String _baseUrl = 'https://api.cultureconnect.com/v1';
  final Uuid _uuid = const Uuid();

  // Mock data for development
  final List<ExperienceCreationModel> _mockExperiences = [];

  ExperienceCreationService() {
    _initializeMockData();
  }

  void _initializeMockData() {
    // Create some mock experiences for development
    _mockExperiences.addAll([
      ExperienceCreationModel(
        id: 'exp_001',
        title: 'Historical City Tour',
        description:
            'Explore the rich history of our city with a knowledgeable guide.',
        category: 'History',
        tags: ['History', 'Walking Tour', 'Architecture'],
        price: 49.99,
        currency: 'USD',
        durationMinutes: 120,
        maxParticipants: 10,
        meetingPoint: 'City Hall',
        meetingPointCoordinates: {
          'latitude': 40.7128,
          'longitude': -74.0060,
        },
        includedItems: ['Guided tour', 'Entry tickets', 'Bottled water'],
        excludedItems: ['Transportation', 'Meals'],
        requirements: ['Comfortable walking shoes', 'Sunscreen'],
        cancellationPolicy:
            'Full refund if cancelled 24 hours before the experience.',
        languages: ['English', 'Spanish'],
        accessibilityFeatures: ['Wheelchair accessible', 'Hearing assistance'],
        imageUrls: [
          'https://picsum.photos/id/1031/800/600',
          'https://picsum.photos/id/1032/800/600',
          'https://picsum.photos/id/1033/800/600',
        ],
        isPrivate: false,
        isDraft: false,
        guideId: 'guide_001',
        createdAt: DateTime.now().subtract(const Duration(days: 30)),
        updatedAt: DateTime.now().subtract(const Duration(days: 15)),
      ),
      ExperienceCreationModel(
        id: 'exp_002',
        title: 'Local Cuisine Experience',
        description: 'Taste the authentic local cuisine with a food expert.',
        category: 'Food',
        tags: ['Food', 'Culinary', 'Local'],
        price: 79.99,
        currency: 'USD',
        durationMinutes: 180,
        maxParticipants: 8,
        meetingPoint: 'Central Market',
        meetingPointCoordinates: {
          'latitude': 40.7228,
          'longitude': -74.0060,
        },
        includedItems: ['Food tastings', 'Drinks', 'Recipe booklet'],
        excludedItems: ['Transportation'],
        requirements: ['No dietary restrictions'],
        cancellationPolicy:
            'Full refund if cancelled 48 hours before the experience.',
        languages: ['English', 'French'],
        accessibilityFeatures: ['Wheelchair accessible'],
        imageUrls: [
          'https://picsum.photos/id/292/800/600',
          'https://picsum.photos/id/293/800/600',
          'https://picsum.photos/id/294/800/600',
        ],
        isPrivate: false,
        isDraft: false,
        guideId: 'guide_001',
        createdAt: DateTime.now().subtract(const Duration(days: 20)),
        updatedAt: DateTime.now().subtract(const Duration(days: 10)),
      ),
      ExperienceCreationModel(
        id: 'exp_003',
        title: 'Night Photography Workshop',
        description:
            'Learn how to capture stunning night photos with a professional photographer.',
        category: 'Photography',
        tags: ['Photography', 'Night', 'Workshop'],
        price: 99.99,
        currency: 'USD',
        durationMinutes: 240,
        maxParticipants: 6,
        meetingPoint: 'City Viewpoint',
        meetingPointCoordinates: {
          'latitude': 40.7328,
          'longitude': -74.0160,
        },
        includedItems: ['Photography tips booklet', 'Tripod rental'],
        excludedItems: ['Camera', 'Transportation'],
        requirements: [
          'DSLR or mirrorless camera',
          'Basic photography knowledge'
        ],
        cancellationPolicy:
            'Full refund if cancelled 72 hours before the experience.',
        languages: ['English'],
        accessibilityFeatures: [],
        imageUrls: [
          'https://picsum.photos/id/1039/800/600',
          'https://picsum.photos/id/1040/800/600',
          'https://picsum.photos/id/1041/800/600',
        ],
        isPrivate: true,
        isDraft: true,
        guideId: 'guide_001',
        createdAt: DateTime.now().subtract(const Duration(days: 5)),
        updatedAt: DateTime.now().subtract(const Duration(days: 2)),
      ),
    ]);
  }

  /// Get all experiences created by the current guide.
  Future<List<ExperienceCreationModel>> getGuideExperiences() async {
    try {
      // In a real app, we would make an API call here
      // final response = await http.get(
      //   Uri.parse('$_baseUrl/experiences/guide'),
      //   headers: {'Authorization': 'Bearer $token'},
      // );
      // if (response.statusCode == 200) {
      //   final List<dynamic> data = json.decode(response.body);
      //   return data.map((json) => ExperienceCreationModel.fromJson(json)).toList();
      // } else {
      //   throw Exception('Failed to load experiences');
      // }

      // For development, return mock data
      await Future.delayed(
          const Duration(milliseconds: 800)); // Simulate network delay
      return _mockExperiences;
    } catch (e) {
      throw Exception('Failed to load experiences: $e');
    }
  }

  /// Get an experience by ID.
  Future<ExperienceCreationModel?> getExperienceById(
      String experienceId) async {
    try {
      // In a real app, we would make an API call here
      // final response = await http.get(
      //   Uri.parse('$_baseUrl/experiences/$experienceId'),
      //   headers: {'Authorization': 'Bearer $token'},
      // );
      // if (response.statusCode == 200) {
      //   final data = json.decode(response.body);
      //   return ExperienceCreationModel.fromJson(data);
      // } else if (response.statusCode == 404) {
      //   return null;
      // } else {
      //   throw Exception('Failed to load experience');
      // }

      // For development, return mock data
      await Future.delayed(
          const Duration(milliseconds: 500)); // Simulate network delay
      final experience = _mockExperiences.firstWhere(
        (exp) => exp.id == experienceId,
        orElse: () => throw Exception('Experience not found'),
      );
      return experience;
    } catch (e) {
      throw Exception('Failed to load experience: $e');
    }
  }

  /// Create a new experience.
  Future<ExperienceCreationModel> createExperience(
      ExperienceCreationModel experience) async {
    try {
      // In a real app, we would make an API call here
      // final response = await http.post(
      //   Uri.parse('$_baseUrl/experiences'),
      //   headers: {
      //     'Authorization': 'Bearer $token',
      //     'Content-Type': 'application/json',
      //   },
      //   body: json.encode(experience.toJson()),
      // );
      // if (response.statusCode == 201) {
      //   final data = json.decode(response.body);
      //   return ExperienceCreationModel.fromJson(data);
      // } else {
      //   throw Exception('Failed to create experience');
      // }

      // For development, create a new mock experience
      await Future.delayed(
          const Duration(milliseconds: 800)); // Simulate network delay
      final newExperience = experience.copyWith(
        id: _uuid.v4(),
        guideId: 'guide_001',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );
      _mockExperiences.add(newExperience);
      return newExperience;
    } catch (e) {
      throw Exception('Failed to create experience: $e');
    }
  }

  /// Update an existing experience.
  Future<ExperienceCreationModel> updateExperience(
      ExperienceCreationModel experience) async {
    try {
      // In a real app, we would make an API call here
      // final response = await http.put(
      //   Uri.parse('$_baseUrl/experiences/${experience.id}'),
      //   headers: {
      //     'Authorization': 'Bearer $token',
      //     'Content-Type': 'application/json',
      //   },
      //   body: json.encode(experience.toJson()),
      // );
      // if (response.statusCode == 200) {
      //   final data = json.decode(response.body);
      //   return ExperienceCreationModel.fromJson(data);
      // } else {
      //   throw Exception('Failed to update experience');
      // }

      // For development, update the mock experience
      await Future.delayed(
          const Duration(milliseconds: 800)); // Simulate network delay
      final index =
          _mockExperiences.indexWhere((exp) => exp.id == experience.id);
      if (index == -1) {
        throw Exception('Experience not found');
      }
      final updatedExperience = experience.copyWith(
        updatedAt: DateTime.now(),
      );
      _mockExperiences[index] = updatedExperience;
      return updatedExperience;
    } catch (e) {
      throw Exception('Failed to update experience: $e');
    }
  }

  /// Delete an experience.
  Future<void> deleteExperience(String experienceId) async {
    try {
      // In a real app, we would make an API call here
      // final response = await http.delete(
      //   Uri.parse('$_baseUrl/experiences/$experienceId'),
      //   headers: {'Authorization': 'Bearer $token'},
      // );
      // if (response.statusCode != 204) {
      //   throw Exception('Failed to delete experience');
      // }

      // For development, delete the mock experience
      await Future.delayed(
          const Duration(milliseconds: 500)); // Simulate network delay
      final index =
          _mockExperiences.indexWhere((exp) => exp.id == experienceId);
      if (index == -1) {
        throw Exception('Experience not found');
      }
      _mockExperiences.removeAt(index);
    } catch (e) {
      throw Exception('Failed to delete experience: $e');
    }
  }

  /// Upload an image for an experience.
  Future<String> uploadExperienceImage(File imageFile) async {
    try {
      // In a real app, we would make an API call here
      // final request = http.MultipartRequest(
      //   'POST',
      //   Uri.parse('$_baseUrl/experiences/images'),
      // );
      // request.headers['Authorization'] = 'Bearer $token';
      // request.files.add(await http.MultipartFile.fromPath(
      //   'image',
      //   imageFile.path,
      // ));
      // final response = await request.send();
      // if (response.statusCode == 201) {
      //   final responseData = await response.stream.bytesToString();
      //   final data = json.decode(responseData);
      //   return data['imageUrl'] as String;
      // } else {
      //   throw Exception('Failed to upload image');
      // }

      // For development, return a mock image URL
      await Future.delayed(
          const Duration(seconds: 2)); // Simulate network delay
      final randomId = _uuid.v4().substring(0, 8);
      return 'https://picsum.photos/id/${1000 + _mockExperiences.length}/800/600?random=$randomId';
    } catch (e) {
      throw Exception('Failed to upload image: $e');
    }
  }

  /// Publish an experience (change from draft to published).
  Future<ExperienceCreationModel> publishExperience(String experienceId) async {
    try {
      // In a real app, we would make an API call here
      // final response = await http.post(
      //   Uri.parse('$_baseUrl/experiences/$experienceId/publish'),
      //   headers: {'Authorization': 'Bearer $token'},
      // );
      // if (response.statusCode == 200) {
      //   final data = json.decode(response.body);
      //   return ExperienceCreationModel.fromJson(data);
      // } else {
      //   throw Exception('Failed to publish experience');
      // }

      // For development, update the mock experience
      await Future.delayed(
          const Duration(milliseconds: 500)); // Simulate network delay
      final index =
          _mockExperiences.indexWhere((exp) => exp.id == experienceId);
      if (index == -1) {
        throw Exception('Experience not found');
      }
      final experience = _mockExperiences[index];
      final publishedExperience = experience.copyWith(
        isDraft: false,
        updatedAt: DateTime.now(),
      );
      _mockExperiences[index] = publishedExperience;
      return publishedExperience;
    } catch (e) {
      throw Exception('Failed to publish experience: $e');
    }
  }
}
