import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:uuid/uuid.dart';
import 'package:culture_connect/models/loyalty/loyalty_points_transaction.dart';
import 'package:culture_connect/models/loyalty/loyalty_program_model.dart';
import 'package:culture_connect/models/loyalty/loyalty_reward.dart';

/// Service for handling loyalty program
class LoyaltyService {
  final Box<String> _loyaltyProgramBox;
  final Box<String> _loyaltyRewardsBox;
  final Uuid _uuid = const Uuid();
  
  // Stream controllers
  final _loyaltyProgramController = StreamController<LoyaltyProgramModel?>.broadcast();
  final _loyaltyRewardsController = StreamController<List<LoyaltyReward>>.broadcast();
  
  /// Creates a new loyalty service
  LoyaltyService()
      : _loyaltyProgramBox = Hive.box<String>('loyalty_program'),
        _loyaltyRewardsBox = Hive.box<String>('loyalty_rewards') {
    _loadInitialData();
  }
  
  /// Loads initial data
  Future<void> _loadInitialData() async {
    // Initialize loyalty program if it doesn't exist
    if (!_loyaltyProgramBox.containsKey('user_loyalty_program')) {
      await _initializeLoyaltyProgram();
    }
    
    // Initialize rewards if they don't exist
    if (_loyaltyRewardsBox.isEmpty) {
      await _initializeRewards();
    }
    
    // Notify listeners of initial data
    _notifyLoyaltyProgramListeners();
    _notifyLoyaltyRewardsListeners();
  }
  
  /// Initializes the loyalty program
  Future<void> _initializeLoyaltyProgram() async {
    final loyaltyProgram = LoyaltyProgramModel(
      id: _uuid.v4(),
      name: 'CultureConnect Rewards',
      description: 'Earn points for every booking and redeem them for exclusive rewards.',
      pointsBalance: 0,
      lifetimePoints: 0,
      tier: LoyaltyTier.bronze,
      enrollmentDate: DateTime.now(),
      lastActivityDate: DateTime.now(),
      pointsExpirationDate: DateTime.now().add(const Duration(days: 365)),
      pointsHistory: [],
      availableRewards: [],
      redeemedRewards: [],
    );
    
    await _loyaltyProgramBox.put(
      'user_loyalty_program',
      jsonEncode(loyaltyProgram.toJson()),
    );
  }
  
  /// Initializes the rewards
  Future<void> _initializeRewards() async {
    final rewards = <LoyaltyReward>[
      LoyaltyReward(
        id: _uuid.v4(),
        name: '10% Discount on Next Booking',
        description: 'Get a 10% discount on your next booking.',
        type: LoyaltyRewardType.discount,
        status: LoyaltyRewardStatus.available,
        pointsRequired: 500,
        value: 10.0,
        expirationDate: DateTime.now().add(const Duration(days: 365)),
        termsAndConditions: 'Valid for one-time use. Cannot be combined with other offers.',
      ),
      LoyaltyReward(
        id: _uuid.v4(),
        name: 'Free Room Upgrade',
        description: 'Upgrade your room to the next category for free.',
        type: LoyaltyRewardType.roomUpgrade,
        status: LoyaltyRewardStatus.available,
        pointsRequired: 1000,
        minimumTier: LoyaltyTier.silver,
        expirationDate: DateTime.now().add(const Duration(days: 365)),
        termsAndConditions: 'Subject to availability. Must be redeemed at check-in.',
      ),
      LoyaltyReward(
        id: _uuid.v4(),
        name: 'Free Night Stay',
        description: 'Enjoy a complimentary night at any of our partner hotels.',
        type: LoyaltyRewardType.freeNight,
        status: LoyaltyRewardStatus.available,
        pointsRequired: 5000,
        minimumTier: LoyaltyTier.gold,
        expirationDate: DateTime.now().add(const Duration(days: 365)),
        termsAndConditions: 'Subject to availability. Blackout dates may apply.',
      ),
      LoyaltyReward(
        id: _uuid.v4(),
        name: 'Airport Transfer',
        description: 'Complimentary airport transfer to your hotel.',
        type: LoyaltyRewardType.airportTransfer,
        status: LoyaltyRewardStatus.available,
        pointsRequired: 2000,
        minimumTier: LoyaltyTier.silver,
        expirationDate: DateTime.now().add(const Duration(days: 365)),
        termsAndConditions: 'Must be booked at least 48 hours in advance.',
      ),
      LoyaltyReward(
        id: _uuid.v4(),
        name: 'Spa Treatment Voucher',
        description: 'Enjoy a complimentary spa treatment at select hotels.',
        type: LoyaltyRewardType.spaTreatment,
        status: LoyaltyRewardStatus.available,
        pointsRequired: 3000,
        minimumTier: LoyaltyTier.gold,
        expirationDate: DateTime.now().add(const Duration(days: 365)),
        termsAndConditions: 'Subject to availability. Must be booked in advance.',
      ),
      LoyaltyReward(
        id: _uuid.v4(),
        name: 'Restaurant Voucher',
        description: 'Enjoy a meal at select hotel restaurants.',
        type: LoyaltyRewardType.restaurantVoucher,
        status: LoyaltyRewardStatus.available,
        pointsRequired: 2500,
        value: 50.0,
        currency: 'USD',
        minimumTier: LoyaltyTier.silver,
        expirationDate: DateTime.now().add(const Duration(days: 365)),
        termsAndConditions: 'Valid at participating restaurants only.',
      ),
      LoyaltyReward(
        id: _uuid.v4(),
        name: 'Gift Card',
        description: 'Redeem your points for a gift card.',
        type: LoyaltyRewardType.giftCard,
        status: LoyaltyRewardStatus.available,
        pointsRequired: 5000,
        value: 100.0,
        currency: 'USD',
        minimumTier: LoyaltyTier.gold,
        expirationDate: DateTime.now().add(const Duration(days: 365)),
        termsAndConditions: 'Valid at participating retailers only.',
      ),
      LoyaltyReward(
        id: _uuid.v4(),
        name: 'Exclusive Experience',
        description: 'Access to an exclusive cultural experience.',
        type: LoyaltyRewardType.experience,
        status: LoyaltyRewardStatus.available,
        pointsRequired: 10000,
        minimumTier: LoyaltyTier.platinum,
        expirationDate: DateTime.now().add(const Duration(days: 365)),
        termsAndConditions: 'Subject to availability. Must be booked in advance.',
      ),
    ];
    
    for (final reward in rewards) {
      await _loyaltyRewardsBox.put(
        reward.id,
        jsonEncode(reward.toJson()),
      );
    }
  }
  
  /// Notifies loyalty program listeners
  void _notifyLoyaltyProgramListeners() {
    _loyaltyProgramController.add(getLoyaltyProgram());
  }
  
  /// Notifies loyalty rewards listeners
  void _notifyLoyaltyRewardsListeners() {
    _loyaltyRewardsController.add(getLoyaltyRewards());
  }
  
  /// Gets the loyalty program
  LoyaltyProgramModel? getLoyaltyProgram() {
    final json = _loyaltyProgramBox.get('user_loyalty_program');
    if (json == null) return null;
    
    try {
      final data = jsonDecode(json) as Map<String, dynamic>;
      return LoyaltyProgramModel.fromJson(data);
    } catch (e) {
      debugPrint('Error parsing loyalty program: $e');
      return null;
    }
  }
  
  /// Gets all loyalty rewards
  List<LoyaltyReward> getLoyaltyRewards() {
    final rewards = <LoyaltyReward>[];
    
    for (final key in _loyaltyRewardsBox.keys) {
      final json = _loyaltyRewardsBox.get(key);
      if (json == null) continue;
      
      try {
        final data = jsonDecode(json) as Map<String, dynamic>;
        rewards.add(LoyaltyReward.fromJson(data));
      } catch (e) {
        debugPrint('Error parsing loyalty reward: $e');
      }
    }
    
    return rewards;
  }
  
  /// Gets a loyalty reward by ID
  LoyaltyReward? getLoyaltyReward(String id) {
    final json = _loyaltyRewardsBox.get(id);
    if (json == null) return null;
    
    try {
      final data = jsonDecode(json) as Map<String, dynamic>;
      return LoyaltyReward.fromJson(data);
    } catch (e) {
      debugPrint('Error parsing loyalty reward: $e');
      return null;
    }
  }
  
  /// Earns points for a booking
  Future<LoyaltyProgramModel?> earnPointsForBooking({
    required String bookingId,
    required String bookingDescription,
    required double bookingAmount,
    required String currency,
  }) async {
    final program = getLoyaltyProgram();
    if (program == null) return null;
    
    // Calculate points to earn (1 point per dollar spent, multiplied by tier multiplier)
    final pointsToEarn = (bookingAmount * program.tier.pointsMultiplier).round();
    
    // Create a new transaction
    final transaction = LoyaltyPointsTransaction(
      id: _uuid.v4(),
      type: LoyaltyPointsTransactionType.booking,
      points: pointsToEarn,
      description: 'Points earned for booking: $bookingDescription',
      referenceId: bookingId,
      date: DateTime.now(),
    );
    
    // Update points balance and history
    final updatedPointsHistory = List<LoyaltyPointsTransaction>.from(program.pointsHistory)
      ..add(transaction);
    
    final updatedPointsBalance = program.pointsBalance + pointsToEarn;
    final updatedLifetimePoints = program.lifetimePoints + pointsToEarn;
    
    // Determine the new tier based on lifetime points
    LoyaltyTier newTier = program.tier;
    for (final tier in LoyaltyTier.values.reversed) {
      if (updatedLifetimePoints >= tier.pointsRequired) {
        newTier = tier;
        break;
      }
    }
    
    // Update the loyalty program
    final updatedProgram = program.copyWith(
      pointsBalance: updatedPointsBalance,
      lifetimePoints: updatedLifetimePoints,
      tier: newTier,
      lastActivityDate: DateTime.now(),
      pointsHistory: updatedPointsHistory,
    );
    
    // Save the updated program
    await _loyaltyProgramBox.put(
      'user_loyalty_program',
      jsonEncode(updatedProgram.toJson()),
    );
    
    // Notify listeners
    _notifyLoyaltyProgramListeners();
    
    return updatedProgram;
  }
  
  /// Redeems a reward
  Future<LoyaltyProgramModel?> redeemReward(String rewardId) async {
    final program = getLoyaltyProgram();
    if (program == null) return null;
    
    final reward = getLoyaltyReward(rewardId);
    if (reward == null) {
      throw Exception('Reward not found');
    }
    
    // Check if the reward is available
    if (!reward.isAvailable) {
      throw Exception('Reward is not available');
    }
    
    // Check if the user has enough points
    if (program.pointsBalance < reward.pointsRequired) {
      throw Exception('Not enough points to redeem this reward');
    }
    
    // Check if the user meets the minimum tier requirement
    if (reward.minimumTier != null && program.tier.index < reward.minimumTier!.index) {
      throw Exception('Your loyalty tier is not high enough to redeem this reward');
    }
    
    // Create a new transaction
    final transaction = LoyaltyPointsTransaction(
      id: _uuid.v4(),
      type: LoyaltyPointsTransactionType.redemption,
      points: -reward.pointsRequired,
      description: 'Points redeemed for reward: ${reward.name}',
      referenceId: reward.id,
      date: DateTime.now(),
    );
    
    // Update points balance and history
    final updatedPointsHistory = List<LoyaltyPointsTransaction>.from(program.pointsHistory)
      ..add(transaction);
    
    final updatedPointsBalance = program.pointsBalance - reward.pointsRequired;
    
    // Update the reward status
    final updatedReward = reward.copyWith(
      status: LoyaltyRewardStatus.redeemed,
      redemptionDate: DateTime.now(),
      redemptionCode: _generateRedemptionCode(),
    );
    
    // Update the loyalty program
    final updatedAvailableRewards = List<LoyaltyReward>.from(program.availableRewards)
      ..removeWhere((r) => r.id == reward.id);
    
    final updatedRedeemedRewards = List<LoyaltyReward>.from(program.redeemedRewards)
      ..add(updatedReward);
    
    final updatedProgram = program.copyWith(
      pointsBalance: updatedPointsBalance,
      lastActivityDate: DateTime.now(),
      pointsHistory: updatedPointsHistory,
      availableRewards: updatedAvailableRewards,
      redeemedRewards: updatedRedeemedRewards,
    );
    
    // Save the updated reward
    await _loyaltyRewardsBox.put(
      reward.id,
      jsonEncode(updatedReward.toJson()),
    );
    
    // Save the updated program
    await _loyaltyProgramBox.put(
      'user_loyalty_program',
      jsonEncode(updatedProgram.toJson()),
    );
    
    // Notify listeners
    _notifyLoyaltyProgramListeners();
    _notifyLoyaltyRewardsListeners();
    
    return updatedProgram;
  }
  
  /// Generates a random redemption code
  String _generateRedemptionCode() {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    final random = _uuid.v4().replaceAll('-', '').substring(0, 8).toUpperCase();
    return 'CC-$random';
  }
  
  /// Stream of loyalty program updates
  Stream<LoyaltyProgramModel?> get loyaltyProgramStream => _loyaltyProgramController.stream;
  
  /// Stream of loyalty rewards updates
  Stream<List<LoyaltyReward>> get loyaltyRewardsStream => _loyaltyRewardsController.stream;
  
  /// Disposes of resources
  void dispose() {
    _loyaltyProgramController.close();
    _loyaltyRewardsController.close();
  }
}

/// Provider for the loyalty service
final loyaltyServiceProvider = Provider<LoyaltyService>((ref) {
  final service = LoyaltyService();
  
  ref.onDispose(() {
    service.dispose();
  });
  
  return service;
});
