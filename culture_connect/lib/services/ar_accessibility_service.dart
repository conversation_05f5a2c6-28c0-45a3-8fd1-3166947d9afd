// Flutter imports
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

// Package imports
import 'package:flutter_riverpod/flutter_riverpod.dart';

/// A service that provides accessibility features for the AR experience.
class ARAccessibilityService {
  // Accessibility settings
  bool _isHighContrastEnabled = false;
  bool _isLargeTextEnabled = false;
  bool _isScreenReaderEnabled = false;
  bool _isReducedMotionEnabled = false;
  bool _isHapticFeedbackEnabled = true;
  bool _isAudioGuidanceEnabled = false;
  bool _isGestureSimplificationEnabled = false;

  // Text-to-speech settings
  double _speechRate = 1.0;
  double _speechPitch = 1.0;
  String _speechLanguage = 'en-US';

  // Color settings
  Color _primaryAccessibilityColor = Colors.yellow;
  Color _secondaryAccessibilityColor = Colors.black;

  // Getters
  bool get isHighContrastEnabled => _isHighContrastEnabled;
  bool get isLargeTextEnabled => _isLargeTextEnabled;
  bool get isScreenReaderEnabled => _isScreenReaderEnabled;
  bool get isReducedMotionEnabled => _isReducedMotionEnabled;
  bool get isHapticFeedbackEnabled => _isHapticFeedbackEnabled;
  bool get isAudioGuidanceEnabled => _isAudioGuidanceEnabled;
  bool get isGestureSimplificationEnabled => _isGestureSimplificationEnabled;

  double get speechRate => _speechRate;
  double get speechPitch => _speechPitch;
  String get speechLanguage => _speechLanguage;

  Color get primaryAccessibilityColor => _primaryAccessibilityColor;
  Color get secondaryAccessibilityColor => _secondaryAccessibilityColor;

  // Singleton instance
  static final ARAccessibilityService _instance =
      ARAccessibilityService._internal();

  // Factory constructor
  factory ARAccessibilityService() => _instance;

  // Internal constructor
  ARAccessibilityService._internal();

  /// Initialize the accessibility service.
  Future<void> initialize() async {
    // In a real app, this would load settings from shared preferences
    // For now, we'll just simulate loading settings
    await Future.delayed(const Duration(milliseconds: 500));

    // Check system accessibility settings
    _checkSystemAccessibilitySettings();
  }

  /// Check system accessibility settings.
  void _checkSystemAccessibilitySettings() {
    // In a real app, this would check the system accessibility settings
    // For now, we'll just use default values
  }

  /// Toggle high contrast mode.
  void toggleHighContrast(bool value) {
    _isHighContrastEnabled = value;
  }

  /// Toggle large text mode.
  void toggleLargeText(bool value) {
    _isLargeTextEnabled = value;
  }

  /// Toggle screen reader mode.
  void toggleScreenReader(bool value) {
    _isScreenReaderEnabled = value;
  }

  /// Toggle reduced motion mode.
  void toggleReducedMotion(bool value) {
    _isReducedMotionEnabled = value;
  }

  /// Toggle haptic feedback.
  void toggleHapticFeedback(bool value) {
    _isHapticFeedbackEnabled = value;
  }

  /// Toggle audio guidance.
  void toggleAudioGuidance(bool value) {
    _isAudioGuidanceEnabled = value;
  }

  /// Toggle gesture simplification.
  void toggleGestureSimplification(bool value) {
    _isGestureSimplificationEnabled = value;
  }

  /// Set speech rate.
  void setSpeechRate(double value) {
    _speechRate = value;
  }

  /// Set speech pitch.
  void setSpeechPitch(double value) {
    _speechPitch = value;
  }

  /// Set speech language.
  void setSpeechLanguage(String value) {
    _speechLanguage = value;
  }

  /// Set primary accessibility color.
  void setPrimaryAccessibilityColor(Color value) {
    _primaryAccessibilityColor = value;
  }

  /// Set secondary accessibility color.
  void setSecondaryAccessibilityColor(Color value) {
    _secondaryAccessibilityColor = value;
  }

  /// Get the appropriate text style based on accessibility settings.
  TextStyle getAccessibleTextStyle(TextStyle baseStyle) {
    if (_isLargeTextEnabled) {
      baseStyle = baseStyle.copyWith(
        fontSize: (baseStyle.fontSize ?? 14) * 1.5,
      );
    }

    if (_isHighContrastEnabled) {
      baseStyle = baseStyle.copyWith(
        color: _primaryAccessibilityColor,
        backgroundColor: _secondaryAccessibilityColor,
      );
    }

    return baseStyle;
  }

  /// Get the appropriate icon size based on accessibility settings.
  double getAccessibleIconSize(double baseSize) {
    if (_isLargeTextEnabled) {
      return baseSize * 1.5;
    }
    return baseSize;
  }

  /// Get the appropriate padding based on accessibility settings.
  EdgeInsetsGeometry getAccessiblePadding(EdgeInsetsGeometry basePadding) {
    if (_isLargeTextEnabled) {
      if (basePadding is EdgeInsets) {
        return basePadding * 1.5;
      } else {
        // For other types of EdgeInsetsGeometry, we'll just return the original
        return basePadding;
      }
    }
    return basePadding;
  }

  /// Get the appropriate animation duration based on accessibility settings.
  Duration getAccessibleAnimationDuration(Duration baseDuration) {
    if (_isReducedMotionEnabled) {
      return Duration.zero;
    }
    return baseDuration;
  }

  /// Perform haptic feedback if enabled.
  void performHapticFeedback(HapticFeedbackType type) {
    if (!_isHapticFeedbackEnabled) return;

    switch (type) {
      case HapticFeedbackType.light:
        HapticFeedback.lightImpact();
        break;
      case HapticFeedbackType.medium:
        HapticFeedback.mediumImpact();
        break;
      case HapticFeedbackType.heavy:
        HapticFeedback.heavyImpact();
        break;
      case HapticFeedbackType.selection:
        HapticFeedback.selectionClick();
        break;
      case HapticFeedbackType.vibrate:
        HapticFeedback.vibrate();
        break;
    }
  }

  /// Speak text if screen reader is enabled.
  void speak(String text) {
    if (!_isScreenReaderEnabled && !_isAudioGuidanceEnabled) return;

    // In a real app, this would use a text-to-speech engine
    // For now, we'll just print the text
    debugPrint('Speaking: $text');
  }

  /// Get a simplified gesture detector if gesture simplification is enabled.
  Widget getAccessibleGestureDetector({
    required Widget child,
    required VoidCallback onTap,
    GestureTapCallback? onDoubleTap,
    GestureLongPressCallback? onLongPress,
  }) {
    if (_isGestureSimplificationEnabled) {
      // In simplified mode, we only use tap gestures
      return GestureDetector(
        onTap: () {
          performHapticFeedback(HapticFeedbackType.light);
          onTap();
        },
        child: child,
      );
    } else {
      // In normal mode, we use all gestures
      return GestureDetector(
        onTap: () {
          performHapticFeedback(HapticFeedbackType.light);
          onTap();
        },
        onDoubleTap: onDoubleTap != null
            ? () {
                performHapticFeedback(HapticFeedbackType.medium);
                onDoubleTap();
              }
            : null,
        onLongPress: onLongPress != null
            ? () {
                performHapticFeedback(HapticFeedbackType.heavy);
                onLongPress();
              }
            : null,
        child: child,
      );
    }
  }

  /// Get an accessible button with appropriate styling.
  Widget getAccessibleButton({
    required VoidCallback onPressed,
    required Widget child,
    ButtonStyle? style,
  }) {
    final ButtonStyle effectiveStyle = _isLargeTextEnabled
        ? ElevatedButton.styleFrom(
            padding: const EdgeInsets.all(16),
            textStyle: const TextStyle(fontSize: 18),
          ).merge(style ?? const ButtonStyle())
        : style ?? const ButtonStyle();

    return ElevatedButton(
      onPressed: () {
        performHapticFeedback(HapticFeedbackType.light);
        onPressed();
      },
      style: effectiveStyle,
      child: child,
    );
  }

  /// Get an accessible icon button with appropriate styling.
  Widget getAccessibleIconButton({
    required VoidCallback onPressed,
    required Icon icon,
    String? tooltip,
  }) {
    return IconButton(
      onPressed: () {
        performHapticFeedback(HapticFeedbackType.light);
        onPressed();
      },
      icon: Icon(
        icon.icon,
        size: getAccessibleIconSize(icon.size ?? 24),
        color: _isHighContrastEnabled ? _primaryAccessibilityColor : icon.color,
      ),
      tooltip: tooltip,
      padding: _isLargeTextEnabled
          ? const EdgeInsets.all(12)
          : const EdgeInsets.all(8),
    );
  }

  /// Get an accessible card with appropriate styling.
  Widget getAccessibleCard({
    required Widget child,
    Color? color,
    double? elevation,
    ShapeBorder? shape,
  }) {
    return Card(
      color: _isHighContrastEnabled ? _secondaryAccessibilityColor : color,
      elevation: elevation,
      shape: shape,
      child: Padding(
        padding: getAccessiblePadding(const EdgeInsets.all(16)),
        child: child,
      ),
    );
  }

  /// Get an accessible container with appropriate styling.
  Widget getAccessibleContainer({
    required Widget child,
    Color? color,
    BoxDecoration? decoration,
    EdgeInsetsGeometry? padding,
  }) {
    return Container(
      color: _isHighContrastEnabled ? _secondaryAccessibilityColor : color,
      decoration: _isHighContrastEnabled ? null : decoration,
      padding: getAccessiblePadding(padding ?? const EdgeInsets.all(16)),
      child: child,
    );
  }

  /// Get an accessible text with appropriate styling.
  Widget getAccessibleText(
    String text, {
    TextStyle? style,
    TextAlign? textAlign,
    int? maxLines,
    TextOverflow? overflow,
  }) {
    return Text(
      text,
      style: getAccessibleTextStyle(style ?? const TextStyle()),
      textAlign: textAlign,
      maxLines: maxLines,
      overflow: overflow,
    );
  }
}

/// A provider for the AR accessibility service.
final arAccessibilityServiceProvider = Provider<ARAccessibilityService>((ref) {
  return ARAccessibilityService();
});

/// Types of haptic feedback.
enum HapticFeedbackType {
  light,
  medium,
  heavy,
  selection,
  vibrate,
}
