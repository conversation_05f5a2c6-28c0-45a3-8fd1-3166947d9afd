import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:culture_connect/providers/shared_preferences_provider.dart';

/// A service for tracking translation performance metrics
class TranslationMetricsService {
  /// The shared preferences instance
  final SharedPreferences _prefs;

  /// The key for storing metrics in shared preferences
  static const String _metricsKey = 'translation_performance_metrics';

  /// The current metrics
  final Map<String, dynamic> _metrics = {
    'totalTranslations': 0,
    'totalTranslationTimeMs': 0,
    'averageTranslationTimeMs': 0.0,
    'translationsPerLanguage': <String, int>{},
    'translationTimesPerLanguage': <String, int>{},
    'cacheHits': 0,
    'cacheMisses': 0,
    'cacheHitRate': 0.0,
    'backgroundTranslations': 0,
    'offlineTranslations': 0,
    'cancelledTranslations': 0,
    'failedTranslations': 0,
    'pendingSyncTranslations': 0,
    'syncedTranslations': 0,
    'conflictedTranslations': 0,
    'lastUpdated': DateTime.now().toIso8601String(),
  };

  /// Stream controller for metrics updates
  final StreamController<Map<String, dynamic>> _metricsController =
      StreamController<Map<String, dynamic>>.broadcast();

  /// Creates a new translation metrics service
  TranslationMetricsService(this._prefs) {
    _loadMetrics();
  }

  /// Stream of metrics updates
  Stream<Map<String, dynamic>> get metricsStream => _metricsController.stream;

  /// Gets the current metrics
  Map<String, dynamic> get metrics => Map.unmodifiable(_metrics);

  /// Loads metrics from shared preferences
  void _loadMetrics() {
    final metricsJson = _prefs.getString(_metricsKey);
    if (metricsJson != null) {
      try {
        final loadedMetrics = jsonDecode(metricsJson) as Map<String, dynamic>;
        _metrics.addAll(loadedMetrics);

        // Convert language maps from JSON format
        if (loadedMetrics['translationsPerLanguage'] != null) {
          final translationsPerLanguage = <String, int>{};
          (loadedMetrics['translationsPerLanguage'] as Map<String, dynamic>)
              .forEach((key, value) {
            translationsPerLanguage[key] = value as int;
          });
          _metrics['translationsPerLanguage'] = translationsPerLanguage;
        }

        if (loadedMetrics['translationTimesPerLanguage'] != null) {
          final translationTimesPerLanguage = <String, int>{};
          (loadedMetrics['translationTimesPerLanguage'] as Map<String, dynamic>)
              .forEach((key, value) {
            translationTimesPerLanguage[key] = value as int;
          });
          _metrics['translationTimesPerLanguage'] = translationTimesPerLanguage;
        }
      } catch (e) {
        debugPrint('Error loading translation metrics: $e');
      }
    }
  }

  /// Saves metrics to shared preferences
  Future<void> _saveMetrics() async {
    try {
      _metrics['lastUpdated'] = DateTime.now().toIso8601String();
      await _prefs.setString(_metricsKey, jsonEncode(_metrics));
      _metricsController.add(metrics);
    } catch (e) {
      debugPrint('Error saving translation metrics: $e');
    }
  }

  /// Records a translation
  Future<void> recordTranslation({
    required String sourceLanguage,
    required String targetLanguage,
    required int durationMs,
    bool isBackground = false,
    bool isCached = false,
    bool isOffline = false,
    String syncStatus = '',
  }) async {
    // Update total translations
    _metrics['totalTranslations'] = (_metrics['totalTranslations'] as int) + 1;

    // Update total translation time
    _metrics['totalTranslationTimeMs'] =
        (_metrics['totalTranslationTimeMs'] as int) + durationMs;

    // Update average translation time
    _metrics['averageTranslationTimeMs'] =
        (_metrics['totalTranslationTimeMs'] as int) /
            (_metrics['totalTranslations'] as int);

    // Update translations per language
    final translationsPerLanguage =
        _metrics['translationsPerLanguage'] as Map<String, int>;
    final languagePair = '$sourceLanguage-$targetLanguage';
    translationsPerLanguage[languagePair] =
        (translationsPerLanguage[languagePair] ?? 0) + 1;

    // Update translation times per language
    final translationTimesPerLanguage =
        _metrics['translationTimesPerLanguage'] as Map<String, int>;
    translationTimesPerLanguage[languagePair] =
        (translationTimesPerLanguage[languagePair] ?? 0) + durationMs;

    // Update cache metrics
    if (isCached) {
      _metrics['cacheHits'] = (_metrics['cacheHits'] as int) + 1;
    } else {
      _metrics['cacheMisses'] = (_metrics['cacheMisses'] as int) + 1;
    }

    // Update cache hit rate
    final totalCacheAccesses =
        (_metrics['cacheHits'] as int) + (_metrics['cacheMisses'] as int);
    _metrics['cacheHitRate'] = totalCacheAccesses > 0
        ? (_metrics['cacheHits'] as int) / totalCacheAccesses
        : 0.0;

    // Update background translations
    if (isBackground) {
      _metrics['backgroundTranslations'] =
          (_metrics['backgroundTranslations'] as int) + 1;
    }

    // Update offline translations
    if (isOffline) {
      _metrics['offlineTranslations'] =
          (_metrics['offlineTranslations'] as int) + 1;
    }

    // Update sync status metrics
    if (syncStatus.isNotEmpty) {
      switch (syncStatus) {
        case 'pending':
          _metrics['pendingSyncTranslations'] =
              (_metrics['pendingSyncTranslations'] as int) + 1;
          break;
        case 'synced':
          _metrics['syncedTranslations'] =
              (_metrics['syncedTranslations'] as int) + 1;
          break;
        case 'conflict':
          _metrics['conflictedTranslations'] =
              (_metrics['conflictedTranslations'] as int) + 1;
          break;
      }
    }

    await _saveMetrics();
  }

  /// Records a cancelled translation
  Future<void> recordCancelledTranslation() async {
    _metrics['cancelledTranslations'] =
        (_metrics['cancelledTranslations'] as int) + 1;
    await _saveMetrics();
  }

  /// Records a failed translation
  Future<void> recordFailedTranslation() async {
    _metrics['failedTranslations'] =
        (_metrics['failedTranslations'] as int) + 1;
    await _saveMetrics();
  }

  /// Gets the average translation time for a language pair
  double getAverageTranslationTimeForLanguagePair(
      String sourceLanguage, String targetLanguage) {
    final translationsPerLanguage =
        _metrics['translationsPerLanguage'] as Map<String, int>;
    final translationTimesPerLanguage =
        _metrics['translationTimesPerLanguage'] as Map<String, int>;

    final languagePair = '$sourceLanguage-$targetLanguage';
    final translations = translationsPerLanguage[languagePair] ?? 0;
    final totalTime = translationTimesPerLanguage[languagePair] ?? 0;

    return translations > 0 ? totalTime / translations : 0.0;
  }

  /// Resets all metrics
  Future<void> resetMetrics() async {
    _metrics['totalTranslations'] = 0;
    _metrics['totalTranslationTimeMs'] = 0;
    _metrics['averageTranslationTimeMs'] = 0.0;
    _metrics['translationsPerLanguage'] = <String, int>{};
    _metrics['translationTimesPerLanguage'] = <String, int>{};
    _metrics['cacheHits'] = 0;
    _metrics['cacheMisses'] = 0;
    _metrics['cacheHitRate'] = 0.0;
    _metrics['backgroundTranslations'] = 0;
    _metrics['offlineTranslations'] = 0;
    _metrics['cancelledTranslations'] = 0;
    _metrics['failedTranslations'] = 0;
    _metrics['pendingSyncTranslations'] = 0;
    _metrics['syncedTranslations'] = 0;
    _metrics['conflictedTranslations'] = 0;

    await _saveMetrics();
  }

  /// Disposes the service
  void dispose() {
    _metricsController.close();
  }
}

/// Provider for the translation metrics service
final translationMetricsServiceProvider =
    Provider<TranslationMetricsService>((ref) {
  final prefs = ref.watch(sharedPreferencesProvider);
  final service = TranslationMetricsService(prefs);

  ref.onDispose(() {
    service.dispose();
  });

  return service;
});

/// Provider for the current translation metrics
final translationMetricsProvider = StreamProvider<Map<String, dynamic>>((ref) {
  final service = ref.watch(translationMetricsServiceProvider);
  return service.metricsStream;
});
