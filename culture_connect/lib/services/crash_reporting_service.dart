import 'dart:async';
import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:shared_preferences/shared_preferences.dart';

import 'logging_service.dart';

/// Provider for the CrashReportingService
final crashReportingServiceProvider = Provider<CrashReportingService>((ref) {
  final loggingService = ref.watch(loggingServiceProvider);
  return CrashReportingService(loggingService);
});

/// A comprehensive crash reporting service for the application
class CrashReportingService {
  final LoggingService _loggingService;

  /// Firebase Crashlytics instance
  final FirebaseCrashlytics _crashlytics = FirebaseCrashlytics.instance;

  /// Whether crash reporting is enabled
  bool _crashReportingEnabled = true;

  /// Constructor
  CrashReportingService(this._loggingService);

  /// Initialize the crash reporting service
  Future<void> initialize() async {
    try {
      // Check if crash reporting is enabled in preferences
      final prefs = await SharedPreferences.getInstance();
      _crashReportingEnabled = prefs.getBool('crash_reporting_enabled') ?? true;

      // Set Crashlytics collection enabled
      await _crashlytics.setCrashlyticsCollectionEnabled(
          _crashReportingEnabled && !kDebugMode);

      // Set up global error handlers
      FlutterError.onError = _handleFlutterError;

      // Pass all uncaught errors to Crashlytics
      PlatformDispatcher.instance.onError = (error, stack) {
        _handlePlatformError(error, stack);
        return true;
      };

      // Add device and app info
      await _addDeviceInfo();
      await _addAppInfo();

      _loggingService.info(
          'CrashReportingService', 'Crash reporting service initialized');
    } catch (e, stackTrace) {
      _loggingService.error('CrashReportingService',
          'Error initializing crash reporting service', e, stackTrace);
    }
  }

  /// Enable or disable crash reporting
  Future<void> setCrashReportingEnabled(bool enabled) async {
    try {
      _crashReportingEnabled = enabled;

      // Save preference
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('crash_reporting_enabled', enabled);

      // Update Crashlytics
      await _crashlytics
          .setCrashlyticsCollectionEnabled(enabled && !kDebugMode);

      _loggingService.info('CrashReportingService',
          'Crash reporting ${enabled ? 'enabled' : 'disabled'}');
    } catch (e, stackTrace) {
      _loggingService.error('CrashReportingService',
          'Error setting crash reporting enabled', e, stackTrace);
    }
  }

  /// Handle Flutter framework errors
  void _handleFlutterError(FlutterErrorDetails details) {
    if (!_crashReportingEnabled) {
      // Just print to console in debug mode
      FlutterError.dumpErrorToConsole(details);
      return;
    }

    _loggingService.error(
      'FlutterError',
      details.exception.toString(),
      details.exception,
      details.stack,
    );

    // Report to crashlytics in non-debug mode
    if (!kDebugMode) {
      _crashlytics.recordFlutterError(details);
    }
  }

  /// Handle platform errors
  void _handlePlatformError(Object error, StackTrace stack) {
    if (!_crashReportingEnabled) {
      // Just print to console in debug mode
      debugPrint('Uncaught platform error: $error');
      debugPrint('Stack trace: $stack');
      return;
    }

    _loggingService.error(
      'PlatformError',
      error.toString(),
      error,
      stack,
    );

    // Report to crashlytics in non-debug mode
    if (!kDebugMode) {
      _crashlytics.recordError(error, stack);
    }
  }

  /// Add device information to crash reports
  Future<void> _addDeviceInfo() async {
    try {
      final deviceInfoPlugin = DeviceInfoPlugin();

      if (Platform.isAndroid) {
        final androidInfo = await deviceInfoPlugin.androidInfo;
        await _crashlytics.setCustomKey(
            'device_manufacturer', androidInfo.manufacturer);
        await _crashlytics.setCustomKey('device_model', androidInfo.model);
        await _crashlytics.setCustomKey(
            'android_version', androidInfo.version.release);
        await _crashlytics.setCustomKey(
            'android_sdk', androidInfo.version.sdkInt.toString());
      } else if (Platform.isIOS) {
        final iosInfo = await deviceInfoPlugin.iosInfo;
        await _crashlytics.setCustomKey('device_name', iosInfo.name);
        await _crashlytics.setCustomKey('device_model', iosInfo.model);
        await _crashlytics.setCustomKey('ios_version', iosInfo.systemVersion);
      }

      await _crashlytics.setCustomKey('platform', Platform.operatingSystem);
    } catch (e, stackTrace) {
      _loggingService.error(
          'CrashReportingService', 'Error adding device info', e, stackTrace);
    }
  }

  /// Add app information to crash reports
  Future<void> _addAppInfo() async {
    try {
      final packageInfo = await PackageInfo.fromPlatform();
      await _crashlytics.setCustomKey('app_name', packageInfo.appName);
      await _crashlytics.setCustomKey('app_version', packageInfo.version);
      await _crashlytics.setCustomKey(
          'app_build_number', packageInfo.buildNumber);
      await _crashlytics.setCustomKey(
          'app_package_name', packageInfo.packageName);
    } catch (e, stackTrace) {
      _loggingService.error(
          'CrashReportingService', 'Error adding app info', e, stackTrace);
    }
  }

  /// Log a non-fatal error
  Future<void> logError({
    required Object error,
    required StackTrace stackTrace,
    String? reason,
    bool fatal = false,
    Map<String, dynamic>? customKeys,
  }) async {
    if (!_crashReportingEnabled) return;

    try {
      // Add custom keys
      if (customKeys != null) {
        for (final entry in customKeys.entries) {
          await _crashlytics.setCustomKey(entry.key, entry.value.toString());
        }
      }

      // Log error
      await _crashlytics.recordError(
        error,
        stackTrace,
        reason: reason,
        fatal: fatal,
      );

      _loggingService.error(
        'CrashReportingService',
        'Logged error: ${reason ?? error}',
        error,
        stackTrace,
      );
    } catch (e, stackTrace) {
      _loggingService.error(
          'CrashReportingService', 'Error logging error', e, stackTrace);
    }
  }

  /// Log a message
  Future<void> log(String message) async {
    if (!_crashReportingEnabled) return;

    try {
      await _crashlytics.log(message);
    } catch (e, stackTrace) {
      _loggingService.error(
          'CrashReportingService', 'Error logging message', e, stackTrace);
    }
  }

  /// Set a custom key
  Future<void> setCustomKey(String key, dynamic value) async {
    if (!_crashReportingEnabled) return;

    try {
      await _crashlytics.setCustomKey(key, value.toString());
    } catch (e, stackTrace) {
      _loggingService.error(
          'CrashReportingService', 'Error setting custom key', e, stackTrace);
    }
  }

  /// Set user identifier
  Future<void> setUserIdentifier(String identifier) async {
    if (!_crashReportingEnabled) return;

    try {
      await _crashlytics.setUserIdentifier(identifier);
    } catch (e, stackTrace) {
      _loggingService.error('CrashReportingService',
          'Error setting user identifier', e, stackTrace);
    }
  }

  /// Test crash reporting
  Future<void> testCrash() async {
    if (!_crashReportingEnabled) {
      _loggingService.warning('CrashReportingService',
          'Crash reporting is disabled, skipping test crash');
      return;
    }

    _loggingService.info('CrashReportingService', 'Sending test crash');
    _crashlytics.crash();
  }
}
