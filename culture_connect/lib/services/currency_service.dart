import 'dart:async';

import 'package:culture_connect/models/currency/currency_model.dart';
import 'package:culture_connect/models/currency/currency_conversion_history_model.dart';
import 'package:culture_connect/models/currency/currency_preference_model.dart';
import 'package:culture_connect/models/currency/exchange_rate_model.dart';
import 'package:culture_connect/services/currency/currency_service.dart'
    as internal;
import 'package:culture_connect/services/currency/currency_data_service.dart';
import 'package:culture_connect/services/currency/currency_conversion_service.dart';
import 'package:culture_connect/services/currency/exchange_rate_api_service.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:culture_connect/providers/currency/currency_providers.dart';

/// A service for handling currency-related operations.
///
/// This is a facade that provides access to the various currency-related services:
/// - CurrencyService: For basic currency conversion operations
/// - CurrencyDataService: For accessing currency data
/// - CurrencyConversionService: For more advanced conversion features
/// - ExchangeRateApiService: For accessing exchange rate data from the API
///
/// This class exists for backward compatibility with code that imports from
/// 'package:culture_connect/services/currency_service.dart'.
class CurrencyService {
  /// The internal currency service
  final internal.CurrencyService _internalService;

  /// The currency data service
  final CurrencyDataService _dataService;

  /// The currency conversion service
  final CurrencyConversionService? _conversionService;

  /// The exchange rate API service
  final ExchangeRateApiService? _exchangeRateApiService;

  /// Creates a new currency service.
  ///
  /// In a non-Riverpod context, only the basic currency service and data service
  /// will be available. For full functionality, use the factory constructor with
  /// a WidgetRef.
  CurrencyService()
      : _internalService = internal.CurrencyService(),
        _dataService = CurrencyDataService(),
        _conversionService = null,
        _exchangeRateApiService = null;

  /// Creates a new currency service with a WidgetRef.
  ///
  /// This allows access to the full functionality of all currency-related services.
  factory CurrencyService.fromRef(WidgetRef ref) {
    return CurrencyService._withServices(
      internal.CurrencyService(),
      ref.read(currencyDataServiceProvider),
      ref.read(currencyConversionServiceProvider),
      ref.read(exchangeRateApiServiceProvider),
    );
  }

  /// Creates a new currency service with the specified services.
  CurrencyService._withServices(
    this._internalService,
    this._dataService,
    this._conversionService,
    this._exchangeRateApiService,
  );

  /// Initialize the service.
  Future<void> initialize() async {
    await _internalService.initialize();
  }

  /// Get all available currencies.
  List<CurrencyModel> getAllCurrencies() {
    return _dataService.getAllCurrencies();
  }

  /// Get a currency by code.
  CurrencyModel? getCurrency(String code) {
    return _dataService.getCurrencyByCode(code);
  }

  /// Get major currencies.
  List<CurrencyModel> getMajorCurrencies() {
    return _dataService.getMajorCurrencies();
  }

  /// Get the exchange rate between two currencies.
  double? getExchangeRate(String fromCurrency, String toCurrency) {
    return _internalService.getExchangeRate(fromCurrency, toCurrency);
  }

  /// Convert an amount from one currency to another.
  double? convert(double amount, String fromCurrency, String toCurrency) {
    return _internalService.convert(amount, fromCurrency, toCurrency);
  }

  /// Get the last updated timestamp.
  DateTime? getLastUpdated() {
    return _internalService.getLastUpdated();
  }

  /// Set the base currency.
  void setBaseCurrency(String currencyCode) {
    _internalService.setBaseCurrency(currencyCode);
  }

  /// Get the base currency.
  String getBaseCurrency() {
    return _internalService.getBaseCurrency();
  }

  /// Force refresh of currency data.
  Future<void> refreshCurrencyData() async {
    await _internalService.refreshCurrencyData();
  }

  /// Format an amount according to a currency's rules.
  String formatAmount(String currencyCode, double amount) {
    final currency = _dataService.getCurrencyByCode(currencyCode);
    if (currency != null) {
      return currency.formatAmount(amount);
    } else {
      return '$currencyCode${amount.toStringAsFixed(2)}';
    }
  }

  /// Get historical exchange rates.
  ///
  /// This method requires the CurrencyService to be created with a WidgetRef.
  Future<CurrencyConversionHistoryModel?> getHistoricalRates(
    String baseCurrency,
    String targetCurrency, {
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    if (_exchangeRateApiService == null) {
      throw UnsupportedError(
        'getHistoricalRates requires CurrencyService to be created with a WidgetRef',
      );
    }

    return _exchangeRateApiService!.getHistoricalRates(
      baseCurrency,
      targetCurrency,
      startDate: startDate,
      endDate: endDate,
    );
  }

  /// Get the current currency preferences.
  ///
  /// This method requires the CurrencyService to be created with a WidgetRef.
  CurrencyPreferenceModel? getCurrentPreferences() {
    if (_conversionService == null) {
      throw UnsupportedError(
        'getCurrentPreferences requires CurrencyService to be created with a WidgetRef',
      );
    }

    return _conversionService!.currencyPreferences;
  }

  /// Update currency preferences.
  ///
  /// This method requires the CurrencyService to be created with a WidgetRef.
  Future<void> updatePreferences(CurrencyPreferenceModel preferences) async {
    if (_conversionService == null) {
      throw UnsupportedError(
        'updatePreferences requires CurrencyService to be created with a WidgetRef',
      );
    }

    await _conversionService!.updatePreferences(preferences);
  }

  /// Get a stream of currency preference updates.
  ///
  /// This method requires the CurrencyService to be created with a WidgetRef.
  Stream<CurrencyPreferenceModel>? get preferencesStream {
    if (_conversionService == null) {
      throw UnsupportedError(
        'preferencesStream requires CurrencyService to be created with a WidgetRef',
      );
    }

    return _conversionService!.currencyPreferenceStream;
  }

  /// Get a stream of exchange rate updates.
  ///
  /// This method requires the CurrencyService to be created with a WidgetRef.
  Stream<ExchangeRateModel>? get exchangeRateStream {
    if (_conversionService == null) {
      throw UnsupportedError(
        'exchangeRateStream requires CurrencyService to be created with a WidgetRef',
      );
    }

    return _conversionService!.exchangeRateStream;
  }

  /// Check if the service is in offline mode.
  ///
  /// This method requires the CurrencyService to be created with a WidgetRef.
  bool isOfflineMode() {
    return _internalService.isOfflineMode();
  }

  /// Toggle offline mode manually.
  ///
  /// This method requires the CurrencyService to be created with a WidgetRef.
  Future<void> setOfflineMode(bool enabled) async {
    await _internalService.setOfflineMode(enabled);
  }

  /// Get a connectivity stream that emits when connectivity status changes.
  ///
  /// This method requires the CurrencyService to be created with a WidgetRef.
  Stream<bool>? get connectivityStream {
    if (_conversionService == null) {
      throw UnsupportedError(
        'connectivityStream requires CurrencyService to be created with a WidgetRef',
      );
    }

    return _conversionService!.connectivityStream;
  }

  /// Detect the local currency based on the user's location.
  ///
  /// This method requires the CurrencyService to be created with a WidgetRef.
  Future<void> detectLocalCurrency() async {
    if (_conversionService == null) {
      throw UnsupportedError(
        'detectLocalCurrency requires CurrencyService to be created with a WidgetRef',
      );
    }

    // The internal implementation will handle detecting the local currency
    // based on the user's location and updating preferences accordingly
    try {
      // This is a placeholder since the actual method is not exposed
      // In a real implementation, we would call the appropriate method
      await refreshCurrencyData();
    } catch (e) {
      // In a real implementation, we would log this error
      // using a proper logging framework
    }
  }
}
