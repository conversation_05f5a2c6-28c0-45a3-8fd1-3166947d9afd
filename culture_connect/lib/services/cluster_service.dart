import 'dart:math';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:culture_connect/models/experience.dart';

class ClusterService {
  static const double _clusterRadius = 50.0; // meters
  static const int _minClusterSize = 2;

  static List<Cluster> createClusters(
    List<Experience> experiences,
    CameraPosition cameraPosition,
    double zoomLevel,
  ) {
    final List<Cluster> clusters = [];
    final List<Experience> unclusteredExperiences = List.from(experiences);

    while (unclusteredExperiences.isNotEmpty) {
      final Experience experience = unclusteredExperiences.first;
      final List<Experience> clusterExperiences = [experience];
      unclusteredExperiences.removeAt(0);

      // Find nearby experiences
      for (int i = unclusteredExperiences.length - 1; i >= 0; i--) {
        final Experience nearbyExperience = unclusteredExperiences[i];
        if (_isNearby(
            experience.coordinates, nearbyExperience.coordinates, zoomLevel)) {
          clusterExperiences.add(nearbyExperience);
          unclusteredExperiences.removeAt(i);
        }
      }

      // Create cluster if there are enough nearby experiences
      if (clusterExperiences.length >= _minClusterSize) {
        final LatLng clusterCenter =
            _calculateClusterCenter(clusterExperiences);
        clusters.add(Cluster(
          position: clusterCenter,
          experiences: clusterExperiences,
        ));
      } else {
        // Add individual experiences as single-item clusters
        for (final exp in clusterExperiences) {
          clusters.add(Cluster(
            position: exp.coordinates,
            experiences: [exp],
          ));
        }
      }
    }

    return clusters;
  }

  static bool _isNearby(LatLng point1, LatLng point2, double zoomLevel) {
    // Adjust cluster radius based on zoom level
    final double adjustedRadius = _clusterRadius * (1 / zoomLevel);

    // Calculate distance between points using Haversine formula
    final double distance = _calculateDistance(point1, point2);
    return distance <= adjustedRadius;
  }

  static double _calculateDistance(LatLng point1, LatLng point2) {
    const double earthRadius = 6371000; // meters
    final double lat1 = point1.latitude * pi / 180;
    final double lat2 = point2.latitude * pi / 180;
    final double dLat = (point2.latitude - point1.latitude) * pi / 180;
    final double dLon = (point2.longitude - point1.longitude) * pi / 180;

    final double a = sin(dLat / 2) * sin(dLat / 2) +
        cos(lat1) * cos(lat2) * sin(dLon / 2) * sin(dLon / 2);
    final double c = 2 * atan2(sqrt(a), sqrt(1 - a));

    return earthRadius * c;
  }

  static LatLng _calculateClusterCenter(List<Experience> experiences) {
    double totalLat = 0;
    double totalLng = 0;

    for (final experience in experiences) {
      totalLat += experience.coordinates.latitude;
      totalLng += experience.coordinates.longitude;
    }

    return LatLng(
      totalLat / experiences.length,
      totalLng / experiences.length,
    );
  }
}

class Cluster {
  final LatLng position;
  final List<Experience> experiences;

  Cluster({
    required this.position,
    required this.experiences,
  });

  bool get isMultiple => experiences.length > 1;
  int get count => experiences.length;
}
