import 'dart:async';
import 'dart:io';

// Package imports
import 'package:arcore_flutter_plugin/arcore_flutter_plugin.dart';
import 'package:camera/camera.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:http/http.dart' as http;
import 'package:path_provider/path_provider.dart';
import 'package:vector_math/vector_math_64.dart';

// Project imports
import 'package:culture_connect/models/experience.dart';
import 'package:culture_connect/services/location_service.dart';
import 'package:culture_connect/services/logging_service.dart';

/// Service for handling AR experiences
class ARExperienceService {
  final LoggingService _loggingService;
  final LocationService _locationService;

  ArCoreController? _arController;
  CameraController? _cameraController;
  bool _isInitialized = false;

  // Cache for downloaded AR models
  final Map<String, String> _modelCache = {};

  /// Creates a new AR experience service
  ARExperienceService(this._loggingService, this._locationService);

  /// Initializes the AR service
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      _loggingService.debug('ARExperienceService', 'Initializing AR service');

      // Initialize camera
      final cameras = await availableCameras();
      if (cameras.isNotEmpty) {
        _cameraController = CameraController(
          cameras.first,
          ResolutionPreset.high,
          enableAudio: false,
        );
        await _cameraController!.initialize();
      }

      // Create AR models directory if it doesn't exist
      final appDir = await getApplicationDocumentsDirectory();
      final arModelsDir = Directory('${appDir.path}/ar_models');
      if (!await arModelsDir.exists()) {
        await arModelsDir.create(recursive: true);
      }

      _isInitialized = true;
      _loggingService.debug('ARExperienceService', 'AR service initialized');
    } catch (e, stackTrace) {
      _loggingService.error(
        'ARExperienceService',
        'Error initializing AR service',
        e,
        stackTrace,
      );
      rethrow;
    }
  }

  /// Starts the AR experience
  Future<void> startAR() async {
    if (!_isInitialized) {
      await initialize();
    }

    // Additional AR initialization can be done here
    _loggingService.debug('ARExperienceService', 'AR experience started');
  }

  /// Stops the AR experience
  void stopAR() {
    _cameraController?.dispose();
    _arController?.dispose();
    _isInitialized = false;
    _loggingService.debug('ARExperienceService', 'AR experience stopped');
  }

  /// Gets the camera controller for AR view
  CameraController? get cameraController => _cameraController;

  /// Callback for when the AR Core view is created
  void onArCoreViewCreated(ArCoreController controller) {
    _arController = controller;
    _setupARScene();
    _loggingService.debug('ARExperienceService', 'AR Core view created');
  }

  /// Sets up the AR scene
  void _setupARScene() {
    if (_arController == null) return;

    _arController!.onNodeTap = (names) {
      _loggingService.debug('ARExperienceService', 'Node tapped: $names');
    };

    _arController!.onPlaneTap = (hits) {
      _loggingService.debug(
          'ARExperienceService', 'Plane tapped: ${hits.length} hits');
    };
  }

  /// Gets nearby experiences with AR content
  Future<List<Experience>> getNearbyARExperiences() async {
    try {
      _loggingService.debug(
          'ARExperienceService', 'Getting nearby AR experiences');

      final currentPosition = await _locationService.getCurrentPosition();

      // In a real app, this would query a backend for AR experiences near the current location
      // For now, we'll return a mock list
      return [
        Experience(
          id: 'exp1',
          title: 'AR Cultural Tour of Lagos',
          description:
              'Explore the rich cultural heritage of Lagos with our AR-enhanced tour.',
          category: 'Cultural',
          price: 50.0,
          location: 'Lagos, Nigeria',
          imageUrl: 'https://example.com/lagos.jpg',
          rating: 4.5,
          reviewCount: 120,
          guideId: 'guide1',
          guideName: 'John Doe',
          guideImageUrl: 'https://example.com/guide.jpg',
          coordinates: LatLng(
            currentPosition.latitude,
            currentPosition.longitude,
          ),
          languages: ['English', 'Yoruba'],
          includedItems: ['AR Tour guide', 'Water', 'Snacks'],
          requirements: [
            'Smartphone with AR capabilities',
            'Comfortable shoes'
          ],
          createdAt: DateTime.now().subtract(const Duration(days: 30)),
          updatedAt: DateTime.now().subtract(const Duration(days: 5)),
          durationHours: 2.0,
          hasARContent: true,
          arContentType: ARContentType.model3d,
          arModelUrl: 'https://example.com/models/lagos_landmark.glb',
        ),
        Experience(
          id: 'exp2',
          title: 'AR Cooking Class',
          description: 'Learn traditional cooking with AR assistance.',
          category: 'Cooking',
          price: 35.0,
          location: 'Lagos, Nigeria',
          imageUrl: 'https://example.com/cooking.jpg',
          rating: 4.8,
          reviewCount: 85,
          guideId: 'guide2',
          guideName: 'Chef Ade',
          guideImageUrl: 'https://example.com/chef.jpg',
          coordinates: LatLng(
            currentPosition.latitude + 0.01,
            currentPosition.longitude + 0.01,
          ),
          languages: ['English', 'Yoruba'],
          includedItems: ['Ingredients', 'Recipe Book', 'AR Cooking Guide'],
          requirements: ['Smartphone with AR capabilities'],
          createdAt: DateTime.now().subtract(const Duration(days: 45)),
          updatedAt: DateTime.now().subtract(const Duration(days: 10)),
          durationHours: 3.0,
          hasARContent: true,
          arContentType: ARContentType.interactive,
          arModelUrl: 'https://example.com/models/cooking_guide.glb',
        ),
      ];
    } catch (e, stackTrace) {
      _loggingService.error(
        'ARExperienceService',
        'Error getting nearby AR experiences',
        e,
        stackTrace,
      );
      return [];
    }
  }

  /// Downloads and caches an AR model for an experience
  Future<String?> getARModelForExperience(Experience experience) async {
    if (!experience.hasARContent || experience.arModelUrl == null) {
      return null;
    }

    try {
      _loggingService.debug(
        'ARExperienceService',
        'Getting AR model for experience: ${experience.id}',
      );

      // Check if model is already cached
      if (_modelCache.containsKey(experience.id)) {
        return _modelCache[experience.id];
      }

      // Get the file name from the URL
      final uri = Uri.parse(experience.arModelUrl!);
      final fileName = uri.pathSegments.last;

      // Get the local file path
      final appDir = await getApplicationDocumentsDirectory();
      final filePath = '${appDir.path}/ar_models/$fileName';
      final file = File(filePath);

      // Check if file already exists
      if (await file.exists()) {
        _modelCache[experience.id] = filePath;
        return filePath;
      }

      // Download the file
      _loggingService.debug(
        'ARExperienceService',
        'Downloading AR model: ${experience.arModelUrl}',
      );

      final response = await http.get(Uri.parse(experience.arModelUrl!));
      if (response.statusCode == 200) {
        await file.writeAsBytes(response.bodyBytes);
        _modelCache[experience.id] = filePath;
        return filePath;
      } else {
        _loggingService.error(
          'ARExperienceService',
          'Failed to download AR model',
          'HTTP Status: ${response.statusCode}',
          StackTrace.current,
        );
        return null;
      }
    } catch (e, stackTrace) {
      _loggingService.error(
        'ARExperienceService',
        'Error getting AR model for experience',
        e,
        stackTrace,
      );
      return null;
    }
  }

  /// Places an AR model in the scene
  Future<void> placeARModel(Experience experience, Vector3 position) async {
    if (_arController == null) {
      _loggingService.error(
        'ARExperienceService',
        'Cannot place AR model: AR controller is null',
        null,
        StackTrace.current,
      );
      return;
    }

    try {
      _loggingService.debug(
        'ARExperienceService',
        'Placing AR model for experience: ${experience.id}',
      );

      final modelPath = await getARModelForExperience(experience);
      if (modelPath == null) {
        _loggingService.error(
          'ARExperienceService',
          'Cannot place AR model: Model path is null',
          null,
          StackTrace.current,
        );
        return;
      }

      // Create a reference node for the 3D model
      final referenceNode = ArCoreReferenceNode(
        objectUrl: modelPath,
        scale: Vector3(0.5, 0.5, 0.5),
      );

      // Create the main node with the reference node as a child
      final node = ArCoreNode(
        name: 'experience_${experience.id}',
        position: position,
        children: [referenceNode],
      );

      _arController!.addArCoreNode(node);
      _loggingService.debug(
        'ARExperienceService',
        'AR model placed successfully',
      );
    } catch (e, stackTrace) {
      _loggingService.error(
        'ARExperienceService',
        'Error placing AR model',
        e,
        stackTrace,
      );
    }
  }

  /// Disposes of the AR experience service
  void dispose() {
    _cameraController?.dispose();
    _arController?.dispose();
    _isInitialized = false;
    _loggingService.debug(
        'ARExperienceService', 'AR experience service disposed');
  }
}
