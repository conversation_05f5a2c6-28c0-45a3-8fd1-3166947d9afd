import 'package:culture_connect/models/currency/currency_model.dart';

/// Service for providing currency data
class CurrencyDataService {
  // Singleton instance
  static final CurrencyDataService _instance = CurrencyDataService._internal();
  factory CurrencyDataService() => _instance;
  CurrencyDataService._internal();
  
  /// Get all supported currencies
  List<CurrencyModel> getAllCurrencies() {
    return _currencies;
  }
  
  /// Get major currencies
  List<CurrencyModel> getMajorCurrencies() {
    return _currencies.where((currency) => currency.isMajor).toList();
  }
  
  /// Get a currency by code
  CurrencyModel? getCurrencyByCode(String code) {
    try {
      return _currencies.firstWhere(
        (currency) => currency.code.toUpperCase() == code.toUpperCase(),
      );
    } catch (e) {
      return null;
    }
  }
  
  /// Get a currency symbol by code
  String getCurrencySymbol(String code) {
    final currency = getCurrencyByCode(code);
    return currency?.symbol ?? code;
  }
  
  /// Format an amount according to a currency's rules
  String formatAmount(String currencyCode, double amount) {
    final currency = getCurrencyByCode(currencyCode);
    if (currency != null) {
      return currency.formatAmount(amount);
    } else {
      return '$currencyCode${amount.toStringAsFixed(2)}';
    }
  }
  
  /// List of supported currencies
  final List<CurrencyModel> _currencies = [
    const CurrencyModel(
      code: 'USD',
      name: 'US Dollar',
      symbol: '\$',
      flag: '🇺🇸',
      decimalPlaces: 2,
      isMajor: true,
    ),
    const CurrencyModel(
      code: 'EUR',
      name: 'Euro',
      symbol: '€',
      flag: '🇪🇺',
      decimalPlaces: 2,
      isMajor: true,
    ),
    const CurrencyModel(
      code: 'GBP',
      name: 'British Pound',
      symbol: '£',
      flag: '🇬🇧',
      decimalPlaces: 2,
      isMajor: true,
    ),
    const CurrencyModel(
      code: 'JPY',
      name: 'Japanese Yen',
      symbol: '¥',
      flag: '🇯🇵',
      decimalPlaces: 0,
      isMajor: true,
    ),
    const CurrencyModel(
      code: 'CAD',
      name: 'Canadian Dollar',
      symbol: 'C\$',
      flag: '🇨🇦',
      decimalPlaces: 2,
      isMajor: true,
    ),
    const CurrencyModel(
      code: 'AUD',
      name: 'Australian Dollar',
      symbol: 'A\$',
      flag: '🇦🇺',
      decimalPlaces: 2,
      isMajor: true,
    ),
    const CurrencyModel(
      code: 'CHF',
      name: 'Swiss Franc',
      symbol: 'Fr',
      flag: '🇨🇭',
      decimalPlaces: 2,
      isMajor: true,
    ),
    const CurrencyModel(
      code: 'CNY',
      name: 'Chinese Yuan',
      symbol: '¥',
      flag: '🇨🇳',
      decimalPlaces: 2,
      isMajor: true,
    ),
    const CurrencyModel(
      code: 'HKD',
      name: 'Hong Kong Dollar',
      symbol: 'HK\$',
      flag: '🇭🇰',
      decimalPlaces: 2,
      isMajor: false,
    ),
    const CurrencyModel(
      code: 'NZD',
      name: 'New Zealand Dollar',
      symbol: 'NZ\$',
      flag: '🇳🇿',
      decimalPlaces: 2,
      isMajor: false,
    ),
    const CurrencyModel(
      code: 'SEK',
      name: 'Swedish Krona',
      symbol: 'kr',
      flag: '🇸🇪',
      decimalPlaces: 2,
      isMajor: false,
    ),
    const CurrencyModel(
      code: 'KRW',
      name: 'South Korean Won',
      symbol: '₩',
      flag: '🇰🇷',
      decimalPlaces: 0,
      isMajor: false,
    ),
    const CurrencyModel(
      code: 'SGD',
      name: 'Singapore Dollar',
      symbol: 'S\$',
      flag: '🇸🇬',
      decimalPlaces: 2,
      isMajor: false,
    ),
    const CurrencyModel(
      code: 'NOK',
      name: 'Norwegian Krone',
      symbol: 'kr',
      flag: '🇳🇴',
      decimalPlaces: 2,
      isMajor: false,
    ),
    const CurrencyModel(
      code: 'MXN',
      name: 'Mexican Peso',
      symbol: '\$',
      flag: '🇲🇽',
      decimalPlaces: 2,
      isMajor: false,
    ),
    const CurrencyModel(
      code: 'INR',
      name: 'Indian Rupee',
      symbol: '₹',
      flag: '🇮🇳',
      decimalPlaces: 2,
      isMajor: false,
    ),
    const CurrencyModel(
      code: 'RUB',
      name: 'Russian Ruble',
      symbol: '₽',
      flag: '🇷🇺',
      decimalPlaces: 2,
      isMajor: false,
    ),
    const CurrencyModel(
      code: 'ZAR',
      name: 'South African Rand',
      symbol: 'R',
      flag: '🇿🇦',
      decimalPlaces: 2,
      isMajor: false,
    ),
    const CurrencyModel(
      code: 'TRY',
      name: 'Turkish Lira',
      symbol: '₺',
      flag: '🇹🇷',
      decimalPlaces: 2,
      isMajor: false,
    ),
    const CurrencyModel(
      code: 'BRL',
      name: 'Brazilian Real',
      symbol: 'R\$',
      flag: '🇧🇷',
      decimalPlaces: 2,
      isMajor: false,
    ),
    const CurrencyModel(
      code: 'TWD',
      name: 'Taiwan Dollar',
      symbol: 'NT\$',
      flag: '🇹🇼',
      decimalPlaces: 2,
      isMajor: false,
    ),
    const CurrencyModel(
      code: 'DKK',
      name: 'Danish Krone',
      symbol: 'kr',
      flag: '🇩🇰',
      decimalPlaces: 2,
      isMajor: false,
    ),
    const CurrencyModel(
      code: 'PLN',
      name: 'Polish Złoty',
      symbol: 'zł',
      flag: '🇵🇱',
      decimalPlaces: 2,
      isMajor: false,
    ),
    const CurrencyModel(
      code: 'THB',
      name: 'Thai Baht',
      symbol: '฿',
      flag: '🇹🇭',
      decimalPlaces: 2,
      isMajor: false,
    ),
    const CurrencyModel(
      code: 'IDR',
      name: 'Indonesian Rupiah',
      symbol: 'Rp',
      flag: '🇮🇩',
      decimalPlaces: 0,
      isMajor: false,
    ),
    const CurrencyModel(
      code: 'HUF',
      name: 'Hungarian Forint',
      symbol: 'Ft',
      flag: '🇭🇺',
      decimalPlaces: 0,
      isMajor: false,
    ),
    const CurrencyModel(
      code: 'CZK',
      name: 'Czech Koruna',
      symbol: 'Kč',
      flag: '🇨🇿',
      decimalPlaces: 2,
      isMajor: false,
    ),
    const CurrencyModel(
      code: 'ILS',
      name: 'Israeli New Shekel',
      symbol: '₪',
      flag: '🇮🇱',
      decimalPlaces: 2,
      isMajor: false,
    ),
    const CurrencyModel(
      code: 'CLP',
      name: 'Chilean Peso',
      symbol: '\$',
      flag: '🇨🇱',
      decimalPlaces: 0,
      isMajor: false,
    ),
    const CurrencyModel(
      code: 'PHP',
      name: 'Philippine Peso',
      symbol: '₱',
      flag: '🇵🇭',
      decimalPlaces: 2,
      isMajor: false,
    ),
    const CurrencyModel(
      code: 'AED',
      name: 'UAE Dirham',
      symbol: 'د.إ',
      flag: '🇦🇪',
      decimalPlaces: 2,
      isMajor: false,
    ),
    const CurrencyModel(
      code: 'COP',
      name: 'Colombian Peso',
      symbol: '\$',
      flag: '🇨🇴',
      decimalPlaces: 0,
      isMajor: false,
    ),
    const CurrencyModel(
      code: 'SAR',
      name: 'Saudi Riyal',
      symbol: '﷼',
      flag: '🇸🇦',
      decimalPlaces: 2,
      isMajor: false,
    ),
    const CurrencyModel(
      code: 'MYR',
      name: 'Malaysian Ringgit',
      symbol: 'RM',
      flag: '🇲🇾',
      decimalPlaces: 2,
      isMajor: false,
    ),
    const CurrencyModel(
      code: 'RON',
      name: 'Romanian Leu',
      symbol: 'lei',
      flag: '🇷🇴',
      decimalPlaces: 2,
      isMajor: false,
    ),
    // African currencies
    const CurrencyModel(
      code: 'NGN',
      name: 'Nigerian Naira',
      symbol: '₦',
      flag: '🇳🇬',
      decimalPlaces: 2,
      isMajor: false,
    ),
    const CurrencyModel(
      code: 'KES',
      name: 'Kenyan Shilling',
      symbol: 'KSh',
      flag: '🇰🇪',
      decimalPlaces: 2,
      isMajor: false,
    ),
    const CurrencyModel(
      code: 'GHS',
      name: 'Ghanaian Cedi',
      symbol: '₵',
      flag: '🇬🇭',
      decimalPlaces: 2,
      isMajor: false,
    ),
    const CurrencyModel(
      code: 'EGP',
      name: 'Egyptian Pound',
      symbol: 'E£',
      flag: '🇪🇬',
      decimalPlaces: 2,
      isMajor: false,
    ),
    // Cryptocurrencies
    const CurrencyModel(
      code: 'BTC',
      name: 'Bitcoin',
      symbol: '₿',
      flag: '🪙',
      decimalPlaces: 8,
      isMajor: false,
      isCrypto: true,
    ),
    const CurrencyModel(
      code: 'ETH',
      name: 'Ethereum',
      symbol: 'Ξ',
      flag: '🪙',
      decimalPlaces: 6,
      isMajor: false,
      isCrypto: true,
    ),
  ];
}
