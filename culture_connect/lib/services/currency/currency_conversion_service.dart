import 'dart:async';
import 'dart:convert';
import 'package:geolocator/geolocator.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:intl/intl.dart';
import 'package:connectivity_plus/connectivity_plus.dart';

import 'package:culture_connect/models/currency/currency_model.dart';
import 'package:culture_connect/models/currency/exchange_rate_model.dart';
import 'package:culture_connect/models/currency/currency_conversion_history_model.dart';
import 'package:culture_connect/models/currency/currency_preference_model.dart';
import 'package:culture_connect/services/currency/currency_data_service.dart';
import 'package:culture_connect/services/currency/exchange_rate_api_service.dart';
import 'package:culture_connect/services/logging_service.dart';
import 'package:culture_connect/services/location_service.dart';

/// Service for currency conversion
class CurrencyConversionService {
  /// The exchange rate API service
  final ExchangeRateApiService _exchangeRateApiService;

  /// The currency data service
  final CurrencyDataService _currencyDataService;

  /// The logging service
  final LoggingService _loggingService;

  /// The location service
  final LocationService _locationService;

  /// The shared preferences instance
  final SharedPreferences _preferences;

  /// The connectivity instance for checking network connectivity
  final Connectivity _connectivity;

  /// Stream controller for currency preference updates
  final _currencyPreferenceController =
      StreamController<CurrencyPreferenceModel>.broadcast();

  /// Stream controller for exchange rate updates
  final _exchangeRateController =
      StreamController<ExchangeRateModel>.broadcast();

  /// Stream controller for connectivity status
  final _connectivityController = StreamController<bool>.broadcast();

  /// Whether the device is currently online
  bool _isOnline = true;

  /// Subscription for connectivity changes
  StreamSubscription<ConnectivityResult>? _connectivitySubscription;

  /// The key for storing currency preferences in shared preferences
  static const _preferencesKey = 'currency_preferences';

  /// Creates a new currency conversion service
  CurrencyConversionService({
    required ExchangeRateApiService exchangeRateApiService,
    required CurrencyDataService currencyDataService,
    required LoggingService loggingService,
    required LocationService locationService,
    required SharedPreferences preferences,
  })  : _exchangeRateApiService = exchangeRateApiService,
        _currencyDataService = currencyDataService,
        _loggingService = loggingService,
        _locationService = locationService,
        _preferences = preferences,
        _connectivity = Connectivity() {
    _initConnectivity();
    _loadPreferences();
    _startBackgroundRefresh();
  }

  /// Initialize connectivity monitoring
  Future<void> _initConnectivity() async {
    try {
      // Check initial connectivity
      final result = await _connectivity.checkConnectivity();
      _updateConnectionStatus(result);

      // Listen for connectivity changes
      _connectivitySubscription =
          _connectivity.onConnectivityChanged.listen(_updateConnectionStatus);
    } catch (e, stackTrace) {
      _loggingService.error(
        'CurrencyConversionService',
        'Error initializing connectivity',
        e,
        stackTrace,
      );
    }
  }

  /// Update connection status based on connectivity result
  void _updateConnectionStatus(ConnectivityResult result) {
    final wasOnline = _isOnline;
    _isOnline = result != ConnectivityResult.none;

    // Only emit an event if the status has changed
    if (wasOnline != _isOnline) {
      _connectivityController.add(_isOnline);

      _loggingService.info(
        'CurrencyConversionService',
        'Connectivity changed: ${_isOnline ? 'Online' : 'Offline'}',
      );

      // If we just came back online, refresh exchange rates
      if (!wasOnline && _isOnline) {
        _refreshExchangeRates();
      }
    }
  }

  /// Stream of connectivity status changes
  Stream<bool> get connectivityStream => _connectivityController.stream;

  /// The current currency preferences
  CurrencyPreferenceModel? _currencyPreferences;

  /// Get the current currency preferences
  CurrencyPreferenceModel get currencyPreferences {
    return _currencyPreferences ?? CurrencyPreferenceModel.defaultPreferences;
  }

  /// Load currency preferences from shared preferences
  Future<void> _loadPreferences() async {
    try {
      final preferencesJson = _preferences.getString(_preferencesKey);
      if (preferencesJson != null) {
        final json = jsonDecode(preferencesJson);
        _currencyPreferences = CurrencyPreferenceModel.fromJson(json);
        _currencyPreferenceController.add(_currencyPreferences!);
      } else {
        // If no preferences are stored, use defaults
        _currencyPreferences = CurrencyPreferenceModel.defaultPreferences;
        await _savePreferences();
      }

      // If auto-detect is enabled, try to detect the local currency
      if (_currencyPreferences!.autoDetectCurrency) {
        unawaited(_detectLocalCurrency());
      }
    } catch (e, stackTrace) {
      _loggingService.error(
        'CurrencyConversionService',
        'Error loading currency preferences',
        e,
        stackTrace,
      );
      _currencyPreferences = CurrencyPreferenceModel.defaultPreferences;
      await _savePreferences();
    }
  }

  /// Save currency preferences to shared preferences
  Future<void> _savePreferences() async {
    try {
      if (_currencyPreferences != null) {
        final json = jsonEncode(_currencyPreferences!.toJson());
        await _preferences.setString(_preferencesKey, json);
        _currencyPreferenceController.add(_currencyPreferences!);
      }
    } catch (e, stackTrace) {
      _loggingService.error(
        'CurrencyConversionService',
        'Error saving currency preferences',
        e,
        stackTrace,
      );
    }
  }

  /// Update currency preferences
  Future<void> updatePreferences(CurrencyPreferenceModel preferences) async {
    _currencyPreferences = preferences;
    await _savePreferences();

    // If auto-detect is enabled, try to detect the local currency
    if (preferences.autoDetectCurrency) {
      unawaited(_detectLocalCurrency());
    }
  }

  /// Detect the local currency based on the user's location
  Future<void> _detectLocalCurrency() async {
    try {
      // Get current position using Geolocator directly
      final position = await _locationService.getCurrentPosition();

      // For demo purposes, we'll use a simplified approach to get country
      // In a real app, you would use a geocoding service
      // We'll use a mock implementation that returns a country code based on coordinates
      final country = _getMockCountryFromCoordinates(
        position.latitude,
        position.longitude,
      );

      if (country != null) {
        final currencyCode = _getCurrencyCodeForCountry(country);
        if (currencyCode != null &&
            _currencyDataService.getCurrencyByCode(currencyCode) != null) {
          // Update the preferred currency
          final updatedPreferences = _currencyPreferences!.copyWith(
            preferredCurrency: currencyCode,
          );
          await updatePreferences(updatedPreferences);
        }
      }
    } catch (e, stackTrace) {
      _loggingService.error(
        'CurrencyConversionService',
        'Error detecting local currency',
        e,
        stackTrace,
      );
    }
  }

  /// Get a mock country code from coordinates
  String? _getMockCountryFromCoordinates(double latitude, double longitude) {
    // This is a simplified approach for demo purposes
    // In a real app, you would use a geocoding service

    // North America
    if (latitude > 15 && latitude < 70 && longitude > -170 && longitude < -50) {
      if (longitude < -100) return 'US'; // United States
      return 'CA'; // Canada
    }

    // Europe
    if (latitude > 35 && latitude < 70 && longitude > -10 && longitude < 40) {
      if (longitude < 0) return 'GB'; // United Kingdom
      if (longitude < 10) return 'FR'; // France
      if (longitude < 20) return 'DE'; // Germany
      return 'IT'; // Italy
    }

    // Asia
    if (latitude > 0 && latitude < 60 && longitude > 60 && longitude < 150) {
      if (longitude > 120) return 'JP'; // Japan
      if (longitude > 100) return 'CN'; // China
      return 'IN'; // India
    }

    // Default to US
    return 'US';
  }

  /// Get the currency code for a country
  String? _getCurrencyCodeForCountry(String countryCode) {
    // This is a simplified mapping for demo purposes
    // In a real app, you would use a more comprehensive mapping
    final currencyMap = {
      'US': 'USD', // United States
      'CA': 'CAD', // Canada
      'GB': 'GBP', // United Kingdom
      'EU': 'EUR', // European Union
      'JP': 'JPY', // Japan
      'AU': 'AUD', // Australia
      'CH': 'CHF', // Switzerland
      'CN': 'CNY', // China
      'HK': 'HKD', // Hong Kong
      'NZ': 'NZD', // New Zealand
      'SE': 'SEK', // Sweden
      'KR': 'KRW', // South Korea
      'SG': 'SGD', // Singapore
      'NO': 'NOK', // Norway
      'MX': 'MXN', // Mexico
      'IN': 'INR', // India
      'RU': 'RUB', // Russia
      'ZA': 'ZAR', // South Africa
      'TR': 'TRY', // Turkey
      'BR': 'BRL', // Brazil
      'TW': 'TWD', // Taiwan
      'DK': 'DKK', // Denmark
      'PL': 'PLN', // Poland
      'TH': 'THB', // Thailand
      'ID': 'IDR', // Indonesia
      'HU': 'HUF', // Hungary
      'CZ': 'CZK', // Czech Republic
      'IL': 'ILS', // Israel
      'CL': 'CLP', // Chile
      'PH': 'PHP', // Philippines
      'AE': 'AED', // United Arab Emirates
      'CO': 'COP', // Colombia
      'SA': 'SAR', // Saudi Arabia
      'MY': 'MYR', // Malaysia
      'RO': 'RON', // Romania
      // African countries
      'NG': 'NGN', // Nigeria
      'KE': 'KES', // Kenya
      'GH': 'GHS', // Ghana
      'EG': 'EGP', // Egypt
    };

    return currencyMap[countryCode.toUpperCase()];
  }

  /// Start background refresh of exchange rates
  void _startBackgroundRefresh() {
    // Refresh exchange rates every hour
    Timer.periodic(const Duration(hours: 1), (timer) {
      _refreshExchangeRates();
    });

    // Initial refresh
    _refreshExchangeRates();
  }

  /// Refresh exchange rates
  Future<void> _refreshExchangeRates() async {
    try {
      final baseCurrency = _currencyPreferences?.preferredCurrency ?? 'USD';
      await _exchangeRateApiService.getLatestRates(baseCurrency);
    } catch (e, stackTrace) {
      _loggingService.error(
        'CurrencyConversionService',
        'Error refreshing exchange rates',
        e,
        stackTrace,
      );
    }
  }

  /// Convert an amount from one currency to another
  Future<double> convertCurrency(
    double amount,
    String fromCurrency,
    String toCurrency,
  ) async {
    try {
      // If the currencies are the same, no conversion needed
      if (fromCurrency == toCurrency) {
        return amount;
      }

      // Get the exchange rate
      final exchangeRate = await _exchangeRateApiService.getExchangeRate(
        fromCurrency,
        toCurrency,
      );

      // Notify listeners of the exchange rate
      _exchangeRateController.add(exchangeRate);

      // Convert the amount
      return exchangeRate.convert(amount);
    } catch (e, stackTrace) {
      _loggingService.error(
        'CurrencyConversionService',
        'Error converting currency',
        e,
        stackTrace,
      );

      // If conversion fails, return the original amount
      return amount;
    }
  }

  /// Format an amount according to a currency's rules
  String formatAmount(String currencyCode, double amount) {
    return _currencyDataService.formatAmount(currencyCode, amount);
  }

  /// Format an amount according to a currency's rules and the user's locale
  String formatAmountWithLocale(
      String currencyCode, double amount, String locale) {
    try {
      final currency = _currencyDataService.getCurrencyByCode(currencyCode);
      if (currency != null) {
        final format = NumberFormat.currency(
          locale: locale,
          symbol: currency.symbol,
          decimalDigits: currency.decimalPlaces,
        );
        return format.format(amount);
      } else {
        // Fallback to basic formatting
        return formatAmount(currencyCode, amount);
      }
    } catch (e) {
      // If formatting with locale fails, fall back to basic formatting
      return formatAmount(currencyCode, amount);
    }
  }

  /// Get all supported currencies
  List<CurrencyModel> getAllCurrencies() {
    return _currencyDataService.getAllCurrencies();
  }

  /// Get major currencies
  List<CurrencyModel> getMajorCurrencies() {
    return _currencyDataService.getMajorCurrencies();
  }

  /// Get a currency by code
  CurrencyModel? getCurrencyByCode(String code) {
    return _currencyDataService.getCurrencyByCode(code);
  }

  /// Get historical exchange rates
  Future<CurrencyConversionHistoryModel> getHistoricalRates(
    String baseCurrency,
    String targetCurrency, {
    DateTime? startDate,
    DateTime? endDate,
  }) {
    return _exchangeRateApiService.getHistoricalRates(
      baseCurrency,
      targetCurrency,
      startDate: startDate,
      endDate: endDate,
    );
  }

  /// Add a currency to the user's favorite currencies
  Future<void> addFavoriteCurrency(String currencyCode) async {
    if (_currencyPreferences == null) {
      await _loadPreferences();
    }

    if (_currencyPreferences!.favoriteCurrencies.contains(currencyCode)) {
      return;
    }

    final updatedFavorites =
        List<String>.from(_currencyPreferences!.favoriteCurrencies)
          ..add(currencyCode);

    final updatedPreferences = _currencyPreferences!.copyWith(
      favoriteCurrencies: updatedFavorites,
    );

    await updatePreferences(updatedPreferences);
  }

  /// Remove a currency from the user's favorite currencies
  Future<void> removeFavoriteCurrency(String currencyCode) async {
    if (_currencyPreferences == null) {
      await _loadPreferences();
    }

    if (!_currencyPreferences!.favoriteCurrencies.contains(currencyCode)) {
      return;
    }

    final updatedFavorites =
        List<String>.from(_currencyPreferences!.favoriteCurrencies)
          ..remove(currencyCode);

    final updatedPreferences = _currencyPreferences!.copyWith(
      favoriteCurrencies: updatedFavorites,
    );

    await updatePreferences(updatedPreferences);
  }

  /// Add a currency to the user's recently used currencies
  Future<void> addRecentlyUsedCurrency(String currencyCode) async {
    if (_currencyPreferences == null) {
      await _loadPreferences();
    }

    // Remove the currency if it's already in the list
    final updatedRecent =
        List<String>.from(_currencyPreferences!.recentlyUsedCurrencies)
          ..remove(currencyCode);

    // Add the currency to the beginning of the list
    updatedRecent.insert(0, currencyCode);

    // Limit the list to 10 items
    if (updatedRecent.length > 10) {
      updatedRecent.removeLast();
    }

    final updatedPreferences = _currencyPreferences!.copyWith(
      recentlyUsedCurrencies: updatedRecent,
    );

    await updatePreferences(updatedPreferences);
  }

  /// Stream of currency preference updates
  Stream<CurrencyPreferenceModel> get currencyPreferenceStream =>
      _currencyPreferenceController.stream;

  /// Stream of exchange rate updates
  Stream<ExchangeRateModel> get exchangeRateStream =>
      _exchangeRateController.stream;

  /// Dispose resources
  void dispose() {
    _connectivitySubscription?.cancel();
    _connectivityController.close();
    _currencyPreferenceController.close();
    _exchangeRateController.close();
  }
}
