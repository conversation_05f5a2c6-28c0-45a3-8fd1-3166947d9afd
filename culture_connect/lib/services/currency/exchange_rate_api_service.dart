import 'dart:convert';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:hive/hive.dart';
import 'package:culture_connect/models/currency/exchange_rate_model.dart';
import 'package:culture_connect/models/currency/currency_conversion_history_model.dart';
import 'package:culture_connect/services/logging_service.dart';

/// Service for fetching exchange rates from an API
class ExchangeRateApiService {
  /// The base URL for the exchange rate API
  final String _baseUrl;

  /// The API key for the exchange rate API
  final String _apiKey;

  /// The HTTP client for making API requests
  final http.Client _client;

  /// The logging service for logging errors
  final LoggingService _loggingService;

  /// The connectivity instance for checking network connectivity
  final Connectivity _connectivity;

  /// The cache box for storing exchange rates
  final Box<String> _exchangeRateCache;

  /// The cache box for storing historical exchange rates
  final Box<String> _historicalRateCache;

  /// Creates a new exchange rate API service
  ExchangeRateApiService({
    required String baseUrl,
    required String apiKey,
    required http.Client client,
    required LoggingService loggingService,
    required Box<String> exchangeRateCache,
    required Box<String> historicalRateCache,
  })  : _baseUrl = baseUrl,
        _apiKey = apiKey,
        _client = client,
        _loggingService = loggingService,
        _connectivity = Connectivity(),
        _exchangeRateCache = exchangeRateCache,
        _historicalRateCache = historicalRateCache;

  /// Get the latest exchange rates for a base currency
  Future<ExchangeRatesCollection> getLatestRates(String baseCurrency) async {
    // Check if we have a cached version
    final cacheKey = 'rates_$baseCurrency';
    final cachedData = _exchangeRateCache.get(cacheKey);

    // Check network connectivity
    final connectivityResult = await _connectivity.checkConnectivity();
    final isOnline = connectivityResult != ConnectivityResult.none;

    // If we're offline and have cached data, use it
    if (!isOnline && cachedData != null) {
      try {
        final json = jsonDecode(cachedData);
        final rates = ExchangeRatesCollection.fromJson(json);
        return rates.copyWith(isFromCache: true);
      } catch (e, stackTrace) {
        _loggingService.error(
          'ExchangeRateApiService',
          'Error parsing cached exchange rates',
          e,
          stackTrace,
        );
        // If we can't parse the cached data, continue to try the API
      }
    }

    // If we're offline and don't have cached data, return a mock response
    if (!isOnline) {
      return _getMockExchangeRates(baseCurrency);
    }

    try {
      // In a real app, this would be an actual API call
      // For demo purposes, we'll simulate a network request
      await Future.delayed(const Duration(milliseconds: 800));

      // For demo purposes, we'll use mock data
      final rates = _getMockExchangeRates(baseCurrency);

      // Cache the result
      await _cacheExchangeRates(cacheKey, rates);

      return rates;
    } catch (e, stackTrace) {
      _loggingService.error(
        'ExchangeRateApiService',
        'Error fetching exchange rates',
        e,
        stackTrace,
      );

      // If we have cached data, return it as fallback
      if (cachedData != null) {
        try {
          final json = jsonDecode(cachedData);
          final rates = ExchangeRatesCollection.fromJson(json);
          return rates.copyWith(isFromCache: true);
        } catch (e, stackTrace) {
          _loggingService.error(
            'ExchangeRateApiService',
            'Error parsing cached exchange rates',
            e,
            stackTrace,
          );
        }
      }

      // If all else fails, return mock data
      return _getMockExchangeRates(baseCurrency);
    }
  }

  /// Get a specific exchange rate
  Future<ExchangeRateModel> getExchangeRate(
    String baseCurrency,
    String targetCurrency,
  ) async {
    final rates = await getLatestRates(baseCurrency);
    try {
      return rates.getRate(targetCurrency);
    } catch (e) {
      throw Exception('Exchange rate not found for $targetCurrency');
    }
  }

  /// Get historical exchange rates
  Future<CurrencyConversionHistoryModel> getHistoricalRates(
    String baseCurrency,
    String targetCurrency, {
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    // Default to last 30 days if not specified
    final end = endDate ?? DateTime.now();
    final start = startDate ?? end.subtract(const Duration(days: 30));

    // Check if we have a cached version
    final cacheKey =
        'history_${baseCurrency}_${targetCurrency}_${start.toIso8601String()}_${end.toIso8601String()}';
    final cachedData = _historicalRateCache.get(cacheKey);

    // Check network connectivity
    final connectivityResult = await _connectivity.checkConnectivity();
    final isOnline = connectivityResult != ConnectivityResult.none;

    // If we're offline and have cached data, use it
    if (!isOnline && cachedData != null) {
      try {
        final json = jsonDecode(cachedData);
        final history = CurrencyConversionHistoryModel.fromJson(json);
        return history.copyWith(isFromCache: true);
      } catch (e, stackTrace) {
        _loggingService.error(
          'ExchangeRateApiService',
          'Error parsing cached historical rates',
          e,
          stackTrace,
        );
        // If we can't parse the cached data, continue to try the API
      }
    }

    // If we're offline and don't have cached data, return a mock response
    if (!isOnline) {
      return _getMockHistoricalRates(baseCurrency, targetCurrency, start, end);
    }

    try {
      // In a real app, this would be an actual API call
      // For demo purposes, we'll simulate a network request
      await Future.delayed(const Duration(milliseconds: 1200));

      // For demo purposes, we'll use mock data
      final history =
          _getMockHistoricalRates(baseCurrency, targetCurrency, start, end);

      // Cache the result
      await _cacheHistoricalRates(cacheKey, history);

      return history;
    } catch (e, stackTrace) {
      _loggingService.error(
        'ExchangeRateApiService',
        'Error fetching historical rates',
        e,
        stackTrace,
      );

      // If we have cached data, return it as fallback
      if (cachedData != null) {
        try {
          final json = jsonDecode(cachedData);
          final history = CurrencyConversionHistoryModel.fromJson(json);
          return history.copyWith(isFromCache: true);
        } catch (e, stackTrace) {
          _loggingService.error(
            'ExchangeRateApiService',
            'Error parsing cached historical rates',
            e,
            stackTrace,
          );
        }
      }

      // If all else fails, return mock data
      return _getMockHistoricalRates(baseCurrency, targetCurrency, start, end);
    }
  }

  /// Cache exchange rates
  Future<void> _cacheExchangeRates(
    String cacheKey,
    ExchangeRatesCollection rates,
  ) async {
    try {
      await _exchangeRateCache.put(cacheKey, jsonEncode(rates.toJson()));
    } catch (e, stackTrace) {
      _loggingService.error(
        'ExchangeRateApiService',
        'Error caching exchange rates',
        e,
        stackTrace,
      );
    }
  }

  /// Cache historical rates
  Future<void> _cacheHistoricalRates(
    String cacheKey,
    CurrencyConversionHistoryModel history,
  ) async {
    try {
      await _historicalRateCache.put(cacheKey, jsonEncode(history.toJson()));
    } catch (e, stackTrace) {
      _loggingService.error(
        'ExchangeRateApiService',
        'Error caching historical rates',
        e,
        stackTrace,
      );
    }
  }

  /// Get mock exchange rates for demo purposes
  ExchangeRatesCollection _getMockExchangeRates(String baseCurrency) {
    final now = DateTime.now();
    final rates = <String, double>{};

    // Generate mock rates based on the base currency
    if (baseCurrency == 'USD') {
      rates['EUR'] = 0.85;
      rates['GBP'] = 0.75;
      rates['JPY'] = 110.0;
      rates['CAD'] = 1.25;
      rates['AUD'] = 1.35;
      rates['CHF'] = 0.92;
      rates['CNY'] = 6.45;
      rates['HKD'] = 7.78;
      rates['NZD'] = 1.42;
      rates['SEK'] = 8.65;
      rates['KRW'] = 1150.0;
      rates['SGD'] = 1.35;
      rates['NOK'] = 8.75;
      rates['MXN'] = 20.0;
      rates['INR'] = 74.5;
      rates['RUB'] = 73.5;
      rates['ZAR'] = 14.5;
      rates['TRY'] = 8.65;
      rates['BRL'] = 5.25;
      rates['TWD'] = 28.0;
      rates['DKK'] = 6.35;
      rates['PLN'] = 3.85;
      rates['THB'] = 33.0;
      rates['IDR'] = 14500.0;
      rates['HUF'] = 300.0;
      rates['CZK'] = 21.75;
      rates['ILS'] = 3.25;
      rates['CLP'] = 750.0;
      rates['PHP'] = 50.0;
      rates['AED'] = 3.67;
      rates['COP'] = 3800.0;
      rates['SAR'] = 3.75;
      rates['MYR'] = 4.2;
      rates['RON'] = 4.15;
      // African currencies
      rates['NGN'] = 410.0;
      rates['KES'] = 108.0;
      rates['GHS'] = 6.0;
      rates['EGP'] = 15.7;
      // Cryptocurrencies
      rates['BTC'] = 0.000025;
      rates['ETH'] = 0.00035;
    } else {
      // For other base currencies, we'll convert from USD rates
      // This is a simplified approach for demo purposes
      final usdRates = _getMockExchangeRates('USD').rates;
      final usdToBase = usdRates[baseCurrency] ?? 1.0;

      for (final entry in usdRates.entries) {
        if (entry.key != baseCurrency) {
          rates[entry.key] = entry.value / usdToBase;
        }
      }

      // Add USD rate
      rates['USD'] = 1.0 / usdToBase;
    }

    return ExchangeRatesCollection(
      baseCurrency: baseCurrency,
      timestamp: now,
      rates: rates,
      isFromCache: false,
      source: 'Mock API',
    );
  }

  /// Get mock historical rates for demo purposes
  CurrencyConversionHistoryModel _getMockHistoricalRates(
    String baseCurrency,
    String targetCurrency,
    DateTime startDate,
    DateTime endDate,
  ) {
    final dataPoints = <ExchangeRateHistoryPoint>[];
    final random = DateTime.now().millisecondsSinceEpoch % 100;

    // Get the current exchange rate as a starting point
    final currentRates = _getMockExchangeRates(baseCurrency);
    final currentRate = currentRates.rates[targetCurrency] ?? 1.0;

    // Generate data points for each day in the range
    final days = endDate.difference(startDate).inDays;
    for (var i = 0; i <= days; i++) {
      final date = startDate.add(Duration(days: i));

      // Generate a slightly random rate based on the current rate
      // This creates a somewhat realistic looking chart
      const volatility = 0.02; // 2% daily volatility
      final dailyChange = (random / 100 - 0.5) * volatility;
      final daysFactor = 1.0 + (i / days) * (random % 2 == 0 ? 0.1 : -0.1);
      final rate = currentRate * daysFactor * (1 + dailyChange);

      dataPoints.add(ExchangeRateHistoryPoint(
        date: date,
        rate: rate,
      ));
    }

    return CurrencyConversionHistoryModel(
      baseCurrency: baseCurrency,
      targetCurrency: targetCurrency,
      dataPoints: dataPoints,
      startDate: startDate,
      endDate: endDate,
      source: 'Mock API',
      isFromCache: false,
    );
  }

  /// Dispose resources
  void dispose() {
    _client.close();
  }
}
