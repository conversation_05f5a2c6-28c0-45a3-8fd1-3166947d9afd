import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import 'package:culture_connect/models/translation/image_text_translation_model.dart';
import 'package:culture_connect/models/translation/translation_confidence_model.dart';
import 'package:culture_connect/models/translation/translation_cultural_context.dart';
import 'package:culture_connect/models/translation/custom_vocabulary_model.dart';
import 'package:culture_connect/services/image_text_recognition_service.dart';
import 'package:culture_connect/services/voice_translation/voice_translation_service.dart';
import 'package:culture_connect/services/voice_translation/custom_vocabulary_service.dart';
import 'package:culture_connect/services/voice_translation/cultural_context_service.dart';
import 'package:culture_connect/providers/shared_preferences_provider.dart'
    as app_prefs;

/// A service for translating text from images
class ImageTextTranslationService {
  /// The image text recognition service
  final ImageTextRecognitionService _recognitionService;

  /// The voice translation service
  final VoiceTranslationService _translationService;

  /// The custom vocabulary service
  final CustomVocabularyService _customVocabularyService;

  /// The cultural context service
  final CulturalContextService _culturalContextService;

  /// The shared preferences instance
  final SharedPreferences _prefs;

  /// The HTTP client
  final http.Client _client;

  /// The translation events controller
  final StreamController<String> _translationEventsController =
      StreamController<String>.broadcast();

  /// The translation history
  List<ImageTextTranslationModel> _translationHistory = [];

  /// Whether to use offline mode
  bool _useOfflineMode = false;

  /// Whether to use custom vocabulary
  bool _useCustomVocabulary = true;

  /// Whether to use cultural context
  bool _useCulturalContext = true;

  /// Creates a new image text translation service
  ImageTextTranslationService(
    this._recognitionService,
    this._translationService,
    this._customVocabularyService,
    this._culturalContextService,
    this._prefs,
    this._client,
  ) {
    _loadSettings();
    _loadTranslationHistory();
  }

  /// Load settings from shared preferences
  Future<void> _loadSettings() async {
    _useOfflineMode =
        _prefs.getBool('image_text_translation_offline_mode') ?? false;
    _useCustomVocabulary =
        _prefs.getBool('image_text_translation_custom_vocabulary') ?? true;
    _useCulturalContext = _prefs.getBool('use_cultural_context') ?? true;
  }

  /// Save settings to shared preferences
  Future<void> _saveSettings() async {
    await _prefs.setBool(
        'image_text_translation_offline_mode', _useOfflineMode);
    await _prefs.setBool(
        'image_text_translation_custom_vocabulary', _useCustomVocabulary);
    await _prefs.setBool('use_cultural_context', _useCulturalContext);
  }

  /// Load translation history from shared preferences
  Future<void> _loadTranslationHistory() async {
    try {
      final historyJson = _prefs.getString('image_text_translation_history');
      if (historyJson != null) {
        final List<dynamic> historyList = jsonDecode(historyJson);
        _translationHistory = historyList
            .map((item) => ImageTextTranslationModel.fromJson(item))
            .toList();

        // Filter out translations with missing image files
        _translationHistory = await Future.wait(
          _translationHistory.map((translation) async {
            final exists = await translation.imageExists();
            return exists ? translation : null;
          }),
        ).then((list) => list.whereType<ImageTextTranslationModel>().toList());

        // Sort by timestamp (newest first)
        _translationHistory.sort((a, b) => b.timestamp.compareTo(a.timestamp));
      }
    } catch (e) {
      debugPrint('Error loading translation history: $e');
      _translationHistory = [];
    }
  }

  /// Save translation history to shared preferences
  Future<void> _saveTranslationHistory() async {
    try {
      // Limit history to 50 items
      if (_translationHistory.length > 50) {
        _translationHistory = _translationHistory.sublist(0, 50);
      }

      final historyJson =
          jsonEncode(_translationHistory.map((item) => item.toJson()).toList());
      await _prefs.setString('image_text_translation_history', historyJson);
    } catch (e) {
      debugPrint('Error saving translation history: $e');
    }
  }

  /// Get the translation history
  List<ImageTextTranslationModel> get translationHistory => _translationHistory;

  /// Get the translation events stream
  Stream<String> get translationEventsStream =>
      _translationEventsController.stream;

  /// Get whether to use offline mode
  bool get useOfflineMode => _useOfflineMode;

  /// Set whether to use offline mode
  Future<void> setUseOfflineMode(bool value) async {
    _useOfflineMode = value;
    await _saveSettings();
    _translationEventsController
        .add('Offline mode ${value ? 'enabled' : 'disabled'}');
  }

  /// Get whether to use custom vocabulary
  bool get useCustomVocabulary => _useCustomVocabulary;

  /// Set whether to use custom vocabulary
  Future<void> setUseCustomVocabulary(bool value) async {
    _useCustomVocabulary = value;
    await _saveSettings();
    _translationEventsController
        .add('Custom vocabulary ${value ? 'enabled' : 'disabled'}');
  }

  /// Get whether to use cultural context
  bool get useCulturalContext => _useCulturalContext;

  /// Set whether to use cultural context
  Future<void> setUseCulturalContext(bool value) async {
    _useCulturalContext = value;
    await _saveSettings();
    _translationEventsController
        .add('Cultural context ${value ? 'enabled' : 'disabled'}');
  }

  /// Translate text from an image
  Future<ImageTextTranslationModel> translateImageText(
    String imagePath,
    String targetLanguage,
  ) async {
    // Create a new translation model
    final translation = ImageTextTranslationModel.create(
      imagePath: imagePath,
      targetLanguage: targetLanguage,
      status: ImageTextTranslationStatus.inProgress,
    );

    // Add to history
    _translationHistory.insert(0, translation);
    await _saveTranslationHistory();

    try {
      // 1. Recognize text in the image
      _translationEventsController.add('Recognizing text in image');
      final recognizedBlocks =
          await _recognitionService.recognizeText(imagePath);

      if (recognizedBlocks.isEmpty) {
        throw Exception('No text found in the image');
      }

      // 2. Get combined text
      final recognizedText =
          _recognitionService.getCombinedText(recognizedBlocks);

      // 3. Detect source language
      _translationEventsController.add('Detecting language');
      final sourceLanguage =
          await _recognitionService.detectDominantLanguage(recognizedText);

      if (sourceLanguage == null) {
        throw Exception('Could not detect language');
      }

      // 4. Get custom vocabulary if enabled
      List<CustomVocabularyModel>? customVocabulary;
      if (_useCustomVocabulary) {
        customVocabulary = _customVocabularyService
            .getVocabularyTermsByLanguage(sourceLanguage);
      }

      // 5. Translate the text
      _translationEventsController
          .add('Translating text from $sourceLanguage to $targetLanguage');
      final translatedText = await _translationService.translateText(
        recognizedText,
        sourceLanguage,
        targetLanguage,
        useOffline: _useOfflineMode,
        customVocabulary: customVocabulary,
      );

      // 6. Generate confidence information
      final confidence = TranslationConfidenceModel.fromScore(0.85);

      // 7. Get cultural context information if enabled
      TranslationCulturalContext? culturalContext;
      if (_useCulturalContext) {
        _translationEventsController
            .add('Getting cultural context information');
        try {
          culturalContext = await _culturalContextService.getCulturalContext(
            text: translatedText,
            sourceLanguage: sourceLanguage,
            targetLanguage: targetLanguage,
            sourceRegion: null,
            targetRegion: null,
          );
        } catch (e) {
          debugPrint('Error getting cultural context: $e');
        }
      }

      // 8. Update the translation model
      final updatedTranslation = translation.copyWith(
        recognizedTextBlocks: recognizedBlocks,
        recognizedText: recognizedText,
        translatedText: translatedText,
        sourceLanguage: sourceLanguage,
        status: ImageTextTranslationStatus.completed,
        isOfflineTranslation: _useOfflineMode,
        confidence: confidence,
        culturalContext: culturalContext,
        showCulturalContext: _useCulturalContext,
      );

      // 8. Update history
      final index =
          _translationHistory.indexWhere((item) => item.id == translation.id);
      if (index >= 0) {
        _translationHistory[index] = updatedTranslation;
        await _saveTranslationHistory();
      }

      _translationEventsController.add('Translation completed');
      return updatedTranslation;
    } catch (e) {
      // Update the translation model with error
      final updatedTranslation = translation.copyWith(
        status: ImageTextTranslationStatus.failed,
        errorMessage: e.toString(),
      );

      // Update history
      final index =
          _translationHistory.indexWhere((item) => item.id == translation.id);
      if (index >= 0) {
        _translationHistory[index] = updatedTranslation;
        await _saveTranslationHistory();
      }

      _translationEventsController.add('Translation failed: $e');
      return updatedTranslation;
    }
  }

  /// Toggle favorite status of a translation
  Future<ImageTextTranslationModel> toggleFavorite(String id) async {
    final index = _translationHistory.indexWhere((item) => item.id == id);
    if (index < 0) {
      throw Exception('Translation not found');
    }

    final translation = _translationHistory[index];
    final updatedTranslation = translation.copyWith(
      isFavorite: !translation.isFavorite,
    );

    _translationHistory[index] = updatedTranslation;
    await _saveTranslationHistory();

    return updatedTranslation;
  }

  /// Delete a translation
  Future<void> deleteTranslation(String id) async {
    final index = _translationHistory.indexWhere((item) => item.id == id);
    if (index < 0) {
      throw Exception('Translation not found');
    }

    _translationHistory.removeAt(index);
    await _saveTranslationHistory();
  }

  /// Clear translation history
  Future<void> clearHistory() async {
    _translationHistory = [];
    await _saveTranslationHistory();
  }

  /// Dispose the service
  Future<void> dispose() async {
    await _translationEventsController.close();
    _client.close();
  }
}

/// Provider for the image text translation service
final imageTextTranslationServiceProvider =
    Provider<ImageTextTranslationService>((ref) {
  final recognitionService = ref.watch(imageTextRecognitionServiceProvider);
  final translationService = ref.watch(voiceTranslationServiceProvider);
  final customVocabularyService = ref.watch(customVocabularyServiceProvider);
  final culturalContextService = ref.watch(culturalContextServiceProvider);
  final prefs = ref.watch(app_prefs.sharedPreferencesProvider);

  final client = http.Client();

  final service = ImageTextTranslationService(
    recognitionService,
    translationService,
    customVocabularyService,
    culturalContextService,
    prefs,
    client,
  );

  ref.onDispose(() {
    service.dispose();
  });

  return service;
});
