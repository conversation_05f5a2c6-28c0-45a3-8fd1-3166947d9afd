import 'dart:io';
import 'dart:typed_data';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:flutter/foundation.dart';
import 'package:path/path.dart' as path;
import 'package:uuid/uuid.dart';
import 'package:culture_connect/services/logging_service.dart';
import 'package:culture_connect/utils/file_utils.dart';

/// Service for handling file storage operations
class StorageService {
  /// Firebase Storage instance
  final FirebaseStorage _storage;

  /// Logging service
  final LoggingService _loggingService;

  /// UUID generator
  final Uuid _uuid;

  /// Creates a new storage service
  StorageService({
    FirebaseStorage? storage,
    required LoggingService loggingService,
  })  : _storage = storage ?? FirebaseStorage.instance,
        _loggingService = loggingService,
        _uuid = const Uuid();

  /// Upload a file to storage
  ///
  /// Returns the download URL of the uploaded file, or null if the upload failed
  ///
  /// [file] - The file to upload
  /// [path] - The path to upload the file to
  /// [customFileName] - A custom filename to use (optional)
  /// [metadata] - Custom metadata to attach to the file (optional)
  /// [onProgress] - Callback for upload progress (optional)
  Future<String?> uploadFile({
    required File file,
    required String path,
    String? customFileName,
    Map<String, String>? metadata,
    Function(int bytesTransferred, int totalBytes)? onProgress,
  }) async {
    try {
      // Generate a unique filename if not provided
      final fileName = customFileName ?? _generateFileName(file);
      final fullPath = '$path/$fileName';

      // Create a reference to the file location
      final ref = _storage.ref().child(fullPath);

      // Upload the file
      final uploadTask = ref.putFile(
        file,
        SettableMetadata(
          contentType: FileUtils.getMimeType(file.path),
          customMetadata: metadata,
        ),
      );

      // Listen for progress updates
      if (onProgress != null) {
        uploadTask.snapshotEvents.listen((TaskSnapshot snapshot) {
          final bytesTransferred = snapshot.bytesTransferred;
          final totalBytes = snapshot.totalBytes;
          onProgress(bytesTransferred, totalBytes);
        });
      }

      // Wait for the upload to complete
      final snapshot = await uploadTask;

      // Get the download URL
      final downloadUrl = await snapshot.ref.getDownloadURL();
      _loggingService.info(
          'StorageService', 'File uploaded successfully: $fullPath');
      return downloadUrl;
    } catch (e, stackTrace) {
      _loggingService.error(
        'StorageService',
        'Error uploading file',
        e,
        stackTrace,
      );
      return null;
    }
  }

  /// Upload data to storage
  ///
  /// Returns the download URL of the uploaded data, or null if the upload failed
  Future<String?> uploadData({
    required Uint8List data,
    required String path,
    required String fileName,
    String? contentType,
    Map<String, String>? metadata,
  }) async {
    try {
      // Create a reference to the file location
      final fullPath = '$path/$fileName';
      final ref = _storage.ref().child(fullPath);

      // Upload the data
      final uploadTask = ref.putData(
        data,
        SettableMetadata(
          contentType: contentType,
          customMetadata: metadata,
        ),
      );

      // Wait for the upload to complete
      final snapshot = await uploadTask;

      // Get the download URL
      final downloadUrl = await snapshot.ref.getDownloadURL();
      _loggingService.info(
          'StorageService', 'Data uploaded successfully: $fullPath');
      return downloadUrl;
    } catch (e, stackTrace) {
      _loggingService.error(
        'StorageService',
        'Error uploading data',
        e,
        stackTrace,
      );
      return null;
    }
  }

  /// Download a file from storage
  ///
  /// Returns the downloaded file, or null if the download failed
  Future<File?> downloadFile({
    required String url,
    required String localPath,
  }) async {
    try {
      // Create a reference to the file
      final ref = _storage.refFromURL(url);

      // Create the local file
      final file = File(localPath);

      // Download the file
      await ref.writeToFile(file);
      _loggingService.info(
          'StorageService', 'File downloaded successfully: $localPath');
      return file;
    } catch (e, stackTrace) {
      _loggingService.error(
        'StorageService',
        'Error downloading file',
        e,
        stackTrace,
      );
      return null;
    }
  }

  /// Delete a file from storage
  ///
  /// Returns true if the file was deleted successfully, false otherwise
  Future<bool> deleteFile(String url) async {
    try {
      // Create a reference to the file
      final ref = _storage.refFromURL(url);

      // Delete the file
      await ref.delete();
      _loggingService.info('StorageService', 'File deleted successfully: $url');
      return true;
    } catch (e, stackTrace) {
      _loggingService.error(
        'StorageService',
        'Error deleting file',
        e,
        stackTrace,
      );
      return false;
    }
  }

  /// Get metadata for a file
  ///
  /// Returns the metadata for the file, or null if the metadata could not be retrieved
  Future<Map<String, String>?> getMetadata(String url) async {
    try {
      // Create a reference to the file
      final ref = _storage.refFromURL(url);

      // Get the metadata
      final metadata = await ref.getMetadata();
      return metadata.customMetadata;
    } catch (e, stackTrace) {
      _loggingService.error(
        'StorageService',
        'Error getting metadata',
        e,
        stackTrace,
      );
      return null;
    }
  }

  /// Update metadata for a file
  ///
  /// Returns true if the metadata was updated successfully, false otherwise
  Future<bool> updateMetadata({
    required String url,
    required Map<String, String> metadata,
  }) async {
    try {
      // Create a reference to the file
      final ref = _storage.refFromURL(url);

      // Update the metadata
      await ref.updateMetadata(
        SettableMetadata(customMetadata: metadata),
      );
      _loggingService.info(
          'StorageService', 'Metadata updated successfully: $url');
      return true;
    } catch (e, stackTrace) {
      _loggingService.error(
        'StorageService',
        'Error updating metadata',
        e,
        stackTrace,
      );
      return false;
    }
  }

  /// Generate a unique filename for a file
  String _generateFileName(File file) {
    final extension = path.extension(file.path);
    final uuid = _uuid.v4();
    return '$uuid$extension';
  }
}
