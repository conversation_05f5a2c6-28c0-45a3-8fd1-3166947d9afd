import 'dart:async';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:culture_connect/services/logging_service.dart';

/// Service for checking network connectivity
class ConnectivityService {
  // Singleton instance
  static final ConnectivityService _instance = ConnectivityService._internal();
  factory ConnectivityService() => _instance;
  ConnectivityService._internal() {
    _initConnectivity();
    _connectivity.onConnectivityChanged.listen(_updateConnectionStatus);
  }
  
  // Connectivity instance
  final Connectivity _connectivity = Connectivity();
  
  // Current connectivity status
  ConnectivityResult _connectionStatus = ConnectivityResult.none;
  
  // Stream controller for connectivity status
  final _connectivityStreamController = StreamController<ConnectivityResult>.broadcast();
  
  // Stream of connectivity status
  Stream<ConnectivityResult> get connectivityStream => _connectivityStreamController.stream;
  
  // Logging service
  final LoggingService _loggingService = LoggingService();
  
  /// Initialize connectivity
  Future<void> _initConnectivity() async {
    try {
      _connectionStatus = await _connectivity.checkConnectivity();
      _connectivityStreamController.add(_connectionStatus);
    } catch (e, stackTrace) {
      _loggingService.error(
        'ConnectivityService',
        'Error initializing connectivity',
        e,
        stackTrace,
      );
    }
  }
  
  /// Update connection status
  void _updateConnectionStatus(ConnectivityResult result) {
    _connectionStatus = result;
    _connectivityStreamController.add(result);
  }
  
  /// Check if device is connected to the internet
  Future<bool> isConnected() async {
    try {
      final result = await _connectivity.checkConnectivity();
      return result != ConnectivityResult.none;
    } catch (e, stackTrace) {
      _loggingService.error(
        'ConnectivityService',
        'Error checking connectivity',
        e,
        stackTrace,
      );
      return false;
    }
  }
  
  /// Get current connection type
  ConnectivityResult get connectionType => _connectionStatus;
  
  /// Dispose resources
  void dispose() {
    _connectivityStreamController.close();
  }
}
