import 'dart:async';
import 'dart:convert';

// Flutter imports
import 'package:flutter/material.dart';

// Package imports
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';

// Project imports
import 'package:culture_connect/models/translation/language_model.dart';

/// A simple implementation of SharedPreferences for the provider
class SimpleSharedPreferences implements SharedPreferences {
  final Map<String, Object> _data = {};

  @override
  bool containsKey(String key) => _data.containsKey(key);

  @override
  Object? get(String key) => _data[key];

  @override
  bool? getBool(String key) => _data[key] as bool?;

  @override
  double? getDouble(String key) => _data[key] as double?;

  @override
  int? getInt(String key) => _data[key] as int?;

  @override
  Set<String> getKeys() => _data.keys.toSet();

  @override
  String? getString(String key) => _data[key] as String?;

  @override
  List<String>? getStringList(String key) => _data[key] as List<String>?;

  @override
  Future<bool> setBool(String key, bool value) async {
    _data[key] = value;
    return true;
  }

  @override
  Future<bool> setDouble(String key, double value) async {
    _data[key] = value;
    return true;
  }

  @override
  Future<bool> setInt(String key, int value) async {
    _data[key] = value;
    return true;
  }

  @override
  Future<bool> setString(String key, String value) async {
    _data[key] = value;
    return true;
  }

  @override
  Future<bool> setStringList(String key, List<String> value) async {
    _data[key] = value;
    return true;
  }

  @override
  Future<bool> remove(String key) async {
    _data.remove(key);
    return true;
  }

  @override
  Future<bool> clear() async {
    _data.clear();
    return true;
  }

  @override
  Future<void> reload() async {}

  @override
  Future<bool> commit() async => true;
}

/// A service for detecting languages in text
class LanguageDetectionService {
  /// The HTTP client
  final http.Client _client;

  /// The shared preferences instance
  final SharedPreferences _prefs;

  /// API key for language detection service
  final String _apiKey;

  /// Cache for language detection results
  final Map<String, String> _detectionCache = {};

  /// Stream controller for language detection events
  final StreamController<String> _detectionEventsController =
      StreamController<String>.broadcast();

  /// Supported languages for detection
  final List<LanguageModel> _supportedLanguages = [
    const LanguageModel(code: 'en', name: 'English', flag: '🇺🇸'),
    const LanguageModel(code: 'fr', name: 'French', flag: '🇫🇷'),
    const LanguageModel(code: 'es', name: 'Spanish', flag: '🇪🇸'),
    const LanguageModel(code: 'yo', name: 'Yoruba', flag: '🇳🇬'),
    const LanguageModel(code: 'ig', name: 'Igbo', flag: '🇳🇬'),
    const LanguageModel(code: 'ha', name: 'Hausa', flag: '🇳🇬'),
    const LanguageModel(code: 'sw', name: 'Swahili', flag: '🇹🇿'),
    const LanguageModel(code: 'zu', name: 'Zulu', flag: '🇿🇦'),
    const LanguageModel(code: 'xh', name: 'Xhosa', flag: '🇿🇦'),
    const LanguageModel(code: 'ar', name: 'Arabic', flag: '🇸🇦'),
    const LanguageModel(code: 'pt', name: 'Portuguese', flag: '🇵🇹'),
    const LanguageModel(code: 'de', name: 'German', flag: '🇩🇪'),
    const LanguageModel(code: 'it', name: 'Italian', flag: '🇮🇹'),
    const LanguageModel(code: 'ja', name: 'Japanese', flag: '🇯🇵'),
    const LanguageModel(code: 'ko', name: 'Korean', flag: '🇰🇷'),
    const LanguageModel(code: 'zh', name: 'Chinese', flag: '🇨🇳'),
    const LanguageModel(code: 'ru', name: 'Russian', flag: '🇷🇺'),
  ];

  /// Creates a new language detection service
  LanguageDetectionService(this._client, this._prefs, this._apiKey) {
    _loadCachedDetections();
  }

  /// Stream of language detection events
  Stream<String> get detectionEvents => _detectionEventsController.stream;

  /// Get supported languages
  List<LanguageModel> get supportedLanguages => _supportedLanguages;

  /// Load cached detections from shared preferences
  Future<void> _loadCachedDetections() async {
    try {
      final cachedDetections = _prefs.getString('language_detection_cache');
      if (cachedDetections != null) {
        final Map<String, dynamic> decoded = jsonDecode(cachedDetections);
        decoded.forEach((key, value) {
          _detectionCache[key] = value.toString();
        });
      }
    } catch (e) {
      debugPrint('Error loading cached language detections: $e');
    }
  }

  /// Save detection cache to shared preferences
  Future<void> _saveDetectionCache() async {
    try {
      await _prefs.setString(
          'language_detection_cache', jsonEncode(_detectionCache));
    } catch (e) {
      debugPrint('Error saving language detection cache: $e');
    }
  }

  /// Get cache key for a text
  String _getCacheKey(String text) {
    // Use a hash of the text to avoid storing large strings as keys
    return text.hashCode.toString();
  }

  /// Detect the language of a text
  Future<String> detectLanguage(String text) async {
    // For very short texts, default to English
    if (text.trim().length < 3) {
      return 'en';
    }

    // Check cache first
    final cacheKey = _getCacheKey(text);
    if (_detectionCache.containsKey(cacheKey)) {
      return _detectionCache[cacheKey]!;
    }

    try {
      // For demo purposes, we'll use a mock implementation
      // In a real app, this would call a language detection API
      final detectedLanguage = await _mockDetectLanguage(text);

      // Cache the result
      _detectionCache[cacheKey] = detectedLanguage;
      await _saveDetectionCache();

      // Notify listeners
      _detectionEventsController.add(cacheKey);

      return detectedLanguage;
    } catch (e) {
      debugPrint('Error detecting language: $e');
      // Default to English if detection fails
      return 'en';
    }
  }

  /// Mock language detection for demo purposes
  Future<String> _mockDetectLanguage(String text) async {
    // Simulate network delay
    await Future.delayed(const Duration(milliseconds: 300));

    // Simple mock detection based on common words
    final lowerText = text.toLowerCase();

    // Check for French
    if (lowerText.contains('bonjour') ||
        lowerText.contains('merci') ||
        lowerText.contains('je suis') ||
        lowerText.contains('comment') ||
        lowerText.contains('vous')) {
      return 'fr';
    }

    // Check for Spanish
    if (lowerText.contains('hola') ||
        lowerText.contains('gracias') ||
        lowerText.contains('buenos días') ||
        lowerText.contains('cómo estás') ||
        lowerText.contains('señor')) {
      return 'es';
    }

    // Check for Yoruba
    if (lowerText.contains('bawo ni') ||
        lowerText.contains('jowo') ||
        lowerText.contains('e kaaro')) {
      return 'yo';
    }

    // Check for Igbo
    if (lowerText.contains('kedu') ||
        lowerText.contains('biko') ||
        lowerText.contains('daalu')) {
      return 'ig';
    }

    // Check for Hausa
    if (lowerText.contains('sannu') ||
        lowerText.contains('yaya') ||
        lowerText.contains('na gode')) {
      return 'ha';
    }

    // Check for Swahili
    if (lowerText.contains('jambo') ||
        lowerText.contains('habari') ||
        lowerText.contains('asante')) {
      return 'sw';
    }

    // Default to English
    return 'en';
  }

  /// Clear the detection cache
  Future<void> clearCache() async {
    _detectionCache.clear();
    await _saveDetectionCache();
  }

  /// Dispose resources
  void dispose() {
    _detectionEventsController.close();
    _client.close();
  }
}

/// Provider for the language detection service
final languageDetectionServiceProvider =
    Provider<LanguageDetectionService>((ref) {
  const apiKey =
      'mock_api_key'; // In a real app, this would come from environment variables
  final client = http.Client();

  // Create a simple SharedPreferences implementation
  // In a real app, this would be properly injected
  final simplePrefs = SimpleSharedPreferences();

  // Create service
  final service = LanguageDetectionService(client, simplePrefs, apiKey);

  ref.onDispose(() {
    service.dispose();
  });

  return service;
});
