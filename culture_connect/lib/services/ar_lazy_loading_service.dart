import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:path_provider/path_provider.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// A service that handles lazy loading of AR features.
class ARLazyLoadingService {
  static final ARLazyLoadingService _instance =
      ARLazyLoadingService._internal();
  factory ARLazyLoadingService() => _instance;
  ARLazyLoadingService._internal();

  /// Flag to track if AR features are loaded
  bool _arFeaturesLoaded = false;

  /// Flag to track if AR models are loaded
  bool _arModelsLoaded = false;

  /// Flag to track if AR textures are loaded
  bool _arTexturesLoaded = false;

  /// Completer for tracking AR features loading
  final Completer<bool> _arFeaturesLoadingCompleter = Completer<bool>();

  /// Get AR features loading status
  bool get arFeaturesLoaded => _arFeaturesLoaded;

  /// Get AR features loading future
  Future<bool> get arFeaturesLoadingFuture =>
      _arFeaturesLoadingCompleter.future;

  /// Initialize AR features
  Future<bool> initializeARFeatures() async {
    if (_arFeaturesLoaded) return true;

    final stopwatch = Stopwatch()..start();
    debugPrint('🚀 Starting AR features initialization');

    try {
      // Load AR features in parallel
      await Future.wait([
        _loadARModels(),
        _loadARTextures(),
      ]);

      // Mark as loaded
      _arFeaturesLoaded = true;
      _arFeaturesLoadingCompleter.complete(true);

      // Log initialization time
      stopwatch.stop();
      debugPrint(
          '✅ AR features initialized in ${stopwatch.elapsedMilliseconds}ms');

      return true;
    } catch (e) {
      debugPrint('❌ Error during AR features initialization: $e');
      _arFeaturesLoadingCompleter.completeError(e);
      return false;
    }
  }

  /// Load AR models
  Future<void> _loadARModels() async {
    if (_arModelsLoaded) return;

    try {
      final stopwatch = Stopwatch()..start();

      // Get application documents directory
      final appDocDir = await getApplicationDocumentsDirectory();
      final arModelsDir = Directory('${appDocDir.path}/ar_models');

      // Create directory if it doesn't exist
      if (!await arModelsDir.exists()) {
        await arModelsDir.create(recursive: true);
      }

      // Get shared preferences
      final prefs = await SharedPreferences.getInstance();
      final lastModelUpdateTime =
          prefs.getInt('last_ar_model_update_time') ?? 0;

      // Check if models need to be updated (once a day)
      final currentTime = DateTime.now().millisecondsSinceEpoch;
      const oneDayInMillis = 24 * 60 * 60 * 1000;

      if (currentTime - lastModelUpdateTime > oneDayInMillis) {
        // In a real app, you would download models from a server
        // For this example, we'll just copy from assets
        await _copyARModelsFromAssets(arModelsDir);

        // Update last update time
        await prefs.setInt('last_ar_model_update_time', currentTime);
      }

      _arModelsLoaded = true;
      stopwatch.stop();
      debugPrint('✅ AR models loaded in ${stopwatch.elapsedMilliseconds}ms');
    } catch (e) {
      debugPrint('❌ Error loading AR models: $e');
      rethrow;
    }
  }

  /// Copy AR models from assets
  Future<void> _copyARModelsFromAssets(Directory targetDir) async {
    try {
      // List of model files to copy
      final modelFiles = [
        'assets/models/landmark_1.glb',
        'assets/models/landmark_2.glb',
      ];

      // Copy each file
      for (final assetPath in modelFiles) {
        final fileName = assetPath.split('/').last;
        final targetFile = File('${targetDir.path}/$fileName');

        // Check if file already exists
        if (await targetFile.exists()) {
          continue;
        }

        // Load asset
        final data = await rootBundle.load(assetPath);

        // Write to file
        await targetFile.writeAsBytes(
          data.buffer.asUint8List(data.offsetInBytes, data.lengthInBytes),
        );

        debugPrint('✅ Copied AR model: $assetPath to ${targetFile.path}');
      }
    } catch (e) {
      debugPrint('❌ Error copying AR models from assets: $e');
      rethrow;
    }
  }

  /// Load AR textures
  Future<void> _loadARTextures() async {
    if (_arTexturesLoaded) return;

    try {
      final stopwatch = Stopwatch()..start();

      // Get application documents directory
      final appDocDir = await getApplicationDocumentsDirectory();
      final arTexturesDir = Directory('${appDocDir.path}/ar_textures');

      // Create directory if it doesn't exist
      if (!await arTexturesDir.exists()) {
        await arTexturesDir.create(recursive: true);
      }

      // Get shared preferences
      final prefs = await SharedPreferences.getInstance();
      final lastTextureUpdateTime =
          prefs.getInt('last_ar_texture_update_time') ?? 0;

      // Check if textures need to be updated (once a day)
      final currentTime = DateTime.now().millisecondsSinceEpoch;
      const oneDayInMillis = 24 * 60 * 60 * 1000;

      if (currentTime - lastTextureUpdateTime > oneDayInMillis) {
        // In a real app, you would download textures from a server
        // For this example, we'll just copy from assets
        await _copyARTexturesFromAssets(arTexturesDir);

        // Update last update time
        await prefs.setInt('last_ar_texture_update_time', currentTime);
      }

      _arTexturesLoaded = true;
      stopwatch.stop();
      debugPrint('✅ AR textures loaded in ${stopwatch.elapsedMilliseconds}ms');
    } catch (e) {
      debugPrint('❌ Error loading AR textures: $e');
      rethrow;
    }
  }

  /// Copy AR textures from assets
  Future<void> _copyARTexturesFromAssets(Directory targetDir) async {
    try {
      // List of texture files to copy
      final textureFiles = [
        'assets/models/textures/texture_1.jpg',
        'assets/models/textures/texture_2.jpg',
      ];

      // Copy each file
      for (final assetPath in textureFiles) {
        final fileName = assetPath.split('/').last;
        final targetFile = File('${targetDir.path}/$fileName');

        // Check if file already exists
        if (await targetFile.exists()) {
          continue;
        }

        try {
          // Load asset
          final data = await rootBundle.load(assetPath);

          // Write to file
          await targetFile.writeAsBytes(
            data.buffer.asUint8List(data.offsetInBytes, data.lengthInBytes),
          );

          debugPrint('✅ Copied AR texture: $assetPath to ${targetFile.path}');
        } catch (e) {
          // If asset doesn't exist, just log and continue
          debugPrint('⚠️ AR texture not found: $assetPath');
        }
      }
    } catch (e) {
      debugPrint('❌ Error copying AR textures from assets: $e');
      rethrow;
    }
  }

  /// Get AR model file path
  Future<String?> getARModelFilePath(String modelName) async {
    try {
      // Ensure AR features are loaded
      if (!_arFeaturesLoaded) {
        await initializeARFeatures();
      }

      // Get application documents directory
      final appDocDir = await getApplicationDocumentsDirectory();
      final modelFile = File('${appDocDir.path}/ar_models/$modelName');

      // Check if file exists
      if (await modelFile.exists()) {
        return modelFile.path;
      }

      return null;
    } catch (e) {
      debugPrint('❌ Error getting AR model file path: $e');
      return null;
    }
  }

  /// Get AR texture file path
  Future<String?> getARTextureFilePath(String textureName) async {
    try {
      // Ensure AR features are loaded
      if (!_arFeaturesLoaded) {
        await initializeARFeatures();
      }

      // Get application documents directory
      final appDocDir = await getApplicationDocumentsDirectory();
      final textureFile = File('${appDocDir.path}/ar_textures/$textureName');

      // Check if file exists
      if (await textureFile.exists()) {
        return textureFile.path;
      }

      return null;
    } catch (e) {
      debugPrint('❌ Error getting AR texture file path: $e');
      return null;
    }
  }
}
