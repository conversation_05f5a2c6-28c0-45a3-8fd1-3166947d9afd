// Flutter imports
import 'dart:async';
import 'package:flutter/material.dart';

// Package imports
import 'package:speech_to_text/speech_to_text.dart' as stt;

class VoiceService {
  final stt.SpeechToText _speech = stt.SpeechToText();
  bool _isInitialized = false;

  // Stream controller for recognized words
  final StreamController<String> _recognizedWordsController =
      StreamController<String>.broadcast();

  Future<bool> initialize() async {
    if (!_isInitialized) {
      _isInitialized = await _speech.initialize(
        onError: (error) => debugPrint('Speech recognition error: $error'),
        onStatus: (status) => debugPrint('Speech recognition status: $status'),
      );
    }
    return _isInitialized;
  }

  Future<String?> startListening() async {
    if (!_isInitialized) {
      final initialized = await initialize();
      if (!initialized) return null;
    }

    if (!_speech.isListening) {
      await _speech.listen(
        onResult: (result) {
          debugPrint('Speech recognition result: ${result.recognizedWords}');
          _recognizedWordsController.add(result.recognizedWords);
        },
        localeId: 'en_US',
      );
    }

    return null;
  }

  void stopListening() {
    if (_speech.isListening) {
      _speech.stop();
    }
  }

  bool get isListening => _speech.isListening;

  Stream<String> get recognizedWords => _recognizedWordsController.stream;

  void dispose() {
    _recognizedWordsController.close();
  }
}
