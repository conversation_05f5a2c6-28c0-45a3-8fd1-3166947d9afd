import 'dart:async';
import 'dart:io';

// Flutter imports
import 'package:flutter/material.dart';

// Package imports
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_mlkit_language_id/google_mlkit_language_id.dart';
import 'package:google_mlkit_text_recognition/google_mlkit_text_recognition.dart';

// Project imports
import 'package:culture_connect/models/translation/image_text_translation_model.dart';

/// A service for recognizing text in images
class ImageTextRecognitionService {
  /// The text recognizer
  final TextRecognizer _textRecognizer;

  /// The language identifier
  final LanguageIdentifier _languageIdentifier;

  /// The recognition events controller
  final StreamController<String> _recognitionEventsController =
      StreamController<String>.broadcast();

  /// Whether the service is busy
  bool _isBusy = false;

  /// Creates a new image text recognition service
  ImageTextRecognitionService()
      : _textRecognizer = TextRecognizer(),
        _languageIdentifier = LanguageIdentifier(confidenceThreshold: 0.5);

  /// Get the recognition events stream
  Stream<String> get recognitionEventsStream =>
      _recognitionEventsController.stream;

  /// Recognize text in an image
  Future<List<RecognizedTextBlock>> recognizeText(String imagePath) async {
    if (_isBusy) {
      throw Exception('Text recognition is already in progress');
    }

    _isBusy = true;
    _recognitionEventsController.add('Starting text recognition');

    try {
      final inputImage = InputImage.fromFilePath(imagePath);
      final recognizedText = await _textRecognizer.processImage(inputImage);

      // Get image dimensions for normalizing coordinates
      final imageFile = File(imagePath);
      final imageBytes = await imageFile.readAsBytes();
      final imageInfo = await decodeImageFromList(imageBytes);
      final imageWidth = imageInfo.width.toDouble();
      final imageHeight = imageInfo.height.toDouble();

      final recognizedBlocks = <RecognizedTextBlock>[];

      for (final textBlock in recognizedText.blocks) {
        // Normalize coordinates to 0.0-1.0 range
        final normalizedRect = Rect.fromLTRB(
          textBlock.boundingBox.left / imageWidth,
          textBlock.boundingBox.top / imageHeight,
          textBlock.boundingBox.right / imageWidth,
          textBlock.boundingBox.bottom / imageHeight,
        );

        // Detect language for this block
        String? languageCode;
        try {
          final possibleLanguages =
              await _languageIdentifier.identifyLanguage(textBlock.text);
          if (possibleLanguages.isNotEmpty) {
            languageCode = possibleLanguages;
          }
        } catch (e) {
          debugPrint('Error identifying language: $e');
        }

        // Create recognized text block
        final recognizedBlock = RecognizedTextBlock(
          text: textBlock.text,
          boundingBox: normalizedRect,
          confidence: 0.8, // ML Kit doesn't always provide confidence
          languageCode: languageCode,
        );

        recognizedBlocks.add(recognizedBlock);
      }

      _recognitionEventsController.add(
          'Text recognition completed: ${recognizedBlocks.length} blocks found');
      return recognizedBlocks;
    } catch (e) {
      _recognitionEventsController.add('Text recognition failed: $e');
      debugPrint('Error recognizing text: $e');
      rethrow;
    } finally {
      _isBusy = false;
    }
  }

  /// Get the combined text from recognized blocks
  String getCombinedText(List<RecognizedTextBlock> blocks) {
    return blocks.map((block) => block.text).join('\n');
  }

  /// Detect the dominant language in the recognized text
  Future<String?> detectDominantLanguage(String text) async {
    if (text.isEmpty) {
      return null;
    }

    try {
      final possibleLanguages =
          await _languageIdentifier.identifyLanguage(text);
      return possibleLanguages;
    } catch (e) {
      debugPrint('Error identifying language: $e');
      return null;
    }
  }

  /// Dispose the service
  Future<void> dispose() async {
    await _textRecognizer.close();
    await _languageIdentifier.close();
    await _recognitionEventsController.close();
  }
}

/// Provider for the image text recognition service
final imageTextRecognitionServiceProvider =
    Provider<ImageTextRecognitionService>((ref) {
  final service = ImageTextRecognitionService();

  ref.onDispose(() {
    service.dispose();
  });

  return service;
});
