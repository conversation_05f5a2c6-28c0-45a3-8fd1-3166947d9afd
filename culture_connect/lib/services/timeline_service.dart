import 'dart:async';
import 'dart:convert';

import 'package:culture_connect/models/travel/timeline.dart';
import 'package:culture_connect/models/travel/timeline_event.dart';
import 'package:culture_connect/models/travel/itinerary.dart';
import 'package:culture_connect/services/travel/timeline_service.dart' as internal;
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:culture_connect/providers/travel/timeline_providers.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:culture_connect/services/logging_service.dart';
import 'package:culture_connect/services/error_handling_service.dart';

/// A service for managing timelines.
/// 
/// This is a facade that provides access to the timeline service.
/// It exists for backward compatibility with code that imports from
/// 'package:culture_connect/services/timeline_service.dart'.
class TimelineService {
  /// The internal timeline service
  final internal.TimelineService _internalService;
  
  /// Creates a new timeline service.
  /// 
  /// This constructor requires the same dependencies as the internal service.
  TimelineService(
    SharedPreferences preferences,
    LoggingService loggingService,
    ErrorHandlingService? errorHandlingService,
  ) : _internalService = internal.TimelineService(
        preferences,
        loggingService,
        errorHandlingService,
      );
  
  /// Creates a new timeline service with a WidgetRef.
  /// 
  /// This allows access to the full functionality of the timeline service.
  factory TimelineService.fromRef(WidgetRef ref) {
    return TimelineService._withService(
      ref.read(timelineServiceProvider),
    );
  }
  
  /// Creates a new timeline service with the specified service.
  TimelineService._withService(this._internalService);
  
  /// Get all timelines for a user
  Future<List<Timeline>> getTimelines(String userId) async {
    return _internalService.getTimelines(userId);
  }
  
  /// Get a timeline by ID
  Future<Timeline?> getTimeline(String id) async {
    return _internalService.getTimeline(id);
  }
  
  /// Create a new timeline
  Future<Timeline> createTimeline(Timeline timeline) async {
    return _internalService.createTimeline(timeline);
  }
  
  /// Update a timeline
  Future<Timeline> updateTimeline(Timeline timeline) async {
    return _internalService.updateTimeline(timeline);
  }
  
  /// Delete a timeline
  Future<bool> deleteTimeline(String id, String userId) async {
    return _internalService.deleteTimeline(id, userId);
  }
  
  /// Add an event to a timeline
  Future<Timeline> addEventToTimeline(String timelineId, TimelineEvent event) async {
    return _internalService.addEventToTimeline(timelineId, event);
  }
  
  /// Update an event in a timeline
  Future<Timeline> updateEventInTimeline(String timelineId, TimelineEvent event) async {
    return _internalService.updateEventInTimeline(timelineId, event);
  }
  
  /// Remove an event from a timeline
  Future<Timeline> removeEventFromTimeline(String timelineId, String eventId) async {
    return _internalService.removeEventFromTimeline(timelineId, eventId);
  }
  
  /// Create a timeline from an itinerary
  Future<Timeline> createTimelineFromItinerary(Itinerary itinerary) async {
    return _internalService.createTimelineFromItinerary(itinerary);
  }
  
  /// Get timelines for an itinerary
  Future<List<Timeline>> getTimelinesForItinerary(String itineraryId) async {
    return _internalService.getTimelinesForItinerary(itineraryId);
  }
  
  /// Get all timelines for the current user
  Future<List<Timeline>> getCurrentUserTimelines(String currentUserId) async {
    return getTimelines(currentUserId);
  }
  
  /// Get all public timelines
  Future<List<Timeline>> getPublicTimelines() async {
    final allTimelines = await getTimelines('all');
    return allTimelines
        .where((timeline) => timeline.visibility == TimelineVisibility.public)
        .toList();
  }
  
  /// Get timelines shared with a user
  Future<List<Timeline>> getSharedTimelines(String userId) async {
    final allTimelines = await getTimelines('all');
    return allTimelines
        .where((timeline) => 
            timeline.visibility == TimelineVisibility.shared && 
            timeline.userId != userId)
        .toList();
  }
  
  /// Get timelines by theme
  Future<List<Timeline>> getTimelinesByTheme(TimelineTheme theme) async {
    final allTimelines = await getTimelines('all');
    return allTimelines.where((timeline) => timeline.theme == theme).toList();
  }
  
  /// Get timelines for a date range
  Future<List<Timeline>> getTimelinesForDateRange(
      DateTime startDate, DateTime endDate) async {
    final allTimelines = await getTimelines('all');
    return allTimelines.where((timeline) {
      return (timeline.startDate.isAfter(startDate) || 
              timeline.startDate.isAtSameMomentAs(startDate)) && 
             (timeline.endDate.isBefore(endDate) || 
              timeline.endDate.isAtSameMomentAs(endDate));
    }).toList();
  }
  
  /// Get timelines with AR content
  Future<List<Timeline>> getTimelinesWithARContent() async {
    final allTimelines = await getTimelines('all');
    return allTimelines
        .where((timeline) => timeline.eventsWithARContent.isNotEmpty)
        .toList();
  }
  
  /// Share a timeline
  Future<Timeline> shareTimeline(String id, TimelineVisibility visibility) async {
    final timeline = await getTimeline(id);
    if (timeline == null) {
      throw Exception('Timeline not found');
    }
    
    final updatedTimeline = timeline.copyWith(
      visibility: visibility,
      shareUrl: visibility == TimelineVisibility.private 
          ? null 
          : 'https://cultureconnect.app/timeline/$id',
    );
    
    return updateTimeline(updatedTimeline);
  }
}
