import 'dart:async';
import 'dart:convert';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:http/http.dart' as http;
import 'package:culture_connect/models/travel/travel.dart';

/// Model for availability information
class AvailabilityInfo {
  /// Type of service (hotel, flight, car, restaurant, security, cruise)
  final String serviceType;

  /// ID of the service
  final String serviceId;

  /// Whether the service is available
  final bool isAvailable;

  /// Number of available units (rooms, seats, cars, etc.)
  final int availableCount;

  /// Next available date if not currently available
  final DateTime? nextAvailableDate;

  /// Timestamp of when this information was retrieved
  final DateTime timestamp;

  /// Whether this information is from cache
  final bool isFromCache;

  /// Creates a new availability info
  const AvailabilityInfo({
    required this.serviceType,
    required this.serviceId,
    required this.isAvailable,
    required this.availableCount,
    this.nextAvailableDate,
    required this.timestamp,
    this.isFromCache = false,
  });

  /// Creates a copy with updated fields
  AvailabilityInfo copyWith({
    String? serviceType,
    String? serviceId,
    bool? isAvailable,
    int? availableCount,
    DateTime? nextAvailableDate,
    DateTime? timestamp,
    bool? isFromCache,
  }) {
    return AvailabilityInfo(
      serviceType: serviceType ?? this.serviceType,
      serviceId: serviceId ?? this.serviceId,
      isAvailable: isAvailable ?? this.isAvailable,
      availableCount: availableCount ?? this.availableCount,
      nextAvailableDate: nextAvailableDate ?? this.nextAvailableDate,
      timestamp: timestamp ?? this.timestamp,
      isFromCache: isFromCache ?? this.isFromCache,
    );
  }

  /// Converts to JSON
  Map<String, dynamic> toJson() {
    return {
      'serviceType': serviceType,
      'serviceId': serviceId,
      'isAvailable': isAvailable,
      'availableCount': availableCount,
      'nextAvailableDate': nextAvailableDate?.toIso8601String(),
      'timestamp': timestamp.toIso8601String(),
      'isFromCache': isFromCache,
    };
  }

  /// Creates from JSON
  factory AvailabilityInfo.fromJson(Map<String, dynamic> json) {
    return AvailabilityInfo(
      serviceType: json['serviceType'] as String,
      serviceId: json['serviceId'] as String,
      isAvailable: json['isAvailable'] as bool,
      availableCount: json['availableCount'] as int,
      nextAvailableDate: json['nextAvailableDate'] != null
          ? DateTime.parse(json['nextAvailableDate'] as String)
          : null,
      timestamp: DateTime.parse(json['timestamp'] as String),
      isFromCache: json['isFromCache'] as bool? ?? false,
    );
  }
}

/// Service for checking real-time availability of travel services
class TravelAvailabilityService {
  final Box<String> _availabilityCache;
  final Connectivity _connectivity;
  StreamSubscription<ConnectivityResult>? _connectivitySubscription;
  bool _isOnline = true;

  // Cache expiration time (in minutes)
  static const int _cacheExpirationMinutes = 15;

  // Refresh interval for background updates (in minutes)
  static const int _backgroundRefreshMinutes = 30;

  // Maximum number of cached items
  static const int _maxCacheItems = 100;

  // Stream controller for availability updates
  final _availabilityUpdateController = StreamController<String>.broadcast();

  // Background refresh timer
  Timer? _backgroundRefreshTimer;

  /// Creates a new travel availability service
  TravelAvailabilityService()
      : _availabilityCache = Hive.box<String>('travel_availability_cache'),
        _connectivity = Connectivity() {
    _initConnectivityListener();
    _startBackgroundRefresh();
  }

  /// Initializes the connectivity listener
  void _initConnectivityListener() {
    _connectivitySubscription =
        _connectivity.onConnectivityChanged.listen((result) {
      final wasOnline = _isOnline;
      _isOnline = result != ConnectivityResult.none;

      // If we just came back online, refresh availability data
      if (!wasOnline && _isOnline) {
        _refreshExpiredCache();
      }
    });
  }

  /// Starts the background refresh timer
  void _startBackgroundRefresh() {
    _backgroundRefreshTimer?.cancel();
    _backgroundRefreshTimer = Timer.periodic(
      Duration(minutes: _backgroundRefreshMinutes),
      (_) => _refreshExpiredCache(),
    );
  }

  /// Refreshes expired cache items
  Future<void> _refreshExpiredCache() async {
    if (!_isOnline) return;

    final now = DateTime.now();
    final expiredKeys = <String>[];

    // Find expired cache items
    for (final key in _availabilityCache.keys) {
      final json = _availabilityCache.get(key);
      if (json == null) continue;

      try {
        final data = jsonDecode(json) as Map<String, dynamic>;
        final timestamp = DateTime.parse(data['timestamp'] as String);
        final expirationTime =
            timestamp.add(Duration(minutes: _cacheExpirationMinutes));

        if (now.isAfter(expirationTime)) {
          expiredKeys.add(key);
        }
      } catch (e) {
        debugPrint('Error parsing cache item: $e');
        expiredKeys.add(key);
      }
    }

    // Refresh expired items
    for (final key in expiredKeys) {
      final parts = key.split('_');
      if (parts.length != 2) continue;

      final serviceType = parts[0];
      final serviceId = parts[1];

      try {
        await checkAvailability(serviceType, serviceId);
      } catch (e) {
        debugPrint('Error refreshing availability: $e');
      }
    }
  }

  /// Cleans up old cache items if we exceed the maximum
  void _cleanupCache() {
    if (_availabilityCache.length <= _maxCacheItems) return;

    final entries = <MapEntry<String, DateTime>>[];

    // Get all cache items with their timestamps
    for (final key in _availabilityCache.keys) {
      final json = _availabilityCache.get(key);
      if (json == null) continue;

      try {
        final data = jsonDecode(json) as Map<String, dynamic>;
        final timestamp = DateTime.parse(data['timestamp'] as String);
        entries.add(MapEntry(key, timestamp));
      } catch (e) {
        // If we can't parse it, it's a candidate for removal
        entries.add(MapEntry(key, DateTime(1970)));
      }
    }

    // Sort by timestamp (oldest first)
    entries.sort((a, b) => a.value.compareTo(b.value));

    // Remove oldest items to get back to max size
    final itemsToRemove = entries.length - _maxCacheItems;
    if (itemsToRemove <= 0) return;

    for (var i = 0; i < itemsToRemove; i++) {
      _availabilityCache.delete(entries[i].key);
    }
  }

  /// Checks the availability of a travel service
  ///
  /// [serviceType] can be 'hotel', 'flight', 'car', 'restaurant', 'security', or 'cruise'
  /// [serviceId] is the unique identifier for the service
  Future<AvailabilityInfo> checkAvailability(
      String serviceType, String serviceId) async {
    final cacheKey = '${serviceType}_$serviceId';

    // Check cache first
    final cachedData = _getCachedAvailability(cacheKey);
    if (cachedData != null) {
      // If cache is still valid, return it
      final now = DateTime.now();
      final timestamp = cachedData.timestamp;
      final expirationTime =
          timestamp.add(Duration(minutes: _cacheExpirationMinutes));

      if (now.isBefore(expirationTime)) {
        return cachedData;
      }
    }

    // If we're offline, return cached data even if expired
    if (!_isOnline) {
      if (cachedData != null) {
        return cachedData.copyWith(isFromCache: true);
      }

      // If no cache, return unavailable
      return AvailabilityInfo(
        serviceType: serviceType,
        serviceId: serviceId,
        isAvailable: false,
        availableCount: 0,
        nextAvailableDate: null,
        timestamp: DateTime.now(),
        isFromCache: true,
      );
    }

    // Make API request for real-time availability
    try {
      // In a real app, this would be an actual API call
      // For demo purposes, we'll simulate a network request
      await Future.delayed(const Duration(milliseconds: 800));

      // Generate simulated availability data
      final availabilityInfo =
          _generateSimulatedAvailability(serviceType, serviceId);

      // Cache the result
      await _cacheAvailability(cacheKey, availabilityInfo);

      // Notify listeners
      _availabilityUpdateController.add(cacheKey);

      return availabilityInfo;
    } catch (e) {
      debugPrint('Error checking availability: $e');

      // If we have cached data, return it as fallback
      if (cachedData != null) {
        return cachedData.copyWith(isFromCache: true);
      }

      // Otherwise, throw the error
      rethrow;
    }
  }

  /// Gets cached availability data
  AvailabilityInfo? _getCachedAvailability(String cacheKey) {
    final json = _availabilityCache.get(cacheKey);
    if (json == null) return null;

    try {
      final data = jsonDecode(json) as Map<String, dynamic>;
      return AvailabilityInfo.fromJson(data);
    } catch (e) {
      debugPrint('Error parsing cached availability: $e');
      return null;
    }
  }

  /// Caches availability data
  Future<void> _cacheAvailability(
      String cacheKey, AvailabilityInfo info) async {
    await _availabilityCache.put(cacheKey, jsonEncode(info.toJson()));
    _cleanupCache();
  }

  /// Generates simulated availability data for demo purposes
  AvailabilityInfo _generateSimulatedAvailability(
      String serviceType, String serviceId) {
    final random = DateTime.now().millisecondsSinceEpoch % 100;
    final isAvailable = random > 20; // 80% chance of being available
    final availableCount = isAvailable ? (random % 10) + 1 : 0;

    DateTime? nextAvailableDate;
    if (!isAvailable) {
      // If not available now, set a future date
      nextAvailableDate = DateTime.now().add(Duration(days: (random % 7) + 1));
    }

    return AvailabilityInfo(
      serviceType: serviceType,
      serviceId: serviceId,
      isAvailable: isAvailable,
      availableCount: availableCount,
      nextAvailableDate: nextAvailableDate,
      timestamp: DateTime.now(),
      isFromCache: false,
    );
  }

  /// Checks availability for multiple services at once
  Future<List<AvailabilityInfo>> checkMultipleAvailability(
    List<MapEntry<String, String>> services,
  ) async {
    final results = <AvailabilityInfo>[];

    for (final service in services) {
      try {
        final availability =
            await checkAvailability(service.key, service.value);
        results.add(availability);
      } catch (e) {
        debugPrint(
            'Error checking availability for ${service.key}_${service.value}: $e');
        // Add a placeholder for failed checks
        results.add(AvailabilityInfo(
          serviceType: service.key,
          serviceId: service.value,
          isAvailable: false,
          availableCount: 0,
          timestamp: DateTime.now(),
          isFromCache: true,
        ));
      }
    }

    return results;
  }

  /// Stream of availability updates
  Stream<String> get availabilityUpdates =>
      _availabilityUpdateController.stream;

  /// Checks if the device is online
  bool get isOnline => _isOnline;

  /// Manually refreshes availability for a service
  Future<AvailabilityInfo> refreshAvailability(
      String serviceType, String serviceId) async {
    final cacheKey = '${serviceType}_$serviceId';

    // Remove from cache to force a refresh
    await _availabilityCache.delete(cacheKey);

    // Check availability again
    return checkAvailability(serviceType, serviceId);
  }

  /// Disposes of resources
  void dispose() {
    _connectivitySubscription?.cancel();
    _backgroundRefreshTimer?.cancel();
    _availabilityUpdateController.close();
  }
}

/// Provider for the travel availability service
final travelAvailabilityServiceProvider =
    Provider<TravelAvailabilityService>((ref) {
  final service = TravelAvailabilityService();

  ref.onDispose(() {
    service.dispose();
  });

  return service;
});

/// Provider for checking availability of a specific service
final serviceAvailabilityProvider =
    FutureProvider.family<AvailabilityInfo, MapEntry<String, String>>(
        (ref, service) {
  return ref.watch(travelAvailabilityServiceProvider).checkAvailability(
        service.key, // serviceType
        service.value, // serviceId
      );
});

/// Provider for checking availability of multiple services
final multipleServiceAvailabilityProvider = FutureProvider.family<
    List<AvailabilityInfo>, List<MapEntry<String, String>>>((ref, services) {
  return ref
      .watch(travelAvailabilityServiceProvider)
      .checkMultipleAvailability(services);
});

/// Provider for streaming availability updates
final availabilityUpdatesProvider = StreamProvider<String>((ref) {
  return ref.watch(travelAvailabilityServiceProvider).availabilityUpdates;
});
