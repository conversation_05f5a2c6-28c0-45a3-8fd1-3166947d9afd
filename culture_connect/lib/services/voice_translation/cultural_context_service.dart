import 'dart:async';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import 'package:uuid/uuid.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:culture_connect/models/translation/cultural_context_model.dart';
import 'package:culture_connect/models/translation/translation_cultural_context.dart';
import 'package:culture_connect/providers/shared_preferences_provider.dart';

/// A service for handling cultural context information
class CulturalContextService {
  /// The HTTP client
  final http.Client _client;

  /// The shared preferences instance
  final SharedPreferences _prefs;

  /// The connectivity service
  final Connectivity _connectivity;

  /// Cache for cultural context information
  final Map<String, TranslationCulturalContext> _contextCache = {};

  /// Offline cache for cultural context information
  final Map<String, TranslationCulturalContext> _offlineCache = {};

  /// Stream controller for cultural context events
  final StreamController<String> _contextEventsController =
      StreamController<String>.broadcast();

  /// Stream controller for error events
  final StreamController<String> _errorEventsController =
      StreamController<String>.broadcast();

  /// Whether to use cultural context awareness
  bool _useCulturalContext = true;

  /// Whether to show sensitive content
  bool _showSensitiveContent = false;

  /// Whether to use offline mode
  bool _useOfflineMode = false;

  /// Whether the service is currently online
  bool _isOnline = true;

  /// Subscription to connectivity changes
  StreamSubscription<ConnectivityResult>? _connectivitySubscription;

  /// Creates a new cultural context service
  CulturalContextService(this._client, this._prefs, this._connectivity) {
    _loadPreferences();
    _initConnectivity();
    _loadOfflineCache();
  }

  /// Stream of cultural context events
  Stream<String> get contextEvents => _contextEventsController.stream;

  /// Stream of error events
  Stream<String> get errorEvents => _errorEventsController.stream;

  /// Whether to use cultural context awareness
  bool get useCulturalContext => _useCulturalContext;

  /// Whether to show sensitive content
  bool get showSensitiveContent => _showSensitiveContent;

  /// Whether to use offline mode
  bool get useOfflineMode => _useOfflineMode;

  /// Whether the service is currently online
  bool get isOnline => _isOnline;

  /// Initialize connectivity monitoring
  void _initConnectivity() {
    _connectivitySubscription =
        _connectivity.onConnectivityChanged.listen(_updateConnectionStatus);
    // Check initial connection state
    _checkConnectivity();
  }

  /// Check current connectivity
  Future<void> _checkConnectivity() async {
    try {
      final result = await _connectivity.checkConnectivity();
      _updateConnectionStatus(result);
    } on Exception catch (e) {
      debugPrint('Error checking connectivity: $e');
      _isOnline = false;
    }
  }

  /// Update connection status based on connectivity result
  void _updateConnectionStatus(ConnectivityResult result) {
    _isOnline = result != ConnectivityResult.none;
    debugPrint('Connectivity changed: ${_isOnline ? 'Online' : 'Offline'}');
  }

  /// Load preferences
  void _loadPreferences() {
    _useCulturalContext = _prefs.getBool('use_cultural_context') ?? true;
    _showSensitiveContent = _prefs.getBool('show_sensitive_content') ?? false;
    _useOfflineMode = _prefs.getBool('use_offline_mode') ?? false;
  }

  /// Load offline cache from shared preferences
  void _loadOfflineCache() {
    try {
      final offlineCacheJson =
          _prefs.getString('cultural_context_offline_cache');
      if (offlineCacheJson != null) {
        final Map<String, dynamic> cacheData = jsonDecode(offlineCacheJson);
        cacheData.forEach((key, value) {
          _offlineCache[key] = TranslationCulturalContext.fromJson(value);
        });
        debugPrint(
            'Loaded ${_offlineCache.length} cultural context entries from offline cache');
      }
    } catch (e) {
      debugPrint('Error loading offline cache: $e');
      // Clear corrupted cache
      _prefs.remove('cultural_context_offline_cache');
    }
  }

  /// Save offline cache to shared preferences
  Future<void> _saveOfflineCache() async {
    try {
      final Map<String, dynamic> cacheData = {};
      _offlineCache.forEach((key, value) {
        cacheData[key] = value.toJson();
      });
      await _prefs.setString(
          'cultural_context_offline_cache', jsonEncode(cacheData));
      debugPrint(
          'Saved ${_offlineCache.length} cultural context entries to offline cache');
    } catch (e) {
      debugPrint('Error saving offline cache: $e');
      _errorEventsController.add('Failed to save offline cache: $e');
    }
  }

  /// Set whether to use cultural context awareness
  Future<void> setUseCulturalContext(bool value) async {
    _useCulturalContext = value;
    await _prefs.setBool('use_cultural_context', value);
  }

  /// Set whether to show sensitive content
  Future<void> setShowSensitiveContent(bool value) async {
    _showSensitiveContent = value;
    await _prefs.setBool('show_sensitive_content', value);
  }

  /// Set whether to use offline mode
  Future<void> setUseOfflineMode(bool value) async {
    _useOfflineMode = value;
    await _prefs.setBool('use_offline_mode', value);

    // If enabling offline mode, save current cache to offline cache
    if (value) {
      _offlineCache.addAll(_contextCache);
      await _saveOfflineCache();
    }
  }

  /// Get cultural context information for a translation
  Future<TranslationCulturalContext> getCulturalContext({
    required String text,
    required String sourceLanguage,
    required String targetLanguage,
    String? sourceRegion,
    String? targetRegion,
    bool forceRefresh = false,
  }) async {
    if (!_useCulturalContext) {
      return TranslationCulturalContext.empty(
        sourceLanguage: sourceLanguage,
        targetLanguage: targetLanguage,
      );
    }

    // Create a cache key
    final cacheKey = '${sourceLanguage}_${targetLanguage}_${text.hashCode}';

    // Check the memory cache first (unless force refresh is requested)
    if (!forceRefresh && _contextCache.containsKey(cacheKey)) {
      return _contextCache[cacheKey]!;
    }

    // If offline mode is enabled or we're not online, check the offline cache
    if ((_useOfflineMode || !_isOnline) &&
        _offlineCache.containsKey(cacheKey)) {
      final offlineContext = _offlineCache[cacheKey]!;
      // Update memory cache
      _contextCache[cacheKey] = offlineContext;
      return offlineContext;
    }

    // If we're offline and the data isn't in the offline cache, return empty context
    if (!_isOnline) {
      final emptyContext = TranslationCulturalContext.empty(
        sourceLanguage: sourceLanguage,
        targetLanguage: targetLanguage,
      );
      _errorEventsController.add(
          'No internet connection. Unable to fetch cultural context information.');
      return emptyContext;
    }

    try {
      // In a real app, we would call an API to get cultural context information
      // For demo purposes, we'll generate mock data
      final context = await _fetchCulturalContext(
        text,
        sourceLanguage,
        targetLanguage,
        sourceRegion,
        targetRegion,
      );

      // Cache the result in memory
      _contextCache[cacheKey] = context;

      // If offline mode is enabled, also cache for offline use
      if (_useOfflineMode) {
        _offlineCache[cacheKey] = context;
        // Save to persistent storage (don't await to avoid blocking)
        _saveOfflineCache();
      }

      // Notify listeners
      _contextEventsController.add(cacheKey);

      return context;
    } catch (e) {
      debugPrint('Error getting cultural context: $e');
      _errorEventsController.add('Failed to get cultural context: $e');

      // If we have an offline version, use that as fallback
      if (_offlineCache.containsKey(cacheKey)) {
        debugPrint('Using offline cache as fallback');
        return _offlineCache[cacheKey]!;
      }

      return TranslationCulturalContext.empty(
        sourceLanguage: sourceLanguage,
        targetLanguage: targetLanguage,
      );
    }
  }

  /// Fetch cultural context from API (simulated with mock data for now)
  Future<TranslationCulturalContext> _fetchCulturalContext(
    String text,
    String sourceLanguage,
    String targetLanguage,
    String? sourceRegion,
    String? targetRegion,
  ) async {
    // Simulate network delay
    await Future.delayed(const Duration(milliseconds: 300));

    // Simulate network error occasionally (10% chance)
    if (DateTime.now().millisecondsSinceEpoch % 10 == 0) {
      throw Exception('Simulated network error');
    }

    // Generate mock data
    return _generateMockCulturalContext(
      text,
      sourceLanguage,
      targetLanguage,
      sourceRegion,
      targetRegion,
    );
  }

  /// Generate mock cultural context information
  TranslationCulturalContext _generateMockCulturalContext(
    String text,
    String sourceLanguage,
    String targetLanguage,
    String? sourceRegion,
    String? targetRegion,
  ) {
    // For demo purposes, we'll generate mock data based on the language pair
    // In a real app, this would come from an API or a database

    final notes = <CulturalContextNote>[];
    String? generalNote;

    // Generate language pair specific notes
    if (sourceLanguage == 'en' && targetLanguage == 'fr') {
      generalNote =
          'French culture places high value on formality and politeness. '
          'The French language has formal and informal forms of address.';

      // Add some mock notes for common phrases
      if (text.toLowerCase().contains('how are you')) {
        notes.add(CulturalContextNote(
          id: const Uuid().v4(),
          textSegment: 'Comment allez-vous',
          startIndex: text.toLowerCase().indexOf('how are you'),
          endIndex:
              text.toLowerCase().indexOf('how are you') + 'how are you'.length,
          type: CulturalContextType.formality,
          explanation:
              '"Comment allez-vous" is the formal way to ask "How are you?" in French. '
              'Use this form when speaking to someone you don\'t know well, someone older, '
              'or in professional settings.',
          alternatives: ['Comment vas-tu', 'Ça va'],
          languageCode: 'fr',
          formalityLevel: 'Formal',
        ));
      }

      if (text.toLowerCase().contains('thank you')) {
        notes.add(CulturalContextNote(
          id: const Uuid().v4(),
          textSegment: 'Merci',
          startIndex: text.toLowerCase().indexOf('thank you'),
          endIndex:
              text.toLowerCase().indexOf('thank you') + 'thank you'.length,
          type: CulturalContextType.general,
          explanation: 'In French culture, expressing gratitude is important. '
              '"Merci" can be enhanced with "beaucoup" (thank you very much) or "mille fois" '
              '(a thousand times) for stronger emphasis.',
          alternatives: [
            'Merci beaucoup',
            'Merci mille fois',
            'Je vous remercie'
          ],
          languageCode: 'fr',
        ));
      }
    } else if (sourceLanguage == 'en' && targetLanguage == 'yo') {
      generalNote =
          'Yoruba is a tonal language spoken primarily in Nigeria and Benin. '
          'Respect for elders is a fundamental aspect of Yoruba culture, reflected in greetings and forms of address.';

      // Add some mock notes for common phrases
      if (text.toLowerCase().contains('good morning')) {
        notes.add(CulturalContextNote(
          id: const Uuid().v4(),
          textSegment: 'Ẹ kàárọ̀',
          startIndex: text.toLowerCase().indexOf('good morning'),
          endIndex: text.toLowerCase().indexOf('good morning') +
              'good morning'.length,
          type: CulturalContextType.formality,
          explanation:
              'Yoruba greetings vary by time of day and social context. "Ẹ kàárọ̀" is formal '
              'and used for elders or respected individuals. "O kàárọ̀" is less formal and used among peers.',
          alternatives: ['O kàárọ̀'],
          languageCode: 'yo',
          formalityLevel: 'Formal',
        ));
      }

      if (text.toLowerCase().contains('thank you')) {
        notes.add(CulturalContextNote(
          id: const Uuid().v4(),
          textSegment: 'Ẹ ṣeun',
          startIndex: text.toLowerCase().indexOf('thank you'),
          endIndex:
              text.toLowerCase().indexOf('thank you') + 'thank you'.length,
          type: CulturalContextType.formality,
          explanation:
              'In Yoruba culture, expressing gratitude often includes bowing slightly or nodding '
              'the head as a sign of respect. "Ẹ ṣeun" is formal, while "O ṣeun" is less formal.',
          alternatives: ['O ṣeun', 'Ẹ dúpẹ́'],
          languageCode: 'yo',
        ));
      }
    }

    // Add some idioms and slang based on the text content
    if (text.toLowerCase().contains('raining cats and dogs')) {
      notes.add(CulturalContextNote(
        id: const Uuid().v4(),
        textSegment: 'raining cats and dogs',
        startIndex: text.toLowerCase().indexOf('raining cats and dogs'),
        endIndex: text.toLowerCase().indexOf('raining cats and dogs') +
            'raining cats and dogs'.length,
        type: CulturalContextType.idiom,
        explanation: 'This is an English idiom meaning "raining very heavily". '
            'Different languages have their own expressions for heavy rain.',
        alternatives: [],
        languageCode: 'en',
      ));
    }

    if (text.toLowerCase().contains('break a leg')) {
      notes.add(CulturalContextNote(
        id: const Uuid().v4(),
        textSegment: 'break a leg',
        startIndex: text.toLowerCase().indexOf('break a leg'),
        endIndex:
            text.toLowerCase().indexOf('break a leg') + 'break a leg'.length,
        type: CulturalContextType.idiom,
        explanation: 'This is an English idiom used to wish someone good luck, '
            'especially before a performance or presentation. It originated in theater '
            'where wishing someone good luck was considered bad luck.',
        alternatives: [],
        languageCode: 'en',
      ));
    }

    // Add some cultural references
    if (text.toLowerCase().contains('thanksgiving')) {
      notes.add(CulturalContextNote(
        id: const Uuid().v4(),
        textSegment: 'Thanksgiving',
        startIndex: text.toLowerCase().indexOf('thanksgiving'),
        endIndex:
            text.toLowerCase().indexOf('thanksgiving') + 'thanksgiving'.length,
        type: CulturalContextType.reference,
        explanation:
            'Thanksgiving is an important holiday in the United States and Canada, '
            'though celebrated on different dates. It involves family gatherings and traditional '
            'foods like turkey, cranberry sauce, and pumpkin pie.',
        alternatives: [],
        languageCode: 'en',
        region: 'North America',
      ));
    }

    // Add some sensitive content
    if (text.toLowerCase().contains('damn') ||
        text.toLowerCase().contains('hell')) {
      notes.add(CulturalContextNote(
        id: const Uuid().v4(),
        textSegment: text.toLowerCase().contains('damn') ? 'damn' : 'hell',
        startIndex: text.toLowerCase().contains('damn')
            ? text.toLowerCase().indexOf('damn')
            : text.toLowerCase().indexOf('hell'),
        endIndex: text.toLowerCase().contains('damn')
            ? text.toLowerCase().indexOf('damn') + 'damn'.length
            : text.toLowerCase().indexOf('hell') + 'hell'.length,
        type: CulturalContextType.taboo,
        explanation:
            'This word can be considered mild profanity in some contexts. '
            'The level of offense varies by region and social setting.',
        alternatives: ['darn', 'heck'],
        languageCode: 'en',
        isSensitive: true,
      ));
    }

    return TranslationCulturalContext(
      notes: notes,
      hasContextInformation: notes.isNotEmpty,
      sourceLanguage: sourceLanguage,
      targetLanguage: targetLanguage,
      sourceRegion: sourceRegion,
      targetRegion: targetRegion,
      generalCulturalNote: generalNote,
    );
  }

  /// Clear the in-memory cache
  void clearCache() {
    _contextCache.clear();
    _contextEventsController.add('cache_cleared');
  }

  /// Clear both in-memory and offline caches
  Future<void> clearAllCaches() async {
    _contextCache.clear();
    _offlineCache.clear();
    await _prefs.remove('cultural_context_offline_cache');
    _contextEventsController.add('all_caches_cleared');
  }

  /// Get the size of the in-memory cache
  int getMemoryCacheSize() {
    return _contextCache.length;
  }

  /// Get the size of the offline cache
  int getOfflineCacheSize() {
    return _offlineCache.length;
  }

  /// Dispose resources
  void dispose() {
    _connectivitySubscription?.cancel();
    _contextEventsController.close();
    _errorEventsController.close();
    _client.close();
  }
}

/// Provider for the cultural context service
final culturalContextServiceProvider = Provider<CulturalContextService>((ref) {
  final prefs = ref.watch(sharedPreferencesProvider);
  final connectivity = Connectivity();
  final client = http.Client();

  final service = CulturalContextService(client, prefs, connectivity);

  ref.onDispose(() {
    service.dispose();
  });

  return service;
});
