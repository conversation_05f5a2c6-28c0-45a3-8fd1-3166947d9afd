import 'dart:async';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:uuid/uuid.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:culture_connect/models/translation/custom_vocabulary_model.dart';
import 'package:culture_connect/providers/shared_preferences_provider.dart';

/// A service for managing custom vocabulary
class CustomVocabularyService {
  /// The shared preferences instance
  final SharedPreferences _prefs;

  /// The connectivity service
  final Connectivity _connectivity;

  /// The custom vocabulary terms
  List<CustomVocabularyModel> _vocabularyTerms = [];

  /// The vocabulary terms stream controller
  final StreamController<List<CustomVocabularyModel>>
      _vocabularyTermsController =
      StreamController<List<CustomVocabularyModel>>.broadcast();

  /// The error stream controller
  final StreamController<String> _errorController =
      StreamController<String>.broadcast();

  /// Whether the service is currently online
  bool _isOnline = true;

  /// Subscription to connectivity changes
  StreamSubscription<ConnectivityResult>? _connectivitySubscription;

  /// Creates a new custom vocabulary service
  CustomVocabularyService(this._prefs, this._connectivity) {
    _loadVocabularyTerms();
    _initConnectivity();
  }

  /// The stream of error events
  Stream<String> get errorStream => _errorController.stream;

  /// Whether the service is currently online
  bool get isOnline => _isOnline;

  /// Initialize connectivity monitoring
  void _initConnectivity() {
    _connectivitySubscription =
        _connectivity.onConnectivityChanged.listen(_updateConnectionStatus);
    // Check initial connection state
    _checkConnectivity();
  }

  /// Check current connectivity
  Future<void> _checkConnectivity() async {
    try {
      final result = await _connectivity.checkConnectivity();
      _updateConnectionStatus(result);
    } on Exception catch (e) {
      debugPrint('Error checking connectivity: $e');
      _isOnline = false;
    }
  }

  /// Update connection status based on connectivity result
  void _updateConnectionStatus(ConnectivityResult result) {
    _isOnline = result != ConnectivityResult.none;
    debugPrint('Connectivity changed: ${_isOnline ? 'Online' : 'Offline'}');
  }

  /// Load vocabulary terms from shared preferences
  Future<void> _loadVocabularyTerms() async {
    try {
      final termsJson = _prefs.getString('custom_vocabulary');
      if (termsJson != null) {
        final List<dynamic> decoded = jsonDecode(termsJson);
        _vocabularyTerms = decoded
            .map((item) => CustomVocabularyModel.fromJson(item))
            .toList();

        // Notify listeners
        _vocabularyTermsController.add(_vocabularyTerms);
        debugPrint('Loaded ${_vocabularyTerms.length} vocabulary terms');
      }
    } catch (e) {
      debugPrint('Error loading vocabulary terms: $e');
      _errorController.add('Error loading vocabulary terms: $e');
    }
  }

  /// Save vocabulary terms to shared preferences
  Future<bool> _saveVocabularyTerms() async {
    try {
      final termsJson =
          jsonEncode(_vocabularyTerms.map((item) => item.toJson()).toList());
      final result = await _prefs.setString('custom_vocabulary', termsJson);

      if (result) {
        debugPrint('Saved ${_vocabularyTerms.length} vocabulary terms');
      } else {
        debugPrint('Failed to save vocabulary terms');
        _errorController.add('Failed to save vocabulary terms');
      }

      return result;
    } catch (e) {
      debugPrint('Error saving vocabulary terms: $e');
      _errorController.add('Error saving vocabulary terms: $e');
      return false;
    }
  }

  /// Add a new vocabulary term
  Future<CustomVocabularyModel> addVocabularyTerm({
    required String originalTerm,
    required String originalLanguageCode,
    required Map<String, String> translations,
    required VocabularyCategory category,
    String? customCategory,
    String? description,
  }) async {
    try {
      final now = DateTime.now();
      final id = const Uuid().v4();

      final term = CustomVocabularyModel(
        id: id,
        originalTerm: originalTerm,
        originalLanguageCode: originalLanguageCode,
        translations: translations,
        category: category,
        customCategory: customCategory,
        description: description,
        createdAt: now,
        updatedAt: now,
      );

      _vocabularyTerms.add(term);
      _vocabularyTermsController.add(_vocabularyTerms);
      await _saveVocabularyTerms();

      return term;
    } catch (e) {
      debugPrint('Error adding vocabulary term: $e');
      rethrow;
    }
  }

  /// Update a vocabulary term
  Future<CustomVocabularyModel?> updateVocabularyTerm({
    required String id,
    String? originalTerm,
    String? originalLanguageCode,
    Map<String, String>? translations,
    VocabularyCategory? category,
    String? customCategory,
    String? description,
  }) async {
    try {
      final index = _vocabularyTerms.indexWhere((term) => term.id == id);
      if (index < 0) {
        return null;
      }

      final term = _vocabularyTerms[index];
      final updatedTerm = term.copyWith(
        originalTerm: originalTerm,
        originalLanguageCode: originalLanguageCode,
        translations: translations,
        category: category,
        customCategory: customCategory,
        description: description,
        updatedAt: DateTime.now(),
      );

      _vocabularyTerms[index] = updatedTerm;
      _vocabularyTermsController.add(_vocabularyTerms);
      await _saveVocabularyTerms();

      return updatedTerm;
    } catch (e) {
      debugPrint('Error updating vocabulary term: $e');
      return null;
    }
  }

  /// Delete a vocabulary term
  Future<bool> deleteVocabularyTerm(String id) async {
    try {
      final index = _vocabularyTerms.indexWhere((term) => term.id == id);
      if (index < 0) {
        return false;
      }

      _vocabularyTerms.removeAt(index);
      _vocabularyTermsController.add(_vocabularyTerms);
      await _saveVocabularyTerms();

      return true;
    } catch (e) {
      debugPrint('Error deleting vocabulary term: $e');
      return false;
    }
  }

  /// Toggle favorite status of a vocabulary term
  Future<CustomVocabularyModel?> toggleFavorite(String id) async {
    try {
      final index = _vocabularyTerms.indexWhere((term) => term.id == id);
      if (index < 0) {
        return null;
      }

      final term = _vocabularyTerms[index];
      final updatedTerm = term.copyWith(
        isFavorite: !term.isFavorite,
        updatedAt: DateTime.now(),
      );

      _vocabularyTerms[index] = updatedTerm;
      _vocabularyTermsController.add(_vocabularyTerms);
      await _saveVocabularyTerms();

      return updatedTerm;
    } catch (e) {
      debugPrint('Error toggling favorite: $e');
      return null;
    }
  }

  /// Increment usage count of a vocabulary term
  Future<CustomVocabularyModel?> incrementUsageCount(String id) async {
    try {
      final index = _vocabularyTerms.indexWhere((term) => term.id == id);
      if (index < 0) {
        return null;
      }

      final term = _vocabularyTerms[index];
      final updatedTerm = term.copyWith(
        usageCount: term.usageCount + 1,
        updatedAt: DateTime.now(),
      );

      _vocabularyTerms[index] = updatedTerm;
      _vocabularyTermsController.add(_vocabularyTerms);
      await _saveVocabularyTerms();

      return updatedTerm;
    } catch (e) {
      debugPrint('Error incrementing usage count: $e');
      return null;
    }
  }

  /// Get a vocabulary term by ID
  CustomVocabularyModel? getVocabularyTerm(String id) {
    try {
      return _vocabularyTerms.firstWhere((term) => term.id == id);
    } catch (e) {
      return null;
    }
  }

  /// Search vocabulary terms
  List<CustomVocabularyModel> searchVocabularyTerms(String query) {
    if (query.isEmpty) {
      return _vocabularyTerms;
    }

    final lowerQuery = query.toLowerCase();
    return _vocabularyTerms.where((term) {
      // Search in original term
      if (term.originalTerm.toLowerCase().contains(lowerQuery)) {
        return true;
      }

      // Search in translations
      for (final translation in term.translations.values) {
        if (translation.toLowerCase().contains(lowerQuery)) {
          return true;
        }
      }

      // Search in description
      if (term.description != null &&
          term.description!.toLowerCase().contains(lowerQuery)) {
        return true;
      }

      return false;
    }).toList();
  }

  /// Get vocabulary terms by category
  List<CustomVocabularyModel> getVocabularyTermsByCategory(
      VocabularyCategory category) {
    return _vocabularyTerms.where((term) => term.category == category).toList();
  }

  /// Get vocabulary terms by language
  List<CustomVocabularyModel> getVocabularyTermsByLanguage(
      String languageCode) {
    return _vocabularyTerms
        .where((term) => term.originalLanguageCode == languageCode)
        .toList();
  }

  /// Get favorite vocabulary terms
  List<CustomVocabularyModel> getFavoriteVocabularyTerms() {
    return _vocabularyTerms.where((term) => term.isFavorite).toList();
  }

  /// Get most used vocabulary terms
  List<CustomVocabularyModel> getMostUsedVocabularyTerms({int limit = 10}) {
    final sortedTerms = List<CustomVocabularyModel>.from(_vocabularyTerms)
      ..sort((a, b) => b.usageCount.compareTo(a.usageCount));

    return sortedTerms.take(limit).toList();
  }

  /// Get recently added vocabulary terms
  List<CustomVocabularyModel> getRecentlyAddedVocabularyTerms(
      {int limit = 10}) {
    final sortedTerms = List<CustomVocabularyModel>.from(_vocabularyTerms)
      ..sort((a, b) => b.createdAt.compareTo(a.createdAt));

    return sortedTerms.take(limit).toList();
  }

  /// Get the vocabulary terms stream
  Stream<List<CustomVocabularyModel>> get vocabularyTermsStream =>
      _vocabularyTermsController.stream;

  /// Get all vocabulary terms
  List<CustomVocabularyModel> get vocabularyTerms => _vocabularyTerms;

  /// Dispose the service
  Future<void> dispose() async {
    _connectivitySubscription?.cancel();
    await _vocabularyTermsController.close();
    await _errorController.close();
  }
}

/// Provider for the custom vocabulary service
final customVocabularyServiceProvider =
    Provider<CustomVocabularyService>((ref) {
  final prefs = ref.watch(sharedPreferencesProvider);
  final connectivity = Connectivity();

  final service = CustomVocabularyService(prefs, connectivity);

  ref.onDispose(() {
    service.dispose();
  });

  return service;
});
