import 'dart:async';
import 'dart:io';
import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:http/http.dart' as http;
import 'package:path_provider/path_provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:uuid/uuid.dart';
import 'package:culture_connect/models/message_model.dart';
import 'package:culture_connect/models/translation/custom_vocabulary_model.dart';
import 'package:culture_connect/models/translation/message_translation_metadata.dart';
import 'package:culture_connect/models/translation/translation_confidence_model.dart';
import 'package:culture_connect/models/translation/translation_cultural_context.dart';
import 'package:culture_connect/models/translation/translation_pronunciation.dart';
import 'package:culture_connect/models/translation/translation_slang_idiom.dart';
import 'package:culture_connect/services/voice_translation/audio_playback_service.dart';
import 'package:culture_connect/services/voice_translation/cultural_context_service.dart';
import 'package:culture_connect/services/voice_translation/custom_vocabulary_service.dart';
import 'package:culture_connect/services/voice_translation/language_pack_service.dart';
import 'package:culture_connect/services/voice_translation/pronunciation_service.dart';
import 'package:culture_connect/services/voice_translation/slang_idiom_service.dart';
import 'package:culture_connect/services/voice_translation/voice_translation_service.dart';

/// A service for translating messages
class MessageTranslationService {
  /// The voice translation service
  final VoiceTranslationService _voiceTranslationService;

  /// The audio playback service
  final AudioPlaybackService _playbackService;

  /// The language pack service
  final LanguagePackService _languagePackService;

  /// The custom vocabulary service
  final CustomVocabularyService _customVocabularyService;

  /// The cultural context service
  final CulturalContextService _culturalContextService;

  /// The slang and idiom service
  final SlangIdiomService _slangIdiomService;

  /// The pronunciation service
  final PronunciationService _pronunciationService;

  /// The shared preferences instance
  final SharedPreferences _prefs;

  /// The HTTP client
  final http.Client _client;

  /// Whether to use offline mode
  bool _useOfflineMode = false;

  /// Whether to use dialect recognition
  bool _useDialectRecognition = false;

  /// Whether to use custom vocabulary
  bool _useCustomVocabulary = false;

  /// Cache for translated messages
  final Map<String, MessageTranslationMetadata> _translationCache = {};

  /// Stream controller for translation events
  final StreamController<String> _translationEventsController =
      StreamController<String>.broadcast();

  /// Creates a new message translation service
  MessageTranslationService(
    this._voiceTranslationService,
    this._playbackService,
    this._languagePackService,
    this._customVocabularyService,
    this._culturalContextService,
    this._slangIdiomService,
    this._pronunciationService,
    this._prefs,
    this._client,
  ) {
    _loadPreferences();
  }

  /// Stream of translation events
  Stream<String> get translationEvents => _translationEventsController.stream;

  /// Load preferences
  void _loadPreferences() {
    _useOfflineMode = _prefs.getBool('use_offline_mode') ?? false;
    _useDialectRecognition = _prefs.getBool('use_dialect_recognition') ?? false;
    _useCustomVocabulary = _prefs.getBool('use_custom_vocabulary') ?? false;
  }

  /// Set whether to use offline mode
  void setUseOfflineMode(bool value) {
    _useOfflineMode = value;
    _prefs.setBool('use_offline_mode', value);
  }

  /// Set whether to use dialect recognition
  void setUseDialectRecognition(bool value) {
    _useDialectRecognition = value;
    _prefs.setBool('use_dialect_recognition', value);
  }

  /// Set whether to use custom vocabulary
  void setUseCustomVocabulary(bool value) {
    _useCustomVocabulary = value;
    _prefs.setBool('use_custom_vocabulary', value);
  }

  /// Translate a message
  Future<MessageTranslationMetadata> translateMessage(
    MessageModel message,
    String targetLanguage,
  ) async {
    // Check if we already have a translation for this message and target language
    final cacheKey = '${message.id}_$targetLanguage';
    if (_translationCache.containsKey(cacheKey)) {
      return _translationCache[cacheKey]!;
    }

    try {
      // Get custom vocabulary if enabled
      List<CustomVocabularyModel>? customVocabulary;
      if (_useCustomVocabulary) {
        // In a real implementation, we would get custom vocabulary
        // For now, we'll just use an empty list
        customVocabulary = [];
      }

      // Get dialect information if enabled
      String? sourceLangDialect;
      String? targetLangDialect;
      if (_useDialectRecognition) {
        sourceLangDialect =
            _prefs.getString('dialect_${message.originalLanguage}');
        targetLangDialect = _prefs.getString('dialect_$targetLanguage');
      }

      // Translate the text
      final translatedText = await _translateText(
        message.text,
        message.originalLanguage,
        targetLanguage,
        useOffline: _useOfflineMode,
        sourceDialect: sourceLangDialect,
        targetDialect: targetLangDialect,
        customVocabulary: customVocabulary,
      );

      // Generate audio for the translated text
      final translatedAudioPath = await _textToSpeech(
        translatedText,
        targetLanguage,
        useOffline: _useOfflineMode,
        dialect: targetLangDialect,
      );

      // Generate confidence information
      final confidenceScore = _generateConfidenceScore(
        message.text,
        translatedText,
        message.originalLanguage,
        targetLanguage,
      );

      final confidence = TranslationConfidenceModel.fromScore(confidenceScore);

      // Get cultural context information
      final culturalContext = await _getCulturalContext(
        translatedText,
        message.originalLanguage,
        targetLanguage,
        sourceLangDialect,
        targetLangDialect,
      );

      // Get slang and idiom information
      final slangIdiom = await _getSlangIdiom(
        translatedText,
        message.originalLanguage,
        targetLanguage,
        sourceLangDialect,
        targetLangDialect,
      );

      // Get pronunciation information
      final pronunciation = await _getPronunciation(
        translatedText,
        message.originalLanguage,
        targetLanguage,
        sourceLangDialect,
        targetLangDialect,
      );

      // Create translation metadata
      final metadata = MessageTranslationMetadata(
        originalText: message.text,
        translatedText: translatedText,
        sourceLanguage: message.originalLanguage,
        targetLanguage: targetLanguage,
        translatedAt: DateTime.now(),
        isAudioAvailable: translatedAudioPath != null,
        translatedAudioPath: translatedAudioPath,
        confidence: confidence,
        culturalContext: culturalContext,
        slangIdiom: slangIdiom,
        pronunciation: pronunciation,
      );

      // Cache the translation
      _translationCache[cacheKey] = metadata;

      // Notify listeners
      _translationEventsController.add(message.id);

      return metadata;
    } catch (e) {
      debugPrint('Error translating message: $e');
      rethrow;
    }
  }

  /// Translate text
  Future<String> _translateText(
    String text,
    String sourceLanguageCode,
    String targetLanguageCode, {
    bool useOffline = false,
    String? sourceDialect,
    String? targetDialect,
    List<CustomVocabularyModel>? customVocabulary,
  }) async {
    // Delegate to the voice translation service
    return _voiceTranslationService.translateText(
      text,
      sourceLanguageCode,
      targetLanguageCode,
      useOffline: useOffline,
      sourceDialect: sourceDialect,
      targetDialect: targetDialect,
      customVocabulary: customVocabulary,
    );
  }

  /// Convert text to speech
  Future<String?> _textToSpeech(
    String text,
    String languageCode, {
    bool useOffline = false,
    String? dialect,
  }) async {
    // Delegate to the voice translation service
    return _voiceTranslationService.textToSpeech(
      text,
      languageCode,
      useOffline: useOffline,
      dialect: dialect,
    );
  }

  /// Play the translated audio
  Future<void> playTranslatedAudio(String audioPath) async {
    try {
      // Load and play the audio
      await _playbackService.loadAudio(audioPath);
      await _playbackService.play();
    } catch (e) {
      debugPrint('Error playing translated audio: $e');
    }
  }

  /// Stop audio playback
  Future<void> stopAudio() async {
    await _playbackService.stop();
  }

  /// Rate translation quality
  Future<MessageTranslationMetadata> rateTranslation(
    String messageId,
    String targetLanguage,
    TranslationQuality quality,
  ) async {
    final cacheKey = '${messageId}_$targetLanguage';
    if (!_translationCache.containsKey(cacheKey)) {
      throw Exception('Translation not found');
    }

    final metadata = _translationCache[cacheKey]!;
    final updatedMetadata = metadata.copyWith(quality: quality);

    // Update cache
    _translationCache[cacheKey] = updatedMetadata;

    // Notify listeners
    _translationEventsController.add(messageId);

    // In a real app, we would send this feedback to the server
    // to improve future translations

    return updatedMetadata;
  }

  /// Generate a confidence score for a translation
  double _generateConfidenceScore(
    String originalText,
    String translatedText,
    String sourceLanguage,
    String targetLanguage,
  ) {
    // In a real implementation, this would be based on the translation model's confidence
    // For demo purposes, we'll generate a random score with some logic

    // Base score between 0.6 and 0.95
    final random = math.Random();
    double score = 0.6 + (random.nextDouble() * 0.35);

    // Adjust based on language pair
    // Some language pairs are harder to translate than others
    if (sourceLanguage == 'en' && targetLanguage == 'fr') {
      // English to French is relatively easy
      score += 0.1;
    } else if (sourceLanguage == 'en' && targetLanguage == 'yo') {
      // English to Yoruba is harder
      score -= 0.15;
    }

    // Adjust based on text length
    // Very short or very long texts might be less reliable
    final textLength = originalText.length;
    if (textLength < 10) {
      score -= 0.05;
    } else if (textLength > 100) {
      score -= 0.1;
    }

    // Ensure score is between 0.0 and 1.0
    return math.max(0.0, math.min(1.0, score));
  }

  /// Get cultural context information for a translation
  Future<TranslationCulturalContext> _getCulturalContext(
    String text,
    String sourceLanguage,
    String targetLanguage,
    String? sourceRegion,
    String? targetRegion,
  ) async {
    try {
      // Delegate to the cultural context service
      return _culturalContextService.getCulturalContext(
        text: text,
        sourceLanguage: sourceLanguage,
        targetLanguage: targetLanguage,
        sourceRegion: sourceRegion,
        targetRegion: targetRegion,
      );
    } catch (e) {
      debugPrint('Error getting cultural context: $e');
      return TranslationCulturalContext.empty(
        sourceLanguage: sourceLanguage,
        targetLanguage: targetLanguage,
      );
    }
  }

  /// Get slang and idiom information for a translation
  Future<TranslationSlangIdiom> _getSlangIdiom(
    String text,
    String sourceLanguage,
    String targetLanguage,
    String? sourceRegion,
    String? targetRegion,
  ) async {
    try {
      // Delegate to the slang and idiom service
      return _slangIdiomService.getSlangIdiom(
        text: text,
        sourceLanguage: sourceLanguage,
        targetLanguage: targetLanguage,
        sourceRegion: sourceRegion,
        targetRegion: targetRegion,
      );
    } catch (e) {
      debugPrint('Error getting slang and idiom information: $e');
      return TranslationSlangIdiom.empty(
        sourceLanguage: sourceLanguage,
        targetLanguage: targetLanguage,
      );
    }
  }

  /// Get pronunciation information for a translation
  Future<TranslationPronunciation> _getPronunciation(
    String text,
    String sourceLanguage,
    String targetLanguage,
    String? sourceRegion,
    String? targetRegion,
  ) async {
    try {
      // Delegate to the pronunciation service
      return _pronunciationService.getPronunciation(
        text: text,
        sourceLanguage: sourceLanguage,
        targetLanguage: targetLanguage,
        sourceRegion: sourceRegion,
        targetRegion: targetRegion,
      );
    } catch (e) {
      debugPrint('Error getting pronunciation information: $e');
      return TranslationPronunciation.empty(
        sourceLanguage: sourceLanguage,
        targetLanguage: targetLanguage,
      );
    }
  }

  /// Clear the translation cache
  void clearCache() {
    _translationCache.clear();
  }

  /// Dispose resources
  void dispose() {
    _translationEventsController.close();
    _client.close();
  }
}

/// Provider for the message translation service
final messageTranslationServiceProvider =
    Provider<MessageTranslationService>((ref) {
  final voiceTranslationService = ref.watch(voiceTranslationServiceProvider);
  final playbackService = ref.watch(audioPlaybackServiceProvider);
  final languagePackService = ref.watch(languagePackServiceProvider);
  final customVocabularyService = ref.watch(customVocabularyServiceProvider);
  final culturalContextService = ref.watch(culturalContextServiceProvider);
  final slangIdiomService = ref.watch(slangIdiomServiceProvider);
  final pronunciationService = ref.watch(pronunciationServiceProvider);
  final prefs = ref.watch(sharedPreferencesProvider);
  final client = http.Client();

  final service = MessageTranslationService(
    voiceTranslationService,
    playbackService,
    languagePackService,
    customVocabularyService,
    culturalContextService,
    slangIdiomService,
    pronunciationService,
    prefs,
    client,
  );

  ref.onDispose(() {
    service.dispose();
  });

  return service;
});
