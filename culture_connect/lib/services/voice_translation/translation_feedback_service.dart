import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import 'package:uuid/uuid.dart';
import 'package:culture_connect/models/message_model.dart';
import 'package:culture_connect/models/translation/message_translation_metadata.dart';
import 'package:culture_connect/models/translation/translation_confidence_model.dart';
import 'package:culture_connect/models/translation/translation_feedback_model.dart';
import 'package:culture_connect/providers/auth_provider.dart';
import 'package:culture_connect/providers/shared_preferences_provider.dart';
import 'package:culture_connect/services/voice_translation/message_translation_service.dart';

/// A service for handling translation feedback
class TranslationFeedbackService {
  /// The HTTP client
  final http.Client _client;
  
  /// The shared preferences instance
  final SharedPreferences _prefs;
  
  /// Cache for feedback models
  final Map<String, TranslationFeedbackModel> _feedbackCache = {};
  
  /// Stream controller for feedback events
  final StreamController<String> _feedbackEventsController = StreamController<String>.broadcast();
  
  /// Creates a new translation feedback service
  TranslationFeedbackService(this._client, this._prefs);
  
  /// Stream of feedback events
  Stream<String> get feedbackEvents => _feedbackEventsController.stream;
  
  /// Submit feedback for a translation
  Future<TranslationFeedbackModel> submitFeedback({
    required String messageId,
    required String userId,
    required String sourceLanguage,
    required String targetLanguage,
    required String originalText,
    required String translatedText,
    String? suggestedCorrection,
    required TranslationQuality quality,
    required TranslationFeedbackType feedbackType,
    String? comments,
    double? originalConfidence,
    List<TextSegment>? problematicSegments,
  }) async {
    try {
      // Generate a unique ID for the feedback
      final id = const Uuid().v4();
      
      // Create the feedback model
      final feedback = TranslationFeedbackModel(
        id: id,
        messageId: messageId,
        userId: userId,
        sourceLanguage: sourceLanguage,
        targetLanguage: targetLanguage,
        originalText: originalText,
        translatedText: translatedText,
        suggestedCorrection: suggestedCorrection,
        quality: quality,
        feedbackType: feedbackType,
        comments: comments,
        isReviewed: false,
        isIncorporated: false,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        originalConfidence: originalConfidence,
        problematicSegments: problematicSegments,
      );
      
      // In a real app, we would send this to the server
      // For now, we'll just store it locally
      await _storeFeedbackLocally(feedback);
      
      // Update the cache
      _feedbackCache[id] = feedback;
      
      // Notify listeners
      _feedbackEventsController.add(id);
      
      return feedback;
    } catch (e) {
      debugPrint('Error submitting feedback: $e');
      rethrow;
    }
  }
  
  /// Store feedback locally
  Future<void> _storeFeedbackLocally(TranslationFeedbackModel feedback) async {
    try {
      // Get the existing feedback list
      final feedbackList = _prefs.getStringList('translation_feedback') ?? [];
      
      // Add the new feedback
      feedbackList.add(feedback.id);
      
      // Save the updated list
      await _prefs.setStringList('translation_feedback', feedbackList);
      
      // Save the feedback data
      await _prefs.setString('feedback_${feedback.id}', feedback.toJson().toString());
    } catch (e) {
      debugPrint('Error storing feedback locally: $e');
    }
  }
  
  /// Get feedback for a message
  Future<TranslationFeedbackModel?> getFeedback(String feedbackId) async {
    // Check the cache first
    if (_feedbackCache.containsKey(feedbackId)) {
      return _feedbackCache[feedbackId];
    }
    
    try {
      // In a real app, we would fetch this from the server
      // For now, we'll just retrieve it from local storage
      final feedbackJson = _prefs.getString('feedback_$feedbackId');
      if (feedbackJson == null) {
        return null;
      }
      
      // Parse the JSON
      final feedback = TranslationFeedbackModel.fromJson(
        Map<String, dynamic>.from(feedbackJson as Map),
      );
      
      // Update the cache
      _feedbackCache[feedbackId] = feedback;
      
      return feedback;
    } catch (e) {
      debugPrint('Error getting feedback: $e');
      return null;
    }
  }
  
  /// Get all feedback for a user
  Future<List<TranslationFeedbackModel>> getUserFeedback(String userId) async {
    try {
      // Get the feedback list
      final feedbackList = _prefs.getStringList('translation_feedback') ?? [];
      
      // Fetch each feedback
      final feedbackModels = <TranslationFeedbackModel>[];
      for (final feedbackId in feedbackList) {
        final feedback = await getFeedback(feedbackId);
        if (feedback != null && feedback.userId == userId) {
          feedbackModels.add(feedback);
        }
      }
      
      return feedbackModels;
    } catch (e) {
      debugPrint('Error getting user feedback: $e');
      return [];
    }
  }
  
  /// Get all feedback for a message
  Future<List<TranslationFeedbackModel>> getMessageFeedback(String messageId) async {
    try {
      // Get the feedback list
      final feedbackList = _prefs.getStringList('translation_feedback') ?? [];
      
      // Fetch each feedback
      final feedbackModels = <TranslationFeedbackModel>[];
      for (final feedbackId in feedbackList) {
        final feedback = await getFeedback(feedbackId);
        if (feedback != null && feedback.messageId == messageId) {
          feedbackModels.add(feedback);
        }
      }
      
      return feedbackModels;
    } catch (e) {
      debugPrint('Error getting message feedback: $e');
      return [];
    }
  }
  
  /// Get feedback statistics
  Future<Map<String, dynamic>> getFeedbackStatistics() async {
    try {
      // Get the feedback list
      final feedbackList = _prefs.getStringList('translation_feedback') ?? [];
      
      // Fetch each feedback
      final feedbackModels = <TranslationFeedbackModel>[];
      for (final feedbackId in feedbackList) {
        final feedback = await getFeedback(feedbackId);
        if (feedback != null) {
          feedbackModels.add(feedback);
        }
      }
      
      // Calculate statistics
      final totalFeedback = feedbackModels.length;
      final qualityDistribution = <TranslationQuality, int>{};
      final typeDistribution = <TranslationFeedbackType, int>{};
      final languagePairs = <String, int>{};
      
      for (final feedback in feedbackModels) {
        // Quality distribution
        qualityDistribution[feedback.quality] = (qualityDistribution[feedback.quality] ?? 0) + 1;
        
        // Type distribution
        typeDistribution[feedback.feedbackType] = (typeDistribution[feedback.feedbackType] ?? 0) + 1;
        
        // Language pairs
        final languagePair = '${feedback.sourceLanguage}-${feedback.targetLanguage}';
        languagePairs[languagePair] = (languagePairs[languagePair] ?? 0) + 1;
      }
      
      return {
        'totalFeedback': totalFeedback,
        'qualityDistribution': qualityDistribution.map((k, v) => MapEntry(k.index, v)),
        'typeDistribution': typeDistribution.map((k, v) => MapEntry(k.index, v)),
        'languagePairs': languagePairs,
      };
    } catch (e) {
      debugPrint('Error getting feedback statistics: $e');
      return {};
    }
  }
  
  /// Clear the feedback cache
  void clearCache() {
    _feedbackCache.clear();
  }
  
  /// Dispose resources
  void dispose() {
    _feedbackEventsController.close();
    _client.close();
  }
}

/// Provider for the translation feedback service
final translationFeedbackServiceProvider = Provider<TranslationFeedbackService>((ref) {
  final prefs = ref.watch(sharedPreferencesProvider);
  final client = http.Client();
  
  final service = TranslationFeedbackService(client, prefs);
  
  ref.onDispose(() {
    service.dispose();
  });
  
  return service;
});
