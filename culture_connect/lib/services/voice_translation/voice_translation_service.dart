import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:http/http.dart' as http;
import 'package:path_provider/path_provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:uuid/uuid.dart';
import 'package:culture_connect/models/translation/voice_translation_model.dart';
import 'package:culture_connect/models/translation/language_model.dart';
import 'package:culture_connect/models/translation/custom_vocabulary_model.dart';
import 'package:culture_connect/models/translation/dialect_model.dart';
import 'package:culture_connect/services/voice_translation/voice_recording_service.dart';
import 'package:culture_connect/services/voice_translation/audio_playback_service.dart';
import 'package:culture_connect/services/voice_translation/language_pack_service.dart';
import 'package:culture_connect/services/voice_translation/custom_vocabulary_service.dart';
import 'package:culture_connect/services/voice_translation/offline_translation_service.dart';
import 'package:culture_connect/services/voice_translation/offline_translation_manager.dart';
import 'package:culture_connect/services/voice_translation/dialect_accent_detection_service.dart';
import 'package:culture_connect/database/translation_database_helper.dart';

/// A service for translating voice recordings
class VoiceTranslationService {
  /// The API key for the translation service
  final String _apiKey;

  /// The HTTP client
  final http.Client _client;

  /// The shared preferences instance
  final SharedPreferences _prefs;

  /// The voice recording service
  final VoiceRecordingService _recordingService;

  /// The audio playback service
  final AudioPlaybackService _playbackService;

  /// The language pack service
  final LanguagePackService _languagePackService;

  /// The custom vocabulary service
  final CustomVocabularyService _customVocabularyService;

  /// The offline translation service
  final OfflineTranslationService _offlineTranslationService;

  /// The offline translation manager
  final OfflineTranslationManager _offlineTranslationManager;

  /// The dialect accent detection service
  final DialectAccentDetectionService _dialectAccentDetectionService;

  /// The translation database helper
  final TranslationDatabaseHelper _dbHelper;

  /// The translation history
  List<VoiceTranslationModel> _translationHistory = [];

  /// The translation history stream controller
  final StreamController<List<VoiceTranslationModel>> _historyStreamController =
      StreamController<List<VoiceTranslationModel>>.broadcast();

  /// The translation events controller
  final StreamController<String> _translationEventsController =
      StreamController<String>.broadcast();

  /// Whether to use offline mode when available
  bool _useOfflineMode = true;

  /// Whether to use dialect recognition
  bool _useDialectRecognition = true;

  /// Whether to use custom vocabulary
  bool _useCustomVocabulary = true;

  /// Creates a new voice translation service
  VoiceTranslationService(
    this._apiKey,
    this._client,
    this._prefs,
    this._recordingService,
    this._playbackService,
    this._languagePackService,
    this._customVocabularyService,
    this._offlineTranslationService,
    this._offlineTranslationManager,
    this._dialectAccentDetectionService,
    this._dbHelper,
  ) {
    _loadTranslationHistory();
    _loadSettings();
  }

  /// Load settings from shared preferences
  Future<void> _loadSettings() async {
    try {
      _useOfflineMode = _prefs.getBool('use_offline_mode') ?? true;
      _useDialectRecognition =
          _prefs.getBool('use_dialect_recognition') ?? true;
      _useCustomVocabulary = _prefs.getBool('use_custom_vocabulary') ?? true;
    } catch (e) {
      debugPrint('Error loading translation settings: $e');
    }
  }

  /// Save settings to shared preferences
  Future<void> _saveSettings() async {
    try {
      await _prefs.setBool('use_offline_mode', _useOfflineMode);
      await _prefs.setBool('use_dialect_recognition', _useDialectRecognition);
      await _prefs.setBool('use_custom_vocabulary', _useCustomVocabulary);
    } catch (e) {
      debugPrint('Error saving translation settings: $e');
    }
  }

  /// Load the translation history from shared preferences
  Future<void> _loadTranslationHistory() async {
    try {
      final historyJson = _prefs.getString('voice_translation_history');
      if (historyJson != null) {
        final List<dynamic> decoded = jsonDecode(historyJson);
        _translationHistory = decoded
            .map((item) => VoiceTranslationModel.fromJson(item))
            .toList();

        // Sort by timestamp (newest first)
        _translationHistory.sort((a, b) => b.timestamp.compareTo(a.timestamp));

        // Notify listeners
        _historyStreamController.add(_translationHistory);
      }
    } catch (e) {
      debugPrint('Error loading translation history: $e');
    }
  }

  /// Save the translation history to shared preferences
  Future<void> _saveTranslationHistory() async {
    try {
      final historyJson =
          jsonEncode(_translationHistory.map((item) => item.toJson()).toList());
      await _prefs.setString('voice_translation_history', historyJson);
    } catch (e) {
      debugPrint('Error saving translation history: $e');
    }
  }

  /// Start a new translation
  Future<VoiceTranslationModel> startTranslation({
    required String sourceLanguage,
    required String targetLanguage,
  }) async {
    // Create a new translation model
    final id = const Uuid().v4();
    final translation = VoiceTranslationModel(
      id: id,
      sourceLanguage: sourceLanguage,
      targetLanguage: targetLanguage,
      status: VoiceTranslationStatus.initial,
      timestamp: DateTime.now(),
    );

    // Add to history
    _translationHistory.insert(0, translation);
    _historyStreamController.add(_translationHistory);
    await _saveTranslationHistory();

    return translation;
  }

  /// Start recording for a translation
  Future<VoiceTranslationModel?> startRecording(String translationId) async {
    try {
      // Find the translation
      final index =
          _translationHistory.indexWhere((item) => item.id == translationId);
      if (index == -1) {
        debugPrint('Translation not found: $translationId');
        return null;
      }

      // Start recording
      final recordingPath = await _recordingService.startRecording();
      if (recordingPath == null) {
        debugPrint('Failed to start recording');
        return null;
      }

      // Update the translation
      final updatedTranslation = _translationHistory[index].copyWith(
        originalAudioPath: recordingPath,
        status: VoiceTranslationStatus.recording,
      );

      _translationHistory[index] = updatedTranslation;
      _historyStreamController.add(_translationHistory);
      await _saveTranslationHistory();

      return updatedTranslation;
    } catch (e) {
      debugPrint('Error starting recording: $e');
      return null;
    }
  }

  /// Stop recording and process the translation
  Future<VoiceTranslationModel?> stopRecordingAndTranslate(
      String translationId) async {
    try {
      // Find the translation
      final index =
          _translationHistory.indexWhere((item) => item.id == translationId);
      if (index == -1) {
        debugPrint('Translation not found: $translationId');
        return null;
      }

      // Stop recording
      final recordingPath = await _recordingService.stopRecording();
      if (recordingPath == null) {
        debugPrint('Failed to stop recording');
        return null;
      }

      // Update the translation to processing state
      var updatedTranslation = _translationHistory[index].copyWith(
        originalAudioPath: recordingPath,
        status: VoiceTranslationStatus.processing,
      );

      _translationHistory[index] = updatedTranslation;
      _historyStreamController.add(_translationHistory);
      await _saveTranslationHistory();

      // Process the translation
      updatedTranslation = await _processTranslation(updatedTranslation);

      // Update the translation with the results
      _translationHistory[index] = updatedTranslation;
      _historyStreamController.add(_translationHistory);
      await _saveTranslationHistory();

      return updatedTranslation;
    } catch (e) {
      debugPrint('Error stopping recording and translating: $e');

      // Update the translation to error state
      final index =
          _translationHistory.indexWhere((item) => item.id == translationId);
      if (index != -1) {
        final updatedTranslation = _translationHistory[index].copyWith(
          status: VoiceTranslationStatus.error,
          errorMessage: e.toString(),
        );

        _translationHistory[index] = updatedTranslation;
        _historyStreamController.add(_translationHistory);
        await _saveTranslationHistory();

        return updatedTranslation;
      }

      return null;
    }
  }

  /// Cancel the current recording
  Future<VoiceTranslationModel?> cancelRecording(String translationId) async {
    try {
      // Find the translation
      final index =
          _translationHistory.indexWhere((item) => item.id == translationId);
      if (index == -1) {
        debugPrint('Translation not found: $translationId');
        return null;
      }

      // Cancel recording
      await _recordingService.cancelRecording();

      // Update the translation
      final updatedTranslation = _translationHistory[index].copyWith(
        status: VoiceTranslationStatus.initial,
      );

      _translationHistory[index] = updatedTranslation;
      _historyStreamController.add(_translationHistory);
      await _saveTranslationHistory();

      return updatedTranslation;
    } catch (e) {
      debugPrint('Error canceling recording: $e');
      return null;
    }
  }

  /// Process a translation
  Future<VoiceTranslationModel> _processTranslation(
      VoiceTranslationModel translation) async {
    try {
      // Check if we should use offline mode
      final useOffline = _useOfflineMode &&
          _languagePackService
              .isLanguagePackDownloaded(translation.sourceLanguage) &&
          _languagePackService
              .isLanguagePackDownloaded(translation.targetLanguage);

      // Get dialect information if enabled
      String? sourceLangDialect;
      String? targetLangDialect;

      if (_useDialectRecognition) {
        // Check if there are preferred dialects
        sourceLangDialect = _dialectAccentDetectionService.getPreferredDialect(
          translation.sourceLanguage,
        );

        targetLangDialect = _dialectAccentDetectionService.getPreferredDialect(
          translation.targetLanguage,
        );

        // If no preferred dialects, use the language pack selected dialects
        if (sourceLangDialect == null) {
          final sourcePack =
              _languagePackService.getLanguagePack(translation.sourceLanguage);
          sourceLangDialect = sourcePack?.selectedDialect;
        }

        if (targetLangDialect == null) {
          final targetPack =
              _languagePackService.getLanguagePack(translation.targetLanguage);
          targetLangDialect = targetPack?.selectedDialect;
        }
      }

      // Get custom vocabulary if enabled
      List<CustomVocabularyModel>? customVocabulary;

      if (_useCustomVocabulary) {
        customVocabulary = _customVocabularyService
            .getVocabularyTermsByLanguage(translation.sourceLanguage);
      }

      // Log the translation mode
      debugPrint(
          'Processing translation in ${useOffline ? 'offline' : 'online'} mode');
      if (sourceLangDialect != null) {
        debugPrint('Source language dialect: $sourceLangDialect');
      }
      if (targetLangDialect != null) {
        debugPrint('Target language dialect: $targetLangDialect');
      }
      if (customVocabulary != null) {
        debugPrint('Using ${customVocabulary.length} custom vocabulary terms');
      }

      // 1. Convert speech to text
      final originalText = await _speechToText(
        translation.originalAudioPath!,
        translation.sourceLanguage,
        useOffline: useOffline,
        dialect: sourceLangDialect,
        customVocabulary: customVocabulary,
      );

      // 2. Translate the text
      final translatedText = await _translateText(
        originalText,
        translation.sourceLanguage,
        translation.targetLanguage,
        useOffline: useOffline,
        sourceDialect: sourceLangDialect,
        targetDialect: targetLangDialect,
        customVocabulary: customVocabulary,
      );

      // 3. Convert translated text to speech
      final translatedAudioPath = await _textToSpeech(
        translatedText,
        translation.targetLanguage,
        useOffline: useOffline,
        dialect: targetLangDialect,
      );

      // 4. Update usage count for any custom vocabulary terms used
      if (_useCustomVocabulary && customVocabulary != null) {
        for (final term in customVocabulary) {
          if (originalText
              .toLowerCase()
              .contains(term.originalTerm.toLowerCase())) {
            await _customVocabularyService.incrementUsageCount(term.id);
          }
        }
      }

      // 5. Return the updated translation
      return translation.copyWith(
        originalText: originalText,
        translatedText: translatedText,
        translatedAudioPath: translatedAudioPath,
        status: VoiceTranslationStatus.completed,
      );
    } catch (e) {
      debugPrint('Error processing translation: $e');
      return translation.copyWith(
        status: VoiceTranslationStatus.error,
        errorMessage: e.toString(),
      );
    }
  }

  /// Convert speech to text
  Future<String> _speechToText(
    String audioPath,
    String languageCode, {
    bool useOffline = false,
    String? dialect,
    List<CustomVocabularyModel>? customVocabulary,
  }) async {
    // For demo purposes, we'll use a mock implementation
    // In a real app, this would call the speech-to-text API

    // Simulate network delay (shorter for offline mode)
    await Future.delayed(Duration(milliseconds: useOffline ? 500 : 1500));

    // Get a mock result based on the language
    String recognizedText;
    if (languageCode == 'fr') {
      recognizedText = 'Bonjour, comment allez-vous aujourd\'hui?';
    } else if (languageCode == 'yo') {
      recognizedText = 'Bawo ni, bawo ni o se wa loni?';
    } else if (languageCode == 'ig') {
      recognizedText = 'Kedu, kedu ka ị mere taa?';
    } else if (languageCode == 'ha') {
      recognizedText = 'Sannu, yaya kake yau?';
    } else if (languageCode == 'sw') {
      recognizedText = 'Habari, habari yako leo?';
    } else {
      // For English, use a more varied response to demonstrate dialect detection
      final language = supportedLanguages.firstWhere(
        (lang) => lang.code == languageCode,
        orElse: () => supportedLanguages.first,
      );

      // If dialect is specified, use dialect-specific text
      if (dialect != null && _useDialectRecognition) {
        // Find the dialect model
        DialectModel? dialectModel;
        for (final d in language.dialects) {
          if (d.code == dialect) {
            dialectModel = d;
            break;
          }
        }

        if (dialectModel != null &&
            dialectModel.dialectSpecificTerms.isNotEmpty) {
          // Use some dialect-specific terms in the response
          if (dialectModel.code == 'en-us') {
            recognizedText =
                'Hello! I\'m taking the elevator to my apartment. I\'ll be on vacation next week.';
          } else if (dialectModel.code == 'en-gb') {
            recognizedText =
                'Hello! I\'m taking the lift to my flat. I\'ll be on holiday next week.';
          } else if (dialectModel.code == 'en-au') {
            recognizedText =
                'G\'day mate! I\'ll be at the barbie this arvo. Might grab some brekkie first.';
          } else {
            recognizedText = 'Hello, how are you today?';
          }
        } else {
          recognizedText = 'Hello, how are you today?';
        }
      } else {
        recognizedText = 'Hello, how are you today?';
      }
    }

    // If dialect recognition is enabled but no dialect was specified,
    // try to detect the dialect from the recognized text
    if (_useDialectRecognition && dialect == null) {
      final dialectResult = await _dialectAccentDetectionService.detectDialect(
        recognizedText,
        languageCode,
      );

      if (dialectResult != null && dialectResult.confidence >= 0.6) {
        // Log the detected dialect
        _translationEventsController.add(
          'Detected dialect: ${dialectResult.dialect.name} (confidence: ${dialectResult.confidence.toStringAsFixed(2)})',
        );

        // If there are detected terms, enhance the response
        if (dialectResult.detectedTerms.isNotEmpty) {
          _translationEventsController.add(
            'Detected dialect-specific terms: ${dialectResult.detectedTerms.join(', ')}',
          );
        }
      }
    }

    // If accent detection is enabled and we have a dialect,
    // try to detect the accent from the audio
    if (_useDialectRecognition &&
        dialect != null &&
        _dialectAccentDetectionService.useAccentDetection) {
      final accentResult =
          await _dialectAccentDetectionService.detectAccentFromAudio(
        audioPath,
        dialect,
      );

      if (accentResult != null && accentResult.confidence >= 0.5) {
        // Log the detected accent
        _translationEventsController.add(
          'Detected accent: ${accentResult.accent.name} (confidence: ${accentResult.confidence.toStringAsFixed(2)})',
        );
      }
    }

    return recognizedText;
  }

  /// Translate text (public method for use by other services)
  Future<String> translateText(
    String text,
    String sourceLanguageCode,
    String targetLanguageCode, {
    bool useOffline = false,
    String? sourceDialect,
    String? targetDialect,
    List<CustomVocabularyModel>? customVocabulary,
  }) async {
    return _translateText(
      text,
      sourceLanguageCode,
      targetLanguageCode,
      useOffline: useOffline,
      sourceDialect: sourceDialect,
      targetDialect: targetDialect,
      customVocabulary: customVocabulary,
    );
  }

  /// Translate text (internal implementation)
  Future<String> _translateText(
    String text,
    String sourceLanguageCode,
    String targetLanguageCode, {
    bool useOffline = false,
    String? sourceDialect,
    String? targetDialect,
    List<CustomVocabularyModel>? customVocabulary,
  }) async {
    // For demo purposes, we'll use a mock implementation
    // In a real app, this would call the translation API

    // Simulate network delay (shorter for offline mode)
    await Future.delayed(Duration(milliseconds: useOffline ? 500 : 1500));

    // Apply custom vocabulary if available
    String processedText = text;
    if (customVocabulary != null) {
      for (final term in customVocabulary) {
        if (term.originalLanguageCode == sourceLanguageCode) {
          final translation = term.getTranslation(targetLanguageCode);
          if (translation != null) {
            processedText = processedText.replaceAll(
              RegExp(term.originalTerm, caseSensitive: false),
              translation,
            );
          }
        }
      }
    }

    // Return a mock result based on the target language
    if (targetLanguageCode == 'fr') {
      return 'Bonjour, comment allez-vous aujourd\'hui?';
    } else if (targetLanguageCode == 'yo') {
      return 'Bawo ni, bawo ni o se wa loni?';
    } else if (targetLanguageCode == 'ig') {
      return 'Kedu, kedu ka ị mere taa?';
    } else if (targetLanguageCode == 'ha') {
      return 'Sannu, yaya kake yau?';
    } else if (targetLanguageCode == 'sw') {
      return 'Habari, habari yako leo?';
    } else {
      return 'Hello, how are you today?';
    }
  }

  /// Convert text to speech (public method for use by other services)
  Future<String?> textToSpeech(
    String text,
    String languageCode, {
    bool useOffline = false,
    String? dialect,
  }) async {
    return _textToSpeech(
      text,
      languageCode,
      useOffline: useOffline,
      dialect: dialect,
    );
  }

  /// Convert text to speech (internal implementation)
  Future<String> _textToSpeech(
    String text,
    String languageCode, {
    bool useOffline = false,
    String? dialect,
  }) async {
    // For demo purposes, we'll use a mock implementation
    // In a real app, this would call the text-to-speech API

    // Simulate network delay (shorter for offline mode)
    await Future.delayed(Duration(milliseconds: useOffline ? 500 : 1500));

    // Create a mock audio file
    final directory = await getTemporaryDirectory();
    final path = '${directory.path}/translated_audio_${const Uuid().v4()}.m4a';

    // Copy the original audio file to simulate a translated audio
    // In a real app, this would be the actual audio from the text-to-speech service
    final file = File(path);
    await file.writeAsString('Mock audio file');

    return path;
  }

  /// Set whether to use offline mode
  Future<void> setUseOfflineMode(bool value) async {
    _useOfflineMode = value;
    await _saveSettings();
  }

  /// Set whether to use dialect recognition
  Future<void> setUseDialectRecognition(bool value) async {
    _useDialectRecognition = value;
    await _saveSettings();
  }

  /// Set whether to use custom vocabulary
  Future<void> setUseCustomVocabulary(bool value) async {
    _useCustomVocabulary = value;
    await _saveSettings();
  }

  /// Get whether offline mode is enabled
  bool get useOfflineMode => _useOfflineMode;

  /// Get whether dialect recognition is enabled
  bool get useDialectRecognition => _useDialectRecognition;

  /// Get whether custom vocabulary is enabled
  bool get useCustomVocabulary => _useCustomVocabulary;

  /// Play the original audio
  Future<void> playOriginalAudio(String translationId) async {
    try {
      // Find the translation
      final translation =
          _translationHistory.firstWhere((item) => item.id == translationId);

      if (translation.originalAudioPath == null) {
        debugPrint('No original audio path');
        return;
      }

      // Load and play the audio
      await _playbackService.loadAudio(translation.originalAudioPath!);
      await _playbackService.play();
    } catch (e) {
      debugPrint('Error playing original audio: $e');
    }
  }

  /// Play the translated audio
  Future<void> playTranslatedAudio(String translationId) async {
    try {
      // Find the translation
      final translation =
          _translationHistory.firstWhere((item) => item.id == translationId);

      if (translation.translatedAudioPath == null) {
        debugPrint('No translated audio path');
        return;
      }

      // Load and play the audio
      await _playbackService.loadAudio(translation.translatedAudioPath!);
      await _playbackService.play();
    } catch (e) {
      debugPrint('Error playing translated audio: $e');
    }
  }

  /// Stop audio playback
  Future<void> stopAudio() async {
    await _playbackService.stop();
  }

  /// Toggle favorite status of a translation
  Future<VoiceTranslationModel?> toggleFavorite(String translationId) async {
    try {
      // Find the translation
      final index =
          _translationHistory.indexWhere((item) => item.id == translationId);
      if (index == -1) {
        debugPrint('Translation not found: $translationId');
        return null;
      }

      // Toggle favorite status
      final updatedTranslation = _translationHistory[index].copyWith(
        isFavorite: !_translationHistory[index].isFavorite,
      );

      _translationHistory[index] = updatedTranslation;
      _historyStreamController.add(_translationHistory);
      await _saveTranslationHistory();

      return updatedTranslation;
    } catch (e) {
      debugPrint('Error toggling favorite: $e');
      return null;
    }
  }

  /// Delete a translation
  Future<bool> deleteTranslation(String translationId) async {
    try {
      // Find the translation
      final index =
          _translationHistory.indexWhere((item) => item.id == translationId);
      if (index == -1) {
        debugPrint('Translation not found: $translationId');
        return false;
      }

      // Delete audio files
      final translation = _translationHistory[index];
      if (translation.originalAudioPath != null) {
        final originalFile = File(translation.originalAudioPath!);
        if (await originalFile.exists()) {
          await originalFile.delete();
        }
      }

      if (translation.translatedAudioPath != null) {
        final translatedFile = File(translation.translatedAudioPath!);
        if (await translatedFile.exists()) {
          await translatedFile.delete();
        }
      }

      // Remove from history
      _translationHistory.removeAt(index);
      _historyStreamController.add(_translationHistory);
      await _saveTranslationHistory();

      return true;
    } catch (e) {
      debugPrint('Error deleting translation: $e');
      return false;
    }
  }

  /// Clear all translations
  Future<bool> clearAllTranslations() async {
    try {
      // Delete all audio files
      for (final translation in _translationHistory) {
        if (translation.originalAudioPath != null) {
          final originalFile = File(translation.originalAudioPath!);
          if (await originalFile.exists()) {
            await originalFile.delete();
          }
        }

        if (translation.translatedAudioPath != null) {
          final translatedFile = File(translation.translatedAudioPath!);
          if (await translatedFile.exists()) {
            await translatedFile.delete();
          }
        }
      }

      // Clear history
      _translationHistory = [];
      _historyStreamController.add(_translationHistory);
      await _saveTranslationHistory();

      return true;
    } catch (e) {
      debugPrint('Error clearing all translations: $e');
      return false;
    }
  }

  /// Get the translation history stream
  Stream<List<VoiceTranslationModel>> get translationHistoryStream =>
      _historyStreamController.stream;

  /// Get the translation history
  List<VoiceTranslationModel> get translationHistory => _translationHistory;

  /// Get the recording service
  VoiceRecordingService get recordingService => _recordingService;

  /// Get the playback service
  AudioPlaybackService get playbackService => _playbackService;

  /// Dispose the service
  Future<void> dispose() async {
    await _historyStreamController.close();
    await _translationEventsController.close();
  }
}

/// Provider for the voice translation service
final voiceTranslationServiceProvider =
    Provider<VoiceTranslationService>((ref) {
  const apiKey =
      'mock_api_key'; // In a real app, this would come from environment variables
  final client = http.Client();
  final prefs = ref.watch(sharedPreferencesProvider);
  final recordingService = ref.watch(voiceRecordingServiceProvider);
  final playbackService = ref.watch(audioPlaybackServiceProvider);
  final languagePackService = ref.watch(languagePackServiceProvider);
  final customVocabularyService = ref.watch(customVocabularyServiceProvider);
  final offlineTranslationService =
      ref.watch(offlineTranslationServiceProvider);
  final offlineTranslationManager =
      ref.watch(offlineTranslationManagerProvider);
  final dbHelper = TranslationDatabaseHelper();

  final dialectAccentDetectionService =
      ref.watch(dialectAccentDetectionServiceProvider);

  final service = VoiceTranslationService(
    apiKey,
    client,
    prefs,
    recordingService,
    playbackService,
    languagePackService,
    customVocabularyService,
    offlineTranslationService,
    offlineTranslationManager,
    dialectAccentDetectionService,
    dbHelper,
  );

  ref.onDispose(() {
    service.dispose();
  });

  return service;
});

/// Provider for shared preferences
final sharedPreferencesProvider = Provider<SharedPreferences>((ref) {
  throw UnimplementedError('SharedPreferences provider not initialized');
});
