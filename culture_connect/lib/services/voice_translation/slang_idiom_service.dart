import 'dart:async';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import 'package:uuid/uuid.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:culture_connect/models/translation/slang_idiom_model.dart';
import 'package:culture_connect/models/translation/translation_slang_idiom.dart';
import 'package:culture_connect/providers/shared_preferences_provider.dart';
import 'package:culture_connect/services/logging_service.dart';

/// A service for handling slang and idiom information
class SlangIdiomService {
  /// The HTTP client
  final http.Client _client;

  /// The shared preferences instance
  final SharedPreferences _prefs;

  /// The logging service
  final LoggingService _loggingService;

  /// The connectivity service
  final Connectivity _connectivity;

  /// Whether the service is currently online
  bool _isOnline = true;

  /// Subscription to connectivity changes
  StreamSubscription<ConnectivityResult>? _connectivitySubscription;

  /// Cache for slang and idiom information
  final Map<String, TranslationSlangIdiom> _slangIdiomCache = {};

  /// Stream controller for slang and idiom events
  final StreamController<String> _slangIdiomEventsController =
      StreamController<String>.broadcast();

  /// Stream controller for error events
  final StreamController<String> _errorController =
      StreamController<String>.broadcast();

  /// Whether to use slang and idiom detection
  bool _useSlangIdiomDetection = true;

  /// Whether to show potentially offensive content
  bool _showPotentiallyOffensiveContent = false;

  /// Whether to use offline mode
  bool _useOfflineMode = false;

  /// Creates a new slang and idiom service
  SlangIdiomService(
      this._client, this._prefs, this._loggingService, this._connectivity) {
    _loadPreferences();
    _initConnectivity();
  }

  /// Stream of slang and idiom events
  Stream<String> get slangIdiomEvents => _slangIdiomEventsController.stream;

  /// Stream of error events
  Stream<String> get errorStream => _errorController.stream;

  /// Whether to use slang and idiom detection
  bool get useSlangIdiomDetection => _useSlangIdiomDetection;

  /// Whether to show potentially offensive content
  bool get showPotentiallyOffensiveContent => _showPotentiallyOffensiveContent;

  /// Whether to use offline mode
  bool get useOfflineMode => _useOfflineMode;

  /// Whether the service is currently online
  bool get isOnline => _isOnline;

  /// Initialize connectivity monitoring
  void _initConnectivity() {
    _connectivitySubscription =
        _connectivity.onConnectivityChanged.listen(_updateConnectionStatus);
    // Check initial connection state
    _checkConnectivity();
  }

  /// Check current connectivity
  Future<void> _checkConnectivity() async {
    try {
      final result = await _connectivity.checkConnectivity();
      _updateConnectionStatus(result);
    } on Exception catch (e) {
      debugPrint('Error checking connectivity: $e');
      _loggingService.error(
          'SlangIdiomService', 'Error checking connectivity: $e');
      _isOnline = false;
    }
  }

  /// Update connection status based on connectivity result
  void _updateConnectionStatus(ConnectivityResult result) {
    _isOnline = result != ConnectivityResult.none;
    _loggingService.info('SlangIdiomService',
        'Connectivity changed: ${_isOnline ? 'Online' : 'Offline'}');
  }

  /// Load preferences
  void _loadPreferences() {
    _useSlangIdiomDetection =
        _prefs.getBool('use_slang_idiom_detection') ?? true;
    _showPotentiallyOffensiveContent =
        _prefs.getBool('show_potentially_offensive_content') ?? false;
    _useOfflineMode = _prefs.getBool('use_offline_mode_slang_idiom') ?? false;
    _loggingService.info('SlangIdiomService', 'Loaded preferences');
  }

  /// Set whether to use slang and idiom detection
  Future<bool> setUseSlangIdiomDetection(bool value) async {
    try {
      _useSlangIdiomDetection = value;
      final result = await _prefs.setBool('use_slang_idiom_detection', value);

      if (result) {
        _loggingService.info(
            'SlangIdiomService', 'Set use slang idiom detection: $value');
      } else {
        _loggingService.error(
            'SlangIdiomService', 'Failed to set use slang idiom detection');
        _errorController.add('Failed to set use slang idiom detection');
      }

      return result;
    } catch (e) {
      _loggingService.error(
          'SlangIdiomService', 'Error setting use slang idiom detection: $e');
      _errorController.add('Error setting use slang idiom detection: $e');
      return false;
    }
  }

  /// Set whether to show potentially offensive content
  Future<bool> setShowPotentiallyOffensiveContent(bool value) async {
    try {
      _showPotentiallyOffensiveContent = value;
      final result =
          await _prefs.setBool('show_potentially_offensive_content', value);

      if (result) {
        _loggingService.info('SlangIdiomService',
            'Set show potentially offensive content: $value');
      } else {
        _loggingService.error('SlangIdiomService',
            'Failed to set show potentially offensive content');
        _errorController
            .add('Failed to set show potentially offensive content');
      }

      return result;
    } catch (e) {
      _loggingService.error('SlangIdiomService',
          'Error setting show potentially offensive content: $e');
      _errorController
          .add('Error setting show potentially offensive content: $e');
      return false;
    }
  }

  /// Set whether to use offline mode
  Future<bool> setUseOfflineMode(bool value) async {
    try {
      _useOfflineMode = value;
      final result =
          await _prefs.setBool('use_offline_mode_slang_idiom', value);

      if (result) {
        _loggingService.info(
            'SlangIdiomService', 'Set use offline mode: $value');
      } else {
        _loggingService.error(
            'SlangIdiomService', 'Failed to set use offline mode');
        _errorController.add('Failed to set use offline mode');
      }

      return result;
    } catch (e) {
      _loggingService.error(
          'SlangIdiomService', 'Error setting use offline mode: $e');
      _errorController.add('Error setting use offline mode: $e');
      return false;
    }
  }

  /// Get slang and idiom information for a translation
  Future<TranslationSlangIdiom> getSlangIdiom({
    required String text,
    required String sourceLanguage,
    required String targetLanguage,
    String? sourceRegion,
    String? targetRegion,
  }) async {
    if (!_useSlangIdiomDetection) {
      _loggingService.info(
          'SlangIdiomService', 'Slang idiom detection is disabled');
      return TranslationSlangIdiom.empty(
        sourceLanguage: sourceLanguage,
        targetLanguage: targetLanguage,
      );
    }

    // Create a cache key
    final cacheKey = '${sourceLanguage}_${targetLanguage}_${text.hashCode}';

    // Check the cache
    if (_slangIdiomCache.containsKey(cacheKey)) {
      _loggingService.info(
          'SlangIdiomService', 'Using cached slang idiom information');
      return _slangIdiomCache[cacheKey]!;
    }

    // Check if we're offline and not in offline mode
    if (!_isOnline && !_useOfflineMode) {
      const errorMsg = 'Cannot get slang idiom information while offline';
      _loggingService.error('SlangIdiomService', errorMsg);
      _errorController.add(errorMsg);

      return TranslationSlangIdiom.empty(
        sourceLanguage: sourceLanguage,
        targetLanguage: targetLanguage,
      );
    }

    try {
      // In a real app, we would call an API to get slang and idiom information
      // For demo purposes, we'll generate mock data
      final slangIdiom = _generateMockSlangIdiom(
        text,
        sourceLanguage,
        targetLanguage,
        sourceRegion,
        targetRegion,
      );

      // Cache the result
      _slangIdiomCache[cacheKey] = slangIdiom;

      // Notify listeners
      _slangIdiomEventsController.add(cacheKey);

      _loggingService.info(
          'SlangIdiomService', 'Generated slang idiom information');

      return slangIdiom;
    } catch (e) {
      final errorMsg = 'Error getting slang and idiom information: $e';
      debugPrint(errorMsg);
      _loggingService.error('SlangIdiomService', errorMsg);
      _errorController.add(errorMsg);

      return TranslationSlangIdiom.empty(
        sourceLanguage: sourceLanguage,
        targetLanguage: targetLanguage,
      );
    }
  }

  /// Generate mock slang and idiom information
  TranslationSlangIdiom _generateMockSlangIdiom(
    String text,
    String sourceLanguage,
    String targetLanguage,
    String? sourceRegion,
    String? targetRegion,
  ) {
    // For demo purposes, we'll generate mock data based on the language pair and text content
    // In a real app, this would come from an API or a database

    final expressions = <SlangIdiomExpression>[];
    String? generalNotes;

    // Generate language pair specific notes
    if (sourceLanguage == 'en' && targetLanguage == 'fr') {
      generalNotes =
          'French has many idioms and expressions that don\'t translate literally. '
          'Slang varies significantly by region and age group.';

      // Check for common English idioms and slang
      if (text.toLowerCase().contains('break a leg')) {
        expressions.add(SlangIdiomExpression(
          id: const Uuid().v4(),
          expression: 'Break a leg',
          startIndex: text.toLowerCase().indexOf('break a leg'),
          endIndex:
              text.toLowerCase().indexOf('break a leg') + 'break a leg'.length,
          type: ExpressionType.idiom,
          literalMeaning: 'Physically break someone\'s leg',
          actualMeaning: 'Good luck (especially before a performance)',
          examples: ['Break a leg at your audition tomorrow!'],
          languageCode: 'en',
          formalityLevel: FormalityLevel.informal,
          isPotentiallyOffensive: false,
          origin:
              'Theater superstition where wishing someone good luck was thought to bring bad luck',
          standardAlternatives: ['Good luck', 'All the best'],
        ));
      }

      if (text.toLowerCase().contains('piece of cake')) {
        expressions.add(SlangIdiomExpression(
          id: const Uuid().v4(),
          expression: 'Piece of cake',
          startIndex: text.toLowerCase().indexOf('piece of cake'),
          endIndex: text.toLowerCase().indexOf('piece of cake') +
              'piece of cake'.length,
          type: ExpressionType.idiom,
          literalMeaning: 'A slice of cake',
          actualMeaning: 'Something very easy to do',
          examples: ['The test was a piece of cake.'],
          languageCode: 'en',
          formalityLevel: FormalityLevel.informal,
          isPotentiallyOffensive: false,
          standardAlternatives: ['Very easy', 'Simple'],
        ));
      }

      if (text.toLowerCase().contains('cool')) {
        expressions.add(SlangIdiomExpression(
          id: const Uuid().v4(),
          expression: 'Cool',
          startIndex: text.toLowerCase().indexOf('cool'),
          endIndex: text.toLowerCase().indexOf('cool') + 'cool'.length,
          type: ExpressionType.slang,
          literalMeaning: 'Low temperature',
          actualMeaning: 'Good, excellent, or fashionable',
          examples: ["That's a cool jacket!", "She has a cool new job."],
          languageCode: 'en',
          formalityLevel: FormalityLevel.informal,
          isPotentiallyOffensive: false,
          standardAlternatives: ['Good', 'Excellent', 'Impressive'],
        ));
      }
    } else if (sourceLanguage == 'en' && targetLanguage == 'es') {
      generalNotes = 'Spanish has rich regional variations in slang. '
          'Many idioms have cultural or historical origins that may not be obvious.';

      // Check for common English idioms and slang
      if (text.toLowerCase().contains('hit the road')) {
        expressions.add(SlangIdiomExpression(
          id: const Uuid().v4(),
          expression: 'Hit the road',
          startIndex: text.toLowerCase().indexOf('hit the road'),
          endIndex: text.toLowerCase().indexOf('hit the road') +
              'hit the road'.length,
          type: ExpressionType.idiom,
          literalMeaning: 'Physically strike the road',
          actualMeaning: 'To leave or start a journey',
          examples: ["It's getting late, we should hit the road."],
          languageCode: 'en',
          formalityLevel: FormalityLevel.informal,
          isPotentiallyOffensive: false,
          standardAlternatives: ['Let\'s go', 'Let\'s leave'],
        ));
      }

      if (text.toLowerCase().contains('awesome')) {
        expressions.add(SlangIdiomExpression(
          id: const Uuid().v4(),
          expression: 'Awesome',
          startIndex: text.toLowerCase().indexOf('awesome'),
          endIndex: text.toLowerCase().indexOf('awesome') + 'awesome'.length,
          type: ExpressionType.slang,
          literalMeaning: 'Inspiring awe',
          actualMeaning: 'Very good, excellent',
          examples: ['The concert was awesome!'],
          languageCode: 'en',
          formalityLevel: FormalityLevel.informal,
          isPotentiallyOffensive: false,
          standardAlternatives: ['Excellent', 'Wonderful'],
        ));
      }
    }

    // Add some potentially offensive slang
    if (text.toLowerCase().contains('sucks')) {
      expressions.add(SlangIdiomExpression(
        id: const Uuid().v4(),
        expression: 'Sucks',
        startIndex: text.toLowerCase().indexOf('sucks'),
        endIndex: text.toLowerCase().indexOf('sucks') + 'sucks'.length,
        type: ExpressionType.slang,
        literalMeaning: 'To draw into the mouth by creating a vacuum',
        actualMeaning: 'Something is bad or of poor quality',
        examples: ['This movie sucks.'],
        languageCode: 'en',
        formalityLevel: FormalityLevel.veryInformal,
        isPotentiallyOffensive: true,
        standardAlternatives: ['Is bad', 'Is poor quality'],
        notes: 'Considered mildly vulgar in formal settings',
      ));
    }

    // Add some colloquialisms
    if (text.toLowerCase().contains('hang out')) {
      expressions.add(SlangIdiomExpression(
        id: const Uuid().v4(),
        expression: 'Hang out',
        startIndex: text.toLowerCase().indexOf('hang out'),
        endIndex: text.toLowerCase().indexOf('hang out') + 'hang out'.length,
        type: ExpressionType.colloquialism,
        literalMeaning: 'To suspend or be suspended',
        actualMeaning: 'To spend time relaxing or socializing',
        examples: ['Let\'s hang out this weekend.'],
        languageCode: 'en',
        formalityLevel: FormalityLevel.informal,
        isPotentiallyOffensive: false,
        standardAlternatives: ['Spend time together', 'Socialize'],
      ));
    }

    // Add some proverbs
    if (text.toLowerCase().contains('early bird')) {
      expressions.add(SlangIdiomExpression(
        id: const Uuid().v4(),
        expression: 'The early bird catches the worm',
        startIndex: text.toLowerCase().indexOf('early bird'),
        endIndex:
            text.toLowerCase().indexOf('early bird') + 'early bird'.length,
        type: ExpressionType.proverb,
        literalMeaning: 'A bird that wakes up early will find worms to eat',
        actualMeaning: 'People who start early have an advantage',
        examples: [
          'I always get to sales early - the early bird catches the worm!'
        ],
        languageCode: 'en',
        formalityLevel: FormalityLevel.neutral,
        isPotentiallyOffensive: false,
        origin: 'Traditional English proverb',
        standardAlternatives: ['Being early gives you an advantage'],
      ));
    }

    return TranslationSlangIdiom(
      expressions: expressions,
      hasExpressions: expressions.isNotEmpty,
      sourceLanguage: sourceLanguage,
      targetLanguage: targetLanguage,
      sourceRegion: sourceRegion,
      targetRegion: targetRegion,
      generalNotes: generalNotes,
    );
  }

  /// Clear the slang and idiom cache
  void clearCache() {
    _slangIdiomCache.clear();
    _loggingService.info('SlangIdiomService', 'Cleared slang idiom cache');
  }

  /// Get the size of the cache
  int getCacheSize() {
    return _slangIdiomCache.length;
  }

  /// Dispose resources
  void dispose() {
    _connectivitySubscription?.cancel();
    _slangIdiomEventsController.close();
    _errorController.close();
    _client.close();
    _loggingService.info('SlangIdiomService', 'Disposed slang idiom service');
  }
}

/// Provider for the slang and idiom service
final slangIdiomServiceProvider = Provider<SlangIdiomService>((ref) {
  final prefs = ref.watch(sharedPreferencesProvider);
  final client = http.Client();
  final loggingService = LoggingService();
  final connectivity = Connectivity();

  final service =
      SlangIdiomService(client, prefs, loggingService, connectivity);

  ref.onDispose(() {
    service.dispose();
  });

  return service;
});
