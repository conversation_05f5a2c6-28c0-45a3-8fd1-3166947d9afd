import 'dart:async';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:culture_connect/models/translation/dialect_accent_model.dart';
import 'package:culture_connect/providers/shared_preferences_provider.dart';
import 'package:culture_connect/services/logging_service.dart';

/// A service for handling dialect and accent detection and adaptation
class DialectAccentService {
  /// The HTTP client
  final http.Client _client;
  
  /// The shared preferences instance
  final SharedPreferences _prefs;
  
  /// The logging service
  final LoggingService _loggingService;
  
  /// The connectivity service
  final Connectivity _connectivity;
  
  /// Whether the service is currently online
  bool _isOnline = true;
  
  /// Subscription to connectivity changes
  StreamSubscription<ConnectivityResult>? _connectivitySubscription;
  
  /// Cache for dialect and accent information
  final Map<String, DialectAccentModel> _dialectAccentCache = {};
  
  /// Stream controller for dialect and accent events
  final StreamController<String> _dialectAccentEventsController = 
      StreamController<String>.broadcast();
      
  /// Stream controller for error events
  final StreamController<String> _errorController = 
      StreamController<String>.broadcast();
  
  /// Whether to use dialect and accent detection
  bool _useDialectAccentDetection = true;
  
  /// Whether to use offline mode
  bool _useOfflineMode = false;
  
  /// Creates a new dialect and accent service
  DialectAccentService(this._client, this._prefs, this._loggingService, this._connectivity) {
    _loadPreferences();
    _initConnectivity();
  }
  
  /// Stream of dialect and accent events
  Stream<String> get dialectAccentEvents => _dialectAccentEventsController.stream;
  
  /// Stream of error events
  Stream<String> get errorStream => _errorController.stream;
  
  /// Whether to use dialect and accent detection
  bool get useDialectAccentDetection => _useDialectAccentDetection;
  
  /// Whether to use offline mode
  bool get useOfflineMode => _useOfflineMode;
  
  /// Whether the service is currently online
  bool get isOnline => _isOnline;
  
  /// Initialize connectivity monitoring
  void _initConnectivity() {
    _connectivitySubscription = _connectivity.onConnectivityChanged.listen(_updateConnectionStatus);
    // Check initial connection state
    _checkConnectivity();
  }
  
  /// Check current connectivity
  Future<void> _checkConnectivity() async {
    try {
      final result = await _connectivity.checkConnectivity();
      _updateConnectionStatus(result);
    } on Exception catch (e) {
      debugPrint('Error checking connectivity: $e');
      _loggingService.error('DialectAccentService', 'Error checking connectivity: $e');
      _isOnline = false;
    }
  }
  
  /// Update connection status based on connectivity result
  void _updateConnectionStatus(ConnectivityResult result) {
    _isOnline = result != ConnectivityResult.none;
    _loggingService.info('DialectAccentService', 'Connectivity changed: ${_isOnline ? 'Online' : 'Offline'}');
  }
  
  /// Load preferences
  void _loadPreferences() {
    _useDialectAccentDetection = _prefs.getBool('use_dialect_accent_detection') ?? true;
    _useOfflineMode = _prefs.getBool('use_offline_mode_dialect_accent') ?? false;
    _loggingService.info('DialectAccentService', 'Loaded preferences');
  }
  
  /// Set whether to use dialect and accent detection
  Future<bool> setUseDialectAccentDetection(bool value) async {
    try {
      _useDialectAccentDetection = value;
      final result = await _prefs.setBool('use_dialect_accent_detection', value);
      
      if (result) {
        _loggingService.info('DialectAccentService', 'Set use dialect accent detection: $value');
      } else {
        _loggingService.error('DialectAccentService', 'Failed to set use dialect accent detection');
        _errorController.add('Failed to set use dialect accent detection');
      }
      
      return result;
    } catch (e) {
      _loggingService.error('DialectAccentService', 'Error setting use dialect accent detection: $e');
      _errorController.add('Error setting use dialect accent detection: $e');
      return false;
    }
  }
  
  /// Set whether to use offline mode
  Future<bool> setUseOfflineMode(bool value) async {
    try {
      _useOfflineMode = value;
      final result = await _prefs.setBool('use_offline_mode_dialect_accent', value);
      
      if (result) {
        _loggingService.info('DialectAccentService', 'Set use offline mode: $value');
      } else {
        _loggingService.error('DialectAccentService', 'Failed to set use offline mode');
        _errorController.add('Failed to set use offline mode');
      }
      
      return result;
    } catch (e) {
      _loggingService.error('DialectAccentService', 'Error setting use offline mode: $e');
      _errorController.add('Error setting use offline mode: $e');
      return false;
    }
  }
  
  /// Get dialect and accent information for a text
  Future<DialectAccentModel> getDialectAccent({
    required String text,
    required String languageCode,
    String? regionCode,
  }) async {
    if (!_useDialectAccentDetection) {
      _loggingService.info('DialectAccentService', 'Dialect accent detection is disabled');
      return DialectAccentModel.empty(languageCode: languageCode);
    }
    
    // Create a cache key
    final cacheKey = '${languageCode}_${regionCode ?? ''}_${text.hashCode}';
    
    // Check the cache
    if (_dialectAccentCache.containsKey(cacheKey)) {
      _loggingService.info('DialectAccentService', 'Using cached dialect accent information');
      return _dialectAccentCache[cacheKey]!;
    }
    
    // Check if we're offline and not in offline mode
    if (!_isOnline && !_useOfflineMode) {
      const errorMsg = 'Cannot get dialect accent information while offline';
      _loggingService.error('DialectAccentService', errorMsg);
      _errorController.add(errorMsg);
      
      return DialectAccentModel.empty(languageCode: languageCode);
    }
    
    try {
      // In a real app, we would call an API to get dialect and accent information
      // For demo purposes, we'll generate mock data
      final dialectAccent = _generateMockDialectAccent(
        text,
        languageCode,
        regionCode,
      );
      
      // Cache the result
      _dialectAccentCache[cacheKey] = dialectAccent;
      
      // Notify listeners
      _dialectAccentEventsController.add(cacheKey);
      
      _loggingService.info('DialectAccentService', 'Generated dialect accent information');
      
      return dialectAccent;
    } catch (e) {
      final errorMsg = 'Error getting dialect and accent information: $e';
      debugPrint(errorMsg);
      _loggingService.error('DialectAccentService', errorMsg);
      _errorController.add(errorMsg);
      
      return DialectAccentModel.empty(languageCode: languageCode);
    }
  }
  
  /// Generate mock dialect and accent information
  DialectAccentModel _generateMockDialectAccent(
    String text,
    String languageCode,
    String? regionCode,
  ) {
    // This is a mock implementation for demo purposes
    // In a real app, this would call an API
    
    if (languageCode == 'en') {
      if (regionCode == 'US') {
        return DialectAccentModel(
          languageCode: languageCode,
          regionCode: regionCode,
          dialectName: 'American English',
          accentName: text.length % 3 == 0 ? 'Southern' : 'General American',
          confidenceScore: 0.85,
          alternativeDialects: [
            'Midwestern',
            'New York',
          ],
          regionalExpressions: [
            'y\'all',
            'fixin\' to',
          ],
          pronunciationTips: [
            'Emphasize the "r" sounds',
            'Elongate vowels in certain words',
          ],
        );
      } else if (regionCode == 'GB') {
        return DialectAccentModel(
          languageCode: languageCode,
          regionCode: regionCode,
          dialectName: 'British English',
          accentName: text.length % 2 == 0 ? 'Received Pronunciation' : 'Cockney',
          confidenceScore: 0.9,
          alternativeDialects: [
            'Scottish',
            'Welsh',
          ],
          regionalExpressions: [
            'Cheers',
            'Bloody',
          ],
          pronunciationTips: [
            'Drop the "r" at the end of words',
            'Use rising intonation for questions',
          ],
        );
      }
    } else if (languageCode == 'es') {
      if (regionCode == 'ES') {
        return DialectAccentModel(
          languageCode: languageCode,
          regionCode: regionCode,
          dialectName: 'Castilian Spanish',
          accentName: 'Madrid',
          confidenceScore: 0.88,
          alternativeDialects: [
            'Andalusian',
            'Catalan',
          ],
          regionalExpressions: [
            'Vale',
            'Guay',
          ],
          pronunciationTips: [
            'Use the "th" sound for "z" and soft "c"',
            'Pronounce all syllables clearly',
          ],
        );
      } else if (regionCode == 'MX') {
        return DialectAccentModel(
          languageCode: languageCode,
          regionCode: regionCode,
          dialectName: 'Mexican Spanish',
          accentName: 'Central Mexican',
          confidenceScore: 0.82,
          alternativeDialects: [
            'Northern Mexican',
            'Yucatecan',
          ],
          regionalExpressions: [
            '¿Qué onda?',
            'Órale',
          ],
          pronunciationTips: [
            'Soften the "s" sounds',
            'Use rising intonation for emphasis',
          ],
        );
      }
    }
    
    // Default case
    return DialectAccentModel(
      languageCode: languageCode,
      regionCode: regionCode,
      dialectName: 'Standard $languageCode',
      accentName: 'Neutral',
      confidenceScore: 0.7,
      alternativeDialects: [],
      regionalExpressions: [],
      pronunciationTips: [],
    );
  }
  
  /// Clear the dialect and accent cache
  void clearCache() {
    _dialectAccentCache.clear();
    _loggingService.info('DialectAccentService', 'Cleared dialect accent cache');
  }
  
  /// Get the size of the cache
  int getCacheSize() {
    return _dialectAccentCache.length;
  }
  
  /// Dispose resources
  void dispose() {
    _connectivitySubscription?.cancel();
    _dialectAccentEventsController.close();
    _errorController.close();
    _client.close();
    _loggingService.info('DialectAccentService', 'Disposed dialect accent service');
  }
}

/// Provider for the dialect and accent service
final dialectAccentServiceProvider = Provider<DialectAccentService>((ref) {
  final prefs = ref.watch(sharedPreferencesProvider);
  final client = http.Client();
  final loggingService = LoggingService();
  final connectivity = Connectivity();
  
  final service = DialectAccentService(client, prefs, loggingService, connectivity);
  
  ref.onDispose(() {
    service.dispose();
  });
  
  return service;
});
