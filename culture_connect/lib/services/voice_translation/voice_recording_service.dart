import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:path_provider/path_provider.dart';
import 'package:record/record.dart';
import 'package:uuid/uuid.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:culture_connect/services/logging_service.dart';

/// A service for recording voice
class VoiceRecordingService {
  /// The audio recorder instance
  final AudioRecorder _recorder = AudioRecorder();

  /// Whether the service is initialized
  bool _isInitialized = false;

  /// The path of the current recording
  String? _currentRecordingPath;

  /// The amplitude stream subscription
  StreamSubscription<Amplitude>? _amplitudeSubscription;

  /// The connectivity subscription
  StreamSubscription<ConnectivityResult>? _connectivitySubscription;

  /// The logging service
  final LoggingService _loggingService;

  /// The connectivity service
  final Connectivity _connectivity;

  /// Whether the service is currently online
  bool _isOnline = true;

  /// The amplitude stream controller
  final StreamController<double> _amplitudeStreamController =
      StreamController<double>.broadcast();

  /// The error stream controller
  final StreamController<String> _errorController =
      StreamController<String>.broadcast();

  /// Creates a new voice recording service
  VoiceRecordingService(this._loggingService, this._connectivity) {
    _initConnectivity();
  }

  /// Initialize connectivity monitoring
  void _initConnectivity() {
    _connectivitySubscription =
        _connectivity.onConnectivityChanged.listen(_updateConnectionStatus);
    // Check initial connection state
    _checkConnectivity();
  }

  /// Check current connectivity
  Future<void> _checkConnectivity() async {
    try {
      final result = await _connectivity.checkConnectivity();
      _updateConnectionStatus(result);
    } on Exception catch (e) {
      debugPrint('Error checking connectivity: $e');
      _loggingService.error(
          'VoiceRecordingService', 'Error checking connectivity: $e');
      _isOnline = false;
    }
  }

  /// Update connection status based on connectivity result
  void _updateConnectionStatus(ConnectivityResult result) {
    _isOnline = result != ConnectivityResult.none;
    _loggingService.info('VoiceRecordingService',
        'Connectivity changed: ${_isOnline ? 'Online' : 'Offline'}');
  }

  /// Get the error stream
  Stream<String> get errorStream => _errorController.stream;

  /// Whether the service is currently online
  bool get isOnline => _isOnline;

  /// Initialize the voice recording service
  Future<bool> initialize() async {
    if (_isInitialized) return true;

    try {
      // Check for recording permission
      final hasPermission = await _recorder.hasPermission();
      if (!hasPermission) {
        const errorMsg = 'Microphone permission not granted';
        debugPrint(errorMsg);
        _errorController.add(errorMsg);
        _loggingService.error('VoiceRecordingService', errorMsg);
        return false;
      }

      _isInitialized = true;
      _loggingService.info(
          'VoiceRecordingService', 'Voice recording service initialized');
      return true;
    } catch (e) {
      final errorMsg = 'Error initializing voice recording service: $e';
      debugPrint(errorMsg);
      _errorController.add(errorMsg);
      _loggingService.error('VoiceRecordingService', errorMsg);
      return false;
    }
  }

  /// Start recording
  Future<String?> startRecording() async {
    if (!_isInitialized) {
      final initialized = await initialize();
      if (!initialized) return null;
    }

    // Check if already recording
    final isRecording = await _recorder.isRecording();
    if (isRecording) {
      const infoMsg = 'Already recording';
      debugPrint(infoMsg);
      _loggingService.info('VoiceRecordingService', infoMsg);
      return _currentRecordingPath;
    }

    try {
      // Create a unique file name
      final uuid = const Uuid().v4();
      final directory = await getTemporaryDirectory();
      final path = '${directory.path}/voice_recording_$uuid.m4a';

      // Start recording
      await _recorder.start(
        const RecordConfig(
          encoder: AudioEncoder.aacLc,
          bitRate: 128000,
          sampleRate: 44100,
        ),
        path: path,
      );

      _currentRecordingPath = path;

      // Start listening to amplitude
      _startAmplitudeListener();

      _loggingService.info(
          'VoiceRecordingService', 'Started recording at: $path');

      return path;
    } catch (e) {
      final errorMsg = 'Error starting recording: $e';
      debugPrint(errorMsg);
      _errorController.add(errorMsg);
      _loggingService.error('VoiceRecordingService', errorMsg);
      return null;
    }
  }

  /// Stop recording
  Future<String?> stopRecording() async {
    // Check if recording
    final isRecording = await _recorder.isRecording();
    if (!isRecording) {
      const infoMsg = 'Not recording';
      debugPrint(infoMsg);
      _loggingService.info('VoiceRecordingService', infoMsg);
      return null;
    }

    try {
      // Stop amplitude listener
      await _stopAmplitudeListener();

      // Stop recording
      final path = await _recorder.stop();

      _loggingService.info(
          'VoiceRecordingService', 'Stopped recording at: $path');

      return path;
    } catch (e) {
      final errorMsg = 'Error stopping recording: $e';
      debugPrint(errorMsg);
      _errorController.add(errorMsg);
      _loggingService.error('VoiceRecordingService', errorMsg);
      return null;
    }
  }

  /// Cancel recording
  Future<void> cancelRecording() async {
    // Check if recording
    final isRecording = await _recorder.isRecording();
    if (!isRecording) {
      const infoMsg = 'Not recording';
      debugPrint(infoMsg);
      _loggingService.info('VoiceRecordingService', infoMsg);
      return;
    }

    try {
      // Stop amplitude listener
      await _stopAmplitudeListener();

      // Cancel recording (this also deletes the file)
      await _recorder.cancel();

      _currentRecordingPath = null;
      _loggingService.info('VoiceRecordingService', 'Canceled recording');
    } catch (e) {
      final errorMsg = 'Error canceling recording: $e';
      debugPrint(errorMsg);
      _errorController.add(errorMsg);
      _loggingService.error('VoiceRecordingService', errorMsg);
    }
  }

  /// Start listening to amplitude
  void _startAmplitudeListener() {
    _amplitudeSubscription = _recorder
        .onAmplitudeChanged(const Duration(milliseconds: 100))
        .listen((amplitude) {
      // Convert to a value between 0 and 1
      final normalizedValue = (amplitude.current + 60) / 60;
      _amplitudeStreamController.add(normalizedValue.clamp(0.0, 1.0));
    });
  }

  /// Stop listening to amplitude
  Future<void> _stopAmplitudeListener() async {
    await _amplitudeSubscription?.cancel();
    _amplitudeSubscription = null;
  }

  /// Get the amplitude stream
  Stream<double> get amplitudeStream => _amplitudeStreamController.stream;

  /// Check if recording is in progress
  Future<bool> get isRecording => _recorder.isRecording();

  /// Dispose the service
  Future<void> dispose() async {
    await _connectivitySubscription?.cancel();
    await _stopAmplitudeListener();
    await _amplitudeStreamController.close();
    await _errorController.close();

    // Check if recording and cancel if needed
    final isRecording = await _recorder.isRecording();
    if (isRecording) {
      await cancelRecording();
    }

    // Dispose the recorder
    await _recorder.dispose();

    _loggingService.info(
        'VoiceRecordingService', 'Voice recording service disposed');
  }
}

/// Provider for the voice recording service
final voiceRecordingServiceProvider = Provider<VoiceRecordingService>((ref) {
  final loggingService = LoggingService();
  final connectivity = Connectivity();
  final service = VoiceRecordingService(loggingService, connectivity);

  ref.onDispose(() {
    service.dispose();
  });

  return service;
});
