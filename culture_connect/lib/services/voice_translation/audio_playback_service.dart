import 'dart:async';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:just_audio/just_audio.dart';
import 'package:path_provider/path_provider.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:culture_connect/services/logging_service.dart';

/// A service for playing audio files
class AudioPlaybackService {
  /// The audio player instance
  final AudioPlayer _player = AudioPlayer();

  /// Whether the service is initialized
  bool _isInitialized = false;

  /// The current audio path
  String? _currentAudioPath;

  /// The position stream subscription
  StreamSubscription<Duration>? _positionSubscription;

  /// The connectivity service
  final Connectivity _connectivity;

  /// The logging service
  final LoggingService _loggingService;

  /// Whether the service is currently online
  bool _isOnline = true;

  /// Subscription to connectivity changes
  StreamSubscription<ConnectivityResult>? _connectivitySubscription;

  /// The position stream controller
  final StreamController<Duration> _positionStreamController =
      StreamController<Duration>.broadcast();

  /// The error stream controller
  final StreamController<String> _errorStreamController =
      StreamController<String>.broadcast();

  /// The playback state stream controller
  final StreamController<AudioPlaybackState> _stateStreamController =
      StreamController<AudioPlaybackState>.broadcast();

  /// Creates a new audio playback service
  AudioPlaybackService(this._connectivity, this._loggingService) {
    _initConnectivity();
  }

  /// Initialize connectivity monitoring
  void _initConnectivity() {
    _connectivitySubscription =
        _connectivity.onConnectivityChanged.listen(_updateConnectionStatus);
    // Check initial connection state
    _checkConnectivity();
  }

  /// Check current connectivity
  Future<void> _checkConnectivity() async {
    try {
      final result = await _connectivity.checkConnectivity();
      _updateConnectionStatus(result);
    } on Exception catch (e) {
      debugPrint('Error checking connectivity: $e');
      _loggingService.error(
          'AudioPlaybackService', 'Error checking connectivity: $e');
      _isOnline = false;
    }
  }

  /// Update connection status based on connectivity result
  void _updateConnectionStatus(ConnectivityResult result) {
    _isOnline = result != ConnectivityResult.none;
    _loggingService.info('AudioPlaybackService',
        'Connectivity changed: ${_isOnline ? 'Online' : 'Offline'}');
  }

  /// Whether the service is currently online
  bool get isOnline => _isOnline;

  /// The error stream
  Stream<String> get errorStream => _errorStreamController.stream;

  /// Initialize the audio playback service
  Future<bool> initialize() async {
    if (_isInitialized) return true;

    try {
      // Set up position updates
      _positionSubscription = _player.positionStream.listen((position) {
        _positionStreamController.add(position);
      });

      // Set up player state updates
      _player.playerStateStream.listen((state) {
        if (state.processingState == ProcessingState.completed) {
          _stateStreamController.add(AudioPlaybackState.stopped);
        } else if (state.processingState == ProcessingState.ready &&
            !state.playing) {
          _stateStreamController.add(AudioPlaybackState.paused);
        } else if (state.playing) {
          _stateStreamController.add(AudioPlaybackState.playing);
        } else if (state.processingState == ProcessingState.buffering) {
          // Handle buffering state
          _loggingService.info('AudioPlaybackService', 'Audio buffering');
        } else if (state.processingState == ProcessingState.loading) {
          // Handle loading state
          _loggingService.info('AudioPlaybackService', 'Audio loading');
        } else {
          // Handle any other error states
          const errorMsg = "Error processing audio";
          _errorStreamController.add(errorMsg);
          _loggingService.error('AudioPlaybackService', errorMsg);
          _stateStreamController.add(AudioPlaybackState.error);
        }
      });

      _isInitialized = true;
      _stateStreamController.add(AudioPlaybackState.initial);
      _loggingService.info(
          'AudioPlaybackService', 'Audio playback service initialized');
      return true;
    } catch (e) {
      final errorMsg = 'Error initializing audio playback service: $e';
      debugPrint(errorMsg);
      _errorStreamController.add(errorMsg);
      _loggingService.error('AudioPlaybackService', errorMsg);
      return false;
    }
  }

  /// Load audio from a file path
  Future<Duration?> loadAudio(String filePath) async {
    if (!_isInitialized) {
      final initialized = await initialize();
      if (!initialized) return null;
    }

    try {
      // Check if file exists
      final file = File(filePath);
      if (!await file.exists()) {
        final error = "Audio file not found: $filePath";
        debugPrint(error);
        _errorStreamController.add(error);
        _loggingService.error('AudioPlaybackService', error);
        _stateStreamController.add(AudioPlaybackState.error);
        return null;
      }

      // Stop any current playback
      await _player.stop();

      // Load the audio file
      final duration = await _player.setFilePath(filePath);
      _currentAudioPath = filePath;

      _stateStreamController.add(AudioPlaybackState.loaded);
      _loggingService.info('AudioPlaybackService', 'Audio loaded: $filePath');
      return duration;
    } catch (e) {
      final errorMsg = 'Error loading audio: $e';
      debugPrint(errorMsg);
      _errorStreamController.add(errorMsg);
      _loggingService.error('AudioPlaybackService', errorMsg);
      _stateStreamController.add(AudioPlaybackState.error);
      return null;
    }
  }

  /// Play the loaded audio
  Future<bool> play() async {
    if (!_isInitialized) {
      final initialized = await initialize();
      if (!initialized) return false;
    }

    if (_currentAudioPath == null) {
      const error = 'No audio loaded';
      debugPrint(error);
      _errorStreamController.add(error);
      return false;
    }

    try {
      await _player.play();
      _stateStreamController.add(AudioPlaybackState.playing);
      return true;
    } catch (e) {
      debugPrint('Error playing audio: $e');
      _errorStreamController.add('Error playing audio: $e');
      _stateStreamController.add(AudioPlaybackState.error);
      return false;
    }
  }

  /// Pause the playback
  Future<bool> pause() async {
    if (!_isInitialized || _currentAudioPath == null) return false;

    try {
      await _player.pause();
      _stateStreamController.add(AudioPlaybackState.paused);
      return true;
    } catch (e) {
      debugPrint('Error pausing audio: $e');
      _errorStreamController.add('Error pausing audio: $e');
      return false;
    }
  }

  /// Stop the playback
  Future<bool> stop() async {
    if (!_isInitialized || _currentAudioPath == null) return false;

    try {
      await _player.stop();
      _stateStreamController.add(AudioPlaybackState.stopped);
      return true;
    } catch (e) {
      debugPrint('Error stopping audio: $e');
      _errorStreamController.add('Error stopping audio: $e');
      return false;
    }
  }

  /// Seek to a specific position
  Future<bool> seekTo(Duration position) async {
    if (!_isInitialized || _currentAudioPath == null) return false;

    try {
      if (_player.duration == null) {
        _errorStreamController.add("Cannot seek: Duration unknown");
        return false;
      }

      if (position > (_player.duration ?? Duration.zero)) {
        position = _player.duration!;
      }

      await _player.seek(position);
      return true;
    } catch (e) {
      debugPrint('Error seeking audio: $e');
      _errorStreamController.add('Error seeking audio: $e');
      return false;
    }
  }

  /// Set the volume
  Future<bool> setVolume(double volume) async {
    if (!_isInitialized) return false;

    try {
      // Ensure volume is between 0.0 and 1.0
      volume = volume.clamp(0.0, 1.0);
      await _player.setVolume(volume);
      return true;
    } catch (e) {
      debugPrint('Error setting volume: $e');
      _errorStreamController.add('Error setting volume: $e');
      return false;
    }
  }

  /// Set the playback speed
  Future<bool> setSpeed(double speed) async {
    if (!_isInitialized) return false;

    try {
      // Ensure speed is between 0.5 and 2.0
      speed = speed.clamp(0.5, 2.0);
      await _player.setSpeed(speed);
      return true;
    } catch (e) {
      debugPrint('Error setting speed: $e');
      _errorStreamController.add('Error setting speed: $e');
      return false;
    }
  }

  /// Create a temporary audio file from raw data
  Future<String?> createTempAudioFile(
      List<int> audioData, String extension) async {
    try {
      final tempDir = await getTemporaryDirectory();
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final path = '${tempDir.path}/audio_$timestamp.$extension';

      final file = File(path);
      await file.writeAsBytes(audioData);

      return path;
    } catch (e) {
      debugPrint('Error creating temp audio file: $e');
      _errorStreamController.add('Error creating temp audio file: $e');
      return null;
    }
  }

  /// Get the current position
  Duration get position => _player.position;

  /// Get the total duration
  Duration? get duration => _player.duration;

  /// Check if audio is playing
  bool get isPlaying => _player.playing;

  /// Get the current audio path
  String? get currentAudioPath => _currentAudioPath;

  /// Get the current playback state
  AudioPlaybackState get currentState {
    if (_player.playing) {
      return AudioPlaybackState.playing;
    } else if (_player.processingState == ProcessingState.idle) {
      return AudioPlaybackState.initial;
    } else if (_player.processingState == ProcessingState.completed) {
      return AudioPlaybackState.stopped;
    } else if (_currentAudioPath != null) {
      return AudioPlaybackState.paused;
    } else {
      return AudioPlaybackState.initial;
    }
  }

  /// Get the position stream
  Stream<Duration> get positionStream => _positionStreamController.stream;

  /// Get the just_audio player state stream
  Stream<PlayerState> get playerStateStream => _player.playerStateStream;

  /// Get the audio playback state stream
  Stream<AudioPlaybackState> get stateStream => _stateStreamController.stream;

  // Error stream is already defined above

  /// Dispose the service
  Future<void> dispose() async {
    await _connectivitySubscription?.cancel();
    await _positionSubscription?.cancel();
    await _positionStreamController.close();
    await _stateStreamController.close();
    await _errorStreamController.close();
    await _player.dispose();
    _loggingService.info(
        'AudioPlaybackService', 'Audio playback service disposed');
  }
}

/// The audio playback state
enum AudioPlaybackState {
  /// Initial state
  initial,

  /// Audio is loaded
  loaded,

  /// Audio is playing
  playing,

  /// Audio is paused
  paused,

  /// Audio is stopped
  stopped,

  /// Error state
  error,
}

/// Provider for the audio playback service
final audioPlaybackServiceProvider = Provider<AudioPlaybackService>((ref) {
  final connectivity = Connectivity();
  final loggingService = LoggingService();
  final service = AudioPlaybackService(connectivity, loggingService);

  ref.onDispose(() {
    service.dispose();
  });

  return service;
});
