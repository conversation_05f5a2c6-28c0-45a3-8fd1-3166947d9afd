import 'dart:async';
import 'dart:io';
import 'dart:math';

import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:path_provider/path_provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:http/http.dart' as http;
import 'package:path/path.dart' as path;
import 'package:uuid/uuid.dart';
import 'package:connectivity_plus/connectivity_plus.dart';

import 'package:culture_connect/models/translation/language_model.dart';
import 'package:culture_connect/models/translation/language_pack_model.dart';
import 'package:culture_connect/providers/shared_preferences_provider.dart';
import 'package:culture_connect/services/voice_translation/language_pack_service.dart';
import 'package:culture_connect/services/voice_translation/offline_translation_manager.dart';
import 'package:culture_connect/services/logging_service.dart';

/// A service for handling offline translations
class OfflineTranslationService {
  /// The language pack service
  final LanguagePackService _languagePackService;

  /// The offline translation manager
  final OfflineTranslationManager _offlineTranslationManager;

  /// The logging service
  final LoggingService _loggingService;

  /// The shared preferences instance (used by connectivity methods)
  final SharedPreferences _prefs;

  /// The connectivity service
  final Connectivity _connectivity;

  /// Whether the service is currently online
  bool _isOnline = true;

  /// Subscription to connectivity changes
  StreamSubscription<ConnectivityResult>? _connectivitySubscription;

  /// The translation events controller
  final StreamController<String> _translationEventsController =
      StreamController<String>.broadcast();

  /// The error stream controller
  final StreamController<String> _errorController =
      StreamController<String>.broadcast();

  /// Creates a new offline translation service
  OfflineTranslationService(
    this._languagePackService,
    this._offlineTranslationManager,
    this._loggingService,
    this._prefs,
    this._connectivity,
  ) {
    _initConnectivity();
  }

  /// Stream of translation events
  Stream<String> get translationEvents => _translationEventsController.stream;

  /// Stream of error events
  Stream<String> get errorStream => _errorController.stream;

  /// Whether the service is currently online
  bool get isOnline => _isOnline;

  /// Initialize connectivity monitoring
  void _initConnectivity() {
    _connectivitySubscription =
        _connectivity.onConnectivityChanged.listen(_updateConnectionStatus);
    // Check initial connection state
    _checkConnectivity();
  }

  /// Check current connectivity
  Future<void> _checkConnectivity() async {
    try {
      final result = await _connectivity.checkConnectivity();
      _updateConnectionStatus(result);
    } on Exception catch (e) {
      debugPrint('Error checking connectivity: $e');
      _loggingService.error(
          'OfflineTranslationService', 'Error checking connectivity: $e');
      _isOnline = false;
    }
  }

  /// Update connection status based on connectivity result
  void _updateConnectionStatus(ConnectivityResult result) {
    _isOnline = result != ConnectivityResult.none;
    _loggingService.info('OfflineTranslationService',
        'Connectivity changed: ${_isOnline ? 'Online' : 'Offline'}');
  }

  /// Translate text using offline models
  Future<String> translateText(
    String text,
    String sourceLanguageCode,
    String targetLanguageCode, {
    String? sourceDialect,
    String? targetDialect,
  }) async {
    try {
      // Check if offline translation is available for this language pair
      final isAvailable =
          await _offlineTranslationManager.isOfflineTranslationAvailable(
        sourceLanguageCode,
        targetLanguageCode,
      );

      if (!isAvailable) {
        throw Exception(
            'Offline translation not available for this language pair');
      }

      // Check if we have a cached translation
      final cachedTranslation = _offlineTranslationManager.getCachedTranslation(
        text,
        sourceLanguageCode,
        targetLanguageCode,
      );

      if (cachedTranslation != null) {
        _loggingService.debug(
            'OfflineTranslationService', 'Using cached translation');
        _translationEventsController.add('Using cached translation');
        return cachedTranslation;
      }

      // Get the language packs
      final sourcePack =
          _languagePackService.getLanguagePack(sourceLanguageCode);
      final targetPack =
          _languagePackService.getLanguagePack(targetLanguageCode);

      if (sourcePack == null || targetPack == null) {
        throw Exception('Language packs not found');
      }

      // In a real app, this would use the local ML model to translate
      // For now, we'll simulate translation with a mock implementation

      // Simulate processing time
      await Future.delayed(const Duration(milliseconds: 500));

      // Get the target language
      final targetLanguage = supportedLanguages.firstWhere(
        (lang) => lang.code == targetLanguageCode,
        orElse: () => const LanguageModel(
          code: 'unknown',
          name: 'Unknown',
          flag: '🏳️',
        ),
      );

      // Mock translation based on language codes
      String translatedText = '';

      // Simple mock translations for demo purposes
      if (sourceLanguageCode == 'en' && targetLanguageCode == 'fr') {
        // English to French
        if (text.toLowerCase().contains('hello')) {
          translatedText = 'Bonjour';
        } else if (text.toLowerCase().contains('goodbye')) {
          translatedText = 'Au revoir';
        } else if (text.toLowerCase().contains('thank you')) {
          translatedText = 'Merci beaucoup';
        } else {
          // For other text, just add a French-looking prefix
          translatedText = 'Le ${text.toLowerCase()}';
        }
      } else if (sourceLanguageCode == 'en' && targetLanguageCode == 'es') {
        // English to Spanish
        if (text.toLowerCase().contains('hello')) {
          translatedText = 'Hola';
        } else if (text.toLowerCase().contains('goodbye')) {
          translatedText = 'Adiós';
        } else if (text.toLowerCase().contains('thank you')) {
          translatedText = 'Muchas gracias';
        } else {
          // For other text, just add a Spanish-looking prefix
          translatedText = 'El ${text.toLowerCase()}';
        }
      } else {
        // For other language pairs, just append the target language name
        translatedText = '[$targetLanguage.name] $text';
      }

      // Add to cache
      await _offlineTranslationManager.addToCache(
        text,
        translatedText,
        sourceLanguageCode,
        targetLanguageCode,
      );

      // In a real implementation, we would update last used timestamp for language packs
      // _languagePackService.updateLastUsed(sourceLanguageCode);
      // _languagePackService.updateLastUsed(targetLanguageCode);

      _translationEventsController.add('Translated offline');
      return translatedText;
    } catch (e) {
      _loggingService.error(
          'OfflineTranslationService', 'Error translating text: $e');
      throw Exception('Offline translation failed: $e');
    }
  }

  /// Convert speech to text using offline models
  Future<String> speechToText(
    String audioPath,
    String languageCode, {
    String? dialect,
  }) async {
    try {
      // Check if offline translation is available for this language
      final languagePack = _languagePackService.getLanguagePack(languageCode);

      if (languagePack == null ||
          languagePack.status != LanguagePackStatus.downloaded) {
        throw Exception(
            'Offline speech recognition not available for this language');
      }

      // In a real app, this would use the local ML model to recognize speech
      // For now, we'll simulate speech recognition with a mock implementation

      // Simulate processing time
      await Future.delayed(const Duration(milliseconds: 700));

      // Mock recognized text based on language code
      String recognizedText = '';

      // Simple mock recognitions for demo purposes
      if (languageCode == 'en') {
        // English
        recognizedText = 'Hello, this is a test of offline speech recognition.';
      } else if (languageCode == 'fr') {
        // French
        recognizedText =
            'Bonjour, ceci est un test de reconnaissance vocale hors ligne.';
      } else if (languageCode == 'es') {
        // Spanish
        recognizedText =
            'Hola, esta es una prueba de reconocimiento de voz sin conexión.';
      } else {
        // For other languages, just use a generic message
        recognizedText =
            'This is a test message in ${languageCode.toUpperCase()}.';
      }

      // In a real implementation, we would update last used timestamp for language pack
      // _languagePackService.updateLastUsed(languageCode);

      _translationEventsController.add('Speech recognized offline');
      return recognizedText;
    } catch (e) {
      _loggingService.error(
          'OfflineTranslationService', 'Error recognizing speech: $e');
      throw Exception('Offline speech recognition failed: $e');
    }
  }

  /// Convert text to speech using offline models
  Future<String> textToSpeech(
    String text,
    String languageCode, {
    String? dialect,
  }) async {
    try {
      // Check if offline translation is available for this language
      final languagePack = _languagePackService.getLanguagePack(languageCode);

      if (languagePack == null ||
          languagePack.status != LanguagePackStatus.downloaded) {
        throw Exception(
            'Offline text-to-speech not available for this language');
      }

      // In a real app, this would use the local ML model to generate speech
      // For now, we'll simulate text-to-speech with a mock implementation

      // Simulate processing time
      await Future.delayed(const Duration(milliseconds: 600));

      // Create a mock audio file
      final directory = await getTemporaryDirectory();
      final path = '${directory.path}/offline_tts_${const Uuid().v4()}.m4a';

      // Create an empty file (in a real app, this would contain the generated audio)
      final file = File(path);
      await file.writeAsString('Mock TTS data for: $text in $languageCode');

      // In a real implementation, we would update last used timestamp for language pack
      // _languagePackService.updateLastUsed(languageCode);

      _translationEventsController.add('Speech generated offline');
      return path;
    } catch (e) {
      _loggingService.error(
          'OfflineTranslationService', 'Error generating speech: $e');
      throw Exception('Offline text-to-speech failed: $e');
    }
  }

  /// Calculate confidence score for an offline translation
  double calculateConfidenceScore(
    String sourceText,
    String translatedText,
    String sourceLanguageCode,
    String targetLanguageCode,
  ) {
    // In a real app, this would use the local ML model to calculate confidence
    // For now, we'll use a simple heuristic based on text length

    // Simple confidence calculation based on text length ratio
    final sourceLength = sourceText.length;
    final targetLength = translatedText.length;

    // Most languages have different average word lengths
    // This is a very simplified approach
    double expectedRatio = 1.0;

    if (sourceLanguageCode == 'en' && targetLanguageCode == 'fr') {
      // English to French (French tends to be ~20% longer)
      expectedRatio = 1.2;
    } else if (sourceLanguageCode == 'en' && targetLanguageCode == 'de') {
      // English to German (German tends to be ~30% longer)
      expectedRatio = 1.3;
    } else if (sourceLanguageCode == 'en' && targetLanguageCode == 'ja') {
      // English to Japanese (Japanese tends to be ~50% shorter in character count)
      expectedRatio = 0.5;
    }

    // Calculate actual ratio
    final actualRatio = targetLength / max(sourceLength, 1);

    // Calculate how close the actual ratio is to the expected ratio
    final ratioDifference = (actualRatio - expectedRatio).abs();

    // Convert to a confidence score (0.0 to 1.0)
    // If the difference is 0, confidence is 1.0
    // If the difference is 0.5 or more, confidence is 0.5
    double confidence = max(0.5, 1.0 - ratioDifference);

    // Add some randomness for demo purposes
    confidence =
        min(1.0, max(0.5, confidence + (Random().nextDouble() - 0.5) * 0.2));

    return confidence;
  }

  /// Dispose resources
  void dispose() {
    _connectivitySubscription?.cancel();
    _translationEventsController.close();
    _errorController.close();
  }
}

/// Provider for the offline translation service
final offlineTranslationServiceProvider =
    Provider<OfflineTranslationService>((ref) {
  final languagePackService = ref.watch(languagePackServiceProvider);
  final offlineTranslationManager =
      ref.watch(offlineTranslationManagerProvider);
  final loggingService = LoggingService();
  final prefs = ref.watch(sharedPreferencesProvider);
  final connectivity = Connectivity();

  final service = OfflineTranslationService(
    languagePackService,
    offlineTranslationManager,
    loggingService,
    prefs,
    connectivity,
  );

  ref.onDispose(() {
    service.dispose();
  });

  return service;
});
