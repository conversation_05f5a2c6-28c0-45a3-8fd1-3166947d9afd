import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:path_provider/path_provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:http/http.dart' as http;
import 'package:path/path.dart' as path;
import 'package:connectivity_plus/connectivity_plus.dart';

import 'package:culture_connect/models/translation/language_pack_model.dart';
import 'package:culture_connect/models/offline/offline_settings.dart';
import 'package:culture_connect/providers/shared_preferences_provider.dart';
import 'package:culture_connect/providers/offline_settings_provider.dart';
import 'package:culture_connect/services/voice_translation/language_pack_service.dart';
import 'package:culture_connect/services/logging_service.dart';

/// A service for managing offline translation capabilities
class OfflineTranslationManager {
  /// The API key for the translation service (not used directly in this class)
  final String _apiKey;

  /// The HTTP client
  final http.Client _client;

  /// The shared preferences instance
  final SharedPreferences _prefs;

  /// The language pack service
  final LanguagePackService _languagePackService;

  /// The logging service
  final LoggingService _loggingService;

  /// The offline settings
  final OfflineSettings _offlineSettings;

  /// The connectivity service
  final Connectivity _connectivity;

  /// Whether the service is currently online
  bool _isOnline = true;

  /// Subscription to connectivity changes
  StreamSubscription<ConnectivityResult>? _connectivitySubscription;

  /// The translation cache
  final Map<String, String> _translationCache = {};

  /// The translation cache controller
  final StreamController<Map<String, String>> _translationCacheController =
      StreamController<Map<String, String>>.broadcast();

  /// The error stream controller
  final StreamController<String> _errorController =
      StreamController<String>.broadcast();

  /// The translation cache stream
  Stream<Map<String, String>> get translationCache =>
      _translationCacheController.stream;

  /// The error stream
  Stream<String> get errorStream => _errorController.stream;

  /// Whether the service is currently online
  bool get isOnline => _isOnline;

  /// Creates a new offline translation manager
  OfflineTranslationManager(
    this._apiKey,
    this._client,
    this._prefs,
    this._languagePackService,
    this._loggingService,
    this._offlineSettings,
    this._connectivity,
  ) {
    _loadTranslationCache();
    _initConnectivity();
  }

  /// Initialize connectivity monitoring
  void _initConnectivity() {
    _connectivitySubscription =
        _connectivity.onConnectivityChanged.listen(_updateConnectionStatus);
    // Check initial connection state
    _checkConnectivity();
  }

  /// Check current connectivity
  Future<void> _checkConnectivity() async {
    try {
      final result = await _connectivity.checkConnectivity();
      _updateConnectionStatus(result);
    } on Exception catch (e) {
      debugPrint('Error checking connectivity: $e');
      _loggingService.error(
          'OfflineTranslationManager', 'Error checking connectivity: $e');
      _isOnline = false;
    }
  }

  /// Update connection status based on connectivity result
  void _updateConnectionStatus(ConnectivityResult result) {
    _isOnline = result != ConnectivityResult.none;
    _loggingService.info('OfflineTranslationManager',
        'Connectivity changed: ${_isOnline ? 'Online' : 'Offline'}');
  }

  /// Load the translation cache from shared preferences
  Future<void> _loadTranslationCache() async {
    try {
      final cacheJson = _prefs.getString('translation_cache');
      if (cacheJson != null) {
        final Map<String, dynamic> decoded = jsonDecode(cacheJson);
        _translationCache.clear();
        decoded.forEach((key, value) {
          _translationCache[key] = value.toString();
        });

        // Notify listeners
        _translationCacheController.add(_translationCache);
      }
    } catch (e) {
      _loggingService.error(
          'OfflineTranslationManager', 'Error loading translation cache: $e');
    }
  }

  /// Save the translation cache to shared preferences
  Future<void> _saveTranslationCache() async {
    try {
      // Limit cache size based on settings
      if (_translationCache.length > 1000) {
        // Remove oldest entries if cache is too large
        final sortedEntries = _translationCache.entries.toList()
          ..sort((a, b) => a.key.compareTo(b.key));

        while (_translationCache.length > 1000) {
          final oldestKey = sortedEntries.removeAt(0).key;
          _translationCache.remove(oldestKey);
        }
      }

      final cacheJson = jsonEncode(_translationCache);
      await _prefs.setString('translation_cache', cacheJson);
    } catch (e) {
      _loggingService.error(
          'OfflineTranslationManager', 'Error saving translation cache: $e');
    }
  }

  /// Check if offline translation is available for a language pair
  Future<bool> isOfflineTranslationAvailable(
    String sourceLanguageCode,
    String targetLanguageCode,
  ) async {
    try {
      // Check if both source and target language packs are downloaded
      final sourcePack =
          _languagePackService.getLanguagePack(sourceLanguageCode);
      final targetPack =
          _languagePackService.getLanguagePack(targetLanguageCode);

      return sourcePack?.status == LanguagePackStatus.downloaded &&
          targetPack?.status == LanguagePackStatus.downloaded;
    } catch (e) {
      _loggingService.error('OfflineTranslationManager',
          'Error checking offline translation availability: $e');
      _errorController
          .add('Error checking offline translation availability: $e');
      return false;
    }
  }

  /// Get the cached translation for a text
  String? getCachedTranslation(
    String text,
    String sourceLanguageCode,
    String targetLanguageCode,
  ) {
    final key = _getCacheKey(text, sourceLanguageCode, targetLanguageCode);
    return _translationCache[key];
  }

  /// Add a translation to the cache
  Future<void> addToCache(
    String text,
    String translation,
    String sourceLanguageCode,
    String targetLanguageCode,
  ) async {
    final key = _getCacheKey(text, sourceLanguageCode, targetLanguageCode);
    _translationCache[key] = translation;

    // Notify listeners
    _translationCacheController.add(_translationCache);

    // Save to shared preferences
    await _saveTranslationCache();
  }

  /// Get the cache key for a translation
  String _getCacheKey(
    String text,
    String sourceLanguageCode,
    String targetLanguageCode,
  ) {
    return '$sourceLanguageCode:$targetLanguageCode:${text.toLowerCase()}';
  }

  /// Clear the translation cache
  Future<void> clearCache() async {
    _translationCache.clear();

    // Notify listeners
    _translationCacheController.add(_translationCache);

    // Save to shared preferences
    await _prefs.remove('translation_cache');
  }

  /// Get the total size of all downloaded language packs
  Future<int> getTotalLanguagePackSize() async {
    final languagePacks = _languagePackService.languagePacks;
    final downloadedPacks = languagePacks
        .where((pack) => pack.status == LanguagePackStatus.downloaded)
        .toList();

    int totalSize = 0;
    for (final pack in downloadedPacks) {
      totalSize += (pack.sizeMB * 1024 * 1024).round(); // Convert MB to bytes
    }

    return totalSize;
  }

  /// Check if there's enough storage space for a new language pack
  Future<bool> hasEnoughStorageSpace(double sizeMB) async {
    // Get the total size of all downloaded language packs
    final totalSize = await getTotalLanguagePackSize();

    // Check if adding the new pack would exceed the max storage space
    final newTotalSize = totalSize + (sizeMB * 1024 * 1024).round();
    return newTotalSize <= _offlineSettings.maxStorageSpace;
  }

  /// Clean up unused language packs
  Future<void> cleanupUnusedLanguagePacks() async {
    if (!_offlineSettings.autoCleanupExpiredContent) {
      return;
    }

    final languagePacks = _languagePackService.languagePacks;
    final downloadedPacks = languagePacks
        .where((pack) => pack.status == LanguagePackStatus.downloaded)
        .toList();

    // Sort by last used date (oldest first)
    downloadedPacks.sort((a, b) => a.lastUsedAt.compareTo(b.lastUsedAt));

    // Get the total size
    final totalSize = await getTotalLanguagePackSize();

    // If we're below the threshold, no need to clean up
    if (totalSize < _offlineSettings.storageThresholdForCleanup) {
      return;
    }

    // Remove oldest packs until we're below the threshold
    for (final pack in downloadedPacks) {
      // Don't remove primary language pack
      if (pack.isPrimary) {
        continue;
      }

      // Remove the pack
      await _languagePackService.deleteLanguagePack(pack.languageCode);

      // Check if we're now below the threshold
      final newTotalSize = await getTotalLanguagePackSize();
      if (newTotalSize < _offlineSettings.storageThresholdForCleanup) {
        break;
      }
    }
  }

  /// Dispose resources
  void dispose() {
    _connectivitySubscription?.cancel();
    _translationCacheController.close();
    _errorController.close();
    _client.close();
  }
}

/// Provider for the offline translation manager
final offlineTranslationManagerProvider =
    Provider<OfflineTranslationManager>((ref) {
  const apiKey =
      'mock_api_key'; // In a real app, this would come from environment variables
  final client = http.Client();
  final prefs = ref.watch(sharedPreferencesProvider);
  final languagePackService = ref.watch(languagePackServiceProvider);
  final loggingService = LoggingService();
  final offlineSettings = ref.watch(offlineSettingsProvider);
  final connectivity = Connectivity();

  final manager = OfflineTranslationManager(
    apiKey,
    client,
    prefs,
    languagePackService,
    loggingService,
    offlineSettings,
    connectivity,
  );

  ref.onDispose(() {
    manager.dispose();
  });

  return manager;
});
