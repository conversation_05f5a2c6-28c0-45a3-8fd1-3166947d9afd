import 'dart:async';
import 'dart:math';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:culture_connect/models/translation/language_model.dart';
import 'package:culture_connect/providers/shared_preferences_provider.dart';

/// A service for detecting languages in text
class LanguageDetectionService {
  /// The shared preferences instance
  final SharedPreferences _prefs;
  
  /// The language detection events controller
  final StreamController<String> _detectionEventsController = 
      StreamController<String>.broadcast();
  
  /// Creates a new language detection service
  LanguageDetectionService(this._prefs);
  
  /// Detect the language of a text
  Future<String?> detectLanguage(String text, {double confidenceThreshold = 0.6}) async {
    try {
      if (text.isEmpty) {
        return null;
      }
      
      // In a real app, this would call a language detection API or use a local ML model
      // For demo purposes, we'll use a simple heuristic based on common words
      
      // Normalize the text
      final normalizedText = text.toLowerCase();
      
      // Calculate scores for each language
      final scores = <String, double>{};
      
      // English detection
      final englishScore = _calculateEnglishScore(normalizedText);
      if (englishScore > 0) {
        scores['en'] = englishScore;
      }
      
      // French detection
      final frenchScore = _calculateFrenchScore(normalizedText);
      if (frenchScore > 0) {
        scores['fr'] = frenchScore;
      }
      
      // Spanish detection
      final spanishScore = _calculateSpanishScore(normalizedText);
      if (spanishScore > 0) {
        scores['es'] = spanishScore;
      }
      
      // German detection
      final germanScore = _calculateGermanScore(normalizedText);
      if (germanScore > 0) {
        scores['de'] = germanScore;
      }
      
      // If no scores, return null
      if (scores.isEmpty) {
        return null;
      }
      
      // Get the language with the highest score
      final entries = scores.entries.toList()
        ..sort((a, b) => b.value.compareTo(a.value));
      
      final topLanguage = entries.first;
      
      // Check if the confidence is high enough
      if (topLanguage.value < confidenceThreshold) {
        return null;
      }
      
      _detectionEventsController.add('Detected language: ${topLanguage.key} (confidence: ${topLanguage.value.toStringAsFixed(2)})');
      
      return topLanguage.key;
    } catch (e) {
      debugPrint('Error detecting language: $e');
      return null;
    }
  }
  
  /// Calculate a confidence score for English
  double _calculateEnglishScore(String text) {
    final englishWords = [
      'the', 'and', 'is', 'in', 'it', 'you', 'that', 'was', 'for', 'on',
      'are', 'with', 'as', 'have', 'be', 'at', 'this', 'from', 'or', 'an',
      'would', 'what', 'will', 'there', 'which', 'they', 'can', 'all', 'their', 'if',
    ];
    
    return _calculateLanguageScore(text, englishWords);
  }
  
  /// Calculate a confidence score for French
  double _calculateFrenchScore(String text) {
    final frenchWords = [
      'le', 'la', 'les', 'un', 'une', 'des', 'et', 'est', 'en', 'dans',
      'pour', 'qui', 'que', 'pas', 'sur', 'ce', 'cette', 'ces', 'mais', 'ou',
      'avec', 'par', 'je', 'tu', 'il', 'elle', 'nous', 'vous', 'ils', 'elles',
    ];
    
    return _calculateLanguageScore(text, frenchWords);
  }
  
  /// Calculate a confidence score for Spanish
  double _calculateSpanishScore(String text) {
    final spanishWords = [
      'el', 'la', 'los', 'las', 'un', 'una', 'unos', 'unas', 'y', 'es',
      'en', 'de', 'para', 'por', 'con', 'sin', 'que', 'como', 'pero', 'o',
      'yo', 'tu', 'él', 'ella', 'nosotros', 'vosotros', 'ellos', 'ellas', 'mi', 'su',
    ];
    
    return _calculateLanguageScore(text, spanishWords);
  }
  
  /// Calculate a confidence score for German
  double _calculateGermanScore(String text) {
    final germanWords = [
      'der', 'die', 'das', 'ein', 'eine', 'und', 'ist', 'in', 'zu', 'den',
      'mit', 'für', 'auf', 'dem', 'sich', 'des', 'nicht', 'auch', 'von', 'bei',
      'ich', 'du', 'er', 'sie', 'es', 'wir', 'ihr', 'sie', 'mein', 'dein',
    ];
    
    return _calculateLanguageScore(text, germanWords);
  }
  
  /// Calculate a language score based on common words
  double _calculateLanguageScore(String text, List<String> commonWords) {
    final words = text.split(RegExp(r'\s+'));
    int matches = 0;
    
    for (final word in words) {
      final cleanWord = word.replaceAll(RegExp(r'[^\w]'), '');
      if (commonWords.contains(cleanWord)) {
        matches++;
      }
    }
    
    // Calculate score as a percentage of matched words
    return words.isEmpty ? 0 : matches / words.length;
  }
  
  /// Detect the dialect of a language
  Future<String?> detectDialect(String text, String languageCode) async {
    try {
      if (text.isEmpty) {
        return null;
      }
      
      // In a real app, this would use more sophisticated dialect detection
      // For demo purposes, we'll use a simple approach
      
      switch (languageCode) {
        case 'en':
          return _detectEnglishDialect(text);
        case 'fr':
          return _detectFrenchDialect(text);
        case 'es':
          return _detectSpanishDialect(text);
        default:
          return null;
      }
    } catch (e) {
      debugPrint('Error detecting dialect: $e');
      return null;
    }
  }
  
  /// Detect English dialect
  String? _detectEnglishDialect(String text) {
    final normalizedText = text.toLowerCase();
    
    // British English indicators
    final britishWords = ['colour', 'flavour', 'centre', 'theatre', 'realise', 'organise', 'lorry', 'flat', 'lift', 'autumn'];
    int britishCount = 0;
    
    // American English indicators
    final americanWords = ['color', 'flavor', 'center', 'theater', 'realize', 'organize', 'truck', 'apartment', 'elevator', 'fall'];
    int americanCount = 0;
    
    // Australian English indicators
    final australianWords = ['mate', 'g\'day', 'arvo', 'barbie', 'brekkie', 'footy', 'ute', 'thongs', 'servo', 'bottle-o'];
    int australianCount = 0;
    
    for (final word in britishWords) {
      if (normalizedText.contains(word)) {
        britishCount++;
      }
    }
    
    for (final word in americanWords) {
      if (normalizedText.contains(word)) {
        americanCount++;
      }
    }
    
    for (final word in australianWords) {
      if (normalizedText.contains(word)) {
        australianCount++;
      }
    }
    
    if (britishCount > americanCount && britishCount > australianCount) {
      return 'British';
    } else if (americanCount > britishCount && americanCount > australianCount) {
      return 'American';
    } else if (australianCount > britishCount && australianCount > americanCount) {
      return 'Australian';
    }
    
    // Default to American English if no clear dialect is detected
    return 'American';
  }
  
  /// Detect French dialect
  String? _detectFrenchDialect(String text) {
    final normalizedText = text.toLowerCase();
    
    // Canadian French indicators
    final canadianWords = ['char', 'magasiner', 'dépanneur', 'chum', 'blonde', 'fin de semaine', 'breuvage', 'tantôt', 'présentement', 'bienvenue'];
    int canadianCount = 0;
    
    // Standard French indicators
    final standardWords = ['voiture', 'faire des achats', 'épicerie', 'ami', 'petite amie', 'week-end', 'boisson', 'bientôt', 'actuellement', 'de rien'];
    int standardCount = 0;
    
    for (final word in canadianWords) {
      if (normalizedText.contains(word)) {
        canadianCount++;
      }
    }
    
    for (final word in standardWords) {
      if (normalizedText.contains(word)) {
        standardCount++;
      }
    }
    
    if (canadianCount > standardCount) {
      return 'Canadian';
    } else {
      return 'Standard';
    }
  }
  
  /// Detect Spanish dialect
  String? _detectSpanishDialect(String text) {
    final normalizedText = text.toLowerCase();
    
    // Mexican Spanish indicators
    final mexicanWords = ['chavo', 'chido', 'padre', 'órale', 'güey', 'chamba', 'chambear', 'mande', 'ahorita', 'neta'];
    int mexicanCount = 0;
    
    // Spain Spanish indicators
    final spainWords = ['chaval', 'guay', 'molar', 'vale', 'tío', 'curro', 'currar', 'diga', 'ahora mismo', 'verdad'];
    int spainCount = 0;
    
    for (final word in mexicanWords) {
      if (normalizedText.contains(word)) {
        mexicanCount++;
      }
    }
    
    for (final word in spainWords) {
      if (normalizedText.contains(word)) {
        spainCount++;
      }
    }
    
    if (mexicanCount > spainCount) {
      return 'Mexican';
    } else {
      return 'Spain';
    }
  }
  
  /// Get the language detection events stream
  Stream<String> get detectionEventsStream => _detectionEventsController.stream;
  
  /// Dispose the service
  Future<void> dispose() async {
    await _detectionEventsController.close();
  }
}

/// Provider for the language detection service
final languageDetectionServiceProvider = Provider<LanguageDetectionService>((ref) {
  final prefs = ref.watch(sharedPreferencesProvider);
  
  final service = LanguageDetectionService(prefs);
  
  ref.onDispose(() {
    service.dispose();
  });
  
  return service;
});
