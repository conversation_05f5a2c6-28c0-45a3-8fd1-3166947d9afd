import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:geolocator/geolocator.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart' as gmaps;
import 'package:shared_preferences/shared_preferences.dart';
import 'package:culture_connect/models/landmark.dart';
import 'package:culture_connect/models/location/lat_lng.dart';
import 'package:culture_connect/models/location/place_result.dart';
import 'package:culture_connect/services/logging_service.dart';
import 'package:culture_connect/services/error_handling_service.dart';

/// Location permission status
enum LocationPermissionStatus {
  /// Location permission has not been requested yet
  notDetermined,

  /// Location permission has been denied
  denied,

  /// Location permission has been denied permanently
  deniedPermanently,

  /// Location permission has been granted
  granted,

  /// Location services are disabled
  disabled,
}

class LocationService {
  final _locationController = StreamController<Position>.broadcast();
  final _permissionStatusController =
      StreamController<LocationPermissionStatus>.broadcast();
  final _locationUpdateController = StreamController<gmaps.LatLng>.broadcast();

  Stream<Position> get locationStream => _locationController.stream;
  Stream<LocationPermissionStatus> get permissionStatusStream =>
      _permissionStatusController.stream;
  Stream<gmaps.LatLng> get locationUpdateStream =>
      _locationUpdateController.stream;

  bool _isTracking = false;
  Timer? _locationTimer;
  LocationPermissionStatus _permissionStatus =
      LocationPermissionStatus.notDetermined;
  Position? _lastKnownPosition;

  // Cache for offline use
  final Map<String, dynamic> _locationCache = {};

  // For logging
  final LoggingService? _loggingService;
  final ErrorHandlingService? _errorHandlingService;

  LocationService(
      {LoggingService? loggingService,
      ErrorHandlingService? errorHandlingService})
      : _loggingService = loggingService,
        _errorHandlingService = errorHandlingService {
    _checkPermissionStatus();
  }

  /// Check the current permission status
  Future<void> _checkPermissionStatus() async {
    try {
      // Check if location services are enabled
      final serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        _updatePermissionStatus(LocationPermissionStatus.disabled);
        return;
      }

      // Check location permission
      final permission = await Geolocator.checkPermission();

      switch (permission) {
        case LocationPermission.denied:
          _updatePermissionStatus(LocationPermissionStatus.denied);
          break;
        case LocationPermission.deniedForever:
          _updatePermissionStatus(LocationPermissionStatus.deniedPermanently);
          break;
        case LocationPermission.whileInUse:
        case LocationPermission.always:
          _updatePermissionStatus(LocationPermissionStatus.granted);
          break;
        case LocationPermission.unableToDetermine:
          _updatePermissionStatus(LocationPermissionStatus.notDetermined);
          break;
      }
    } catch (e) {
      _logError('Error checking permission status', e);
    }
  }

  /// Update permission status
  void _updatePermissionStatus(LocationPermissionStatus status) {
    _permissionStatus = status;
    _permissionStatusController.add(status);
    _log('Permission status updated: ${status.name}');
  }

  /// Log a message
  void _log(String message) {
    if (_loggingService != null) {
      _loggingService!.debug('LocationService', message);
    } else {
      debugPrint('LocationService: $message');
    }
  }

  /// Log an error
  void _logError(String message, dynamic error, [StackTrace? stackTrace]) {
    if (_loggingService != null) {
      _loggingService!.error('LocationService', message, error, stackTrace);

      if (_errorHandlingService != null) {
        _errorHandlingService!.handleError(
          error: error,
          context: 'LocationService.$message',
          stackTrace: stackTrace ?? StackTrace.current,
          type: ErrorType.location,
          severity: ErrorSeverity.medium,
        );
      }
    } else {
      debugPrint('LocationService ERROR: $message - $error');
      if (stackTrace != null) {
        debugPrint(stackTrace.toString());
      }
    }
  }

  /// Start tracking the user's location
  Future<bool> startLocationTracking() async {
    if (_isTracking) return true;

    try {
      // Check if location services are enabled
      final serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        _updatePermissionStatus(LocationPermissionStatus.disabled);
        _log('Location services are disabled');
        return false;
      }

      // Check location permission
      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        _updatePermissionStatus(LocationPermissionStatus.denied);

        // Request permission
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          _log('Location permission denied');
          return false;
        }
      }

      if (permission == LocationPermission.deniedForever) {
        _updatePermissionStatus(LocationPermissionStatus.deniedPermanently);
        _log('Location permission permanently denied');
        return false;
      }

      // Permission granted
      _updatePermissionStatus(LocationPermissionStatus.granted);

      // Start location tracking
      _isTracking = true;

      // Use position stream instead of timer for more accurate tracking
      final positionStream = Geolocator.getPositionStream(
        locationSettings: const LocationSettings(
          accuracy: LocationAccuracy.high,
          distanceFilter: 10, // Update every 10 meters
        ),
      );

      positionStream.listen(
        (Position position) {
          _locationController.add(position);
          _lastKnownPosition = position;
          _locationUpdateController
              .add(gmaps.LatLng(position.latitude, position.longitude));

          // Cache the position for offline use
          _cachePosition(position);

          _log('Location updated: ${position.latitude}, ${position.longitude}');
        },
        onError: (e) {
          _logError('Error getting location updates', e);
        },
      );

      // Get initial position
      final position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
      );
      _locationController.add(position);
      _lastKnownPosition = position;
      _locationUpdateController
          .add(gmaps.LatLng(position.latitude, position.longitude));

      // Cache the position for offline use
      _cachePosition(position);

      _log('Location tracking started');
      return true;
    } catch (e, stackTrace) {
      _logError('Error starting location tracking', e, stackTrace);
      return false;
    }
  }

  /// Cache the position for offline use
  Future<void> _cachePosition(Position position) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final positionJson = {
        'latitude': position.latitude,
        'longitude': position.longitude,
        'timestamp': DateTime.now().millisecondsSinceEpoch,
        'accuracy': position.accuracy,
        'altitude': position.altitude,
        'heading': position.heading,
        'speed': position.speed,
        'speedAccuracy': position.speedAccuracy,
      };

      // Store in memory cache
      _locationCache['lastPosition'] = positionJson;

      // Store in shared preferences
      await prefs.setString('lastPosition', position.toString());
    } catch (e) {
      _logError('Error caching position', e);
    }
  }

  /// Get nearby landmarks
  Future<List<Landmark>> getNearbyLandmarks(
    double latitude,
    double longitude,
    double radiusInMeters, {
    bool useCache = true,
  }) async {
    try {
      // Check if we have cached landmarks
      if (useCache && _locationCache.containsKey('landmarks')) {
        final cachedLandmarks = _locationCache['landmarks'] as List<Landmark>;

        // Check if any landmarks are within the radius
        final nearbyLandmarks = cachedLandmarks.where((landmark) {
          final landmarkLat = landmark.location['latitude'] as double;
          final landmarkLng = landmark.location['longitude'] as double;

          final distance = calculateDistance(
            latitude,
            longitude,
            landmarkLat,
            landmarkLng,
          );
          return distance <= radiusInMeters;
        }).toList();

        if (nearbyLandmarks.isNotEmpty) {
          _log('Using cached landmarks: ${nearbyLandmarks.length} found');
          return nearbyLandmarks;
        }
      }

      // In a real app, this would make an API call to get nearby landmarks
      // For now, we'll return some sample landmarks
      final landmarks = [
        Landmark(
          id: '1',
          name: 'National Museum',
          description: 'A museum showcasing the country\'s history and culture',
          historicalSignificance:
              'Built in 1950, this museum houses artifacts from ancient civilizations.',
          imageUrl: 'https://picsum.photos/200/300',
          rating: 4.5,
          reviewCount: 120,
          tags: ['Museum', 'History', 'Culture'],
          translations: {
            'fr': 'Musée National',
            'es': 'Museo Nacional',
          },
          location: {
            'latitude': latitude + 0.01,
            'longitude': longitude + 0.01,
            'address': '123 Museum Street',
            'city': 'Cityville',
          },
          arContent: {
            'modelUrl': 'https://example.com/models/museum.glb',
            'animations': ['rotate', 'highlight'],
          },
        ),
        Landmark(
          id: '2',
          name: 'Central Market',
          description: 'A bustling market with local goods and food',
          historicalSignificance:
              'This market has been operating since the 1800s.',
          imageUrl: 'https://picsum.photos/200/301',
          rating: 4.2,
          reviewCount: 85,
          tags: ['Market', 'Food', 'Shopping'],
          translations: {
            'fr': 'Marché Central',
            'es': 'Mercado Central',
          },
          location: {
            'latitude': latitude - 0.01,
            'longitude': longitude - 0.01,
            'address': '456 Market Street',
            'city': 'Cityville',
          },
          arContent: {
            'modelUrl': 'https://example.com/models/market.glb',
            'animations': ['pulse', 'highlight'],
          },
        ),
        Landmark(
          id: '3',
          name: 'City Park',
          description: 'A beautiful park in the heart of the city',
          historicalSignificance:
              'This park was designed by a famous landscape architect in 1920.',
          imageUrl: 'https://picsum.photos/200/302',
          rating: 4.7,
          reviewCount: 150,
          tags: ['Park', 'Nature', 'Recreation'],
          translations: {
            'fr': 'Parc de la Ville',
            'es': 'Parque de la Ciudad',
          },
          location: {
            'latitude': latitude + 0.02,
            'longitude': longitude - 0.02,
            'address': '789 Park Avenue',
            'city': 'Cityville',
          },
          arContent: {
            'modelUrl': 'https://example.com/models/park.glb',
            'animations': ['float', 'highlight'],
          },
        ),
      ];

      // Cache the landmarks
      _locationCache['landmarks'] = landmarks;

      // Filter landmarks by distance
      final nearbyLandmarks = landmarks.where((landmark) {
        final landmarkLat = landmark.location['latitude'] as double;
        final landmarkLng = landmark.location['longitude'] as double;

        final distance = calculateDistance(
          latitude,
          longitude,
          landmarkLat,
          landmarkLng,
        );
        return distance <= radiusInMeters;
      }).toList();

      _log('Fetched landmarks: ${nearbyLandmarks.length} found');
      return nearbyLandmarks;
    } catch (e, stackTrace) {
      _logError('Error getting nearby landmarks', e, stackTrace);
      return [];
    }
  }

  /// Calculate distance between two points in meters
  double calculateDistance(
    double startLat,
    double startLng,
    double endLat,
    double endLng,
  ) {
    return Geolocator.distanceBetween(
      startLat,
      startLng,
      endLat,
      endLng,
    );
  }

  /// Format distance for display
  String formatDistance(double distanceInMeters) {
    if (distanceInMeters < 1000) {
      return '${distanceInMeters.round()} m';
    } else {
      final distanceInKm = distanceInMeters / 1000;
      return '${distanceInKm.toStringAsFixed(1)} km';
    }
  }

  /// Get location-based recommendations
  Future<List<Landmark>> getRecommendedLandmarks(
    double latitude,
    double longitude, {
    int limit = 5,
    List<String>? categories,
  }) async {
    try {
      // Get nearby landmarks within 5km
      final landmarks = await getNearbyLandmarks(latitude, longitude, 5000);

      // Filter by categories if provided
      var filteredLandmarks = landmarks;
      if (categories != null && categories.isNotEmpty) {
        filteredLandmarks = landmarks
            .where((landmark) =>
                categories.any((category) => landmark.tags.contains(category)))
            .toList();
      }

      // Sort by distance
      filteredLandmarks.sort((a, b) {
        final aLat = a.location['latitude'] as double;
        final aLng = a.location['longitude'] as double;
        final bLat = b.location['latitude'] as double;
        final bLng = b.location['longitude'] as double;

        final distanceA = calculateDistance(
          latitude,
          longitude,
          aLat,
          aLng,
        );
        final distanceB = calculateDistance(
          latitude,
          longitude,
          bLat,
          bLng,
        );
        return distanceA.compareTo(distanceB);
      });

      // Return limited number of landmarks
      return filteredLandmarks.take(limit).toList();
    } catch (e, stackTrace) {
      _logError('Error getting recommended landmarks', e, stackTrace);
      return [];
    }
  }

  /// Search for places by query
  Future<List<PlaceResult>> searchPlaces(String query) async {
    try {
      _log('Searching for places: $query');

      // In a real app, this would call a Places API
      // For now, return mock data
      await Future.delayed(const Duration(milliseconds: 500));

      return [
        PlaceResult(
          id: '1',
          name: 'JFK Airport',
          address: 'Queens, NY 11430',
          city: 'New York',
          country: 'United States',
          postalCode: '11430',
          latitude: 40.6413,
          longitude: -73.7781,
          type: 'airport',
        ),
        PlaceResult(
          id: '2',
          name: 'LaGuardia Airport',
          address: 'Queens, NY 11371',
          city: 'New York',
          country: 'United States',
          postalCode: '11371',
          latitude: 40.7769,
          longitude: -73.8740,
          type: 'airport',
        ),
        PlaceResult(
          id: '3',
          name: 'Times Square',
          address: 'Manhattan, NY 10036',
          city: 'New York',
          country: 'United States',
          postalCode: '10036',
          latitude: 40.7580,
          longitude: -73.9855,
          type: 'attraction',
        ),
      ];
    } catch (e, stackTrace) {
      _logError('Error searching for places', e, stackTrace);
      return [];
    }
  }

  /// Search for places near a location
  Future<List<PlaceResult>> searchNearbyPlaces(
      double latitude, double longitude, double radiusInMeters,
      {String? type}) async {
    try {
      _log(
          'Searching for nearby places at $latitude, $longitude with radius ${radiusInMeters}m');

      // In a real app, this would call a Places API
      // For now, return mock data
      await Future.delayed(const Duration(milliseconds: 500));

      final places = [
        PlaceResult(
          id: '1',
          name: 'JFK Airport',
          address: 'Queens, NY 11430',
          city: 'New York',
          country: 'United States',
          postalCode: '11430',
          latitude: 40.6413,
          longitude: -73.7781,
          type: 'airport',
        ),
        PlaceResult(
          id: '2',
          name: 'LaGuardia Airport',
          address: 'Queens, NY 11371',
          city: 'New York',
          country: 'United States',
          postalCode: '11371',
          latitude: 40.7769,
          longitude: -73.8740,
          type: 'airport',
        ),
        PlaceResult(
          id: '3',
          name: 'Times Square',
          address: 'Manhattan, NY 10036',
          city: 'New York',
          country: 'United States',
          postalCode: '10036',
          latitude: 40.7580,
          longitude: -73.9855,
          type: 'attraction',
        ),
      ];

      // Filter by type if provided
      if (type != null) {
        return places.where((place) => place.type == type).toList();
      }

      return places;
    } catch (e, stackTrace) {
      _logError('Error searching for nearby places', e, stackTrace);
      return [];
    }
  }

  /// Get nearby airports
  Future<List<PlaceResult>> getNearbyAirports(LatLng coordinates,
      {double radiusKm = 50}) async {
    try {
      _log(
          'Getting nearby airports at ${coordinates.latitude}, ${coordinates.longitude}');

      // Convert LatLng to our custom class if needed
      final latitude = coordinates.latitude;
      final longitude = coordinates.longitude;

      // Call searchNearbyPlaces with type 'airport'
      return searchNearbyPlaces(
        latitude,
        longitude,
        radiusKm * 1000, // Convert to meters
        type: 'airport',
      );
    } catch (e, stackTrace) {
      _logError('Error getting nearby airports', e, stackTrace);
      return [];
    }
  }

  /// Load map style from asset
  Future<String?> loadMapStyle(String styleName) async {
    try {
      // In a real app, this would load the style from an asset file
      // For now, we'll return a sample style
      switch (styleName) {
        case 'standard':
          return null; // Default style
        case 'silver':
          return '''
            [
              {
                "elementType": "geometry",
                "stylers": [
                  {
                    "color": "#f5f5f5"
                  }
                ]
              },
              {
                "elementType": "labels.icon",
                "stylers": [
                  {
                    "visibility": "off"
                  }
                ]
              },
              {
                "elementType": "labels.text.fill",
                "stylers": [
                  {
                    "color": "#616161"
                  }
                ]
              }
            ]
          ''';
        case 'retro':
          return '''
            [
              {
                "elementType": "geometry",
                "stylers": [
                  {
                    "color": "#ebe3cd"
                  }
                ]
              },
              {
                "elementType": "labels.text.fill",
                "stylers": [
                  {
                    "color": "#523735"
                  }
                ]
              },
              {
                "elementType": "labels.text.stroke",
                "stylers": [
                  {
                    "color": "#f5f1e6"
                  }
                ]
              }
            ]
          ''';
        case 'dark':
          return '''
            [
              {
                "elementType": "geometry",
                "stylers": [
                  {
                    "color": "#212121"
                  }
                ]
              },
              {
                "elementType": "labels.icon",
                "stylers": [
                  {
                    "visibility": "off"
                  }
                ]
              },
              {
                "elementType": "labels.text.fill",
                "stylers": [
                  {
                    "color": "#757575"
                  }
                ]
              }
            ]
          ''';
        case 'night':
          return '''
            [
              {
                "elementType": "geometry",
                "stylers": [
                  {
                    "color": "#242f3e"
                  }
                ]
              },
              {
                "elementType": "labels.text.fill",
                "stylers": [
                  {
                    "color": "#746855"
                  }
                ]
              },
              {
                "elementType": "labels.text.stroke",
                "stylers": [
                  {
                    "color": "#242f3e"
                  }
                ]
              }
            ]
          ''';
        case 'aubergine':
          return '''
            [
              {
                "elementType": "geometry",
                "stylers": [
                  {
                    "color": "#1d2c4d"
                  }
                ]
              },
              {
                "elementType": "labels.text.fill",
                "stylers": [
                  {
                    "color": "#8ec3b9"
                  }
                ]
              },
              {
                "elementType": "labels.text.stroke",
                "stylers": [
                  {
                    "color": "#1a3646"
                  }
                ]
              }
            ]
          ''';
        default:
          return null;
      }
    } catch (e, stackTrace) {
      _logError('Error loading map style', e, stackTrace);
      return null;
    }
  }

  /// Get route between two points
  Future<List<LatLng>> getRoute(LatLng origin, LatLng destination) async {
    try {
      // In a real app, this would make an API call to a routing service like Google Directions API
      // For now, we'll return a simple straight line route

      // Create a simple route with 5 points
      final route = <LatLng>[];

      // Add origin
      route.add(origin);

      // Add 3 intermediate points
      for (int i = 1; i <= 3; i++) {
        final fraction = i / 4;
        final lat = origin.latitude +
            (destination.latitude - origin.latitude) * fraction;
        final lng = origin.longitude +
            (destination.longitude - origin.longitude) * fraction;
        route.add(LatLng(lat, lng));
      }

      // Add destination
      route.add(destination);

      return route;
    } catch (e, stackTrace) {
      _logError('Error getting route', e, stackTrace);
      return [origin, destination]; // Fallback to direct route
    }
  }

  /// Calculate route distance in meters
  double calculateRouteDistance(List<LatLng> route) {
    double distance = 0;

    for (int i = 0; i < route.length - 1; i++) {
      distance += calculateDistance(
        route[i].latitude,
        route[i].longitude,
        route[i + 1].latitude,
        route[i + 1].longitude,
      );
    }

    return distance;
  }

  /// Estimate travel time in minutes
  int estimateTravelTime(double distanceInMeters, String mode) {
    // Average speeds in meters per minute
    const walkingSpeed = 80.0; // ~5 km/h
    const cyclingSpeed = 250.0; // ~15 km/h
    const drivingSpeed = 500.0; // ~30 km/h

    switch (mode) {
      case 'walking':
        return (distanceInMeters / walkingSpeed).ceil();
      case 'cycling':
        return (distanceInMeters / cyclingSpeed).ceil();
      case 'driving':
        return (distanceInMeters / drivingSpeed).ceil();
      default:
        return (distanceInMeters / walkingSpeed).ceil();
    }
  }

  /// Dispose of resources
  void dispose() {
    _locationTimer?.cancel();
    _locationController.close();
    _permissionStatusController.close();
    _locationUpdateController.close();
    _isTracking = false;
    _log('Location service disposed');
  }

  // Get current position
  Future<Position> getCurrentPosition() async {
    bool serviceEnabled;
    LocationPermission permission;

    // Test if location services are enabled.
    serviceEnabled = await Geolocator.isLocationServiceEnabled();
    if (!serviceEnabled) {
      // Location services are not enabled, request user to enable
      return Future.error('Location services are disabled.');
    }

    permission = await Geolocator.checkPermission();
    if (permission == LocationPermission.denied) {
      permission = await Geolocator.requestPermission();
      if (permission == LocationPermission.denied) {
        // Permissions are denied, next time you could try
        // requesting permissions again
        return Future.error('Location permissions are denied');
      }
    }

    if (permission == LocationPermission.deniedForever) {
      // Permissions are denied forever, handle appropriately.
      return Future.error(
          'Location permissions are permanently denied, we cannot request permissions.');
    }

    // When we reach here, permissions are granted and we can
    // continue accessing the position of the device.
    return await Geolocator.getCurrentPosition(
      desiredAccuracy: LocationAccuracy.high,
    );
  }

  // Get last known position
  Future<Position?> getLastKnownPosition() async {
    return await Geolocator.getLastKnownPosition();
  }

  // Stream position updates
  Stream<Position> getPositionStream({
    LocationAccuracy accuracy = LocationAccuracy.high,
    int distanceFilter = 10,
  }) {
    return Geolocator.getPositionStream(
      locationSettings: LocationSettings(
        accuracy: accuracy,
        distanceFilter: distanceFilter,
      ),
    );
  }

  // Check if location services are enabled
  Future<bool> isLocationServiceEnabled() async {
    return await Geolocator.isLocationServiceEnabled();
  }

  // Request location permission
  Future<LocationPermission> requestPermission() async {
    return await Geolocator.requestPermission();
  }

  // Check location permission
  Future<LocationPermission> checkPermission() async {
    return await Geolocator.checkPermission();
  }

  // Open location settings
  Future<bool> openLocationSettings() async {
    return await Geolocator.openLocationSettings();
  }

  // Open app settings
  Future<bool> openAppSettings() async {
    return await Geolocator.openAppSettings();
  }
}

// Providers
final locationServiceProvider = Provider<LocationService>((ref) {
  final loggingService = ref.watch(loggingServiceProvider);
  final errorHandlingService = ref.watch(errorHandlingServiceProvider);
  return LocationService(
    loggingService: loggingService,
    errorHandlingService: errorHandlingService,
  );
});

/// Provider for the current position
final currentPositionProvider = FutureProvider<Position>((ref) async {
  final locationService = ref.watch(locationServiceProvider);
  return await locationService.getCurrentPosition();
});

/// Provider for position updates
final positionStreamProvider = StreamProvider<Position>((ref) {
  final locationService = ref.watch(locationServiceProvider);
  return locationService.getPositionStream();
});

/// Provider for location permission status
final locationPermissionProvider =
    FutureProvider<LocationPermission>((ref) async {
  final locationService = ref.watch(locationServiceProvider);
  return await locationService.checkPermission();
});

/// Provider for location permission status as enum
final locationPermissionStatusProvider =
    StreamProvider<LocationPermissionStatus>((ref) {
  final locationService = ref.watch(locationServiceProvider);
  return locationService.permissionStatusStream;
});

/// Provider for location updates as LatLng
final locationUpdateProvider = StreamProvider<gmaps.LatLng>((ref) {
  final locationService = ref.watch(locationServiceProvider);
  return locationService.locationUpdateStream;
});

/// Provider for map style
final mapStyleProvider =
    FutureProvider.family<String?, String>((ref, styleName) async {
  final locationService = ref.watch(locationServiceProvider);
  return await locationService.loadMapStyle(styleName);
});

/// Provider for nearby landmarks
final nearbyLandmarksProvider =
    FutureProvider.family<List<Landmark>, double>((ref, radiusInMeters) async {
  final locationService = ref.watch(locationServiceProvider);
  final position = await ref.watch(currentPositionProvider.future);
  return await locationService.getNearbyLandmarks(
    position.latitude,
    position.longitude,
    radiusInMeters,
  );
});

/// Provider for recommended landmarks
final recommendedLandmarksProvider =
    FutureProvider.family<List<Landmark>, List<String>?>(
        (ref, categories) async {
  final locationService = ref.watch(locationServiceProvider);
  final position = await ref.watch(currentPositionProvider.future);
  return await locationService.getRecommendedLandmarks(
    position.latitude,
    position.longitude,
    categories: categories,
  );
});
