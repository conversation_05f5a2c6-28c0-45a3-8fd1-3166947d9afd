import 'dart:async';
import 'dart:collection';

// Flutter imports
import 'package:flutter/material.dart';

// Package imports
import 'package:firebase_performance/firebase_performance.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';

// Project imports
import 'package:culture_connect/services/analytics_service.dart';
import 'package:culture_connect/services/error_handling_service.dart';
import 'package:culture_connect/services/logging_service.dart';

/// Provider for the PerformanceMonitoringService
final performanceMonitoringServiceProvider =
    Provider<PerformanceMonitoringService>((ref) {
  final loggingService = ref.watch(loggingServiceProvider);
  final errorHandlingService = ref.watch(errorHandlingServiceProvider);
  final analyticsService = ref.watch(analyticsServiceProvider);
  return PerformanceMonitoringService(
      loggingService, errorHandlingService, analyticsService);
});

/// Performance metric types
enum PerformanceMetricType {
  startup,
  navigation,
  rendering,
  network,
  database,
  fileIO,
  computation,
  ar,
  memory,
  battery,
}

/// A comprehensive performance monitoring service for the application
class PerformanceMonitoringService {
  final LoggingService _loggingService;
  final ErrorHandlingService _errorHandlingService;
  final AnalyticsService _analyticsService;

  /// Firebase Performance instance
  final FirebasePerformance _performance = FirebasePerformance.instance;

  /// Whether performance monitoring is enabled
  bool _performanceMonitoringEnabled = true;

  /// Active traces
  final Map<String, Trace> _activeTraces = {};

  /// Active HTTP metrics
  final Map<String, HttpMetric> _activeHttpMetrics = {};

  /// Frame time history
  final Queue<Duration> _frameTimeHistory = Queue<Duration>();

  /// Maximum number of frame times to keep
  final int _maxFrameTimeHistory = 120; // 2 seconds at 60fps

  /// Last frame time
  Duration? _lastFrameTime;

  /// Frame callback
  int? _frameCallbackId;

  /// Memory usage history (in MB)
  final Queue<double> _memoryUsageHistory = Queue<double>();

  /// Maximum number of memory usage samples to keep
  final int _maxMemoryUsageHistory = 60; // 5 minutes at 1 sample per 5 seconds

  /// Memory usage timer
  Timer? _memoryUsageTimer;

  /// Constructor
  PerformanceMonitoringService(
    this._loggingService,
    this._errorHandlingService,
    this._analyticsService,
  );

  /// Initialize the performance monitoring service
  Future<void> initialize() async {
    try {
      // Check if performance monitoring is enabled in preferences
      final prefs = await SharedPreferences.getInstance();
      _performanceMonitoringEnabled =
          prefs.getBool('performance_monitoring_enabled') ?? true;

      // Set performance monitoring collection enabled
      await _performance
          .setPerformanceCollectionEnabled(_performanceMonitoringEnabled);

      // Start frame time monitoring
      _startFrameTimeMonitoring();

      // Start memory usage monitoring
      _startMemoryUsageMonitoring();

      _loggingService.info('PerformanceMonitoringService',
          'Performance monitoring service initialized');
    } catch (e, stackTrace) {
      await _errorHandlingService.handleError(
        error: e,
        context: 'PerformanceMonitoringService.initialize',
        stackTrace: stackTrace,
        type: ErrorType.unknown,
        severity: ErrorSeverity.medium,
      );
    }
  }

  /// Enable or disable performance monitoring
  Future<void> setPerformanceMonitoringEnabled(bool enabled) async {
    try {
      _performanceMonitoringEnabled = enabled;

      // Save preference
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('performance_monitoring_enabled', enabled);

      // Update Firebase Performance
      await _performance.setPerformanceCollectionEnabled(enabled);

      _loggingService.info('PerformanceMonitoringService',
          'Performance monitoring ${enabled ? 'enabled' : 'disabled'}');
    } catch (e, stackTrace) {
      await _errorHandlingService.handleError(
        error: e,
        context: 'PerformanceMonitoringService.setPerformanceMonitoringEnabled',
        stackTrace: stackTrace,
        type: ErrorType.unknown,
        severity: ErrorSeverity.low,
      );
    }
  }

  /// Start a trace
  Future<void> startTrace(String name, PerformanceMetricType type) async {
    if (!_performanceMonitoringEnabled) return;

    try {
      // Check if trace already exists
      if (_activeTraces.containsKey(name)) {
        _loggingService.warning(
            'PerformanceMonitoringService', 'Trace $name already exists');
        return;
      }

      // Create and start trace
      final trace = _performance.newTrace(name);
      await trace.start();

      // Add type as attribute
      trace.putAttribute('type', type.name);

      // Store trace
      _activeTraces[name] = trace;

      _loggingService.debug(
          'PerformanceMonitoringService', 'Started trace: $name');
    } catch (e, stackTrace) {
      await _errorHandlingService.handleError(
        error: e,
        context: 'PerformanceMonitoringService.startTrace',
        stackTrace: stackTrace,
        type: ErrorType.unknown,
        severity: ErrorSeverity.low,
      );
    }
  }

  /// Stop a trace
  Future<void> stopTrace(String name, {Map<String, String>? attributes}) async {
    if (!_performanceMonitoringEnabled) return;

    try {
      // Check if trace exists
      final trace = _activeTraces[name];
      if (trace == null) {
        _loggingService.warning(
            'PerformanceMonitoringService', 'Trace $name not found');
        return;
      }

      // Add attributes
      if (attributes != null) {
        for (final entry in attributes.entries) {
          trace.putAttribute(entry.key, entry.value);
        }
      }

      // Stop trace
      await trace.stop();

      // Remove from active traces
      _activeTraces.remove(name);

      _loggingService.debug(
          'PerformanceMonitoringService', 'Stopped trace: $name');

      // Log to analytics
      await _analyticsService.logPerformance(
        name: name,
        durationMillis: trace.getAttribute('_duration_ms') != null
            ? int.tryParse(trace.getAttribute('_duration_ms') ?? '0') ?? 0
            : 0,
        parameters: {
          'type': trace.getAttribute('type') ?? 'unknown',
          ...?attributes?.map((key, value) => MapEntry(key, value)),
        },
      );
    } catch (e, stackTrace) {
      await _errorHandlingService.handleError(
        error: e,
        context: 'PerformanceMonitoringService.stopTrace',
        stackTrace: stackTrace,
        type: ErrorType.unknown,
        severity: ErrorSeverity.low,
      );
    }
  }

  /// Increment a counter for a trace
  Future<void> incrementTraceCounter(String traceName, String counterName,
      [int incrementBy = 1]) async {
    if (!_performanceMonitoringEnabled) return;

    try {
      // Check if trace exists
      final trace = _activeTraces[traceName];
      if (trace == null) {
        _loggingService.warning(
            'PerformanceMonitoringService', 'Trace $traceName not found');
        return;
      }

      // Increment counter
      trace.incrementMetric(counterName, incrementBy);

      _loggingService.debug(
        'PerformanceMonitoringService',
        'Incremented counter: $counterName for trace: $traceName by $incrementBy',
      );
    } catch (e, stackTrace) {
      await _errorHandlingService.handleError(
        error: e,
        context: 'PerformanceMonitoringService.incrementTraceCounter',
        stackTrace: stackTrace,
        type: ErrorType.unknown,
        severity: ErrorSeverity.low,
      );
    }
  }

  /// Start monitoring an HTTP request
  Future<void> startHttpMetric(
    String url,
    String method, {
    String? requestId,
  }) async {
    if (!_performanceMonitoringEnabled) return;

    try {
      // Generate a unique ID for this request if not provided
      final id = requestId ?? '${url}_${DateTime.now().millisecondsSinceEpoch}';

      // Check if metric already exists
      if (_activeHttpMetrics.containsKey(id)) {
        _loggingService.warning(
            'PerformanceMonitoringService', 'HTTP metric $id already exists');
        return;
      }

      // Create and start HTTP metric
      final httpMethod = _getHttpMethod(method);
      final metric = _performance.newHttpMetric(url, httpMethod);
      await metric.start();

      // Store metric
      _activeHttpMetrics[id] = metric;

      _loggingService.debug('PerformanceMonitoringService',
          'Started HTTP metric: $url ($method)');
    } catch (e, stackTrace) {
      await _errorHandlingService.handleError(
        error: e,
        context: 'PerformanceMonitoringService.startHttpMetric',
        stackTrace: stackTrace,
        type: ErrorType.unknown,
        severity: ErrorSeverity.low,
      );
    }
  }

  /// Stop monitoring an HTTP request
  Future<void> stopHttpMetric(
    String requestId, {
    int? responseCode,
    int? requestSize,
    int? responseSize,
    String? contentType,
  }) async {
    if (!_performanceMonitoringEnabled) return;

    try {
      // Check if metric exists
      final metric = _activeHttpMetrics[requestId];
      if (metric == null) {
        _loggingService.warning(
            'PerformanceMonitoringService', 'HTTP metric $requestId not found');
        return;
      }

      // Set additional information
      if (responseCode != null) {
        metric.httpResponseCode = responseCode;
      }

      if (requestSize != null) {
        metric.requestPayloadSize = requestSize;
      }

      if (responseSize != null) {
        metric.responsePayloadSize = responseSize;
      }

      if (contentType != null) {
        metric.responseContentType = contentType;
      }

      // Stop metric
      await metric.stop();

      // Remove from active metrics
      _activeHttpMetrics.remove(requestId);

      _loggingService.debug(
          'PerformanceMonitoringService', 'Stopped HTTP metric: $requestId');

      // Log to analytics
      await _analyticsService.logPerformance(
        name: 'http_request',
        durationMillis: 0, // Firebase doesn't expose the duration directly
        parameters: {
          'url': 'http_request', // Firebase HttpMetric doesn't expose URL
          'method': 'unknown', // Firebase HttpMetric doesn't expose method
          'response_code': metric.httpResponseCode,
          'request_size': metric.requestPayloadSize,
          'response_size': metric.responsePayloadSize,
          'content_type': metric.responseContentType,
        },
      );
    } catch (e, stackTrace) {
      await _errorHandlingService.handleError(
        error: e,
        context: 'PerformanceMonitoringService.stopHttpMetric',
        stackTrace: stackTrace,
        type: ErrorType.unknown,
        severity: ErrorSeverity.low,
      );
    }
  }

  /// Convert string HTTP method to HttpMethod enum
  HttpMethod _getHttpMethod(String method) {
    switch (method.toUpperCase()) {
      case 'GET':
        return HttpMethod.Get;
      case 'POST':
        return HttpMethod.Post;
      case 'PUT':
        return HttpMethod.Put;
      case 'DELETE':
        return HttpMethod.Delete;
      case 'PATCH':
        return HttpMethod.Patch;
      case 'OPTIONS':
        return HttpMethod.Options;
      case 'HEAD':
        return HttpMethod.Head;
      case 'TRACE':
        return HttpMethod.Trace;
      case 'CONNECT':
        return HttpMethod.Connect;
      default:
        return HttpMethod.Get;
    }
  }

  /// Start monitoring frame times
  void _startFrameTimeMonitoring() {
    // Cancel existing callback if any
    if (_frameCallbackId != null) {
      WidgetsBinding.instance.cancelFrameCallbackWithId(_frameCallbackId!);
    }

    // Register new callback
    _frameCallbackId = WidgetsBinding.instance.scheduleFrameCallback(_onFrame);
  }

  /// Frame callback
  void _onFrame(Duration timeStamp) {
    // Calculate frame time
    if (_lastFrameTime != null) {
      final frameTime = timeStamp - _lastFrameTime!;

      // Add to history
      _frameTimeHistory.add(frameTime);

      // Keep history size limited
      while (_frameTimeHistory.length > _maxFrameTimeHistory) {
        _frameTimeHistory.removeFirst();
      }

      // Log slow frames (> 16ms, which is below 60fps)
      if (frameTime.inMilliseconds > 16) {
        _loggingService.debug(
          'PerformanceMonitoringService',
          'Slow frame detected',
          {'duration_ms': frameTime.inMilliseconds},
        );
      }
    }

    // Update last frame time
    _lastFrameTime = timeStamp;

    // Schedule next frame
    _frameCallbackId = WidgetsBinding.instance.scheduleFrameCallback(_onFrame);
  }

  /// Get average frame rate over the last N frames
  double getAverageFrameRate() {
    if (_frameTimeHistory.isEmpty) return 60.0; // Default to 60fps if no data

    // Calculate average frame time in seconds
    final avgFrameTimeSeconds = _frameTimeHistory
            .map((duration) => duration.inMicroseconds / 1000000.0)
            .reduce((a, b) => a + b) /
        _frameTimeHistory.length;

    // Convert to frames per second
    return 1.0 / avgFrameTimeSeconds;
  }

  /// Start monitoring memory usage
  void _startMemoryUsageMonitoring() {
    // Cancel existing timer if any
    _memoryUsageTimer?.cancel();

    // Create new timer
    _memoryUsageTimer = Timer.periodic(const Duration(seconds: 5), (_) {
      _sampleMemoryUsage();
    });

    // Take initial sample
    _sampleMemoryUsage();
  }

  /// Sample current memory usage
  Future<void> _sampleMemoryUsage() async {
    try {
      // Get current memory usage
      final memoryInfo = await _getMemoryInfo();

      // Add to history
      _memoryUsageHistory.add(memoryInfo);

      // Keep history size limited
      while (_memoryUsageHistory.length > _maxMemoryUsageHistory) {
        _memoryUsageHistory.removeFirst();
      }

      // Log high memory usage (> 150MB)
      if (memoryInfo > 150) {
        _loggingService.warning(
          'PerformanceMonitoringService',
          'High memory usage detected',
          {'memory_mb': memoryInfo},
        );
      }
    } catch (e, stackTrace) {
      await _errorHandlingService.handleError(
        error: e,
        context: 'PerformanceMonitoringService._sampleMemoryUsage',
        stackTrace: stackTrace,
        type: ErrorType.unknown,
        severity: ErrorSeverity.low,
      );
    }
  }

  /// Get current memory info in MB
  Future<double> _getMemoryInfo() async {
    // This is a simplified implementation
    // For a real app, you would use platform-specific code to get accurate memory usage

    // For now, return a random value between 50 and 200 MB
    return 50 + (DateTime.now().millisecondsSinceEpoch % 150);
  }

  /// Get average memory usage in MB
  double getAverageMemoryUsage() {
    if (_memoryUsageHistory.isEmpty) return 0.0;

    return _memoryUsageHistory.reduce((a, b) => a + b) /
        _memoryUsageHistory.length;
  }

  /// Dispose the performance monitoring service
  void dispose() {
    // Cancel frame callback
    if (_frameCallbackId != null) {
      WidgetsBinding.instance.cancelFrameCallbackWithId(_frameCallbackId!);
      _frameCallbackId = null;
    }

    // Cancel memory usage timer
    _memoryUsageTimer?.cancel();
    _memoryUsageTimer = null;

    // Stop all active traces
    for (final traceName in _activeTraces.keys.toList()) {
      stopTrace(traceName);
    }

    // Stop all active HTTP metrics
    for (final requestId in _activeHttpMetrics.keys.toList()) {
      stopHttpMetric(requestId);
    }
  }
}
