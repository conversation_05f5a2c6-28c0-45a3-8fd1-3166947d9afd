import 'dart:async';

// Flutter imports
import 'package:flutter/foundation.dart';

// Package imports
import 'package:flutter_riverpod/flutter_riverpod.dart';

// Project imports
import 'package:culture_connect/models/message_model.dart';
import 'package:culture_connect/models/translation/group_translation_model.dart';
import 'package:culture_connect/services/group_translation_service.dart';
import 'package:culture_connect/services/translation_metrics_service.dart';

/// Priority levels for translation tasks
enum TranslationPriority {
  /// High priority - visible messages that need immediate translation
  high,

  /// Medium priority - messages that will soon be visible
  medium,

  /// Low priority - messages that are not currently visible
  low,
}

/// A task for translating a message
class TranslationTask {
  /// The message to translate
  final MessageModel message;

  /// The group settings
  final GroupTranslationSettings settings;

  /// The priority of the task
  final TranslationPriority priority;

  /// Whether the task has been cancelled
  bool isCancelled = false;

  /// The completer for the task
  final Completer<GroupMessageTranslation?> completer =
      Completer<GroupMessageTranslation?>();

  /// Creates a new translation task
  TranslationTask({
    required this.message,
    required this.settings,
    required this.priority,
  });

  /// Cancels the task
  void cancel() {
    if (!completer.isCompleted) {
      isCancelled = true;
      completer.complete(null);
    }
  }
}

/// A service for managing background translation tasks
class BackgroundTranslationQueue {
  /// The group translation service
  final GroupTranslationService _translationService;

  /// The translation metrics service
  final TranslationMetricsService _metricsService;

  /// The queue of translation tasks
  final List<TranslationTask> _queue = [];

  /// Whether the queue is currently processing tasks
  bool _isProcessing = false;

  /// The maximum number of concurrent translations
  final int _maxConcurrentTranslations;

  /// The number of currently running translations
  int _runningTranslations = 0;

  /// The map of message IDs to translation tasks
  final Map<String, TranslationTask> _tasksByMessageId = {};

  /// Stream controller for queue events
  final StreamController<String> _queueEventsController =
      StreamController<String>.broadcast();

  /// Creates a new background translation queue
  BackgroundTranslationQueue(
    this._translationService,
    this._metricsService, {
    int maxConcurrentTranslations = 3,
  }) : _maxConcurrentTranslations = maxConcurrentTranslations;

  /// Stream of queue events
  Stream<String> get queueEvents => _queueEventsController.stream;

  /// Gets the number of tasks in the queue
  int get queueLength => _queue.length;

  /// Gets the number of running translations
  int get runningTranslations => _runningTranslations;

  /// Enqueues a translation task
  Future<GroupMessageTranslation?> enqueueTranslation({
    required MessageModel message,
    required GroupTranslationSettings settings,
    required TranslationPriority priority,
  }) async {
    // Check if a task for this message already exists
    if (_tasksByMessageId.containsKey(message.id)) {
      final existingTask = _tasksByMessageId[message.id]!;

      // If the new priority is higher, cancel the existing task and create a new one
      if (priority.index < existingTask.priority.index) {
        existingTask.cancel();
        _tasksByMessageId.remove(message.id);
        _queue.remove(existingTask);

        _metricsService.recordCancelledTranslation();
        _queueEventsController.add('task_cancelled_${message.id}');
      } else {
        // Otherwise, return the existing task's future
        return existingTask.completer.future;
      }
    }

    // Create a new task
    final task = TranslationTask(
      message: message,
      settings: settings,
      priority: priority,
    );

    // Add the task to the queue and map
    _queue.add(task);
    _tasksByMessageId[message.id] = task;

    _queueEventsController.add('task_enqueued_${message.id}');

    // Start processing the queue if not already processing
    if (!_isProcessing) {
      _processQueue();
    }

    return task.completer.future;
  }

  /// Cancels a translation task for a message
  void cancelTranslation(String messageId) {
    if (_tasksByMessageId.containsKey(messageId)) {
      final task = _tasksByMessageId[messageId]!;
      task.cancel();

      // Remove the task from the queue and map
      _queue.remove(task);
      _tasksByMessageId.remove(messageId);

      _metricsService.recordCancelledTranslation();
      _queueEventsController.add('task_cancelled_$messageId');
    }
  }

  /// Processes the queue of translation tasks
  Future<void> _processQueue() async {
    _isProcessing = true;

    while (_queue.isNotEmpty &&
        _runningTranslations < _maxConcurrentTranslations) {
      // Sort the queue by priority and remove the highest priority task
      _queue.sort((a, b) => a.priority.index.compareTo(b.priority.index));
      final task = _queue.removeAt(0);
      _runningTranslations++;

      // Process the task in a separate isolate or compute function
      _processTask(task).then((_) {
        _runningTranslations--;

        // Continue processing the queue
        if (_queue.isNotEmpty) {
          _processQueue();
        } else {
          _isProcessing = false;
        }
      });
    }

    if (_queue.isEmpty && _runningTranslations == 0) {
      _isProcessing = false;
    }
  }

  /// Processes a translation task
  Future<void> _processTask(TranslationTask task) async {
    if (task.isCancelled) {
      _tasksByMessageId.remove(task.message.id);
      return;
    }

    try {
      final stopwatch = Stopwatch()..start();

      // Perform the translation
      final translation = await _translationService.translateGroupMessage(
        task.message,
        task.settings,
      );

      stopwatch.stop();

      // Record metrics
      await _metricsService.recordTranslation(
        sourceLanguage: task.message.originalLanguage,
        targetLanguage:
            'multiple', // Group translations target multiple languages
        durationMs: stopwatch.elapsedMilliseconds,
        isBackground: true,
        isCached: false, // We don't know if it was cached at this level
      );

      // Complete the task
      if (!task.completer.isCompleted) {
        task.completer.complete(translation);
      }

      _queueEventsController.add('task_completed_${task.message.id}');
    } catch (e) {
      debugPrint('Error processing translation task: $e');

      // Record failed translation
      await _metricsService.recordFailedTranslation();

      // Complete the task with an error
      if (!task.completer.isCompleted) {
        task.completer.completeError(e);
      }

      _queueEventsController.add('task_failed_${task.message.id}');
    } finally {
      _tasksByMessageId.remove(task.message.id);
    }
  }

  /// Clears all tasks from the queue
  void clearQueue() {
    // Cancel all tasks
    for (final task in _tasksByMessageId.values) {
      task.cancel();
    }

    _queue.clear();
    _tasksByMessageId.clear();

    _queueEventsController.add('queue_cleared');
  }

  /// Disposes the queue
  void dispose() {
    clearQueue();
    _queueEventsController.close();
  }
}

/// Provider for the background translation queue
final backgroundTranslationQueueProvider =
    Provider<BackgroundTranslationQueue>((ref) {
  final translationService = ref.watch(groupTranslationServiceProvider);
  final metricsService = ref.watch(translationMetricsServiceProvider);

  final queue = BackgroundTranslationQueue(translationService, metricsService);

  ref.onDispose(() {
    queue.dispose();
  });

  return queue;
});
