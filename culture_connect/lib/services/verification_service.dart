import 'dart:io';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:uuid/uuid.dart';
import 'package:culture_connect/models/verification_model.dart';
import 'package:culture_connect/models/verification_document.dart';
import 'package:culture_connect/providers/auth_provider.dart';

/// Service for handling verification-related operations
class VerificationService {
  final FirebaseFirestore _firestore;
  final FirebaseStorage _storage;
  final String _userId;
  final Uuid _uuid = const Uuid();

  /// Creates a new verification service
  VerificationService(this._firestore, this._storage, this._userId);

  /// Get all verification badges for a user
  Future<List<VerificationBadge>> getVerificationBadges(
      {String? userId}) async {
    try {
      final targetUserId = userId ?? _userId;
      final snapshot = await _firestore
          .collection('verification_badges')
          .where('userId', isEqualTo: targetUserId)
          .get();

      return snapshot.docs
          .map((doc) =>
              VerificationBadge.fromJson({...doc.data(), 'id': doc.id}))
          .toList();
    } catch (e) {
      rethrow;
    }
  }

  /// Get all valid verification badges for a user
  Future<List<VerificationBadge>> getValidVerificationBadges(
      {String? userId}) async {
    try {
      final badges = await getVerificationBadges(userId: userId);
      return badges.where((badge) => badge.isValid).toList();
    } catch (e) {
      rethrow;
    }
  }

  /// Get a specific verification badge
  Future<VerificationBadge?> getVerificationBadge(String badgeId) async {
    try {
      final doc =
          await _firestore.collection('verification_badges').doc(badgeId).get();

      if (!doc.exists) return null;

      return VerificationBadge.fromJson({...doc.data()!, 'id': doc.id});
    } catch (e) {
      rethrow;
    }
  }

  /// Submit a verification request
  Future<VerificationRequest> submitVerificationRequest({
    required VerificationType type,
    required List<dynamic> documents,
    String? notes,
  }) async {
    try {
      // Convert dynamic list to File list if possible
      final List<File> fileDocuments = [];
      for (final doc in documents) {
        if (doc is File) {
          fileDocuments.add(doc);
        }
      }

      // Upload documents to storage
      final documentUrls = await _uploadDocuments(fileDocuments);

      // Create verification request
      final docRef = _firestore.collection('verification_requests').doc();
      final now = DateTime.now();

      final request = VerificationRequest(
        id: docRef.id,
        userId: _userId,
        type: type,
        status: VerificationStatus.pending,
        submittedAt: now,
        documentUrls: documentUrls,
        notes: notes,
      );

      await docRef.set(request.toJson());
      return request;
    } catch (e) {
      rethrow;
    }
  }

  /// Get all verification requests for the current user
  Future<List<VerificationRequest>> getVerificationRequests() async {
    try {
      final snapshot = await _firestore
          .collection('verification_requests')
          .where('userId', isEqualTo: _userId)
          .orderBy('submittedAt', descending: true)
          .get();

      return snapshot.docs
          .map((doc) =>
              VerificationRequest.fromJson({...doc.data(), 'id': doc.id}))
          .toList();
    } catch (e) {
      rethrow;
    }
  }

  /// Get a specific verification request
  Future<VerificationRequest?> getVerificationRequest(String requestId) async {
    try {
      final doc = await _firestore
          .collection('verification_requests')
          .doc(requestId)
          .get();

      if (!doc.exists) return null;

      return VerificationRequest.fromJson({...doc.data()!, 'id': doc.id});
    } catch (e) {
      rethrow;
    }
  }

  /// Cancel a verification request
  Future<void> cancelVerificationRequest(String requestId) async {
    try {
      final request = await getVerificationRequest(requestId);
      if (request == null) {
        throw Exception('Verification request not found');
      }

      if (request.userId != _userId) {
        throw Exception('You can only cancel your own verification requests');
      }

      if (request.status != VerificationStatus.pending) {
        throw Exception('You can only cancel pending verification requests');
      }

      // Delete the request
      await _firestore
          .collection('verification_requests')
          .doc(requestId)
          .delete();

      // Delete uploaded documents
      for (final url in request.documentUrls) {
        try {
          final ref = _storage.refFromURL(url);
          await ref.delete();
        } catch (e) {
          // Ignore errors when deleting documents
          debugPrint('Error deleting document: $e');
        }
      }
    } catch (e) {
      rethrow;
    }
  }

  /// Check if a user has a specific verification type
  Future<bool> hasVerification(VerificationType type, {String? userId}) async {
    try {
      final badges = await getValidVerificationBadges(userId: userId);
      return badges.any((badge) => badge.type == type);
    } catch (e) {
      rethrow;
    }
  }

  /// Get all verification documents for the current user
  Future<List<VerificationDocument>> getUserDocuments() async {
    try {
      final snapshot = await _firestore
          .collection('verification_documents')
          .where('userId', isEqualTo: _userId)
          .get();

      if (snapshot.docs.isEmpty) {
        // Return mock data for demo purposes
        return [
          VerificationDocument(
            id: '1',
            type: DocumentType.passport,
            fileName: 'passport.jpg',
            uploadDate: DateTime.now().subtract(const Duration(days: 30)),
            status: VerificationStatus.approved,
          ),
          VerificationDocument(
            id: '2',
            type: DocumentType.driversLicense,
            fileName: 'drivers_license.jpg',
            uploadDate: DateTime.now().subtract(const Duration(days: 20)),
            status: VerificationStatus.approved,
          ),
          VerificationDocument(
            id: '3',
            type: DocumentType.visa,
            fileName: 'visa.pdf',
            uploadDate: DateTime.now().subtract(const Duration(days: 10)),
            status: VerificationStatus.pending,
          ),
        ];
      }

      return snapshot.docs
          .map((doc) =>
              VerificationDocument.fromJson({...doc.data(), 'id': doc.id}))
          .toList();
    } catch (e) {
      // Return mock data in case of error
      return [
        VerificationDocument(
          id: '1',
          type: DocumentType.passport,
          fileName: 'passport.jpg',
          uploadDate: DateTime.now().subtract(const Duration(days: 30)),
          status: VerificationStatus.approved,
        ),
        VerificationDocument(
          id: '2',
          type: DocumentType.driversLicense,
          fileName: 'drivers_license.jpg',
          uploadDate: DateTime.now().subtract(const Duration(days: 20)),
          status: VerificationStatus.approved,
        ),
        VerificationDocument(
          id: '3',
          type: DocumentType.visa,
          fileName: 'visa.pdf',
          uploadDate: DateTime.now().subtract(const Duration(days: 10)),
          status: VerificationStatus.pending,
        ),
      ];
    }
  }

  /// Upload documents to storage
  Future<List<String>> _uploadDocuments(List<File> documents) async {
    try {
      final urls = <String>[];

      for (final document in documents) {
        final fileName =
            '${_userId}_${_uuid.v4()}_${document.path.split('/').last}';
        final ref = _storage.ref().child('verification_documents/$fileName');

        final uploadTask = ref.putFile(document);
        final snapshot = await uploadTask;

        final url = await snapshot.ref.getDownloadURL();
        urls.add(url);
      }

      return urls;
    } catch (e) {
      rethrow;
    }
  }
}

/// Provider for the verification service
final verificationServiceProvider = Provider<VerificationService>((ref) {
  final user = ref.watch(authStateProvider).user;
  if (user == null) {
    throw Exception('User must be logged in to access verification services');
  }

  final firestore = FirebaseFirestore.instance;
  final storage = FirebaseStorage.instance;

  return VerificationService(firestore, storage, user.id);
});

/// Stream provider for verification badges
final verificationBadgesProvider =
    StreamProvider<List<VerificationBadge>>((ref) {
  final user = ref.watch(authStateProvider).user;
  if (user == null) {
    throw Exception('User must be logged in to access verification badges');
  }

  return FirebaseFirestore.instance
      .collection('verification_badges')
      .where('userId', isEqualTo: user.id)
      .snapshots()
      .map((snapshot) => snapshot.docs
          .map((doc) =>
              VerificationBadge.fromJson({...doc.data(), 'id': doc.id}))
          .toList());
});

/// Stream provider for verification requests
final verificationRequestsProvider =
    StreamProvider<List<VerificationRequest>>((ref) {
  final user = ref.watch(authStateProvider).user;
  if (user == null) {
    throw Exception('User must be logged in to access verification requests');
  }

  return FirebaseFirestore.instance
      .collection('verification_requests')
      .where('userId', isEqualTo: user.id)
      .orderBy('submittedAt', descending: true)
      .snapshots()
      .map((snapshot) => snapshot.docs
          .map((doc) =>
              VerificationRequest.fromJson({...doc.data(), 'id': doc.id}))
          .toList());
});
