import 'dart:async';

import 'package:culture_connect/models/travel/document/visa_requirement.dart';
import 'package:culture_connect/services/travel/document/visa_requirement_service.dart'
    as internal;
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:culture_connect/providers/travel/document/travel_document_providers.dart';

/// A service for handling visa-related operations.
///
/// This is a facade that provides access to the visa requirement service.
/// It exists for backward compatibility with code that imports from
/// 'package:culture_connect/services/visa_service.dart'.
class VisaService {
  /// The internal visa requirement service
  final internal.VisaRequirementService _internalService;

  /// Creates a new visa service.
  VisaService() : _internalService = internal.VisaRequirementService();

  /// Creates a new visa service with a WidgetRef.
  ///
  /// This allows access to the full functionality of the visa requirement service.
  factory VisaService.fromRef(WidgetRef ref) {
    return VisaService._withService(
      ref.read(visaRequirementServiceProvider),
    );
  }

  /// Creates a new visa service with the specified service.
  VisaService._withService(this._internalService);

  /// Initialize the service.
  Future<void> initialize() async {
    await _internalService.initialize();
  }

  /// Get visa requirements between two countries.
  Future<VisaRequirement?> getVisaRequirement(
      String countryFrom, String countryTo) async {
    return _internalService.getVisaRequirement(countryFrom, countryTo);
  }

  /// Get all visa requirements.
  Future<List<VisaRequirement>> getAllVisaRequirements() async {
    return _internalService.getAllVisaRequirements();
  }

  /// Get all visa requirements for a specific country of origin.
  Future<List<VisaRequirement>> getVisaRequirementsForCountry(
      String countryFrom) async {
    return _internalService.getVisaRequirementsForCountry(countryFrom);
  }

  /// Check if a visa is required for a destination.
  Future<bool> isVisaRequired(String countryFrom, String countryTo) async {
    return _internalService.isVisaRequired(countryFrom, countryTo);
  }

  /// Add a new visa requirement.
  Future<VisaRequirement> addVisaRequirement(
      VisaRequirement requirement) async {
    return _internalService.addVisaRequirement(requirement);
  }

  /// Update a visa requirement.
  Future<VisaRequirement> updateVisaRequirement(
      VisaRequirement requirement) async {
    return _internalService.updateVisaRequirement(requirement);
  }

  /// Delete a visa requirement.
  Future<void> deleteVisaRequirement(String id) async {
    return _internalService.deleteVisaRequirement(id);
  }

  /// Get visa requirements for multiple countries.
  Future<Map<String, VisaRequirement?>> getVisaRequirementsForMultipleCountries(
    String countryFrom,
    List<String> countriesTo,
  ) async {
    final result = <String, VisaRequirement?>{};

    // Process in batches to avoid too many concurrent requests
    const batchSize = 5;
    for (var i = 0; i < countriesTo.length; i += batchSize) {
      final batch = countriesTo.skip(i).take(batchSize);
      final futures = batch.map((countryTo) async {
        final requirement = await getVisaRequirement(countryFrom, countryTo);
        return MapEntry(countryTo, requirement);
      });

      final entries = await Future.wait(futures);
      result.addEntries(entries);
    }

    return result;
  }

  /// Check if a country is visa-free for a specific nationality.
  Future<bool> isVisaFree(String countryFrom, String countryTo) async {
    final requirement = await getVisaRequirement(countryFrom, countryTo);
    if (requirement == null) {
      return false; // Assume visa is required if no information is available
    }

    return requirement.requirementType == VisaRequirementType.noVisaRequired ||
        requirement.requirementType == VisaRequirementType.visaExemption;
  }

  /// Get a list of visa-free countries for a specific nationality.
  Future<List<String>> getVisaFreeCountries(String countryFrom) async {
    final requirements = await getVisaRequirementsForCountry(countryFrom);

    return requirements
        .where((requirement) =>
            requirement.requirementType == VisaRequirementType.noVisaRequired ||
            requirement.requirementType == VisaRequirementType.visaExemption)
        .map((requirement) => requirement.countryTo)
        .toList();
  }

  /// Get a list of countries that require a visa for a specific nationality.
  Future<List<String>> getVisaRequiredCountries(String countryFrom) async {
    final requirements = await getVisaRequirementsForCountry(countryFrom);

    return requirements
        .where((requirement) =>
            requirement.requirementType == VisaRequirementType.visaRequired)
        .map((requirement) => requirement.countryTo)
        .toList();
  }

  /// Get a list of countries that offer visa on arrival for a specific nationality.
  Future<List<String>> getVisaOnArrivalCountries(String countryFrom) async {
    final requirements = await getVisaRequirementsForCountry(countryFrom);

    return requirements
        .where((requirement) =>
            requirement.requirementType == VisaRequirementType.visaOnArrival)
        .map((requirement) => requirement.countryTo)
        .toList();
  }

  /// Get a list of countries that offer e-Visa for a specific nationality.
  Future<List<String>> getEVisaCountries(String countryFrom) async {
    final requirements = await getVisaRequirementsForCountry(countryFrom);

    return requirements
        .where((requirement) =>
            requirement.requirementType == VisaRequirementType.eVisa)
        .map((requirement) => requirement.countryTo)
        .toList();
  }
}
