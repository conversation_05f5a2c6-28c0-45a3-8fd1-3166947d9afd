import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:culture_connect/models/trust_score_model.dart';
import 'package:culture_connect/providers/auth_provider.dart';

/// Service for handling trust score-related operations
class TrustScoreService {
  final FirebaseFirestore _firestore;
  final String _userId;

  /// Creates a new trust score service
  TrustScoreService(this._firestore, this._userId);

  /// Get all trust factors
  Future<List<TrustFactor>> getTrustFactors() async {
    try {
      final snapshot = await _firestore
          .collection('trust_factors')
          .where('isActive', isEqualTo: true)
          .get();

      return snapshot.docs
          .map((doc) => TrustFactor.fromJson({...doc.data(), 'id': doc.id}))
          .toList();
    } catch (e) {
      rethrow;
    }
  }

  /// Get a user's trust score
  Future<TrustScore?> getTrustScore({String? userId}) async {
    try {
      final targetUserId = userId ?? _userId;
      final doc = await _firestore
          .collection('trust_scores')
          .doc(targetUserId)
          .get();

      if (!doc.exists) return null;

      return TrustScore.fromJson({...doc.data()!, 'id': doc.id});
    } catch (e) {
      rethrow;
    }
  }

  /// Get a user's trust level
  Future<TrustLevel> getTrustLevel({String? userId}) async {
    try {
      final score = await getTrustScore(userId: userId);
      if (score == null) return TrustLevel.untrusted;
      return score.trustLevel;
    } catch (e) {
      rethrow;
    }
  }

  /// Check if a user meets a minimum trust level
  Future<bool> meetsMinimumTrustLevel(TrustLevel minimumLevel, {String? userId}) async {
    try {
      final level = await getTrustLevel(userId: userId);
      return level.minScore >= minimumLevel.minScore;
    } catch (e) {
      rethrow;
    }
  }

  /// Get a user's factor score
  Future<int> getFactorScore(TrustFactorType factorType, {String? userId}) async {
    try {
      final score = await getTrustScore(userId: userId);
      if (score == null) return 0;
      return score.getFactorScore(factorType);
    } catch (e) {
      rethrow;
    }
  }

  /// Get trust scores for multiple users
  Future<Map<String, TrustScore>> getTrustScoresForUsers(List<String> userIds) async {
    try {
      if (userIds.isEmpty) return {};

      final result = <String, TrustScore>{};
      
      // Firestore doesn't support 'in' queries with more than 10 items
      // So we need to batch the requests
      for (int i = 0; i < userIds.length; i += 10) {
        final end = (i + 10 < userIds.length) ? i + 10 : userIds.length;
        final batch = userIds.sublist(i, end);
        
        final snapshot = await _firestore
            .collection('trust_scores')
            .where(FieldPath.documentId, whereIn: batch)
            .get();
            
        for (final doc in snapshot.docs) {
          result[doc.id] = TrustScore.fromJson({...doc.data(), 'id': doc.id});
        }
      }
      
      return result;
    } catch (e) {
      rethrow;
    }
  }
}

/// Provider for the trust score service
final trustScoreServiceProvider = Provider<TrustScoreService>((ref) {
  final user = ref.watch(authStateProvider).user;
  if (user == null) {
    throw Exception('User must be logged in to access trust score services');
  }
  
  final firestore = FirebaseFirestore.instance;
  
  return TrustScoreService(firestore, user.id);
});

/// Stream provider for trust factors
final trustFactorsProvider = StreamProvider<List<TrustFactor>>((ref) {
  return FirebaseFirestore.instance
      .collection('trust_factors')
      .where('isActive', isEqualTo: true)
      .snapshots()
      .map((snapshot) => snapshot.docs
          .map((doc) => TrustFactor.fromJson({...doc.data(), 'id': doc.id}))
          .toList());
});

/// Stream provider for a user's trust score
final trustScoreProvider = StreamProvider.family<TrustScore?, String>((ref, userId) {
  return FirebaseFirestore.instance
      .collection('trust_scores')
      .doc(userId)
      .snapshots()
      .map((doc) {
        if (!doc.exists) return null;
        return TrustScore.fromJson({...doc.data()!, 'id': doc.id});
      });
});

/// Provider for the current user's trust score
final currentUserTrustScoreProvider = Provider<AsyncValue<TrustScore?>>((ref) {
  final user = ref.watch(authStateProvider).user;
  if (user == null) return const AsyncValue.data(null);
  
  return ref.watch(trustScoreProvider(user.id));
});

/// Provider for the current user's trust level
final currentUserTrustLevelProvider = Provider<TrustLevel>((ref) {
  final trustScoreValue = ref.watch(currentUserTrustScoreProvider);
  
  return trustScoreValue.when(
    data: (score) => score?.trustLevel ?? TrustLevel.untrusted,
    loading: () => TrustLevel.untrusted,
    error: (_, __) => TrustLevel.untrusted,
  );
});
