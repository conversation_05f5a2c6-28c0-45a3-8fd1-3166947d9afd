import 'dart:async';
import 'dart:convert';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:culture_connect/models/message_model.dart';
import 'package:culture_connect/providers/auth_provider.dart';

/// Enum representing the sync status of messages
enum SyncStatus {
  /// Not currently syncing
  idle,

  /// Syncing in progress
  syncing,

  /// Sync completed successfully
  completed,

  /// Sync failed
  failed,

  /// Sync partially completed (some messages failed)
  partial
}

/// Service for handling offline messaging functionality with enhanced capabilities
/// for local storage, synchronization, and conflict resolution
class OfflineMessageService {
  final FirebaseFirestore _firestore;
  final String _userId;
  final Box<String> _messageBox;
  final Box<String> _pendingMessageBox;
  final Box<String>
      _messageMetadataBox; // For storing additional metadata about messages
  final Box<String> _syncStatusBox; // For tracking sync status
  final Connectivity _connectivity;
  StreamSubscription<ConnectivityResult>? _connectivitySubscription;
  bool _isOnline = true;
  bool _isSyncing = false;

  // Stream controller for sync status updates
  final _syncStatusController = StreamController<SyncStatus>.broadcast();

  // Maximum retry attempts for failed messages
  static const int _maxRetryAttempts = 5;

  // Backoff delay for retries (in seconds)
  static const List<int> _retryDelays = [1, 5, 15, 30, 60];

  /// Creates a new offline message service
  OfflineMessageService(this._firestore, this._userId)
      : _messageBox = Hive.box<String>('messages'),
        _pendingMessageBox = Hive.box<String>('pending_messages'),
        _messageMetadataBox = Hive.box<String>('message_metadata'),
        _syncStatusBox = Hive.box<String>('sync_status'),
        _connectivity = Connectivity() {
    _initConnectivityListener();
    _initializeBoxes();
  }

  /// Initialize Hive boxes if they don't exist
  Future<void> _initializeBoxes() async {
    if (!Hive.isBoxOpen('message_metadata')) {
      await Hive.openBox<String>('message_metadata');
    }
    if (!Hive.isBoxOpen('sync_status')) {
      await Hive.openBox<String>('sync_status');
    }
  }

  /// Initializes the connectivity listener
  void _initConnectivityListener() {
    _connectivitySubscription =
        _connectivity.onConnectivityChanged.listen((result) {
      final wasOnline = _isOnline;
      _isOnline = result != ConnectivityResult.none;

      // If we just came back online, sync pending messages
      if (!wasOnline && _isOnline) {
        syncPendingMessages();
      }
    });

    // Check initial connectivity
    _connectivity.checkConnectivity().then((result) {
      _isOnline = result != ConnectivityResult.none;
    });
  }

  /// Disposes of resources
  void dispose() {
    _connectivitySubscription?.cancel();
    _syncStatusController.close();
  }

  /// Stream of sync status updates
  Stream<SyncStatus> get syncStatusStream => _syncStatusController.stream;

  /// Checks if the device is online
  bool get isOnline => _isOnline;

  /// Saves a message locally with metadata
  Future<void> saveMessageLocally(MessageModel message) async {
    final key = '${message.chatId}_${message.id}';
    await _messageBox.put(key, jsonEncode(message.toJson()));

    // Save metadata
    await _saveMessageMetadata(message.chatId, message.id, {
      'lastUpdated': DateTime.now().toIso8601String(),
      'retryCount': 0,
      'lastError': '',
    });
  }

  /// Gets a message from local storage
  MessageModel? getMessageLocally(String chatId, String messageId) {
    final key = '${chatId}_$messageId';
    final json = _messageBox.get(key);
    if (json == null) return null;

    try {
      final Map<String, dynamic> messageData = jsonDecode(json);
      return MessageModel.fromJson(messageData);
    } catch (e) {
      debugPrint('Error parsing message: $e');
      return null;
    }
  }

  /// Saves metadata for a message
  Future<void> _saveMessageMetadata(
      String chatId, String messageId, Map<String, dynamic> metadata) async {
    final key = '${chatId}_$messageId';

    // Get existing metadata or create new
    Map<String, dynamic> existingMetadata = {};
    final existingJson = _messageMetadataBox.get(key);
    if (existingJson != null) {
      try {
        existingMetadata = jsonDecode(existingJson) as Map<String, dynamic>;
      } catch (e) {
        debugPrint('Error parsing message metadata: $e');
      }
    }

    // Merge with new metadata
    final updatedMetadata = {
      ...existingMetadata,
      ...metadata,
    };

    await _messageMetadataBox.put(key, jsonEncode(updatedMetadata));
  }

  /// Gets metadata for a message
  Map<String, dynamic> _getMessageMetadata(String chatId, String messageId) {
    final key = '${chatId}_$messageId';
    final json = _messageMetadataBox.get(key);
    if (json == null) return {};

    try {
      return jsonDecode(json) as Map<String, dynamic>;
    } catch (e) {
      debugPrint('Error parsing message metadata: $e');
      return {};
    }
  }

  /// Increments the retry count for a message
  Future<int> _incrementRetryCount(String chatId, String messageId) async {
    final metadata = _getMessageMetadata(chatId, messageId);
    final retryCount = (metadata['retryCount'] as int?) ?? 0;
    final newRetryCount = retryCount + 1;

    await _saveMessageMetadata(chatId, messageId, {
      'retryCount': newRetryCount,
      'lastRetry': DateTime.now().toIso8601String(),
    });

    return newRetryCount;
  }

  /// Records an error for a message
  Future<void> _recordMessageError(
      String chatId, String messageId, String error) async {
    await _saveMessageMetadata(chatId, messageId, {
      'lastError': error,
      'lastErrorTime': DateTime.now().toIso8601String(),
    });
  }

  /// Gets all messages for a chat from local storage
  List<MessageModel> getMessagesForChat(String chatId) {
    final messages = <MessageModel>[];

    for (final key in _messageBox.keys) {
      if (key.startsWith('${chatId}_')) {
        final json = _messageBox.get(key);
        if (json != null) {
          try {
            final Map<String, dynamic> messageData = jsonDecode(json);
            messages.add(MessageModel.fromJson(messageData));
          } catch (e) {
            debugPrint('Error parsing message: $e');
          }
        }
      }
    }

    // Sort by timestamp
    messages.sort((a, b) => a.timestamp.compareTo(b.timestamp));
    return messages;
  }

  /// Adds a message to the pending queue
  Future<void> addPendingMessage(MessageModel message) async {
    final key = '${message.chatId}_${message.id}';
    await _pendingMessageBox.put(key, jsonEncode(message.toJson()));
  }

  /// Gets all pending messages
  List<MessageModel> getPendingMessages() {
    final messages = <MessageModel>[];

    for (final key in _pendingMessageBox.keys) {
      final json = _pendingMessageBox.get(key);
      if (json != null) {
        try {
          final Map<String, dynamic> messageData = jsonDecode(json);
          messages.add(MessageModel.fromJson(messageData));
        } catch (e) {
          debugPrint('Error parsing pending message: $e');
        }
      }
    }

    return messages;
  }

  /// Removes a message from the pending queue
  Future<void> removePendingMessage(String chatId, String messageId) async {
    final key = '${chatId}_$messageId';
    await _pendingMessageBox.delete(key);
  }

  /// Syncs pending messages with the server
  Future<void> syncPendingMessages() async {
    if (!_isOnline) return;
    if (_isSyncing) return;

    try {
      _isSyncing = true;
      _syncStatusController.add(SyncStatus.syncing);

      // Update sync status
      await _syncStatusBox.put(
          'lastSyncAttempt', DateTime.now().toIso8601String());

      final pendingMessages = getPendingMessages();
      if (pendingMessages.isEmpty) {
        _syncStatusController.add(SyncStatus.completed);
        await _syncStatusBox.put(
            'lastSyncStatus', SyncStatus.completed.toString());
        await _syncStatusBox.put(
            'lastSuccessfulSync', DateTime.now().toIso8601String());
        _isSyncing = false;
        return;
      }

      int successCount = 0;
      int failureCount = 0;

      for (final message in pendingMessages) {
        try {
          // Check retry count
          final metadata = _getMessageMetadata(message.chatId, message.id);
          final retryCount = (metadata['retryCount'] as int?) ?? 0;

          if (retryCount >= _maxRetryAttempts) {
            // Too many retries, mark as failed
            final failedMessage =
                message.copyWith(status: MessageStatus.failed);
            await saveMessageLocally(failedMessage);
            await _recordMessageError(
                message.chatId, message.id, 'Max retry attempts exceeded');
            failureCount++;
            continue;
          }

          // Update retry count
          final newRetryCount =
              await _incrementRetryCount(message.chatId, message.id);

          // Update message status to sending
          final updatedMessage =
              message.copyWith(status: MessageStatus.sending);
          await saveMessageLocally(updatedMessage);

          // Send to Firestore
          await _firestore.collection('messages').add(message.toJson());

          // Update chat with last message info
          if (!message.isGroupMessage) {
            await _firestore.collection('chats').doc(message.chatId).update({
              'lastMessageAt': message.timestamp,
              'lastMessageText': message.text,
              'lastMessageSenderId': message.senderId,
            });
          } else {
            await _firestore
                .collection('group_chats')
                .doc(message.chatId)
                .update({
              'lastMessageAt': message.timestamp,
              'lastMessageText': message.text,
              'lastMessageSenderId': message.senderId,
            });
          }

          // Update message status to sent
          final sentMessage =
              updatedMessage.copyWith(status: MessageStatus.sent);
          await saveMessageLocally(sentMessage);

          // Save success metadata
          await _saveMessageMetadata(message.chatId, message.id, {
            'syncedAt': DateTime.now().toIso8601String(),
            'syncStatus': 'success',
          });

          // Remove from pending queue
          await removePendingMessage(message.chatId, message.id);
          successCount++;
        } catch (e) {
          debugPrint('Error syncing message: $e');
          // Record error
          await _recordMessageError(message.chatId, message.id, e.toString());
          failureCount++;

          // Keep in pending queue to try again later with exponential backoff
          final metadata = _getMessageMetadata(message.chatId, message.id);
          final retryCount = (metadata['retryCount'] as int?) ?? 0;

          // Only retry if we haven't exceeded max attempts
          if (retryCount < _maxRetryAttempts) {
            // Schedule retry with backoff
            final delaySeconds =
                _retryDelays[retryCount.clamp(0, _retryDelays.length - 1)];
            debugPrint(
                'Scheduling retry for message ${message.id} in $delaySeconds seconds');
          }
        }
      }

      // Update sync status
      final status = failureCount == 0
          ? SyncStatus.completed
          : (successCount > 0 ? SyncStatus.partial : SyncStatus.failed);

      _syncStatusController.add(status);
      await _syncStatusBox.put('lastSyncStatus', status.toString());

      if (successCount > 0) {
        await _syncStatusBox.put(
            'lastSuccessfulSync', DateTime.now().toIso8601String());
      }

      await _syncStatusBox.put(
          'lastSyncStats',
          jsonEncode({
            'total': pendingMessages.length,
            'success': successCount,
            'failure': failureCount,
          }));
    } catch (e) {
      debugPrint('Error during sync: $e');
      _syncStatusController.add(SyncStatus.failed);
      await _syncStatusBox.put('lastSyncStatus', SyncStatus.failed.toString());
      await _syncStatusBox.put('lastSyncError', e.toString());
    } finally {
      _isSyncing = false;
    }
  }

  /// Sends a message, handling offline scenarios
  Future<void> sendMessage(MessageModel message) async {
    // Save locally first with metadata
    await saveMessageLocally(message);

    if (_isOnline) {
      try {
        // Update message status to sending
        final updatedMessage = message.copyWith(status: MessageStatus.sending);
        await saveMessageLocally(updatedMessage);

        // Save sending metadata
        await _saveMessageMetadata(message.chatId, message.id, {
          'sendAttemptAt': DateTime.now().toIso8601String(),
        });

        // Send to Firestore
        final docRef =
            await _firestore.collection('messages').add(message.toJson());

        // Save server ID
        await _saveMessageMetadata(message.chatId, message.id, {
          'serverId': docRef.id,
        });

        // Update chat with last message info
        if (!message.isGroupMessage) {
          await _firestore.collection('chats').doc(message.chatId).update({
            'lastMessageAt': message.timestamp,
            'lastMessageText': message.text,
            'lastMessageSenderId': message.senderId,
          });
        } else {
          await _firestore
              .collection('group_chats')
              .doc(message.chatId)
              .update({
            'lastMessageAt': message.timestamp,
            'lastMessageText': message.text,
            'lastMessageSenderId': message.senderId,
          });
        }

        // Update message status to sent
        final sentMessage = updatedMessage.copyWith(status: MessageStatus.sent);
        await saveMessageLocally(sentMessage);

        // Save success metadata
        await _saveMessageMetadata(message.chatId, message.id, {
          'sentAt': DateTime.now().toIso8601String(),
          'sendStatus': 'success',
        });
      } catch (e) {
        debugPrint('Error sending message: $e');

        // Record error
        await _recordMessageError(message.chatId, message.id, e.toString());

        // Update message status to pending
        final pendingMessage = message.copyWith(status: MessageStatus.pending);
        await saveMessageLocally(pendingMessage);

        // Add to pending queue
        await addPendingMessage(pendingMessage);
      }
    } else {
      // Update message status to pending
      final pendingMessage = message.copyWith(status: MessageStatus.pending);
      await saveMessageLocally(pendingMessage);

      // Add to pending queue
      await addPendingMessage(pendingMessage);

      // Save offline metadata
      await _saveMessageMetadata(message.chatId, message.id, {
        'queuedOfflineAt': DateTime.now().toIso8601String(),
        'sendStatus': 'queued',
      });
    }
  }

  /// Gets the sync status
  Future<Map<String, dynamic>> getSyncStatus() async {
    final result = <String, dynamic>{};

    for (final key in _syncStatusBox.keys) {
      final value = _syncStatusBox.get(key);
      if (value != null) {
        if (key == 'lastSyncStats') {
          try {
            result[key] = jsonDecode(value);
          } catch (e) {
            result[key] = value;
          }
        } else {
          result[key] = value;
        }
      }
    }

    // Add current status
    result['isSyncing'] = _isSyncing;
    result['isOnline'] = _isOnline;
    result['pendingCount'] = getPendingMessages().length;

    return result;
  }

  /// Manually trigger a sync of pending messages
  Future<void> manualSync() async {
    if (_isSyncing) {
      debugPrint('Sync already in progress');
      return;
    }

    return syncPendingMessages();
  }

  /// Clears all local messages for a chat
  Future<void> clearLocalMessages(String chatId) async {
    final keysToDelete = <String>[];

    for (final key in _messageBox.keys) {
      if (key.startsWith('${chatId}_')) {
        keysToDelete.add(key);
      }
    }

    for (final key in keysToDelete) {
      await _messageBox.delete(key);
    }
  }

  /// Clears all pending messages for a chat
  Future<void> clearPendingMessages(String chatId) async {
    final keysToDelete = <String>[];

    for (final key in _pendingMessageBox.keys) {
      if (key.startsWith('${chatId}_')) {
        keysToDelete.add(key);
      }
    }

    for (final key in keysToDelete) {
      await _pendingMessageBox.delete(key);
    }
  }
}

/// Provider for the offline message service
final offlineMessageServiceProvider = Provider<OfflineMessageService>((ref) {
  final user = ref.watch(authStateProvider).user;
  if (user == null) {
    throw Exception('User must be logged in to access offline message service');
  }

  final firestore = FirebaseFirestore.instance;
  final service = OfflineMessageService(firestore, user.id);

  ref.onDispose(() {
    service.dispose();
  });

  return service;
});

/// Provider for checking if the device is online
final isOnlineProvider = Provider<bool>((ref) {
  return ref.watch(offlineMessageServiceProvider).isOnline;
});

/// Provider for the sync status stream
final syncStatusStreamProvider = StreamProvider<SyncStatus>((ref) {
  return ref.watch(offlineMessageServiceProvider).syncStatusStream;
});

/// Provider for the current sync status
final syncStatusProvider = FutureProvider<Map<String, dynamic>>((ref) {
  return ref.watch(offlineMessageServiceProvider).getSyncStatus();
});
