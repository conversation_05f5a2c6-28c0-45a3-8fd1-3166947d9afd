import 'dart:convert';
import 'dart:io';
import 'dart:math' show sin, cos, sqrt, atan2, pi;

import 'package:path_provider/path_provider.dart';
import 'package:shared_preferences/shared_preferences.dart';

import 'package:culture_connect/models/ar/ar_content_marker.dart';
import 'package:culture_connect/models/offline/offline_content.dart';
import 'package:culture_connect/services/logging_service.dart';
import 'package:culture_connect/services/error_handling_service.dart';
import 'package:culture_connect/services/offline_mode_service.dart';

/// Service for managing AR content
class ARContentService {
  /// The shared preferences instance
  final SharedPreferences _preferences;

  /// The logging service
  final LoggingService _loggingService;

  /// The error handling service
  final ErrorHandlingService? _errorHandlingService;

  /// The offline mode service
  final OfflineModeService? _offlineModeService;

  /// Directory for storing AR content
  late Directory _arContentDirectory;

  /// Whether the service has been initialized
  bool _isInitialized = false;

  /// Creates a new AR content service
  ARContentService(
    this._preferences,
    this._loggingService,
    this._errorHandlingService,
    this._offlineModeService,
  );

  /// Initialize the service
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // Initialize the AR content directory
      final appDir = await getApplicationDocumentsDirectory();
      _arContentDirectory = Directory('${appDir.path}/ar_content');

      if (!await _arContentDirectory.exists()) {
        await _arContentDirectory.create(recursive: true);
      }

      _isInitialized = true;
      _loggingService.debug('ARContentService', 'Initialized successfully');
    } catch (e, stackTrace) {
      _loggingService.error(
          'ARContentService', 'Failed to initialize', e, stackTrace);
      if (_errorHandlingService != null) {
        await _errorHandlingService!.handleError(
          error: e,
          context: 'ARContentService.initialize',
          stackTrace: stackTrace,
        );
      }
      rethrow;
    }
  }

  /// Get all AR content markers
  Future<List<ARContentMarker>> getAllARContentMarkers() async {
    await initialize();

    try {
      final markerList = _preferences.getStringList('ar_content_markers') ?? [];

      final markers = <ARContentMarker>[];
      for (final markerJson in markerList) {
        try {
          final marker = ARContentMarker.fromJson(jsonDecode(markerJson));
          markers.add(marker);
        } catch (e) {
          _loggingService.error(
              'ARContentService', 'Failed to parse AR content marker', e);
        }
      }

      return markers;
    } catch (e, stackTrace) {
      _loggingService.error('ARContentService',
          'Failed to get AR content markers', e, stackTrace);
      if (_errorHandlingService != null) {
        await _errorHandlingService!.handleError(
          error: e,
          context: 'ARContentService.getAllARContentMarkers',
          stackTrace: stackTrace,
        );
      }
      return [];
    }
  }

  /// Get an AR content marker by ID
  Future<ARContentMarker?> getARContentMarker(String id) async {
    await initialize();

    try {
      final markerJson = _preferences.getString('ar_content_marker_$id');
      if (markerJson == null) return null;

      return ARContentMarker.fromJson(jsonDecode(markerJson));
    } catch (e, stackTrace) {
      _loggingService.error(
          'ARContentService', 'Failed to get AR content marker', e, stackTrace);
      if (_errorHandlingService != null) {
        await _errorHandlingService!.handleError(
          error: e,
          context: 'ARContentService.getARContentMarker',
          stackTrace: stackTrace,
        );
      }
      return null;
    }
  }

  /// Create a new AR content marker
  Future<ARContentMarker> createARContentMarker(ARContentMarker marker) async {
    await initialize();

    try {
      // Save the marker
      await _saveARContentMarker(marker);

      // Add to the marker list
      final markerList = _preferences.getStringList('ar_content_markers') ?? [];
      markerList.add(jsonEncode(marker.toJson()));
      await _preferences.setStringList('ar_content_markers', markerList);

      return marker;
    } catch (e, stackTrace) {
      _loggingService.error('ARContentService',
          'Failed to create AR content marker', e, stackTrace);
      if (_errorHandlingService != null) {
        await _errorHandlingService!.handleError(
          error: e,
          context: 'ARContentService.createARContentMarker',
          stackTrace: stackTrace,
        );
      }
      rethrow;
    }
  }

  /// Update an AR content marker
  Future<ARContentMarker> updateARContentMarker(ARContentMarker marker) async {
    await initialize();

    try {
      // Get the existing marker
      final existingMarker = await getARContentMarker(marker.id);
      if (existingMarker == null) {
        throw Exception('AR content marker not found');
      }

      // Update the marker
      final updatedMarker = marker.copyWith(
        updatedAt: DateTime.now(),
      );

      // Save the marker
      await _saveARContentMarker(updatedMarker);

      // Update the marker list
      final markerList = _preferences.getStringList('ar_content_markers') ?? [];
      final index = markerList.indexWhere((m) {
        try {
          final decoded = jsonDecode(m);
          return decoded['id'] == marker.id;
        } catch (_) {
          return false;
        }
      });

      if (index != -1) {
        markerList[index] = jsonEncode(updatedMarker.toJson());
        await _preferences.setStringList('ar_content_markers', markerList);
      }

      return updatedMarker;
    } catch (e, stackTrace) {
      _loggingService.error('ARContentService',
          'Failed to update AR content marker', e, stackTrace);
      if (_errorHandlingService != null) {
        await _errorHandlingService!.handleError(
          error: e,
          context: 'ARContentService.updateARContentMarker',
          stackTrace: stackTrace,
        );
      }
      rethrow;
    }
  }

  /// Delete an AR content marker
  Future<bool> deleteARContentMarker(String id) async {
    await initialize();

    try {
      // Remove the marker
      await _preferences.remove('ar_content_marker_$id');

      // Remove from the marker list
      final markerList = _preferences.getStringList('ar_content_markers') ?? [];
      final filteredList = markerList.where((m) {
        try {
          final decoded = jsonDecode(m);
          return decoded['id'] != id;
        } catch (_) {
          return true;
        }
      }).toList();

      await _preferences.setStringList('ar_content_markers', filteredList);

      // Remove the content file if it exists
      final contentFile = File('${_arContentDirectory.path}/$id');
      if (await contentFile.exists()) {
        await contentFile.delete();
      }

      return true;
    } catch (e, stackTrace) {
      _loggingService.error('ARContentService',
          'Failed to delete AR content marker', e, stackTrace);
      if (_errorHandlingService != null) {
        await _errorHandlingService!.handleError(
          error: e,
          context: 'ARContentService.deleteARContentMarker',
          stackTrace: stackTrace,
        );
      }
      return false;
    }
  }

  /// Get AR content markers for a location
  Future<List<ARContentMarker>> getARContentMarkersForLocation(
      String location) async {
    await initialize();

    try {
      final allMarkers = await getAllARContentMarkers();
      return allMarkers.where((m) => m.location == location).toList();
    } catch (e, stackTrace) {
      _loggingService.error('ARContentService',
          'Failed to get AR content markers for location', e, stackTrace);
      if (_errorHandlingService != null) {
        await _errorHandlingService!.handleError(
          error: e,
          context: 'ARContentService.getARContentMarkersForLocation',
          stackTrace: stackTrace,
        );
      }
      return [];
    }
  }

  /// Get AR content markers for coordinates
  Future<List<ARContentMarker>> getARContentMarkersForCoordinates(
      double latitude, double longitude, double radiusInMeters) async {
    await initialize();

    try {
      final allMarkers = await getAllARContentMarkers();

      return allMarkers.where((m) {
        if (m.coordinates == null) return false;

        final markerLat = m.coordinates!['lat']!;
        final markerLng = m.coordinates!['lng']!;

        // Calculate distance using Haversine formula
        const earthRadius = 6371000; // in meters
        final dLat = _degreesToRadians(markerLat - latitude);
        final dLng = _degreesToRadians(markerLng - longitude);

        final a = sin(dLat / 2) * sin(dLat / 2) +
            cos(_degreesToRadians(latitude)) *
                cos(_degreesToRadians(markerLat)) *
                sin(dLng / 2) *
                sin(dLng / 2);

        final c = 2 * atan2(sqrt(a), sqrt(1 - a));
        final distance = earthRadius * c;

        return distance <= radiusInMeters;
      }).toList();
    } catch (e, stackTrace) {
      _loggingService.error('ARContentService',
          'Failed to get AR content markers for coordinates', e, stackTrace);
      if (_errorHandlingService != null) {
        await _errorHandlingService!.handleError(
          error: e,
          context: 'ARContentService.getARContentMarkersForCoordinates',
          stackTrace: stackTrace,
        );
      }
      return [];
    }
  }

  /// Download AR content for offline use
  Future<bool> downloadARContentForOfflineUse(String markerId) async {
    await initialize();

    try {
      // Get the marker
      final marker = await getARContentMarker(markerId);
      if (marker == null) {
        throw Exception('AR content marker not found');
      }

      // Check if the offline mode service is available
      if (_offlineModeService == null) {
        throw Exception('Offline mode service not available');
      }

      // Create offline content
      final offlineContent = OfflineContent(
        id: marker.id,
        title: marker.title,
        contentType: 'ar_content',
        contentSize: marker.contentSize,
      );

      // Add to offline content
      final result =
          await _offlineModeService!.addContentForOfflineUse(offlineContent);

      if (result) {
        // Update the marker
        final updatedMarker = marker.copyWith(
          isAvailableOffline: true,
        );

        await updateARContentMarker(updatedMarker);
      }

      return result;
    } catch (e, stackTrace) {
      _loggingService.error('ARContentService',
          'Failed to download AR content for offline use', e, stackTrace);
      if (_errorHandlingService != null) {
        await _errorHandlingService!.handleError(
          error: e,
          context: 'ARContentService.downloadARContentForOfflineUse',
          stackTrace: stackTrace,
        );
      }
      return false;
    }
  }

  /// Remove AR content from offline storage
  Future<bool> removeARContentFromOfflineStorage(String markerId) async {
    await initialize();

    try {
      // Get the marker
      final marker = await getARContentMarker(markerId);
      if (marker == null) {
        throw Exception('AR content marker not found');
      }

      // Check if the offline mode service is available
      if (_offlineModeService == null) {
        throw Exception('Offline mode service not available');
      }

      // Remove from offline content
      final result = await _offlineModeService!.removeOfflineContent(marker.id);

      if (result) {
        // Update the marker
        final updatedMarker = marker.copyWith(
          isAvailableOffline: false,
        );

        await updateARContentMarker(updatedMarker);
      }

      return result;
    } catch (e, stackTrace) {
      _loggingService.error('ARContentService',
          'Failed to remove AR content from offline storage', e, stackTrace);
      if (_errorHandlingService != null) {
        await _errorHandlingService!.handleError(
          error: e,
          context: 'ARContentService.removeARContentFromOfflineStorage',
          stackTrace: stackTrace,
        );
      }
      return false;
    }
  }

  /// Save an AR content marker
  Future<void> _saveARContentMarker(ARContentMarker marker) async {
    await _preferences.setString(
        'ar_content_marker_${marker.id}', jsonEncode(marker.toJson()));
  }

  /// Convert degrees to radians
  double _degreesToRadians(double degrees) {
    return degrees * (pi / 180);
  }
}
