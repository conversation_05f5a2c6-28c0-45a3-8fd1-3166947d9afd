import 'dart:io';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:uuid/uuid.dart';
import 'package:culture_connect/models/review.dart';
import 'package:culture_connect/models/booking.dart';
import 'package:culture_connect/services/logging_service.dart';

/// Provider for the review service
final reviewServiceProvider = Provider<ReviewService>((ref) {
  final firestore = FirebaseFirestore.instance;
  final storage = FirebaseStorage.instance;
  final loggingService = ref.watch(loggingServiceProvider);
  return ReviewService(firestore, storage, loggingService);
});

/// Provider for reviews of an experience
final experienceReviewsProvider =
    FutureProvider.family<List<ReviewModel>, String>((ref, experienceId) {
  final reviewService = ref.watch(reviewServiceProvider);
  return reviewService.getReviewsForExperience(experienceId);
});

/// Provider for reviews by a user
final userReviewsProvider =
    FutureProvider.family<List<ReviewModel>, String>((ref, userId) {
  final reviewService = ref.watch(reviewServiceProvider);
  return reviewService.getReviewsByUser(userId);
});

/// Provider for the current review sort option
final reviewSortOptionProvider = StateProvider<ReviewSortOption>((ref) {
  return ReviewSortOption.newest;
});

/// Provider for sorted reviews of an experience
final sortedExperienceReviewsProvider =
    Provider.family<AsyncValue<List<ReviewModel>>, String>((ref, experienceId) {
  final reviewsAsync = ref.watch(experienceReviewsProvider(experienceId));
  final sortOption = ref.watch(reviewSortOptionProvider);

  return reviewsAsync.whenData((reviews) {
    final sortedReviews = List<ReviewModel>.from(reviews);

    switch (sortOption) {
      case ReviewSortOption.newest:
        sortedReviews.sort((a, b) => b.datePosted.compareTo(a.datePosted));
        break;
      case ReviewSortOption.oldest:
        sortedReviews.sort((a, b) => a.datePosted.compareTo(b.datePosted));
        break;
      case ReviewSortOption.highestRated:
        sortedReviews.sort((a, b) => b.rating.compareTo(a.rating));
        break;
      case ReviewSortOption.lowestRated:
        sortedReviews.sort((a, b) => a.rating.compareTo(b.rating));
        break;
      case ReviewSortOption.mostHelpful:
        sortedReviews.sort((a, b) => b.helpfulCount.compareTo(a.helpfulCount));
        break;
      case ReviewSortOption.withPhotos:
        sortedReviews.sort((a, b) {
          if (a.photoUrls.isNotEmpty && b.photoUrls.isEmpty) return -1;
          if (a.photoUrls.isEmpty && b.photoUrls.isNotEmpty) return 1;
          return b.datePosted.compareTo(a.datePosted); // Secondary sort by date
        });
        break;
    }

    return sortedReviews;
  });
});

/// Provider for rating distribution of an experience
final ratingDistributionProvider =
    FutureProvider.family<Map<int, int>, String>((ref, experienceId) async {
  final reviewsAsync =
      await ref.watch(experienceReviewsProvider(experienceId).future);

  final distribution = {
    5: 0,
    4: 0,
    3: 0,
    2: 0,
    1: 0,
  };

  for (final review in reviewsAsync) {
    final rating = review.rating.round();
    if (rating >= 1 && rating <= 5) {
      distribution[rating] = (distribution[rating] ?? 0) + 1;
    }
  }

  return distribution;
});

/// Service for managing reviews
class ReviewService {
  final FirebaseFirestore _firestore;
  final FirebaseStorage _storage;
  final LoggingService _loggingService;
  final Uuid _uuid = const Uuid();

  // In-memory storage for reviews (for backward compatibility)
  final List<Review> _reviews = [];

  /// Creates a new review service
  ReviewService(this._firestore, this._storage, this._loggingService);

  /// Get reviews for an experience
  Future<List<ReviewModel>> getReviewsForExperience(String experienceId) async {
    try {
      _loggingService.debug(
          'ReviewService', 'Getting reviews for experience: $experienceId');

      final snapshot = await _firestore
          .collection('reviews')
          .where('experienceId', isEqualTo: experienceId)
          .where('isPublished', isEqualTo: true)
          .get();

      return snapshot.docs
          .map((doc) => ReviewModel.fromJson({...doc.data(), 'id': doc.id}))
          .toList();
    } catch (e, stackTrace) {
      _loggingService.error(
        'ReviewService',
        'Error getting reviews for experience: $experienceId',
        e,
        stackTrace,
      );

      // Fallback to in-memory reviews for backward compatibility
      await Future.delayed(const Duration(milliseconds: 500));

      // Convert old Review model to new ReviewModel
      return _reviews
          .where((review) =>
              review.experienceId == experienceId && review.isPublished)
          .map((review) => ReviewModel(
                id: review.id,
                experienceId: review.experienceId,
                bookingId: review.bookingId,
                userId: review.userId,
                userName: 'User', // Default value
                rating: review.rating,
                content: review.comment,
                datePosted: review.createdAt,
                dateUpdated: review.updatedAt,
                photoUrls: review.photoUrls,
                isPublished: review.isPublished,
              ))
          .toList();
    }
  }

  /// Get reviews by a user
  Future<List<ReviewModel>> getReviewsByUser(String userId) async {
    try {
      _loggingService.debug(
          'ReviewService', 'Getting reviews by user: $userId');

      final snapshot = await _firestore
          .collection('reviews')
          .where('userId', isEqualTo: userId)
          .get();

      return snapshot.docs
          .map((doc) => ReviewModel.fromJson({...doc.data(), 'id': doc.id}))
          .toList();
    } catch (e, stackTrace) {
      _loggingService.error(
        'ReviewService',
        'Error getting reviews by user: $userId',
        e,
        stackTrace,
      );

      // Fallback to in-memory reviews for backward compatibility
      await Future.delayed(const Duration(milliseconds: 500));

      // Convert old Review model to new ReviewModel
      return _reviews
          .where((review) => review.userId == userId)
          .map((review) => ReviewModel(
                id: review.id,
                experienceId: review.experienceId,
                bookingId: review.bookingId,
                userId: review.userId,
                userName: 'User', // Default value
                rating: review.rating,
                content: review.comment,
                datePosted: review.createdAt,
                dateUpdated: review.updatedAt,
                photoUrls: review.photoUrls,
                isPublished: review.isPublished,
              ))
          .toList();
    }
  }

  /// Get review for a booking
  Future<Review?> getReviewForBooking(String bookingId) async {
    // TODO: Implement actual backend call
    // For now, return in-memory review

    // Simulate network delay
    await Future.delayed(const Duration(milliseconds: 500));

    try {
      return _reviews.firstWhere((review) => review.bookingId == bookingId);
    } catch (e) {
      return null; // No review found
    }
  }

  /// Create a new review
  Future<ReviewModel> createReview({
    required String experienceId,
    required String bookingId,
    required String userId,
    required String userName,
    String? userProfileImageUrl,
    required double rating,
    required String content,
    List<File> photos = const [],
    List<String> tags = const [],
  }) async {
    try {
      _loggingService.debug(
        'ReviewService',
        'Creating review for experience: $experienceId by user: $userId',
      );

      // Check if user has already reviewed this experience
      final existingReviews = await getReviewsByUser(userId);
      final hasReviewed =
          existingReviews.any((r) => r.experienceId == experienceId);

      if (hasReviewed) {
        throw Exception('You have already reviewed this experience');
      }

      // Upload photos if any
      final photoUrls = <String>[];
      if (photos.isNotEmpty) {
        for (final photo in photos) {
          final fileName =
              '${DateTime.now().millisecondsSinceEpoch}_${photo.path.split('/').last}';
          final storageRef = _storage
              .ref()
              .child('review_photos')
              .child(experienceId)
              .child(userId)
              .child(fileName);

          final uploadTask = storageRef.putFile(photo);
          final snapshot = await uploadTask.whenComplete(() {});

          final photoUrl = await snapshot.ref.getDownloadURL();
          photoUrls.add(photoUrl);
        }
      }

      // Create review document
      final reviewId = _uuid.v4();
      final now = DateTime.now();
      final review = ReviewModel(
        id: reviewId,
        experienceId: experienceId,
        bookingId: bookingId,
        userId: userId,
        userName: userName,
        userProfileImageUrl: userProfileImageUrl,
        rating: rating,
        content: content,
        datePosted: now,
        photoUrls: photoUrls,
        isVerified: true,
        tags: tags,
      );

      // Save to Firestore
      await _firestore.collection('reviews').doc(reviewId).set(review.toJson());

      // Update experience rating
      await _updateExperienceRating(experienceId);

      return review;
    } catch (e, stackTrace) {
      _loggingService.error(
        'ReviewService',
        'Error creating review',
        e,
        stackTrace,
      );

      // Fallback to in-memory storage for backward compatibility
      final now = DateTime.now();
      final oldReview = Review(
        id: 'review-${now.millisecondsSinceEpoch}',
        experienceId: experienceId,
        bookingId: bookingId,
        userId: userId,
        rating: rating,
        comment: content,
        photoUrls: photos
            .map((p) => 'https://example.com/photo_${p.path.split('/').last}')
            .toList(),
        createdAt: now,
        updatedAt: now,
      );

      // Add to in-memory storage
      _reviews.add(oldReview);

      // Convert to new model
      return ReviewModel(
        id: oldReview.id,
        experienceId: oldReview.experienceId,
        bookingId: oldReview.bookingId,
        userId: oldReview.userId,
        userName: userName,
        userProfileImageUrl: userProfileImageUrl,
        rating: oldReview.rating,
        content: oldReview.comment,
        datePosted: oldReview.createdAt,
        dateUpdated: oldReview.updatedAt,
        photoUrls: oldReview.photoUrls,
        isPublished: oldReview.isPublished,
        tags: tags,
      );
    }
  }

  /// Update an existing review
  Future<ReviewModel> updateReview({
    required String reviewId,
    double? rating,
    String? content,
    List<File>? newPhotos,
    List<String>? photoUrlsToKeep,
    List<String>? tags,
  }) async {
    try {
      _loggingService.debug('ReviewService', 'Updating review: $reviewId');

      // Get the existing review
      final docSnapshot =
          await _firestore.collection('reviews').doc(reviewId).get();
      if (!docSnapshot.exists) {
        throw Exception('Review not found');
      }

      final existingReview =
          ReviewModel.fromJson({...docSnapshot.data()!, 'id': reviewId});

      // Process photos
      List<String> updatedPhotoUrls =
          photoUrlsToKeep ?? existingReview.photoUrls;

      if (newPhotos != null && newPhotos.isNotEmpty) {
        for (final photo in newPhotos) {
          final fileName =
              '${DateTime.now().millisecondsSinceEpoch}_${photo.path.split('/').last}';
          final storageRef = _storage
              .ref()
              .child('review_photos')
              .child(existingReview.experienceId)
              .child(existingReview.userId)
              .child(fileName);

          final uploadTask = storageRef.putFile(photo);
          final snapshot = await uploadTask.whenComplete(() {});

          final photoUrl = await snapshot.ref.getDownloadURL();
          updatedPhotoUrls.add(photoUrl);
        }
      }

      // Update review
      final updatedReview = existingReview.copyWith(
        rating: rating,
        content: content,
        photoUrls: updatedPhotoUrls,
        dateUpdated: DateTime.now(),
        tags: tags,
      );

      // Save to Firestore
      await _firestore
          .collection('reviews')
          .doc(reviewId)
          .update(updatedReview.toJson());

      // Update experience rating
      await _updateExperienceRating(existingReview.experienceId);

      return updatedReview;
    } catch (e, stackTrace) {
      _loggingService.error(
        'ReviewService',
        'Error updating review',
        e,
        stackTrace,
      );

      // Fallback to in-memory storage for backward compatibility
      final index = _reviews.indexWhere((r) => r.id == reviewId);
      if (index == -1) {
        throw Exception('Review not found');
      }

      final existingReview = _reviews[index];

      // Process photos
      List<String> updatedPhotoUrls =
          photoUrlsToKeep ?? existingReview.photoUrls;

      if (newPhotos != null && newPhotos.isNotEmpty) {
        for (var i = 0; i < newPhotos.length; i++) {
          // Simulate photo upload
          await Future.delayed(const Duration(milliseconds: 300));
          updatedPhotoUrls.add('https://example.com/photo_updated_$i.jpg');
        }
      }

      final now = DateTime.now();
      final updatedReview = existingReview.copyWith(
        rating: rating,
        comment: content,
        photoUrls: updatedPhotoUrls,
        updatedAt: now,
      );

      // Update in-memory storage
      _reviews[index] = updatedReview;

      // Convert to new model
      return ReviewModel(
        id: updatedReview.id,
        experienceId: updatedReview.experienceId,
        bookingId: updatedReview.bookingId,
        userId: updatedReview.userId,
        userName: 'User', // Default value
        rating: updatedReview.rating,
        content: updatedReview.comment,
        datePosted: updatedReview.createdAt,
        dateUpdated: updatedReview.updatedAt,
        photoUrls: updatedReview.photoUrls,
        isPublished: updatedReview.isPublished,
        tags: tags ?? [],
      );
    }
  }

  /// Delete a review
  Future<bool> deleteReview(String reviewId) async {
    try {
      _loggingService.debug('ReviewService', 'Deleting review: $reviewId');

      // Get the review to get the experience ID
      final docSnapshot =
          await _firestore.collection('reviews').doc(reviewId).get();
      if (!docSnapshot.exists) {
        throw Exception('Review not found');
      }

      final review =
          ReviewModel.fromJson({...docSnapshot.data()!, 'id': reviewId});

      // Delete review document
      await _firestore.collection('reviews').doc(reviewId).delete();

      // Update experience rating
      await _updateExperienceRating(review.experienceId);

      // Delete photos if any
      for (final photoUrl in review.photoUrls) {
        try {
          final ref = _storage.refFromURL(photoUrl);
          await ref.delete();
        } catch (e) {
          _loggingService.warning(
            'ReviewService',
            'Error deleting review photo: $photoUrl',
            e,
          );
        }
      }

      return true;
    } catch (e, stackTrace) {
      _loggingService.error(
        'ReviewService',
        'Error deleting review',
        e,
        stackTrace,
      );

      // Fallback to in-memory storage for backward compatibility
      final index = _reviews.indexWhere((r) => r.id == reviewId);
      if (index == -1) {
        return false;
      }

      _reviews.removeAt(index);

      // Simulate network delay
      await Future.delayed(const Duration(milliseconds: 500));

      return true;
    }
  }

  /// Updates the average rating for an experience
  Future<void> _updateExperienceRating(String experienceId) async {
    try {
      // Get all reviews for the experience
      final snapshot = await _firestore
          .collection('reviews')
          .where('experienceId', isEqualTo: experienceId)
          .where('isPublished', isEqualTo: true)
          .get();

      final reviews = snapshot.docs
          .map((doc) => ReviewModel.fromJson({...doc.data(), 'id': doc.id}))
          .toList();

      if (reviews.isEmpty) {
        // No reviews, reset rating to 0
        await _firestore.collection('experiences').doc(experienceId).update({
          'rating': 0.0,
          'reviewCount': 0,
        });
        return;
      }

      // Calculate average rating
      final totalRating = reviews.fold<double>(
        0,
        (total, review) => total + review.rating,
      );

      final averageRating = totalRating / reviews.length;

      // Update experience document
      await _firestore.collection('experiences').doc(experienceId).update({
        'rating': averageRating,
        'reviewCount': reviews.length,
      });
    } catch (e, stackTrace) {
      _loggingService.error(
        'ReviewService',
        'Error updating experience rating',
        e,
        stackTrace,
      );
      // Don't rethrow, as this is a background operation
    }
  }

  /// Check if a booking is eligible for review
  Future<bool> isBookingEligibleForReview(Booking booking) async {
    // Check if booking is completed
    if (booking.status != BookingStatus.completed) {
      return false;
    }

    // Check if booking already has a review
    final existingReview = await getReviewForBooking(booking.id);
    if (existingReview != null) {
      return false;
    }

    // Check if booking date is in the past
    final now = DateTime.now();
    final bookingDateTime = DateTime(
      booking.date.year,
      booking.date.month,
      booking.date.day,
      booking.timeSlot.endTime.hour,
      booking.timeSlot.endTime.minute,
    );

    return bookingDateTime.isBefore(now);
  }

  /// Get average rating for an experience
  Future<double> getAverageRatingForExperience(String experienceId) async {
    final reviews = await getReviewsForExperience(experienceId);

    if (reviews.isEmpty) {
      return 0.0;
    }

    final totalRating = reviews.fold<double>(
      0.0,
      (total, review) => total + review.rating,
    );

    return totalRating / reviews.length;
  }

  /// Marks a review as helpful
  Future<ReviewModel> markReviewAsHelpful(
      String reviewId, String userId) async {
    try {
      _loggingService.debug(
        'ReviewService',
        'Marking review $reviewId as helpful by user: $userId',
      );

      // Get the existing review
      final docSnapshot =
          await _firestore.collection('reviews').doc(reviewId).get();
      if (!docSnapshot.exists) {
        throw Exception('Review not found');
      }

      final existingReview =
          ReviewModel.fromJson({...docSnapshot.data()!, 'id': reviewId});

      // Check if user has already marked this review as helpful
      if (existingReview.helpfulUserIds.contains(userId)) {
        // Remove the helpful mark
        final updatedHelpfulUserIds =
            List<String>.from(existingReview.helpfulUserIds)..remove(userId);

        final updatedReview = existingReview.copyWith(
          helpfulUserIds: updatedHelpfulUserIds,
          helpfulCount: updatedHelpfulUserIds.length,
        );

        await _firestore
            .collection('reviews')
            .doc(reviewId)
            .update(updatedReview.toJson());

        return updatedReview;
      } else {
        // Add the helpful mark
        final updatedHelpfulUserIds =
            List<String>.from(existingReview.helpfulUserIds)..add(userId);

        final updatedReview = existingReview.copyWith(
          helpfulUserIds: updatedHelpfulUserIds,
          helpfulCount: updatedHelpfulUserIds.length,
        );

        await _firestore
            .collection('reviews')
            .doc(reviewId)
            .update(updatedReview.toJson());

        return updatedReview;
      }
    } catch (e, stackTrace) {
      _loggingService.error(
        'ReviewService',
        'Error marking review as helpful',
        e,
        stackTrace,
      );

      // Fallback to returning the original review
      final reviews = await getReviewsForExperience('');
      final review = reviews.firstWhere(
        (r) => r.id == reviewId,
        orElse: () => throw Exception('Review not found'),
      );

      return review;
    }
  }

  /// Adds a guide response to a review
  Future<ReviewModel> addGuideResponse({
    required String reviewId,
    required String guideId,
    required String guideName,
    required String content,
  }) async {
    try {
      _loggingService.debug(
        'ReviewService',
        'Adding guide response to review: $reviewId',
      );

      // Get the existing review
      final docSnapshot =
          await _firestore.collection('reviews').doc(reviewId).get();
      if (!docSnapshot.exists) {
        throw Exception('Review not found');
      }

      final existingReview =
          ReviewModel.fromJson({...docSnapshot.data()!, 'id': reviewId});

      // Create guide response
      final guideResponse = GuideResponse(
        guideId: guideId,
        guideName: guideName,
        content: content,
        datePosted: DateTime.now(),
      );

      // Update review with guide response
      final updatedReview = existingReview.copyWith(
        guideResponse: guideResponse,
      );

      // Save to Firestore
      await _firestore
          .collection('reviews')
          .doc(reviewId)
          .update(updatedReview.toJson());

      return updatedReview;
    } catch (e, stackTrace) {
      _loggingService.error(
        'ReviewService',
        'Error adding guide response',
        e,
        stackTrace,
      );

      // Fallback to returning the original review
      final reviews = await getReviewsForExperience('');
      final review = reviews.firstWhere(
        (r) => r.id == reviewId,
        orElse: () => throw Exception('Review not found'),
      );

      return review;
    }
  }
}
