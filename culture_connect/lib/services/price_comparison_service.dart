import 'dart:async';
import 'dart:convert';
import 'dart:math';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:http/http.dart' as http;
import 'package:culture_connect/models/travel/travel.dart';
import 'package:culture_connect/models/travel/price_comparison_model.dart';
import 'package:culture_connect/models/travel/price_history_model.dart';

/// Service for comparing prices of travel services
class PriceComparisonService {
  final Box<String> _priceCache;
  final Box<String> _historyCache;
  final Connectivity _connectivity;
  StreamSubscription<ConnectivityResult>? _connectivitySubscription;
  bool _isOnline = true;

  // Cache expiration time (in minutes)
  static const int _cacheExpirationMinutes = 30;

  // Refresh interval for background updates (in minutes)
  static const int _backgroundRefreshMinutes = 60;

  // Maximum number of cached items
  static const int _maxCacheItems = 100;

  // Stream controller for price updates
  final _priceUpdateController = StreamController<String>.broadcast();

  // Background refresh timer
  Timer? _backgroundRefreshTimer;

  // Mock price sources
  final List<PriceSource> _priceSources = [
    PriceSource(
      id: 'expedia',
      name: 'Expedia',
      logoUrl: 'https://example.com/expedia.png',
      websiteUrl: 'https://www.expedia.com',
      rating: 4.5,
      reviewCount: 12500,
      isVerified: true,
      isPartner: true,
    ),
    PriceSource(
      id: 'booking',
      name: 'Booking.com',
      logoUrl: 'https://example.com/booking.png',
      websiteUrl: 'https://www.booking.com',
      rating: 4.7,
      reviewCount: 18300,
      isVerified: true,
      isPartner: true,
    ),
    PriceSource(
      id: 'hotels',
      name: 'Hotels.com',
      logoUrl: 'https://example.com/hotels.png',
      websiteUrl: 'https://www.hotels.com',
      rating: 4.3,
      reviewCount: 9800,
      isVerified: true,
      isPartner: false,
    ),
    PriceSource(
      id: 'airbnb',
      name: 'Airbnb',
      logoUrl: 'https://example.com/airbnb.png',
      websiteUrl: 'https://www.airbnb.com',
      rating: 4.6,
      reviewCount: 21500,
      isVerified: true,
      isPartner: false,
    ),
    PriceSource(
      id: 'kayak',
      name: 'KAYAK',
      logoUrl: 'https://example.com/kayak.png',
      websiteUrl: 'https://www.kayak.com',
      rating: 4.4,
      reviewCount: 8700,
      isVerified: true,
      isPartner: true,
    ),
    PriceSource(
      id: 'orbitz',
      name: 'Orbitz',
      logoUrl: 'https://example.com/orbitz.png',
      websiteUrl: 'https://www.orbitz.com',
      rating: 4.2,
      reviewCount: 7200,
      isVerified: true,
      isPartner: false,
    ),
    PriceSource(
      id: 'travelocity',
      name: 'Travelocity',
      logoUrl: 'https://example.com/travelocity.png',
      websiteUrl: 'https://www.travelocity.com',
      rating: 4.3,
      reviewCount: 6900,
      isVerified: true,
      isPartner: false,
    ),
    PriceSource(
      id: 'priceline',
      name: 'Priceline',
      logoUrl: 'https://example.com/priceline.png',
      websiteUrl: 'https://www.priceline.com',
      rating: 4.1,
      reviewCount: 5800,
      isVerified: true,
      isPartner: true,
    ),
  ];

  /// Creates a new price comparison service
  PriceComparisonService()
      : _priceCache = Hive.box<String>('price_comparison_cache'),
        _historyCache = Hive.box<String>('price_history_cache'),
        _connectivity = Connectivity() {
    _initConnectivityListener();
    _startBackgroundRefresh();
  }

  /// Initializes the connectivity listener
  void _initConnectivityListener() {
    _connectivitySubscription =
        _connectivity.onConnectivityChanged.listen((result) {
      final wasOnline = _isOnline;
      _isOnline = result != ConnectivityResult.none;

      // If we just came back online, refresh price data
      if (!wasOnline && _isOnline) {
        _refreshExpiredCache();
      }
    });
  }

  /// Starts the background refresh timer
  void _startBackgroundRefresh() {
    _backgroundRefreshTimer?.cancel();
    _backgroundRefreshTimer = Timer.periodic(
      const Duration(minutes: _backgroundRefreshMinutes),
      (_) => _refreshExpiredCache(),
    );
  }

  /// Refreshes expired cache items
  Future<void> _refreshExpiredCache() async {
    if (!_isOnline) return;

    final now = DateTime.now();
    final expiredKeys = <String>[];

    // Find expired price cache items
    for (final key in _priceCache.keys) {
      final json = _priceCache.get(key);
      if (json == null) continue;

      try {
        final data = jsonDecode(json) as Map<String, dynamic>;
        final timestamp = DateTime.parse(data['timestamp'] as String);
        final expirationTime =
            timestamp.add(const Duration(minutes: _cacheExpirationMinutes));

        if (now.isAfter(expirationTime)) {
          expiredKeys.add(key);
        }
      } catch (e) {
        debugPrint('Error parsing cache item: $e');
        expiredKeys.add(key);
      }
    }

    // Refresh expired items
    for (final key in expiredKeys) {
      final parts = key.split('_');
      if (parts.length != 2) continue;

      final serviceType = parts[0];
      final serviceId = parts[1];

      try {
        await comparePrices(serviceType, serviceId);
      } catch (e) {
        debugPrint('Error refreshing price comparison: $e');
      }
    }
  }

  /// Cleans up old cache items if we exceed the maximum
  void _cleanupCache() {
    // Clean up price cache
    if (_priceCache.length > _maxCacheItems) {
      final entries = <MapEntry<String, DateTime>>[];

      // Get all cache items with their timestamps
      for (final key in _priceCache.keys) {
        final json = _priceCache.get(key);
        if (json == null) continue;

        try {
          final data = jsonDecode(json) as Map<String, dynamic>;
          final timestamp = DateTime.parse(data['timestamp'] as String);
          entries.add(MapEntry(key, timestamp));
        } catch (e) {
          // If we can't parse it, it's a candidate for removal
          entries.add(MapEntry(key, DateTime(1970)));
        }
      }

      // Sort by timestamp (oldest first)
      entries.sort((a, b) => a.value.compareTo(b.value));

      // Remove oldest items to get back to max size
      final itemsToRemove = entries.length - _maxCacheItems;
      if (itemsToRemove > 0) {
        for (var i = 0; i < itemsToRemove; i++) {
          _priceCache.delete(entries[i].key);
        }
      }
    }

    // Clean up history cache
    if (_historyCache.length > _maxCacheItems) {
      final entries = <MapEntry<String, DateTime>>[];

      // Get all cache items with their timestamps
      for (final key in _historyCache.keys) {
        final json = _historyCache.get(key);
        if (json == null) continue;

        try {
          final data = jsonDecode(json) as Map<String, dynamic>;
          final timestamp = DateTime.parse(data['lastUpdated'] as String);
          entries.add(MapEntry(key, timestamp));
        } catch (e) {
          // If we can't parse it, it's a candidate for removal
          entries.add(MapEntry(key, DateTime(1970)));
        }
      }

      // Sort by timestamp (oldest first)
      entries.sort((a, b) => a.value.compareTo(b.value));

      // Remove oldest items to get back to max size
      final itemsToRemove = entries.length - _maxCacheItems;
      if (itemsToRemove > 0) {
        for (var i = 0; i < itemsToRemove; i++) {
          _historyCache.delete(entries[i].key);
        }
      }
    }
  }

  /// Compares prices for a travel service
  Future<List<PricePoint>> comparePrices(
      String serviceType, String serviceId) async {
    final cacheKey = '${serviceType}_$serviceId';

    // Check cache first
    final cachedData = _getCachedPrices(cacheKey);
    if (cachedData != null) {
      // If cache is still valid, return it
      final now = DateTime.now();
      final timestamp = cachedData.first.timestamp;
      final expirationTime =
          timestamp.add(const Duration(minutes: _cacheExpirationMinutes));

      if (now.isBefore(expirationTime)) {
        return cachedData;
      }
    }

    // If we're offline, return cached data even if expired
    if (!_isOnline) {
      if (cachedData != null) {
        return cachedData.map((p) => p.copyWith(isFromCache: true)).toList();
      }

      // If no cache, return empty list
      return [];
    }

    // Make API request for real-time price comparison
    try {
      // In a real app, this would be an actual API call
      // For demo purposes, we'll simulate a network request
      await Future.delayed(const Duration(milliseconds: 1200));

      // Generate simulated price data
      final pricePoints = _generateSimulatedPrices(serviceType, serviceId);

      // Cache the result
      await _cachePrices(cacheKey, pricePoints);

      // Update price history
      await _updatePriceHistory(serviceType, serviceId, pricePoints);

      // Notify listeners
      _priceUpdateController.add(cacheKey);

      return pricePoints;
    } catch (e) {
      debugPrint('Error comparing prices: $e');

      // If we have cached data, return it as fallback
      if (cachedData != null) {
        return cachedData.map((p) => p.copyWith(isFromCache: true)).toList();
      }

      // Otherwise, throw the error
      rethrow;
    }
  }

  /// Gets cached price data
  List<PricePoint>? _getCachedPrices(String cacheKey) {
    final json = _priceCache.get(cacheKey);
    if (json == null) return null;

    try {
      final data = jsonDecode(json) as Map<String, dynamic>;
      final pricePoints = (data['pricePoints'] as List)
          .map((e) => PricePoint.fromJson(e as Map<String, dynamic>))
          .toList();
      return pricePoints;
    } catch (e) {
      debugPrint('Error parsing cached prices: $e');
      return null;
    }
  }

  /// Caches price data
  Future<void> _cachePrices(
      String cacheKey, List<PricePoint> pricePoints) async {
    final data = {
      'pricePoints': pricePoints.map((p) => p.toJson()).toList(),
      'timestamp': DateTime.now().toIso8601String(),
    };

    await _priceCache.put(cacheKey, jsonEncode(data));
    _cleanupCache();
  }

  /// Gets cached price history
  PriceHistory? _getCachedPriceHistory(String cacheKey) {
    final json = _historyCache.get(cacheKey);
    if (json == null) return null;

    try {
      final data = jsonDecode(json) as Map<String, dynamic>;
      return PriceHistory.fromJson(data);
    } catch (e) {
      debugPrint('Error parsing cached price history: $e');
      return null;
    }
  }

  /// Updates price history with new price points
  Future<void> _updatePriceHistory(
    String serviceType,
    String serviceId,
    List<PricePoint> pricePoints,
  ) async {
    final cacheKey = '${serviceType}_$serviceId';
    final now = DateTime.now();

    // Get existing history or create new one
    final existingHistory = _getCachedPriceHistory(cacheKey);
    final history = existingHistory ??
        PriceHistory(
          id: cacheKey,
          travelServiceId: serviceId,
          travelServiceType: _getTravelServiceType(serviceType),
          entries: [],
          lastUpdated: now,
        );

    // Create new history entries from price points
    final newEntries = pricePoints
        .map((p) => PriceHistoryEntry(
              id: '${p.id}_${now.millisecondsSinceEpoch}',
              travelServiceId: p.travelServiceId,
              travelServiceType: p.travelServiceType,
              source: p.source,
              price: p.finalPrice,
              currency: p.currency,
              date: now,
            ))
        .toList();

    // Combine existing entries with new ones
    final allEntries = [...history.entries, ...newEntries];

    // Keep only the last 30 days of entries per source to avoid excessive growth
    final thirtyDaysAgo = now.subtract(const Duration(days: 30));
    final filteredEntries = <PriceHistoryEntry>[];

    // Group entries by source
    final entriesBySource = <String, List<PriceHistoryEntry>>{};
    for (final entry in allEntries) {
      final sourceId = entry.source.id;
      entriesBySource[sourceId] = entriesBySource[sourceId] ?? [];
      entriesBySource[sourceId]!.add(entry);
    }

    // For each source, keep entries from the last 30 days plus one entry per day before that
    for (final sourceEntries in entriesBySource.values) {
      // Sort by date (newest first)
      sourceEntries.sort((a, b) => b.date.compareTo(a.date));

      // Keep all entries from the last 30 days
      final recentEntries =
          sourceEntries.where((e) => e.date.isAfter(thirtyDaysAgo)).toList();
      filteredEntries.addAll(recentEntries);

      // For older entries, keep one per day
      final olderEntries =
          sourceEntries.where((e) => !e.date.isAfter(thirtyDaysAgo)).toList();
      final entriesByDay = <String, PriceHistoryEntry>{};

      for (final entry in olderEntries) {
        final dayKey =
            '${entry.date.year}-${entry.date.month}-${entry.date.day}';
        if (!entriesByDay.containsKey(dayKey)) {
          entriesByDay[dayKey] = entry;
        }
      }

      filteredEntries.addAll(entriesByDay.values);
    }

    // Create updated history
    final updatedHistory = history.copyWith(
      entries: filteredEntries,
      lastUpdated: now,
    );

    // Cache the updated history
    await _historyCache.put(cacheKey, jsonEncode(updatedHistory.toJson()));
  }

  /// Gets price history for a travel service
  Future<PriceHistory> getPriceHistory(
      String serviceType, String serviceId) async {
    final cacheKey = '${serviceType}_$serviceId';

    // Check if we have history in cache
    final cachedHistory = _getCachedPriceHistory(cacheKey);
    if (cachedHistory != null) {
      return cachedHistory;
    }

    // If no history, fetch prices first to generate history
    await comparePrices(serviceType, serviceId);

    // Now try to get history again
    final history = _getCachedPriceHistory(cacheKey);
    if (history != null) {
      return history;
    }

    // If still no history, create an empty one
    return PriceHistory(
      id: cacheKey,
      travelServiceId: serviceId,
      travelServiceType: _getTravelServiceType(serviceType),
      entries: [],
      lastUpdated: DateTime.now(),
    );
  }

  /// Generates simulated price data for demo purposes
  List<PricePoint> _generateSimulatedPrices(
      String serviceType, String serviceId) {
    final random = Random();
    final now = DateTime.now();
    final travelServiceType = _getTravelServiceType(serviceType);

    // Determine base price range based on service type
    double basePrice;
    switch (travelServiceType) {
      case TravelServiceType.hotel:
        basePrice = 100 + random.nextDouble() * 300;
        break;
      case TravelServiceType.flight:
        basePrice = 200 + random.nextDouble() * 800;
        break;
      case TravelServiceType.carRental:
        basePrice = 50 + random.nextDouble() * 150;
        break;
      case TravelServiceType.restaurant:
        basePrice = 20 + random.nextDouble() * 80;
        break;
      case TravelServiceType.privateSecurity:
        basePrice = 150 + random.nextDouble() * 350;
        break;
      case TravelServiceType.cruise:
        basePrice = 500 + random.nextDouble() * 1500;
        break;
      // All cases are covered, but we need a default to satisfy the compiler
      default:
        basePrice = 100 + random.nextDouble() * 300;
    }

    // Select a random subset of price sources (3-6 sources)
    final numSources = 3 + random.nextInt(4);
    final shuffledSources = List<PriceSource>.from(_priceSources)..shuffle();
    final selectedSources = shuffledSources.take(numSources).toList();

    // Generate price points for each source
    return selectedSources.map((source) {
      // Vary the base price slightly for each source
      final sourceBasePrice = basePrice * (0.9 + random.nextDouble() * 0.2);

      // Calculate tax, fees, and discounts
      final taxRate = 0.05 + random.nextDouble() * 0.1; // 5-15%
      final feeRate = 0.02 + random.nextDouble() * 0.08; // 2-10%
      final discountRate = random.nextDouble() < 0.3
          ? 0.05 + random.nextDouble() * 0.15
          : 0.0; // 0-20% with 30% chance

      final taxAmount = sourceBasePrice * taxRate;
      final feeAmount = sourceBasePrice * feeRate;
      final discountAmount = sourceBasePrice * discountRate;

      final finalPrice =
          sourceBasePrice + taxAmount + feeAmount - discountAmount;

      return PricePoint(
        id: '${source.id}_${serviceId}_${now.millisecondsSinceEpoch}',
        travelServiceId: serviceId,
        travelServiceType: travelServiceType,
        source: source,
        basePrice: sourceBasePrice,
        taxAmount: taxAmount,
        feeAmount: feeAmount,
        discountAmount: discountAmount,
        finalPrice: finalPrice,
        currency: 'USD',
        bookingUrl: '${source.websiteUrl}/book/$serviceType/$serviceId',
        timestamp: now,
        isFromCache: false,
        details: {
          'cancellationPolicy': _getRandomCancellationPolicy(),
          'paymentOptions': _getRandomPaymentOptions(),
          'availability': random.nextInt(10) + 1,
        },
      );
    }).toList()
      ..sort((a, b) => a.finalPrice.compareTo(b.finalPrice));
  }

  /// Gets a random cancellation policy
  String _getRandomCancellationPolicy() {
    final policies = [
      'Free cancellation up to 24 hours before',
      'Free cancellation up to 48 hours before',
      'Free cancellation up to 7 days before',
      'Non-refundable',
      'Partial refund available',
      'Full refund available up to 24 hours before',
    ];
    return policies[Random().nextInt(policies.length)];
  }

  /// Gets random payment options
  List<String> _getRandomPaymentOptions() {
    final options = [
      'Credit Card',
      'Debit Card',
      'PayPal',
      'Apple Pay',
      'Google Pay',
      'Bank Transfer',
      'Pay at Property',
    ];

    final random = Random();
    final numOptions = 2 + random.nextInt(3);
    final shuffledOptions = List<String>.from(options)..shuffle();
    return shuffledOptions.take(numOptions).toList();
  }

  /// Converts a string service type to TravelServiceType enum
  TravelServiceType _getTravelServiceType(String serviceType) {
    switch (serviceType.toLowerCase()) {
      case 'hotel':
        return TravelServiceType.hotel;
      case 'flight':
        return TravelServiceType.flight;
      case 'car':
      case 'carrental':
        return TravelServiceType.carRental;
      case 'restaurant':
        return TravelServiceType.restaurant;
      case 'security':
      case 'privatesecurity':
        return TravelServiceType.privateSecurity;
      case 'cruise':
        return TravelServiceType.cruise;
      default:
        throw ArgumentError('Unknown service type: $serviceType');
    }
  }

  /// Stream of price updates
  Stream<String> get priceUpdates => _priceUpdateController.stream;

  /// Checks if the device is online
  bool get isOnline => _isOnline;

  /// Manually refreshes prices for a service
  Future<List<PricePoint>> refreshPrices(
      String serviceType, String serviceId) async {
    final cacheKey = '${serviceType}_$serviceId';

    // Remove from cache to force a refresh
    await _priceCache.delete(cacheKey);

    // Compare prices again
    return comparePrices(serviceType, serviceId);
  }

  /// Disposes of resources
  void dispose() {
    _connectivitySubscription?.cancel();
    _backgroundRefreshTimer?.cancel();
    _priceUpdateController.close();
  }
}
