import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:share_plus/share_plus.dart';
import 'package:path_provider/path_provider.dart';
import 'dart:io';
import 'package:http/http.dart' as http;
import 'package:culture_connect/models/booking.dart';
import 'package:culture_connect/models/experience.dart';

/// Provider for the sharing service
final sharingServiceProvider = Provider<SharingService>((ref) {
  return SharingService();
});

/// Service for sharing content on social media
class SharingService {
  // Singleton instance
  static final SharingService _instance = SharingService._internal();
  factory SharingService() => _instance;
  SharingService._internal();

  /// Share booking details
  Future<void> shareBooking({
    required Booking booking,
    required String experienceTitle,
    String? customMessage,
    String? imageUrl,
  }) async {
    try {
      // Create share text
      final String shareText = _createBookingShareText(
        booking: booking,
        experienceTitle: experienceTitle,
        customMessage: customMessage,
      );

      // If no image URL is provided, just share text
      if (imageUrl == null || imageUrl.isEmpty) {
        await Share.share(shareText);
        return;
      }

      // Download and share image with text
      await _shareWithImage(shareText, imageUrl);
    } catch (e) {
      debugPrint('Error sharing booking: $e');
      // Fallback to simple text sharing
      await Share.share(
        'Check out my booking for $experienceTitle on CultureConnect!',
      );
    }
  }

  /// Share experience details
  Future<void> shareExperience({
    required Experience experience,
    String? customMessage,
  }) async {
    try {
      // Create share text
      final String shareText = _createExperienceShareText(
        experience: experience,
        customMessage: customMessage,
      );

      // If no image URL is provided, just share text
      if (experience.imageUrl.isEmpty) {
        await Share.share(shareText);
        return;
      }

      // Download and share image with text
      await _shareWithImage(shareText, experience.imageUrl);
    } catch (e) {
      debugPrint('Error sharing experience: $e');
      // Fallback to simple text sharing
      await Share.share(
        'Check out ${experience.title} on CultureConnect!',
      );
    }
  }

  /// Share review
  Future<void> shareReview({
    required String experienceTitle,
    required double rating,
    required String reviewText,
    String? imageUrl,
  }) async {
    try {
      // Create share text
      final String shareText = '''
I just rated $experienceTitle ${rating.toStringAsFixed(1)}/5 stars on CultureConnect!

"$reviewText"

Download CultureConnect to discover authentic cultural experiences!
''';

      // If no image URL is provided, just share text
      if (imageUrl == null || imageUrl.isEmpty) {
        await Share.share(shareText);
        return;
      }

      // Download and share image with text
      await _shareWithImage(shareText, imageUrl);
    } catch (e) {
      debugPrint('Error sharing review: $e');
      // Fallback to simple text sharing
      await Share.share(
        'Check out my review of $experienceTitle on CultureConnect!',
      );
    }
  }

  /// Create booking share text
  String _createBookingShareText({
    required Booking booking,
    required String experienceTitle,
    String? customMessage,
  }) {
    final dateFormat = _formatDate(booking.date);
    final timeSlot = booking.timeSlot.formattedTime;

    String shareText = '''
${customMessage ?? '🎉 I just booked an experience on CultureConnect!'}

🏛️ $experienceTitle
📅 Date: $dateFormat
⏰ Time: $timeSlot
👥 Participants: ${booking.participantCount}

Download CultureConnect to discover authentic cultural experiences!
''';

    return shareText;
  }

  /// Create experience share text
  String _createExperienceShareText({
    required Experience experience,
    String? customMessage,
  }) {
    String shareText = '''
${customMessage ?? '🌟 Check out this amazing cultural experience on CultureConnect!'}

🏛️ ${experience.title}
📍 ${experience.location}
⭐ ${experience.rating.toStringAsFixed(1)}/5 (${experience.reviewCount} reviews)
💰 \$${experience.price.toStringAsFixed(2)}

${experience.description.length > 100 ? '${experience.description.substring(0, 97)}...' : experience.description}

Download CultureConnect to discover authentic cultural experiences!
''';

    return shareText;
  }

  /// Share text with image
  Future<void> _shareWithImage(String text, String imageUrl) async {
    // Download image
    final http.Response response = await http.get(Uri.parse(imageUrl));

    // Get temporary directory
    final directory = await getTemporaryDirectory();
    final imagePath = '${directory.path}/share_image.jpg';

    // Save image to file
    final File imageFile = File(imagePath);
    await imageFile.writeAsBytes(response.bodyBytes);

    // Share text with image
    await Share.shareXFiles(
      [XFile(imagePath)],
      text: text,
    );
  }

  /// Format date for display
  String _formatDate(DateTime date) {
    final months = [
      'January',
      'February',
      'March',
      'April',
      'May',
      'June',
      'July',
      'August',
      'September',
      'October',
      'November',
      'December'
    ];
    final weekdays = [
      'Monday',
      'Tuesday',
      'Wednesday',
      'Thursday',
      'Friday',
      'Saturday',
      'Sunday'
    ];

    final weekday = weekdays[date.weekday - 1];
    final month = months[date.month - 1];

    return '$weekday, $month ${date.day}, ${date.year}';
  }
}
