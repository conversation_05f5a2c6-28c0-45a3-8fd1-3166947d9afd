import 'dart:convert';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:http/http.dart' as http;
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:shared_preferences/shared_preferences.dart';

class TranslationService {
  final String _apiKey;
  final http.Client _client;
  final SharedPreferences _prefs;
  
  // Cache for translations to reduce API calls
  final Map<String, String> _translationCache = {};
  
  // Supported languages
  final List<LanguageModel> _supportedLanguages = [
    LanguageModel(code: 'en', name: 'English'),
    LanguageModel(code: 'fr', name: 'French'),
    LanguageModel(code: 'yo', name: 'Yoruba'),
    LanguageModel(code: 'ig', name: '<PERSON><PERSON><PERSON>'),
    LanguageModel(code: 'ha', name: '<PERSON><PERSON>'),
    LanguageModel(code: 'sw', name: 'Swahili'),
    LanguageModel(code: 'zu', name: 'Zulu'),
    LanguageModel(code: 'xh', name: 'Xhos<PERSON>'),
    LanguageModel(code: 'ar', name: 'Arabic'),
    LanguageModel(code: 'pt', name: 'Portuguese'),
  ];

  TranslationService(this._apiKey, this._client, this._prefs) {
    _loadCachedTranslations();
  }

  List<LanguageModel> get supportedLanguages => _supportedLanguages;

  Future<void> _loadCachedTranslations() async {
    final cachedTranslations = _prefs.getString('translation_cache');
    if (cachedTranslations != null) {
      final Map<String, dynamic> decoded = jsonDecode(cachedTranslations);
      decoded.forEach((key, value) {
        _translationCache[key] = value.toString();
      });
    }
  }

  Future<void> _saveTranslationCache() async {
    await _prefs.setString('translation_cache', jsonEncode(_translationCache));
  }

  String _getCacheKey(String text, String sourceLanguage, String targetLanguage) {
    return '$sourceLanguage:$targetLanguage:${text.hashCode}';
  }

  Future<String> translateText(
    String text,
    String sourceLanguage,
    String targetLanguage,
  ) async {
    // Return original text if languages are the same
    if (sourceLanguage == targetLanguage) {
      return text;
    }

    // Check cache first
    final cacheKey = _getCacheKey(text, sourceLanguage, targetLanguage);
    if (_translationCache.containsKey(cacheKey)) {
      return _translationCache[cacheKey]!;
    }

    try {
      // For demo purposes, we'll use a mock translation
      // In a real app, you would call the Google Cloud Translation API
      final translatedText = await _mockTranslateText(text, sourceLanguage, targetLanguage);
      
      // Cache the result
      _translationCache[cacheKey] = translatedText;
      _saveTranslationCache();
      
      return translatedText;
    } catch (e) {
      // If translation fails, return original text
      return text;
    }
  }

  Future<String> _mockTranslateText(
    String text,
    String sourceLanguage,
    String targetLanguage,
  ) async {
    // Simulate network delay
    await Future.delayed(const Duration(milliseconds: 500));
    
    // Simple mock translations for demo purposes
    if (targetLanguage == 'fr') {
      return 'FR: $text';
    } else if (targetLanguage == 'yo') {
      return 'YO: $text';
    } else if (targetLanguage == 'ig') {
      return 'IG: $text';
    } else if (targetLanguage == 'ha') {
      return 'HA: $text';
    } else if (targetLanguage == 'sw') {
      return 'SW: $text';
    } else {
      return text;
    }
  }

  Future<String> _translateWithGoogleApi(
    String text,
    String sourceLanguage,
    String targetLanguage,
  ) async {
    final url = Uri.parse(
      'https://translation.googleapis.com/language/translate/v2?key=$_apiKey',
    );
    
    final response = await _client.post(
      url,
      body: {
        'q': text,
        'source': sourceLanguage,
        'target': targetLanguage,
        'format': 'text',
      },
    );
    
    if (response.statusCode == 200) {
      final Map<String, dynamic> data = jsonDecode(response.body);
      return data['data']['translations'][0]['translatedText'];
    } else {
      throw Exception('Failed to translate text: ${response.body}');
    }
  }

  Future<String> detectLanguage(String text) async {
    // For demo purposes, we'll return English
    // In a real app, you would call the Google Cloud Translation API
    return 'en';
  }

  Future<bool> isLanguageSupported(String languageCode) async {
    return _supportedLanguages.any((lang) => lang.code == languageCode);
  }

  Future<List<LanguageModel>> getOfflineLanguages() async {
    final offlineLanguages = _prefs.getStringList('offline_languages') ?? [];
    return _supportedLanguages
        .where((lang) => offlineLanguages.contains(lang.code))
        .toList();
  }

  Future<void> downloadLanguage(String languageCode) async {
    // Simulate downloading language pack
    await Future.delayed(const Duration(seconds: 2));
    
    final offlineLanguages = _prefs.getStringList('offline_languages') ?? [];
    if (!offlineLanguages.contains(languageCode)) {
      offlineLanguages.add(languageCode);
      await _prefs.setStringList('offline_languages', offlineLanguages);
    }
  }

  Future<void> removeOfflineLanguage(String languageCode) async {
    final offlineLanguages = _prefs.getStringList('offline_languages') ?? [];
    if (offlineLanguages.contains(languageCode)) {
      offlineLanguages.remove(languageCode);
      await _prefs.setStringList('offline_languages', offlineLanguages);
    }
  }

  Future<void> clearTranslationCache() async {
    _translationCache.clear();
    await _prefs.remove('translation_cache');
  }
}

class LanguageModel {
  final String code;
  final String name;

  LanguageModel({
    required this.code,
    required this.name,
  });
}

final translationServiceProvider = Provider<TranslationService>((ref) {
  final apiKey = dotenv.env['GOOGLE_TRANSLATE_API_KEY'] ?? '';
  final client = http.Client();
  final prefs = ref.watch(sharedPreferencesProvider);
  
  return TranslationService(apiKey, client, prefs);
});

final sharedPreferencesProvider = Provider<SharedPreferences>((ref) {
  throw UnimplementedError('SharedPreferences provider not initialized');
});
