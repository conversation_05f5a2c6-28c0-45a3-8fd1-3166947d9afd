import 'dart:async';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:path_provider/path_provider.dart';
import 'package:share_plus/share_plus.dart';
import 'package:image_picker/image_picker.dart';

/// A service for recording AR experiences.
class ARRecordingService {
  // Recording state
  bool _isRecording = false;
  bool _isPaused = false;
  DateTime? _recordingStartTime;
  Timer? _recordingTimer;
  int _recordingDurationInSeconds = 0;
  
  // Screenshot state
  List<File> _screenshots = [];
  
  // Video recording state
  File? _videoFile;
  
  // Callbacks
  VoidCallback? _onRecordingStateChanged;
  ValueChanged<int>? _onRecordingTimerUpdated;
  
  // Singleton instance
  static final ARRecordingService _instance = ARRecordingService._internal();
  
  // Factory constructor
  factory ARRecordingService() => _instance;
  
  // Internal constructor
  ARRecordingService._internal();
  
  /// Initialize the AR recording service.
  Future<void> initialize({
    VoidCallback? onRecordingStateChanged,
    ValueChanged<int>? onRecordingTimerUpdated,
  }) async {
    _onRecordingStateChanged = onRecordingStateChanged;
    _onRecordingTimerUpdated = onRecordingTimerUpdated;
    
    // Clear temporary files
    await _clearTemporaryFiles();
  }
  
  /// Clear temporary files.
  Future<void> _clearTemporaryFiles() async {
    try {
      final tempDir = await getTemporaryDirectory();
      final recordingsDir = Directory('${tempDir.path}/ar_recordings');
      
      if (await recordingsDir.exists()) {
        await recordingsDir.delete(recursive: true);
      }
      
      await recordingsDir.create(recursive: true);
    } catch (e) {
      debugPrint('Error clearing temporary files: $e');
    }
  }
  
  /// Start recording.
  Future<bool> startRecording() async {
    if (_isRecording) return false;
    
    try {
      _isRecording = true;
      _isPaused = false;
      _recordingStartTime = DateTime.now();
      _recordingDurationInSeconds = 0;
      _screenshots = [];
      _videoFile = null;
      
      // Start recording timer
      _recordingTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
        if (!_isPaused) {
          _recordingDurationInSeconds++;
          _onRecordingTimerUpdated?.call(_recordingDurationInSeconds);
        }
      });
      
      _onRecordingStateChanged?.call();
      return true;
    } catch (e) {
      debugPrint('Error starting recording: $e');
      _isRecording = false;
      return false;
    }
  }
  
  /// Pause recording.
  void pauseRecording() {
    if (!_isRecording || _isPaused) return;
    
    _isPaused = true;
    _onRecordingStateChanged?.call();
  }
  
  /// Resume recording.
  void resumeRecording() {
    if (!_isRecording || !_isPaused) return;
    
    _isPaused = false;
    _onRecordingStateChanged?.call();
  }
  
  /// Stop recording.
  Future<bool> stopRecording() async {
    if (!_isRecording) return false;
    
    try {
      _recordingTimer?.cancel();
      _recordingTimer = null;
      _isRecording = false;
      _isPaused = false;
      
      _onRecordingStateChanged?.call();
      return true;
    } catch (e) {
      debugPrint('Error stopping recording: $e');
      return false;
    }
  }
  
  /// Take a screenshot.
  Future<File?> takeScreenshot() async {
    try {
      // In a real implementation, this would capture the AR view
      // For demo purposes, we'll use the image picker to simulate taking a screenshot
      final picker = ImagePicker();
      final pickedFile = await picker.pickImage(source: ImageSource.camera);
      
      if (pickedFile != null) {
        final tempDir = await getTemporaryDirectory();
        final fileName = 'ar_screenshot_${DateTime.now().millisecondsSinceEpoch}.jpg';
        final targetFile = File('${tempDir.path}/ar_recordings/$fileName');
        
        // Copy the picked file to our target location
        await File(pickedFile.path).copy(targetFile.path);
        
        // Add to screenshots list if recording
        if (_isRecording) {
          _screenshots.add(targetFile);
        }
        
        return targetFile;
      }
      
      return null;
    } catch (e) {
      debugPrint('Error taking screenshot: $e');
      return null;
    }
  }
  
  /// Record video.
  Future<File?> recordVideo() async {
    try {
      // In a real implementation, this would record the AR view
      // For demo purposes, we'll use the image picker to simulate recording a video
      final picker = ImagePicker();
      final pickedFile = await picker.pickVideo(source: ImageSource.camera);
      
      if (pickedFile != null) {
        final tempDir = await getTemporaryDirectory();
        final fileName = 'ar_video_${DateTime.now().millisecondsSinceEpoch}.mp4';
        final targetFile = File('${tempDir.path}/ar_recordings/$fileName');
        
        // Copy the picked file to our target location
        await File(pickedFile.path).copy(targetFile.path);
        
        // Set as video file if recording
        if (_isRecording) {
          _videoFile = targetFile;
        }
        
        return targetFile;
      }
      
      return null;
    } catch (e) {
      debugPrint('Error recording video: $e');
      return null;
    }
  }
  
  /// Share recording.
  Future<bool> shareRecording({
    required String title,
    required String description,
    List<String>? tags,
  }) async {
    try {
      final filesToShare = <XFile>[];
      
      // Add screenshots
      for (final screenshot in _screenshots) {
        filesToShare.add(XFile(screenshot.path));
      }
      
      // Add video
      if (_videoFile != null) {
        filesToShare.add(XFile(_videoFile!.path));
      }
      
      if (filesToShare.isEmpty) {
        return false;
      }
      
      // Share files
      await Share.shareXFiles(
        filesToShare,
        text: '$title\n\n$description${tags != null ? '\n\nTags: ${tags.join(', ')}' : ''}',
      );
      
      return true;
    } catch (e) {
      debugPrint('Error sharing recording: $e');
      return false;
    }
  }
  
  /// Save recording.
  Future<bool> saveRecording({
    required String title,
    required String description,
    List<String>? tags,
  }) async {
    try {
      // In a real implementation, this would save the recording to a database or cloud storage
      // For demo purposes, we'll just return true
      return true;
    } catch (e) {
      debugPrint('Error saving recording: $e');
      return false;
    }
  }
  
  /// Get recording duration in seconds.
  int get recordingDurationInSeconds => _recordingDurationInSeconds;
  
  /// Get recording duration as a formatted string.
  String get recordingDurationFormatted {
    final minutes = _recordingDurationInSeconds ~/ 60;
    final seconds = _recordingDurationInSeconds % 60;
    return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
  }
  
  /// Get recording state.
  bool get isRecording => _isRecording;
  
  /// Get paused state.
  bool get isPaused => _isPaused;
  
  /// Get screenshots.
  List<File> get screenshots => _screenshots;
  
  /// Get video file.
  File? get videoFile => _videoFile;
  
  /// Dispose the service.
  void dispose() {
    _recordingTimer?.cancel();
    _recordingTimer = null;
    _isRecording = false;
    _isPaused = false;
    _onRecordingStateChanged = null;
    _onRecordingTimerUpdated = null;
  }
}

/// A provider for the AR recording service.
final arRecordingServiceProvider = Provider<ARRecordingService>((ref) {
  return ARRecordingService();
});
