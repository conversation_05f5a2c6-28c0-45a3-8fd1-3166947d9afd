import 'dart:async';
import 'dart:io';

// Flutter imports
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';

// Package imports
import 'package:dio/dio.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

// Project imports
import 'package:culture_connect/services/logging_service.dart';

/// Provider for the ErrorHandlingService
final errorHandlingServiceProvider = Provider<ErrorHandlingService>((ref) {
  final loggingService = ref.watch(loggingServiceProvider);
  return ErrorHandlingService(loggingService);
});

/// Error types for categorizing errors
enum ErrorType {
  network,
  authentication,
  authorization,
  validation,
  server,
  database,
  fileSystem,
  unknown,
  ar,
  location,
  payment,
  booking,
  messaging,
  media,
  timeout,
}

/// Error severity levels
enum ErrorSeverity {
  low,
  medium,
  high,
  critical,
}

/// A comprehensive error handling service for the application
class ErrorHandlingService {
  final LoggingService _loggingService;

  /// Constructor
  ErrorHandlingService(this._loggingService);

  /// Initialize the error handling service
  Future<void> initialize() async {
    // Set up global error handlers
    FlutterError.onError = _handleFlutterError;

    // Set up Crashlytics
    await FirebaseCrashlytics.instance
        .setCrashlyticsCollectionEnabled(!kDebugMode);

    // Pass all uncaught errors to Crashlytics
    PlatformDispatcher.instance.onError = (error, stack) {
      _handlePlatformError(error, stack);
      return true;
    };

    _loggingService.info(
        'ErrorHandlingService', 'Error handling service initialized');
  }

  /// Handle Flutter framework errors
  void _handleFlutterError(FlutterErrorDetails details) {
    _loggingService.error(
      'FlutterError',
      details.exception.toString(),
      details.exception,
      details.stack,
    );

    // Report to crashlytics in non-debug mode
    if (!kDebugMode) {
      FirebaseCrashlytics.instance.recordFlutterError(details);
    }
  }

  /// Handle platform errors
  void _handlePlatformError(Object error, StackTrace stack) {
    _loggingService.error(
      'PlatformError',
      error.toString(),
      error,
      stack,
    );

    // Report to crashlytics in non-debug mode
    if (!kDebugMode) {
      FirebaseCrashlytics.instance.recordError(error, stack);
    }
  }

  /// Handle errors with context
  Future<void> handleError({
    required Object error,
    required String context,
    StackTrace? stackTrace,
    ErrorType type = ErrorType.unknown,
    ErrorSeverity severity = ErrorSeverity.medium,
    Map<String, dynamic>? additionalData,
  }) async {
    // Get stack trace if not provided
    stackTrace ??= StackTrace.current;

    // Determine error type if not specified
    if (type == ErrorType.unknown) {
      type = _determineErrorType(error);
    }

    // Log the error based on severity
    final logLevel = _getLogLevelForSeverity(severity);
    final errorMessage = _getErrorMessage(error, type);
    final errorData = {
      'error': error.toString(),
      'type': type.name,
      'severity': severity.name,
      ...?additionalData
    };

    switch (logLevel) {
      case LogLevel.warning:
        _loggingService.warning(
            'Error[$context]', errorMessage, errorData, stackTrace);
        break;
      case LogLevel.error:
        _loggingService.error(
            'Error[$context]', errorMessage, errorData, stackTrace);
        break;
      case LogLevel.critical:
        _loggingService.critical(
            'Error[$context]', errorMessage, errorData, stackTrace);
        break;
      default:
        _loggingService.error(
            'Error[$context]', errorMessage, errorData, stackTrace);
    }

    // Report to crashlytics for high and critical severity
    if (!kDebugMode &&
        (severity == ErrorSeverity.high ||
            severity == ErrorSeverity.critical)) {
      final isCritical = severity == ErrorSeverity.critical;
      await _reportToCrashlytics(
          error, stackTrace, context, type, additionalData,
          fatal: isCritical);
    }
  }

  /// Determine the type of error
  ErrorType _determineErrorType(Object error) {
    if (error is SocketException ||
        error is TimeoutException ||
        (error is DioException &&
            (error.type == DioExceptionType.connectionTimeout ||
                error.type == DioExceptionType.sendTimeout ||
                error.type == DioExceptionType.receiveTimeout ||
                error.type == DioExceptionType.connectionError))) {
      return ErrorType.network;
    } else if (error is FirebaseAuthException) {
      return ErrorType.authentication;
    } else if (error is DioException && error.response?.statusCode == 403) {
      return ErrorType.authorization;
    } else if (error is DioException && error.response?.statusCode == 400) {
      return ErrorType.validation;
    } else if (error is DioException &&
        error.response?.statusCode != null &&
        error.response!.statusCode! >= 500) {
      return ErrorType.server;
    } else if (error is FileSystemException) {
      return ErrorType.fileSystem;
    } else if (error is TimeoutException) {
      return ErrorType.timeout;
    }

    // Check error message for clues
    final errorString = error.toString().toLowerCase();
    if (errorString.contains('database') || errorString.contains('sql')) {
      return ErrorType.database;
    } else if (errorString.contains('permission') ||
        errorString.contains('access')) {
      return ErrorType.authorization;
    } else if (errorString.contains('network') ||
        errorString.contains('connection')) {
      return ErrorType.network;
    } else if (errorString.contains('auth') || errorString.contains('login')) {
      return ErrorType.authentication;
    } else if (errorString.contains('ar') ||
        errorString.contains('augmented')) {
      return ErrorType.ar;
    } else if (errorString.contains('location') ||
        errorString.contains('gps')) {
      return ErrorType.location;
    } else if (errorString.contains('payment') ||
        errorString.contains('transaction')) {
      return ErrorType.payment;
    } else if (errorString.contains('booking') ||
        errorString.contains('reservation')) {
      return ErrorType.booking;
    } else if (errorString.contains('message') ||
        errorString.contains('chat')) {
      return ErrorType.messaging;
    } else if (errorString.contains('media') ||
        errorString.contains('image') ||
        errorString.contains('video')) {
      return ErrorType.media;
    }

    return ErrorType.unknown;
  }

  /// Get appropriate log level for error severity
  LogLevel _getLogLevelForSeverity(ErrorSeverity severity) {
    switch (severity) {
      case ErrorSeverity.low:
        return LogLevel.warning;
      case ErrorSeverity.medium:
        return LogLevel.error;
      case ErrorSeverity.high:
      case ErrorSeverity.critical:
        return LogLevel.critical;
    }
  }

  /// Get a user-friendly error message
  String _getErrorMessage(Object error, ErrorType type) {
    // For known error types, provide more specific messages
    if (error is FirebaseAuthException) {
      return _getAuthErrorMessage(error);
    } else if (error is DioException) {
      return _getNetworkErrorMessage(error);
    }

    // Generic messages based on error type
    switch (type) {
      case ErrorType.network:
        return 'Network connection error. Please check your internet connection and try again.';
      case ErrorType.authentication:
        return 'Authentication error. Please sign in again.';
      case ErrorType.authorization:
        return 'You do not have permission to perform this action.';
      case ErrorType.validation:
        return 'Invalid data provided. Please check your input and try again.';
      case ErrorType.server:
        return 'Server error. Please try again later.';
      case ErrorType.database:
        return 'Database error. Please try again later.';
      case ErrorType.fileSystem:
        return 'File system error. Please check your storage permissions.';
      case ErrorType.ar:
        return 'AR feature error. Please ensure your device supports AR and try again.';
      case ErrorType.location:
        return 'Location service error. Please check your location permissions and try again.';
      case ErrorType.payment:
        return 'Payment processing error. Please try again or use a different payment method.';
      case ErrorType.booking:
        return 'Booking error. Please try again later.';
      case ErrorType.messaging:
        return 'Messaging error. Please try again later.';
      case ErrorType.media:
        return 'Media error. Please check your camera and storage permissions.';
      case ErrorType.timeout:
        return 'The operation timed out. Please try again later.';
      case ErrorType.unknown:
        return 'An unexpected error occurred. Please try again later.';
    }
  }

  /// Get a user-friendly message for authentication errors
  String _getAuthErrorMessage(FirebaseAuthException error) {
    switch (error.code) {
      case 'user-not-found':
        return 'No user found with this email address.';
      case 'wrong-password':
        return 'Incorrect password. Please try again.';
      case 'email-already-in-use':
        return 'An account already exists with this email address.';
      case 'weak-password':
        return 'The password is too weak. Please use a stronger password.';
      case 'invalid-email':
        return 'The email address is not valid.';
      case 'user-disabled':
        return 'This account has been disabled. Please contact support.';
      case 'requires-recent-login':
        return 'This operation requires recent authentication. Please sign in again.';
      case 'account-exists-with-different-credential':
        return 'An account already exists with the same email but different sign-in credentials.';
      case 'invalid-credential':
        return 'The authentication credential is invalid. Please try again.';
      case 'operation-not-allowed':
        return 'This operation is not allowed. Please contact support.';
      case 'too-many-requests':
        return 'Too many unsuccessful login attempts. Please try again later.';
      default:
        return 'Authentication error: ${error.message}';
    }
  }

  /// Get a user-friendly message for network errors
  String _getNetworkErrorMessage(DioException error) {
    switch (error.type) {
      case DioExceptionType.connectionTimeout:
        return 'Connection timeout. Please check your internet connection.';
      case DioExceptionType.sendTimeout:
        return 'Send timeout. Please check your internet connection.';
      case DioExceptionType.receiveTimeout:
        return 'Receive timeout. Please check your internet connection.';
      case DioExceptionType.badResponse:
        if (error.response?.statusCode == 400) {
          return 'Invalid request. Please check your input.';
        } else if (error.response?.statusCode == 401) {
          return 'Unauthorized. Please sign in again.';
        } else if (error.response?.statusCode == 403) {
          return 'Forbidden. You do not have permission to access this resource.';
        } else if (error.response?.statusCode == 404) {
          return 'Resource not found.';
        } else if (error.response?.statusCode == 500) {
          return 'Server error. Please try again later.';
        } else {
          return 'Network error: ${error.response?.statusCode}';
        }
      case DioExceptionType.cancel:
        return 'Request cancelled.';
      case DioExceptionType.connectionError:
        return 'Connection error. Please check your internet connection.';
      case DioExceptionType.unknown:
      default:
        return 'Network error: ${error.message}';
    }
  }

  /// Report error to Crashlytics
  Future<void> _reportToCrashlytics(
    Object error,
    StackTrace stackTrace,
    String context,
    ErrorType type,
    Map<String, dynamic>? additionalData, {
    bool fatal = false,
  }) async {
    try {
      // Set custom keys for context
      FirebaseCrashlytics.instance.setCustomKey('error_context', context);
      FirebaseCrashlytics.instance.setCustomKey('error_type', type.name);

      // Add additional data as custom keys
      if (additionalData != null) {
        for (final entry in additionalData.entries) {
          FirebaseCrashlytics.instance
              .setCustomKey(entry.key, entry.value.toString());
        }
      }

      // Record the error
      await FirebaseCrashlytics.instance.recordError(
        error,
        stackTrace,
        reason: context,
        fatal: fatal,
      );
    } catch (e) {
      _loggingService.error(
          'ErrorHandlingService', 'Failed to report to Crashlytics', e);
    }
  }

  /// Show a user-friendly error dialog
  void showErrorDialog(
    BuildContext context,
    String title,
    String message, {
    String? buttonText,
    VoidCallback? onPressed,
  }) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: onPressed ?? () => Navigator.of(context).pop(),
            child: Text(buttonText ?? 'OK'),
          ),
        ],
      ),
    );
  }

  /// Show a snackbar with an error message
  void showErrorSnackBar(
    BuildContext context,
    String message, {
    Duration duration = const Duration(seconds: 4),
  }) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        duration: duration,
        action: SnackBarAction(
          label: 'Dismiss',
          textColor: Colors.white,
          onPressed: () {
            ScaffoldMessenger.of(context).hideCurrentSnackBar();
          },
        ),
      ),
    );
  }
}
