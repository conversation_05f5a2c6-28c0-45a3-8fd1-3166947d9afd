import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/foundation.dart';
import 'package:path_provider/path_provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:workmanager/workmanager.dart';

import 'package:culture_connect/models/offline/offline_content.dart';
import 'package:culture_connect/models/offline/sync_status.dart';
import 'package:culture_connect/services/logging_service.dart';
import 'package:culture_connect/services/error_handling_service.dart';

/// The task name for background sync
const String backgroundSyncTask = 'com.cultureconnect.backgroundSync';

/// Service for managing offline mode functionality
class OfflineModeService {
  /// The connectivity instance
  final Connectivity _connectivity;
  
  /// The shared preferences instance
  final SharedPreferences _preferences;
  
  /// The logging service
  final LoggingService _loggingService;
  
  /// The error handling service
  final ErrorHandlingService? _errorHandlingService;
  
  /// Subscription for connectivity changes
  StreamSubscription<ConnectivityResult>? _connectivitySubscription;
  
  /// Whether the device is currently online
  bool _isOnline = true;
  
  /// Stream controller for connectivity status changes
  final StreamController<bool> _connectivityStreamController = StreamController<bool>.broadcast();
  
  /// Stream controller for sync status changes
  final StreamController<SyncStatusUpdate> _syncStatusStreamController = StreamController<SyncStatusUpdate>.broadcast();
  
  /// Map of offline content by ID
  final Map<String, OfflineContent> _offlineContent = {};
  
  /// Whether the service has been initialized
  bool _isInitialized = false;
  
  /// Directory for storing offline content
  late Directory _offlineContentDirectory;
  
  /// Creates a new offline mode service
  OfflineModeService(
    this._connectivity,
    this._preferences,
    this._loggingService,
    this._errorHandlingService,
  );
  
  /// Initialize the service
  Future<void> initialize() async {
    if (_isInitialized) return;
    
    try {
      // Initialize the offline content directory
      final appDir = await getApplicationDocumentsDirectory();
      _offlineContentDirectory = Directory('${appDir.path}/offline_content');
      
      if (!await _offlineContentDirectory.exists()) {
        await _offlineContentDirectory.create(recursive: true);
      }
      
      // Load offline content metadata
      await _loadOfflineContentMetadata();
      
      // Initialize connectivity monitoring
      await _initConnectivityMonitoring();
      
      // Initialize background sync
      await _initBackgroundSync();
      
      _isInitialized = true;
      _loggingService.info('OfflineModeService', 'Initialized successfully');
    } catch (e, stackTrace) {
      _loggingService.error('OfflineModeService', 'Failed to initialize', e, stackTrace);
      if (_errorHandlingService != null) {
        await _errorHandlingService!.handleError(
          error: e,
          context: 'OfflineModeService.initialize',
          stackTrace: stackTrace,
        );
      }
      rethrow;
    }
  }
  
  /// Initialize connectivity monitoring
  Future<void> _initConnectivityMonitoring() async {
    // Check initial connectivity
    final connectivityResult = await _connectivity.checkConnectivity();
    _isOnline = connectivityResult != ConnectivityResult.none;
    _connectivityStreamController.add(_isOnline);
    
    // Listen for connectivity changes
    _connectivitySubscription = _connectivity.onConnectivityChanged.listen((result) {
      final wasOnline = _isOnline;
      _isOnline = result != ConnectivityResult.none;
      
      // Notify listeners if status changed
      if (wasOnline != _isOnline) {
        _connectivityStreamController.add(_isOnline);
        
        // If we just came back online, start sync
        if (!wasOnline && _isOnline) {
          syncOfflineContent();
        }
      }
    });
    
    _loggingService.debug('OfflineModeService', 'Connectivity monitoring initialized, isOnline: $_isOnline');
  }
  
  /// Initialize background sync
  Future<void> _initBackgroundSync() async {
    try {
      // Initialize Workmanager
      await Workmanager().initialize(
        callbackDispatcher,
        isInDebugMode: kDebugMode,
      );
      
      // Schedule background sync based on user preferences
      await _scheduleBackgroundSync();
      
      _loggingService.debug('OfflineModeService', 'Background sync initialized');
    } catch (e, stackTrace) {
      _loggingService.error('OfflineModeService', 'Failed to initialize background sync', e, stackTrace);
      if (_errorHandlingService != null) {
        await _errorHandlingService!.handleError(
          error: e,
          context: 'OfflineModeService._initBackgroundSync',
          stackTrace: stackTrace,
        );
      }
    }
  }
  
  /// Schedule background sync based on user preferences
  Future<void> _scheduleBackgroundSync() async {
    // Get sync frequency from preferences
    final syncFrequency = _preferences.getString('offline_sync_frequency') ?? 'daily';
    final syncWifiOnly = _preferences.getBool('offline_sync_wifi_only') ?? true;
    final syncRequiresBattery = _preferences.getBool('offline_sync_requires_charging') ?? false;
    
    // Cancel existing tasks
    await Workmanager().cancelByTag(backgroundSyncTask);
    
    // Schedule new task based on frequency
    Duration frequency;
    switch (syncFrequency) {
      case 'hourly':
        frequency = const Duration(hours: 1);
        break;
      case 'daily':
        frequency = const Duration(hours: 24);
        break;
      case 'weekly':
        frequency = const Duration(days: 7);
        break;
      default:
        frequency = const Duration(hours: 24);
    }
    
    // Schedule the task
    await Workmanager().registerPeriodicTask(
      backgroundSyncTask,
      backgroundSyncTask,
      frequency: frequency,
      constraints: Constraints(
        networkType: syncWifiOnly ? NetworkType.connected : NetworkType.not_required,
        requiresBatteryNotLow: true,
        requiresCharging: syncRequiresBattery,
      ),
      existingWorkPolicy: ExistingWorkPolicy.replace,
      backoffPolicy: BackoffPolicy.exponential,
    );
    
    _loggingService.debug('OfflineModeService', 'Background sync scheduled with frequency: $syncFrequency');
  }
  
  /// Load offline content metadata from shared preferences
  Future<void> _loadOfflineContentMetadata() async {
    try {
      final contentList = _preferences.getStringList('offline_content_list') ?? [];
      
      _offlineContent.clear();
      for (final contentJson in contentList) {
        try {
          final content = OfflineContent.fromJson(jsonDecode(contentJson));
          _offlineContent[content.id] = content;
        } catch (e) {
          _loggingService.error('OfflineModeService', 'Failed to parse offline content', e);
        }
      }
      
      _loggingService.debug('OfflineModeService', 'Loaded ${_offlineContent.length} offline content items');
    } catch (e, stackTrace) {
      _loggingService.error('OfflineModeService', 'Failed to load offline content metadata', e, stackTrace);
      if (_errorHandlingService != null) {
        await _errorHandlingService!.handleError(
          error: e,
          context: 'OfflineModeService._loadOfflineContentMetadata',
          stackTrace: stackTrace,
        );
      }
    }
  }
  
  /// Save offline content metadata to shared preferences
  Future<void> _saveOfflineContentMetadata() async {
    try {
      final contentList = _offlineContent.values
          .map((content) => jsonEncode(content.toJson()))
          .toList();
      
      await _preferences.setStringList('offline_content_list', contentList);
      
      _loggingService.debug('OfflineModeService', 'Saved ${contentList.length} offline content items');
    } catch (e, stackTrace) {
      _loggingService.error('OfflineModeService', 'Failed to save offline content metadata', e, stackTrace);
      if (_errorHandlingService != null) {
        await _errorHandlingService!.handleError(
          error: e,
          context: 'OfflineModeService._saveOfflineContentMetadata',
          stackTrace: stackTrace,
        );
      }
    }
  }
  
  /// Get whether the device is currently online
  bool get isOnline => _isOnline;
  
  /// Get a stream of connectivity status changes
  Stream<bool> get connectivityStream => _connectivityStreamController.stream;
  
  /// Get a stream of sync status updates
  Stream<SyncStatusUpdate> get syncStatusStream => _syncStatusStreamController.stream;
  
  /// Get all offline content
  List<OfflineContent> getAllOfflineContent() {
    return _offlineContent.values.toList();
  }
  
  /// Get offline content by type
  List<OfflineContent> getOfflineContentByType(String contentType) {
    return _offlineContent.values
        .where((content) => content.contentType == contentType)
        .toList();
  }
  
  /// Check if content is available offline
  bool isContentAvailableOffline(String contentId) {
    return _offlineContent.containsKey(contentId) && 
           _offlineContent[contentId]!.syncStatus == SyncStatus.synced;
  }
  
  /// Get the sync status for content
  SyncStatus getContentSyncStatus(String contentId) {
    if (!_offlineContent.containsKey(contentId)) {
      return SyncStatus.notSynced;
    }
    
    return _offlineContent[contentId]!.syncStatus;
  }
  
  /// Add content to be available offline
  Future<bool> addContentForOfflineUse(OfflineContent content) async {
    try {
      // Update sync status
      final updatedContent = content.copyWith(
        syncStatus: SyncStatus.pending,
        lastSyncAttempt: DateTime.now(),
      );
      
      // Add to offline content map
      _offlineContent[content.id] = updatedContent;
      
      // Save metadata
      await _saveOfflineContentMetadata();
      
      // Notify listeners
      _syncStatusStreamController.add(SyncStatusUpdate(
        contentId: content.id,
        status: SyncStatus.pending,
        message: 'Preparing to download',
      ));
      
      // Start download if online
      if (_isOnline) {
        // This would be implemented by the specific content type service
        // For now, we'll just simulate a successful download
        await Future.delayed(const Duration(seconds: 2));
        
        // Update sync status
        final syncedContent = updatedContent.copyWith(
          syncStatus: SyncStatus.synced,
          lastSyncTime: DateTime.now(),
        );
        
        _offlineContent[content.id] = syncedContent;
        await _saveOfflineContentMetadata();
        
        // Notify listeners
        _syncStatusStreamController.add(SyncStatusUpdate(
          contentId: content.id,
          status: SyncStatus.synced,
          message: 'Downloaded successfully',
        ));
        
        return true;
      } else {
        // Will be synced when online
        _syncStatusStreamController.add(SyncStatusUpdate(
          contentId: content.id,
          status: SyncStatus.pending,
          message: 'Will download when online',
        ));
        
        return false;
      }
    } catch (e, stackTrace) {
      _loggingService.error('OfflineModeService', 'Failed to add content for offline use', e, stackTrace);
      if (_errorHandlingService != null) {
        await _errorHandlingService!.handleError(
          error: e,
          context: 'OfflineModeService.addContentForOfflineUse',
          stackTrace: stackTrace,
        );
      }
      
      // Update sync status
      final failedContent = content.copyWith(
        syncStatus: SyncStatus.failed,
        lastSyncAttempt: DateTime.now(),
        errorMessage: e.toString(),
      );
      
      _offlineContent[content.id] = failedContent;
      await _saveOfflineContentMetadata();
      
      // Notify listeners
      _syncStatusStreamController.add(SyncStatusUpdate(
        contentId: content.id,
        status: SyncStatus.failed,
        message: 'Download failed: ${e.toString()}',
      ));
      
      return false;
    }
  }
  
  /// Remove content from offline storage
  Future<bool> removeOfflineContent(String contentId) async {
    try {
      if (!_offlineContent.containsKey(contentId)) {
        return false;
      }
      
      // Get content
      final content = _offlineContent[contentId]!;
      
      // Remove content file if it exists
      final contentFile = File('${_offlineContentDirectory.path}/${content.id}');
      if (await contentFile.exists()) {
        await contentFile.delete();
      }
      
      // Remove from offline content map
      _offlineContent.remove(contentId);
      
      // Save metadata
      await _saveOfflineContentMetadata();
      
      // Notify listeners
      _syncStatusStreamController.add(SyncStatusUpdate(
        contentId: contentId,
        status: SyncStatus.notSynced,
        message: 'Removed from offline storage',
      ));
      
      return true;
    } catch (e, stackTrace) {
      _loggingService.error('OfflineModeService', 'Failed to remove offline content', e, stackTrace);
      if (_errorHandlingService != null) {
        await _errorHandlingService!.handleError(
          error: e,
          context: 'OfflineModeService.removeOfflineContent',
          stackTrace: stackTrace,
        );
      }
      
      return false;
    }
  }
  
  /// Sync all pending offline content
  Future<void> syncOfflineContent() async {
    if (!_isOnline) {
      _loggingService.debug('OfflineModeService', 'Cannot sync offline content: device is offline');
      return;
    }
    
    _loggingService.debug('OfflineModeService', 'Starting offline content sync');
    
    // Get all pending content
    final pendingContent = _offlineContent.values
        .where((content) => content.syncStatus == SyncStatus.pending || 
                           content.syncStatus == SyncStatus.failed)
        .toList();
    
    for (final content in pendingContent) {
      try {
        // Update sync status
        final updatingContent = content.copyWith(
          syncStatus: SyncStatus.syncing,
          lastSyncAttempt: DateTime.now(),
        );
        
        _offlineContent[content.id] = updatingContent;
        await _saveOfflineContentMetadata();
        
        // Notify listeners
        _syncStatusStreamController.add(SyncStatusUpdate(
          contentId: content.id,
          status: SyncStatus.syncing,
          message: 'Syncing...',
        ));
        
        // This would be implemented by the specific content type service
        // For now, we'll just simulate a successful sync
        await Future.delayed(const Duration(seconds: 2));
        
        // Update sync status
        final syncedContent = updatingContent.copyWith(
          syncStatus: SyncStatus.synced,
          lastSyncTime: DateTime.now(),
        );
        
        _offlineContent[content.id] = syncedContent;
        await _saveOfflineContentMetadata();
        
        // Notify listeners
        _syncStatusStreamController.add(SyncStatusUpdate(
          contentId: content.id,
          status: SyncStatus.synced,
          message: 'Synced successfully',
        ));
      } catch (e, stackTrace) {
        _loggingService.error('OfflineModeService', 'Failed to sync content: ${content.id}', e, stackTrace);
        if (_errorHandlingService != null) {
          await _errorHandlingService!.handleError(
            error: e,
            context: 'OfflineModeService.syncOfflineContent',
            stackTrace: stackTrace,
          );
        }
        
        // Update sync status
        final failedContent = content.copyWith(
          syncStatus: SyncStatus.failed,
          lastSyncAttempt: DateTime.now(),
          errorMessage: e.toString(),
        );
        
        _offlineContent[content.id] = failedContent;
        await _saveOfflineContentMetadata();
        
        // Notify listeners
        _syncStatusStreamController.add(SyncStatusUpdate(
          contentId: content.id,
          status: SyncStatus.failed,
          message: 'Sync failed: ${e.toString()}',
        ));
      }
    }
    
    _loggingService.debug('OfflineModeService', 'Offline content sync completed');
  }
  
  /// Update sync settings
  Future<void> updateSyncSettings({
    String? syncFrequency,
    bool? syncWifiOnly,
    bool? syncRequiresCharging,
  }) async {
    try {
      // Update preferences
      if (syncFrequency != null) {
        await _preferences.setString('offline_sync_frequency', syncFrequency);
      }
      
      if (syncWifiOnly != null) {
        await _preferences.setBool('offline_sync_wifi_only', syncWifiOnly);
      }
      
      if (syncRequiresCharging != null) {
        await _preferences.setBool('offline_sync_requires_charging', syncRequiresCharging);
      }
      
      // Reschedule background sync
      await _scheduleBackgroundSync();
      
      _loggingService.debug('OfflineModeService', 'Sync settings updated');
    } catch (e, stackTrace) {
      _loggingService.error('OfflineModeService', 'Failed to update sync settings', e, stackTrace);
      if (_errorHandlingService != null) {
        await _errorHandlingService!.handleError(
          error: e,
          context: 'OfflineModeService.updateSyncSettings',
          stackTrace: stackTrace,
        );
      }
    }
  }
  
  /// Get the current sync settings
  Map<String, dynamic> getSyncSettings() {
    return {
      'syncFrequency': _preferences.getString('offline_sync_frequency') ?? 'daily',
      'syncWifiOnly': _preferences.getBool('offline_sync_wifi_only') ?? true,
      'syncRequiresCharging': _preferences.getBool('offline_sync_requires_charging') ?? false,
    };
  }
  
  /// Get the total size of offline content
  Future<int> getTotalOfflineContentSize() async {
    int totalSize = 0;
    
    try {
      final files = await _offlineContentDirectory.list().toList();
      
      for (final file in files) {
        if (file is File) {
          totalSize += await file.length();
        }
      }
    } catch (e) {
      _loggingService.error('OfflineModeService', 'Failed to get total offline content size', e);
    }
    
    return totalSize;
  }
  
  /// Clear all offline content
  Future<bool> clearAllOfflineContent() async {
    try {
      // Delete all files
      final files = await _offlineContentDirectory.list().toList();
      
      for (final file in files) {
        if (file is File) {
          await file.delete();
        }
      }
      
      // Clear metadata
      _offlineContent.clear();
      await _saveOfflineContentMetadata();
      
      _loggingService.debug('OfflineModeService', 'All offline content cleared');
      return true;
    } catch (e, stackTrace) {
      _loggingService.error('OfflineModeService', 'Failed to clear all offline content', e, stackTrace);
      if (_errorHandlingService != null) {
        await _errorHandlingService!.handleError(
          error: e,
          context: 'OfflineModeService.clearAllOfflineContent',
          stackTrace: stackTrace,
        );
      }
      
      return false;
    }
  }
  
  /// Dispose the service
  void dispose() {
    _connectivitySubscription?.cancel();
    _connectivityStreamController.close();
    _syncStatusStreamController.close();
  }
}

/// Callback dispatcher for background sync
@pragma('vm:entry-point')
void callbackDispatcher() {
  Workmanager().executeTask((task, inputData) async {
    try {
      if (task == backgroundSyncTask) {
        // This would be implemented to perform the actual sync
        // For now, we'll just return success
        return true;
      }
      return false;
    } catch (e) {
      return false;
    }
  });
}
