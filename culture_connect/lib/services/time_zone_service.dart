import 'dart:convert';

// Flutter imports
import 'package:flutter/material.dart';

// Package imports
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:http/http.dart' as http;
import 'package:intl/intl.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:timezone/data/latest.dart' as tz_data;

// Project imports
import 'package:culture_connect/services/error_handling_service.dart';
import 'package:culture_connect/services/logging_service.dart';

/// Provider for the time zone service
final timeZoneServiceProvider = Provider<TimeZoneService>((ref) {
  final loggingService = ref.watch(loggingServiceProvider);
  final errorHandlingService = ref.watch(errorHandlingServiceProvider);
  return TimeZoneService(
    loggingService: loggingService,
    errorHandlingService: errorHandlingService,
  );
});

/// Provider for time zone data at a specific location
final timeZoneProvider =
    FutureProvider.family<TimeZoneData, LatLng>((ref, coordinates) async {
  final timeZoneService = ref.watch(timeZoneServiceProvider);
  return await timeZoneService.getTimeZoneForLocation(
    coordinates.latitude,
    coordinates.longitude,
  );
});

/// Provider for current local time at a specific location
final localTimeProvider =
    FutureProvider.family<DateTime, LatLng>((ref, coordinates) async {
  final timeZoneService = ref.watch(timeZoneServiceProvider);
  return await timeZoneService.getCurrentLocalTime(
    coordinates.latitude,
    coordinates.longitude,
  );
});

/// A service for handling time zone data and conversions
class TimeZoneService {
  // Google Maps Time Zone API key
  // In a real app, this would be stored in environment variables
  static const String _apiKey = 'demo_api_key';

  // Base URL for the Google Maps Time Zone API
  static const String _baseUrl =
      'https://maps.googleapis.com/maps/api/timezone/json';

  // Cache duration in minutes
  static const int _cacheDuration = 1440; // 24 hours

  // HTTP client
  final http.Client _client = http.Client();

  // Cache key prefix
  static const String _cacheKeyPrefix = 'timezone_cache_';

  // For logging
  final LoggingService? _loggingService;
  final ErrorHandlingService? _errorHandlingService;

  // Flag to track initialization
  bool _isInitialized = false;

  // Constructor
  TimeZoneService({
    LoggingService? loggingService,
    ErrorHandlingService? errorHandlingService,
  })  : _loggingService = loggingService,
        _errorHandlingService = errorHandlingService {
    _initialize();
  }

  /// Initialize the time zone service
  Future<void> _initialize() async {
    if (_isInitialized) return;

    try {
      // Initialize time zone data
      tz_data.initializeTimeZones();
      _isInitialized = true;
      _log('Time zone service initialized');
    } catch (e, stackTrace) {
      _logError('Error initializing time zone service', e, stackTrace);
    }
  }

  /// Get time zone data for a location
  Future<TimeZoneData> getTimeZoneForLocation(
    double latitude,
    double longitude,
  ) async {
    try {
      await _initialize();

      // Check connectivity
      final connectivityResult = await Connectivity().checkConnectivity();
      final bool isOnline = connectivityResult != ConnectivityResult.none;

      // Check cache first
      final cacheKey = '$_cacheKeyPrefix${latitude}_$longitude';
      final prefs = await SharedPreferences.getInstance();
      final cachedData = prefs.getString(cacheKey);

      if (!isOnline && cachedData != null) {
        _log('Using cached time zone data for ($latitude, $longitude)');
        final Map<String, dynamic> data = jsonDecode(cachedData);
        final timestamp = data['timestamp'] as int;
        final now = DateTime.now().millisecondsSinceEpoch;

        // Check if cache is still valid (within cache duration)
        if (now - timestamp < _cacheDuration * 60 * 1000) {
          return TimeZoneData.fromJson(data['timezone']);
        }
      }

      if (!isOnline) {
        return _getMockTimeZoneData();
      }

      // Fetch from API
      final timestamp = DateTime.now().millisecondsSinceEpoch ~/ 1000;
      final url = Uri.parse(
        '$_baseUrl?location=$latitude,$longitude&timestamp=$timestamp&key=$_apiKey',
      );

      final response = await _client.get(url);

      if (response.statusCode == 200) {
        final Map<String, dynamic> data = jsonDecode(response.body);

        if (data['status'] == 'OK') {
          final timeZoneData = TimeZoneData.fromJson(data);

          // Cache the data
          await prefs.setString(
              cacheKey,
              jsonEncode({
                'timestamp': DateTime.now().millisecondsSinceEpoch,
                'timezone': data,
              }));

          _log('Fetched time zone data for ($latitude, $longitude)');
          return timeZoneData;
        } else {
          _logError(
            'Failed to fetch time zone data',
            Exception('API returned status: ${data['status']}'),
          );
          return _getMockTimeZoneData();
        }
      } else {
        _logError(
          'Failed to fetch time zone data',
          Exception('API returned ${response.statusCode}: ${response.body}'),
        );
        return _getMockTimeZoneData();
      }
    } catch (e, stackTrace) {
      _logError('Error getting time zone data', e, stackTrace);
      return _getMockTimeZoneData();
    }
  }

  /// Get current local time for a location
  Future<DateTime> getCurrentLocalTime(
    double latitude,
    double longitude,
  ) async {
    try {
      final timeZoneData = await getTimeZoneForLocation(latitude, longitude);
      final now = DateTime.now().toUtc();

      // Apply the time zone offset
      final localTime = now.add(Duration(
        seconds: timeZoneData.rawOffset + timeZoneData.dstOffset,
      ));

      return localTime;
    } catch (e, stackTrace) {
      _logError('Error getting current local time', e, stackTrace);
      return DateTime.now();
    }
  }

  /// Format a date and time for a specific location
  Future<String> formatDateTime(
    DateTime dateTime,
    double latitude,
    double longitude, {
    String format = 'yyyy-MM-dd HH:mm',
  }) async {
    try {
      final timeZoneData = await getTimeZoneForLocation(latitude, longitude);
      final formatter = DateFormat(format);

      // Apply the time zone offset
      final localTime = dateTime.toUtc().add(Duration(
            seconds: timeZoneData.rawOffset + timeZoneData.dstOffset,
          ));

      return formatter.format(localTime);
    } catch (e, stackTrace) {
      _logError('Error formatting date time', e, stackTrace);
      return DateFormat(format).format(dateTime);
    }
  }

  /// Get mock time zone data for demo purposes
  TimeZoneData _getMockTimeZoneData() {
    return TimeZoneData(
      timeZoneId: 'Africa/Lagos',
      timeZoneName: 'West Africa Standard Time',
      rawOffset: 3600, // UTC+1
      dstOffset: 0,
    );
  }

  /// Log a message
  void _log(String message) {
    _loggingService?.debug('TimeZoneService', message);
    debugPrint('TimeZoneService: $message');
  }

  /// Log an error
  void _logError(String message, dynamic error, [StackTrace? stackTrace]) {
    _loggingService?.error('TimeZoneService', message, error, stackTrace);
    _errorHandlingService?.handleError(
        error: error,
        context: 'TimeZoneService',
        stackTrace: stackTrace,
        additionalData: {'message': message});
    debugPrint('TimeZoneService Error: $message - $error');
  }

  /// Dispose of resources
  void dispose() {
    _client.close();
  }
}

/// A model representing time zone data
class TimeZoneData {
  final String timeZoneId;
  final String timeZoneName;
  final int rawOffset;
  final int dstOffset;

  TimeZoneData({
    required this.timeZoneId,
    required this.timeZoneName,
    required this.rawOffset,
    required this.dstOffset,
  });

  /// Create a TimeZoneData instance from a JSON map
  factory TimeZoneData.fromJson(Map<String, dynamic> json) {
    return TimeZoneData(
      timeZoneId: json['timeZoneId'] as String,
      timeZoneName: json['timeZoneName'] as String,
      rawOffset: json['rawOffset'] as int,
      dstOffset: json['dstOffset'] as int,
    );
  }

  /// Get the total offset in seconds
  int get totalOffset => rawOffset + dstOffset;

  /// Get the total offset in hours
  double get totalOffsetHours => totalOffset / 3600;

  /// Get the formatted offset (e.g., UTC+1:00)
  String get formattedOffset {
    final hours = totalOffset ~/ 3600;
    final minutes = (totalOffset % 3600) ~/ 60;

    final sign = hours >= 0 ? '+' : '-';
    final absHours = hours.abs();
    final formattedMinutes = minutes.toString().padLeft(2, '0');

    return 'UTC$sign$absHours:$formattedMinutes';
  }
}
