import 'dart:convert';

// Flutter imports
import 'package:flutter/foundation.dart';

// Package imports
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:uuid/uuid.dart';

// Project imports
import 'package:culture_connect/models/verification_model.dart';
import 'package:culture_connect/providers/auth_provider.dart';
import 'package:culture_connect/services/logging_service.dart';

/// Enum representing the different background check providers
enum BackgroundCheckProvider {
  /// Checkr background check provider
  checkr,

  /// Sterling background check provider
  sterling,

  /// HireRight background check provider
  hireRight,

  /// GoodHire background check provider
  goodHire,
}

/// Enum representing the different background check types
enum BackgroundCheckType {
  /// Basic background check (identity verification, global watchlist)
  basic,

  /// Standard background check (basic + criminal records)
  standard,

  /// Enhanced background check (standard + education, employment verification)
  enhanced,

  /// Comprehensive background check (enhanced + credit check, driving records)
  comprehensive,
}

/// Enum representing the different background check statuses
enum BackgroundCheckStatus {
  /// Background check is pending
  pending,

  /// Background check is in progress
  inProgress,

  /// Background check is completed
  completed,

  /// Background check has failed
  failed,

  /// Background check is disputed
  disputed,
}

/// Model representing a background check
class BackgroundCheck {
  /// Unique identifier for the background check
  final String id;

  /// ID of the user this background check belongs to
  final String userId;

  /// Background check provider
  final BackgroundCheckProvider provider;

  /// Background check type
  final BackgroundCheckType type;

  /// Background check status
  final BackgroundCheckStatus status;

  /// Provider's reference ID for the background check
  final String? providerReferenceId;

  /// Date when the background check was requested
  final DateTime requestedAt;

  /// Date when the background check was completed
  final DateTime? completedAt;

  /// Result of the background check (pass/fail/consider)
  final String? result;

  /// URL to the background check report
  final String? reportUrl;

  /// Additional metadata for the background check
  final Map<String, dynamic>? metadata;

  /// Creates a new background check
  const BackgroundCheck({
    required this.id,
    required this.userId,
    required this.provider,
    required this.type,
    required this.status,
    this.providerReferenceId,
    required this.requestedAt,
    this.completedAt,
    this.result,
    this.reportUrl,
    this.metadata,
  });

  /// Creates a background check from a JSON map
  factory BackgroundCheck.fromJson(Map<String, dynamic> json) {
    return BackgroundCheck(
      id: json['id'] as String,
      userId: json['userId'] as String,
      provider: BackgroundCheckProvider.values.firstWhere(
        (e) => e.toString().split('.').last == json['provider'],
        orElse: () => BackgroundCheckProvider.checkr,
      ),
      type: BackgroundCheckType.values.firstWhere(
        (e) => e.toString().split('.').last == json['type'],
        orElse: () => BackgroundCheckType.basic,
      ),
      status: BackgroundCheckStatus.values.firstWhere(
        (e) => e.toString().split('.').last == json['status'],
        orElse: () => BackgroundCheckStatus.pending,
      ),
      providerReferenceId: json['providerReferenceId'] as String?,
      requestedAt: (json['requestedAt'] as Timestamp).toDate(),
      completedAt: json['completedAt'] != null
          ? (json['completedAt'] as Timestamp).toDate()
          : null,
      result: json['result'] as String?,
      reportUrl: json['reportUrl'] as String?,
      metadata: json['metadata'] as Map<String, dynamic>?,
    );
  }

  /// Converts this background check to a JSON map
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userId': userId,
      'provider': provider.toString().split('.').last,
      'type': type.toString().split('.').last,
      'status': status.toString().split('.').last,
      'providerReferenceId': providerReferenceId,
      'requestedAt': Timestamp.fromDate(requestedAt),
      if (completedAt != null) 'completedAt': Timestamp.fromDate(completedAt!),
      'result': result,
      'reportUrl': reportUrl,
      'metadata': metadata,
    };
  }

  /// Creates a copy of this background check with the given fields replaced with the new values
  BackgroundCheck copyWith({
    String? id,
    String? userId,
    BackgroundCheckProvider? provider,
    BackgroundCheckType? type,
    BackgroundCheckStatus? status,
    String? providerReferenceId,
    DateTime? requestedAt,
    DateTime? completedAt,
    String? result,
    String? reportUrl,
    Map<String, dynamic>? metadata,
  }) {
    return BackgroundCheck(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      provider: provider ?? this.provider,
      type: type ?? this.type,
      status: status ?? this.status,
      providerReferenceId: providerReferenceId ?? this.providerReferenceId,
      requestedAt: requestedAt ?? this.requestedAt,
      completedAt: completedAt ?? this.completedAt,
      result: result ?? this.result,
      reportUrl: reportUrl ?? this.reportUrl,
      metadata: metadata ?? this.metadata,
    );
  }
}

/// Service for handling background check operations
class BackgroundCheckService {
  final FirebaseFirestore _firestore;
  final String _userId;
  final LoggingService _loggingService;
  final Uuid _uuid = const Uuid();

  /// Creates a new background check service
  BackgroundCheckService(this._firestore, FirebaseStorage storage, this._userId,
      this._loggingService);

  /// Get all background checks for a user
  Future<List<BackgroundCheck>> getBackgroundChecks({String? userId}) async {
    try {
      final targetUserId = userId ?? _userId;
      final snapshot = await _firestore
          .collection('background_checks')
          .where('userId', isEqualTo: targetUserId)
          .orderBy('requestedAt', descending: true)
          .get();

      return snapshot.docs
          .map((doc) => BackgroundCheck.fromJson({...doc.data(), 'id': doc.id}))
          .toList();
    } catch (e) {
      _loggingService.error(
          'BackgroundCheckService', 'Error getting background checks', e);
      rethrow;
    }
  }

  /// Get a specific background check
  Future<BackgroundCheck?> getBackgroundCheck(String checkId) async {
    try {
      final doc =
          await _firestore.collection('background_checks').doc(checkId).get();

      if (!doc.exists) return null;

      return BackgroundCheck.fromJson({...doc.data()!, 'id': doc.id});
    } catch (e) {
      _loggingService.error(
          'BackgroundCheckService', 'Error getting background check', e);
      rethrow;
    }
  }

  /// Request a new background check
  Future<BackgroundCheck> requestBackgroundCheck({
    required BackgroundCheckProvider provider,
    required BackgroundCheckType type,
    required Map<String, dynamic> userData,
    required List<String> documentUrls,
  }) async {
    try {
      // Create a new background check document
      final docRef = _firestore.collection('background_checks').doc();
      final now = DateTime.now();

      // Create the background check object
      final backgroundCheck = BackgroundCheck(
        id: docRef.id,
        userId: _userId,
        provider: provider,
        type: type,
        status: BackgroundCheckStatus.pending,
        requestedAt: now,
        metadata: {
          'documentUrls': documentUrls,
          'userData': userData,
        },
      );

      // Save to Firestore
      await docRef.set(backgroundCheck.toJson());

      // In a real implementation, we would call the background check provider's API here
      // For this example, we'll simulate the API call
      _initiateBackgroundCheck(backgroundCheck);

      return backgroundCheck;
    } catch (e) {
      _loggingService.error(
          'BackgroundCheckService', 'Error requesting background check', e);
      rethrow;
    }
  }

  /// Initiate a background check with the provider
  Future<void> _initiateBackgroundCheck(BackgroundCheck backgroundCheck) async {
    try {
      // In a real implementation, this would call the provider's API
      // For this example, we'll simulate the API call

      // Simulate API call delay
      await Future.delayed(const Duration(seconds: 2));

      // Update the background check status to in progress
      final updatedCheck = backgroundCheck.copyWith(
        status: BackgroundCheckStatus.inProgress,
        providerReferenceId: 'BGC-${_uuid.v4()}',
      );

      // Update in Firestore
      await _firestore
          .collection('background_checks')
          .doc(backgroundCheck.id)
          .update(updatedCheck.toJson());

      // In a real implementation, we would set up a webhook to receive updates from the provider
      // For this example, we'll simulate the completion after a delay
      _simulateBackgroundCheckCompletion(updatedCheck);
    } catch (e) {
      _loggingService.error(
          'BackgroundCheckService', 'Error initiating background check', e);

      // Update the background check status to failed
      await _firestore
          .collection('background_checks')
          .doc(backgroundCheck.id)
          .update({
        'status': BackgroundCheckStatus.failed.toString().split('.').last,
        'metadata': {
          ...backgroundCheck.metadata ?? {},
          'error': e.toString(),
        },
      });
    }
  }

  /// Simulate background check completion (for demo purposes)
  Future<void> _simulateBackgroundCheckCompletion(
      BackgroundCheck backgroundCheck) async {
    try {
      // Simulate a delay for the background check to complete
      await Future.delayed(const Duration(seconds: 10));

      // Generate a random result (pass/fail/consider)
      final results = ['pass', 'consider', 'pass', 'pass']; // 75% pass rate
      final result = results[DateTime.now().millisecond % results.length];

      // Update the background check
      final updatedCheck = backgroundCheck.copyWith(
        status: BackgroundCheckStatus.completed,
        completedAt: DateTime.now(),
        result: result,
        reportUrl:
            'https://example.com/reports/${backgroundCheck.providerReferenceId}',
      );

      // Update in Firestore
      await _firestore
          .collection('background_checks')
          .doc(backgroundCheck.id)
          .update(updatedCheck.toJson());

      // If the background check passed, create a verification badge
      if (result == 'pass') {
        await _createBackgroundCheckBadge(updatedCheck);
      }
    } catch (e) {
      _loggingService.error('BackgroundCheckService',
          'Error simulating background check completion', e);
    }
  }

  /// Create a verification badge for a successful background check
  Future<void> _createBackgroundCheckBadge(
      BackgroundCheck backgroundCheck) async {
    try {
      // Create a new verification badge document
      final docRef = _firestore.collection('verification_badges').doc();
      final now = DateTime.now();

      // Create the badge
      final badge = VerificationBadge(
        id: docRef.id,
        userId: backgroundCheck.userId,
        type: VerificationType.background,
        status: VerificationStatus.approved,
        name: 'Background Check',
        description:
            'This user has passed a ${backgroundCheck.type.toString().split('.').last} background check.',
        issuedAt: now,
        expiresAt: now.add(const Duration(days: 365)), // Valid for 1 year
        approvedBy: 'system',
        approvedAt: now,
        metadata: {
          'backgroundCheckId': backgroundCheck.id,
          'provider': backgroundCheck.provider.toString().split('.').last,
          'type': backgroundCheck.type.toString().split('.').last,
          'reportUrl': backgroundCheck.reportUrl,
        },
      );

      // Save to Firestore
      await docRef.set(badge.toJson());
    } catch (e) {
      _loggingService.error(
          'BackgroundCheckService', 'Error creating background check badge', e);
    }
  }

  /// Check the status of a background check with the provider
  Future<void> checkBackgroundCheckStatus(String checkId) async {
    try {
      // Get the background check
      final backgroundCheck = await getBackgroundCheck(checkId);
      if (backgroundCheck == null) {
        throw Exception('Background check not found');
      }

      // In a real implementation, this would call the provider's API to check the status
      // For this example, we'll simulate the API call

      // Simulate API call delay
      await Future.delayed(const Duration(seconds: 1));

      // If the check is already completed or failed, do nothing
      if (backgroundCheck.status == BackgroundCheckStatus.completed ||
          backgroundCheck.status == BackgroundCheckStatus.failed) {
        return;
      }

      // Simulate a status update
      final statuses = [
        BackgroundCheckStatus.inProgress,
        BackgroundCheckStatus.inProgress,
        BackgroundCheckStatus.completed,
      ];
      final newStatus = statuses[DateTime.now().millisecond % statuses.length];

      // If the status is now completed, simulate completion
      if (newStatus == BackgroundCheckStatus.completed) {
        // Generate a random result (pass/fail/consider)
        final results = ['pass', 'consider', 'pass', 'pass']; // 75% pass rate
        final result = results[DateTime.now().millisecond % results.length];

        // Update the background check
        final updatedCheck = backgroundCheck.copyWith(
          status: BackgroundCheckStatus.completed,
          completedAt: DateTime.now(),
          result: result,
          reportUrl:
              'https://example.com/reports/${backgroundCheck.providerReferenceId}',
        );

        // Update in Firestore
        await _firestore
            .collection('background_checks')
            .doc(checkId)
            .update(updatedCheck.toJson());

        // If the background check passed, create a verification badge
        if (result == 'pass') {
          await _createBackgroundCheckBadge(updatedCheck);
        }
      } else {
        // Just update the status
        await _firestore.collection('background_checks').doc(checkId).update({
          'status': newStatus.toString().split('.').last,
        });
      }
    } catch (e) {
      _loggingService.error('BackgroundCheckService',
          'Error checking background check status', e);
      rethrow;
    }
  }
}

/// Provider for the background check service
final backgroundCheckServiceProvider = Provider<BackgroundCheckService>((ref) {
  final user = ref.watch(authStateProvider).user;
  if (user == null) {
    throw Exception(
        'User must be logged in to access background check services');
  }

  final firestore = FirebaseFirestore.instance;
  final storage = FirebaseStorage.instance;
  final loggingService = ref.watch(loggingServiceProvider);

  return BackgroundCheckService(firestore, storage, user.id, loggingService);
});

/// Stream provider for background checks
final backgroundChecksProvider = StreamProvider<List<BackgroundCheck>>((ref) {
  final user = ref.watch(authStateProvider).user;
  if (user == null) {
    throw Exception('User must be logged in to access background checks');
  }

  return FirebaseFirestore.instance
      .collection('background_checks')
      .where('userId', isEqualTo: user.id)
      .orderBy('requestedAt', descending: true)
      .snapshots()
      .map((snapshot) => snapshot.docs
          .map((doc) => BackgroundCheck.fromJson({...doc.data(), 'id': doc.id}))
          .toList());
});

/// Provider for the logging service
final loggingServiceProvider = Provider<LoggingService>((ref) {
  return LoggingService();
});
