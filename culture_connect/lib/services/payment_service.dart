import 'package:flutter/material.dart';
import 'package:uuid/uuid.dart';
import 'package:culture_connect/models/booking.dart';
import 'package:culture_connect/models/payment_transaction.dart';
import 'package:culture_connect/models/receipt.dart';
import 'package:culture_connect/models/payment_result.dart';
import 'package:culture_connect/models/payment/payment_method_model.dart';

/// Payment providers supported by the app
enum PaymentProvider {
  stripe,
  flutterwave,
  paypal,
  mpesa,
}

/// Service for handling payment-related operations
class PaymentService {
  static final PaymentService _instance = PaymentService._internal();
  factory PaymentService() => _instance;
  PaymentService._internal();

  // Global navigator key for accessing context
  static final GlobalKey<NavigatorState> navigatorKey =
      GlobalKey<NavigatorState>();

  bool _isInitialized = false;
  final List<PaymentTransaction> _transactions = [];
  final List<Receipt> _receipts = [];
  final List<PaymentMethodModel> _savedPaymentMethods = [];
  final Uuid _uuid = const Uuid();

  /// Initialize the payment service
  Future<void> initialize() async {
    if (_isInitialized) return;

    // In a real implementation, you would initialize payment SDKs here
    await _loadTransactionHistory();
    await _loadReceipts();
    await _loadSavedPaymentMethods();

    _isInitialized = true;
  }

  /// Process a payment
  Future<PaymentResult> processPayment({
    required Booking booking,
    required PaymentProvider provider,
    required String userEmail,
    required String userName,
    String? userPhone,
    String? paymentMethodId,
  }) async {
    if (!_isInitialized) {
      await initialize();
    }

    try {
      // Simulate payment processing
      await Future.delayed(const Duration(seconds: 2));

      // Generate a transaction ID
      final transactionId = _uuid.v4();

      // Create a payment transaction
      final transaction = PaymentTransaction(
        id: transactionId,
        bookingId: booking.id,
        paymentMethodId: paymentMethodId ?? 'default_method',
        type: TransactionType.payment,
        status: TransactionStatus.completed,
        amount: booking.totalAmount,
        currency: 'USD', // In a real app, this would come from the booking
        provider: provider.toString().split('.').last,
        providerTransactionId: 'prov_${_uuid.v4().substring(0, 8)}',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      // Save the transaction
      _transactions.add(transaction);
      await _saveTransactionHistory();

      // Generate a receipt
      final receipt = await generateReceipt(
        transactionId: transactionId,
        booking: booking,
        paymentMethodName: 'Credit Card',
      );

      return PaymentResult.success(
        transactionId: transactionId,
        additionalData: {
          'receiptId': receipt.id,
          'provider': provider.toString().split('.').last,
        },
      );
    } catch (e) {
      return PaymentResult(
        success: false,
        errorMessage: 'Payment processing failed: $e',
      );
    }
  }

  /// Get transaction history (simplified)
  Future<List<PaymentTransaction>> getTransactionHistory() async {
    if (!_isInitialized) {
      await initialize();
    }
    return _transactions;
  }

  /// Get transaction by ID
  Future<PaymentTransaction?> getTransaction(String transactionId) async {
    if (!_isInitialized) {
      await initialize();
    }
    return _transactions.firstWhere(
      (t) => t.id == transactionId,
      orElse: () => throw Exception('Transaction not found'),
    );
  }

  /// Process a refund
  Future<PaymentResult> processRefund({
    required String transactionId,
    required double amount,
    String? reason,
  }) async {
    if (!_isInitialized) {
      await initialize();
    }

    try {
      // Find the original transaction
      final originalTransaction = await getTransaction(transactionId);
      if (originalTransaction == null) {
        return const PaymentResult(
          success: false,
          errorMessage: 'Original transaction not found',
        );
      }

      // Simulate refund processing
      await Future.delayed(const Duration(seconds: 2));

      // Create a refund transaction
      final refundTransactionId = _uuid.v4();
      final refundTransaction = PaymentTransaction(
        id: refundTransactionId,
        bookingId: originalTransaction.bookingId,
        paymentMethodId: originalTransaction.paymentMethodId,
        type: TransactionType.refund,
        status: TransactionStatus.completed,
        amount: amount,
        currency: originalTransaction.currency,
        provider: originalTransaction.provider,
        providerTransactionId: 'ref_${_uuid.v4().substring(0, 8)}',
        metadata: {
          'originalTransactionId': transactionId,
          'refundReason': reason,
        },
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      // Save the refund transaction
      _transactions.add(refundTransaction);
      await _saveTransactionHistory();

      return PaymentResult.success(
        transactionId: refundTransactionId,
        additionalData: {
          'originalTransactionId': transactionId,
          'refundAmount': amount,
        },
      );
    } catch (e) {
      return PaymentResult(
        success: false,
        errorMessage: 'Refund processing failed: $e',
      );
    }
  }

  /// Generate a receipt for a transaction
  Future<Receipt> generateReceipt({
    required String transactionId,
    required Booking booking,
    required String paymentMethodName,
  }) async {
    if (!_isInitialized) {
      await initialize();
    }

    // Find the transaction
    final transaction = await getTransaction(transactionId);
    if (transaction == null) {
      throw Exception('Transaction not found');
    }

    // Generate a receipt number
    final receiptNumber = 'REC-${DateTime.now().millisecondsSinceEpoch}';

    // Create a receipt
    final receipt = Receipt(
      id: _uuid.v4(),
      transactionId: transactionId,
      bookingId: booking.id,
      userId:
          'user_id', // In a real app, this would come from the authenticated user
      guideId: 'guide_id', // In a real app, this would come from the booking
      experienceName:
          'Cultural Experience', // In a real app, this would come from the booking
      experienceDate: booking.date,
      participantCount: booking.participantCount,
      amount: booking.totalAmount,
      currency: 'USD', // In a real app, this would come from the booking
      taxAmount: booking.totalAmount * 0.1, // Example tax calculation
      taxRate: 10.0, // Example tax rate
      totalAmount: booking.totalAmount * 1.1, // Total with tax
      paymentMethodName: paymentMethodName,
      transactionStatus: transaction.status,
      receiptNumber: receiptNumber,
      createdAt: DateTime.now(),
    );

    // Save the receipt
    _receipts.add(receipt);
    await _saveReceipts();

    return receipt;
  }

  /// Get all receipts
  Future<List<Receipt>> getReceipts() async {
    if (!_isInitialized) {
      await initialize();
    }
    return _receipts;
  }

  /// Get receipt by ID
  Future<Receipt?> getReceipt(String receiptId) async {
    if (!_isInitialized) {
      await initialize();
    }
    try {
      return _receipts.firstWhere((r) => r.id == receiptId);
    } catch (e) {
      return null;
    }
  }

  /// Get saved payment methods
  Future<List<PaymentMethodModel>> getSavedPaymentMethods() async {
    if (!_isInitialized) {
      await initialize();
    }
    return List.from(_savedPaymentMethods);
  }

  /// Add a new payment method
  Future<PaymentMethodModel> addPaymentMethod({
    required PaymentMethodType type,
    required String name,
    String? last4,
    bool isDefault = false,
  }) async {
    if (!_isInitialized) {
      await initialize();
    }

    // If this is the first payment method, make it default
    if (_savedPaymentMethods.isEmpty) {
      isDefault = true;
    }

    // If setting as default, unset other defaults
    if (isDefault) {
      for (int i = 0; i < _savedPaymentMethods.length; i++) {
        _savedPaymentMethods[i] =
            _savedPaymentMethods[i].copyWith(isDefault: false);
      }
    }

    final paymentMethod = PaymentMethodModel(
      id: _uuid.v4(),
      type: type,
      name: name,
      isDefault: isDefault,
      details: last4 != null ? {'last4': last4} : {},
    );

    _savedPaymentMethods.add(paymentMethod);
    await _saveSavedPaymentMethods();

    return paymentMethod;
  }

  /// Remove a payment method
  Future<bool> removePaymentMethod(String paymentMethodId) async {
    if (!_isInitialized) {
      await initialize();
    }

    final index = _savedPaymentMethods
        .indexWhere((method) => method.id == paymentMethodId);
    if (index == -1) {
      return false;
    }

    final removedMethod = _savedPaymentMethods.removeAt(index);

    // If we removed the default method, set another as default
    if (removedMethod.isDefault && _savedPaymentMethods.isNotEmpty) {
      _savedPaymentMethods[0] =
          _savedPaymentMethods[0].copyWith(isDefault: true);
    }

    await _saveSavedPaymentMethods();
    return true;
  }

  /// Set a payment method as default
  Future<bool> setDefaultPaymentMethod(String paymentMethodId) async {
    if (!_isInitialized) {
      await initialize();
    }

    final methodIndex =
        _savedPaymentMethods.indexWhere((m) => m.id == paymentMethodId);
    if (methodIndex == -1) {
      return false;
    }

    // Unset all other defaults and set the selected method as default
    for (int i = 0; i < _savedPaymentMethods.length; i++) {
      _savedPaymentMethods[i] = _savedPaymentMethods[i].copyWith(
        isDefault: i == methodIndex,
      );
    }

    await _saveSavedPaymentMethods();
    return true;
  }

  // Private methods for persistence

  Future<void> _loadTransactionHistory() async {
    // In a real app, this would load from secure storage or a database
    _transactions.clear();
  }

  Future<void> _saveTransactionHistory() async {
    // In a real app, this would save to secure storage or a database
  }

  Future<void> _loadReceipts() async {
    // In a real app, this would load from secure storage or a database
    _receipts.clear();
  }

  Future<void> _saveReceipts() async {
    // In a real app, this would save to secure storage or a database
  }

  Future<void> _loadSavedPaymentMethods() async {
    // In a real app, this would load from secure storage or a database
    _savedPaymentMethods.clear();
    // Add some mock data for development
    _savedPaymentMethods.addAll([
      const PaymentMethodModel(
        id: 'pm_1',
        type: PaymentMethodType.creditCard,
        name: 'Visa ending in 4242',
        isDefault: true,
        details: {'last4': '4242'},
      ),
      const PaymentMethodModel(
        id: 'pm_2',
        type: PaymentMethodType.paypal,
        name: 'PayPal Account',
        isDefault: false,
        details: {},
      ),
    ]);
  }

  Future<void> _saveSavedPaymentMethods() async {
    // In a real app, this would save to secure storage or a database
    // For now, we'll just simulate the operation
  }
}
