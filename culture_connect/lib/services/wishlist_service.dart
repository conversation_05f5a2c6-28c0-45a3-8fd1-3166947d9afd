import 'dart:convert';

// Flutter imports
import 'package:flutter/material.dart';

// Package imports
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';

// Project imports
import 'package:culture_connect/providers/auth_provider.dart';
import 'package:culture_connect/services/error_handling_service.dart';
import 'package:culture_connect/services/logging_service.dart';

/// Provider for the wishlist service
final wishlistServiceProvider = Provider<WishlistService>((ref) {
  final loggingService = ref.watch(loggingServiceProvider);
  final errorHandlingService = ref.watch(errorHandlingServiceProvider);
  final authState = ref.watch(authStateProvider);

  return WishlistService(
    userId: authState.user?.id ?? 'anonymous',
    loggingService: loggingService,
    errorHandlingService: errorHandlingService,
  );
});

/// Provider for the wishlist
final wishlistProvider =
    StateNotifierProvider<WishlistNotifier, AsyncValue<List<String>>>((ref) {
  final wishlistService = ref.watch(wishlistServiceProvider);
  return WishlistNotifier(wishlistService);
});

/// Provider for checking if an experience is in the wishlist
final isInWishlistProvider = Provider.family<bool, String>((ref, experienceId) {
  final wishlistState = ref.watch(wishlistProvider);

  return wishlistState.when(
    data: (wishlist) => wishlist.contains(experienceId),
    loading: () => false,
    error: (_, __) => false,
  );
});

/// A service for managing the user's wishlist
class WishlistService {
  // User ID
  final String userId;

  // Cache key
  late final String _cacheKey;

  // For logging
  final LoggingService? _loggingService;
  final ErrorHandlingService? _errorHandlingService;

  // Constructor
  WishlistService({
    required this.userId,
    LoggingService? loggingService,
    ErrorHandlingService? errorHandlingService,
  })  : _loggingService = loggingService,
        _errorHandlingService = errorHandlingService {
    _cacheKey = 'wishlist_$userId';
  }

  /// Get the user's wishlist
  Future<List<String>> getWishlist() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final wishlistJson = prefs.getString(_cacheKey);

      if (wishlistJson == null) {
        return [];
      }

      final List<dynamic> wishlistData = jsonDecode(wishlistJson);
      final wishlist = wishlistData.cast<String>();

      _log('Retrieved wishlist with ${wishlist.length} items');
      return wishlist;
    } catch (e, stackTrace) {
      _logError('Error getting wishlist', e, stackTrace);
      return [];
    }
  }

  /// Add an experience to the wishlist
  Future<bool> addToWishlist(String experienceId) async {
    try {
      final wishlist = await getWishlist();

      if (wishlist.contains(experienceId)) {
        _log('Experience $experienceId already in wishlist');
        return true;
      }

      wishlist.add(experienceId);

      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_cacheKey, jsonEncode(wishlist));

      _log('Added experience $experienceId to wishlist');
      return true;
    } catch (e, stackTrace) {
      _logError('Error adding to wishlist', e, stackTrace);
      return false;
    }
  }

  /// Remove an experience from the wishlist
  Future<bool> removeFromWishlist(String experienceId) async {
    try {
      final wishlist = await getWishlist();

      if (!wishlist.contains(experienceId)) {
        _log('Experience $experienceId not in wishlist');
        return true;
      }

      wishlist.remove(experienceId);

      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_cacheKey, jsonEncode(wishlist));

      _log('Removed experience $experienceId from wishlist');
      return true;
    } catch (e, stackTrace) {
      _logError('Error removing from wishlist', e, stackTrace);
      return false;
    }
  }

  /// Toggle an experience in the wishlist
  Future<bool> toggleWishlist(String experienceId) async {
    try {
      final wishlist = await getWishlist();

      if (wishlist.contains(experienceId)) {
        return await removeFromWishlist(experienceId);
      } else {
        return await addToWishlist(experienceId);
      }
    } catch (e, stackTrace) {
      _logError('Error toggling wishlist', e, stackTrace);
      return false;
    }
  }

  /// Check if an experience is in the wishlist
  Future<bool> isInWishlist(String experienceId) async {
    try {
      final wishlist = await getWishlist();
      return wishlist.contains(experienceId);
    } catch (e, stackTrace) {
      _logError('Error checking wishlist', e, stackTrace);
      return false;
    }
  }

  /// Clear the wishlist
  Future<bool> clearWishlist() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_cacheKey);

      _log('Cleared wishlist');
      return true;
    } catch (e, stackTrace) {
      _logError('Error clearing wishlist', e, stackTrace);
      return false;
    }
  }

  /// Log a message
  void _log(String message) {
    _loggingService?.debug('WishlistService', message);
    debugPrint('WishlistService: $message');
  }

  /// Log an error
  void _logError(String message, dynamic error, [StackTrace? stackTrace]) {
    _loggingService?.error('WishlistService', message, error, stackTrace);
    _errorHandlingService?.handleError(
      error: error,
      context: 'WishlistService',
      stackTrace: stackTrace,
      additionalData: {'message': message},
    );
    debugPrint('WishlistService Error: $message - $error');
  }
}

/// A notifier for the wishlist
class WishlistNotifier extends StateNotifier<AsyncValue<List<String>>> {
  final WishlistService _wishlistService;

  WishlistNotifier(this._wishlistService) : super(const AsyncValue.loading()) {
    _loadWishlist();
  }

  /// Load the wishlist
  Future<void> _loadWishlist() async {
    try {
      state = const AsyncValue.loading();
      final wishlist = await _wishlistService.getWishlist();
      if (mounted) {
        state = AsyncValue.data(wishlist);
      }
    } catch (e, stackTrace) {
      if (mounted) {
        state = AsyncValue.error(e, stackTrace);
      }
    }
  }

  /// Add an experience to the wishlist
  Future<void> addToWishlist(String experienceId) async {
    try {
      await _wishlistService.addToWishlist(experienceId);
      if (mounted) {
        _loadWishlist();
      }
    } catch (e, stackTrace) {
      if (mounted) {
        state = AsyncValue.error(e, stackTrace);
      }
    }
  }

  /// Remove an experience from the wishlist
  Future<void> removeFromWishlist(String experienceId) async {
    try {
      await _wishlistService.removeFromWishlist(experienceId);
      if (mounted) {
        _loadWishlist();
      }
    } catch (e, stackTrace) {
      if (mounted) {
        state = AsyncValue.error(e, stackTrace);
      }
    }
  }

  /// Toggle an experience in the wishlist
  Future<void> toggleWishlist(String experienceId) async {
    try {
      await _wishlistService.toggleWishlist(experienceId);
      if (mounted) {
        _loadWishlist();
      }
    } catch (e, stackTrace) {
      if (mounted) {
        state = AsyncValue.error(e, stackTrace);
      }
    }
  }

  /// Clear the wishlist
  Future<void> clearWishlist() async {
    try {
      await _wishlistService.clearWishlist();
      if (mounted) {
        _loadWishlist();
      }
    } catch (e, stackTrace) {
      if (mounted) {
        state = AsyncValue.error(e, stackTrace);
      }
    }
  }

  /// Refresh the wishlist
  Future<void> refresh() async {
    if (mounted) {
      _loadWishlist();
    }
  }
}
