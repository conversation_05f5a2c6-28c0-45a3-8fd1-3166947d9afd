import 'dart:async';

import 'package:culture_connect/models/travel/document/document_reminder.dart';
import 'package:culture_connect/models/travel/document/travel_document_base.dart';
import 'package:culture_connect/services/travel/document/document_reminder_service.dart'
    as internal;
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:culture_connect/providers/travel/document/travel_document_providers.dart';

/// A service for managing document reminders.
///
/// This is a facade that provides access to the document reminder service.
/// It exists for backward compatibility with code that imports from
/// 'package:culture_connect/services/document_reminder_service.dart'.
class DocumentReminderService {
  /// The internal document reminder service
  final internal.DocumentReminderService _internalService;

  /// Creates a new document reminder service.
  DocumentReminderService()
      : _internalService = internal.DocumentReminderService();

  /// Creates a new document reminder service with a WidgetRef.
  ///
  /// This allows access to the full functionality of the document reminder service.
  factory DocumentReminderService.fromRef(WidgetRef ref) {
    return DocumentReminderService._withService(
      ref.read(documentReminderServiceProvider),
    );
  }

  /// Creates a new document reminder service with the specified service.
  DocumentReminderService._withService(this._internalService);

  /// Initialize the service.
  Future<void> initialize() async {
    await _internalService.initialize();
  }

  /// Get all reminders for the current user.
  Future<List<DocumentReminder>> getUserReminders() async {
    return _internalService.getUserReminders();
  }

  /// Get all reminders for a specific user.
  Future<List<DocumentReminder>> getReminders(String userId) async {
    return _internalService.getReminders(userId);
  }

  /// Get reminders for a specific document.
  Future<List<DocumentReminder>> getDocumentReminders(String documentId) async {
    return _internalService.getDocumentReminders(documentId);
  }

  /// Get reminders for a specific document.
  ///
  /// This is an alias for getDocumentReminders for backward compatibility.
  Future<List<DocumentReminder>> getRemindersForDocument(
      String documentId) async {
    return _internalService.getDocumentReminders(documentId);
  }

  /// Add a new reminder.
  Future<DocumentReminder> addReminder(DocumentReminder reminder) async {
    return _internalService.addReminder(reminder);
  }

  /// Update a reminder.
  Future<DocumentReminder> updateReminder(DocumentReminder reminder) async {
    return _internalService.updateReminder(reminder);
  }

  /// Delete a reminder.
  Future<void> deleteReminder(String id) async {
    return _internalService.deleteReminder(id);
  }

  /// Mark a reminder as read.
  Future<DocumentReminder> markReminderAsRead(String id) async {
    return _internalService.markReminderAsRead(id);
  }

  /// Dismiss a reminder.
  Future<DocumentReminder> dismissReminder(String id) async {
    return _internalService.dismissReminder(id);
  }

  /// Create reminders for a document.
  Future<List<DocumentReminder>> createDocumentReminders(
      TravelDocument document) async {
    return _internalService.createDocumentReminders(document);
  }

  /// Update reminders for a document.
  Future<List<DocumentReminder>> updateDocumentReminders(
      TravelDocument document) async {
    return _internalService.updateDocumentReminders(document);
  }

  /// Delete reminders for a document.
  Future<bool> deleteDocumentReminders(String documentId) async {
    return _internalService.deleteDocumentReminders(documentId);
  }

  /// Get all due reminders for the current user.
  Future<List<DocumentReminder>> getDueReminders() async {
    final reminders = await getUserReminders();
    return reminders.where((reminder) => reminder.isDue).toList();
  }

  /// Get all upcoming reminders for the current user.
  Future<List<DocumentReminder>> getUpcomingReminders() async {
    final reminders = await getUserReminders();
    return reminders.where((reminder) => reminder.isUpcoming).toList();
  }

  /// Get reminders by type for the current user.
  Future<List<DocumentReminder>> getRemindersByType(
      DocumentReminderType type) async {
    final reminders = await getUserReminders();
    return reminders.where((reminder) => reminder.type == type).toList();
  }

  /// Get reminders for documents expiring within a certain number of days.
  Future<List<DocumentReminder>> getRemindersForDocumentsExpiringWithinDays(
      int days) async {
    final reminders = await getUserReminders();
    return reminders.where((reminder) {
      return reminder.type == DocumentReminderType.expiry &&
          reminder.daysUntilDue <= days &&
          reminder.daysUntilDue >= 0;
    }).toList();
  }

  /// Get reminders for documents that need renewal within a certain number of days.
  Future<List<DocumentReminder>>
      getRemindersForDocumentsNeedingRenewalWithinDays(int days) async {
    final reminders = await getUserReminders();
    return reminders.where((reminder) {
      return reminder.type == DocumentReminderType.renewal &&
          reminder.daysUntilDue <= days &&
          reminder.daysUntilDue >= 0;
    }).toList();
  }
}
