import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:path/path.dart';
import 'package:sqflite/sqflite.dart';
import 'package:culture_connect/models/translation/group_translation_model.dart';
import 'package:culture_connect/models/translation/message_translation_metadata.dart';
import 'package:culture_connect/models/offline/sync_status.dart';

/// A helper class for managing the translation database
class TranslationDatabaseHelper {
  /// The singleton instance
  static final TranslationDatabaseHelper _instance =
      TranslationDatabaseHelper._internal();

  /// The database instance
  Database? _database;

  /// The database version
  static const int _databaseVersion = 1;

  /// The database name
  static const String _databaseName = 'translations.db';

  /// Private constructor
  TranslationDatabaseHelper._internal();

  /// Factory constructor
  factory TranslationDatabaseHelper() {
    return _instance;
  }

  /// Get the database instance
  Future<Database> get database async {
    if (_database != null) return _database!;

    _database = await _initDatabase();
    return _database!;
  }

  /// Initialize the database
  Future<Database> _initDatabase() async {
    final databasesPath = await getDatabasesPath();
    final path = join(databasesPath, _databaseName);

    return await openDatabase(
      path,
      version: _databaseVersion,
      onCreate: _onCreate,
      onUpgrade: _onUpgrade,
    );
  }

  /// Create the database tables
  Future<void> _onCreate(Database db, int version) async {
    // Group translation settings table
    await db.execute('''
      CREATE TABLE group_translation_settings (
        group_id TEXT PRIMARY KEY,
        settings TEXT NOT NULL,
        last_updated INTEGER NOT NULL,
        sync_status TEXT NOT NULL,
        sync_timestamp INTEGER
      )
    ''');

    // Group message translations table
    await db.execute('''
      CREATE TABLE group_message_translations (
        message_id TEXT PRIMARY KEY,
        group_id TEXT NOT NULL,
        original_text TEXT NOT NULL,
        original_language_code TEXT NOT NULL,
        translations TEXT NOT NULL,
        translated_at INTEGER NOT NULL,
        created_locally INTEGER NOT NULL,
        modified_locally INTEGER NOT NULL,
        sync_status TEXT NOT NULL,
        sync_timestamp INTEGER,
        conflict_status TEXT,
        server_version TEXT
      )
    ''');

    // Translation metadata table
    await db.execute('''
      CREATE TABLE translation_metadata (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        message_id TEXT NOT NULL,
        user_id TEXT NOT NULL,
        source_language TEXT NOT NULL,
        target_language TEXT NOT NULL,
        translated_text TEXT NOT NULL,
        confidence REAL,
        has_cultural_context INTEGER NOT NULL DEFAULT 0,
        has_slang_idiom INTEGER NOT NULL DEFAULT 0,
        has_pronunciation INTEGER NOT NULL DEFAULT 0,
        created_at INTEGER NOT NULL,
        UNIQUE(message_id, user_id, target_language)
      )
    ''');

    // Translation sync queue table
    await db.execute('''
      CREATE TABLE translation_sync_queue (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        message_id TEXT NOT NULL,
        group_id TEXT NOT NULL,
        priority INTEGER NOT NULL DEFAULT 0,
        retry_count INTEGER NOT NULL DEFAULT 0,
        last_retry INTEGER,
        created_at INTEGER NOT NULL,
        UNIQUE(message_id)
      )
    ''');

    // Translation conflicts table
    await db.execute('''
      CREATE TABLE translation_conflicts (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        message_id TEXT NOT NULL,
        local_version TEXT NOT NULL,
        server_version TEXT NOT NULL,
        resolved INTEGER NOT NULL DEFAULT 0,
        resolution_type TEXT,
        created_at INTEGER NOT NULL,
        resolved_at INTEGER,
        UNIQUE(message_id)
      )
    ''');

    // Offline translation cache table
    await db.execute('''
      CREATE TABLE offline_translation_cache (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        source_text TEXT NOT NULL,
        source_language TEXT NOT NULL,
        target_language TEXT NOT NULL,
        translated_text TEXT NOT NULL,
        confidence REAL,
        is_offline_generated INTEGER NOT NULL DEFAULT 1,
        created_at INTEGER NOT NULL,
        last_used_at INTEGER NOT NULL,
        use_count INTEGER NOT NULL DEFAULT 1,
        UNIQUE(source_text, source_language, target_language)
      )
    ''');

    // Voice translation cache table
    await db.execute('''
      CREATE TABLE voice_translation_cache (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        audio_hash TEXT NOT NULL,
        source_language TEXT NOT NULL,
        recognized_text TEXT,
        target_language TEXT,
        translated_text TEXT,
        translated_audio_path TEXT,
        confidence REAL,
        is_offline_generated INTEGER NOT NULL DEFAULT 1,
        created_at INTEGER NOT NULL,
        last_used_at INTEGER NOT NULL,
        use_count INTEGER NOT NULL DEFAULT 1,
        UNIQUE(audio_hash, source_language, target_language)
      )
    ''');

    // Create indexes
    await db.execute(
        'CREATE INDEX idx_group_message_translations_group_id ON group_message_translations(group_id)');
    await db.execute(
        'CREATE INDEX idx_translation_metadata_message_id ON translation_metadata(message_id)');
    await db.execute(
        'CREATE INDEX idx_translation_sync_queue_priority ON translation_sync_queue(priority)');
    await db.execute(
        'CREATE INDEX idx_offline_translation_cache_source ON offline_translation_cache(source_language, target_language)');
    await db.execute(
        'CREATE INDEX idx_offline_translation_cache_last_used ON offline_translation_cache(last_used_at)');
    await db.execute(
        'CREATE INDEX idx_voice_translation_cache_source ON voice_translation_cache(source_language, target_language)');
    await db.execute(
        'CREATE INDEX idx_voice_translation_cache_last_used ON voice_translation_cache(last_used_at)');
  }

  /// Upgrade the database
  Future<void> _onUpgrade(Database db, int oldVersion, int newVersion) async {
    // Handle database upgrades here
    if (oldVersion < 2) {
      // Add offline translation cache table
      await db.execute('''
        CREATE TABLE IF NOT EXISTS offline_translation_cache (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          source_text TEXT NOT NULL,
          source_language TEXT NOT NULL,
          target_language TEXT NOT NULL,
          translated_text TEXT NOT NULL,
          confidence REAL,
          is_offline_generated INTEGER NOT NULL DEFAULT 1,
          created_at INTEGER NOT NULL,
          last_used_at INTEGER NOT NULL,
          use_count INTEGER NOT NULL DEFAULT 1,
          UNIQUE(source_text, source_language, target_language)
        )
      ''');

      // Add voice translation cache table
      await db.execute('''
        CREATE TABLE IF NOT EXISTS voice_translation_cache (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          audio_hash TEXT NOT NULL,
          source_language TEXT NOT NULL,
          recognized_text TEXT,
          target_language TEXT,
          translated_text TEXT,
          translated_audio_path TEXT,
          confidence REAL,
          is_offline_generated INTEGER NOT NULL DEFAULT 1,
          created_at INTEGER NOT NULL,
          last_used_at INTEGER NOT NULL,
          use_count INTEGER NOT NULL DEFAULT 1,
          UNIQUE(audio_hash, source_language, target_language)
        )
      ''');

      // Create indexes for new tables
      await db.execute(
          'CREATE INDEX IF NOT EXISTS idx_offline_translation_cache_source ON offline_translation_cache(source_language, target_language)');
      await db.execute(
          'CREATE INDEX IF NOT EXISTS idx_offline_translation_cache_last_used ON offline_translation_cache(last_used_at)');
      await db.execute(
          'CREATE INDEX IF NOT EXISTS idx_voice_translation_cache_source ON voice_translation_cache(source_language, target_language)');
      await db.execute(
          'CREATE INDEX IF NOT EXISTS idx_voice_translation_cache_last_used ON voice_translation_cache(last_used_at)');
    }
  }

  /// Save group translation settings
  Future<void> saveGroupTranslationSettings(
      String groupId, GroupTranslationSettings settings,
      {SyncStatus syncStatus = SyncStatus.pending}) async {
    final db = await database;

    await db.insert(
      'group_translation_settings',
      {
        'group_id': groupId,
        'settings': settings.toJson().toString(),
        'last_updated': DateTime.now().millisecondsSinceEpoch,
        'sync_status': syncStatus.name,
        'sync_timestamp': syncStatus == SyncStatus.synced
            ? DateTime.now().millisecondsSinceEpoch
            : null,
      },
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  /// Get group translation settings
  Future<GroupTranslationSettings?> getGroupTranslationSettings(
      String groupId) async {
    final db = await database;

    final List<Map<String, dynamic>> maps = await db.query(
      'group_translation_settings',
      where: 'group_id = ?',
      whereArgs: [groupId],
    );

    if (maps.isEmpty) {
      return null;
    }

    try {
      final settingsJson = maps.first['settings'] as String;
      return GroupTranslationSettings.fromJson(jsonDecode(settingsJson));
    } catch (e) {
      debugPrint('Error parsing group translation settings: $e');
      return null;
    }
  }

  /// Save group message translation
  Future<void> saveGroupMessageTranslation(
    GroupMessageTranslation translation,
    String groupId, {
    bool createdLocally = true,
    bool modifiedLocally = true,
    SyncStatus syncStatus = SyncStatus.pending,
    String? serverVersion,
  }) async {
    final db = await database;

    await db.insert(
      'group_message_translations',
      {
        'message_id': translation.messageId,
        'group_id': groupId,
        'original_text': translation.originalText,
        'original_language_code': translation.originalLanguageCode,
        'translations': translation.translations.toString(),
        'translated_at': translation.translatedAt.millisecondsSinceEpoch,
        'created_locally': createdLocally ? 1 : 0,
        'modified_locally': modifiedLocally ? 1 : 0,
        'sync_status': syncStatus.name,
        'sync_timestamp': syncStatus == SyncStatus.synced
            ? DateTime.now().millisecondsSinceEpoch
            : null,
        'conflict_status': null,
        'server_version': serverVersion,
      },
      conflictAlgorithm: ConflictAlgorithm.replace,
    );

    // If this is a pending translation, add it to the sync queue
    if (syncStatus == SyncStatus.pending) {
      await addToSyncQueue(translation.messageId, groupId);
    }
  }

  /// Get group message translation
  Future<GroupMessageTranslation?> getGroupMessageTranslation(
      String messageId) async {
    final db = await database;

    final List<Map<String, dynamic>> maps = await db.query(
      'group_message_translations',
      where: 'message_id = ?',
      whereArgs: [messageId],
    );

    if (maps.isEmpty) {
      return null;
    }

    try {
      final map = maps.first;
      final translationsString = map['translations'] as String;
      final translationsMap =
          Map<String, String>.from(translationsString as Map);

      return GroupMessageTranslation(
        messageId: map['message_id'] as String,
        originalText: map['original_text'] as String,
        originalLanguageCode: map['original_language_code'] as String,
        translations: translationsMap,
        translatedAt:
            DateTime.fromMillisecondsSinceEpoch(map['translated_at'] as int),
      );
    } catch (e) {
      debugPrint('Error parsing group message translation: $e');
      return null;
    }
  }

  /// Get all group message translations for a group
  Future<List<GroupMessageTranslation>> getGroupMessageTranslations(
      String groupId) async {
    final db = await database;

    final List<Map<String, dynamic>> maps = await db.query(
      'group_message_translations',
      where: 'group_id = ?',
      whereArgs: [groupId],
    );

    return List.generate(maps.length, (i) {
      try {
        final map = maps[i];
        final translationsString = map['translations'] as String;
        final translationsMap =
            Map<String, String>.from(translationsString as Map);

        return GroupMessageTranslation(
          messageId: map['message_id'] as String,
          originalText: map['original_text'] as String,
          originalLanguageCode: map['original_language_code'] as String,
          translations: translationsMap,
          translatedAt:
              DateTime.fromMillisecondsSinceEpoch(map['translated_at'] as int),
        );
      } catch (e) {
        debugPrint('Error parsing group message translation: $e');
        return GroupMessageTranslation(
          messageId: 'error',
          originalText: 'Error parsing translation',
          originalLanguageCode: 'en',
          translations: {},
          translatedAt: DateTime.now(),
        );
      }
    });
  }

  /// Add a translation to the sync queue
  Future<void> addToSyncQueue(String messageId, String groupId,
      {int priority = 0}) async {
    final db = await database;

    await db.insert(
      'translation_sync_queue',
      {
        'message_id': messageId,
        'group_id': groupId,
        'priority': priority,
        'retry_count': 0,
        'created_at': DateTime.now().millisecondsSinceEpoch,
      },
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  /// Get the next batch of translations to sync
  Future<List<Map<String, dynamic>>> getNextSyncBatch(int limit) async {
    final db = await database;

    return await db.query(
      'translation_sync_queue',
      orderBy: 'priority DESC, created_at ASC',
      limit: limit,
    );
  }

  /// Remove a translation from the sync queue
  Future<void> removeFromSyncQueue(String messageId) async {
    final db = await database;

    await db.delete(
      'translation_sync_queue',
      where: 'message_id = ?',
      whereArgs: [messageId],
    );
  }

  /// Update the sync status of a translation
  Future<void> updateTranslationSyncStatus(
      String messageId, SyncStatus status) async {
    final db = await database;

    await db.update(
      'group_message_translations',
      {
        'sync_status': status.name,
        'sync_timestamp': status == SyncStatus.synced
            ? DateTime.now().millisecondsSinceEpoch
            : null,
      },
      where: 'message_id = ?',
      whereArgs: [messageId],
    );
  }

  /// Record a translation conflict
  Future<void> recordTranslationConflict(
    String messageId,
    String localVersion,
    String serverVersion,
  ) async {
    final db = await database;

    await db.insert(
      'translation_conflicts',
      {
        'message_id': messageId,
        'local_version': localVersion,
        'server_version': serverVersion,
        'resolved': 0,
        'created_at': DateTime.now().millisecondsSinceEpoch,
      },
      conflictAlgorithm: ConflictAlgorithm.replace,
    );

    // Update the conflict status in the translations table
    await db.update(
      'group_message_translations',
      {'conflict_status': 'unresolved'},
      where: 'message_id = ?',
      whereArgs: [messageId],
    );
  }

  /// Get all unresolved translation conflicts
  Future<List<Map<String, dynamic>>> getUnresolvedConflicts() async {
    final db = await database;

    return await db.query(
      'translation_conflicts',
      where: 'resolved = 0',
    );
  }

  /// Resolve a translation conflict
  Future<void> resolveTranslationConflict(
    String messageId,
    String resolutionType,
  ) async {
    final db = await database;

    await db.update(
      'translation_conflicts',
      {
        'resolved': 1,
        'resolution_type': resolutionType,
        'resolved_at': DateTime.now().millisecondsSinceEpoch,
      },
      where: 'message_id = ?',
      whereArgs: [messageId],
    );

    // Update the conflict status in the translations table
    await db.update(
      'group_message_translations',
      {'conflict_status': 'resolved'},
      where: 'message_id = ?',
      whereArgs: [messageId],
    );
  }

  /// Save a text translation to the offline cache
  Future<void> saveOfflineTextTranslation(
    String sourceText,
    String sourceLanguage,
    String targetLanguage,
    String translatedText,
    double confidence,
    bool isOfflineGenerated,
  ) async {
    final db = await database;

    final now = DateTime.now().millisecondsSinceEpoch;

    await db.insert(
      'offline_translation_cache',
      {
        'source_text': sourceText,
        'source_language': sourceLanguage,
        'target_language': targetLanguage,
        'translated_text': translatedText,
        'confidence': confidence,
        'is_offline_generated': isOfflineGenerated ? 1 : 0,
        'created_at': now,
        'last_used_at': now,
        'use_count': 1,
      },
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  /// Get a cached text translation
  Future<Map<String, dynamic>?> getCachedTextTranslation(
    String sourceText,
    String sourceLanguage,
    String targetLanguage,
  ) async {
    final db = await database;

    final List<Map<String, dynamic>> maps = await db.query(
      'offline_translation_cache',
      where: 'source_text = ? AND source_language = ? AND target_language = ?',
      whereArgs: [sourceText, sourceLanguage, targetLanguage],
    );

    if (maps.isEmpty) {
      return null;
    }

    // Update the last used timestamp and use count
    final now = DateTime.now().millisecondsSinceEpoch;
    final useCount = maps.first['use_count'] as int;

    await db.update(
      'offline_translation_cache',
      {
        'last_used_at': now,
        'use_count': useCount + 1,
      },
      where: 'id = ?',
      whereArgs: [maps.first['id']],
    );

    return maps.first;
  }

  /// Save a voice translation to the offline cache
  Future<void> saveOfflineVoiceTranslation(
    String audioHash,
    String sourceLanguage,
    String? recognizedText,
    String? targetLanguage,
    String? translatedText,
    String? translatedAudioPath,
    double? confidence,
    bool isOfflineGenerated,
  ) async {
    final db = await database;

    final now = DateTime.now().millisecondsSinceEpoch;

    await db.insert(
      'voice_translation_cache',
      {
        'audio_hash': audioHash,
        'source_language': sourceLanguage,
        'recognized_text': recognizedText,
        'target_language': targetLanguage,
        'translated_text': translatedText,
        'translated_audio_path': translatedAudioPath,
        'confidence': confidence,
        'is_offline_generated': isOfflineGenerated ? 1 : 0,
        'created_at': now,
        'last_used_at': now,
        'use_count': 1,
      },
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  /// Get a cached voice translation
  Future<Map<String, dynamic>?> getCachedVoiceTranslation(
    String audioHash,
    String sourceLanguage,
    String? targetLanguage,
  ) async {
    final db = await database;

    final List<Map<String, dynamic>> maps = await db.query(
      'voice_translation_cache',
      where: targetLanguage != null
          ? 'audio_hash = ? AND source_language = ? AND target_language = ?'
          : 'audio_hash = ? AND source_language = ?',
      whereArgs: targetLanguage != null
          ? [audioHash, sourceLanguage, targetLanguage]
          : [audioHash, sourceLanguage],
    );

    if (maps.isEmpty) {
      return null;
    }

    // Update the last used timestamp and use count
    final now = DateTime.now().millisecondsSinceEpoch;
    final useCount = maps.first['use_count'] as int;

    await db.update(
      'voice_translation_cache',
      {
        'last_used_at': now,
        'use_count': useCount + 1,
      },
      where: 'id = ?',
      whereArgs: [maps.first['id']],
    );

    return maps.first;
  }

  /// Get the total number of cached translations
  Future<int> getCachedTranslationCount() async {
    final db = await database;

    final textResult = await db
        .rawQuery('SELECT COUNT(*) as count FROM offline_translation_cache');
    final voiceResult = await db
        .rawQuery('SELECT COUNT(*) as count FROM voice_translation_cache');

    final textCount = textResult.first['count'] as int? ?? 0;
    final voiceCount = voiceResult.first['count'] as int? ?? 0;

    return textCount + voiceCount;
  }

  /// Get the total size of cached translations (approximate)
  Future<int> getCachedTranslationSize() async {
    final db = await database;

    // Get text translations size (approximate)
    final textResult = await db.rawQuery('''
      SELECT SUM(LENGTH(source_text) + LENGTH(translated_text)) as total_size
      FROM offline_translation_cache
    ''');

    // Get voice translations size (approximate)
    final voiceResult = await db.rawQuery('''
      SELECT SUM(LENGTH(recognized_text) + LENGTH(translated_text)) as total_size
      FROM voice_translation_cache
    ''');

    final textSize = textResult.first['total_size'] as int? ?? 0;
    final voiceSize = voiceResult.first['total_size'] as int? ?? 0;

    return textSize + voiceSize;
  }

  /// Clean up old cached translations
  Future<int> cleanupOldCachedTranslations(int maxAgeInDays) async {
    final db = await database;

    final cutoffTime = DateTime.now()
        .subtract(Duration(days: maxAgeInDays))
        .millisecondsSinceEpoch;

    // Delete old text translations
    final textDeleteCount = await db.delete(
      'offline_translation_cache',
      where: 'last_used_at < ?',
      whereArgs: [cutoffTime],
    );

    // Delete old voice translations
    final voiceDeleteCount = await db.delete(
      'voice_translation_cache',
      where: 'last_used_at < ?',
      whereArgs: [cutoffTime],
    );

    return textDeleteCount + voiceDeleteCount;
  }

  /// Clean up least used cached translations
  Future<int> cleanupLeastUsedCachedTranslations(int keepCount) async {
    final db = await database;

    // Get the current count
    final currentCount = await getCachedTranslationCount();

    // If we're already below the threshold, no need to clean up
    if (currentCount <= keepCount) {
      return 0;
    }

    // Calculate how many to delete
    final deleteCount = currentCount - keepCount;

    // Delete least used text translations
    final textDeleteCount = await db.rawDelete('''
      DELETE FROM offline_translation_cache
      WHERE id IN (
        SELECT id FROM offline_translation_cache
        ORDER BY use_count ASC, last_used_at ASC
        LIMIT ?
      )
    ''', [deleteCount]);

    // If we've deleted enough, return
    if (textDeleteCount >= deleteCount) {
      return textDeleteCount;
    }

    // Delete least used voice translations
    final voiceDeleteCount = await db.rawDelete('''
      DELETE FROM voice_translation_cache
      WHERE id IN (
        SELECT id FROM voice_translation_cache
        ORDER BY use_count ASC, last_used_at ASC
        LIMIT ?
      )
    ''', [deleteCount - textDeleteCount]);

    return textDeleteCount + voiceDeleteCount;
  }

  /// Close the database
  Future<void> close() async {
    final db = await database;
    db.close();
  }
}
