import 'package:flutter/material.dart';
import 'package:culture_connect/models/translation/language_model.dart';

/// Utility class for RTL (Right-to-Left) language support
class RTLUtils {
  /// List of RTL language codes
  static const List<String> rtlLanguageCodes = [
    'ar', // Arabic
    'fa', // Persian/Farsi
    'he', // Hebrew
    'ur', // Urdu
    'ps', // Pashto
    'sd', // Sindhi
    'ku', // Kurdish
    'dv', // Dhivehi
  ];
  
  /// Check if a language code is RTL
  static bool isRTL(String languageCode) {
    return rtlLanguageCodes.contains(languageCode.toLowerCase());
  }
  
  /// Get the text direction for a language code
  static TextDirection getTextDirection(String languageCode) {
    return isRTL(languageCode) ? TextDirection.rtl : TextDirection.ltr;
  }
  
  /// Get the text direction for a language model
  static TextDirection getTextDirectionForLanguage(LanguageModel language) {
    return getTextDirection(language.code);
  }
  
  /// Get the text alignment for a language code
  static TextAlign getTextAlignment(String languageCode) {
    return isRTL(languageCode) ? TextAlign.right : TextAlign.left;
  }
  
  /// Get the text alignment for a language model
  static TextAlign getTextAlignmentForLanguage(LanguageModel language) {
    return getTextAlignment(language.code);
  }
  
  /// Get the start alignment for a language code (used for Row/Column alignment)
  static MainAxisAlignment getStartAlignment(String languageCode) {
    return isRTL(languageCode) ? MainAxisAlignment.end : MainAxisAlignment.start;
  }
  
  /// Get the end alignment for a language code (used for Row/Column alignment)
  static MainAxisAlignment getEndAlignment(String languageCode) {
    return isRTL(languageCode) ? MainAxisAlignment.start : MainAxisAlignment.end;
  }
  
  /// Get the start cross alignment for a language code
  static CrossAxisAlignment getStartCrossAlignment(String languageCode) {
    return isRTL(languageCode) ? CrossAxisAlignment.end : CrossAxisAlignment.start;
  }
  
  /// Get the end cross alignment for a language code
  static CrossAxisAlignment getEndCrossAlignment(String languageCode) {
    return isRTL(languageCode) ? CrossAxisAlignment.start : CrossAxisAlignment.end;
  }
  
  /// Get the appropriate edge insets for a language code
  static EdgeInsets getEdgeInsets({
    required String languageCode,
    required double start,
    required double end,
    required double top,
    required double bottom,
  }) {
    return isRTL(languageCode)
        ? EdgeInsets.fromLTRB(end, top, start, bottom)
        : EdgeInsets.fromLTRB(start, top, end, bottom);
  }
  
  /// Get the appropriate edge insets for a language model
  static EdgeInsets getEdgeInsetsForLanguage({
    required LanguageModel language,
    required double start,
    required double end,
    required double top,
    required double bottom,
  }) {
    return getEdgeInsets(
      languageCode: language.code,
      start: start,
      end: end,
      top: top,
      bottom: bottom,
    );
  }
  
  /// Get the appropriate horizontal edge insets for a language code
  static EdgeInsets getHorizontalEdgeInsets({
    required String languageCode,
    required double start,
    required double end,
  }) {
    return isRTL(languageCode)
        ? EdgeInsets.only(left: end, right: start)
        : EdgeInsets.only(left: start, right: end);
  }
  
  /// Get the appropriate horizontal edge insets for a language model
  static EdgeInsets getHorizontalEdgeInsetsForLanguage({
    required LanguageModel language,
    required double start,
    required double end,
  }) {
    return getHorizontalEdgeInsets(
      languageCode: language.code,
      start: start,
      end: end,
    );
  }
  
  /// Get the appropriate symmetric edge insets
  static EdgeInsets getSymmetricEdgeInsets({
    required double horizontal,
    required double vertical,
  }) {
    return EdgeInsets.symmetric(
      horizontal: horizontal,
      vertical: vertical,
    );
  }
  
  /// Get the appropriate border radius for a language code
  static BorderRadius getBorderRadius({
    required String languageCode,
    required double topStart,
    required double topEnd,
    required double bottomStart,
    required double bottomEnd,
  }) {
    return isRTL(languageCode)
        ? BorderRadius.only(
            topLeft: Radius.circular(topEnd),
            topRight: Radius.circular(topStart),
            bottomLeft: Radius.circular(bottomEnd),
            bottomRight: Radius.circular(bottomStart),
          )
        : BorderRadius.only(
            topLeft: Radius.circular(topStart),
            topRight: Radius.circular(topEnd),
            bottomLeft: Radius.circular(bottomStart),
            bottomRight: Radius.circular(bottomEnd),
          );
  }
  
  /// Get the appropriate border radius for a language model
  static BorderRadius getBorderRadiusForLanguage({
    required LanguageModel language,
    required double topStart,
    required double topEnd,
    required double bottomStart,
    required double bottomEnd,
  }) {
    return getBorderRadius(
      languageCode: language.code,
      topStart: topStart,
      topEnd: topEnd,
      bottomStart: bottomStart,
      bottomEnd: bottomEnd,
    );
  }
  
  /// Get the appropriate icon for a language code (some icons need to be flipped for RTL)
  static IconData getDirectionalIcon({
    required String languageCode,
    required IconData ltrIcon,
    required IconData rtlIcon,
  }) {
    return isRTL(languageCode) ? rtlIcon : ltrIcon;
  }
  
  /// Get the appropriate icon for a language model
  static IconData getDirectionalIconForLanguage({
    required LanguageModel language,
    required IconData ltrIcon,
    required IconData rtlIcon,
  }) {
    return getDirectionalIcon(
      languageCode: language.code,
      ltrIcon: ltrIcon,
      rtlIcon: rtlIcon,
    );
  }
}
