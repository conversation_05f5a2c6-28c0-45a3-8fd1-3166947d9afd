import 'package:intl/intl.dart';

/// Utility functions for date-related operations
class DateUtils {
  /// Format a date relative to now
  static String formatRelativeDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);
    
    if (difference.inDays == 0) {
      return 'Today';
    } else if (difference.inDays == 1) {
      return 'Yesterday';
    } else if (difference.inDays < 7) {
      return '${difference.inDays} days ago';
    } else {
      return '${date.day}/${date.month}/${date.year}';
    }
  }
  
  /// Format a timestamp for display in a list
  static String formatTimestamp(DateTime timestamp) {
    final now = DateTime.now();
    final difference = now.difference(timestamp);
    
    if (difference.inDays == 0) {
      return DateFormat.jm().format(timestamp); // Today, show time
    } else if (difference.inDays == 1) {
      return 'Yesterday';
    } else if (difference.inDays < 7) {
      return DateFormat.E().format(timestamp); // Weekday
    } else {
      return DateFormat.yMd().format(timestamp); // Full date
    }
  }
  
  /// Format a date for display in a header
  static String formatDateHeader(DateTime date) {
    final now = DateTime.now();
    final yesterday = DateTime(now.year, now.month, now.day - 1);
    final dateOnly = DateTime(date.year, date.month, date.day);
    
    if (dateOnly.isAtSameMomentAs(DateTime(now.year, now.month, now.day))) {
      return 'Today';
    } else if (dateOnly.isAtSameMomentAs(yesterday)) {
      return 'Yesterday';
    } else {
      return DateFormat.yMMMMd().format(date); // e.g., "April 5, 2023"
    }
  }
  
  /// Format a time for display
  static String formatTime(DateTime time) {
    return DateFormat.jm().format(time); // e.g., "2:15 PM"
  }
  
  /// Format a date range for display
  static String formatDateRange(DateTime start, DateTime end) {
    final startFormat = DateFormat.yMMMd().format(start); // e.g., "Apr 5, 2023"
    final endFormat = DateFormat.yMMMd().format(end); // e.g., "Apr 10, 2023"
    
    return '$startFormat - $endFormat';
  }
}
