/// Exception thrown when an API request fails
class ApiException implements Exception {
  /// Creates a new API exception
  ApiException({
    required this.message,
    this.statusCode,
    this.data,
  });

  /// The error message
  final String message;

  /// The HTTP status code
  final int? statusCode;

  /// Additional data associated with the error
  final dynamic data;

  @override
  String toString() {
    return 'ApiException: $message (Status Code: ${statusCode ?? 'Unknown'})';
  }
}
