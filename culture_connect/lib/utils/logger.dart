import 'dart:developer' as developer;

/// A simple logger for the application
class Logger {
  /// The tag for the logger
  final String tag;
  
  /// Creates a new logger with the given tag
  Logger(this.tag);
  
  /// Log an info message
  void info(String message) {
    developer.log(message, name: tag, level: 800);
  }
  
  /// Log a debug message
  void debug(String message) {
    developer.log(message, name: tag, level: 500);
  }
  
  /// Log a warning message
  void warning(String message) {
    developer.log(message, name: tag, level: 900);
  }
  
  /// Log an error message
  void error(String message, [Object? error, StackTrace? stackTrace]) {
    developer.log(
      message,
      name: tag,
      level: 1000,
      error: error,
      stackTrace: stackTrace,
    );
  }
}
