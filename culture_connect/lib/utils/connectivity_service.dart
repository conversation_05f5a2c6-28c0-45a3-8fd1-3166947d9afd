import 'dart:async';
import 'package:connectivity_plus/connectivity_plus.dart';

/// Service for checking network connectivity
class ConnectivityService {
  // Singleton instance
  static final ConnectivityService _instance = ConnectivityService._internal();
  factory ConnectivityService() => _instance;
  ConnectivityService._internal();
  
  // Connectivity instance
  final Connectivity _connectivity = Connectivity();
  
  // Stream controller for connectivity status
  final _connectivityController = StreamController<bool>.broadcast();
  
  // Current connectivity status
  bool _isConnected = true;
  
  /// Stream of connectivity status changes
  Stream<bool> get connectivityStream => _connectivityController.stream;
  
  /// Current connectivity status
  bool get isConnected => _isConnected;
  
  /// Initialize the connectivity service
  Future<void> initialize() async {
    // Get initial connectivity status
    final connectivityResult = await _connectivity.checkConnectivity();
    _updateConnectionStatus(connectivityResult);
    
    // Listen for connectivity changes
    _connectivity.onConnectivityChanged.listen(_updateConnectionStatus);
  }
  
  /// Check if the device is currently connected to the internet
  Future<bool> checkConnectivity() async {
    final connectivityResult = await _connectivity.checkConnectivity();
    _updateConnectionStatus(connectivityResult);
    return _isConnected;
  }
  
  /// Update the connection status based on the connectivity result
  void _updateConnectionStatus(ConnectivityResult result) {
    final wasConnected = _isConnected;
    _isConnected = result != ConnectivityResult.none;
    
    // Only emit an event if the status has changed
    if (wasConnected != _isConnected) {
      _connectivityController.add(_isConnected);
    }
  }
  
  /// Dispose of resources
  void dispose() {
    _connectivityController.close();
  }
}
