import 'dart:io';
import 'dart:math' as math;
import 'package:path/path.dart' as path;
import 'package:mime/mime.dart';

/// Utility class for file operations
class FileUtils {
  /// Get the MIME type of a file
  static String? getMimeType(String filePath) {
    return lookupMimeType(filePath);
  }

  /// Get the file extension from a path
  static String getFileExtension(String filePath) {
    return path.extension(filePath);
  }

  /// Get the file name from a path
  static String getFileName(String filePath) {
    return path.basename(filePath);
  }

  /// Get the file name without extension from a path
  static String getFileNameWithoutExtension(String filePath) {
    return path.basenameWithoutExtension(filePath);
  }

  /// Get the directory path from a file path
  static String getDirectoryPath(String filePath) {
    return path.dirname(filePath);
  }

  /// Check if a file exists
  static Future<bool> fileExists(String filePath) async {
    return File(filePath).exists();
  }

  /// Get the size of a file in bytes
  static Future<int> getFileSize(String filePath) async {
    final file = File(filePath);
    return file.length();
  }

  /// Format file size to a human-readable string
  static String formatFileSize(int bytes) {
    if (bytes <= 0) return '0 B';

    const suffixes = ['B', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];
    final i = (math.log(bytes) / math.log(1024)).floor();

    return '${(bytes / math.pow(1024, i)).toStringAsFixed(2)} ${suffixes[i]}';
  }

  /// Create a directory if it doesn't exist
  static Future<Directory> createDirectory(String directoryPath) async {
    final directory = Directory(directoryPath);

    if (await directory.exists()) {
      return directory;
    }

    return directory.create(recursive: true);
  }

  /// Delete a file
  static Future<bool> deleteFile(String filePath) async {
    try {
      final file = File(filePath);

      if (await file.exists()) {
        await file.delete();
        return true;
      }

      return false;
    } catch (e) {
      return false;
    }
  }

  /// Copy a file to a new location
  static Future<File?> copyFile(
      String sourcePath, String destinationPath) async {
    try {
      final sourceFile = File(sourcePath);

      if (await sourceFile.exists()) {
        return sourceFile.copy(destinationPath);
      }

      return null;
    } catch (e) {
      return null;
    }
  }

  /// Move a file to a new location
  static Future<File?> moveFile(
      String sourcePath, String destinationPath) async {
    try {
      final sourceFile = File(sourcePath);

      if (await sourceFile.exists()) {
        return sourceFile.rename(destinationPath);
      }

      return null;
    } catch (e) {
      return null;
    }
  }

  /// Read a file as a string
  static Future<String?> readFileAsString(String filePath) async {
    try {
      final file = File(filePath);

      if (await file.exists()) {
        return file.readAsString();
      }

      return null;
    } catch (e) {
      return null;
    }
  }

  /// Write a string to a file
  static Future<File?> writeStringToFile(
      String filePath, String content) async {
    try {
      final file = File(filePath);
      return file.writeAsString(content);
    } catch (e) {
      return null;
    }
  }

  /// Get all files in a directory
  static Future<List<FileSystemEntity>> getFilesInDirectory(
      String directoryPath) async {
    try {
      final directory = Directory(directoryPath);

      if (await directory.exists()) {
        return directory.listSync();
      }

      return [];
    } catch (e) {
      return [];
    }
  }

  /// Check if a path is a directory
  static Future<bool> isDirectory(String path) async {
    return Directory(path).exists();
  }

  /// Check if a path is a file
  static Future<bool> isFile(String path) async {
    return File(path).exists();
  }

  /// Get the total size of all files in a directory
  static Future<int> getDirectorySize(String directoryPath) async {
    int totalSize = 0;

    try {
      final directory = Directory(directoryPath);

      if (await directory.exists()) {
        final files = await getFilesInDirectory(directoryPath);

        for (final file in files) {
          if (file is File) {
            totalSize += await file.length();
          } else if (file is Directory) {
            totalSize += await getDirectorySize(file.path);
          }
        }
      }
    } catch (e) {
      // Ignore errors
    }

    return totalSize;
  }
}

/// Extension methods for File
extension FileExtensions on File {
  /// Get the MIME type of the file
  Future<String?> getMimeType() async {
    return FileUtils.getMimeType(this.path);
  }

  /// Get the file extension
  String getExtension() {
    return FileUtils.getFileExtension(this.path);
  }

  /// Get the file name
  String getFileName() {
    return FileUtils.getFileName(this.path);
  }

  /// Get the file name without extension
  String getFileNameWithoutExtension() {
    return FileUtils.getFileNameWithoutExtension(this.path);
  }

  /// Get the formatted file size
  Future<String> getFormattedSize() async {
    final size = await length();
    return FileUtils.formatFileSize(size);
  }
}
