import 'dart:math' as math;
import 'package:flutter/material.dart';

/// Enum representing different types of refresh animations
enum RefreshAnimationType {
  /// Standard circular progress indicator
  circular,
  
  /// Liquid pull effect
  liquid,
  
  /// Bounce effect
  bounce,
  
  /// Flip effect
  flip,
  
  /// Pulse effect
  pulse,
}

/// Utility class for refresh animations
class RefreshAnimationUtils {
  /// Get the curve for a specific animation type
  static Curve getCurveForType(RefreshAnimationType type) {
    switch (type) {
      case RefreshAnimationType.circular:
        return Curves.linear;
      case RefreshAnimationType.liquid:
        return Curves.easeInOut;
      case RefreshAnimationType.bounce:
        return Curves.elasticOut;
      case RefreshAnimationType.flip:
        return Curves.easeInOutBack;
      case RefreshAnimationType.pulse:
        return Curves.easeInOutSine;
    }
  }
  
  /// Get the duration for a specific animation type
  static Duration getDurationForType(RefreshAnimationType type) {
    switch (type) {
      case RefreshAnimationType.circular:
        return const Duration(milliseconds: 1500);
      case RefreshAnimationType.liquid:
        return const Duration(milliseconds: 800);
      case RefreshAnimationType.bounce:
        return const Duration(milliseconds: 1200);
      case RefreshAnimationType.flip:
        return const Duration(milliseconds: 900);
      case RefreshAnimationType.pulse:
        return const Duration(milliseconds: 1000);
    }
  }
  
  /// Create a rotation animation
  static Animation<double> createRotationAnimation(
    AnimationController controller, {
    double begin = 0.0,
    double end = 2 * math.pi,
    Curve curve = Curves.linear,
  }) {
    return Tween<double>(
      begin: begin,
      end: end,
    ).animate(
      CurvedAnimation(
        parent: controller,
        curve: curve,
      ),
    );
  }
  
  /// Create a scale animation
  static Animation<double> createScaleAnimation(
    AnimationController controller, {
    double begin = 1.0,
    double end = 1.5,
    Curve curve = Curves.easeInOut,
  }) {
    return Tween<double>(
      begin: begin,
      end: end,
    ).animate(
      CurvedAnimation(
        parent: controller,
        curve: curve,
      ),
    );
  }
  
  /// Create a color animation
  static Animation<Color?> createColorAnimation(
    AnimationController controller, {
    required Color begin,
    required Color end,
    Curve curve = Curves.easeInOut,
  }) {
    return ColorTween(
      begin: begin,
      end: end,
    ).animate(
      CurvedAnimation(
        parent: controller,
        curve: curve,
      ),
    );
  }
  
  /// Create a position animation
  static Animation<Offset> createPositionAnimation(
    AnimationController controller, {
    Offset begin = Offset.zero,
    required Offset end,
    Curve curve = Curves.easeInOut,
  }) {
    return Tween<Offset>(
      begin: begin,
      end: end,
    ).animate(
      CurvedAnimation(
        parent: controller,
        curve: curve,
      ),
    );
  }
  
  /// Create a bounce animation
  static Animation<double> createBounceAnimation(
    AnimationController controller, {
    double begin = 0.0,
    double end = 1.0,
  }) {
    return Tween<double>(
      begin: begin,
      end: end,
    ).animate(
      CurvedAnimation(
        parent: controller,
        curve: Curves.elasticOut,
      ),
    );
  }
  
  /// Create a liquid animation
  static Animation<double> createLiquidAnimation(
    AnimationController controller, {
    double begin = 0.0,
    double end = 1.0,
  }) {
    return Tween<double>(
      begin: begin,
      end: end,
    ).animate(
      CurvedAnimation(
        parent: controller,
        curve: Curves.easeInOut,
      ),
    );
  }
  
  /// Create a flip animation
  static Animation<double> createFlipAnimation(
    AnimationController controller, {
    double begin = 0.0,
    double end = 1.0,
  }) {
    return Tween<double>(
      begin: begin,
      end: end,
    ).animate(
      CurvedAnimation(
        parent: controller,
        curve: Curves.easeInOutBack,
      ),
    );
  }
  
  /// Create a pulse animation
  static Animation<double> createPulseAnimation(
    AnimationController controller, {
    double begin = 1.0,
    double end = 1.2,
  }) {
    return Tween<double>(
      begin: begin,
      end: end,
    ).animate(
      CurvedAnimation(
        parent: controller,
        curve: Curves.easeInOutSine,
      ),
    );
  }
  
  /// Get the display name for a refresh animation type
  static String getDisplayName(RefreshAnimationType type) {
    switch (type) {
      case RefreshAnimationType.circular:
        return 'Circular';
      case RefreshAnimationType.liquid:
        return 'Liquid';
      case RefreshAnimationType.bounce:
        return 'Bounce';
      case RefreshAnimationType.flip:
        return 'Flip';
      case RefreshAnimationType.pulse:
        return 'Pulse';
    }
  }
  
  /// Get the icon for a refresh animation type
  static IconData getIcon(RefreshAnimationType type) {
    switch (type) {
      case RefreshAnimationType.circular:
        return Icons.refresh;
      case RefreshAnimationType.liquid:
        return Icons.water_drop;
      case RefreshAnimationType.bounce:
        return Icons.height;
      case RefreshAnimationType.flip:
        return Icons.flip;
      case RefreshAnimationType.pulse:
        return Icons.radio_button_checked;
    }
  }
}
