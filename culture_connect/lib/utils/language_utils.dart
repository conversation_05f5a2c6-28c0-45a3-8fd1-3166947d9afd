/// Utility functions for language-related operations
class LanguageUtils {
  /// Get the flag emoji for a language code
  static String getLanguageFlag(String languageCode) {
    // This is a simplified version - in a real app, you would use a more comprehensive mapping
    switch (languageCode) {
      case 'en':
        return '🇺🇸';
      case 'fr':
        return '🇫🇷';
      case 'es':
        return '🇪🇸';
      case 'de':
        return '🇩🇪';
      case 'it':
        return '🇮🇹';
      case 'pt':
        return '🇵🇹';
      case 'ru':
        return '🇷🇺';
      case 'zh':
        return '🇨🇳';
      case 'ja':
        return '🇯🇵';
      case 'ko':
        return '🇰🇷';
      case 'ar':
        return '🇸🇦';
      case 'hi':
        return '🇮🇳';
      case 'bn':
        return '🇧🇩';
      case 'sw':
        return '🇰🇪';
      case 'yo':
        return '🇳🇬';
      case 'ha':
        return '🇳🇬';
      case 'ig':
        return '🇳🇬';
      default:
        return '🌐';
    }
  }
  
  /// Get the name for a language code
  static String getLanguageName(String languageCode) {
    // This is a simplified version - in a real app, you would use a more comprehensive mapping
    switch (languageCode) {
      case 'en':
        return 'English';
      case 'fr':
        return 'French';
      case 'es':
        return 'Spanish';
      case 'de':
        return 'German';
      case 'it':
        return 'Italian';
      case 'pt':
        return 'Portuguese';
      case 'ru':
        return 'Russian';
      case 'zh':
        return 'Chinese';
      case 'ja':
        return 'Japanese';
      case 'ko':
        return 'Korean';
      case 'ar':
        return 'Arabic';
      case 'hi':
        return 'Hindi';
      case 'bn':
        return 'Bengali';
      case 'sw':
        return 'Swahili';
      case 'yo':
        return 'Yoruba';
      case 'ha':
        return 'Hausa';
      case 'ig':
        return 'Igbo';
      default:
        return languageCode;
    }
  }
  
  /// Check if a language is right-to-left
  static bool isRTL(String languageCode) {
    return ['ar', 'he', 'ur', 'fa'].contains(languageCode);
  }
  
  /// Get the text direction for a language
  static bool getTextDirection(String languageCode) {
    return isRTL(languageCode);
  }
}
