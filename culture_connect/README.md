# CultureConnect

A mobile application connecting tourists with local guides for authentic cultural experiences, featuring immersive AR content.

[![CultureConnect CI/CD](https://github.com/cultureconnect/mobile-app/actions/workflows/ci.yml/badge.svg)](https://github.com/cultureconnect/mobile-app/actions/workflows/ci.yml)
[![codecov](https://codecov.io/gh/cultureconnect/mobile-app/branch/main/graph/badge.svg)](https://codecov.io/gh/cultureconnect/mobile-app)

## Features

- **Immersive AR Experiences**: Explore cultural landmarks with augmented reality
- **Voice Commands**: Hands-free interaction with AR content
- **Content Creation**: Create and share your own AR experiences
- **Accessibility**: Inclusive design with features for all users
- **Offline Mode**: Access content without an internet connection
- **Social Sharing**: Share your experiences with friends and community

## Getting Started

### Prerequisites

- Flutter SDK 3.19.0 or higher
- Dart SDK 3.0.0 or higher
- Android Studio / Xcode
- ARCore (Android) / ARKit (iOS) compatible device

### Installation

1. Clone the repository:
   ```bash
   git clone https://github.com/cultureconnect/mobile-app.git
   cd culture_connect
   ```

2. Install dependencies:
   ```bash
   flutter pub get
   ```

3. Run the app:
   ```bash
   flutter run
   ```

## Architecture

CultureConnect follows a layered architecture with Riverpod for state management. For more details, see the [Architecture Guide](docs/developer_guides/architecture_guide.md).

## Testing

CultureConnect has a comprehensive testing strategy including unit, widget, integration, and performance tests.

### Running Tests

To run all tests:

```bash
cd culture_connect
./test/run_tests.sh
```

To run specific test types:

```bash
# Unit tests
flutter test test/unit/

# Widget tests
flutter test test/widget/

# Integration tests (requires a connected device)
flutter test test/integration/

# Performance tests (requires a connected device)
flutter test test/performance/
```

### Test Coverage

To generate a test coverage report:

```bash
flutter test --coverage
genhtml coverage/lcov.info -o coverage/html
```

Then open `coverage/html/index.html` in your browser.

## Documentation

- [Code Documentation Guide](docs/code_documentation_guide.md)
- [Architecture Guide](docs/developer_guides/architecture_guide.md)
- [User Guides](docs/user_guides/)

## Release Process

CultureConnect uses semantic versioning. To create a new release:

```bash
# Start a new release
./scripts/release_manager.sh start [major|minor|patch]

# Finish a release
./scripts/release_manager.sh finish [version]
```

## Contributing

Please read our [Contributing Guide](CONTRIBUTING.md) for details on our code of conduct and the process for submitting pull requests.

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.
