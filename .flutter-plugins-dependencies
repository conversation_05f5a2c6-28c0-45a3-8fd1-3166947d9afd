{"info": "This is a generated file; do not edit or check into version control.", "plugins": {"ios": [{"name": "audio_session", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/audio_session-0.1.25/", "native_build": true, "dependencies": []}, {"name": "camera_avfoundation", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.19/", "native_build": true, "dependencies": []}, {"name": "cloud_firestore", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cloud_firestore-4.17.5/", "native_build": true, "dependencies": ["firebase_core"]}, {"name": "connectivity_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus-5.0.2/", "native_build": true, "dependencies": []}, {"name": "file_picker", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file_picker-9.2.3/", "native_build": true, "dependencies": []}, {"name": "firebase_analytics", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_analytics-10.10.7/", "native_build": true, "dependencies": ["firebase_core"]}, {"name": "firebase_app_check", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_app_check-0.2.2/", "native_build": true, "dependencies": ["firebase_core"]}, {"name": "firebase_auth", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_auth-4.16.0/", "native_build": true, "dependencies": ["firebase_core"]}, {"name": "firebase_core", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core-2.32.0/", "native_build": true, "dependencies": []}, {"name": "firebase_crashlytics", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_crashlytics-3.5.7/", "native_build": true, "dependencies": ["firebase_core"]}, {"name": "firebase_messaging", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_messaging-14.7.10/", "native_build": true, "dependencies": ["firebase_core"]}, {"name": "firebase_storage", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_storage-11.6.5/", "native_build": true, "dependencies": ["firebase_core"]}, {"name": "flutter_facebook_auth", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_facebook_auth-6.2.0/", "native_build": true, "dependencies": []}, {"name": "flutter_local_notifications", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications-16.3.3/", "native_build": true, "dependencies": []}, {"name": "flutter_secure_storage", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.4/", "native_build": true, "dependencies": []}, {"name": "geolocator_apple", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13/", "shared_darwin_source": true, "native_build": true, "dependencies": []}, {"name": "google_maps_flutter_ios", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.2/", "native_build": true, "dependencies": []}, {"name": "google_mlkit_commons", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_mlkit_commons-0.6.1/", "native_build": true, "dependencies": []}, {"name": "google_sign_in_ios", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_sign_in_ios-5.9.0/", "shared_darwin_source": true, "native_build": true, "dependencies": []}, {"name": "image_picker_ios", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/", "native_build": true, "dependencies": []}, {"name": "just_audio", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/just_audio-0.9.46/", "shared_darwin_source": true, "native_build": true, "dependencies": ["audio_session"]}, {"name": "local_auth_darwin", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/local_auth_darwin-1.4.3/", "shared_darwin_source": true, "native_build": true, "dependencies": []}, {"name": "path_provider_foundation", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/", "shared_darwin_source": true, "native_build": true, "dependencies": []}, {"name": "permission_handler_apple", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/", "native_build": true, "dependencies": []}, {"name": "record_darwin", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/record_darwin-1.2.2/", "native_build": true, "dependencies": []}, {"name": "sensors_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sensors_plus-4.0.2/", "native_build": true, "dependencies": []}, {"name": "share_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/share_plus-7.2.2/", "native_build": true, "dependencies": []}, {"name": "shared_preferences_foundation", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/", "shared_darwin_source": true, "native_build": true, "dependencies": []}, {"name": "sign_in_with_apple", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sign_in_with_apple-5.0.0/", "native_build": true, "dependencies": []}, {"name": "smart_auth", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/smart_auth-1.1.1/", "native_build": true, "dependencies": []}, {"name": "speech_to_text", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/speech_to_text-6.6.0/", "native_build": true, "dependencies": []}, {"name": "sqflite_darwin", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1+1/", "shared_darwin_source": true, "native_build": true, "dependencies": []}, {"name": "stripe_ios", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/stripe_ios-10.2.0/", "native_build": true, "dependencies": []}, {"name": "stripe_payment", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/stripe_payment-1.1.4/", "native_build": true, "dependencies": []}, {"name": "url_launcher_ios", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.3/", "native_build": true, "dependencies": []}], "android": [{"name": "arcore_flutter_plugin", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/arcore_flutter_plugin-0.1.0/", "native_build": true, "dependencies": []}, {"name": "audio_session", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/audio_session-0.1.25/", "native_build": true, "dependencies": []}, {"name": "camera_android", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/camera_android-0.10.10+3/", "native_build": true, "dependencies": ["flutter_plugin_android_lifecycle"]}, {"name": "cloud_firestore", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cloud_firestore-4.17.5/", "native_build": true, "dependencies": ["firebase_core"]}, {"name": "connectivity_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus-5.0.2/", "native_build": true, "dependencies": []}, {"name": "file_picker", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file_picker-9.2.3/", "native_build": true, "dependencies": ["flutter_plugin_android_lifecycle"]}, {"name": "firebase_analytics", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_analytics-10.10.7/", "native_build": true, "dependencies": ["firebase_core"]}, {"name": "firebase_app_check", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_app_check-0.2.2/", "native_build": true, "dependencies": ["firebase_core"]}, {"name": "firebase_auth", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_auth-4.16.0/", "native_build": true, "dependencies": ["firebase_core"]}, {"name": "firebase_core", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core-2.32.0/", "native_build": true, "dependencies": []}, {"name": "firebase_crashlytics", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_crashlytics-3.5.7/", "native_build": true, "dependencies": ["firebase_core"]}, {"name": "firebase_messaging", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_messaging-14.7.10/", "native_build": true, "dependencies": ["firebase_core"]}, {"name": "firebase_storage", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_storage-11.6.5/", "native_build": true, "dependencies": ["firebase_core"]}, {"name": "flutter_facebook_auth", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_facebook_auth-6.2.0/", "native_build": true, "dependencies": []}, {"name": "flutter_local_notifications", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications-16.3.3/", "native_build": true, "dependencies": []}, {"name": "flutter_plugin_android_lifecycle", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_plugin_android_lifecycle-2.0.28/", "native_build": true, "dependencies": []}, {"name": "flutter_secure_storage", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.4/", "native_build": true, "dependencies": []}, {"name": "geolocator_android", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_android-4.6.2/", "native_build": true, "dependencies": []}, {"name": "google_maps_flutter_android", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_android-2.16.1/", "native_build": true, "dependencies": ["flutter_plugin_android_lifecycle"]}, {"name": "google_mlkit_commons", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_mlkit_commons-0.6.1/", "native_build": true, "dependencies": []}, {"name": "google_sign_in_android", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_sign_in_android-6.2.1/", "native_build": true, "dependencies": []}, {"name": "image_picker_android", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_android-0.8.12+23/", "native_build": true, "dependencies": ["flutter_plugin_android_lifecycle"]}, {"name": "just_audio", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/just_audio-0.9.46/", "native_build": true, "dependencies": ["audio_session"]}, {"name": "local_auth_android", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/local_auth_android-1.0.49/", "native_build": true, "dependencies": ["flutter_plugin_android_lifecycle"]}, {"name": "path_provider_android", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_android-2.2.17/", "native_build": true, "dependencies": []}, {"name": "permission_handler_android", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_android-12.1.0/", "native_build": true, "dependencies": []}, {"name": "record_android", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/record_android-1.3.3/", "native_build": true, "dependencies": []}, {"name": "sensors_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sensors_plus-4.0.2/", "native_build": true, "dependencies": []}, {"name": "share_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/share_plus-7.2.2/", "native_build": true, "dependencies": []}, {"name": "shared_preferences_android", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_android-2.4.10/", "native_build": true, "dependencies": []}, {"name": "sign_in_with_apple", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sign_in_with_apple-5.0.0/", "native_build": true, "dependencies": []}, {"name": "smart_auth", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/smart_auth-1.1.1/", "native_build": true, "dependencies": []}, {"name": "speech_to_text", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/speech_to_text-6.6.0/", "native_build": true, "dependencies": []}, {"name": "sqflite_android", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_android-2.4.0/", "native_build": true, "dependencies": []}, {"name": "stripe_android", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/stripe_android-10.2.1/", "native_build": true, "dependencies": []}, {"name": "stripe_payment", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/stripe_payment-1.1.4/", "native_build": true, "dependencies": []}, {"name": "url_launcher_android", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_android-6.3.16/", "native_build": true, "dependencies": []}], "macos": [{"name": "audio_session", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/audio_session-0.1.25/", "native_build": true, "dependencies": []}, {"name": "cloud_firestore", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cloud_firestore-4.17.5/", "native_build": true, "dependencies": ["firebase_core"]}, {"name": "connectivity_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus-5.0.2/", "native_build": true, "dependencies": []}, {"name": "facebook_auth_desktop", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/facebook_auth_desktop-1.0.3/", "native_build": true, "dependencies": []}, {"name": "file_picker", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file_picker-9.2.3/", "native_build": true, "dependencies": []}, {"name": "file_selector_macos", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_macos-0.9.4+2/", "native_build": true, "dependencies": []}, {"name": "firebase_analytics", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_analytics-10.10.7/", "native_build": true, "dependencies": ["firebase_core"]}, {"name": "firebase_app_check", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_app_check-0.2.2/", "native_build": true, "dependencies": ["firebase_core"]}, {"name": "firebase_auth", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_auth-4.16.0/", "native_build": true, "dependencies": ["firebase_core"]}, {"name": "firebase_core", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core-2.32.0/", "native_build": true, "dependencies": []}, {"name": "firebase_crashlytics", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_crashlytics-3.5.7/", "native_build": true, "dependencies": ["firebase_core"]}, {"name": "firebase_messaging", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_messaging-14.7.10/", "native_build": true, "dependencies": ["firebase_core"]}, {"name": "firebase_storage", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_storage-11.6.5/", "native_build": true, "dependencies": ["firebase_core"]}, {"name": "flutter_local_notifications", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications-16.3.3/", "native_build": true, "dependencies": []}, {"name": "flutter_secure_storage_macos", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_macos-3.1.3/", "native_build": true, "dependencies": []}, {"name": "geolocator_apple", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13/", "shared_darwin_source": true, "native_build": true, "dependencies": []}, {"name": "google_sign_in_ios", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_sign_in_ios-5.9.0/", "shared_darwin_source": true, "native_build": true, "dependencies": []}, {"name": "image_picker_macos", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_macos-0.2.1+2/", "native_build": false, "dependencies": ["file_selector_macos"]}, {"name": "just_audio", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/just_audio-0.9.46/", "shared_darwin_source": true, "native_build": true, "dependencies": ["audio_session"]}, {"name": "local_auth_darwin", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/local_auth_darwin-1.4.3/", "shared_darwin_source": true, "native_build": true, "dependencies": []}, {"name": "path_provider_foundation", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/", "shared_darwin_source": true, "native_build": true, "dependencies": []}, {"name": "record_darwin", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/record_darwin-1.2.2/", "native_build": true, "dependencies": []}, {"name": "share_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/share_plus-7.2.2/", "native_build": true, "dependencies": []}, {"name": "shared_preferences_foundation", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/", "shared_darwin_source": true, "native_build": true, "dependencies": []}, {"name": "sign_in_with_apple", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sign_in_with_apple-5.0.0/", "native_build": true, "dependencies": []}, {"name": "smart_auth", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/smart_auth-1.1.1/", "native_build": true, "dependencies": []}, {"name": "speech_to_text_macos", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/speech_to_text_macos-1.1.0/", "native_build": true, "dependencies": []}, {"name": "sqflite_darwin", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1+1/", "shared_darwin_source": true, "native_build": true, "dependencies": []}, {"name": "url_launcher_macos", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_macos-3.2.2/", "native_build": true, "dependencies": []}], "linux": [{"name": "connectivity_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus-5.0.2/", "native_build": false, "dependencies": []}, {"name": "file_picker", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file_picker-9.2.3/", "native_build": false, "dependencies": []}, {"name": "file_selector_linux", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_linux-0.9.3+2/", "native_build": true, "dependencies": []}, {"name": "flutter_local_notifications_linux", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications_linux-4.0.1/", "native_build": false, "dependencies": []}, {"name": "flutter_secure_storage_linux", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_linux-1.2.3/", "native_build": true, "dependencies": []}, {"name": "image_picker_linux", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_linux-0.2.1+2/", "native_build": false, "dependencies": ["file_selector_linux"]}, {"name": "path_provider_linux", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/", "native_build": false, "dependencies": []}, {"name": "record_linux", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/record_linux-0.7.2/", "native_build": true, "dependencies": []}, {"name": "share_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/share_plus-7.2.2/", "native_build": false, "dependencies": ["url_launcher_linux"]}, {"name": "shared_preferences_linux", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_linux-2.4.1/", "native_build": false, "dependencies": ["path_provider_linux"]}, {"name": "smart_auth", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/smart_auth-1.1.1/", "native_build": true, "dependencies": []}, {"name": "url_launcher_linux", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_linux-3.2.1/", "native_build": true, "dependencies": []}], "windows": [{"name": "cloud_firestore", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cloud_firestore-4.17.5/", "native_build": true, "dependencies": ["firebase_core"]}, {"name": "connectivity_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus-5.0.2/", "native_build": true, "dependencies": []}, {"name": "file_picker", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file_picker-9.2.3/", "native_build": false, "dependencies": []}, {"name": "file_selector_windows", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_windows-0.9.3+4/", "native_build": true, "dependencies": []}, {"name": "firebase_auth", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_auth-4.16.0/", "native_build": true, "dependencies": ["firebase_core"]}, {"name": "firebase_core", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core-2.32.0/", "native_build": true, "dependencies": []}, {"name": "firebase_storage", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_storage-11.6.5/", "native_build": true, "dependencies": ["firebase_core"]}, {"name": "flutter_secure_storage_windows", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_windows-3.1.2/", "native_build": true, "dependencies": []}, {"name": "geolocator_windows", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_windows-0.2.5/", "native_build": true, "dependencies": []}, {"name": "image_picker_windows", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_windows-0.2.1+1/", "native_build": false, "dependencies": ["file_selector_windows"]}, {"name": "local_auth_windows", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/local_auth_windows-1.0.11/", "native_build": true, "dependencies": []}, {"name": "path_provider_windows", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/", "native_build": false, "dependencies": []}, {"name": "permission_handler_windows", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_windows-0.2.1/", "native_build": true, "dependencies": []}, {"name": "record_windows", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/record_windows-1.0.5/", "native_build": true, "dependencies": []}, {"name": "share_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/share_plus-7.2.2/", "native_build": true, "dependencies": ["url_launcher_windows"]}, {"name": "shared_preferences_windows", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_windows-2.4.1/", "native_build": false, "dependencies": ["path_provider_windows"]}, {"name": "smart_auth", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/smart_auth-1.1.1/", "native_build": true, "dependencies": []}, {"name": "url_launcher_windows", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_windows-3.1.4/", "native_build": true, "dependencies": []}], "web": [{"name": "audio_session", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/audio_session-0.1.25/", "dependencies": []}, {"name": "camera_web", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/camera_web-0.3.5/", "dependencies": []}, {"name": "cloud_firestore_web", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cloud_firestore_web-3.12.5/", "dependencies": ["firebase_core_web"]}, {"name": "connectivity_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus-5.0.2/", "dependencies": []}, {"name": "file_picker", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file_picker-9.2.3/", "dependencies": []}, {"name": "firebase_analytics_web", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_analytics_web-0.5.7+7/", "dependencies": ["firebase_core_web"]}, {"name": "firebase_app_check_web", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_app_check_web-0.1.2/", "dependencies": ["firebase_core_web"]}, {"name": "firebase_auth_web", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_auth_web-5.8.13/", "dependencies": ["firebase_core_web"]}, {"name": "firebase_core_web", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core_web-2.22.0/", "dependencies": []}, {"name": "firebase_messaging_web", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_messaging_web-3.5.18/", "dependencies": ["firebase_core_web"]}, {"name": "firebase_storage_web", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_storage_web-3.6.22/", "dependencies": ["firebase_core_web"]}, {"name": "flutter_facebook_auth_web", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_facebook_auth_web-5.0.0/", "dependencies": []}, {"name": "flutter_secure_storage_web", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_web-1.2.1/", "dependencies": []}, {"name": "geolocator_web", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_web-2.2.1/", "dependencies": []}, {"name": "google_maps_flutter_web", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_web-0.5.12/", "dependencies": []}, {"name": "google_sign_in_web", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_sign_in_web-0.12.4+4/", "dependencies": []}, {"name": "image_picker_for_web", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_for_web-3.0.6/", "dependencies": []}, {"name": "just_audio_web", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/just_audio_web-0.4.16/", "dependencies": []}, {"name": "permission_handler_html", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_html-0.1.3+5/", "dependencies": []}, {"name": "record_web", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/record_web-1.1.6/", "dependencies": []}, {"name": "sensors_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sensors_plus-4.0.2/", "dependencies": []}, {"name": "share_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/share_plus-7.2.2/", "dependencies": ["url_launcher_web"]}, {"name": "shared_preferences_web", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_web-2.4.3/", "dependencies": []}, {"name": "sign_in_with_apple_web", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sign_in_with_apple_web-1.0.1/", "dependencies": []}, {"name": "smart_auth", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/smart_auth-1.1.1/", "dependencies": []}, {"name": "speech_to_text", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/speech_to_text-6.6.0/", "dependencies": []}, {"name": "url_launcher_web", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_web-2.4.1/", "dependencies": []}]}, "dependencyGraph": [{"name": "arcore_flutter_plugin", "dependencies": []}, {"name": "audio_session", "dependencies": []}, {"name": "camera", "dependencies": ["camera_android", "camera_avfoundation", "camera_web", "flutter_plugin_android_lifecycle"]}, {"name": "camera_android", "dependencies": ["flutter_plugin_android_lifecycle"]}, {"name": "camera_avfoundation", "dependencies": []}, {"name": "camera_web", "dependencies": []}, {"name": "cloud_firestore", "dependencies": ["cloud_firestore_web", "firebase_core"]}, {"name": "cloud_firestore_web", "dependencies": ["firebase_core", "firebase_core_web"]}, {"name": "connectivity_plus", "dependencies": []}, {"name": "facebook_auth_desktop", "dependencies": ["flutter_secure_storage"]}, {"name": "file_picker", "dependencies": ["flutter_plugin_android_lifecycle"]}, {"name": "file_selector_linux", "dependencies": []}, {"name": "file_selector_macos", "dependencies": []}, {"name": "file_selector_windows", "dependencies": []}, {"name": "firebase_analytics", "dependencies": ["firebase_analytics_web", "firebase_core"]}, {"name": "firebase_analytics_web", "dependencies": ["firebase_core", "firebase_core_web"]}, {"name": "firebase_app_check", "dependencies": ["firebase_app_check_web", "firebase_core"]}, {"name": "firebase_app_check_web", "dependencies": ["firebase_core", "firebase_core_web"]}, {"name": "firebase_auth", "dependencies": ["firebase_auth_web", "firebase_core"]}, {"name": "firebase_auth_web", "dependencies": ["firebase_core", "firebase_core_web"]}, {"name": "firebase_core", "dependencies": ["firebase_core_web"]}, {"name": "firebase_core_web", "dependencies": []}, {"name": "firebase_crashlytics", "dependencies": ["firebase_core"]}, {"name": "firebase_messaging", "dependencies": ["firebase_core", "firebase_messaging_web"]}, {"name": "firebase_messaging_web", "dependencies": ["firebase_core", "firebase_core_web"]}, {"name": "firebase_storage", "dependencies": ["firebase_core", "firebase_storage_web"]}, {"name": "firebase_storage_web", "dependencies": ["firebase_core", "firebase_core_web"]}, {"name": "flutter_facebook_auth", "dependencies": ["flutter_facebook_auth_web", "facebook_auth_desktop"]}, {"name": "flutter_facebook_auth_web", "dependencies": []}, {"name": "flutter_local_notifications", "dependencies": ["flutter_local_notifications_linux"]}, {"name": "flutter_local_notifications_linux", "dependencies": []}, {"name": "flutter_plugin_android_lifecycle", "dependencies": []}, {"name": "flutter_secure_storage", "dependencies": ["flutter_secure_storage_linux", "flutter_secure_storage_macos", "flutter_secure_storage_web", "flutter_secure_storage_windows"]}, {"name": "flutter_secure_storage_linux", "dependencies": []}, {"name": "flutter_secure_storage_macos", "dependencies": []}, {"name": "flutter_secure_storage_web", "dependencies": []}, {"name": "flutter_secure_storage_windows", "dependencies": ["path_provider"]}, {"name": "flutter_stripe", "dependencies": ["stripe_android", "stripe_ios"]}, {"name": "geolocator", "dependencies": ["geolocator_android", "geolocator_apple", "geolocator_web", "geolocator_windows"]}, {"name": "geolocator_android", "dependencies": []}, {"name": "geolocator_apple", "dependencies": []}, {"name": "geolocator_web", "dependencies": []}, {"name": "geolocator_windows", "dependencies": []}, {"name": "google_maps_flutter", "dependencies": ["google_maps_flutter_android", "google_maps_flutter_ios", "google_maps_flutter_web"]}, {"name": "google_maps_flutter_android", "dependencies": ["flutter_plugin_android_lifecycle"]}, {"name": "google_maps_flutter_ios", "dependencies": []}, {"name": "google_maps_flutter_web", "dependencies": []}, {"name": "google_mlkit_commons", "dependencies": []}, {"name": "google_sign_in", "dependencies": ["google_sign_in_android", "google_sign_in_ios", "google_sign_in_web"]}, {"name": "google_sign_in_android", "dependencies": []}, {"name": "google_sign_in_ios", "dependencies": []}, {"name": "google_sign_in_web", "dependencies": []}, {"name": "image_picker", "dependencies": ["image_picker_android", "image_picker_for_web", "image_picker_ios", "image_picker_linux", "image_picker_macos", "image_picker_windows"]}, {"name": "image_picker_android", "dependencies": ["flutter_plugin_android_lifecycle"]}, {"name": "image_picker_for_web", "dependencies": []}, {"name": "image_picker_ios", "dependencies": []}, {"name": "image_picker_linux", "dependencies": ["file_selector_linux"]}, {"name": "image_picker_macos", "dependencies": ["file_selector_macos"]}, {"name": "image_picker_windows", "dependencies": ["file_selector_windows"]}, {"name": "just_audio", "dependencies": ["just_audio_web", "audio_session", "path_provider"]}, {"name": "just_audio_web", "dependencies": []}, {"name": "local_auth", "dependencies": ["local_auth_android", "local_auth_darwin", "local_auth_windows"]}, {"name": "local_auth_android", "dependencies": ["flutter_plugin_android_lifecycle"]}, {"name": "local_auth_darwin", "dependencies": []}, {"name": "local_auth_windows", "dependencies": []}, {"name": "path_provider", "dependencies": ["path_provider_android", "path_provider_foundation", "path_provider_linux", "path_provider_windows"]}, {"name": "path_provider_android", "dependencies": []}, {"name": "path_provider_foundation", "dependencies": []}, {"name": "path_provider_linux", "dependencies": []}, {"name": "path_provider_windows", "dependencies": []}, {"name": "permission_handler", "dependencies": ["permission_handler_android", "permission_handler_apple", "permission_handler_html", "permission_handler_windows"]}, {"name": "permission_handler_android", "dependencies": []}, {"name": "permission_handler_apple", "dependencies": []}, {"name": "permission_handler_html", "dependencies": []}, {"name": "permission_handler_windows", "dependencies": []}, {"name": "record", "dependencies": ["record_web", "record_windows", "record_linux", "record_android", "record_darwin"]}, {"name": "record_android", "dependencies": []}, {"name": "record_darwin", "dependencies": []}, {"name": "record_linux", "dependencies": []}, {"name": "record_web", "dependencies": []}, {"name": "record_windows", "dependencies": []}, {"name": "sensors_plus", "dependencies": []}, {"name": "share_plus", "dependencies": ["url_launcher_web", "url_launcher_windows", "url_launcher_linux"]}, {"name": "shared_preferences", "dependencies": ["shared_preferences_android", "shared_preferences_foundation", "shared_preferences_linux", "shared_preferences_web", "shared_preferences_windows"]}, {"name": "shared_preferences_android", "dependencies": []}, {"name": "shared_preferences_foundation", "dependencies": []}, {"name": "shared_preferences_linux", "dependencies": ["path_provider_linux"]}, {"name": "shared_preferences_web", "dependencies": []}, {"name": "shared_preferences_windows", "dependencies": ["path_provider_windows"]}, {"name": "sign_in_with_apple", "dependencies": ["sign_in_with_apple_web"]}, {"name": "sign_in_with_apple_web", "dependencies": []}, {"name": "smart_auth", "dependencies": []}, {"name": "speech_to_text", "dependencies": ["speech_to_text_macos"]}, {"name": "speech_to_text_macos", "dependencies": []}, {"name": "sqflite", "dependencies": ["sqflite_android", "sqflite_darwin"]}, {"name": "sqflite_android", "dependencies": []}, {"name": "sqflite_darwin", "dependencies": []}, {"name": "stripe_android", "dependencies": []}, {"name": "stripe_ios", "dependencies": []}, {"name": "stripe_payment", "dependencies": []}, {"name": "url_launcher", "dependencies": ["url_launcher_android", "url_launcher_ios", "url_launcher_linux", "url_launcher_macos", "url_launcher_web", "url_launcher_windows"]}, {"name": "url_launcher_android", "dependencies": []}, {"name": "url_launcher_ios", "dependencies": []}, {"name": "url_launcher_linux", "dependencies": []}, {"name": "url_launcher_macos", "dependencies": []}, {"name": "url_launcher_web", "dependencies": []}, {"name": "url_launcher_windows", "dependencies": []}], "date_created": "2025-05-19 03:40:13.093729", "version": "3.27.2", "swift_package_manager_enabled": false}