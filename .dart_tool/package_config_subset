_fe_analyzer_shared
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/_fe_analyzer_shared-76.0.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/_fe_analyzer_shared-76.0.0/lib/
_flutterfire_internals
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/_flutterfire_internals-1.3.35/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/_flutterfire_internals-1.3.35/lib/
analyzer
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/analyzer-6.11.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/analyzer-6.11.0/lib/
archive
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/archive-3.6.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/archive-3.6.1/lib/
arcore_flutter_plugin
2.14
file:///Users/<USER>/.pub-cache/hosted/pub.dev/arcore_flutter_plugin-0.1.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/arcore_flutter_plugin-0.1.0/lib/
args
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/args-2.7.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/args-2.7.0/lib/
async
2.18
file:///Users/<USER>/.pub-cache/hosted/pub.dev/async-2.11.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/async-2.11.0/lib/
audio_session
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/audio_session-0.1.25/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/audio_session-0.1.25/lib/
auto_route
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/auto_route-7.9.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/auto_route-7.9.2/lib/
auto_route_generator
2.17
file:///Users/<USER>/.pub-cache/hosted/pub.dev/auto_route_generator-7.3.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/auto_route_generator-7.3.2/lib/
barcode
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/barcode-2.2.9/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/barcode-2.2.9/lib/
bidi
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/bidi-2.0.13/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/bidi-2.0.13/lib/
boolean_selector
2.17
file:///Users/<USER>/.pub-cache/hosted/pub.dev/boolean_selector-2.1.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/boolean_selector-2.1.1/lib/
build
3.6
file:///Users/<USER>/.pub-cache/hosted/pub.dev/build-2.4.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/build-2.4.2/lib/
build_config
3.6
file:///Users/<USER>/.pub-cache/hosted/pub.dev/build_config-1.1.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/build_config-1.1.2/lib/
build_daemon
3.6
file:///Users/<USER>/.pub-cache/hosted/pub.dev/build_daemon-4.0.4/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/build_daemon-4.0.4/lib/
build_resolvers
3.6
file:///Users/<USER>/.pub-cache/hosted/pub.dev/build_resolvers-2.4.4/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/build_resolvers-2.4.4/lib/
build_runner
3.6
file:///Users/<USER>/.pub-cache/hosted/pub.dev/build_runner-2.4.15/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/build_runner-2.4.15/lib/
build_runner_core
3.6
file:///Users/<USER>/.pub-cache/hosted/pub.dev/build_runner_core-8.0.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/build_runner_core-8.0.0/lib/
built_collection
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/built_collection-5.1.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/built_collection-5.1.1/lib/
built_value
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/built_value-8.9.5/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/built_value-8.9.5/lib/
cached_network_image
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/cached_network_image-3.4.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/cached_network_image-3.4.1/lib/
cached_network_image_platform_interface
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/cached_network_image_platform_interface-4.1.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/cached_network_image_platform_interface-4.1.1/lib/
cached_network_image_web
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/cached_network_image_web-1.3.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/cached_network_image_web-1.3.1/lib/
camera
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/camera-0.10.6/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/camera-0.10.6/lib/
camera_android
3.6
file:///Users/<USER>/.pub-cache/hosted/pub.dev/camera_android-0.10.10+3/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/camera_android-0.10.10+3/lib/
camera_avfoundation
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.19/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.19/lib/
camera_platform_interface
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/camera_platform_interface-2.10.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/camera_platform_interface-2.10.0/lib/
camera_web
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/camera_web-0.3.5/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/camera_web-0.3.5/lib/
characters
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/characters-1.3.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/characters-1.3.0/lib/
checked_yaml
2.19
file:///Users/<USER>/.pub-cache/hosted/pub.dev/checked_yaml-2.0.3/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/checked_yaml-2.0.3/lib/
clock
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/clock-1.1.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/clock-1.1.1/lib/
cloud_firestore
2.18
file:///Users/<USER>/.pub-cache/hosted/pub.dev/cloud_firestore-4.17.5/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/cloud_firestore-4.17.5/lib/
cloud_firestore_platform_interface
2.18
file:///Users/<USER>/.pub-cache/hosted/pub.dev/cloud_firestore_platform_interface-6.2.5/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/cloud_firestore_platform_interface-6.2.5/lib/
cloud_firestore_web
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/cloud_firestore_web-3.12.5/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/cloud_firestore_web-3.12.5/lib/
code_builder
3.5
file:///Users/<USER>/.pub-cache/hosted/pub.dev/code_builder-4.10.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/code_builder-4.10.1/lib/
collection
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.0/lib/
connectivity_plus
2.18
file:///Users/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus-5.0.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus-5.0.2/lib/
connectivity_plus_platform_interface
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus_platform_interface-1.2.4/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus_platform_interface-1.2.4/lib/
convert
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/convert-3.1.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/convert-3.1.2/lib/
cross_file
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/cross_file-0.3.4+2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/cross_file-0.3.4+2/lib/
crypto
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/lib/
csslib
3.1
file:///Users/<USER>/.pub-cache/hosted/pub.dev/csslib-1.0.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/csslib-1.0.2/lib/
cupertino_icons
3.1
file:///Users/<USER>/.pub-cache/hosted/pub.dev/cupertino_icons-1.0.8/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/cupertino_icons-1.0.8/lib/
dart_style
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/dart_style-2.3.8/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/dart_style-2.3.8/lib/
dbus
2.17
file:///Users/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.11/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.11/lib/
dio
2.18
file:///Users/<USER>/.pub-cache/hosted/pub.dev/dio-5.8.0+1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/dio-5.8.0+1/lib/
dio_web_adapter
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/dio_web_adapter-2.1.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/dio_web_adapter-2.1.1/lib/
equatable
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/equatable-2.0.7/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/equatable-2.0.7/lib/
facebook_auth_desktop
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/facebook_auth_desktop-1.0.3/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/facebook_auth_desktop-1.0.3/lib/
fake_async
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/fake_async-1.3.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/fake_async-1.3.1/lib/
ffi
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/ffi-2.1.3/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/ffi-2.1.3/lib/
file
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/
file_picker
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/file_picker-9.2.3/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/file_picker-9.2.3/lib/
file_selector_linux
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_linux-0.9.3+2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_linux-0.9.3+2/lib/
file_selector_macos
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_macos-0.9.4+2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_macos-0.9.4+2/lib/
file_selector_platform_interface
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_platform_interface-2.6.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_platform_interface-2.6.2/lib/
file_selector_windows
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_windows-0.9.3+4/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_windows-0.9.3+4/lib/
firebase_analytics
2.18
file:///Users/<USER>/.pub-cache/hosted/pub.dev/firebase_analytics-10.10.7/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/firebase_analytics-10.10.7/lib/
firebase_analytics_platform_interface
2.18
file:///Users/<USER>/.pub-cache/hosted/pub.dev/firebase_analytics_platform_interface-3.10.8/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/firebase_analytics_platform_interface-3.10.8/lib/
firebase_analytics_web
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/firebase_analytics_web-0.5.7+7/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/firebase_analytics_web-0.5.7+7/lib/
firebase_app_check
2.18
file:///Users/<USER>/.pub-cache/hosted/pub.dev/firebase_app_check-0.2.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/firebase_app_check-0.2.2/lib/
firebase_app_check_platform_interface
2.18
file:///Users/<USER>/.pub-cache/hosted/pub.dev/firebase_app_check_platform_interface-0.1.0+29/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/firebase_app_check_platform_interface-0.1.0+29/lib/
firebase_app_check_web
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/firebase_app_check_web-0.1.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/firebase_app_check_web-0.1.2/lib/
firebase_auth
2.18
file:///Users/<USER>/.pub-cache/hosted/pub.dev/firebase_auth-4.16.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/firebase_auth-4.16.0/lib/
firebase_auth_platform_interface
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/firebase_auth_platform_interface-7.3.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/firebase_auth_platform_interface-7.3.0/lib/
firebase_auth_web
2.18
file:///Users/<USER>/.pub-cache/hosted/pub.dev/firebase_auth_web-5.8.13/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/firebase_auth_web-5.8.13/lib/
firebase_core
2.18
file:///Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core-2.32.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core-2.32.0/lib/
firebase_core_platform_interface
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core_platform_interface-5.4.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core_platform_interface-5.4.0/lib/
firebase_core_web
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core_web-2.22.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core_web-2.22.0/lib/
firebase_crashlytics
2.18
file:///Users/<USER>/.pub-cache/hosted/pub.dev/firebase_crashlytics-3.5.7/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/firebase_crashlytics-3.5.7/lib/
firebase_crashlytics_platform_interface
2.18
file:///Users/<USER>/.pub-cache/hosted/pub.dev/firebase_crashlytics_platform_interface-3.6.35/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/firebase_crashlytics_platform_interface-3.6.35/lib/
firebase_messaging
2.18
file:///Users/<USER>/.pub-cache/hosted/pub.dev/firebase_messaging-14.7.10/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/firebase_messaging-14.7.10/lib/
firebase_messaging_platform_interface
2.18
file:///Users/<USER>/.pub-cache/hosted/pub.dev/firebase_messaging_platform_interface-4.5.37/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/firebase_messaging_platform_interface-4.5.37/lib/
firebase_messaging_web
2.18
file:///Users/<USER>/.pub-cache/hosted/pub.dev/firebase_messaging_web-3.5.18/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/firebase_messaging_web-3.5.18/lib/
firebase_storage
2.18
file:///Users/<USER>/.pub-cache/hosted/pub.dev/firebase_storage-11.6.5/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/firebase_storage-11.6.5/lib/
firebase_storage_platform_interface
2.18
file:///Users/<USER>/.pub-cache/hosted/pub.dev/firebase_storage_platform_interface-5.1.22/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/firebase_storage_platform_interface-5.1.22/lib/
firebase_storage_web
2.18
file:///Users/<USER>/.pub-cache/hosted/pub.dev/firebase_storage_web-3.6.22/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/firebase_storage_web-3.6.22/lib/
fixnum
3.1
file:///Users/<USER>/.pub-cache/hosted/pub.dev/fixnum-1.1.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/fixnum-1.1.1/lib/
fl_chart
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/fl_chart-0.65.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/fl_chart-0.65.0/lib/
flutter_animate
2.17
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_animate-4.5.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_animate-4.5.2/lib/
flutter_cache_manager
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/
flutter_dotenv
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_dotenv-5.2.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_dotenv-5.2.1/lib/
flutter_facebook_auth
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_facebook_auth-6.2.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_facebook_auth-6.2.0/lib/
flutter_facebook_auth_platform_interface
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_facebook_auth_platform_interface-5.0.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_facebook_auth_platform_interface-5.0.0/lib/
flutter_facebook_auth_web
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_facebook_auth_web-5.0.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_facebook_auth_web-5.0.0/lib/
flutter_lints
3.1
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_lints-3.0.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_lints-3.0.2/lib/
flutter_local_notifications
2.17
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications-16.3.3/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications-16.3.3/lib/
flutter_local_notifications_linux
2.17
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications_linux-4.0.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications_linux-4.0.1/lib/
flutter_local_notifications_platform_interface
2.17
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications_platform_interface-7.2.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications_platform_interface-7.2.0/lib/
flutter_plugin_android_lifecycle
3.6
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_plugin_android_lifecycle-2.0.28/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_plugin_android_lifecycle-2.0.28/lib/
flutter_riverpod
2.17
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_riverpod-2.6.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_riverpod-2.6.1/lib/
flutter_screenutil
2.17
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_screenutil-5.9.3/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_screenutil-5.9.3/lib/
flutter_secure_storage
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.4/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.4/lib/
flutter_secure_storage_linux
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_linux-1.2.3/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_linux-1.2.3/lib/
flutter_secure_storage_macos
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_macos-3.1.3/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_macos-3.1.3/lib/
flutter_secure_storage_platform_interface
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_platform_interface-1.1.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_platform_interface-1.1.2/lib/
flutter_secure_storage_web
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_web-1.2.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_web-1.2.1/lib/
flutter_secure_storage_windows
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_windows-3.1.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_windows-3.1.2/lib/
flutter_shaders
2.19
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_shaders-0.1.3/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_shaders-0.1.3/lib/
flutter_stripe
2.17
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_stripe-10.2.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_stripe-10.2.0/lib/
flutter_svg
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_svg-2.1.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_svg-2.1.0/lib/
font_awesome_flutter
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/font_awesome_flutter-10.8.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/font_awesome_flutter-10.8.0/lib/
freezed_annotation
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/freezed_annotation-2.4.4/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/freezed_annotation-2.4.4/lib/
frontend_server_client
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/frontend_server_client-4.0.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/frontend_server_client-4.0.0/lib/
geolocator
2.15
file:///Users/<USER>/.pub-cache/hosted/pub.dev/geolocator-10.1.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/geolocator-10.1.1/lib/
geolocator_android
3.5
file:///Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_android-4.6.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_android-4.6.2/lib/
geolocator_apple
3.5
file:///Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13/lib/
geolocator_platform_interface
3.5
file:///Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_platform_interface-4.2.6/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_platform_interface-4.2.6/lib/
geolocator_web
2.15
file:///Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_web-2.2.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_web-2.2.1/lib/
geolocator_windows
3.5
file:///Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_windows-0.2.5/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_windows-0.2.5/lib/
get_it
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/get_it-7.7.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/get_it-7.7.0/lib/
glob
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/glob-2.1.3/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/glob-2.1.3/lib/
go_router
2.18
file:///Users/<USER>/.pub-cache/hosted/pub.dev/go_router-6.5.9/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/go_router-6.5.9/lib/
google_fonts
2.14
file:///Users/<USER>/.pub-cache/hosted/pub.dev/google_fonts-6.2.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/google_fonts-6.2.1/lib/
google_identity_services_web
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/google_identity_services_web-0.3.3+1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/google_identity_services_web-0.3.3+1/lib/
google_maps
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/google_maps-8.1.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/google_maps-8.1.1/lib/
google_maps_cluster_manager
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/google_maps_cluster_manager-3.1.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/google_maps_cluster_manager-3.1.0/lib/
google_maps_flutter
3.6
file:///Users/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter-2.12.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter-2.12.1/lib/
google_maps_flutter_android
3.6
file:///Users/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_android-2.16.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_android-2.16.1/lib/
google_maps_flutter_ios
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.2/lib/
google_maps_flutter_platform_interface
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_platform_interface-2.11.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_platform_interface-2.11.1/lib/
google_maps_flutter_web
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_web-0.5.12/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_web-0.5.12/lib/
google_mlkit_commons
2.16
file:///Users/<USER>/.pub-cache/hosted/pub.dev/google_mlkit_commons-0.6.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/google_mlkit_commons-0.6.1/lib/
google_sign_in
3.6
file:///Users/<USER>/.pub-cache/hosted/pub.dev/google_sign_in-6.3.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/google_sign_in-6.3.0/lib/
google_sign_in_android
3.6
file:///Users/<USER>/.pub-cache/hosted/pub.dev/google_sign_in_android-6.2.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/google_sign_in_android-6.2.1/lib/
google_sign_in_ios
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/google_sign_in_ios-5.9.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/google_sign_in_ios-5.9.0/lib/
google_sign_in_platform_interface
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/google_sign_in_platform_interface-2.5.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/google_sign_in_platform_interface-2.5.0/lib/
google_sign_in_web
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/google_sign_in_web-0.12.4+4/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/google_sign_in_web-0.12.4+4/lib/
graphs
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/graphs-2.3.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/graphs-2.3.2/lib/
hive
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/hive-2.2.3/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/hive-2.2.3/lib/
hive_flutter
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/hive_flutter-1.1.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/hive_flutter-1.1.0/lib/
hive_generator
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/hive_generator-2.0.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/hive_generator-2.0.1/lib/
html
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/html-0.15.6/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/html-0.15.6/lib/
http
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/
http_multi_server
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/http_multi_server-3.2.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/http_multi_server-3.2.2/lib/
http_parser
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.1.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.1.2/lib/
image
2.15
file:///Users/<USER>/.pub-cache/hosted/pub.dev/image-4.3.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/image-4.3.0/lib/
image_picker
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker-1.1.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker-1.1.2/lib/
image_picker_android
3.6
file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_android-0.8.12+23/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_android-0.8.12+23/lib/
image_picker_for_web
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_for_web-3.0.6/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_for_web-3.0.6/lib/
image_picker_ios
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/lib/
image_picker_linux
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_linux-0.2.1+2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_linux-0.2.1+2/lib/
image_picker_macos
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_macos-0.2.1+2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_macos-0.2.1+2/lib/
image_picker_platform_interface
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_platform_interface-2.10.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_platform_interface-2.10.1/lib/
image_picker_windows
2.19
file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_windows-0.2.1+1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_windows-0.2.1+1/lib/
injectable
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/injectable-2.5.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/injectable-2.5.0/lib/
injectable_generator
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/injectable_generator-2.6.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/injectable_generator-2.6.2/lib/
intl
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.18.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.18.1/lib/
io
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/io-1.0.5/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/io-1.0.5/lib/
js
2.19
file:///Users/<USER>/.pub-cache/hosted/pub.dev/js-0.6.7/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/js-0.6.7/lib/
json_annotation
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/json_annotation-4.9.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/json_annotation-4.9.0/lib/
just_audio
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/just_audio-0.9.46/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/just_audio-0.9.46/lib/
just_audio_platform_interface
2.17
file:///Users/<USER>/.pub-cache/hosted/pub.dev/just_audio_platform_interface-4.5.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/just_audio_platform_interface-4.5.0/lib/
just_audio_web
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/just_audio_web-0.4.16/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/just_audio_web-0.4.16/lib/
leak_tracker
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker-10.0.7/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker-10.0.7/lib/
leak_tracker_flutter_testing
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker_flutter_testing-3.0.8/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker_flutter_testing-3.0.8/lib/
leak_tracker_testing
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker_testing-3.0.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker_testing-3.0.1/lib/
lints
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/lints-3.0.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/lints-3.0.0/lib/
local_auth
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/local_auth-2.3.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/local_auth-2.3.0/lib/
local_auth_android
3.6
file:///Users/<USER>/.pub-cache/hosted/pub.dev/local_auth_android-1.0.49/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/local_auth_android-1.0.49/lib/
local_auth_darwin
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/local_auth_darwin-1.4.3/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/local_auth_darwin-1.4.3/lib/
local_auth_platform_interface
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/local_auth_platform_interface-1.0.10/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/local_auth_platform_interface-1.0.10/lib/
local_auth_windows
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/local_auth_windows-1.0.11/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/local_auth_windows-1.0.11/lib/
logging
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/logging-1.3.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/logging-1.3.0/lib/
lottie
3.1
file:///Users/<USER>/.pub-cache/hosted/pub.dev/lottie-2.7.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/lottie-2.7.0/lib/
macros
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/macros-0.1.3-main.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/macros-0.1.3-main.0/lib/
matcher
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/matcher-0.12.16+1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/matcher-0.12.16+1/lib/
material_color_utilities
2.17
file:///Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/
meta
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/meta-1.15.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/meta-1.15.0/lib/
mime
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/mime-1.0.6/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/mime-1.0.6/lib/
nested
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/nested-1.0.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/nested-1.0.0/lib/
nm
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/nm-0.5.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/nm-0.5.0/lib/
octo_image
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/octo_image-2.1.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/octo_image-2.1.0/lib/
package_config
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/package_config-2.2.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/package_config-2.2.0/lib/
path
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.0/lib/
path_parsing
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_parsing-1.1.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_parsing-1.1.0/lib/
path_provider
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider-2.1.5/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider-2.1.5/lib/
path_provider_android
3.6
file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_android-2.2.17/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_android-2.2.17/lib/
path_provider_foundation
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/lib/
path_provider_linux
2.19
file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/lib/
path_provider_platform_interface
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_platform_interface-2.1.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_platform_interface-2.1.2/lib/
path_provider_windows
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/lib/
pdf
2.19
file:///Users/<USER>/.pub-cache/hosted/pub.dev/pdf-3.11.3/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/pdf-3.11.3/lib/
pedantic
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/pedantic-1.11.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/pedantic-1.11.1/lib/
permission_handler
3.5
file:///Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler-11.4.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler-11.4.0/lib/
permission_handler_android
3.5
file:///Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_android-12.1.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_android-12.1.0/lib/
permission_handler_apple
2.18
file:///Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/lib/
permission_handler_html
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_html-0.1.3+5/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_html-0.1.3+5/lib/
permission_handler_platform_interface
3.5
file:///Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_platform_interface-4.3.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_platform_interface-4.3.0/lib/
permission_handler_windows
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_windows-0.2.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_windows-0.2.1/lib/
petitparser
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/
photo_view
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/photo_view-0.14.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/photo_view-0.14.0/lib/
pinput
2.15
file:///Users/<USER>/.pub-cache/hosted/pub.dev/pinput-3.0.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/pinput-3.0.1/lib/
platform
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/platform-3.1.6/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/platform-3.1.6/lib/
plugin_platform_interface
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/plugin_platform_interface-2.1.8/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/plugin_platform_interface-2.1.8/lib/
pool
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/pool-1.5.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/pool-1.5.1/lib/
protobuf
2.19
file:///Users/<USER>/.pub-cache/hosted/pub.dev/protobuf-3.1.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/protobuf-3.1.0/lib/
provider
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/provider-6.1.5/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/provider-6.1.5/lib/
pub_semver
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/pub_semver-2.2.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/pub_semver-2.2.0/lib/
pubspec_parse
3.6
file:///Users/<USER>/.pub-cache/hosted/pub.dev/pubspec_parse-1.5.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/pubspec_parse-1.5.0/lib/
qr
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/qr-3.0.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/qr-3.0.2/lib/
recase
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/recase-4.1.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/recase-4.1.0/lib/
record
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/record-5.2.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/record-5.2.1/lib/
record_android
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/record_android-1.3.3/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/record_android-1.3.3/lib/
record_darwin
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/record_darwin-1.2.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/record_darwin-1.2.2/lib/
record_linux
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/record_linux-0.7.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/record_linux-0.7.2/lib/
record_platform_interface
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/record_platform_interface-1.2.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/record_platform_interface-1.2.0/lib/
record_web
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/record_web-1.1.6/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/record_web-1.1.6/lib/
record_windows
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/record_windows-1.0.5/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/record_windows-1.0.5/lib/
retrofit
2.19
file:///Users/<USER>/.pub-cache/hosted/pub.dev/retrofit-4.4.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/retrofit-4.4.2/lib/
retrofit_generator
2.19
file:///Users/<USER>/.pub-cache/hosted/pub.dev/retrofit_generator-8.2.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/retrofit_generator-8.2.1/lib/
riverpod
2.17
file:///Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/
rxdart
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/
sanitize_html
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/sanitize_html-2.1.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/sanitize_html-2.1.0/lib/
sensors_plus
2.18
file:///Users/<USER>/.pub-cache/hosted/pub.dev/sensors_plus-4.0.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/sensors_plus-4.0.2/lib/
sensors_plus_platform_interface
2.18
file:///Users/<USER>/.pub-cache/hosted/pub.dev/sensors_plus_platform_interface-1.2.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/sensors_plus_platform_interface-1.2.0/lib/
share_plus
2.18
file:///Users/<USER>/.pub-cache/hosted/pub.dev/share_plus-7.2.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/share_plus-7.2.2/lib/
share_plus_platform_interface
2.18
file:///Users/<USER>/.pub-cache/hosted/pub.dev/share_plus_platform_interface-3.4.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/share_plus_platform_interface-3.4.0/lib/
shared_preferences
3.5
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences-2.5.3/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences-2.5.3/lib/
shared_preferences_android
3.6
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_android-2.4.10/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_android-2.4.10/lib/
shared_preferences_foundation
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/lib/
shared_preferences_linux
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_linux-2.4.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_linux-2.4.1/lib/
shared_preferences_platform_interface
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_platform_interface-2.4.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_platform_interface-2.4.1/lib/
shared_preferences_web
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_web-2.4.3/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_web-2.4.3/lib/
shared_preferences_windows
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_windows-2.4.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_windows-2.4.1/lib/
shelf
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shelf-1.4.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shelf-1.4.2/lib/
shelf_web_socket
3.5
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shelf_web_socket-3.0.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shelf_web_socket-3.0.0/lib/
shimmer
2.17
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shimmer-3.0.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shimmer-3.0.0/lib/
sign_in_with_apple
2.18
file:///Users/<USER>/.pub-cache/hosted/pub.dev/sign_in_with_apple-5.0.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/sign_in_with_apple-5.0.0/lib/
sign_in_with_apple_platform_interface
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/sign_in_with_apple_platform_interface-1.1.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/sign_in_with_apple_platform_interface-1.1.0/lib/
sign_in_with_apple_web
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/sign_in_with_apple_web-1.0.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/sign_in_with_apple_web-1.0.1/lib/
simple_gesture_detector
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/simple_gesture_detector-0.2.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/simple_gesture_detector-0.2.1/lib/
smart_auth
2.16
file:///Users/<USER>/.pub-cache/hosted/pub.dev/smart_auth-1.1.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/smart_auth-1.1.1/lib/
smooth_page_indicator
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/smooth_page_indicator-1.2.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/smooth_page_indicator-1.2.1/lib/
source_gen
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/source_gen-1.5.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/source_gen-1.5.0/lib/
source_helper
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/source_helper-1.3.5/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/source_helper-1.3.5/lib/
source_span
2.18
file:///Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.0/lib/
speech_to_text
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/speech_to_text-6.6.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/speech_to_text-6.6.0/lib/
speech_to_text_macos
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/speech_to_text_macos-1.1.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/speech_to_text_macos-1.1.0/lib/
speech_to_text_platform_interface
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/speech_to_text_platform_interface-2.3.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/speech_to_text_platform_interface-2.3.0/lib/
sprintf
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/sprintf-7.0.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/sprintf-7.0.0/lib/
sqflite
3.5
file:///Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.4.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.4.1/lib/
sqflite_android
3.5
file:///Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_android-2.4.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_android-2.4.0/lib/
sqflite_common
3.5
file:///Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.4+6/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.4+6/lib/
sqflite_darwin
3.5
file:///Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1+1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1+1/lib/
sqflite_platform_interface
3.5
file:///Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_platform_interface-2.4.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_platform_interface-2.4.0/lib/
stack_trace
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/stack_trace-1.12.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/stack_trace-1.12.0/lib/
state_notifier
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/state_notifier-1.0.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/state_notifier-1.0.0/lib/
stream_channel
2.19
file:///Users/<USER>/.pub-cache/hosted/pub.dev/stream_channel-2.1.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/stream_channel-2.1.2/lib/
stream_transform
3.1
file:///Users/<USER>/.pub-cache/hosted/pub.dev/stream_transform-2.1.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/stream_transform-2.1.1/lib/
string_scanner
3.1
file:///Users/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.3.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.3.0/lib/
stripe_android
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/stripe_android-10.2.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/stripe_android-10.2.1/lib/
stripe_ios
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/stripe_ios-10.2.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/stripe_ios-10.2.0/lib/
stripe_payment
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/stripe_payment-1.1.4/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/stripe_payment-1.1.4/lib/
stripe_platform_interface
2.17
file:///Users/<USER>/.pub-cache/hosted/pub.dev/stripe_platform_interface-10.2.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/stripe_platform_interface-10.2.0/lib/
syncfusion_flutter_charts
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-28.2.12/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-28.2.12/lib/
syncfusion_flutter_core
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_core-28.2.12/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_core-28.2.12/lib/
synchronized
3.5
file:///Users/<USER>/.pub-cache/hosted/pub.dev/synchronized-3.3.0+3/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/synchronized-3.3.0+3/lib/
table_calendar
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/table_calendar-3.0.9/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/table_calendar-3.0.9/lib/
term_glyph
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/term_glyph-1.2.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/term_glyph-1.2.1/lib/
test_api
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/test_api-0.7.3/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/test_api-0.7.3/lib/
timeline_tile
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/timeline_tile-2.0.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/timeline_tile-2.0.0/lib/
timezone
2.19
file:///Users/<USER>/.pub-cache/hosted/pub.dev/timezone-0.9.4/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/timezone-0.9.4/lib/
timing
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/timing-1.0.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/timing-1.0.2/lib/
tuple
2.17
file:///Users/<USER>/.pub-cache/hosted/pub.dev/tuple-2.0.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/tuple-2.0.2/lib/
typed_data
3.5
file:///Users/<USER>/.pub-cache/hosted/pub.dev/typed_data-1.4.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/typed_data-1.4.0/lib/
universal_platform
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/universal_platform-1.1.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/universal_platform-1.1.0/lib/
url_launcher
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher-6.3.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher-6.3.1/lib/
url_launcher_android
3.6
file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_android-6.3.16/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_android-6.3.16/lib/
url_launcher_ios
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.3/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.3/lib/
url_launcher_linux
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_linux-3.2.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_linux-3.2.1/lib/
url_launcher_macos
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_macos-3.2.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_macos-3.2.2/lib/
url_launcher_platform_interface
3.1
file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_platform_interface-2.3.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_platform_interface-2.3.2/lib/
url_launcher_web
3.6
file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_web-2.4.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_web-2.4.1/lib/
url_launcher_windows
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_windows-3.1.4/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_windows-3.1.4/lib/
uuid
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/lib/
vector_graphics
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/vector_graphics-1.1.18/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/vector_graphics-1.1.18/lib/
vector_graphics_codec
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/vector_graphics_codec-1.1.13/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/vector_graphics_codec-1.1.13/lib/
vector_graphics_compiler
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/vector_graphics_compiler-1.1.16/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/vector_graphics_compiler-1.1.16/lib/
vector_math
2.14
file:///Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/
vm_service
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/vm_service-14.3.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/vm_service-14.3.0/lib/
watcher
3.1
file:///Users/<USER>/.pub-cache/hosted/pub.dev/watcher-1.1.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/watcher-1.1.1/lib/
web
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/
web_socket
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/web_socket-1.0.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/web_socket-1.0.1/lib/
web_socket_channel
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/web_socket_channel-3.0.3/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/web_socket_channel-3.0.3/lib/
win32
3.5
file:///Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/
xdg_directories
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/xdg_directories-1.1.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/xdg_directories-1.1.0/lib/
xml
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/
yaml
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/yaml-3.1.3/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/yaml-3.1.3/lib/
culture_connect
3.0
file:///Users/<USER>/Desktop/CurrentProject/cultureConnect/
file:///Users/<USER>/Desktop/CurrentProject/cultureConnect/lib/
_macros
3.4
file:///Users/<USER>/Desktop/Development/flutter/bin/cache/dart-sdk/pkg/_macros/
file:///Users/<USER>/Desktop/Development/flutter/bin/cache/dart-sdk/pkg/_macros/lib/
sky_engine
3.2
file:///Users/<USER>/Desktop/Development/flutter/bin/cache/pkg/sky_engine/
file:///Users/<USER>/Desktop/Development/flutter/bin/cache/pkg/sky_engine/lib/
flutter
3.3
file:///Users/<USER>/Desktop/Development/flutter/packages/flutter/
file:///Users/<USER>/Desktop/Development/flutter/packages/flutter/lib/
flutter_test
3.3
file:///Users/<USER>/Desktop/Development/flutter/packages/flutter_test/
file:///Users/<USER>/Desktop/Development/flutter/packages/flutter_test/lib/
flutter_web_plugins
3.2
file:///Users/<USER>/Desktop/Development/flutter/packages/flutter_web_plugins/
file:///Users/<USER>/Desktop/Development/flutter/packages/flutter_web_plugins/lib/
2
