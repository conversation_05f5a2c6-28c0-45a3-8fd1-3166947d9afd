# CultureConnect Architecture and Authentication Flow

## Overview

CultureConnect now follows a hybrid architecture approach with two client applications:

1. **Mobile App (Flutter)**: Focused on tourist/consumer experiences
2. **Guide Portal (PWA)**: Focused on service provider (guide) management

Both applications connect to the same backend API, sharing data models and authentication mechanisms while providing optimized experiences for their respective user types.

## System Architecture

### High-Level Architecture

```
┌─────────────────┐     ┌─────────────────┐
│                 │     │                 │
│   Mobile App    │     │   Guide Portal  │
│    (Flutter)    │     │      (PWA)      │
│                 │     │                 │
└────────┬────────┘     └────────┬────────┘
         │                       │
         │                       │
         │                       │
         ▼                       ▼
┌─────────────────────────────────────────┐
│                                         │
│            FastAPI Backend              │
│                                         │
└────────┬────────────────────┬───────────┘
         │                    │
         │                    │
         ▼                    ▼
┌─────────────────┐  ┌─────────────────────┐
│                 │  │                     │
│    Database     │  │  Storage Services   │
│  (PostgreSQL)   │  │  (Cloud Storage)    │
│                 │  │                     │
└─────────────────┘  └─────────────────────┘
```

### Backend Architecture

The backend is built with FastAPI and follows a clean architecture pattern:

- **API Layer**: Handles HTTP requests and responses
- **Service Layer**: Contains business logic
- **Repository Layer**: Handles data access
- **Model Layer**: Defines data structures

### Mobile App Architecture (Flutter)

- **UI Layer**: Screens and widgets
- **State Management**: Riverpod providers
- **Service Layer**: API clients and local services
- **Model Layer**: Data models and DTOs

### Guide Portal Architecture (PWA)

- **UI Layer**: React components and pages
- **State Management**: React Query for server state, Zustand for client state
- **Service Layer**: API clients and utilities
- **Model Layer**: TypeScript interfaces and types

## Authentication Flow

### Shared Authentication Mechanism

Both applications use JWT (JSON Web Token) based authentication with the same backend endpoints:

```
┌─────────────┐                  ┌─────────────┐                  ┌─────────────┐
│             │                  │             │                  │             │
│  Client App │                  │   Backend   │                  │  Database   │
│             │                  │             │                  │             │
└──────┬──────┘                  └──────┬──────┘                  └──────┬──────┘
       │                                │                                │
       │  POST /auth/login              │                                │
       │  {email, password}             │                                │
       │────────────────────────────────▶                                │
       │                                │                                │
       │                                │  Verify credentials            │
       │                                │────────────────────────────────▶
       │                                │                                │
       │                                │  User data                     │
       │                                │◀────────────────────────────────
       │                                │                                │
       │                                │  Generate JWT                  │
       │                                │  with user claims              │
       │                                │                                │
       │  JWT Token                     │                                │
       │◀────────────────────────────────                                │
       │                                │                                │
       │  Store token                   │                                │
       │  locally                       │                                │
       │                                │                                │
       │                                │                                │
       │  API Request with              │                                │
       │  Authorization header          │                                │
       │────────────────────────────────▶                                │
       │                                │                                │
       │                                │  Validate JWT                  │
       │                                │  Extract user info             │
       │                                │                                │
       │  Response                      │                                │
       │◀────────────────────────────────                                │
       │                                │                                │
```

### Mobile App Authentication (Flutter)

1. User logs in with email/password or social auth
2. Backend validates credentials and returns JWT token
3. Token is stored securely using `flutter_secure_storage`
4. Token is included in Authorization header for all API requests
5. Token refresh is handled automatically when needed

### Guide Portal Authentication (PWA)

1. Guide logs in with email/password
2. Backend validates credentials and returns JWT token
3. Token is stored in localStorage (with appropriate security measures)
4. Token is included in Authorization header for all API requests
5. Token expiration is handled with automatic refresh

### User Role Separation

The backend distinguishes between user types using a `role` field in the JWT payload:

```json
{
  "sub": "user_id",
  "email": "<EMAIL>",
  "role": "USER|GUIDE|ADMIN",
  "exp": 1672531200
}
```

This allows the backend to:
- Validate that users can only access appropriate endpoints
- Apply role-specific business logic
- Return role-appropriate data

## Data Models

### Shared Models

Both applications share core data models, ensuring consistency:

- **User**: Basic user information
- **Experience**: Cultural experience details
- **Booking**: Booking information
- **Review**: User reviews
- **Message**: Communication between users and guides

### Mobile App Specific Models

- **AR Models**: AR-specific data structures
- **Safety Models**: Safety feature data
- **Tourist-specific preferences**

### Guide Portal Specific Models

- **Guide Profile**: Professional guide information
- **Availability**: Schedule and time slot management
- **Earnings**: Financial data and analytics
- **Performance Metrics**: Guide performance data

## API Integration

### Endpoint Structure

The API follows RESTful principles with role-based access control:

- `/auth/*`: Authentication endpoints (shared)
- `/users/*`: User management endpoints (shared)
- `/experiences/*`: Experience endpoints (shared, with role-specific behaviors)
- `/bookings/*`: Booking endpoints (shared, with role-specific behaviors)
- `/guides/*`: Guide-specific endpoints (primarily for Guide Portal)
- `/tourists/*`: Tourist-specific endpoints (primarily for Mobile App)

### Request Authentication

All authenticated requests include:

```
Authorization: Bearer <jwt_token>
```

### Error Handling

Both applications implement consistent error handling:

1. HTTP status codes for high-level errors
2. Detailed error responses with:
   - Error code
   - Error message
   - Additional details when appropriate
3. Retry logic for transient errors
4. Graceful degradation when services are unavailable

## Security Considerations

### JWT Security

- Short expiration times (1 hour)
- Refresh token rotation
- Secure storage mechanisms
- Token invalidation on logout

### Mobile App Security

- Secure storage for tokens
- Certificate pinning
- App-level encryption
- Biometric authentication option

### PWA Security

- HTTPS only
- Content Security Policy
- XSS protection
- CSRF protection
- Secure cookie handling

## Offline Support

### Mobile App Offline Capabilities

- Cached experiences and bookings
- Offline maps
- AR content caching
- Background sync when connection is restored

### PWA Offline Capabilities

- Service worker for asset caching
- IndexedDB for data storage
- Offline actions queue
- Clear offline mode indicators
- Sync when connection is restored

## Conclusion

This hybrid architecture provides the best of both worlds:

1. **Mobile App**: Optimized for tourists with AR features, offline maps, and on-the-go booking
2. **Guide Portal**: Optimized for guides with comprehensive management tools and analytics

Both applications share the same backend and data models, ensuring consistency while allowing each platform to focus on its specific user needs.
