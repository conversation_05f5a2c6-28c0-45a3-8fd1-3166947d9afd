# CultureConnect

CultureConnect is a platform that connects tourists with local cultural experiences and guides. The project consists of two main applications:

1. **Tourist Mobile App**: A Flutter mobile application for tourists to discover, book, and experience cultural activities.
2. **Guide Portal**: A Flutter web application (PWA) for guides and service providers to manage their offerings, bookings, and communications with tourists.

## Project Structure

The project follows a hybrid architecture with separate codebases for the tourist mobile app and the guide portal:

```
cultureConnect/
├── culture_connect/       # Tourist mobile app (Flutter)
│   ├── lib/               # Main source code for the mobile app
│   ├── test/              # Tests for the mobile app
│   └── ...                # Other Flutter project files
│
├── guide_portal/          # Guide portal web app (Flutter PWA)
│   ├── lib/               # Main source code for the guide portal
│   ├── test/              # Tests for the guide portal
│   └── ...                # Other Flutter project files
│
└── shared/                # Shared code and resources (if any)
```

## Tourist Mobile App Features

- Browse and search cultural experiences
- View experience details with AR enhancements
- Book experiences with flexible scheduling
- Secure payment processing with Stripe and Flutterwave
- Booking management and history
- User profiles and preferences
- Multi-language support
- AR exploration of cultural sites
- Offline support for key features

## Guide Portal Features

- Create and manage cultural experiences
- Manage bookings and availability
- Communicate with tourists
- Create AR content for experiences
- View analytics and earnings
- Manage profile and credentials
- Set pricing and availability
- Receive payments and track earnings

## Getting Started

### Prerequisites

- Flutter SDK (>=3.0.0)
- Dart SDK (>=3.0.0)
- Android Studio / VS Code with Flutter extensions
- Git

### Installation

1. Clone the repository:
   ```bash
   git clone https://github.com/yourusername/cultureconnect.git
   cd cultureconnect
   ```

2. Install dependencies for the tourist mobile app:
   ```bash
   cd culture_connect
   flutter pub get
   ```

3. Install dependencies for the guide portal:
   ```bash
   cd ../guide_portal
   flutter pub get
   ```

4. Create a `.env` file in each project:
   ```bash
   cp .env.example .env
   ```
   Then edit the `.env` file with your API keys and configuration.

5. Run the tourist mobile app:
   ```bash
   cd ../culture_connect
   flutter run
   ```

6. Run the guide portal:
   ```bash
   cd ../guide_portal
   flutter run -d chrome
   ```

## Payment Integration

The app supports two payment providers:

1. **Stripe** - For international payments
   - Credit/Debit cards
   - Apple Pay/Google Pay
   - International bank transfers

2. **Flutterwave** - For African payments
   - Local cards
   - USSD
   - Bank transfers
   - Mobile money

## Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Acknowledgments

- [Flutter](https://flutter.dev/)
- [Stripe](https://stripe.com/)
- [Flutterwave](https://flutterwave.com/)