#!/bin/bash

# Script to fix imports in backup files
# This script will convert relative imports to package imports in .bak files

# Set colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${YELLOW}Finding backup files with relative imports...${NC}"

# Find all backup files with relative imports
FILES_WITH_RELATIVE_IMPORTS=$(find . -name "*.bak" -type f -exec grep -l "import '\.\." {} \;)

# Count the number of files with relative imports
FILE_COUNT=$(echo "$FILES_WITH_RELATIVE_IMPORTS" | wc -l | tr -d ' ')

echo -e "${GREEN}Found $FILE_COUNT files with relative imports.${NC}"

# Process each file
for FILE in $FILES_WITH_RELATIVE_IMPORTS; do
  echo -e "${YELLOW}Processing $FILE...${NC}"
  
  # Replace relative imports with package imports
  # This handles imports like '../models/user.dart' -> 'package:culture_connect/models/user.dart'
  sed -i '' -E 's|import '\''\.\./(.*)'\'';|import '\''package:culture_connect/\1'\'';|g' "$FILE"
  
  # Handle deeper relative imports (../../)
  sed -i '' -E 's|import '\''\.\.\/\.\.\/(.*)'\'';|import '\''package:culture_connect/\1'\'';|g' "$FILE"
  
  # Handle even deeper relative imports (../../../)
  sed -i '' -E 's|import '\''\.\.\/\.\.\/\.\.\/(.*)'\'';|import '\''package:culture_connect/\1'\'';|g' "$FILE"
  
  # Handle local imports (./)
  sed -i '' -E 's|import '\''\.\/(.*)'\'';|import '\''package:culture_connect/\1'\'';|g' "$FILE"
  
  echo -e "${GREEN}Fixed imports in $FILE${NC}"
done

echo -e "${GREEN}All relative imports in backup files have been converted to package imports.${NC}"
