# Travel Document Feature Developer Guide

This guide provides detailed information for developers working on the Travel Document feature in the CultureConnect application. It covers the architecture, key components, and implementation details.

## Table of Contents

1. [Architecture Overview](#architecture-overview)
2. [Models](#models)
   - [TravelDocument](#traveldocument)
   - [Passport](#passport)
   - [Visa](#visa)
   - [DocumentReminder](#documentreminder)
   - [VisaRequirement](#visarequirement)
3. [Providers](#providers)
   - [TravelDocumentProvider](#traveldocumentprovider)
   - [DocumentReminderProvider](#documentreminderprovider)
   - [VisaRequirementProvider](#visarequirementprovider)
4. [Services](#services)
   - [DocumentService](#documentservice)
   - [ReminderService](#reminderservice)
   - [VisaRequirementService](#visarequirementservice)
5. [UI Components](#ui-components)
   - [Screens](#screens)
   - [Widgets](#widgets)
6. [Implementation Details](#implementation-details)
   - [Document Management](#document-management)
   - [Reminder System](#reminder-system)
   - [Visa Requirement Checking](#visa-requirement-checking)
7. [Testing](#testing)
8. [Common Issues and Solutions](#common-issues-and-solutions)

## Architecture Overview

The Travel Document feature follows the Provider pattern for state management and uses a layered architecture:

1. **UI Layer**: Screens and widgets that display information and handle user interactions
2. **Provider Layer**: Providers that manage state and business logic
3. **Service Layer**: Services that handle data operations and API calls
4. **Model Layer**: Data models that represent the domain entities

![Architecture Diagram](../assets/images/travel_document_architecture.png)

## Models

### TravelDocument

`TravelDocument` is an abstract base class for all document types. It contains common properties and methods.

```dart
abstract class TravelDocument {
  final String id;
  final String userId;
  final String name;
  final String documentNumber;
  final String issuedBy;
  final DateTime issuedDate;
  final DateTime expiryDate;
  final TravelDocumentStatus status;
  final List<String> documentImageUrls;
  final String? notes;
  final DateTime createdAt;
  final DateTime updatedAt;
  
  // Getters
  TravelDocumentType get type;
  bool get isExpired;
  bool get isExpiringSoon;
  int get daysUntilExpiry;
  TravelDocumentStatus get calculatedStatus;
  
  // Formatted getters
  String get formattedIssuedDate;
  String get formattedExpiryDate;
}
```

### Passport

`Passport` extends `TravelDocument` with passport-specific properties.

```dart
class Passport extends TravelDocument {
  final String nationality;
  final String countryCode;
  final String placeOfBirth;
  final DateTime dateOfBirth;
  final String gender;
  final String? mrzLine1;
  final String? mrzLine2;
  
  // Getters
  int get age;
  String get formattedDateOfBirth;
  
  // Override
  @override
  TravelDocumentType get type => TravelDocumentType.passport;
}
```

### Visa

`Visa` extends `TravelDocument` with visa-specific properties.

```dart
class Visa extends TravelDocument {
  final VisaType visaType;
  final VisaEntryType entryType;
  final String countryOfIssue;
  final String countryValidFor;
  final int maxStayDuration;
  final int? numberOfEntries;
  final int? processingTime;
  final String? applicationReference;
  
  // Getters
  String get formattedMaxStayDuration;
  
  // Override
  @override
  TravelDocumentType get type => TravelDocumentType.visa;
}
```

### DocumentReminder

`DocumentReminder` represents a reminder for a document.

```dart
class DocumentReminder {
  final String id;
  final String documentId;
  final String title;
  final String message;
  final DateTime reminderDate;
  final DocumentReminderType type;
  final bool isRead;
  final bool isDismissed;
  final DateTime createdAt;
  final DateTime updatedAt;
  
  // Getters
  int get daysUntilDue;
}
```

### VisaRequirement

`VisaRequirement` represents visa requirements between countries.

```dart
class VisaRequirement {
  final String id;
  final String countryFrom;
  final String countryTo;
  final VisaRequirementType requirementType;
  final String description;
  final List<String> requiredDocuments;
  final int? maxStayDuration;
  final int? processingTime;
  final double? visaFee;
  final String? visaFeeUnit;
  final String? applicationUrl;
  final DateTime lastUpdated;
}
```

## Providers

### TravelDocumentProvider

`TravelDocumentProvider` manages the state and operations for travel documents.

```dart
class TravelDocumentProvider extends ChangeNotifier {
  final DocumentService _documentService;
  
  List<TravelDocument> _documents = [];
  bool _isLoading = false;
  String? _error;
  
  // Getters
  List<TravelDocument> get documents => _documents;
  List<Passport> get passports => _documents.whereType<Passport>().toList();
  List<Visa> get visas => _documents.whereType<Visa>().toList();
  List<TravelDocument> get validDocuments => _documents.where((doc) => !doc.isExpired).toList();
  List<TravelDocument> get expiredDocuments => _documents.where((doc) => doc.isExpired).toList();
  bool get isLoading => _isLoading;
  String? get error => _error;
  
  // Methods
  Future<void> initialize();
  Future<void> loadDocuments();
  Future<TravelDocument?> getDocument(String id);
  Future<Passport> addPassport(Passport passport);
  Future<Visa> addVisa(Visa visa);
  Future<TravelDocument> updateDocument(TravelDocument document);
  Future<bool> deleteDocument(String id);
}
```

### DocumentReminderProvider

`DocumentReminderProvider` manages the state and operations for document reminders.

```dart
class DocumentReminderProvider extends ChangeNotifier {
  final ReminderService _reminderService;
  
  List<DocumentReminder> _reminders = [];
  bool _isLoading = false;
  String? _error;
  
  // Getters
  List<DocumentReminder> get reminders => _reminders;
  List<DocumentReminder> get dueReminders => _reminders.where((r) => !r.isRead && !r.isDismissed && r.reminderDate.isBefore(DateTime.now())).toList();
  List<DocumentReminder> get upcomingReminders => _reminders.where((r) => !r.isDismissed && r.reminderDate.isAfter(DateTime.now())).toList();
  bool get isLoading => _isLoading;
  String? get error => _error;
  
  // Methods
  Future<void> initialize();
  Future<void> loadReminders();
  Future<void> createReminder(DocumentReminder reminder);
  Future<void> markReminderAsRead(String id);
  Future<void> dismissReminder(String id);
  Future<void> deleteDocumentReminders(String documentId);
  Future<void> updateDocumentReminders(TravelDocument document);
}
```

### VisaRequirementProvider

`VisaRequirementProvider` manages the state and operations for visa requirements.

```dart
class VisaRequirementProvider extends ChangeNotifier {
  final VisaRequirementService _visaRequirementService;
  
  bool _isLoading = false;
  String? _error;
  
  // Getters
  bool get isLoading => _isLoading;
  String? get error => _error;
  
  // Methods
  Future<void> initialize();
  Future<VisaRequirement?> getVisaRequirement(String countryFrom, String countryTo);
}
```

## Services

### DocumentService

`DocumentService` handles data operations for travel documents.

```dart
class DocumentService {
  final FirebaseFirestore _firestore;
  final FirebaseAuth _auth;
  
  // Methods
  Future<List<TravelDocument>> getDocuments();
  Future<TravelDocument?> getDocument(String id);
  Future<Passport> addPassport(Passport passport);
  Future<Visa> addVisa(Visa visa);
  Future<TravelDocument> updateDocument(TravelDocument document);
  Future<bool> deleteDocument(String id);
}
```

### ReminderService

`ReminderService` handles data operations for document reminders.

```dart
class ReminderService {
  final FirebaseFirestore _firestore;
  final FirebaseAuth _auth;
  
  // Methods
  Future<List<DocumentReminder>> getReminders();
  Future<DocumentReminder> createReminder(DocumentReminder reminder);
  Future<DocumentReminder> updateReminder(DocumentReminder reminder);
  Future<bool> deleteReminder(String id);
  Future<bool> deleteDocumentReminders(String documentId);
}
```

### VisaRequirementService

`VisaRequirementService` handles data operations for visa requirements.

```dart
class VisaRequirementService {
  final FirebaseFirestore _firestore;
  
  // Methods
  Future<VisaRequirement?> getVisaRequirement(String countryFrom, String countryTo);
}
```

## UI Components

### Screens

- **TravelDocumentsScreen**: Main screen for viewing and managing documents
- **DocumentDetailsScreen**: Screen for viewing document details
- **DocumentUploadScreen**: Screen for adding or editing documents
- **DocumentRemindersScreen**: Screen for viewing and managing reminders
- **VisaRequirementsScreen**: Screen for checking visa requirements

### Widgets

- **DocumentCard**: Card widget for displaying a document
- **DocumentUploadForm**: Form widget for adding or editing documents
- **VisaRequirementCard**: Card widget for displaying visa requirements
- **CountrySelector**: Widget for selecting a country
- **DocumentReminderCard**: Card widget for displaying a reminder

## Implementation Details

### Document Management

Documents are stored in Firebase Firestore in the `travel_documents` collection. Each document has a unique ID and is associated with a user ID.

When adding or updating a document:
1. Validate the document data
2. Upload any document images to Firebase Storage
3. Save the document data to Firestore
4. Update the local state
5. Create or update reminders for the document

### Reminder System

Reminders are automatically created for documents based on their expiry dates:
- 90 days before expiry
- 60 days before expiry
- 30 days before expiry
- 7 days before expiry
- On the expiry date

Reminders are stored in Firebase Firestore in the `document_reminders` collection. Each reminder is associated with a document ID.

### Visa Requirement Checking

Visa requirements are stored in Firebase Firestore in the `visa_requirements` collection. Each requirement is identified by a combination of `countryFrom` and `countryTo`.

When checking visa requirements:
1. Get the requirement from Firestore based on the selected countries
2. If no requirement is found, create a mock requirement with a default message
3. Display the requirement to the user

## Testing

The Travel Document feature has comprehensive tests:

- **Unit Tests**: Test providers and services in isolation
- **Widget Tests**: Test UI components
- **Integration Tests**: Test complete user flows

See the [Testing Guide](../testing/testing_guide.md) for more information.

## Common Issues and Solutions

### Document Images Not Uploading

**Issue**: Document images fail to upload to Firebase Storage.

**Solution**:
- Check that the Firebase Storage rules allow uploads
- Ensure the image file size is not too large
- Add error handling for image upload failures

### Reminders Not Being Created

**Issue**: Reminders are not being created for documents.

**Solution**:
- Check that the reminder creation logic is being called
- Ensure the document has a valid expiry date
- Verify that the ReminderService is working correctly

### Visa Requirements Not Found

**Issue**: Visa requirements are not found for certain country pairs.

**Solution**:
- Check that the country codes are correct
- Ensure the visa_requirements collection has data for the country pair
- Implement a fallback mechanism for missing requirements
