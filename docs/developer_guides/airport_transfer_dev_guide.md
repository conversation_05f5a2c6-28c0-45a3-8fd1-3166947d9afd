# Airport Transfer Feature Developer Guide

This guide provides detailed information for developers working on the Airport Transfer feature in the CultureConnect application. It covers the architecture, key components, and implementation details.

## Table of Contents

1. [Architecture Overview](#architecture-overview)
2. [Models](#models)
   - [TransferService](#transferservice)
   - [TransferVehicle](#transfervehicle)
   - [TransferDriver](#transferdriver)
   - [TransferLocation](#transferlocation)
   - [TransferBooking](#transferbooking)
   - [FlightInfo](#flightinfo)
3. [Providers](#providers)
   - [TransferProvider](#transferprovider)
   - [TransferLocationProvider](#transferlocationprovider)
   - [FlightIntegrationProvider](#flightintegrationprovider)
4. [Services](#services)
   - [TransferService](#transferservice-1)
   - [LocationService](#locationservice)
   - [FlightIntegrationService](#flightintegrationservice)
5. [UI Components](#ui-components)
   - [Screens](#screens)
   - [Widgets](#widgets)
6. [Implementation Details](#implementation-details)
   - [Transfer Management](#transfer-management)
   - [Booking Process](#booking-process)
   - [Flight Integration](#flight-integration)
   - [Location Picking](#location-picking)
7. [Testing](#testing)
8. [Common Issues and Solutions](#common-issues-and-solutions)

## Architecture Overview

The Airport Transfer feature follows the Provider pattern for state management and uses a layered architecture:

1. **UI Layer**: Screens and widgets that display information and handle user interactions
2. **Provider Layer**: Providers that manage state and business logic
3. **Service Layer**: Services that handle data operations and API calls
4. **Model Layer**: Data models that represent the domain entities

![Architecture Diagram](../assets/images/airport_transfer_architecture.png)

## Models

### TransferService

`TransferService` represents a transfer service offered by a provider.

```dart
class TransferService {
  final String id;
  final String name;
  final String description;
  final String provider;
  final String location;
  final LatLng coordinates;
  final String imageUrl;
  final List<String> additionalImages;
  final double price;
  final double? originalPrice;
  final int? discountPercentage;
  final String currency;
  final double rating;
  final int reviewCount;
  final TransferVehicleType vehicleType;
  final TransferVehicle vehicle;
  final TransferDriver? driver;
  final int passengerCapacity;
  final int luggageCapacity;
  final bool isPrivate;
  final bool includesMeetAndGreet;
  final bool includesFlightTracking;
  final bool includesWaitingTime;
  final int freeWaitingTime;
  final bool isAvailable24Hours;
  final int minimumNotice;
  final String cancellationPolicy;
  final int freeCancellationHours;
  final List<String> amenities;
  final bool isFeatured;
  final bool isOnSale;
  final DateTime createdAt;
  final DateTime updatedAt;
  
  // Getters
  String get formattedFreeWaitingTime;
  String get formattedMinimumNotice;
  String get formattedFreeCancellation;
  bool get hasFreeWaitingTime;
  bool get hasFreeCancellation;
}
```

### TransferVehicle

`TransferVehicle` represents a vehicle used for transfers.

```dart
class TransferVehicle {
  final String make;
  final String model;
  final int year;
  final String color;
  final String licensePlate;
  final bool hasAirConditioning;
  final bool hasWifi;
  final bool hasUsb;
  final bool hasChildSeat;
  final bool hasWheelchairAccess;
  
  // Getters
  String get fullName;
}
```

### TransferDriver

`TransferDriver` represents a driver for transfers.

```dart
class TransferDriver {
  final String id;
  final String name;
  final String photoUrl;
  final double rating;
  final int reviewCount;
  final int yearsOfExperience;
  final List<String> languages;
  final String phoneNumber;
  
  // Getters
  String get formattedYearsOfExperience;
  String get formattedLanguages;
}
```

### TransferLocation

`TransferLocation` represents a pickup or dropoff location.

```dart
class TransferLocation {
  final String id;
  final TransferLocationType type;
  final String name;
  final String address;
  final String city;
  final String country;
  final LatLng coordinates;
  final String? terminal;
  final String? gate;
  final String? flightNumber;
}
```

### TransferBooking

`TransferBooking` represents a booking for a transfer service.

```dart
class TransferBooking {
  final String id;
  final String userId;
  final String transferId;
  final TransferService? transferService;
  final TransferLocation pickupLocation;
  final TransferLocation dropoffLocation;
  final DateTime pickupDateTime;
  final int passengerCount;
  final int luggageCount;
  final String? specialRequests;
  final String contactName;
  final String contactPhone;
  final String contactEmail;
  final String? flightInfo;
  final TransferBookingStatus status;
  final double totalPrice;
  final String currency;
  final String? paymentMethod;
  final String? confirmationCode;
  final DateTime createdAt;
  final DateTime updatedAt;
  
  // Getters
  String get formattedPickupDate;
  String get formattedPickupTime;
  String get formattedPickupDateTime;
  String get formattedTotalPrice;
}
```

### FlightInfo

`FlightInfo` represents flight information for a transfer.

```dart
class FlightInfo {
  final String flightNumber;
  final String airlineName;
  final String airlineCode;
  final String departureAirportCode;
  final String departureAirportName;
  final String arrivalAirportCode;
  final String arrivalAirportName;
  final DateTime scheduledDepartureTime;
  final DateTime scheduledArrivalTime;
  final FlightStatus status;
  final String? departureTerminal;
  final String? departureGate;
  final String? arrivalTerminal;
  final String? arrivalGate;
  final int? delayMinutes;
}
```

## Providers

### TransferProvider

`TransferProvider` manages the state and operations for transfer services and bookings.

```dart
class TransferProvider extends ChangeNotifier {
  final TransferService _transferService;
  
  List<TransferService> _transfers = [];
  List<TransferBooking> _bookings = [];
  bool _isLoading = false;
  String? _error;
  
  // Getters
  List<TransferService> get transfers => _transfers;
  List<TransferService> get featuredTransfers => _transfers.where((t) => t.isFeatured).toList();
  List<TransferService> get transfersOnSale => _transfers.where((t) => t.isOnSale).toList();
  List<TransferBooking> get bookings => _bookings;
  List<TransferBooking> get upcomingBookings => _bookings.where((b) => b.pickupDateTime.isAfter(DateTime.now()) && b.status != TransferBookingStatus.cancelled).toList();
  List<TransferBooking> get pastBookings => _bookings.where((b) => b.pickupDateTime.isBefore(DateTime.now()) && b.status != TransferBookingStatus.cancelled).toList();
  List<TransferBooking> get cancelledBookings => _bookings.where((b) => b.status == TransferBookingStatus.cancelled).toList();
  bool get isLoading => _isLoading;
  String? get error => _error;
  
  // Methods
  Future<void> initialize();
  Future<void> loadTransfers();
  Future<void> loadBookings();
  Future<TransferService?> getTransfer(String id);
  Future<TransferBooking?> getBooking(String id);
  Future<List<TransferService>> searchTransfers({
    String? location,
    TransferVehicleType? vehicleType,
    int? minPassengerCapacity,
    int? minLuggageCapacity,
    bool? isPrivate,
    bool? includesMeetAndGreet,
    bool? includesFlightTracking,
    double? maxPrice,
  });
  Future<TransferBooking?> bookTransfer(TransferBooking booking);
  Future<TransferBooking?> updateBooking(TransferBooking booking);
  Future<TransferBooking?> cancelBooking(String id, {String? reason});
}
```

### TransferLocationProvider

`TransferLocationProvider` manages the state and operations for transfer locations.

```dart
class TransferLocationProvider extends ChangeNotifier {
  final LocationService _locationService;
  
  List<TransferLocation> _searchResults = [];
  bool _isLoading = false;
  String? _error;
  
  // Getters
  List<TransferLocation> get searchResults => _searchResults;
  bool get isLoading => _isLoading;
  String? get error => _error;
  
  // Methods
  Future<void> initialize();
  Future<void> searchLocations(String query);
  Future<TransferLocation?> getLocation(String id);
}
```

### FlightIntegrationProvider

`FlightIntegrationProvider` manages the state and operations for flight integration.

```dart
class FlightIntegrationProvider extends ChangeNotifier {
  final FlightIntegrationService _flightService;
  
  bool _isLoading = false;
  String? _error;
  
  // Getters
  bool get isLoading => _isLoading;
  String? get error => _error;
  
  // Methods
  Future<void> initialize();
  Future<FlightInfo?> getFlightInfo(String flightNumber, DateTime date);
  static FlightNumberInfo? parseFlightNumber(String flightNumber);
}
```

## Services

### TransferService

`TransferService` handles data operations for transfer services and bookings.

```dart
class TransferService {
  final FirebaseFirestore _firestore;
  final FirebaseAuth _auth;
  
  // Methods
  Future<List<TransferService>> getAllTransfers();
  Future<TransferService?> getTransfer(String id);
  Future<List<TransferService>> searchTransfers({
    String? location,
    TransferVehicleType? vehicleType,
    int? minPassengerCapacity,
    int? minLuggageCapacity,
    bool? isPrivate,
    bool? includesMeetAndGreet,
    bool? includesFlightTracking,
    double? maxPrice,
  });
  Future<List<TransferBooking>> getUserBookings();
  Future<TransferBooking?> getBooking(String id);
  Future<TransferBooking> bookTransfer(TransferBooking booking);
  Future<TransferBooking> updateBooking(TransferBooking booking);
  Future<TransferBooking> cancelBooking(String id, {String? reason});
}
```

### LocationService

`LocationService` handles data operations for transfer locations.

```dart
class LocationService {
  final FirebaseFirestore _firestore;
  
  // Methods
  Future<List<TransferLocation>> searchLocations(String query);
  Future<TransferLocation?> getLocation(String id);
}
```

### FlightIntegrationService

`FlightIntegrationService` handles data operations for flight integration.

```dart
class FlightIntegrationService {
  final String _apiKey;
  final String _apiUrl;
  
  // Methods
  Future<FlightInfo?> getFlightInfo(String flightNumber, DateTime date);
}
```

## UI Components

### Screens

- **TransferServicesScreen**: Main screen for browsing transfer services
- **TransferDetailsScreen**: Screen for viewing transfer details
- **TransferBookingScreen**: Screen for booking a transfer
- **TransferBookingDetailsScreen**: Screen for reviewing booking details
- **TransferBookingConfirmationScreen**: Screen for booking confirmation
- **TransferBookingsScreen**: Screen for viewing and managing bookings
- **TransferSearchScreen**: Screen for searching transfer services

### Widgets

- **TransferCard**: Card widget for displaying a transfer service
- **VehicleSelector**: Widget for selecting a vehicle type
- **LocationPicker**: Widget for selecting a pickup or dropoff location
- **FlightInfoForm**: Form widget for entering flight information
- **TransferBookingCard**: Card widget for displaying a booking

## Implementation Details

### Transfer Management

Transfer services are stored in Firebase Firestore in the `transfer_services` collection. Each service has a unique ID and is associated with a provider.

When loading transfers:
1. Get all transfers from Firestore
2. Filter transfers based on user preferences
3. Sort transfers based on relevance or price
4. Update the local state

### Booking Process

The booking process involves several steps:
1. User selects a transfer service
2. User enters booking details (pickup, dropoff, date, passengers, etc.)
3. User reviews booking details and selects payment method
4. User confirms and pays for the booking
5. Booking is saved to Firestore
6. User receives booking confirmation

Bookings are stored in Firebase Firestore in the `transfer_bookings` collection. Each booking has a unique ID and is associated with a user ID and a transfer ID.

### Flight Integration

Flight integration allows users to add flight information to their bookings:
1. User enters flight number and date
2. System calls the flight API to get flight details
3. Flight information is added to the booking
4. Transfer provider can track the flight and adjust pickup time if needed

Flight information is obtained from a third-party API and is not stored in Firestore.

### Location Picking

Location picking allows users to select pickup and dropoff locations:
1. User enters a location query
2. System searches for matching locations
3. User selects a location from the results
4. Location is added to the booking

Locations are stored in Firebase Firestore in the `transfer_locations` collection. Each location has a unique ID and is associated with a type (airport, hotel, address, etc.).

## Testing

The Airport Transfer feature has comprehensive tests:

- **Unit Tests**: Test providers and services in isolation
- **Widget Tests**: Test UI components
- **Integration Tests**: Test complete user flows

See the [Testing Guide](../testing/testing_guide.md) for more information.

## Common Issues and Solutions

### Booking Confirmation Not Received

**Issue**: User does not receive booking confirmation after payment.

**Solution**:
- Check that the booking is being saved to Firestore
- Ensure the confirmation email is being sent
- Add a retry mechanism for failed bookings

### Flight Information Not Found

**Issue**: Flight information is not found for a valid flight number.

**Solution**:
- Check that the flight API is working correctly
- Ensure the flight number format is correct
- Add error handling for flight API failures

### Location Search Not Working

**Issue**: Location search returns no results for valid queries.

**Solution**:
- Check that the location service is working correctly
- Ensure the search query is being formatted correctly
- Add fuzzy search for better matching
