# Code Documentation Guide

This guide outlines the standards and best practices for documenting code in the CultureConnect application. Following these guidelines ensures that our codebase is maintainable, understandable, and consistent.

## Table of Contents

1. [Introduction](#introduction)
2. [Dart Documentation Comments](#dart-documentation-comments)
   - [Class Documentation](#class-documentation)
   - [Method Documentation](#method-documentation)
   - [Property Documentation](#property-documentation)
   - [Parameter Documentation](#parameter-documentation)
3. [Code Examples](#code-examples)
   - [Model Documentation](#model-documentation)
   - [Provider Documentation](#provider-documentation)
   - [Service Documentation](#service-documentation)
   - [Widget Documentation](#widget-documentation)
   - [Screen Documentation](#screen-documentation)
4. [Documentation Tools](#documentation-tools)
5. [Best Practices](#best-practices)
6. [Common Mistakes](#common-mistakes)

## Introduction

Good code documentation is essential for:
- Helping other developers understand your code
- Making the codebase maintainable
- Enabling effective collaboration
- Facilitating onboarding of new team members
- Ensuring code quality and consistency

In the CultureConnect project, we use Dar<PERSON>'s built-in documentation comments and follow a consistent style for all code documentation.

## Dart Documentation Comments

<PERSON><PERSON> uses `///` for documentation comments. These comments are processed by tools like `dartdoc` to generate HTML documentation.

### Class Documentation

Document every class with a description of its purpose and usage.

```dart
/// A model representing a travel document.
///
/// Travel documents can be passports, visas, or other identification documents.
/// This class serves as the base class for all document types and contains
/// common properties and methods.
abstract class TravelDocument {
  // Class implementation
}
```

### Method Documentation

Document every public method with a description of what it does, its parameters, and its return value.

```dart
/// Loads all documents for the current user.
///
/// Fetches documents from the database and updates the local state.
/// Returns a list of [TravelDocument] objects.
///
/// Throws a [DatabaseException] if the database operation fails.
Future<List<TravelDocument>> loadDocuments() async {
  // Method implementation
}
```

### Property Documentation

Document important properties, especially those that are part of the public API.

```dart
/// The unique identifier of the document.
final String id;

/// The date when the document expires.
///
/// Used to calculate [isExpired] and [isExpiringSoon].
final DateTime expiryDate;
```

### Parameter Documentation

Document parameters using the `@param` tag.

```dart
/// Updates a document with new information.
///
/// @param document The document to update.
/// @param notify Whether to notify listeners of the change.
/// @return The updated document.
Future<TravelDocument> updateDocument(TravelDocument document, {bool notify = true}) async {
  // Method implementation
}
```

## Code Examples

### Model Documentation

```dart
/// A model representing a passport document.
///
/// Extends [TravelDocument] with passport-specific properties and methods.
/// Used to store and manage passport information in the application.
class Passport extends TravelDocument {
  /// The nationality of the passport holder.
  final String nationality;
  
  /// The country code of the issuing country.
  final String countryCode;
  
  /// The place of birth of the passport holder.
  final String placeOfBirth;
  
  /// The date of birth of the passport holder.
  final DateTime dateOfBirth;
  
  /// The gender of the passport holder.
  ///
  /// Usually 'M' for male, 'F' for female, or 'X' for other.
  final String gender;
  
  /// The first line of the machine-readable zone (MRZ).
  ///
  /// Optional, as not all passports have an MRZ.
  final String? mrzLine1;
  
  /// The second line of the machine-readable zone (MRZ).
  ///
  /// Optional, as not all passports have an MRZ.
  final String? mrzLine2;
  
  /// Creates a new passport.
  ///
  /// All parameters except [mrzLine1] and [mrzLine2] are required.
  Passport({
    required String id,
    required String userId,
    required String name,
    required String documentNumber,
    required String issuedBy,
    required DateTime issuedDate,
    required DateTime expiryDate,
    required TravelDocumentStatus status,
    required List<String> documentImageUrls,
    required this.nationality,
    required this.countryCode,
    required this.placeOfBirth,
    required this.dateOfBirth,
    required this.gender,
    this.mrzLine1,
    this.mrzLine2,
    String? notes,
    required DateTime createdAt,
    required DateTime updatedAt,
  }) : super(
          id: id,
          userId: userId,
          name: name,
          documentNumber: documentNumber,
          issuedBy: issuedBy,
          issuedDate: issuedDate,
          expiryDate: expiryDate,
          status: status,
          documentImageUrls: documentImageUrls,
          notes: notes,
          createdAt: createdAt,
          updatedAt: updatedAt,
        );
  
  /// The age of the passport holder in years.
  ///
  /// Calculated based on the [dateOfBirth] and the current date.
  int get age {
    final now = DateTime.now();
    int age = now.year - dateOfBirth.year;
    if (now.month < dateOfBirth.month || 
        (now.month == dateOfBirth.month && now.day < dateOfBirth.day)) {
      age--;
    }
    return age;
  }
  
  /// The date of birth formatted as 'DD/MM/YYYY'.
  String get formattedDateOfBirth {
    return '${dateOfBirth.day.toString().padLeft(2, '0')}/${dateOfBirth.month.toString().padLeft(2, '0')}/${dateOfBirth.year}';
  }
  
  /// The document type.
  ///
  /// Always returns [TravelDocumentType.passport] for passport documents.
  @override
  TravelDocumentType get type => TravelDocumentType.passport;
  
  /// Creates a copy of this passport with the given fields replaced with new values.
  ///
  /// @param id The new ID, or null to keep the current value.
  /// @param userId The new user ID, or null to keep the current value.
  /// @param name The new name, or null to keep the current value.
  /// @param documentNumber The new document number, or null to keep the current value.
  /// @param issuedBy The new issuing authority, or null to keep the current value.
  /// @param issuedDate The new issue date, or null to keep the current value.
  /// @param expiryDate The new expiry date, or null to keep the current value.
  /// @param status The new status, or null to keep the current value.
  /// @param documentImageUrls The new document image URLs, or null to keep the current value.
  /// @param nationality The new nationality, or null to keep the current value.
  /// @param countryCode The new country code, or null to keep the current value.
  /// @param placeOfBirth The new place of birth, or null to keep the current value.
  /// @param dateOfBirth The new date of birth, or null to keep the current value.
  /// @param gender The new gender, or null to keep the current value.
  /// @param mrzLine1 The new MRZ line 1, or null to keep the current value.
  /// @param mrzLine2 The new MRZ line 2, or null to keep the current value.
  /// @param notes The new notes, or null to keep the current value.
  /// @param createdAt The new creation date, or null to keep the current value.
  /// @param updatedAt The new update date, or null to keep the current value.
  /// @return A new [Passport] with the updated fields.
  Passport copyWith({
    String? id,
    String? userId,
    String? name,
    String? documentNumber,
    String? issuedBy,
    DateTime? issuedDate,
    DateTime? expiryDate,
    TravelDocumentStatus? status,
    List<String>? documentImageUrls,
    String? nationality,
    String? countryCode,
    String? placeOfBirth,
    DateTime? dateOfBirth,
    String? gender,
    String? mrzLine1,
    String? mrzLine2,
    String? notes,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Passport(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      name: name ?? this.name,
      documentNumber: documentNumber ?? this.documentNumber,
      issuedBy: issuedBy ?? this.issuedBy,
      issuedDate: issuedDate ?? this.issuedDate,
      expiryDate: expiryDate ?? this.expiryDate,
      status: status ?? this.status,
      documentImageUrls: documentImageUrls ?? this.documentImageUrls,
      nationality: nationality ?? this.nationality,
      countryCode: countryCode ?? this.countryCode,
      placeOfBirth: placeOfBirth ?? this.placeOfBirth,
      dateOfBirth: dateOfBirth ?? this.dateOfBirth,
      gender: gender ?? this.gender,
      mrzLine1: mrzLine1 ?? this.mrzLine1,
      mrzLine2: mrzLine2 ?? this.mrzLine2,
      notes: notes ?? this.notes,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}
```

### Provider Documentation

```dart
/// A provider for managing travel documents.
///
/// This provider handles loading, adding, updating, and deleting travel documents.
/// It also provides filtered lists of documents by type and status.
class TravelDocumentProvider extends ChangeNotifier {
  final DocumentService _documentService;
  
  /// Creates a new travel document provider.
  ///
  /// @param documentService The service for handling document operations.
  TravelDocumentProvider(this._documentService);
  
  // Provider implementation
}
```

### Service Documentation

```dart
/// A service for handling document operations.
///
/// This service interacts with the database to perform CRUD operations
/// on travel documents.
class DocumentService {
  final FirebaseFirestore _firestore;
  final FirebaseAuth _auth;
  
  /// Creates a new document service.
  ///
  /// @param firestore The Firestore instance.
  /// @param auth The Firebase Auth instance.
  DocumentService(this._firestore, this._auth);
  
  // Service implementation
}
```

### Widget Documentation

```dart
/// A card widget for displaying a travel document.
///
/// This widget shows the document's basic information and provides
/// options to view, edit, or delete the document.
class DocumentCard extends StatelessWidget {
  /// The document to display.
  final TravelDocument document;
  
  /// Callback when the card is tapped.
  final VoidCallback? onTap;
  
  /// Callback when the edit button is tapped.
  final VoidCallback? onEdit;
  
  /// Callback when the delete button is tapped.
  final VoidCallback? onDelete;
  
  /// Creates a new document card.
  ///
  /// The [document] parameter is required. The [onTap], [onEdit], and [onDelete]
  /// parameters are optional. If [onEdit] or [onDelete] is null, the corresponding
  /// button will not be shown.
  const DocumentCard({
    Key? key,
    required this.document,
    this.onTap,
    this.onEdit,
    this.onDelete,
  }) : super(key: key);
  
  // Widget implementation
}
```

### Screen Documentation

```dart
/// A screen for displaying travel documents.
///
/// This screen shows a list of the user's travel documents organized by type.
/// It also provides options to add, edit, or delete documents.
class TravelDocumentsScreen extends StatefulWidget {
  /// Creates a new travel documents screen.
  const TravelDocumentsScreen({Key? key}) : super(key: key);
  
  @override
  State<TravelDocumentsScreen> createState() => _TravelDocumentsScreenState();
}
```

## Documentation Tools

We use the following tools for documentation:

- **dartdoc**: Generates HTML documentation from Dart comments
- **VSCode Dart Documentation Extension**: Provides snippets and formatting for documentation comments
- **GitHub Actions**: Automatically checks documentation coverage

## Best Practices

1. **Be Concise**: Write clear, concise documentation that explains the purpose and usage of the code.
2. **Keep It Updated**: Update documentation when you change code.
3. **Use Examples**: Include examples for complex functionality.
4. **Document Exceptions**: Document any exceptions that might be thrown.
5. **Use Proper Grammar**: Use complete sentences and proper grammar.
6. **Be Consistent**: Follow the same style throughout the codebase.
7. **Document Public API**: Always document public classes, methods, and properties.
8. **Use Links**: Link to related classes and methods using square brackets.

## Common Mistakes

1. **Outdated Documentation**: Documentation that doesn't match the current code.
2. **Obvious Comments**: Comments that just repeat what the code does without adding value.
3. **Missing Documentation**: Public API elements without documentation.
4. **Inconsistent Style**: Mixing different documentation styles.
5. **Too Verbose**: Documentation that is too long and detailed.
6. **Too Brief**: Documentation that doesn't provide enough information.
