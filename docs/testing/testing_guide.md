# CultureConnect Testing Guide

This comprehensive guide explains the testing approach for the CultureConnect application. It covers all types of tests, how to write them, how to run them, and best practices.

## Table of Contents

1. [Introduction](#introduction)
2. [Testing Pyramid](#testing-pyramid)
3. [Unit Tests](#unit-tests)
4. [Widget Tests](#widget-tests)
5. [Integration Tests](#integration-tests)
6. [Performance Tests](#performance-tests)
7. [Test Coverage](#test-coverage)
8. [Continuous Integration](#continuous-integration)
9. [Troubleshooting](#troubleshooting)
10. [Best Practices](#best-practices)

## Introduction

Testing is a critical part of the CultureConnect development process. It ensures that our application works as expected, helps catch bugs early, and provides confidence when making changes. This guide will help you understand our testing approach and how to contribute to our test suite.

## Testing Pyramid

We follow the testing pyramid approach, which suggests having:

- Many **unit tests** (testing individual components in isolation)
- Fewer **widget tests** (testing UI components)
- Even fewer **integration tests** (testing complete features or flows)
- A few **performance tests** (testing app performance)

This approach provides a good balance between test coverage, execution speed, and maintenance cost.

![Testing Pyramid](../assets/images/testing_pyramid.png)

## Unit Tests

Unit tests verify that individual units of code (functions, methods, classes) work as expected in isolation.

### Writing Unit Tests

Unit tests are located in the `test/unit` directory, mirroring the structure of the `lib` directory.

Example of a unit test for a provider:

```dart
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:culture_connect/providers/my_provider.dart';
import 'package:culture_connect/services/my_service.dart';

class MockMyService extends Mock implements MyService {}

void main() {
  late MockMyService mockService;
  late MyProvider provider;

  setUp(() {
    mockService = MockMyService();
    provider = MyProvider(mockService);
  });

  test('should load data successfully', () async {
    // Arrange
    when(mockService.getData()).thenAnswer((_) async => ['data1', 'data2']);

    // Act
    await provider.loadData();

    // Assert
    verify(mockService.getData()).called(1);
    expect(provider.data.length, 2);
    expect(provider.isLoading, false);
    expect(provider.error, null);
  });
}
```

### Running Unit Tests

To run all unit tests:

```bash
flutter test test/unit
```

To run a specific unit test file:

```bash
flutter test test/unit/path/to/test_file.dart
```

## Widget Tests

Widget tests verify that UI components render correctly and respond to user interactions as expected.

### Writing Widget Tests

Widget tests are located in the `test/widget` directory, mirroring the structure of the `lib/widgets` directory.

Example of a widget test:

```dart
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:culture_connect/widgets/my_widget.dart';

void main() {
  testWidgets('MyWidget displays correctly', (WidgetTester tester) async {
    // Build our widget
    await tester.pumpWidget(
      MaterialApp(
        home: MyWidget(title: 'Test Title'),
      ),
    );

    // Verify that the widget displays the title
    expect(find.text('Test Title'), findsOneWidget);

    // Tap on a button
    await tester.tap(find.byType(ElevatedButton));
    await tester.pump();

    // Verify that the widget responds to the tap
    expect(find.text('Button Pressed'), findsOneWidget);
  });
}
```

### Running Widget Tests

To run all widget tests:

```bash
flutter test test/widget
```

To run a specific widget test file:

```bash
flutter test test/widget/path/to/test_file.dart
```

## Integration Tests

Integration tests verify that different parts of the application work together correctly, testing complete features or user flows.

### Writing Integration Tests

Integration tests are located in the `test/integration` directory, organized by feature.

Example of an integration test:

```dart
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:culture_connect/main.dart' as app;

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  testWidgets('Complete login flow', (WidgetTester tester) async {
    // Launch the app
    app.main();
    await tester.pumpAndSettle();

    // Navigate to login screen
    await tester.tap(find.text('Login'));
    await tester.pumpAndSettle();

    // Enter credentials
    await tester.enterText(find.byKey(const Key('email_field')), '<EMAIL>');
    await tester.enterText(find.byKey(const Key('password_field')), 'password123');
    await tester.tap(find.byType(ElevatedButton));
    await tester.pumpAndSettle();

    // Verify successful login
    expect(find.text('Welcome, User!'), findsOneWidget);
  });
}
```

### Running Integration Tests

To run all integration tests:

```bash
flutter test integration_test
```

To run a specific integration test file:

```bash
flutter test integration_test/path/to/test_file.dart
```

## Performance Tests

Performance tests verify that the application meets performance requirements, such as startup time, frame rate, and memory usage.

### Writing Performance Tests

Performance tests are located in the `test/performance` directory.

Example of a performance test:

```dart
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:culture_connect/main.dart' as app;

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  testWidgets('Measure app startup time', (WidgetTester tester) async {
    // Start timing
    final stopwatch = Stopwatch()..start();

    // Launch the app
    app.main();
    await tester.pumpAndSettle();

    // Stop timing
    stopwatch.stop();

    // Verify that the app starts within the expected time
    expect(stopwatch.elapsedMilliseconds, lessThan(2000));
  });
}
```

### Running Performance Tests

To run all performance tests:

```bash
flutter test test/performance
```

To run a specific performance test file:

```bash
flutter test test/performance/path/to/test_file.dart
```

## Test Coverage

Test coverage measures how much of your code is covered by tests. We aim for high test coverage to ensure that most of our code is tested.

### Generating Test Coverage Reports

To generate a test coverage report:

```bash
flutter test --coverage
```

This will generate a `coverage/lcov.info` file. You can convert this to a more readable HTML report using the `lcov` tool:

```bash
genhtml coverage/lcov.info -o coverage/html
```

Then open `coverage/html/index.html` in a web browser to view the report.

## Continuous Integration

We use GitHub Actions for continuous integration (CI). Every pull request is automatically tested to ensure that it doesn't break existing functionality.

Our CI pipeline runs:
- Static code analysis (linting)
- Unit tests
- Widget tests
- Integration tests (on emulators)

See `.github/workflows/ci.yml` for the CI configuration.

## Troubleshooting

### Common Issues

#### Tests Failing on CI but Passing Locally

This is often due to differences in the environment. Make sure your local environment matches the CI environment as closely as possible.

#### Widget Tests Timing Out

Widget tests might time out if animations or asynchronous operations take too long. Use `tester.pumpAndSettle()` to wait for animations to complete, or `tester.pump(Duration(seconds: 1))` to wait for a specific duration.

#### Mocks Not Working as Expected

Make sure you're using the latest version of Mockito and that you've generated the mock classes correctly. Run `flutter pub run build_runner build` to generate the mock classes.

## Best Practices

1. **Write tests first (TDD)**: Consider writing tests before implementing features to ensure that your code meets requirements.

2. **Keep tests focused**: Each test should verify a single behavior or aspect of your code.

3. **Use descriptive test names**: Test names should clearly describe what is being tested and the expected outcome.

4. **Isolate tests**: Tests should not depend on each other or on external resources.

5. **Mock external dependencies**: Use mocks to isolate the code being tested from external dependencies.

6. **Test edge cases**: Test not only the happy path but also edge cases and error conditions.

7. **Maintain tests**: Keep tests up to date as the code changes.

8. **Run tests regularly**: Run tests locally before pushing changes to avoid breaking the build.

9. **Review test coverage**: Regularly review test coverage to identify areas that need more testing.

10. **Refactor tests**: Refactor tests to remove duplication and improve readability.

For more information, see the [Flutter Testing Documentation](https://docs.flutter.dev/testing).
