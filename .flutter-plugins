# This is a generated file; do not edit or check into version control.
arcore_flutter_plugin=/Users/<USER>/.pub-cache/hosted/pub.dev/arcore_flutter_plugin-0.1.0/
audio_session=/Users/<USER>/.pub-cache/hosted/pub.dev/audio_session-0.1.25/
camera=/Users/<USER>/.pub-cache/hosted/pub.dev/camera-0.10.6/
camera_android=/Users/<USER>/.pub-cache/hosted/pub.dev/camera_android-0.10.10+3/
camera_avfoundation=/Users/<USER>/.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.19/
camera_web=/Users/<USER>/.pub-cache/hosted/pub.dev/camera_web-0.3.5/
cloud_firestore=/Users/<USER>/.pub-cache/hosted/pub.dev/cloud_firestore-4.17.5/
cloud_firestore_web=/Users/<USER>/.pub-cache/hosted/pub.dev/cloud_firestore_web-3.12.5/
connectivity_plus=/Users/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus-5.0.2/
facebook_auth_desktop=/Users/<USER>/.pub-cache/hosted/pub.dev/facebook_auth_desktop-1.0.3/
file_picker=/Users/<USER>/.pub-cache/hosted/pub.dev/file_picker-9.2.3/
file_selector_linux=/Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_linux-0.9.3+2/
file_selector_macos=/Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_macos-0.9.4+2/
file_selector_windows=/Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_windows-0.9.3+4/
firebase_analytics=/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_analytics-10.10.7/
firebase_analytics_web=/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_analytics_web-0.5.7+7/
firebase_app_check=/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_app_check-0.2.2/
firebase_app_check_web=/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_app_check_web-0.1.2/
firebase_auth=/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_auth-4.16.0/
firebase_auth_web=/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_auth_web-5.8.13/
firebase_core=/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core-2.32.0/
firebase_core_web=/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core_web-2.22.0/
firebase_crashlytics=/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_crashlytics-3.5.7/
firebase_messaging=/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_messaging-14.7.10/
firebase_messaging_web=/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_messaging_web-3.5.18/
firebase_storage=/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_storage-11.6.5/
firebase_storage_web=/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_storage_web-3.6.22/
flutter_facebook_auth=/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_facebook_auth-6.2.0/
flutter_facebook_auth_web=/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_facebook_auth_web-5.0.0/
flutter_local_notifications=/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications-16.3.3/
flutter_local_notifications_linux=/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications_linux-4.0.1/
flutter_plugin_android_lifecycle=/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_plugin_android_lifecycle-2.0.28/
flutter_secure_storage=/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.4/
flutter_secure_storage_linux=/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_linux-1.2.3/
flutter_secure_storage_macos=/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_macos-3.1.3/
flutter_secure_storage_web=/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_web-1.2.1/
flutter_secure_storage_windows=/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_windows-3.1.2/
flutter_stripe=/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_stripe-10.2.0/
geolocator=/Users/<USER>/.pub-cache/hosted/pub.dev/geolocator-10.1.1/
geolocator_android=/Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_android-4.6.2/
geolocator_apple=/Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13/
geolocator_web=/Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_web-2.2.1/
geolocator_windows=/Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_windows-0.2.5/
google_maps_flutter=/Users/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter-2.12.1/
google_maps_flutter_android=/Users/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_android-2.16.1/
google_maps_flutter_ios=/Users/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.2/
google_maps_flutter_web=/Users/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_web-0.5.12/
google_mlkit_commons=/Users/<USER>/.pub-cache/hosted/pub.dev/google_mlkit_commons-0.6.1/
google_sign_in=/Users/<USER>/.pub-cache/hosted/pub.dev/google_sign_in-6.3.0/
google_sign_in_android=/Users/<USER>/.pub-cache/hosted/pub.dev/google_sign_in_android-6.2.1/
google_sign_in_ios=/Users/<USER>/.pub-cache/hosted/pub.dev/google_sign_in_ios-5.9.0/
google_sign_in_web=/Users/<USER>/.pub-cache/hosted/pub.dev/google_sign_in_web-0.12.4+4/
image_picker=/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker-1.1.2/
image_picker_android=/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_android-0.8.12+23/
image_picker_for_web=/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_for_web-3.0.6/
image_picker_ios=/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/
image_picker_linux=/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_linux-0.2.1+2/
image_picker_macos=/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_macos-0.2.1+2/
image_picker_windows=/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_windows-0.2.1+1/
just_audio=/Users/<USER>/.pub-cache/hosted/pub.dev/just_audio-0.9.46/
just_audio_web=/Users/<USER>/.pub-cache/hosted/pub.dev/just_audio_web-0.4.16/
local_auth=/Users/<USER>/.pub-cache/hosted/pub.dev/local_auth-2.3.0/
local_auth_android=/Users/<USER>/.pub-cache/hosted/pub.dev/local_auth_android-1.0.49/
local_auth_darwin=/Users/<USER>/.pub-cache/hosted/pub.dev/local_auth_darwin-1.4.3/
local_auth_windows=/Users/<USER>/.pub-cache/hosted/pub.dev/local_auth_windows-1.0.11/
path_provider=/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider-2.1.5/
path_provider_android=/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_android-2.2.17/
path_provider_foundation=/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/
path_provider_linux=/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/
path_provider_windows=/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/
permission_handler=/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler-11.4.0/
permission_handler_android=/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_android-12.1.0/
permission_handler_apple=/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/
permission_handler_html=/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_html-0.1.3+5/
permission_handler_windows=/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_windows-0.2.1/
record=/Users/<USER>/.pub-cache/hosted/pub.dev/record-5.2.1/
record_android=/Users/<USER>/.pub-cache/hosted/pub.dev/record_android-1.3.3/
record_darwin=/Users/<USER>/.pub-cache/hosted/pub.dev/record_darwin-1.2.2/
record_linux=/Users/<USER>/.pub-cache/hosted/pub.dev/record_linux-0.7.2/
record_web=/Users/<USER>/.pub-cache/hosted/pub.dev/record_web-1.1.6/
record_windows=/Users/<USER>/.pub-cache/hosted/pub.dev/record_windows-1.0.5/
sensors_plus=/Users/<USER>/.pub-cache/hosted/pub.dev/sensors_plus-4.0.2/
share_plus=/Users/<USER>/.pub-cache/hosted/pub.dev/share_plus-7.2.2/
shared_preferences=/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences-2.5.3/
shared_preferences_android=/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_android-2.4.10/
shared_preferences_foundation=/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/
shared_preferences_linux=/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_linux-2.4.1/
shared_preferences_web=/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_web-2.4.3/
shared_preferences_windows=/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_windows-2.4.1/
sign_in_with_apple=/Users/<USER>/.pub-cache/hosted/pub.dev/sign_in_with_apple-5.0.0/
sign_in_with_apple_web=/Users/<USER>/.pub-cache/hosted/pub.dev/sign_in_with_apple_web-1.0.1/
smart_auth=/Users/<USER>/.pub-cache/hosted/pub.dev/smart_auth-1.1.1/
speech_to_text=/Users/<USER>/.pub-cache/hosted/pub.dev/speech_to_text-6.6.0/
speech_to_text_macos=/Users/<USER>/.pub-cache/hosted/pub.dev/speech_to_text_macos-1.1.0/
sqflite=/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.4.1/
sqflite_android=/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_android-2.4.0/
sqflite_darwin=/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1+1/
stripe_android=/Users/<USER>/.pub-cache/hosted/pub.dev/stripe_android-10.2.1/
stripe_ios=/Users/<USER>/.pub-cache/hosted/pub.dev/stripe_ios-10.2.0/
stripe_payment=/Users/<USER>/.pub-cache/hosted/pub.dev/stripe_payment-1.1.4/
url_launcher=/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher-6.3.1/
url_launcher_android=/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_android-6.3.16/
url_launcher_ios=/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.3/
url_launcher_linux=/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_linux-3.2.1/
url_launcher_macos=/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_macos-3.2.2/
url_launcher_web=/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_web-2.4.1/
url_launcher_windows=/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_windows-3.1.4/
