import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:culture_connect/models/travel/transfer/transfers.dart';
import 'package:culture_connect/widgets/travel/transfer/transfer_card.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

void main() {
  group('TransferCard Widget', () {
    final testVehicle = TransferVehicle(
      make: 'Toyota',
      model: 'Camry',
      year: 2022,
      color: 'Black',
      licensePlate: 'ABC123',
      hasAirConditioning: true,
      hasWifi: true,
      hasUsb: true,
      hasChildSeat: false,
      hasWheelchairAccess: false,
    );

    final testDriver = TransferDriver(
      id: 'driver1',
      name: '<PERSON>',
      photoUrl: 'https://example.com/driver.jpg',
      rating: 4.8,
      reviewCount: 120,
      yearsOfExperience: 5,
      languages: ['English', 'Spanish'],
      phoneNumber: '+**********',
    );

    final testTransfer = TransferService(
      id: 'transfer1',
      name: 'Airport to Hotel Transfer',
      description: 'Comfortable transfer from airport to your hotel',
      provider: 'ExampleTransfers',
      location: 'New York',
      coordinates: const LatLng(40.7128, -74.0060),
      imageUrl: 'https://example.com/transfer.jpg',
      additionalImages: ['https://example.com/transfer2.jpg'],
      price: 50.0,
      currency: 'USD',
      rating: 4.5,
      reviewCount: 100,
      vehicleType: TransferVehicleType.sedan,
      vehicle: testVehicle,
      driver: testDriver,
      passengerCapacity: 4,
      luggageCapacity: 2,
      isPrivate: true,
      includesMeetAndGreet: true,
      includesFlightTracking: true,
      includesWaitingTime: true,
      freeWaitingTime: 60,
      isAvailable24Hours: true,
      minimumNotice: 120,
      cancellationPolicy: 'Free cancellation up to 24 hours before pickup',
      freeCancellationHours: 24,
      amenities: ['Bottled water', 'Newspapers'],
      isFeatured: true,
      isOnSale: false,
      createdAt: DateTime(2023, 1, 1),
      updatedAt: DateTime(2023, 1, 1),
    );

    final saleTransfer = TransferService(
      id: 'transfer2',
      name: 'Hotel to Airport Transfer',
      description: 'Comfortable transfer from hotel to airport',
      provider: 'ExampleTransfers',
      location: 'New York',
      coordinates: const LatLng(40.7128, -74.0060),
      imageUrl: 'https://example.com/transfer.jpg',
      additionalImages: ['https://example.com/transfer2.jpg'],
      price: 40.0,
      originalPrice: 50.0,
      discountPercentage: 20,
      currency: 'USD',
      rating: 4.5,
      reviewCount: 100,
      vehicleType: TransferVehicleType.sedan,
      vehicle: testVehicle,
      driver: testDriver,
      passengerCapacity: 4,
      luggageCapacity: 2,
      isPrivate: true,
      includesMeetAndGreet: true,
      includesFlightTracking: true,
      includesWaitingTime: true,
      freeWaitingTime: 60,
      isAvailable24Hours: true,
      minimumNotice: 120,
      cancellationPolicy: 'Free cancellation up to 24 hours before pickup',
      freeCancellationHours: 24,
      amenities: ['Bottled water', 'Newspapers'],
      isFeatured: false,
      isOnSale: true,
      createdAt: DateTime(2023, 1, 1),
      updatedAt: DateTime(2023, 1, 1),
    );

    testWidgets('renders transfer card correctly', (WidgetTester tester) async {
      // Arrange
      bool onTapCalled = false;

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: TransferCard(
              transfer: testTransfer,
              onTap: () => onTapCalled = true,
            ),
          ),
        ),
      );

      // Assert
      expect(find.text('Airport to Hotel Transfer'), findsOneWidget);
      expect(find.text('ExampleTransfers'), findsOneWidget);
      expect(find.text('New York'), findsOneWidget);
      expect(find.text('USD50.00'), findsOneWidget);
      expect(find.text('(100)'), findsOneWidget);
      expect(find.text('4 passengers'), findsOneWidget);
      expect(find.text('2 luggage'), findsOneWidget);
      expect(find.text('Sedan'), findsOneWidget);
      expect(find.text('Private'), findsOneWidget);
      expect(find.text('Free cancellation up to 24 hours before pickup'), findsOneWidget);
      expect(find.text('View Details'), findsOneWidget);
      expect(find.text('FEATURED'), findsOneWidget);
      
      // Test onTap callback
      await tester.tap(find.byType(InkWell));
      expect(onTapCalled, true);
    });

    testWidgets('renders sale transfer card correctly', (WidgetTester tester) async {
      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: TransferCard(
              transfer: saleTransfer,
              onTap: () {},
            ),
          ),
        ),
      );

      // Assert
      expect(find.text('Hotel to Airport Transfer'), findsOneWidget);
      expect(find.text('ExampleTransfers'), findsOneWidget);
      expect(find.text('USD50.00'), findsOneWidget);
      expect(find.text('USD40.00'), findsOneWidget);
      expect(find.text('20% OFF'), findsOneWidget);
      expect(find.text('FEATURED'), findsNothing);
    });

    testWidgets('renders full details when showFullDetails is true', (WidgetTester tester) async {
      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: SingleChildScrollView(
              child: TransferCard(
                transfer: testTransfer,
                onTap: () {},
                showFullDetails: true,
              ),
            ),
          ),
        ),
      );

      // Assert
      expect(find.text('Description'), findsOneWidget);
      expect(find.text('Comfortable transfer from airport to your hotel'), findsOneWidget);
      expect(find.text('Features'), findsOneWidget);
      expect(find.text('Meet & Greet'), findsOneWidget);
      expect(find.text('Flight Tracking'), findsOneWidget);
      expect(find.text('60 min Free Waiting'), findsOneWidget);
      expect(find.text('Vehicle'), findsOneWidget);
      expect(find.text('Toyota Camry - Black'), findsOneWidget);
      expect(find.text('A/C'), findsOneWidget);
      expect(find.text('WiFi'), findsOneWidget);
      expect(find.text('USB'), findsOneWidget);
      expect(find.text('View Details'), findsNothing);
    });

    testWidgets('renders network image with error handler', (WidgetTester tester) async {
      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: TransferCard(
              transfer: testTransfer.copyWith(
                imageUrl: 'invalid_url',
              ),
              onTap: () {},
            ),
          ),
        ),
      );

      // Assert - should show error placeholder
      expect(find.byIcon(Icons.airport_shuttle), findsOneWidget);
    });
  });
}
