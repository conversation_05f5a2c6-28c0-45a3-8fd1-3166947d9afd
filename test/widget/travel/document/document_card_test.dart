import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:culture_connect/models/travel/document/travel_documents.dart';
import 'package:culture_connect/widgets/travel/document/document_card.dart';
import 'package:culture_connect/theme/app_colors.dart';

void main() {
  group('DocumentCard Widget', () {
    final testPassport = Passport(
      id: 'passport1',
      userId: 'user1',
      name: 'US Passport',
      documentNumber: 'AB123456',
      issuedBy: 'Department of State',
      issuedDate: DateTime(2020, 1, 1),
      expiryDate: DateTime(2030, 1, 1),
      status: TravelDocumentStatus.valid,
      documentImageUrls: ['https://example.com/passport.jpg'],
      createdAt: DateTime(2020, 1, 1),
      updatedAt: DateTime(2020, 1, 1),
      nationality: 'United States',
      countryCode: 'US',
      placeOfBirth: 'New York',
      dateOfBirth: DateTime(1990, 1, 1),
      gender: 'M',
    );

    final expiredPassport = Passport(
      id: 'passport2',
      userId: 'user1',
      name: 'Expired Passport',
      documentNumber: 'CD789012',
      issuedBy: 'Department of State',
      issuedDate: DateTime(2010, 1, 1),
      expiryDate: DateTime(2020, 1, 1),
      status: TravelDocumentStatus.expired,
      documentImageUrls: ['https://example.com/passport.jpg'],
      createdAt: DateTime(2010, 1, 1),
      updatedAt: DateTime(2010, 1, 1),
      nationality: 'United States',
      countryCode: 'US',
      placeOfBirth: 'New York',
      dateOfBirth: DateTime(1990, 1, 1),
      gender: 'M',
    );

    final expiringSoonPassport = Passport(
      id: 'passport3',
      userId: 'user1',
      name: 'Expiring Soon Passport',
      documentNumber: 'EF345678',
      issuedBy: 'Department of State',
      issuedDate: DateTime(2015, 1, 1),
      expiryDate: DateTime.now().add(const Duration(days: 30)),
      status: TravelDocumentStatus.valid,
      documentImageUrls: ['https://example.com/passport.jpg'],
      createdAt: DateTime(2015, 1, 1),
      updatedAt: DateTime(2015, 1, 1),
      nationality: 'United States',
      countryCode: 'US',
      placeOfBirth: 'New York',
      dateOfBirth: DateTime(1990, 1, 1),
      gender: 'M',
    );

    testWidgets('renders valid document correctly', (WidgetTester tester) async {
      // Arrange
      bool onTapCalled = false;
      bool onEditCalled = false;
      bool onDeleteCalled = false;

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: DocumentCard(
              document: testPassport,
              onTap: () => onTapCalled = true,
              onEdit: () => onEditCalled = true,
              onDelete: () => onDeleteCalled = true,
            ),
          ),
        ),
      );

      // Assert
      expect(find.text('US Passport'), findsOneWidget);
      expect(find.text('AB123456'), findsOneWidget);
      expect(find.text('Department of State'), findsOneWidget);
      expect(find.text('01/01/2020'), findsOneWidget);
      expect(find.text('01/01/2030'), findsOneWidget);
      expect(find.text('Valid'), findsOneWidget);
      expect(find.text('Expires in ${testPassport.daysUntilExpiry} days'), findsOneWidget);
      
      // Test onTap callback
      await tester.tap(find.byType(InkWell));
      expect(onTapCalled, true);
      
      // Test onEdit callback
      await tester.tap(find.byIcon(Icons.edit));
      expect(onEditCalled, true);
      
      // Test onDelete callback
      await tester.tap(find.byIcon(Icons.delete));
      await tester.pumpAndSettle(); // Wait for dialog animation
      expect(find.text('Delete Document'), findsOneWidget);
      expect(find.text('Are you sure you want to delete US Passport? This action cannot be undone.'), findsOneWidget);
      
      // Cancel deletion
      await tester.tap(find.text('CANCEL'));
      await tester.pumpAndSettle();
      expect(onDeleteCalled, false);
      
      // Confirm deletion
      await tester.tap(find.byIcon(Icons.delete));
      await tester.pumpAndSettle();
      await tester.tap(find.text('DELETE'));
      await tester.pumpAndSettle();
      expect(onDeleteCalled, true);
    });

    testWidgets('renders expired document correctly', (WidgetTester tester) async {
      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: DocumentCard(
              document: expiredPassport,
              onTap: () {},
              onEdit: () {},
              onDelete: () {},
            ),
          ),
        ),
      );

      // Assert
      expect(find.text('Expired Passport'), findsOneWidget);
      expect(find.text('CD789012'), findsOneWidget);
      expect(find.text('Department of State'), findsOneWidget);
      expect(find.text('01/01/2010'), findsOneWidget);
      expect(find.text('01/01/2020'), findsOneWidget);
      expect(find.text('Expired'), findsOneWidget);
      
      // Check for expired indicator
      final textWidgets = tester.widgetList<Text>(find.byType(Text));
      final expiredText = textWidgets.firstWhere(
        (widget) => widget.data?.startsWith('Expired') ?? false,
        orElse: () => const Text(''),
      );
      expect(expiredText.data?.startsWith('Expired'), true);
      expect(expiredText.style?.color, AppColors.error);
    });

    testWidgets('renders expiring soon document correctly', (WidgetTester tester) async {
      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: DocumentCard(
              document: expiringSoonPassport,
              onTap: () {},
              onEdit: () {},
              onDelete: () {},
            ),
          ),
        ),
      );

      // Assert
      expect(find.text('Expiring Soon Passport'), findsOneWidget);
      expect(find.text('EF345678'), findsOneWidget);
      expect(find.text('Department of State'), findsOneWidget);
      expect(find.text('01/01/2015'), findsOneWidget);
      expect(find.text('Valid'), findsOneWidget);
      
      // Check for expiring soon indicator
      final textWidgets = tester.widgetList<Text>(find.byType(Text));
      final expiringText = textWidgets.firstWhere(
        (widget) => widget.data?.startsWith('Expires in') ?? false,
        orElse: () => const Text(''),
      );
      expect(expiringText.data?.startsWith('Expires in'), true);
      expect(expiringText.style?.color, AppColors.warning);
    });

    testWidgets('renders without edit and delete buttons when callbacks are null', (WidgetTester tester) async {
      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: DocumentCard(
              document: testPassport,
              onTap: () {},
            ),
          ),
        ),
      );

      // Assert
      expect(find.byIcon(Icons.edit), findsNothing);
      expect(find.byIcon(Icons.delete), findsNothing);
    });
  });
}
