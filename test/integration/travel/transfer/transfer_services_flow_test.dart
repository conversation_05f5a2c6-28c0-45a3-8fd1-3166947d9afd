import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:culture_connect/main.dart' as app;
import 'package:culture_connect/models/travel/transfer/transfers.dart';
import 'package:culture_connect/providers/travel/transfer/transfer_providers.dart';
import 'package:culture_connect/screens/travel/transfer/transfer_screens.dart';
import 'package:provider/provider.dart';
import 'package:mockito/mockito.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

class MockTransferProvider extends Mock implements TransferProvider {}
class MockTransferLocationProvider extends Mock implements TransferLocationProvider {}
class MockFlightIntegrationProvider extends Mock implements FlightIntegrationProvider {}

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  group('Transfer Services Flow Integration Tests', () {
    late MockTransferProvider mockTransferProvider;
    late MockTransferLocationProvider mockLocationProvider;
    late MockFlightIntegrationProvider mockFlightProvider;

    setUp(() {
      mockTransferProvider = MockTransferProvider();
      mockLocationProvider = MockTransferLocationProvider();
      mockFlightProvider = MockFlightIntegrationProvider();
    });

    testWidgets('Complete transfer services flow', (WidgetTester tester) async {
      // Setup mock data
      final testVehicle = TransferVehicle(
        make: 'Toyota',
        model: 'Camry',
        year: 2022,
        color: 'Black',
        licensePlate: 'ABC123',
        hasAirConditioning: true,
        hasWifi: true,
        hasUsb: true,
        hasChildSeat: false,
        hasWheelchairAccess: false,
      );

      final testDriver = TransferDriver(
        id: 'driver1',
        name: 'John Doe',
        photoUrl: 'https://example.com/driver.jpg',
        rating: 4.8,
        reviewCount: 120,
        yearsOfExperience: 5,
        languages: ['English', 'Spanish'],
        phoneNumber: '+**********',
      );

      final testTransfer = TransferService(
        id: 'transfer1',
        name: 'Airport to Hotel Transfer',
        description: 'Comfortable transfer from airport to your hotel',
        provider: 'ExampleTransfers',
        location: 'New York',
        coordinates: const LatLng(40.7128, -74.0060),
        imageUrl: 'https://example.com/transfer.jpg',
        additionalImages: ['https://example.com/transfer2.jpg'],
        price: 50.0,
        currency: 'USD',
        rating: 4.5,
        reviewCount: 100,
        vehicleType: TransferVehicleType.sedan,
        vehicle: testVehicle,
        driver: testDriver,
        passengerCapacity: 4,
        luggageCapacity: 2,
        isPrivate: true,
        includesMeetAndGreet: true,
        includesFlightTracking: true,
        includesWaitingTime: true,
        freeWaitingTime: 60,
        isAvailable24Hours: true,
        minimumNotice: 120,
        cancellationPolicy: 'Free cancellation up to 24 hours before pickup',
        freeCancellationHours: 24,
        amenities: ['Bottled water', 'Newspapers'],
        isFeatured: true,
        isOnSale: false,
        createdAt: DateTime(2023, 1, 1),
        updatedAt: DateTime(2023, 1, 1),
      );

      final testBooking = TransferBooking(
        id: 'booking1',
        userId: 'user1',
        transferId: 'transfer1',
        transferService: testTransfer,
        pickupLocation: TransferLocation(
          id: 'location1',
          type: TransferLocationType.airport,
          name: 'JFK Airport',
          address: 'JFK Airport, Queens, NY',
          city: 'New York',
          country: 'USA',
          coordinates: const LatLng(40.6413, -73.7781),
        ),
        dropoffLocation: TransferLocation(
          id: 'location2',
          type: TransferLocationType.hotel,
          name: 'Hilton Hotel',
          address: '1335 Avenue of the Americas, New York',
          city: 'New York',
          country: 'USA',
          coordinates: const LatLng(40.7621, -73.9771),
        ),
        pickupDateTime: DateTime(2023, 6, 1, 14, 0),
        passengerCount: 2,
        luggageCount: 2,
        contactName: 'Jane Smith',
        contactPhone: '+**********',
        contactEmail: '<EMAIL>',
        status: TransferBookingStatus.confirmed,
        totalPrice: 50.0,
        currency: 'USD',
        confirmationCode: 'ABC123',
        createdAt: DateTime(2023, 1, 1),
        updatedAt: DateTime(2023, 1, 1),
      );

      // Setup mock provider responses
      when(mockTransferProvider.isLoading).thenReturn(false);
      when(mockTransferProvider.error).thenReturn(null);
      when(mockTransferProvider.transfers).thenReturn([testTransfer]);
      when(mockTransferProvider.featuredTransfers).thenReturn([testTransfer]);
      when(mockTransferProvider.transfersOnSale).thenReturn([]);
      when(mockTransferProvider.bookings).thenReturn([testBooking]);
      when(mockTransferProvider.upcomingBookings).thenReturn([testBooking]);
      when(mockTransferProvider.pastBookings).thenReturn([]);
      when(mockTransferProvider.cancelledBookings).thenReturn([]);
      when(mockTransferProvider.initialize()).thenAnswer((_) async {});
      when(mockTransferProvider.loadTransfers()).thenAnswer((_) async {});
      when(mockTransferProvider.loadBookings()).thenAnswer((_) async {});
      when(mockTransferProvider.getTransfer('transfer1')).thenAnswer((_) async => testTransfer);
      when(mockTransferProvider.getBooking('booking1')).thenAnswer((_) async => testBooking);
      when(mockTransferProvider.searchTransfers(
        location: anyNamed('location'),
        vehicleType: anyNamed('vehicleType'),
        minPassengerCapacity: anyNamed('minPassengerCapacity'),
        minLuggageCapacity: anyNamed('minLuggageCapacity'),
        isPrivate: anyNamed('isPrivate'),
        includesMeetAndGreet: anyNamed('includesMeetAndGreet'),
        includesFlightTracking: anyNamed('includesFlightTracking'),
        maxPrice: anyNamed('maxPrice'),
      )).thenAnswer((_) async => [testTransfer]);
      when(mockTransferProvider.bookTransfer(any)).thenAnswer((_) async => testBooking);
      when(mockTransferProvider.updateBooking(any)).thenAnswer((_) async => testBooking);
      when(mockTransferProvider.cancelBooking(any, reason: anyNamed('reason'))).thenAnswer((_) async => testBooking.copyWith(
        status: TransferBookingStatus.cancelled,
      ));

      when(mockLocationProvider.isLoading).thenReturn(false);
      when(mockLocationProvider.error).thenReturn(null);
      when(mockLocationProvider.initialize()).thenAnswer((_) async {});

      when(mockFlightProvider.isLoading).thenReturn(false);
      when(mockFlightProvider.error).thenReturn(null);
      when(mockFlightProvider.initialize()).thenAnswer((_) async {});

      // Build our app and trigger a frame
      await tester.pumpWidget(
        MultiProvider(
          providers: [
            ChangeNotifierProvider<TransferProvider>.value(value: mockTransferProvider),
            ChangeNotifierProvider<TransferLocationProvider>.value(value: mockLocationProvider),
            ChangeNotifierProvider<FlightIntegrationProvider>.value(value: mockFlightProvider),
          ],
          child: MaterialApp(
            home: const TransferServicesScreen(),
            routes: {
              '/transfer_details': (context) => const TransferDetailsScreen(transferId: 'transfer1'),
              '/transfer_booking': (context) => const TransferBookingScreen(transferId: 'transfer1'),
              '/transfer_bookings': (context) => const TransferBookingsScreen(),
              '/transfer_search': (context) => const TransferSearchScreen(),
            },
          ),
        ),
      );

      // Wait for the screen to load
      await tester.pumpAndSettle();

      // Verify the initial screen shows the transfers
      expect(find.text('Airport Transfers'), findsOneWidget);
      expect(find.text('Find Airport Transfers'), findsOneWidget);
      expect(find.text('Featured Transfers'), findsOneWidget);
      expect(find.text('Airport to Hotel Transfer'), findsOneWidget);
      expect(find.text('ExampleTransfers'), findsOneWidget);
      expect(find.text('USD50.00'), findsOneWidget);
      expect(find.text('View Details'), findsOneWidget);

      // Tap on the transfer to view details
      await tester.tap(find.text('Airport to Hotel Transfer').first);
      await tester.pumpAndSettle();

      // Verify the transfer details screen
      expect(find.text('Airport to Hotel Transfer'), findsOneWidget);
      expect(find.text('ExampleTransfers'), findsOneWidget);
      expect(find.text('USD50.00'), findsOneWidget);
      expect(find.text('Description'), findsOneWidget);
      expect(find.text('Comfortable transfer from airport to your hotel'), findsOneWidget);
      expect(find.text('Vehicle Details'), findsOneWidget);
      expect(find.text('Toyota Camry - Black'), findsOneWidget);
      expect(find.text('Service Details'), findsOneWidget);
      expect(find.text('Private'), findsOneWidget);
      expect(find.text('Included'), findsAtLeast(1));
      expect(find.text('Cancellation Policy'), findsOneWidget);
      expect(find.text('Free cancellation up to 24 hours before pickup'), findsOneWidget);
      expect(find.text('Book Now'), findsOneWidget);

      // Tap on the Book Now button
      await tester.tap(find.text('Book Now'));
      await tester.pumpAndSettle();

      // Verify the booking screen
      expect(find.text('Book Transfer'), findsOneWidget);
      expect(find.text('Booking Summary'), findsOneWidget);
      expect(find.text('Airport to Hotel Transfer'), findsOneWidget);
      expect(find.text('ExampleTransfers'), findsOneWidget);
      expect(find.text('USD50.00'), findsOneWidget);
      expect(find.text('Pickup & Dropoff'), findsOneWidget);
      expect(find.text('Pickup Location'), findsOneWidget);
      expect(find.text('Dropoff Location'), findsOneWidget);
      expect(find.text('Pickup Date & Time'), findsOneWidget);
      expect(find.text('Passengers & Luggage'), findsOneWidget);
      expect(find.text('Contact Information'), findsOneWidget);
      expect(find.text('Special Requests (Optional)'), findsOneWidget);
      expect(find.text('Search'), findsOneWidget);

      // Go back to the transfer details screen
      await tester.tap(find.byIcon(Icons.arrow_back));
      await tester.pumpAndSettle();

      // Go back to the transfers screen
      await tester.tap(find.byIcon(Icons.arrow_back));
      await tester.pumpAndSettle();

      // Tap on the bookings icon
      await tester.tap(find.byIcon(Icons.history));
      await tester.pumpAndSettle();

      // Verify the bookings screen
      expect(find.text('My Bookings'), findsOneWidget);
      expect(find.text('Upcoming'), findsOneWidget);
      expect(find.text('Past'), findsOneWidget);
      expect(find.text('Cancelled'), findsOneWidget);
      expect(find.text('JFK Airport'), findsOneWidget);
      expect(find.text('Hilton Hotel'), findsOneWidget);
      expect(find.text('Confirmed'), findsOneWidget);
      expect(find.text('Cancel'), findsOneWidget);

      // Tap on the booking to view details
      await tester.tap(find.text('Airport to Hotel Transfer'));
      await tester.pumpAndSettle();

      // Verify the booking details screen
      expect(find.text('Booking Details'), findsOneWidget);
      expect(find.text('Airport to Hotel Transfer'), findsOneWidget);
      expect(find.text('ExampleTransfers'), findsOneWidget);
      expect(find.text('JFK Airport'), findsOneWidget);
      expect(find.text('Hilton Hotel'), findsOneWidget);
      expect(find.text('Passengers: 2'), findsOneWidget);
      expect(find.text('Luggage: 2'), findsOneWidget);
      expect(find.text('USD50.00'), findsOneWidget);
      expect(find.text('Confirmed'), findsOneWidget);
      expect(find.text('ABC123'), findsOneWidget);

      // Go back to the bookings screen
      await tester.tap(find.byIcon(Icons.arrow_back));
      await tester.pumpAndSettle();

      // Go back to the transfers screen
      await tester.tap(find.byIcon(Icons.arrow_back));
      await tester.pumpAndSettle();

      // Tap on the search icon
      await tester.tap(find.byIcon(Icons.search));
      await tester.pumpAndSettle();

      // Verify the search screen
      expect(find.text('Search Transfers'), findsOneWidget);
      expect(find.text('Enter airport, hotel, or address'), findsOneWidget);
      expect(find.text('Search'), findsOneWidget);
      expect(find.byIcon(Icons.filter_list), findsOneWidget);

      // Tap on the filter icon
      await tester.tap(find.byIcon(Icons.filter_list));
      await tester.pumpAndSettle();

      // Verify the filter dialog
      expect(find.text('Filter Transfers'), findsOneWidget);
      expect(find.text('Vehicle Type'), findsOneWidget);
      expect(find.text('Passenger Capacity'), findsOneWidget);
      expect(find.text('Luggage Capacity'), findsOneWidget);
      expect(find.text('Maximum Price'), findsOneWidget);
      expect(find.text('Features'), findsOneWidget);
      expect(find.text('Private Transfer'), findsOneWidget);
      expect(find.text('Meet & Greet'), findsOneWidget);
      expect(find.text('Flight Tracking'), findsOneWidget);
      expect(find.text('Reset'), findsOneWidget);
      expect(find.text('Apply'), findsOneWidget);
    });
  });
}
